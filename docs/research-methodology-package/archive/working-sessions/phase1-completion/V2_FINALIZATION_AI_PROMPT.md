# AI Agent Prompt: Yemen Market Integration v2 Architecture Finalization

## 🎯 Context and Objective

You are tasked with finalizing and wiring up the v2 architecture for the Yemen Market Integration econometric research platform. The project has discovered that exchange rate divergence (535 YER/USD in Houthi areas vs 2000+ YER/USD in government areas) explains apparent negative price premiums in conflict zones.

Phase 1 (Foundation) is complete with literature review, theoretical framework, and data inventory. We're now transitioning to Phase 2 (Methodology) and need the v2 architecture fully operational to support advanced econometric analysis.

## 📁 Key Project Files to Review

### Architecture Files
- `/v2/` - Contains the v2 implementation structure
- `/v2/src/` - Core domain models and services
- `/v2/pyproject.toml` - Dependencies and configuration
- `/archive/planning/V2_PRODUCTION_TRANSITION_PLAN.md` - Original v2 vision

### Research Context
- `/docs/research-methodology-package/CLAUDE.md` - Complete research overview
- `/src/yemen_market/models/three_tier/` - Current v1 three-tier implementation
- `/docs/architecture/v2-architecture.md` - v2 design specifications

## 🏗️ V2 Architecture Requirements

### 1. Core Domain Models (DDD Approach)
```python
# Complete implementation needed for:
- Market Entity (with currency zone support)
- Price Value Objects (YER and USD denominated)
- Conflict Events (with geographic boundaries)
- Exchange Rate Time Series (by zone)
- Aid Distribution (with modality tracking)
```

### 2. Three-Tier Analysis Pipeline
```python
# Tier 1: Pooled Panel Analysis
- Fixed effects with Driscoll-Kraay standard errors
- Currency-adjusted price transformations
- Exchange rate zone indicators

# Tier 2: Commodity-Specific Models  
- Threshold VECM for regime switching
- Cointegration tests by currency zone
- Cross-border arbitrage analysis

# Tier 3: Policy Validation
- Factor models for conflict spillovers
- Aid effectiveness by currency regime
- Natural experiment identification
```

### 3. Data Pipeline Requirements
```python
# Data Sources Integration:
1. Exchange Rates:
   - CBY Aden (official rates)
   - CBY Sana'a (controlled rates)
   - Parallel market rates (OCHA)
   - UN operational rates

2. Price Data:
   - WFP VAM (3000+ markets)
   - FAO GIEWS (119 markets)
   - Local monitoring systems

3. Aid Distribution:
   - OCHA 3W (Who, What, Where)
   - Cash Consortium Yemen
   - Cluster coordination data

4. Conflict Events:
   - ACLED (weekly updates)
   - Yemen Data Project
   - CIMP (civilian impact)
```

### 4. Analysis Features Required

#### A. Exchange Rate Mechanism (H1)
```python
def analyze_exchange_rate_mechanism():
    """
    Test if negative price premiums disappear when analyzed in USD
    Implement regression discontinuity at currency zone boundaries
    """
    pass
```

#### B. Aid Effectiveness (H7)
```python
def test_aid_effectiveness_by_regime():
    """
    Validate that aid is 26% less effective in volatile currency zones
    Implement triple-difference specification
    """
    pass
```

#### C. Natural Experiments
```python
def identify_natural_experiments():
    """
    1. 2020 aid cuts → 35-42% price spike
    2. Currency zone boundaries
    3. Market control switches
    """
    pass
```

## 🔧 Specific Implementation Tasks

### 1. Complete v2 Domain Models
- [ ] Implement all entity classes with proper value objects
- [ ] Add currency zone support to market entities
- [ ] Create exchange rate repository with multi-zone support
- [ ] Implement aid distribution tracking by modality

### 2. Wire Up Data Pipeline
- [ ] Create unified data loader for all 40+ sources
- [ ] Implement data validation and quality checks
- [ ] Add missing data imputation for conflict zones
- [ ] Create balanced panel construction logic

### 3. Implement Analysis Pipeline
- [ ] Port three-tier models from v1 to v2 architecture
- [ ] Add currency adjustment transformations
- [ ] Implement natural experiment identification
- [ ] Create robustness check battery

### 4. Add API Endpoints
```python
# Priority endpoints:
POST /api/v2/analysis/three-tier
GET /api/v2/data/exchange-rates/{zone}
POST /api/v2/experiments/aid-cuts-2020
GET /api/v2/results/{analysis-id}
```

### 5. Testing & Validation
- [ ] Unit tests for all domain models
- [ ] Integration tests for data pipeline
- [ ] Validation against v1 results
- [ ] Performance benchmarks for large panels

## 📊 Expected Deliverables

### 1. Functional v2 System
- Complete domain model implementation
- Working data pipeline for all sources
- Three-tier analysis capability
- API endpoints for key operations

### 2. Documentation Updates
- API documentation with examples
- Data pipeline flow diagrams
- Analysis methodology guide
- Deployment instructions

### 3. Migration Support
- v1 to v2 data migration scripts
- Result comparison tools
- Backward compatibility layer

### 4. Performance Metrics
- Handle 50 markets × 23 commodities × 60 months
- Sub-second response for cached analyses
- Support for parallel analysis runs

## 🚀 Implementation Priority

### Week 1: Core Infrastructure
1. Complete domain models
2. Basic data pipeline
3. Exchange rate integration

### Week 2: Analysis Pipeline
1. Port three-tier models
2. Implement H1 and H7 tests
3. Natural experiment framework

### Week 3: Integration & Testing
1. API endpoints
2. Comprehensive testing
3. Documentation

### Week 4: Deployment & Validation
1. Production deployment
2. Performance optimization
3. v1 result validation

## 💡 Key Considerations

### 1. Currency Zone Handling
- Never mix YER and USD prices in same analysis
- Maintain zone-specific exchange rate series
- Handle boundary markets specially

### 2. Missing Data
- Conflict-driven missingness is non-random
- Implement appropriate imputation
- Document data quality by region/time

### 3. Scalability
- Design for expanding to other countries
- Support for additional currency zones
- Flexible hypothesis testing framework

## 📋 Success Criteria

1. **v2 system reproduces v1 results** where applicable
2. **New analyses support 10 hypotheses** (H1-H10)
3. **Data pipeline handles all 40+ sources**
4. **Performance meets research needs**
5. **Clear migration path from v1**

## 🔗 Resources

- Original PRD: `/docs/PRD_Yemen_Market_Integration.md`
- v1 Implementation: `/src/yemen_market/`
- Research Methodology: `/docs/research-methodology-package/`
- Architecture Docs: `/docs/architecture/`

Focus on creating a production-ready v2 system that supports groundbreaking econometric research on market integration in fragmented monetary systems. The implementation should be robust, scalable, and ready for publication-quality analysis.