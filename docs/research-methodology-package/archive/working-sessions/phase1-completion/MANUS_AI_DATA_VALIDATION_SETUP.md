# Manus AI Data Validation Setup for Yemen Research

## 🎯 Objective
Validate data availability and quality for Phase 2 methodology development before proceeding with hypothesis operationalization.

## 📁 Files to Upload to Manus AI

### Priority 1 (Essential Context)
1. **`CLAUDE.md`** - Complete research overview with key discoveries
2. **`01-foundation/PROJECT_CLAUDE.md`** - Exchange rate mechanism and variable definitions
3. **`02-data/sources/available-sources.md`** - Complete data inventory with URLs

### Priority 2 (Research Framework)
4. **`01-foundation/theory/testable-hypotheses.md`** - All 10 hypotheses needing data validation
5. **`01-foundation/literature/integrated-literature-review.md`** - Research context and natural experiments

### Priority 3 (Methodology Context)
6. **`01-foundation/RESEARCH_QUESTION_EVOLUTION.md`** - How we arrived at current framework

## 🤖 Recommended Manus AI Prompt

```
I'm conducting econometric research on Yemen market integration. My core discovery is that exchange rate divergence (535 YER/USD in Houthi areas vs 2000+ YER/USD in government areas) explains apparent negative price premiums in conflict zones.

I've completed Phase 1 (literature review) and identified 10 testable hypotheses, but before moving to Phase 2 (methodology), I need to validate that my planned data sources actually support my research design.

Please analyze the attached files and help me:

## PRIMARY TASKS:

### 1. Data Source Validation
- Review my 40+ identified data sources and assess:
  * Which ones are actually accessible and downloadable
  * Temporal coverage vs. what I need (2015-2025, monthly/daily frequency)
  * Geographic granularity (district-level minimum for Yemen's 333 districts)
  * Data quality indicators and known limitations

### 2. Natural Experiment Feasibility  
- Assess whether my identified natural experiments are viable:
  * **2020 Aid Cuts**: Can I identify exact timing and geographic distribution?
  * **Currency Zone Boundaries**: Are exchange rate zones stable enough for RD design?
  * **Market Control Switches**: Can I track which markets changed control when?

### 3. Hypothesis-Data Alignment
- For each of my 10 hypotheses (H1-H10), verify:
  * Required variables are available in identified datasets
  * Sample sizes sufficient for statistical power
  * Identification strategies feasible with available data

### 4. Critical Gap Identification
- Highlight any major data gaps that would require:
  * Alternative data sources
  * Modified research design
  * Different identification strategies

## SPECIFIC DATA PRIORITIES:

1. **Exchange Rates**: Daily/weekly parallel market rates by geographic zone
2. **Prices**: Market-level food prices with currency denomination 
3. **Aid Distribution**: Geographic and temporal variation for 2015-2025
4. **Conflict Events**: High-frequency, geo-coded incident data

## OUTPUT REQUESTED:

1. **Data Feasibility Report**: Which sources work, which don't
2. **Research Design Recommendations**: Adjustments needed based on data reality
3. **Priority Data Collection Plan**: What to download first for Phase 2
4. **Risk Assessment**: Potential roadblocks and mitigation strategies

Focus on practical, actionable insights that will make Phase 2 methodology development efficient and robust.
```

## 🔧 Alternative Prompts by Focus Area

### If Manus AI Struggles with Full Scope, Use These Focused Prompts:

#### Prompt A: Data Source Assessment Only
```
I have identified 40+ data sources for Yemen economic research. Please analyze my data inventory and assess:
1. Which sources are actually accessible (test 5-10 key URLs)
2. Data quality and coverage vs. stated requirements
3. Priority ranking for most reliable sources
4. Gaps requiring alternative approaches

Focus on practical data acquisition strategy.
```

#### Prompt B: Natural Experiment Validation
```
I've identified several natural experiments for Yemen market analysis:
- 2020 humanitarian aid funding cuts
- Currency zone boundary effects  
- Market control switching events

Please assess whether these are empirically viable using available data sources and suggest the strongest identification strategy.
```

#### Prompt C: Hypothesis-Data Mapping
```
I have 10 econometric hypotheses about Yemen market integration. Please map each hypothesis to available data sources and identify:
1. Which hypotheses have strong data support
2. Which need data modifications  
3. Sample size and power considerations
4. Recommended testing sequence
```

## 📊 Expected Manus AI Outputs

### 1. Data Quality Assessment Matrix
| Source | Accessibility | Coverage | Quality | Priority |
|--------|--------------|----------|---------|----------|
| WFP Prices | High | 80% | Good | 1 |
| World Bank FX | Medium | 60% | Fair | 2 |
| OCHA Aid | High | 90% | Good | 1 |

### 2. Research Design Feasibility Score
- H1 (Exchange Rate): 95% feasible ✅
- H2 (Aid Effects): 85% feasible ⚠️
- H3 (Demand): 70% feasible ⚠️
- etc.

### 3. Critical Path Recommendations
1. Download WFP and OCHA data first (highest quality)
2. Test 2020 aid cut identification with actual data
3. Map currency zones using available geographic indicators
4. Assess power for smaller effects in contested areas

### 4. Risk Mitigation Plan
- Alternative data sources for gaps
- Modified specifications if needed
- Backup identification strategies

## 🎯 Success Criteria

After Manus AI analysis, you should have:
- ✅ Confidence in data availability for core hypotheses
- ✅ Realistic timeline for data collection
- ✅ Adjusted research design based on data reality
- ✅ Clear priorities for Phase 2 launch

## 📋 Next Steps After Manus Analysis

1. **High Feasibility (90%+)**: Proceed directly to Phase 2
2. **Medium Feasibility (70-90%)**: Make recommended adjustments, then proceed
3. **Low Feasibility (<70%)**: Significant redesign needed - return to Phase 1

This validation ensures your Phase 2 methodology development is built on solid data foundations.
```