# Quick Processing Guide - When ChatGPT Results Arrive

## Immediate Actions (5 minutes)

1. **Save Raw Output**
   ```bash
   # Copy entire ChatGPT response to:
   humanitarian-aid/raw-research/chatgpt-deep-research-output.md
   ```

2. **Quick Scan for Yemen**
   - Ctrl+F "Yemen"
   - Note any specific mentions
   - Flag Yemen-specific papers

3. **Identify Top 5 Papers**
   - Most relevant to our context
   - Best identification strategies
   - Recent (post-2015 preferred)

## First Processing Pass (15 minutes)

### Extract Key Elements

1. **Identification Strategies**
   - List all IV approaches
   - Note natural experiments
   - Flag spatial methods

2. **Data Sources**
   - New datasets mentioned
   - Access methods
   - Yemen coverage

3. **Main Findings**
   - Aid-price relationships
   - Exchange rate effects
   - Heterogeneous impacts

4. **Methodological Insights**
   - Panel data approaches
   - Robustness checks
   - Common pitfalls

## Create Summary Document (10 minutes)

Save to: `humanitarian-aid/processed/key-findings-summary.md`

```markdown
# Key Findings from Humanitarian Aid Literature

## Top 5 Most Relevant Papers
1. [Author Year] - Title
   - Key finding:
   - Method:
   - Yemen relevance:

## Main Identification Strategies
1. **[Strategy Name]**
   - Description:
   - Application to Yemen:
   - Data requirements:

## New Data Sources
1. **[Source Name]**
   - Coverage:
   - Access:
   - Variables:

## Econometric Specifications
\```stata
* Most promising specification
\```

## Yemen-Specific Applications
1. 
2. 
3. 
```

## Integration Actions (15 minutes)

1. **Update CLAUDE.md**
   - Add new insights to H2 hypothesis
   - Include new data sources
   - Add identification strategies

2. **Create New Specifications**
   ```stata
   * Based on literature findings
   * Focus on implementable approaches
   ```

3. **Draft Follow-up Questions**
   - What needs clarification?
   - What papers need full text?
   - What data to pursue first?

## Next Research Stream

After processing humanitarian aid research:
1. Submit "Demand Destruction" prompt
2. Create folder structure for next stream
3. Update research tracker

## Quick Wins to Implement

- [ ] Test simplest new specification
- [ ] Check if we have suggested data
- [ ] Email for data access if needed
- [ ] Add best papers to reading list
- [ ] Update hypotheses document

Remember: Focus on actionable insights for Yemen research!