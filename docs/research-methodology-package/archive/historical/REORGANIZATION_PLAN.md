# Research Methodology Package Reorganization Plan

## Current Problems
- Mixed purposes at root level (reference, working docs, guides)
- No clear hierarchy or workflow
- Duplicate/overlapping content
- Difficult to find what you need

## Proposed New Structure

```
research-methodology-package/
├── 00-Core-Reference/              # ⭐ Essential docs you need constantly
│   ├── Research-Discovery/         # Your key findings
│   ├── Hypotheses/                 # H1-H10 ready reference
│   ├── Methodology-Framework/      # Core methodology docs
│   └── Context-Instructions/       # CLAUDE files
│
├── 01-Phase-1-Foundation/          # ✅ COMPLETE - Literature & Theory
│   ├── Literature-Synthesis/       # All three mechanisms
│   ├── Theoretical-Framework/      # Models and hypotheses
│   ├── Data-Inventory/            # 40+ sources mapped
│   └── Comparative-Analysis/       # Yemen, Syria, Rwanda, etc.
│
├── 02-Phase-2-Methodology/         # 🔄 CURRENT - Operationalization
│   ├── Hypothesis-Specifications/ # Converting H1-H10 to regressions
│   ├── Identification-Strategies/ # Natural experiments, IV, RD
│   ├── Code-Templates/            # R, Stata, Python implementations
│   └── Robustness-Design/         # Testing procedures
│
├── 03-Phase-3-Data/               # 📊 NEXT - Collection & Preparation
│   ├── Data-Collection/           # Download scripts and logs
│   ├── Data-Cleaning/             # Processing pipelines
│   ├── Panel-Construction/        # Building analysis datasets
│   └── Quality-Checks/            # Validation procedures
│
├── 04-Phase-4-Analysis/           # 📈 FUTURE - Running Models
│   ├── Main-Results/              # Primary specifications
│   ├── Robustness-Checks/         # Sensitivity analysis
│   ├── Mechanism-Tests/           # Testing channels
│   └── Diagnostics/               # Model validation
│
├── 05-Phase-5-Synthesis/          # 📝 FUTURE - Paper Writing
│   ├── Manuscript-Drafts/         # Paper versions
│   ├── Tables-Figures/            # Publication-ready outputs
│   ├── Supplementary-Materials/   # Online appendix
│   └── Submission-Materials/      # Journal requirements
│
├── Working-Sessions/              # 💻 Active work area
│   ├── AI-Sessions/               # Genspark, ChatGPT, Perplexity outputs
│   ├── Session-Logs/              # Progress tracking
│   ├── Draft-Documents/           # Work in progress
│   └── Notes-Ideas/               # Quick thoughts, todos
│
├── Tools-and-Workflows/           # 🛠️ How-to guides
│   ├── AI-Usage-Guides/           # Genspark, ChatGPT workflows
│   ├── Technical-Guides/          # Econometric procedures
│   ├── Project-Management/        # Organization, tracking
│   └── Quick-References/          # Cheat sheets, commands
│
└── Archive/                       # 🗄️ Old/completed materials
    ├── Original-Structure/        # Pre-reorganization files
    ├── Completed-Sessions/        # Finished AI work
    └── Superseded-Documents/      # Older versions
```

## Benefits of This Structure

### 1. **Clear Workflow**
- Numbered phases show progression
- Each phase has consistent subfolders
- Easy to see where you are

### 2. **Separation of Concerns**
- Reference materials separate from working docs
- Tools separate from research content
- Archive keeps things clean

### 3. **Easy Navigation**
- Numbers force correct ordering
- Descriptive names clarify purpose
- Icons provide visual cues

### 4. **Supports Your Process**
- Phases match paper development
- Working area for daily use
- Reference always accessible

## Migration Plan

### Step 1: Create New Structure
- Set up all folders with README files
- Ensure clear documentation

### Step 2: Move Core Materials
- CLAUDE files → 00-Core-Reference/
- Phase work → Numbered phase folders
- Guides → Tools-and-Workflows/

### Step 3: Organize Working Materials
- Current AI outputs → Working-Sessions/
- Progress trackers → Working-Sessions/Session-Logs/

### Step 4: Archive Old Structure
- Move duplicate folders to Archive/
- Keep for reference but out of way

## Quick Access Paths

For your most common needs:
- **Current hypotheses**: `00-Core-Reference/Hypotheses/`
- **Today's work**: `Working-Sessions/AI-Sessions/[today]/`
- **Literature findings**: `01-Phase-1-Foundation/Literature-Synthesis/`
- **Next phase prep**: `02-Phase-2-Methodology/`

This structure will grow with your project while staying organized!