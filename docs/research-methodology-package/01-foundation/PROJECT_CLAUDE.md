# CLAUDE.md - Yemen Market Integration Econometric Research Guide

## 🎯 Core Research Question
**Why do high-conflict areas in Yemen show LOWER prices, contradicting standard economic theory?**

## 💡 KEY DISCOVERY (DO NOT FORGET)
The "negative price premiums" may be an artifact of exchange rate divergence:
- **Houthi areas**: 535-539 YER/USD (stable, controlled)
- **Government areas**: 2,000-2,150 YER/USD (4x depreciation!)

**ALWAYS CHECK**: Are we analyzing in YER or USD? This fundamentally changes results.

## 📊 Critical Data Notes

### Exchange Rates
- **Multiple rates exist**: Official CBY-Aden, Official CBY-Sana'a, parallel market
- **Timing matters**: When was the price recorded vs when was FX rate recorded?
- **Black market premiums**: Vary by location and time

### Price Data
- **WFP prices**: Field-collected, includes both YER and USD
- **FAO prices**: May use different collection methods - compare!
- **Missing data**: Not random - markets stop reporting during intense conflict

### Aid Distribution
- **OCHA 3W**: Who-What-Where dataset has cash vs in-kind breakdown
- **Endogeneity**: Aid goes to worst-affected areas (instrument needed)
- **Timing**: Aid announcements vs actual distribution

## 🧮 Key Variable Definitions

```python
# ALWAYS USE THESE DEFINITIONS
exchange_rate_it = official_rate * (1 + black_market_premium_it)
price_usd_it = price_yer_it / exchange_rate_it
real_price_it = price_yer_it / cpi_it
conflict_it = log(1 + events_it)  # Log transformation for count data
aid_per_capita_it = total_aid_it / population_it
```

## ⚠️ Common Pitfalls (AVOID THESE)

1. **Currency Confusion**: Never mix YER and USD prices in same regression
2. **Exchange Rate Timing**: Transaction rate ≠ Official rate on same date
3. **Survivorship Bias**: Markets that stop reporting are systematically different
4. **Aid Endogeneity**: Cannot regress prices on aid without instruments
5. **Aggregation Error**: Don't average prices across different commodities
6. **Missing Data**: Imputation must account for conflict-driven missingness
7. **Seasonal Patterns**: Always include month FE (Ramadan effects huge)
8. **Quality Changes**: Same commodity name ≠ same quality over time
9. **Trader Selection**: Only resilient traders survive (selection bias)
10. **Control Changes**: Market switching control zones creates discontinuities

## 🔬 Current Hypotheses

### H1: Exchange Rate Mechanism (PRIMARY)
- Negative premiums disappear when using USD prices
- Law of one price holds for tradeable goods in USD terms
- Currency controls prevent arbitrage in YER terms

### H2: Aid Distribution Channel
- Cash aid depresses prices in recipient markets
- In-kind aid has larger price effects than cash
- Aid creates "Dutch disease in reverse"

### H3: Demand Destruction
- Population displacement reduces local demand
- Purchasing power collapse dominates supply constraints
- Effect strongest for non-essential goods

## 📈 Econometric Specifications to Test

### Main Specification (ALWAYS RUN FIRST)
```stata
* Local currency analysis
reg price_yer conflict exchange_rate global_price aid_pc i.market##i.commodity i.month, cluster(market)

* USD price analysis  
reg price_usd conflict global_price aid_pc i.market##i.commodity i.month, cluster(market)

* First differences (for non-stationarity)
reg D.price_yer D.conflict D.exchange_rate D.global_price D.aid_pc i.month, cluster(market)
```

### Identification Strategy
```stata
* IV for conflict (using spatial lags)
ivregress 2sls price_yer (conflict = conflict_neighbors) controls, cluster(market)

* RD at currency zone boundaries
rd price_yer distance_to_boundary if abs(distance) < bandwidth, fuzzy(houthi_control)
```

## 🗂️ Data Pipeline

```bash
# 1. ALWAYS start with exchange rate data
python scripts/build_exchange_rates.py --include-parallel-market

# 2. Process prices in BOTH currencies
python scripts/process_prices.py --currencies="YER,USD"

# 3. Validate with comparison
python scripts/compare_wfp_fao.py --output=data_validation_report.md

# 4. Run main specifications
python scripts/run_econometric_models.py --spec=main --currencies=both
```

## 📝 Paper Structure Mapping

1. **Introduction**: Exchange rate puzzle hooks reader
2. **Context**: Yemen's dual currency system 
3. **Data**: Emphasize novel exchange rate dataset
4. **Identification**: Currency zone discontinuity
5. **Results**: 
   - Table 1: Summary stats by currency zone
   - Table 2: Main results (YER vs USD)
   - Table 3: Mechanisms (aid, global prices)
   - Table 4: Robustness battery
6. **Policy**: Cash vs in-kind given FX regimes

## 🎬 Quick Commands

```bash
# Update all data
make update-data

# Run core analysis
make econometric-analysis

# Generate paper tables
make paper-tables

# Check data quality
python scripts/data_quality_report.py
```

## 🔍 Current Focus
**Week of [DATE]**: Testing exchange rate mechanism with triple-diff specification

## ❓ Open Questions
1. How to get parallel market rates for all markets?
2. Should we use trade-weighted exchange rates?
3. How to handle markets that switch control?
4. What instruments for aid distribution?

## 📚 Key References
- Cariolle et al. (2023) - Multi-currency zones in conflict
- Atkin & Donaldson (2015) - Law of one price tests
- Burke et al. (2023) - Aid in conflict settings

---
Remember: This is econometric research, not software development. Focus on identification, not code optimization.