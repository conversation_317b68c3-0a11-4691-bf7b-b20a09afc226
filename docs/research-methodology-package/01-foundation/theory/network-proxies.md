# Network Proxies from Existing Data

## Core Concept
Trader networks and supply chains critically affect price transmission but direct network data unavailable. We construct proxies from observable market characteristics that correlate with network density.

## Hypothesis N1: Network Density Moderates Price Transmission
Markets with denser trader networks show stronger price integration, especially within currency zones. Network effects amplify or dampen the impact of distance and currency boundaries.

## Three Network Proxies

### 1. Market Reporting Consistency
**Logic**: Markets reporting prices consistently have more active traders
```python
trader_density = price_reports_count / total_possible_periods
```
- High density (>0.8) = Well-connected market
- Low density (<0.4) = Isolated/thin market

### 2. Distance to Distribution Hubs
**Logic**: Proximity to major hubs indicates supply chain integration
- Primary hubs: Aden (south), Sana'a (north), Hodeidah (west)
- Markets <50km from hub = High network integration
- Markets >200km from hub = Low network integration

### 3. Pre-War Infrastructure
**Logic**: 2014 road density predicts current trader networks (persistence)
- Use OpenStreetMap 2014 snapshot or World Bank data
- Road density = km of paved roads / district area
- Assumption: Networks follow pre-existing infrastructure

## Implementation in Price Transmission Model
```stata
* Network-augmented price transmission
gen high_network = (trader_density > 0.8)
gen hub_proximity = (min_hub_distance < 50)

* Interaction with currency zones
reg price_corr c.distance##i.same_zone##i.high_network, cluster(market_pair)

* Expected results:
* - High network markets show stronger integration
* - Network effects larger within currency zones
* - Networks partially offset currency fragmentation
```

## Data Construction Steps
1. Calculate reporting frequency from WFP price data
2. Geocode distribution centers and calculate distances
3. Extract pre-war road density from infrastructure datasets
4. Create composite network index if needed