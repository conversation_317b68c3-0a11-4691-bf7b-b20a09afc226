# Spatial Price Transmission Under Currency Fragmentation

## Core Insight
Spatial price relationships fundamentally altered by currency zone boundaries. Traditional distance-decay models must incorporate currency discontinuities. The 4x exchange rate differential between zones creates economic distance that dominates physical distance.

## Hypothesis S1: Currency Boundaries Trump Geographic Distance
Price correlation stronger within currency zones than across, even controlling for physical distance. Markets 10km apart but in different currency zones show weaker integration than markets 200km apart in same zone.

## Implementation
- Spatial weight matrix: W_ij = exp(-d_ij/θ) × I(same_currency_zone)
- Conley HAC errors with 100km bandwidth
- Test: β(distance|same_zone) vs β(distance|different_zone)
- Expected: |β_same| > |β_different| despite shorter distances

## Empirical Strategy
```stata
* Generate spatial weights with currency zone interaction
gen same_zone = (zone_i == zone_j)
gen spatial_weight = exp(-distance/100) * same_zone

* Test differential effects
reg price_corr c.distance##i.same_zone, cluster(market_pair)
test distance + distance#1.same_zone = 0  // Effect within zones
test distance = 0                         // Effect across zones
```

## Data Requirements
- Market coordinates from WFP dataset ✓
- Currency zone classification from ACLED control ✓
- Distance calculation between all market pairs
- Panel structure for price correlations

## Key References
- <PERSON><PERSON> & Donaldson (2015): Law of one price and distance
- Fackler & Goodwin (2001): Spatial price transmission methodology