# Missing Data Methodology for Conflict Settings

**Target Audience**: Data Scientists, Econometricians  
**Module**: `yemen_market.data.missing_data`

## Overview

Missing data in conflict zones is often non-random and linked to conflict intensity or market accessibility. This document details advanced methods for handling missing data that go beyond simple listwise deletion or interpolation.

## Missing Data Mechanisms

### Missing Completely at Random (MCAR)

Data missing due to factors unrelated to observed or unobserved variables.
- **Example**: Random equipment failures
- **Implication**: Listwise deletion is unbiased but inefficient

### Missing at Random (MAR)

Missingness depends on observed variables but not unobserved values.
- **Example**: Markets stop reporting during high observed conflict
- **Implication**: Multiple imputation can provide unbiased estimates

### Missing Not at Random (MNAR)

Missingness depends on unobserved values of the missing variable itself.
- **Example**: Markets with extreme prices stop reporting
- **Implication**: Requires specialized methods (selection models)

## Conflict-Specific Missing Data Patterns

### Accessibility-Related Missingness

- **Cause**: Physical access restrictions due to conflict
- **Pattern**: Systematic missingness in high-conflict areas
- **Mechanism**: Likely MAR conditional on conflict measures

### Security-Related Missingness

- **Cause**: Safety concerns for data collectors
- **Pattern**: Missing during conflict escalations
- **Mechanism**: MAR conditional on security indicators

### Market Disruption Missingness

- **Cause**: Markets cease functioning during intense conflict
- **Pattern**: Complete missingness for extended periods
- **Mechanism**: Potentially MNAR if related to unobserved market conditions

## Advanced Missing Data Methods

### 1. Multiple Imputation using Chained Equations (MICE)

**Principle**: Create multiple plausible datasets, analyze each, pool results

**Implementation Strategy**:

1. **Specify imputation models** for each variable with missing data
2. **Include auxiliary variables** correlated with missingness
3. **Generate multiple datasets** (typically 20-100)
4. **Analyze each dataset** using standard methods
5. **Pool results** using Rubin's rules

**Key Requirements**:
- Rich set of auxiliary variables
- Proper specification of imputation models
- Sufficient iterations for convergence

**Auxiliary Variables for Yemen Context**:
- Conflict intensity and lags
- Market accessibility indicators
- Seasonal patterns
- Regional characteristics
- Infrastructure quality measures

### 2. Selection Models (Heckman-type)

**Principle**: Jointly model outcome and missingness probability

**Two-Equation System**:

**Selection Equation**:
```
Observed_ict = 1[α·Z_ict + u_ict > 0]
```

**Outcome Equation**:
```
Price_ict = β·X_ict + ε_ict  (observed only if Observed_ict = 1)
```

**Identification Requirements**:
- **Exclusion restriction**: Variable in Z not in X
- **Distributional assumptions**: Bivariate normality of errors

**Potential Exclusion Restrictions**:
- Distance to data collection center
- Communication infrastructure quality
- Historical reporting reliability

### 3. Pattern Mixture Models

**Principle**: Model outcomes separately by missing data pattern

**Implementation**:
1. **Identify patterns**: Classify observations by missingness pattern
2. **Estimate separately**: Model outcomes within each pattern
3. **Pool or compare**: Combine estimates or test differences

**Advantages**:
- No MAR assumption required
- Directly models pattern heterogeneity
- Transparent about assumptions

**Disadvantages**:
- Requires sufficient data in each pattern
- May be inefficient with many patterns

### 4. Sensitivity Analysis Framework

**Principle**: Compare results across different missing data assumptions

**Implementation Strategy**:

1. **Complete case analysis**: Listwise deletion baseline
2. **Simple imputation**: Forward/backward fill, interpolation
3. **MICE imputation**: Under MAR assumption
4. **Selection models**: Under MNAR assumption
5. **Pattern mixture**: Alternative MNAR approach

**Robustness Assessment**:
- **Consistent results**: High confidence in findings
- **Divergent results**: Sensitivity to missing data assumptions
- **Systematic patterns**: Identify most plausible mechanism

## Implementation Guidelines

### MICE Implementation

**Step 1: Data Preparation**
```python
# Include rich auxiliary variables
auxiliary_vars = [
    'conflict_intensity', 'conflict_lag1', 'conflict_lag2',
    'market_accessibility', 'distance_to_capital',
    'infrastructure_quality', 'population_density',
    'seasonal_indicators', 'regional_dummies'
]
```

**Step 2: Imputation Model Specification**
- **Continuous variables**: Predictive mean matching or linear regression
- **Binary variables**: Logistic regression
- **Categorical variables**: Multinomial logistic regression

**Step 3: Convergence Diagnostics**
- Trace plots of imputed values
- Potential scale reduction factors
- Between/within imputation variance

**Step 4: Pooling Results**
- Rubin's rules for parameter estimates
- Appropriate degrees of freedom calculation
- Multiple imputation inference

### Selection Model Implementation

**Step 1: Identify Exclusion Restrictions**
- Variables affecting missingness but not outcomes
- Test validity through overidentification

**Step 2: Joint Estimation**
- Maximum likelihood estimation
- Robust standard errors
- Convergence diagnostics

**Step 3: Model Diagnostics**
- Test for selection bias (ρ ≠ 0)
- Residual analysis
- Specification tests

## Validation and Diagnostics

### MICE Validation

**Convergence Checks**:
- Trace plots show mixing
- Gelman-Rubin statistics < 1.1
- Stable parameter estimates

**Plausibility Checks**:
- Imputed values within reasonable ranges
- Preserve relationships between variables
- Maintain distributional properties

**Sensitivity Analysis**:
- Vary number of imputations
- Different imputation models
- Alternative auxiliary variable sets

### Selection Model Validation

**Identification Tests**:
- Test exclusion restrictions
- Overidentification tests (if applicable)
- Weak instrument diagnostics

**Model Specification**:
- Test distributional assumptions
- Residual diagnostics
- Alternative functional forms

## Integration with Three-Tier Framework

### Tier 1: Pooled Panel Models

- Apply MICE to full 3D panel dataset
- Include commodity-specific imputation models
- Pool results across imputed datasets

### Tier 2: Threshold VECMs

- Handle missing data before threshold estimation
- Ensure balanced panels for VECM estimation
- Test robustness across missing data methods

### Tier 3: Factor Analysis

- Use MICE for factor extraction
- Compare factors across imputation methods
- Validate factor stability

## Reporting Standards

### Method Description

For each missing data method, report:
1. **Assumptions**: MCAR, MAR, or MNAR
2. **Implementation details**: Software, iterations, convergence
3. **Auxiliary variables**: Variables included in imputation models
4. **Diagnostics**: Convergence checks, validation tests

### Results Presentation

**Primary Results**: Based on most defensible method (typically MICE)
**Sensitivity Analysis**: Table comparing across methods
**Robustness Assessment**: Narrative discussion of consistency

### Transparency Requirements

- **Missing data patterns**: Descriptive statistics and visualizations
- **Mechanism assessment**: Evidence for MAR vs. MNAR
- **Method justification**: Rationale for chosen approach

## Computational Considerations

### MICE Performance

- **Memory requirements**: Multiple datasets in memory
- **Computation time**: Scales with iterations and variables
- **Parallelization**: Can parallelize across imputations

### Selection Model Performance

- **Convergence issues**: Non-linear optimization challenges
- **Identification problems**: Weak exclusion restrictions
- **Robustness**: Sensitive to distributional assumptions

## See Also

- [Data Validation Report](data_validation_report.md)
- [Quality Control Procedures](../sources/quality-control.md)
- [Python Implementation](../../04-implementation/code-examples/missing-data-implementations.py)
- [Advanced Robustness Checks](../../03-methodology/robustness/advanced-robustness-checks.md)
