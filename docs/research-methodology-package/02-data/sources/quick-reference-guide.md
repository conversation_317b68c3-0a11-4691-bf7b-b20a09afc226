# Yemen Market Analysis: Quick Data Reference Guide

## 🚀 Priority Data Sources (Start Here!)

### Exchange Rates - Critical for Analysis
1. **UN OCHA Exchange Rate Tracker** ⭐⭐⭐⭐⭐
   - URL: `data.humdata.org/dataset/yemen-exchange-rate`
   - Weekly updates, regional variation
   - Download: Direct CSV/Excel

2. **Yemen Cash Consortium** ⭐⭐⭐⭐
   - Monthly bulletins on ReliefWeb
   - Parallel market rates by governorate

### Price Data - Core Analysis
1. **WFP VAM Portal** ⭐⭐⭐⭐⭐
   - URL: `dataviz.vam.wfp.org/yemen`
   - 333 markets, 30+ commodities
   - API: Available with documentation

2. **FAO FPMA** ⭐⭐⭐⭐
   - URL: `www.fao.org/giews/food-prices`
   - Cross-validation source

### Aid Distribution - Key Variable
1. **OCHA 3W Database** ⭐⭐⭐⭐⭐
   - URL: `data.humdata.org/dataset/yemen-3w-operational-presence`
   - Cash vs in-kind breakdown
   - Monthly updates

### Conflict Events - Context
1. **ACLED** ⭐⭐⭐⭐⭐
   - URL: `acleddata.com`
   - Free academic access
   - Weekly updates, geocoded

## 📊 Quick Download Commands

```bash
# Exchange rates (UN OCHA)
curl -L "https://data.humdata.org/dataset/[DATASET-ID]/resource/[RESOURCE-ID]/download/exchange_rates.csv" -o exchange_rates.csv

# WFP Prices (via HDX Python)
pip install hdx-python-api
python -c "
from hdx.api.simple import Context
from hdx.data.dataset import Dataset
Context.create('yemen')
Dataset.read_from_hdx('wfp-food-prices-for-yemen').download()
"

# ACLED Data
# Register at acleddata.com for API key
curl "https://api.acleddata.com/acled/read?key=[YOUR-KEY]&country=Yemen&year=2024" -o acled_2024.json
```

## 🔑 Key Variables to Extract

### From Exchange Rate Data
- `parallel_rate_YER_USD` - Market rate
- `official_rate_YER_USD` - CBY rate
- `black_market_premium` = (parallel - official) / official

### From Price Data
- `price_YER` - Local currency price
- `price_USD` - Dollar equivalent
- `commodity_source` - Local vs imported

### From Aid Data
- `cash_beneficiaries` - Households receiving cash
- `cash_amount_USD` - Transfer value
- `inkind_MT` - Metric tons of food aid

### From Conflict Data
- `conflict_events` - Count by district-month
- `fatalities` - Conflict deaths
- `event_type` - Battles, air strikes, etc.

## ⚡ Minimal Working Example

```python
import pandas as pd
import requests

# 1. Get exchange rates
exchange_url = "https://data.humdata.org/[...]/exchange_rates.csv"
fx_data = pd.read_csv(exchange_url)

# 2. Get WFP prices
prices_url = "https://data.humdata.org/[...]/wfp_prices.csv"
price_data = pd.read_csv(prices_url)

# 3. Merge on date and location
analysis_data = pd.merge(
    price_data,
    fx_data,
    on=['date', 'governorate'],
    how='left'
)

# 4. Convert prices to USD
analysis_data['price_usd'] = (
    analysis_data['price_yer'] / 
    analysis_data['parallel_rate']
)

# 5. Basic regression
import statsmodels.api as sm
model = sm.OLS(
    analysis_data['price_usd'],
    analysis_data[['conflict_events', 'aid_dummy']]
).fit()
print(model.summary())
```

## 🚨 Critical Checks

1. **Currency Check**: Is price in YER or USD?
2. **Date Alignment**: Do price and FX rate match?
3. **Geographic Match**: Same market definition?
4. **Missing Pattern**: Why is data missing?
5. **Outlier Source**: Conflict or data error?

## 📱 Mobile-Friendly Sources

For field work or quick checks:
- **WFP Mobile**: m.vam.wfp.org
- **OCHA Reports**: reliefweb.int (mobile optimized)
- **Telegram Channels**: Exchange rate updates

## 🆘 Help Contacts

- **Technical Issues**: <EMAIL>
- **Data Access**: <EMAIL>
- **Research Collaboration**: [Your institutional contact]

---
*Last updated: January 2025 | Next review: March 2025*