# Network Proxy Calculations for Yemen Market Analysis

import pandas as pd
import numpy as np
from geopy.distance import geodesic

def create_network_proxies(price_data, market_locations, infrastructure_data=None):
    """
    Generate network strength indicators from available data
    
    Parameters:
    -----------
    price_data : DataFrame with columns [date, market, commodity, price]
    market_locations : DataFrame with columns [market, lat, lon]
    infrastructure_data : Optional DataFrame with pre-war road density
    
    Returns:
    --------
    DataFrame with network proxy variables by market
    """
    
    proxies = pd.DataFrame()
    proxies['market'] = market_locations['market'].unique()
    
    # Proxy 1: Market reporting consistency (trader density proxy)
    # Higher reporting frequency = more active traders
    total_periods = price_data['date'].nunique()
    reporting_freq = (
        price_data.groupby('market')['date']
        .nunique() / total_periods
    )
    proxies = proxies.merge(
        reporting_freq.rename('trader_density').reset_index(),
        on='market', how='left'
    )
    
    # Proxy 2: Distance to main distribution hubs
    distribution_hubs = {
        'Aden': (12.7855, 45.0187),      # Southern hub
        'Sanaa': (15.3694, 44.1910),     # Northern hub  
        'Hodeidah': (14.7973, 42.9549)   # Western port
    }
    
    # Calculate minimum distance to any hub
    for idx, market in market_locations.iterrows():
        market_coord = (market['lat'], market['lon'])
        min_distance = min([
            geodesic(market_coord, hub_coord).km 
            for hub_coord in distribution_hubs.values()
        ])
        proxies.loc[proxies['market'] == market['market'], 'min_hub_distance'] = min_distance
    
    # Create hub proximity indicator
    proxies['hub_proximity'] = (proxies['min_hub_distance'] < 50).astype(int)
    
    # Proxy 3: Pre-war road connectivity (if data available)
    if infrastructure_data is not None:
        proxies = proxies.merge(
            infrastructure_data[['market', 'road_density_2014']],
            on='market', how='left'
        )
    else:
        # Use hub proximity as fallback
        proxies['road_access_2014'] = proxies['hub_proximity']
    
    # Create composite network strength index
    proxies['network_strength'] = (
        proxies['trader_density'] * 0.4 +
        (1 - proxies['min_hub_distance']/proxies['min_hub_distance'].max()) * 0.3 +
        proxies.get('road_access_2014', proxies['hub_proximity']) * 0.3
    )
    
    # Categorical indicators for analysis
    proxies['high_network'] = (proxies['network_strength'] > 
                               proxies['network_strength'].median()).astype(int)
    
    return proxies

def calculate_network_distance_matrix(market_locations, network_proxies):
    """
    Create distance matrix weighted by network connectivity
    
    Network distance = geographic distance / (network_strength_i * network_strength_j)
    """
    n_markets = len(market_locations)
    network_distances = np.zeros((n_markets, n_markets))
    
    # Merge network strength
    markets_with_network = market_locations.merge(
        network_proxies[['market', 'network_strength']], 
        on='market'
    )
    
    for i in range(n_markets):
        for j in range(n_markets):
            if i != j:
                # Geographic distance
                coord1 = (markets_with_network.iloc[i]['lat'], 
                         markets_with_network.iloc[i]['lon'])
                coord2 = (markets_with_network.iloc[j]['lat'], 
                         markets_with_network.iloc[j]['lon'])
                geo_distance = geodesic(coord1, coord2).km
                
                # Network adjustment
                network_factor = (markets_with_network.iloc[i]['network_strength'] * 
                                 markets_with_network.iloc[j]['network_strength'])
                
                # Prevent division by zero
                network_distances[i,j] = geo_distance / max(network_factor, 0.1)
    
    return network_distances

# Example usage:
if __name__ == "__main__":
    # Example data structure
    print("Network proxy variables to be created:")
    print("1. trader_density: Share of periods with price reports")
    print("2. min_hub_distance: Distance to nearest major distribution center")
    print("3. hub_proximity: Binary indicator if <50km from hub")
    print("4. network_strength: Composite index (0-1)")
    print("5. high_network: Binary indicator for above-median network strength")