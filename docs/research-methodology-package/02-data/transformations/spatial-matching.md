# Spatial Data Matching Methodology

**Target Audience**: GIS Analysts, Data Scientists  
**Module**: `yemen_market.data.spatial_joins`

## Overview

This document details the spatial matching procedures used to integrate geographic data from multiple sources in the Yemen Market Integration analysis. Accurate spatial matching is crucial for linking conflict events, humanitarian access constraints, and administrative boundaries to market locations.

## Spatial Data Framework

### Coordinate Systems and Projections

```python
import geopandas as gpd
import pandas as pd
import numpy as np
from shapely.geometry import Point, Polygon, MultiPolygon
from shapely.ops import nearest_points
from scipy.spatial import cKDTree
from typing import Dict, List, Tuple, Optional, Union
import warnings
from pyproj import CRS, Transformer
import folium
from folium import plugins

class SpatialMatcher:
    """
    Comprehensive spatial matching for Yemen market data.
    
    Handles coordinate system transformations, spatial joins,
    and distance calculations.
    """
    
    def __init__(self, crs: str = 'EPSG:4326'):
        """
        Initialize spatial matcher.
        
        Parameters
        ----------
        crs : str
            Coordinate Reference System (default: WGS84)
        """
        self.crs = CRS(crs)
        self.transformers = {}
        self._load_base_geometries()
        
    def _load_base_geometries(self):
        """Load Yemen administrative boundaries and key geometries."""
        # Load admin boundaries
        self.admin_boundaries = {
            'country': self._load_country_boundary(),
            'governorate': self._load_governorate_boundaries(),
            'district': self._load_district_boundaries()
        }
        
        # Load infrastructure
        self.infrastructure = {
            'ports': self._load_port_locations(),
            'airports': self._load_airport_locations(),
            'roads': self._load_road_network(),
            'borders': self._load_border_crossings()
        }
        
        # Create spatial indices for efficiency
        self._create_spatial_indices()
    
    def match_markets_to_admin(
        self,
        markets: pd.DataFrame,
        admin_level: str = 'district'
    ) -> pd.DataFrame:
        """
        Match market locations to administrative boundaries.
        
        Parameters
        ----------
        markets : DataFrame
            Market data with latitude/longitude
        admin_level : str
            Administrative level ('governorate' or 'district')
        """
        # Convert markets to GeoDataFrame
        market_geometry = [
            Point(row['longitude'], row['latitude']) 
            for _, row in markets.iterrows()
        ]
        
        markets_gdf = gpd.GeoDataFrame(
            markets,
            geometry=market_geometry,
            crs=self.crs
        )
        
        # Spatial join with admin boundaries
        admin_gdf = self.admin_boundaries[admin_level]
        
        # First attempt: standard spatial join
        matched = gpd.sjoin(
            markets_gdf,
            admin_gdf,
            how='left',
            predicate='within'
        )
        
        # Handle markets on boundaries or with imprecise coordinates
        unmatched_mask = matched['index_right'].isna()
        
        if unmatched_mask.any():
            # Use nearest neighbor for unmatched markets
            unmatched_markets = markets_gdf[unmatched_mask]
            
            for idx, market in unmatched_markets.iterrows():
                nearest_admin = self._find_nearest_admin(
                    market.geometry,
                    admin_gdf
                )
                
                # Update matched dataframe
                for col in admin_gdf.columns:
                    if col != 'geometry':
                        matched.loc[idx, col] = nearest_admin[col]
        
        # Clean up columns
        matched = matched.drop(columns=['index_right'])
        
        # Add distance to admin center
        matched['distance_to_admin_center'] = matched.apply(
            lambda row: self._calculate_distance_to_center(
                row.geometry,
                admin_gdf[admin_gdf['admin_id'] == row['admin_id']].iloc[0].geometry
            ),
            axis=1
        )
        
        return matched
    
    def match_conflict_to_markets(
        self,
        conflict_events: pd.DataFrame,
        markets: pd.DataFrame,
        buffer_km: float = 50.0,
        method: str = 'buffer'
    ) -> pd.DataFrame:
        """
        Match conflict events to market catchment areas.
        
        Parameters
        ----------
        conflict_events : DataFrame
            Conflict data with coordinates
        markets : DataFrame
            Market locations
        buffer_km : float
            Buffer radius in kilometers
        method : str
            'buffer': Use circular buffer
            'voronoi': Use Voronoi polygons
            'kernel': Use kernel density
        """
        # Convert to GeoDataFrames
        conflict_gdf = self._create_geodataframe(
            conflict_events,
            'longitude',
            'latitude'
        )
        
        markets_gdf = self._create_geodataframe(
            markets,
            'longitude',
            'latitude'
        )
        
        if method == 'buffer':
            return self._buffer_matching(
                conflict_gdf,
                markets_gdf,
                buffer_km
            )
        elif method == 'voronoi':
            return self._voronoi_matching(
                conflict_gdf,
                markets_gdf
            )
        elif method == 'kernel':
            return self._kernel_density_matching(
                conflict_gdf,
                markets_gdf,
                buffer_km
            )
        else:
            raise ValueError(f"Unknown matching method: {method}")
    
    def _buffer_matching(
        self,
        events: gpd.GeoDataFrame,
        markets: gpd.GeoDataFrame,
        buffer_km: float
    ) -> pd.DataFrame:
        """Match events using circular buffers around markets."""
        # Project to Yemen TM for accurate distance calculations
        yemen_tm = CRS('EPSG:2090')  # Yemen Transverse Mercator
        
        markets_proj = markets.to_crs(yemen_tm)
        events_proj = events.to_crs(yemen_tm)
        
        # Create buffers (buffer_km converted to meters)
        market_buffers = markets_proj.copy()
        market_buffers['geometry'] = markets_proj.buffer(buffer_km * 1000)
        
        # Spatial join events to buffers
        matched = gpd.sjoin(
            events_proj,
            market_buffers,
            how='left',
            predicate='within'
        )
        
        # Calculate distances for matched events
        if 'market_id' in matched.columns:
            matched['distance_to_market'] = matched.apply(
                lambda row: row.geometry.distance(
                    markets_proj[markets_proj['market_id'] == row['market_id']].iloc[0].geometry
                ) / 1000  # Convert to km
                if pd.notna(row['market_id']) else np.nan,
                axis=1
            )
        
        # Handle events matched to multiple markets
        if matched.duplicated(subset=['event_id']).any():
            # Keep closest market
            matched = matched.sort_values('distance_to_market').drop_duplicates(
                subset=['event_id'],
                keep='first'
            )
        
        return matched
    
    def _voronoi_matching(
        self,
        events: gpd.GeoDataFrame,
        markets: gpd.GeoDataFrame
    ) -> pd.DataFrame:
        """Match events using Voronoi polygons (market influence areas)."""
        from scipy.spatial import Voronoi, voronoi_plot_2d
        import shapely.ops as ops
        
        # Get market coordinates
        market_coords = np.array([
            [geom.x, geom.y] for geom in markets.geometry
        ])
        
        # Add dummy points far outside to bound Voronoi regions
        bbox = markets.total_bounds
        dummy_points = [
            [bbox[0] - 10, bbox[1] - 10],  # SW
            [bbox[2] + 10, bbox[1] - 10],  # SE
            [bbox[2] + 10, bbox[3] + 10],  # NE
            [bbox[0] - 10, bbox[3] + 10],  # NW
        ]
        
        all_points = np.vstack([market_coords, dummy_points])
        
        # Create Voronoi diagram
        vor = Voronoi(all_points)
        
        # Convert Voronoi regions to polygons
        voronoi_polygons = []
        market_ids = []
        
        for idx, point_idx in enumerate(vor.point_region[:len(markets)]):
            region = vor.regions[point_idx]
            
            if -1 not in region and len(region) > 0:
                # Valid bounded region
                polygon_vertices = [vor.vertices[i] for i in region]
                poly = Polygon(polygon_vertices)
                
                # Clip to country boundary
                country_boundary = self.admin_boundaries['country'].geometry.iloc[0]
                clipped_poly = poly.intersection(country_boundary)
                
                voronoi_polygons.append(clipped_poly)
                market_ids.append(markets.iloc[idx]['market_id'])
        
        # Create GeoDataFrame of Voronoi cells
        voronoi_gdf = gpd.GeoDataFrame({
            'market_id': market_ids,
            'geometry': voronoi_polygons
        }, crs=markets.crs)
        
        # Match events to Voronoi cells
        matched = gpd.sjoin(
            events,
            voronoi_gdf,
            how='left',
            predicate='within'
        )
        
        # Calculate distances
        matched = self._add_distances(matched, markets)
        
        return matched
    
    def _kernel_density_matching(
        self,
        events: gpd.GeoDataFrame,
        markets: gpd.GeoDataFrame,
        bandwidth_km: float
    ) -> pd.DataFrame:
        """
        Match events using kernel density estimation.
        
        Assigns events to markets based on weighted influence.
        """
        from sklearn.neighbors import KernelDensity
        
        # Convert to projected coordinates
        yemen_tm = CRS('EPSG:2090')
        events_proj = events.to_crs(yemen_tm)
        markets_proj = markets.to_crs(yemen_tm)
        
        # Extract coordinates
        event_coords = np.array([
            [geom.x, geom.y] for geom in events_proj.geometry
        ])
        
        market_coords = np.array([
            [geom.x, geom.y] for geom in markets_proj.geometry
        ])
        
        # Calculate influence of each market on each event
        bandwidth = bandwidth_km * 1000  # Convert to meters
        
        influence_matrix = np.zeros((len(events), len(markets)))
        
        for i, market_coord in enumerate(market_coords):
            # Fit KDE centered on market
            kde = KernelDensity(
                bandwidth=bandwidth,
                kernel='gaussian'
            )
            kde.fit(market_coord.reshape(1, -1))
            
            # Calculate density at event locations
            log_density = kde.score_samples(event_coords)
            influence_matrix[:, i] = np.exp(log_density)
        
        # Normalize influences (each event sums to 1)
        row_sums = influence_matrix.sum(axis=1, keepdims=True)
        row_sums[row_sums == 0] = 1  # Avoid division by zero
        influence_matrix = influence_matrix / row_sums
        
        # Assign events to markets based on maximum influence
        assigned_markets = np.argmax(influence_matrix, axis=1)
        max_influences = np.max(influence_matrix, axis=1)
        
        # Create matched dataframe
        matched = events.copy()
        matched['market_id'] = [
            markets.iloc[idx]['market_id'] for idx in assigned_markets
        ]
        matched['influence_score'] = max_influences
        
        # Add distances
        matched = self._add_distances(matched, markets)
        
        # Flag weak assignments
        matched['weak_assignment'] = matched['influence_score'] < 0.1
        
        return matched
    
    def calculate_market_accessibility(
        self,
        markets: pd.DataFrame,
        infrastructure: Dict[str, gpd.GeoDataFrame]
    ) -> pd.DataFrame:
        """
        Calculate accessibility metrics for each market.
        
        Includes distance to ports, borders, main roads, etc.
        """
        markets_gdf = self._create_geodataframe(
            markets,
            'longitude',
            'latitude'
        )
        
        # Project for accurate distances
        yemen_tm = CRS('EPSG:2090')
        markets_proj = markets_gdf.to_crs(yemen_tm)
        
        accessibility = markets.copy()
        
        # Distance to nearest port
        if 'ports' in infrastructure:
            ports_proj = infrastructure['ports'].to_crs(yemen_tm)
            accessibility['nearest_port_km'] = markets_proj.geometry.apply(
                lambda geom: self._nearest_distance(geom, ports_proj) / 1000
            )
            
            # Identify nearest port
            accessibility['nearest_port_name'] = markets_proj.geometry.apply(
                lambda geom: self._nearest_feature_name(geom, ports_proj, 'port_name')
            )
        
        # Distance to nearest border crossing
        if 'borders' in infrastructure:
            borders_proj = infrastructure['borders'].to_crs(yemen_tm)
            accessibility['nearest_border_km'] = markets_proj.geometry.apply(
                lambda geom: self._nearest_distance(geom, borders_proj) / 1000
            )
        
        # Distance to main road
        if 'roads' in infrastructure:
            roads_proj = infrastructure['roads'].to_crs(yemen_tm)
            # Filter for main roads only
            main_roads = roads_proj[roads_proj['road_type'].isin(['primary', 'trunk'])]
            
            accessibility['nearest_main_road_km'] = markets_proj.geometry.apply(
                lambda geom: self._nearest_distance(geom, main_roads) / 1000
            )
        
        # Accessibility index (composite measure)
        accessibility['accessibility_index'] = self._calculate_accessibility_index(
            accessibility
        )
        
        # Identify isolated markets
        accessibility['is_isolated'] = (
            (accessibility['nearest_main_road_km'] > 50) |
            (accessibility['nearest_port_km'] > 200)
        )
        
        return accessibility
    
    def create_spatial_weights(
        self,
        locations: pd.DataFrame,
        method: str = 'distance',
        threshold: float = None,
        k: int = None
    ) -> Dict[str, np.ndarray]:
        """
        Create spatial weights matrix for econometric analysis.
        
        Parameters
        ----------
        locations : DataFrame
            Locations with coordinates
        method : str
            'distance': Inverse distance weights
            'knn': K-nearest neighbors
            'contiguity': Shared boundaries
        threshold : float
            Distance threshold for distance-based weights
        k : int
            Number of neighbors for KNN
        """
        # Convert to GeoDataFrame
        gdf = self._create_geodataframe(
            locations,
            'longitude',
            'latitude'
        )
        
        n = len(gdf)
        
        if method == 'distance':
            # Distance-based weights
            coords = np.array([[geom.x, geom.y] for geom in gdf.geometry])
            
            # Calculate distance matrix
            from scipy.spatial.distance import cdist
            dist_matrix = cdist(coords, coords, metric='euclidean')
            
            # Convert to km (approximate)
            dist_matrix *= 111  # degrees to km at Yemen latitude
            
            # Apply threshold if specified
            if threshold:
                dist_matrix[dist_matrix > threshold] = 0
            
            # Inverse distance weights
            with np.errstate(divide='ignore'):
                weights = 1 / dist_matrix
                weights[np.isinf(weights)] = 0
                
            # Row-normalize
            row_sums = weights.sum(axis=1, keepdims=True)
            row_sums[row_sums == 0] = 1
            weights = weights / row_sums
            
        elif method == 'knn':
            # K-nearest neighbors
            if k is None:
                k = min(5, n - 1)
            
            coords = np.array([[geom.x, geom.y] for geom in gdf.geometry])
            tree = cKDTree(coords)
            
            weights = np.zeros((n, n))
            
            for i in range(n):
                # Find k nearest neighbors
                distances, indices = tree.query(coords[i], k=k+1)
                
                # Exclude self
                neighbor_indices = indices[1:]
                neighbor_distances = distances[1:]
                
                # Inverse distance weights
                with np.errstate(divide='ignore'):
                    neighbor_weights = 1 / neighbor_distances
                    neighbor_weights[np.isinf(neighbor_weights)] = 1
                
                # Normalize
                neighbor_weights = neighbor_weights / neighbor_weights.sum()
                
                # Assign weights
                weights[i, neighbor_indices] = neighbor_weights
                
        elif method == 'contiguity':
            # Contiguity-based weights (requires polygon geometries)
            if 'admin_id' in locations.columns:
                # Use administrative boundaries
                admin_gdf = self.admin_boundaries['district']
                
                # Match locations to admin units
                location_admin = self.match_markets_to_admin(
                    locations,
                    'district'
                )
                
                # Create contiguity matrix
                weights = np.zeros((n, n))
                
                for i in range(n):
                    admin_i = location_admin.iloc[i]['admin_id']
                    geom_i = admin_gdf[admin_gdf['admin_id'] == admin_i].iloc[0].geometry
                    
                    for j in range(n):
                        if i != j:
                            admin_j = location_admin.iloc[j]['admin_id']
                            geom_j = admin_gdf[admin_gdf['admin_id'] == admin_j].iloc[0].geometry
                            
                            # Check if boundaries touch
                            if geom_i.touches(geom_j):
                                weights[i, j] = 1
                
                # Row-normalize
                row_sums = weights.sum(axis=1, keepdims=True)
                row_sums[row_sums == 0] = 1
                weights = weights / row_sums
        
        return {
            'W': weights,
            'method': method,
            'n': n,
            'sparsity': (weights == 0).sum() / (n * n),
            'avg_neighbors': (weights > 0).sum(axis=1).mean()
        }
```

### Conflict Event Aggregation

```python
class ConflictSpatialAggregator:
    """Aggregate conflict events to market areas with various methods."""
    
    def __init__(self, spatial_matcher: SpatialMatcher):
        """Initialize with spatial matcher instance."""
        self.matcher = spatial_matcher
        
    def aggregate_conflict_to_markets(
        self,
        conflict_events: pd.DataFrame,
        markets: pd.DataFrame,
        method: str = 'weighted_buffer',
        temporal_window: int = 30,
        **kwargs
    ) -> pd.DataFrame:
        """
        Aggregate conflict events to market-month observations.
        
        Parameters
        ----------
        conflict_events : DataFrame
            Conflict event data
        markets : DataFrame
            Market locations
        method : str
            Aggregation method
        temporal_window : int
            Days to aggregate events
        """
        if method == 'weighted_buffer':
            return self._weighted_buffer_aggregation(
                conflict_events,
                markets,
                **kwargs
            )
        elif method == 'travel_time':
            return self._travel_time_aggregation(
                conflict_events,
                markets,
                **kwargs
            )
        elif method == 'administrative':
            return self._administrative_aggregation(
                conflict_events,
                markets,
                **kwargs
            )
        else:
            raise ValueError(f"Unknown aggregation method: {method}")
    
    def _weighted_buffer_aggregation(
        self,
        events: pd.DataFrame,
        markets: pd.DataFrame,
        buffer_radii: List[float] = [10, 25, 50, 100],
        decay_function: str = 'exponential'
    ) -> pd.DataFrame:
        """
        Aggregate with distance-weighted buffers.
        
        Uses multiple buffer zones with decaying weights.
        """
        # Match events to markets
        matched_events = self.matcher.match_conflict_to_markets(
            events,
            markets,
            buffer_km=max(buffer_radii),
            method='buffer'
        )
        
        # Calculate weights based on distance
        if decay_function == 'exponential':
            # w = exp(-distance / scale)
            scale = buffer_radii[0]  # Use smallest radius as scale
            matched_events['weight'] = np.exp(
                -matched_events['distance_to_market'] / scale
            )
        elif decay_function == 'inverse':
            # w = 1 / (1 + distance / scale)
            scale = buffer_radii[0]
            matched_events['weight'] = 1 / (
                1 + matched_events['distance_to_market'] / scale
            )
        elif decay_function == 'step':
            # Step function based on buffer zones
            matched_events['weight'] = 0
            for i, radius in enumerate(sorted(buffer_radii)):
                in_zone = matched_events['distance_to_market'] <= radius
                if i == 0:
                    matched_events.loc[in_zone, 'weight'] = 1.0
                else:
                    prev_radius = sorted(buffer_radii)[i-1]
                    in_ring = (
                        (matched_events['distance_to_market'] > prev_radius) & 
                        in_zone
                    )
                    matched_events.loc[in_ring, 'weight'] = 1.0 / (i + 1)
        
        # Aggregate to market-month level
        matched_events['year_month'] = pd.to_datetime(
            matched_events['event_date']
        ).dt.to_period('M')
        
        aggregated = matched_events.groupby(
            ['market_id', 'year_month']
        ).agg({
            'event_id': 'count',  # Event count
            'fatalities': lambda x: (x * matched_events.loc[x.index, 'weight']).sum(),
            'weight': 'sum',  # Total weight
            'distance_to_market': 'mean'  # Average distance
        }).rename(columns={
            'event_id': 'conflict_events',
            'fatalities': 'weighted_fatalities',
            'weight': 'total_weight',
            'distance_to_market': 'avg_conflict_distance'
        })
        
        # Add conflict intensity measures
        aggregated['conflict_intensity'] = (
            aggregated['conflict_events'] + 
            0.1 * aggregated['weighted_fatalities']
        )
        
        # Normalize by area if needed
        market_areas = self._calculate_market_areas(markets, buffer_radii[-1])
        aggregated = aggregated.merge(
            market_areas[['market_id', 'area_km2']],
            on='market_id',
            how='left'
        )
        
        aggregated['conflict_density'] = (
            aggregated['conflict_intensity'] / aggregated['area_km2']
        )
        
        return aggregated
    
    def _travel_time_aggregation(
        self,
        events: pd.DataFrame,
        markets: pd.DataFrame,
        max_travel_hours: float = 4.0,
        road_network: gpd.GeoDataFrame = None
    ) -> pd.DataFrame:
        """
        Aggregate based on estimated travel time.
        
        More realistic than simple distance for mountainous Yemen.
        """
        # Load road network if not provided
        if road_network is None:
            road_network = self.matcher.infrastructure['roads']
        
        # Calculate travel time matrix
        travel_times = self._calculate_travel_time_matrix(
            events,
            markets,
            road_network
        )
        
        # Assign events based on travel time threshold
        assigned_events = []
        
        for i, event in events.iterrows():
            event_travel_times = travel_times[i]
            
            # Find markets within travel time threshold
            accessible_markets = np.where(
                event_travel_times <= max_travel_hours
            )[0]
            
            if len(accessible_markets) > 0:
                # Assign to closest market by travel time
                closest_market_idx = accessible_markets[
                    np.argmin(event_travel_times[accessible_markets])
                ]
                
                assigned_event = event.copy()
                assigned_event['market_id'] = markets.iloc[closest_market_idx]['market_id']
                assigned_event['travel_time_hours'] = event_travel_times[closest_market_idx]
                
                # Weight by inverse travel time
                assigned_event['weight'] = 1 / (1 + assigned_event['travel_time_hours'])
                
                assigned_events.append(assigned_event)
        
        # Convert to DataFrame and aggregate
        assigned_df = pd.DataFrame(assigned_events)
        
        # Aggregate by market-month
        assigned_df['year_month'] = pd.to_datetime(
            assigned_df['event_date']
        ).dt.to_period('M')
        
        aggregated = assigned_df.groupby(
            ['market_id', 'year_month']
        ).agg({
            'event_id': 'count',
            'fatalities': lambda x: (x * assigned_df.loc[x.index, 'weight']).sum(),
            'travel_time_hours': 'mean',
            'weight': 'sum'
        }).rename(columns={
            'event_id': 'conflict_events',
            'fatalities': 'weighted_fatalities',
            'travel_time_hours': 'avg_travel_time'
        })
        
        return aggregated
    
    def _calculate_travel_time_matrix(
        self,
        origins: pd.DataFrame,
        destinations: pd.DataFrame,
        road_network: gpd.GeoDataFrame
    ) -> np.ndarray:
        """
        Calculate travel time between origins and destinations.
        
        Simplified version - production code would use routing algorithms.
        """
        # Convert to projected coordinates
        yemen_tm = CRS('EPSG:2090')
        
        # Get coordinates
        origin_coords = np.array([
            [row['longitude'], row['latitude']] 
            for _, row in origins.iterrows()
        ])
        
        dest_coords = np.array([
            [row['longitude'], row['latitude']] 
            for _, row in destinations.iterrows()
        ])
        
        # Simple approximation: adjust Euclidean distance by terrain
        from scipy.spatial.distance import cdist
        
        # Calculate straight-line distances
        distances = cdist(origin_coords, dest_coords, metric='euclidean')
        distances *= 111  # Convert degrees to km
        
        # Adjust for terrain and road quality
        # Mountainous areas: reduce speed
        # Main roads: increase speed
        
        # Simplified terrain factor based on elevation variance
        terrain_factor = 1.5  # Average for Yemen's mountainous terrain
        
        # Convert to travel time (assuming 40 km/h average speed)
        avg_speed_kmh = 40 / terrain_factor
        travel_times = distances / avg_speed_kmh
        
        return travel_times
```

### Humanitarian Access Integration

```python
class HumanitarianAccessMatcher:
    """Match humanitarian access constraints to markets."""
    
    def __init__(self, spatial_matcher: SpatialMatcher):
        """Initialize with spatial matcher."""
        self.matcher = spatial_matcher
        
    def match_access_constraints(
        self,
        access_data: pd.DataFrame,
        markets: pd.DataFrame,
        method: str = 'administrative'
    ) -> pd.DataFrame:
        """
        Match humanitarian access constraints to markets.
        
        Parameters
        ----------
        access_data : DataFrame
            ACAPS or similar access constraint data
        markets : DataFrame
            Market locations
        method : str
            Matching method
        """
        if method == 'administrative':
            # Match based on administrative units
            return self._admin_based_matching(access_data, markets)
        elif method == 'nearest_incident':
            # Match to nearest access incident
            return self._incident_based_matching(access_data, markets)
        elif method == 'movement_restrictions':
            # Special handling for movement restrictions
            return self._movement_restriction_matching(access_data, markets)
    
    def _admin_based_matching(
        self,
        access_data: pd.DataFrame,
        markets: pd.DataFrame
    ) -> pd.DataFrame:
        """Match access constraints by administrative unit."""
        # Ensure markets have admin assignments
        if 'governorate_id' not in markets.columns:
            markets = self.matcher.match_markets_to_admin(
                markets,
                'governorate'
            )
        
        # Aggregate access data to governorate level
        access_by_gov = access_data.groupby(
            ['governorate_id', 'date']
        ).agg({
            'access_score': 'mean',  # Average access score
            'movement_restricted': 'max',  # Any restrictions
            'humanitarian_presence': 'sum',  # Total organizations
            'incident_count': 'sum'  # Total incidents
        }).reset_index()
        
        # Merge with markets
        matched = markets.merge(
            access_by_gov,
            on=['governorate_id', 'date'],
            how='left'
        )
        
        # Fill missing values
        matched['access_score'] = matched['access_score'].fillna(5)  # Assume moderate
        matched['movement_restricted'] = matched['movement_restricted'].fillna(0)
        matched['humanitarian_presence'] = matched['humanitarian_presence'].fillna(0)
        
        return matched
    
    def _movement_restriction_matching(
        self,
        access_data: pd.DataFrame,
        markets: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Match movement restrictions along supply routes.
        
        Critical for understanding price transmission.
        """
        # Identify key supply routes
        supply_routes = self._identify_supply_routes(markets)
        
        # Check for restrictions on each route
        route_restrictions = []
        
        for route in supply_routes:
            origin_market = route['origin']
            dest_market = route['destination']
            route_geometry = route['geometry']
            
            # Buffer route to catch nearby restrictions
            route_buffer = route_geometry.buffer(10000)  # 10km buffer
            
            # Find restrictions within buffer
            restrictions_gdf = self.matcher._create_geodataframe(
                access_data[access_data['restriction_type'] == 'movement'],
                'longitude',
                'latitude'
            )
            
            route_restrictions_mask = restrictions_gdf.geometry.within(route_buffer)
            route_restrictions_data = restrictions_gdf[route_restrictions_mask]
            
            if len(route_restrictions_data) > 0:
                route_restrictions.append({
                    'origin_market': origin_market,
                    'destination_market': dest_market,
                    'restriction_count': len(route_restrictions_data),
                    'restriction_severity': route_restrictions_data['severity'].max(),
                    'restricted_dates': route_restrictions_data['date'].unique()
                })
        
        # Convert to DataFrame
        route_restrictions_df = pd.DataFrame(route_restrictions)
        
        # Create market-level restriction indicators
        market_restrictions = []
        
        for market_id in markets['market_id'].unique():
            # Check if market is affected by route restrictions
            affected_routes = route_restrictions_df[
                (route_restrictions_df['origin_market'] == market_id) |
                (route_restrictions_df['destination_market'] == market_id)
            ]
            
            if len(affected_routes) > 0:
                market_restrictions.append({
                    'market_id': market_id,
                    'n_restricted_routes': len(affected_routes),
                    'max_restriction_severity': affected_routes['restriction_severity'].max(),
                    'supply_disruption_risk': len(affected_routes) / len(supply_routes)
                })
        
        market_restrictions_df = pd.DataFrame(market_restrictions)
        
        # Merge with markets
        return markets.merge(
            market_restrictions_df,
            on='market_id',
            how='left'
        ).fillna(0)
    
    def _identify_supply_routes(
        self,
        markets: pd.DataFrame
    ) -> List[Dict]:
        """Identify major supply routes between markets."""
        # Key supply routes based on Yemen's geography
        major_routes = [
            # Port to capital
            ('HODEIDAH', 'SANA'),  # Main import route
            ('ADEN', 'SANA'),      # Alternative southern route
            
            # Regional distribution
            ('SANA', 'TAIZ'),
            ('SANA', 'IBB'),
            ('SANA', 'MARIB'),
            ('ADEN', 'MUKALLA'),
            
            # Cross-line routes (between controlled areas)
            ('SANA', 'MARIB'),  # Houthi to Government
            ('HODEIDAH', 'TAIZ'),  # Critical contested route
        ]
        
        routes = []
        
        for origin, destination in major_routes:
            if origin in markets['market_id'].values and destination in markets['market_id'].values:
                origin_coords = markets[markets['market_id'] == origin][['longitude', 'latitude']].iloc[0]
                dest_coords = markets[markets['market_id'] == destination][['longitude', 'latitude']].iloc[0]
                
                # Create simple line geometry (production would use actual road network)
                route_line = LineString([
                    (origin_coords['longitude'], origin_coords['latitude']),
                    (dest_coords['longitude'], dest_coords['latitude'])
                ])
                
                routes.append({
                    'origin': origin,
                    'destination': destination,
                    'geometry': route_line,
                    'distance_km': route_line.length * 111,  # Approximate
                    'importance': 'high' if origin in ['HODEIDAH', 'ADEN'] else 'medium'
                })
        
        return routes
```

### Visualization and Quality Control

```python
class SpatialQualityControl:
    """Quality control for spatial matching results."""
    
    def __init__(self):
        """Initialize quality control tools."""
        self.validation_results = {}
        
    def validate_spatial_matches(
        self,
        matched_data: pd.DataFrame,
        original_data: pd.DataFrame,
        match_type: str
    ) -> Dict[str, any]:
        """
        Validate spatial matching results.
        
        Checks for common issues and anomalies.
        """
        validation = {
            'match_rate': self._calculate_match_rate(matched_data, original_data),
            'distance_distribution': self._analyze_distances(matched_data),
            'duplicate_matches': self._check_duplicates(matched_data),
            'spatial_outliers': self._detect_spatial_outliers(matched_data),
            'boundary_issues': self._check_boundary_issues(matched_data)
        }
        
        # Type-specific validation
        if match_type == 'conflict':
            validation['conflict_specific'] = self._validate_conflict_matches(matched_data)
        elif match_type == 'administrative':
            validation['admin_specific'] = self._validate_admin_matches(matched_data)
        
        # Generate quality score
        validation['quality_score'] = self._calculate_quality_score(validation)
        
        return validation
    
    def create_validation_map(
        self,
        matched_data: pd.DataFrame,
        base_map: folium.Map = None
    ) -> folium.Map:
        """Create interactive map for visual validation."""
        if base_map is None:
            # Create base map centered on Yemen
            base_map = folium.Map(
                location=[15.5527, 48.5164],
                zoom_start=6,
                tiles='OpenStreetMap'
            )
        
        # Add matched points
        for _, row in matched_data.iterrows():
            if pd.notna(row.get('latitude')) and pd.notna(row.get('longitude')):
                # Color based on match quality
                if 'match_quality' in row:
                    if row['match_quality'] == 'high':
                        color = 'green'
                    elif row['match_quality'] == 'medium':
                        color = 'orange'
                    else:
                        color = 'red'
                else:
                    color = 'blue'
                
                folium.CircleMarker(
                    location=[row['latitude'], row['longitude']],
                    radius=5,
                    popup=self._create_popup_text(row),
                    color=color,
                    fillColor=color,
                    fillOpacity=0.6
                ).add_to(base_map)
        
        # Add match connections if applicable
        if 'matched_to_lat' in matched_data.columns:
            for _, row in matched_data.iterrows():
                if all(pd.notna(row[col]) for col in 
                      ['latitude', 'longitude', 'matched_to_lat', 'matched_to_lon']):
                    
                    folium.PolyLine(
                        locations=[
                            [row['latitude'], row['longitude']],
                            [row['matched_to_lat'], row['matched_to_lon']]
                        ],
                        weight=1,
                        opacity=0.5,
                        color='gray'
                    ).add_to(base_map)
        
        # Add legend
        self._add_map_legend(base_map)
        
        return base_map
    
    def _calculate_match_rate(
        self,
        matched: pd.DataFrame,
        original: pd.DataFrame
    ) -> Dict[str, float]:
        """Calculate matching success rates."""
        total_original = len(original)
        total_matched = len(matched[matched['market_id'].notna()])
        
        return {
            'overall_rate': total_matched / total_original,
            'unmatched_count': total_original - total_matched,
            'match_percentage': (total_matched / total_original) * 100
        }
    
    def _analyze_distances(self, matched: pd.DataFrame) -> Dict[str, float]:
        """Analyze distance distribution of matches."""
        if 'distance_to_market' not in matched.columns:
            return {}
        
        distances = matched['distance_to_market'].dropna()
        
        return {
            'mean_distance': distances.mean(),
            'median_distance': distances.median(),
            'std_distance': distances.std(),
            'max_distance': distances.max(),
            'percentile_90': distances.quantile(0.9),
            'extreme_matches': (distances > distances.quantile(0.95)).sum()
        }
    
    def _detect_spatial_outliers(self, matched: pd.DataFrame) -> List[Dict]:
        """Detect unusual spatial matches."""
        outliers = []
        
        # Check for matches with extreme distances
        if 'distance_to_market' in matched.columns:
            distance_threshold = matched['distance_to_market'].quantile(0.95)
            extreme_distance = matched[
                matched['distance_to_market'] > distance_threshold
            ]
            
            for _, row in extreme_distance.iterrows():
                outliers.append({
                    'type': 'extreme_distance',
                    'id': row.get('event_id', row.get('id')),
                    'distance': row['distance_to_market'],
                    'market': row.get('market_id')
                })
        
        # Check for events matched to unexpected markets
        # (e.g., crossing front lines)
        if 'control_area' in matched.columns and 'market_control' in matched.columns:
            mismatched_control = matched[
                matched['control_area'] != matched['market_control']
            ]
            
            for _, row in mismatched_control.iterrows():
                outliers.append({
                    'type': 'control_mismatch',
                    'id': row.get('event_id', row.get('id')),
                    'event_control': row['control_area'],
                    'market_control': row['market_control']
                })
        
        return outliers
```

### Export and Integration

```python
def export_spatial_matches(
    matched_data: pd.DataFrame,
    output_format: str = 'geojson',
    output_path: str = None
) -> None:
    """Export spatial matching results."""
    if output_format == 'geojson':
        # Convert to GeoDataFrame if needed
        if not isinstance(matched_data, gpd.GeoDataFrame):
            geometry = [
                Point(row['longitude'], row['latitude']) 
                for _, row in matched_data.iterrows()
            ]
            matched_gdf = gpd.GeoDataFrame(
                matched_data,
                geometry=geometry,
                crs='EPSG:4326'
            )
        else:
            matched_gdf = matched_data
        
        matched_gdf.to_file(output_path, driver='GeoJSON')
        
    elif output_format == 'shapefile':
        matched_gdf.to_file(output_path)
        
    elif output_format == 'csv':
        # Include WKT geometry for CSV
        matched_data['geometry_wkt'] = matched_data.geometry.apply(
            lambda geom: geom.wkt if hasattr(geom, 'wkt') else None
        )
        matched_data.to_csv(output_path, index=False)
        
    elif output_format == 'parquet':
        # Efficient format that preserves geometry
        matched_data.to_parquet(output_path)

# Integration with panel builder
def integrate_spatial_data(
    panel_data: pd.DataFrame,
    spatial_matches: Dict[str, pd.DataFrame]
) -> pd.DataFrame:
    """Integrate all spatial matches into panel dataset."""
    integrated = panel_data.copy()
    
    # Add conflict data
    if 'conflict' in spatial_matches:
        integrated = integrated.merge(
            spatial_matches['conflict'],
            on=['market_id', 'year_month'],
            how='left',
            suffixes=('', '_conflict')
        )
    
    # Add access constraints
    if 'access' in spatial_matches:
        integrated = integrated.merge(
            spatial_matches['access'],
            on=['market_id', 'date'],
            how='left',
            suffixes=('', '_access')
        )
    
    # Add infrastructure distances
    if 'infrastructure' in spatial_matches:
        integrated = integrated.merge(
            spatial_matches['infrastructure'],
            on='market_id',
            how='left'
        )
    
    # Fill missing spatial indicators
    spatial_cols = [
        'conflict_intensity', 'movement_restricted',
        'nearest_port_km', 'accessibility_index'
    ]
    
    for col in spatial_cols:
        if col in integrated.columns:
            integrated[col] = integrated[col].fillna(0)
    
    return integrated
```

## See Also

- [Panel Construction](panel-construction.md) - Building integrated datasets
- [Missing Data](missing-data.md) - Handling spatial data gaps
- [Conflict Data Processing](../../03-api-reference/data/acled_processor.md) - ACLED integration
- [API Reference: Spatial Joins](../../03-api-reference/data/spatial_joiner.md)