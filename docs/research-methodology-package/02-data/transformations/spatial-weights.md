# Spatial Weights Matrix Construction

## Technical Implementation

### Distance Matrix Calculation
```python
import numpy as np
from geopy.distance import geodesic

def create_distance_matrix(markets_df):
    """Calculate great circle distances between all market pairs"""
    n_markets = len(markets_df)
    distances = np.zeros((n_markets, n_markets))
    
    for i in range(n_markets):
        for j in range(n_markets):
            if i != j:
                coord1 = (markets_df.iloc[i]['lat'], markets_df.iloc[i]['lon'])
                coord2 = (markets_df.iloc[j]['lat'], markets_df.iloc[j]['lon'])
                distances[i,j] = geodesic(coord1, coord2).km
    
    return distances
```

### Currency Zone Augmented Weights
```python
def create_currency_zone_weights(distances, zones, bandwidth=100):
    """
    Spatial weights incorporating currency zone boundaries
    W_ij = exp(-d_ij/bandwidth) × I(same_currency_zone)
    """
    n = len(zones)
    weights = np.zeros((n, n))
    
    for i in range(n):
        for j in range(n):
            if i != j:
                # Distance decay component
                distance_weight = np.exp(-distances[i,j] / bandwidth)
                
                # Currency zone indicator
                same_zone = 1 if zones[i] == zones[j] else 0
                
                # Combined weight
                weights[i,j] = distance_weight * same_zone
    
    # Row-standardize
    row_sums = weights.sum(axis=1, keepdims=True)
    weights = weights / (row_sums + 1e-10)
    
    return weights
```

### Conley HAC Implementation
```python
def conley_weights(distances, cutoff=100):
    """Bartlett kernel for Conley standard errors"""
    weights = np.maximum(0, 1 - distances/cutoff)
    return weights
```

## Required Market Data Structure
```
market_id | market_name | lat      | lon      | zone    | governorate
----------|-------------|----------|----------|---------|------------
YE001     | Sana'a City | 15.3694  | 44.1910  | Houthi  | Sana'a
YE002     | Aden Port   | 12.7855  | 45.0187  | Gov     | Aden
...
```

## Notes
- Bandwidth of 100km chosen based on Yemen's geography
- Zone classification from ACLED territorial control data
- Row standardization ensures weights sum to 1
- Zero weights across currency zones capture fragmentation