# Panel Data Construction Methodology

**Target Audience**: Data Scientists, Econometricians  
**Module**: `yemen_market.data.panel_builder`

## Overview

This document details the methodology for constructing the three-dimensional panel dataset (market × commodity × time) used in the Yemen Market Integration analysis. The process handles multiple data sources, missing values, and ensures temporal alignment for valid econometric analysis.

## Panel Structure

### Three-Dimensional Panel

The Yemen market data follows a hierarchical structure:

```
Panel[i,j,t] where:
  i ∈ {1, ..., 28} markets
  j ∈ {1, ..., 23} commodities  
  t ∈ {2019-01, ..., 2024-12} months
```

Total potential observations: 28 × 23 × 72 = 46,368

## Data Sources Integration

### Primary Data Sources

```python
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import logging
from datetime import datetime

@dataclass
class DataSource:
    """Container for data source information."""
    name: str
    frequency: str
    coverage: Dict[str, List[str]]
    reliability: float
    
class PanelBuilder:
    """
    Constructs integrated panel dataset from multiple sources.
    
    Handles:
    - WFP price data (primary)
    - ACLED conflict events
    - ACAPS humanitarian access
    - Global commodity prices
    """
    
    def __init__(self, config: Dict):
        """Initialize panel builder with configuration."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.data_sources = self._initialize_sources()
        
    def _initialize_sources(self) -> Dict[str, DataSource]:
        """Define data source characteristics."""
        return {
            'wfp': DataSource(
                name='WFP Food Prices',
                frequency='monthly',
                coverage={
                    'markets': ['all'],
                    'commodities': ['food', 'fuel'],
                    'period': ['2019-01', '2024-12']
                },
                reliability=0.95
            ),
            'acled': DataSource(
                name='ACLED Conflict Events',
                frequency='daily',
                coverage={
                    'spatial': ['lat', 'lon'],
                    'types': ['battles', 'explosions', 'protests'],
                    'period': ['2019-01', '2024-12']
                },
                reliability=0.90
            ),
            'acaps': DataSource(
                name='ACAPS Humanitarian Access',
                frequency='monthly',
                coverage={
                    'spatial': ['governorate'],
                    'indicators': ['access_constraints', 'movement_restrictions'],
                    'period': ['2019-01', '2024-12']
                },
                reliability=0.85
            ),
            'global': DataSource(
                name='Global Commodity Prices',
                frequency='daily',
                coverage={
                    'commodities': ['wheat', 'rice', 'oil', 'sugar'],
                    'markets': ['global'],
                    'period': ['2019-01', '2024-12']
                },
                reliability=0.98
            )
        }
    
    def build_panel(self, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Main method to construct panel dataset.
        
        Parameters
        ----------
        start_date : str
            Start date in YYYY-MM format
        end_date : str
            End date in YYYY-MM format
            
        Returns
        -------
        DataFrame
            Integrated panel dataset
        """
        self.logger.info(f"Building panel from {start_date} to {end_date}")
        
        # Step 1: Load and validate data
        price_data = self._load_price_data()
        conflict_data = self._load_conflict_data()
        access_data = self._load_access_data()
        global_data = self._load_global_prices()
        
        # Step 2: Create base panel structure
        base_panel = self._create_base_panel(start_date, end_date)
        
        # Step 3: Merge price data
        panel = self._merge_prices(base_panel, price_data)
        
        # Step 4: Add conflict indicators
        panel = self._add_conflict_measures(panel, conflict_data)
        
        # Step 5: Add access constraints
        panel = self._add_access_indicators(panel, access_data)
        
        # Step 6: Add global prices
        panel = self._add_global_prices(panel, global_data)
        
        # Step 7: Create derived variables
        panel = self._create_derived_variables(panel)
        
        # Step 8: Validate panel structure
        self._validate_panel(panel)
        
        return panel
```

### Creating Base Panel Structure

```python
def _create_base_panel(self, start_date: str, end_date: str) -> pd.DataFrame:
    """Create complete panel structure with all combinations."""
    # Define dimensions
    markets = self._get_market_list()
    commodities = self._get_commodity_list()
    
    # Create date range
    dates = pd.date_range(
        start=start_date,
        end=end_date,
        freq='MS'  # Month start
    )
    
    # Create all combinations
    from itertools import product
    
    panel_index = list(product(markets, commodities, dates))
    
    # Convert to DataFrame
    base_panel = pd.DataFrame(
        panel_index,
        columns=['market_id', 'commodity', 'date']
    )
    
    # Add market characteristics
    market_info = self._load_market_characteristics()
    base_panel = base_panel.merge(
        market_info,
        on='market_id',
        how='left'
    )
    
    # Add commodity characteristics
    commodity_info = self._load_commodity_characteristics()
    base_panel = base_panel.merge(
        commodity_info,
        on='commodity',
        how='left'
    )
    
    # Add time indicators
    base_panel['year'] = base_panel['date'].dt.year
    base_panel['month'] = base_panel['date'].dt.month
    base_panel['quarter'] = base_panel['date'].dt.quarter
    
    # Islamic calendar indicators
    base_panel = self._add_islamic_calendar(base_panel)
    
    self.logger.info(f"Created base panel with {len(base_panel)} observations")
    
    return base_panel

def _get_market_list(self) -> List[str]:
    """Get standardized market list."""
    return [
        'SANA', 'ADEN', 'TAIZ', 'HODEIDAH', 'IBB', 'DHAMAR',
        'HAJJAH', 'AMRAN', 'SADAH', 'MARIB', 'ALBAYDA',
        'SHABWAH', 'HADRAMAUT', 'ALMAHWIT', 'ALMAHRAH',
        'LAHJ', 'ABYAN', 'ALDALEH', 'RAYMAH', 'SOCOTRA'
        # ... complete list
    ]

def _get_commodity_list(self) -> List[str]:
    """Get standardized commodity list."""
    return [
        # Food items
        'wheat', 'wheat_flour', 'rice_imported', 'rice_local',
        'sugar', 'cooking_oil', 'vegetable_oil', 'tea', 'salt',
        
        # Proteins
        'beans_kidney_red', 'beans_white', 'lentils', 'eggs',
        'chicken_meat', 'beef_meat', 'fish',
        
        # Fuel
        'diesel', 'petrol', 'cooking_gas',
        
        # Other essentials
        'onions', 'tomatoes', 'potatoes'
    ]
```

### Price Data Integration

```python
def _merge_prices(
    self,
    base_panel: pd.DataFrame,
    price_data: pd.DataFrame
) -> pd.DataFrame:
    """Merge price data with quality checks."""
    # Standardize price data
    price_data = self._standardize_price_data(price_data)
    
    # Check price units and convert if needed
    price_data = self._standardize_price_units(price_data)
    
    # Merge with base panel
    panel = base_panel.merge(
        price_data[['market_id', 'commodity', 'date', 'price', 'currency']],
        on=['market_id', 'commodity', 'date'],
        how='left'
    )
    
    # Handle multiple price observations
    if panel.duplicated(subset=['market_id', 'commodity', 'date']).any():
        self.logger.warning("Duplicate prices found - taking median")
        panel = panel.groupby(
            ['market_id', 'commodity', 'date']
        ).agg({
            'price': 'median',
            'currency': 'first',
            **{col: 'first' for col in panel.columns 
               if col not in ['price', 'currency']}
        }).reset_index()
    
    # Currency conversion
    panel = self._convert_currencies(panel)
    
    # Add price transformations
    panel['log_price'] = np.log(panel['price'])
    panel['price_change'] = panel.groupby(
        ['market_id', 'commodity']
    )['price'].pct_change()
    
    # Price volatility measure
    panel['price_volatility'] = panel.groupby(
        ['market_id', 'commodity']
    )['price_change'].transform(
        lambda x: x.rolling(window=3, min_periods=1).std()
    )
    
    return panel

def _standardize_price_units(self, price_data: pd.DataFrame) -> pd.DataFrame:
    """Ensure consistent price units across commodities."""
    unit_conversions = {
        'wheat': {'kg': 1, 'g': 0.001, 'lb': 0.453592, '50kg': 50},
        'rice_imported': {'kg': 1, 'g': 0.001, 'lb': 0.453592},
        'cooking_oil': {'liter': 1, 'ml': 0.001, 'gallon': 3.78541},
        'diesel': {'liter': 1, 'gallon': 3.78541},
        # ... more conversions
    }
    
    for commodity, conversions in unit_conversions.items():
        commodity_mask = price_data['commodity'] == commodity
        
        for unit, factor in conversions.items():
            unit_mask = commodity_mask & (price_data['unit'] == unit)
            if unit_mask.any():
                # Convert to standard unit (kg for solids, liter for liquids)
                price_data.loc[unit_mask, 'price'] *= factor
                price_data.loc[unit_mask, 'unit'] = 'kg' if commodity != 'cooking_oil' else 'liter'
    
    return price_data
```

### Conflict Data Integration

```python
def _add_conflict_measures(
    self,
    panel: pd.DataFrame,
    conflict_data: pd.DataFrame
) -> pd.DataFrame:
    """Add conflict intensity measures."""
    # Spatial join conflict events to markets
    conflict_aggregated = self._aggregate_conflict_to_markets(
        conflict_data,
        buffer_km=50  # 50km radius around markets
    )
    
    # Create conflict intensity measures
    conflict_measures = conflict_aggregated.groupby(
        ['market_id', pd.Grouper(key='event_date', freq='MS')]
    ).agg({
        'event_id': 'count',  # Number of events
        'fatalities': 'sum',  # Total fatalities
        'event_type': lambda x: (x == 'Battles').sum()  # Battle count
    }).rename(columns={
        'event_id': 'conflict_events',
        'fatalities': 'conflict_fatalities',
        'event_type': 'battle_events'
    }).reset_index()
    
    conflict_measures.rename(columns={'event_date': 'date'}, inplace=True)
    
    # Create composite conflict intensity
    conflict_measures['conflict_intensity'] = (
        conflict_measures['conflict_events'] + 
        conflict_measures['conflict_fatalities'] * 0.1 +
        conflict_measures['battle_events'] * 2
    )
    
    # Add lagged conflict measures
    for lag in [1, 3, 6]:
        conflict_measures[f'conflict_lag{lag}'] = conflict_measures.groupby(
            'market_id'
        )['conflict_intensity'].shift(lag)
    
    # Merge with panel
    panel = panel.merge(
        conflict_measures,
        on=['market_id', 'date'],
        how='left'
    )
    
    # Fill missing conflict data with zeros
    conflict_cols = [
        'conflict_events', 'conflict_fatalities', 
        'battle_events', 'conflict_intensity'
    ]
    panel[conflict_cols] = panel[conflict_cols].fillna(0)
    
    return panel

def _aggregate_conflict_to_markets(
    self,
    conflict_data: pd.DataFrame,
    buffer_km: float
) -> pd.DataFrame:
    """Spatially aggregate conflict events to market areas."""
    from scipy.spatial import cKDTree
    
    # Get market coordinates
    markets = self._load_market_coordinates()
    
    # Build spatial index
    market_coords = markets[['longitude', 'latitude']].values
    tree = cKDTree(market_coords)
    
    # Find conflicts within buffer of each market
    conflict_coords = conflict_data[['longitude', 'latitude']].values
    
    # Query tree for all conflicts
    distances, indices = tree.query(
        conflict_coords,
        k=1,
        distance_upper_bound=buffer_km / 111  # Approximate km to degrees
    )
    
    # Assign conflicts to nearest market within buffer
    valid_mask = distances < float('inf')
    conflict_data['market_id'] = ''
    conflict_data.loc[valid_mask, 'market_id'] = markets.iloc[
        indices[valid_mask]
    ]['market_id'].values
    
    # Keep only matched conflicts
    return conflict_data[conflict_data['market_id'] != '']
```

### Missing Data Handling

```python
def _handle_missing_prices(
    self,
    panel: pd.DataFrame,
    method: str = 'mixed'
) -> pd.DataFrame:
    """
    Handle missing price observations.
    
    Methods:
    - 'interpolate': Linear interpolation within entity
    - 'forward_fill': Carry forward last observation
    - 'model_based': Predictive imputation
    - 'mixed': Combination approach
    """
    price_cols = ['price', 'log_price']
    
    if method == 'mixed':
        # Step 1: Forward fill for short gaps (≤ 2 months)
        for col in price_cols:
            panel[col] = panel.groupby(
                ['market_id', 'commodity']
            )[col].fillna(method='ffill', limit=2)
        
        # Step 2: Interpolate for medium gaps (3-6 months)
        for col in price_cols:
            panel[col] = panel.groupby(
                ['market_id', 'commodity']
            )[col].transform(
                lambda x: x.interpolate(
                    method='linear',
                    limit=6,
                    limit_direction='both'
                )
            )
        
        # Step 3: Model-based imputation for remaining
        remaining_missing = panel['price'].isna()
        
        if remaining_missing.any():
            panel.loc[remaining_missing, 'price'] = self._impute_with_model(
                panel,
                target_col='price',
                missing_mask=remaining_missing
            )
            
            # Recalculate log price
            panel.loc[remaining_missing, 'log_price'] = np.log(
                panel.loc[remaining_missing, 'price']
            )
    
    elif method == 'model_based':
        # Use predictive model for all missing
        missing_mask = panel['price'].isna()
        panel.loc[missing_mask, 'price'] = self._impute_with_model(
            panel, 'price', missing_mask
        )
    
    # Create missingness indicators
    panel['price_imputed'] = panel['price'].isna()
    
    return panel

def _impute_with_model(
    self,
    panel: pd.DataFrame,
    target_col: str,
    missing_mask: pd.Series
) -> np.ndarray:
    """Model-based imputation using similar markets/commodities."""
    from sklearn.ensemble import RandomForestRegressor
    
    # Features for imputation model
    feature_cols = [
        'year', 'month', 'conflict_intensity',
        'governorate_id', 'commodity_group',
        'global_price_index'
    ]
    
    # One-hot encode categoricals
    feature_data = pd.get_dummies(
        panel[feature_cols],
        columns=['governorate_id', 'commodity_group']
    )
    
    # Split train/test
    train_data = feature_data[~missing_mask]
    test_data = feature_data[missing_mask]
    
    train_target = panel.loc[~missing_mask, target_col]
    
    # Train model
    model = RandomForestRegressor(
        n_estimators=100,
        max_depth=10,
        random_state=42
    )
    
    model.fit(train_data, train_target)
    
    # Predict missing values
    predictions = model.predict(test_data)
    
    # Add noise to avoid perfect imputation
    noise = np.random.normal(0, 0.05 * np.std(train_target), len(predictions))
    predictions = predictions * (1 + noise)
    
    return predictions
```

### Panel Quality Validation

```python
def _validate_panel(self, panel: pd.DataFrame) -> Dict[str, Any]:
    """Comprehensive panel validation."""
    validation_results = {}
    
    # 1. Completeness check
    expected_obs = len(panel)
    actual_obs = panel['price'].notna().sum()
    validation_results['completeness'] = actual_obs / expected_obs
    
    # 2. Balance check
    balance_report = self._check_panel_balance(panel)
    validation_results['balance'] = balance_report
    
    # 3. Temporal consistency
    time_gaps = self._check_temporal_gaps(panel)
    validation_results['temporal_gaps'] = time_gaps
    
    # 4. Price validity
    price_issues = self._validate_prices(panel)
    validation_results['price_issues'] = price_issues
    
    # 5. Duplicate check
    duplicates = panel.duplicated(
        subset=['market_id', 'commodity', 'date']
    ).sum()
    validation_results['duplicates'] = duplicates
    
    # Log results
    self.logger.info(f"Panel validation results: {validation_results}")
    
    # Raise warnings for critical issues
    if validation_results['completeness'] < 0.7:
        self.logger.warning(
            f"Low data completeness: {validation_results['completeness']:.1%}"
        )
    
    if validation_results['duplicates'] > 0:
        raise ValueError(f"Found {duplicates} duplicate observations")
    
    return validation_results

def _check_panel_balance(self, panel: pd.DataFrame) -> Dict[str, float]:
    """Check panel balance across dimensions."""
    # Entity balance
    entity_counts = panel.groupby(
        ['market_id', 'commodity']
    ).size()
    
    max_periods = panel['date'].nunique()
    entity_balance = (entity_counts == max_periods).mean()
    
    # Time balance
    time_counts = panel.groupby('date').size()
    max_entities = len(panel['market_id'].unique()) * len(panel['commodity'].unique())
    time_balance = (time_counts == max_entities).mean()
    
    # Market balance
    market_counts = panel.groupby('market_id')['price'].count()
    market_balance = market_counts.std() / market_counts.mean()
    
    return {
        'entity_balance': entity_balance,
        'time_balance': time_balance,
        'market_balance_cv': market_balance,
        'strongly_balanced': entity_balance > 0.9 and time_balance > 0.9
    }

def _validate_prices(self, panel: pd.DataFrame) -> Dict[str, List]:
    """Validate price data quality."""
    issues = {
        'negative_prices': [],
        'extreme_values': [],
        'suspicious_changes': []
    }
    
    # Check for negative prices
    negative_mask = panel['price'] < 0
    if negative_mask.any():
        issues['negative_prices'] = panel[negative_mask][
            ['market_id', 'commodity', 'date', 'price']
        ].to_dict('records')
    
    # Check for extreme values (>5 std from mean by commodity)
    for commodity in panel['commodity'].unique():
        commodity_data = panel[panel['commodity'] == commodity]
        
        mean_price = commodity_data['price'].mean()
        std_price = commodity_data['price'].std()
        
        extreme_mask = np.abs(commodity_data['price'] - mean_price) > 5 * std_price
        
        if extreme_mask.any():
            issues['extreme_values'].extend(
                commodity_data[extreme_mask][
                    ['market_id', 'commodity', 'date', 'price']
                ].to_dict('records')
            )
    
    # Check for suspicious price changes (>200% month-to-month)
    price_changes = panel.groupby(
        ['market_id', 'commodity']
    )['price'].pct_change()
    
    suspicious_mask = np.abs(price_changes) > 2.0
    if suspicious_mask.any():
        issues['suspicious_changes'] = panel[suspicious_mask][
            ['market_id', 'commodity', 'date', 'price']
        ].to_dict('records')
    
    return issues
```

### Creating Analysis-Ready Features

```python
def _create_derived_variables(self, panel: pd.DataFrame) -> pd.DataFrame:
    """Create variables for econometric analysis."""
    # 1. Relative price measures
    # Market premium (price relative to national average)
    panel['national_avg_price'] = panel.groupby(
        ['commodity', 'date']
    )['price'].transform('mean')
    
    panel['market_premium'] = (
        panel['price'] / panel['national_avg_price'] - 1
    ) * 100
    
    # 2. Spatial price differences
    # Price relative to capital (Sana'a)
    capital_prices = panel[panel['market_id'] == 'SANA'][
        ['commodity', 'date', 'price']
    ].rename(columns={'price': 'capital_price'})
    
    panel = panel.merge(
        capital_prices,
        on=['commodity', 'date'],
        how='left'
    )
    
    panel['price_diff_capital'] = panel['price'] - panel['capital_price']
    
    # 3. Market integration measures
    # Rolling correlation with national average
    def rolling_correlation(group):
        return group['price'].rolling(
            window=12, min_periods=6
        ).corr(group['national_avg_price'])
    
    panel['price_correlation'] = panel.groupby(
        ['market_id', 'commodity']
    ).apply(rolling_correlation).reset_index(0, drop=True)
    
    # 4. Seasonal adjustments
    # Monthly seasonal factors
    seasonal_factors = panel.groupby(
        ['commodity', 'month']
    )['price'].transform('mean')
    
    overall_mean = panel.groupby('commodity')['price'].transform('mean')
    panel['seasonal_factor'] = seasonal_factors / overall_mean
    panel['price_deseasonalized'] = panel['price'] / panel['seasonal_factor']
    
    # 5. Conflict interaction terms
    panel['conflict_x_border'] = (
        panel['conflict_intensity'] * 
        panel['border_market'].astype(int)
    )
    
    panel['conflict_x_rural'] = (
        panel['conflict_intensity'] * 
        (1 - panel['urban'].astype(int))
    )
    
    # 6. Time trends
    panel['time_trend'] = panel.groupby(
        ['market_id', 'commodity']
    ).cumcount() + 1
    
    panel['time_trend_squared'] = panel['time_trend'] ** 2
    
    # 7. Exchange rate adjusted prices (if available)
    if 'exchange_rate' in panel.columns:
        panel['price_usd'] = panel['price'] / panel['exchange_rate']
        panel['log_price_usd'] = np.log(panel['price_usd'])
    
    return panel
```

### Balanced Panel Creation

```python
def create_balanced_panel(
    self,
    panel: pd.DataFrame,
    min_observations: int = 48,
    method: str = 'consecutive'
) -> pd.DataFrame:
    """
    Create balanced panel subset for specific analyses.
    
    Parameters
    ----------
    panel : DataFrame
        Full panel dataset
    min_observations : int
        Minimum observations required per entity
    method : str
        'consecutive': Require consecutive observations
        'total': Require total number of observations
    """
    if method == 'consecutive':
        # Find longest consecutive stretch for each entity
        def find_longest_stretch(group):
            # Create mask for non-missing prices
            not_missing = group['price'].notna()
            
            # Find consecutive groups
            consecutive_groups = not_missing.ne(
                not_missing.shift()
            ).cumsum()
            
            # Find longest group
            group_sizes = not_missing.groupby(consecutive_groups).sum()
            
            if group_sizes.empty or group_sizes.max() < min_observations:
                return pd.DataFrame()
            
            # Get longest stretch
            longest_group = group_sizes.idxmax()
            mask = (consecutive_groups == longest_group) & not_missing
            
            return group[mask]
        
        balanced = panel.groupby(
            ['market_id', 'commodity']
        ).apply(find_longest_stretch).reset_index(drop=True)
        
    else:  # method == 'total'
        # Keep entities with sufficient total observations
        entity_counts = panel.groupby(
            ['market_id', 'commodity']
        )['price'].count()
        
        valid_entities = entity_counts[
            entity_counts >= min_observations
        ].index
        
        balanced = panel.set_index(
            ['market_id', 'commodity']
        ).loc[valid_entities].reset_index()
    
    # Ensure common time period
    if not balanced.empty:
        common_start = balanced.groupby(
            ['market_id', 'commodity']
        )['date'].min().max()
        
        common_end = balanced.groupby(
            ['market_id', 'commodity']
        )['date'].max().min()
        
        balanced = balanced[
            (balanced['date'] >= common_start) &
            (balanced['date'] <= common_end)
        ]
    
    self.logger.info(
        f"Created balanced panel with {len(balanced)} observations "
        f"({len(balanced) / len(panel):.1%} of original)"
    )
    
    return balanced
```

### Export Functions

```python
def export_panel(
    self,
    panel: pd.DataFrame,
    format: str = 'stata',
    path: str = None
) -> None:
    """Export panel data in various formats."""
    if path is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        path = f"panel_data_{timestamp}"
    
    if format == 'stata':
        # Set panel structure
        panel_indexed = panel.set_index(['entity', 'date'])
        
        # Convert to Stata format
        panel_indexed.to_stata(
            f"{path}.dta",
            write_index=True,
            data_label="Yemen Market Integration Panel Data",
            variable_labels={
                'price': 'Commodity price (YER)',
                'log_price': 'Log of commodity price',
                'conflict_intensity': 'Conflict intensity index',
                'market_premium': 'Market premium (%)'
            }
        )
        
    elif format == 'csv':
        panel.to_csv(f"{path}.csv", index=False)
        
        # Also save metadata
        metadata = {
            'n_observations': len(panel),
            'n_markets': panel['market_id'].nunique(),
            'n_commodities': panel['commodity'].nunique(),
            'date_range': [
                panel['date'].min().strftime('%Y-%m-%d'),
                panel['date'].max().strftime('%Y-%m-%d')
            ],
            'completeness': panel['price'].notna().mean()
        }
        
        import json
        with open(f"{path}_metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
    
    elif format == 'parquet':
        panel.to_parquet(f"{path}.parquet", engine='pyarrow')
    
    self.logger.info(f"Exported panel data to {path}.{format}")
```

## See Also

- [Missing Data Handling](missing-data.md) - Detailed imputation methods
- [Spatial Matching](spatial-matching.md) - Geographic data integration
- [Panel Models](../econometric-models/panel-models.md) - Analysis methods
- [API Reference: Panel Builder](../../03-api-reference/data/panel_builder.md)