# Missing Data Handling Methodology

**Target Audience**: Data Scientists, Econometricians  
**Module**: `yemen_market.data.panel_builder`

## Overview

This document details comprehensive methods for handling missing data in the Yemen market panel dataset. Missing data in conflict settings presents unique challenges as missingness is often not random but directly related to conflict intensity and market accessibility.

## Missing Data Patterns

### Types of Missingness

```python
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import IterativeImputer
from sklearn.ensemble import RandomForestRegressor

class MissingDataAnalyzer:
    """
    Comprehensive missing data analysis for panel data.
    
    Identifies patterns and mechanisms of missingness.
    """
    
    def __init__(self, panel_data: pd.DataFrame):
        """Initialize with panel dataset."""
        self.data = panel_data
        self.missing_patterns = {}
        
    def analyze_missingness(self) -> Dict[str, any]:
        """Complete missing data analysis."""
        analysis = {
            'overall_missing': self._calculate_overall_missing(),
            'missing_by_variable': self._missing_by_variable(),
            'missing_patterns': self._identify_missing_patterns(),
            'missing_mechanism': self._test_missing_mechanism(),
            'temporal_patterns': self._analyze_temporal_patterns(),
            'spatial_patterns': self._analyze_spatial_patterns(),
            'conflict_correlation': self._test_conflict_correlation()
        }
        
        return analysis
    
    def _calculate_overall_missing(self) -> Dict[str, float]:
        """Calculate overall missing data statistics."""
        total_cells = self.data.size
        missing_cells = self.data.isna().sum().sum()
        
        # Missing by key variables
        price_missing = self.data['price'].isna().mean()
        conflict_missing = self.data['conflict_intensity'].isna().mean()
        
        # Complete cases
        complete_cases = (~self.data.isna().any(axis=1)).mean()
        
        return {
            'total_missing_pct': missing_cells / total_cells * 100,
            'price_missing_pct': price_missing * 100,
            'conflict_missing_pct': conflict_missing * 100,
            'complete_cases_pct': complete_cases * 100,
            'n_total_obs': len(self.data),
            'n_missing_obs': self.data['price'].isna().sum()
        }
    
    def _identify_missing_patterns(self) -> pd.DataFrame:
        """Identify common missing data patterns."""
        # Create missing indicator matrix
        missing_matrix = self.data.isna()
        
        # Find unique patterns
        patterns = missing_matrix.value_counts()
        
        # Convert to readable format
        pattern_df = pd.DataFrame({
            'pattern': patterns.index.astype(str),
            'count': patterns.values,
            'percentage': patterns.values / len(self.data) * 100
        })
        
        # Identify monotone missingness
        self._check_monotone_missingness()
        
        return pattern_df
    
    def _test_missing_mechanism(self) -> Dict[str, any]:
        """Test whether data is MCAR, MAR, or MNAR."""
        results = {}
        
        # Little's MCAR test
        mcar_result = self._littles_mcar_test()
        results['littles_mcar'] = mcar_result
        
        # Logistic regression test for MAR
        mar_test = self._test_mar_assumption()
        results['mar_test'] = mar_test
        
        # Pattern mixture test for MNAR
        mnar_indicators = self._test_mnar_indicators()
        results['mnar_indicators'] = mnar_indicators
        
        # Determine likely mechanism
        if mcar_result['p_value'] > 0.05:
            results['likely_mechanism'] = 'MCAR'
        elif mar_test['significant_predictors']:
            results['likely_mechanism'] = 'MAR'
        else:
            results['likely_mechanism'] = 'MNAR'
        
        return results
    
    def _littles_mcar_test(self) -> Dict[str, float]:
        """
        Little's MCAR test implementation.
        
        H0: Data is MCAR
        H1: Data is not MCAR
        """
        # Simplified implementation
        # Full implementation requires EM algorithm
        
        # For now, use pattern-based approximation
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns
        
        # Compare means across missing patterns
        chi2_stats = []
        
        for col in numeric_cols:
            if col in ['price', 'conflict_intensity']:
                # Group by missingness of other variables
                for other_col in ['price', 'conflict_intensity']:
                    if col != other_col:
                        missing_mask = self.data[other_col].isna()
                        
                        if missing_mask.sum() > 0 and (~missing_mask).sum() > 0:
                            group1 = self.data.loc[missing_mask, col].dropna()
                            group2 = self.data.loc[~missing_mask, col].dropna()
                            
                            if len(group1) > 1 and len(group2) > 1:
                                stat, p_value = stats.ttest_ind(group1, group2)
                                chi2_stats.append(stat**2)
        
        # Aggregate test statistic
        if chi2_stats:
            chi2_total = sum(chi2_stats)
            df = len(chi2_stats)
            p_value = 1 - stats.chi2.cdf(chi2_total, df)
        else:
            chi2_total = 0
            p_value = 1.0
        
        return {
            'test_statistic': chi2_total,
            'p_value': p_value,
            'reject_mcar': p_value < 0.05
        }
    
    def _test_mar_assumption(self) -> Dict[str, any]:
        """Test Missing at Random assumption using logistic regression."""
        from sklearn.linear_model import LogisticRegression
        
        results = {}
        
        # Test if price missingness can be predicted by observed variables
        price_missing = self.data['price'].isna().astype(int)
        
        # Predictors
        predictors = [
            'conflict_intensity', 'year', 'month',
            'urban', 'border_market', 'governorate_id'
        ]
        
        # Prepare data
        X = pd.get_dummies(
            self.data[predictors].fillna(0),
            columns=['governorate_id']
        )
        
        # Fit logistic regression
        model = LogisticRegression(random_state=42)
        model.fit(X, price_missing)
        
        # Get predictions and evaluate
        predictions = model.predict_proba(X)[:, 1]
        
        # Calculate pseudo R-squared
        null_log_likelihood = -len(price_missing) * np.log(0.5)
        model_log_likelihood = -np.sum(
            price_missing * np.log(predictions + 1e-10) +
            (1 - price_missing) * np.log(1 - predictions + 1e-10)
        )
        
        pseudo_r2 = 1 - model_log_likelihood / null_log_likelihood
        
        # Identify significant predictors
        coef_se = np.sqrt(np.diag(np.linalg.inv(X.T @ X + 1e-10 * np.eye(X.shape[1]))))
        z_scores = model.coef_[0] / (coef_se + 1e-10)
        p_values = 2 * (1 - stats.norm.cdf(np.abs(z_scores)))
        
        significant_predictors = [
            X.columns[i] for i, p in enumerate(p_values) if p < 0.05
        ]
        
        results['pseudo_r2'] = pseudo_r2
        results['significant_predictors'] = significant_predictors
        results['conflict_predicts_missing'] = 'conflict_intensity' in significant_predictors
        
        return results
    
    def _test_conflict_correlation(self) -> Dict[str, float]:
        """Test correlation between conflict and missingness."""
        # Create missing indicator
        price_missing = self.data['price'].isna()
        
        # Group by market and calculate correlations
        correlations = []
        
        for market in self.data['market_id'].unique():
            market_data = self.data[self.data['market_id'] == market]
            
            if len(market_data) > 10:
                corr = market_data['conflict_intensity'].corr(
                    market_data['price'].isna().astype(int)
                )
                if not np.isnan(corr):
                    correlations.append(corr)
        
        return {
            'mean_correlation': np.mean(correlations),
            'median_correlation': np.median(correlations),
            'positive_correlations_pct': sum(c > 0 for c in correlations) / len(correlations) * 100,
            'significant_positive_pct': sum(c > 0.3 for c in correlations) / len(correlations) * 100
        }
```

### Imputation Methods

```python
class MissingDataImputer:
    """
    Advanced imputation methods for panel data.
    
    Handles different missing data mechanisms with appropriate methods.
    """
    
    def __init__(self, panel_data: pd.DataFrame, method: str = 'auto'):
        """
        Initialize imputer.
        
        Parameters
        ----------
        panel_data : DataFrame
            Panel dataset
        method : str
            Imputation method or 'auto' for automatic selection
        """
        self.data = panel_data.copy()
        self.method = method
        self.imputation_log = []
        
    def impute(self) -> pd.DataFrame:
        """Main imputation method."""
        if self.method == 'auto':
            # Analyze missingness and select appropriate method
            analyzer = MissingDataAnalyzer(self.data)
            missing_analysis = analyzer.analyze_missingness()
            
            mechanism = missing_analysis['missing_mechanism']['likely_mechanism']
            
            if mechanism == 'MCAR':
                return self.impute_mcar()
            elif mechanism == 'MAR':
                return self.impute_mar()
            else:  # MNAR
                return self.impute_mnar()
        else:
            # Use specified method
            method_map = {
                'mean': self.impute_mean,
                'forward_fill': self.impute_forward_fill,
                'interpolation': self.impute_interpolation,
                'mice': self.impute_mice,
                'em': self.impute_em,
                'kalman': self.impute_kalman,
                'ml': self.impute_ml
            }
            
            if self.method in method_map:
                return method_map[self.method]()
            else:
                raise ValueError(f"Unknown imputation method: {self.method}")
    
    def impute_mcar(self) -> pd.DataFrame:
        """Imputation for Missing Completely at Random data."""
        # For MCAR, simple methods are unbiased
        return self.impute_mean_by_group()
    
    def impute_mar(self) -> pd.DataFrame:
        """Imputation for Missing at Random data."""
        # For MAR, use multiple imputation or ML methods
        return self.impute_mice()
    
    def impute_mnar(self) -> pd.DataFrame:
        """Imputation for Missing Not at Random data."""
        # For MNAR, use selection models or pattern mixture
        return self.impute_selection_model()
    
    def impute_mean_by_group(self) -> pd.DataFrame:
        """Group-wise mean imputation."""
        imputed = self.data.copy()
        
        # Impute by market-commodity groups
        price_cols = ['price', 'log_price']
        
        for col in price_cols:
            if col in imputed.columns:
                imputed[col] = imputed.groupby(
                    ['market_id', 'commodity']
                )[col].transform(lambda x: x.fillna(x.mean()))
        
        # Log imputation
        self._log_imputation(
            method='mean_by_group',
            n_imputed=self.data['price'].isna().sum() - imputed['price'].isna().sum()
        )
        
        return imputed
    
    def impute_forward_fill(self, limit: int = 2) -> pd.DataFrame:
        """Forward fill imputation with limit."""
        imputed = self.data.copy()
        
        # Sort by time
        imputed = imputed.sort_values(['market_id', 'commodity', 'date'])
        
        # Forward fill within groups
        price_cols = ['price', 'log_price']
        
        for col in price_cols:
            if col in imputed.columns:
                imputed[col] = imputed.groupby(
                    ['market_id', 'commodity']
                )[col].fillna(method='ffill', limit=limit)
        
        self._log_imputation(
            method=f'forward_fill_limit_{limit}',
            n_imputed=self.data['price'].isna().sum() - imputed['price'].isna().sum()
        )
        
        return imputed
    
    def impute_interpolation(self, method: str = 'linear') -> pd.DataFrame:
        """Time-based interpolation."""
        imputed = self.data.copy()
        
        # Ensure datetime index
        imputed = imputed.sort_values(['market_id', 'commodity', 'date'])
        
        # Interpolate within groups
        def interpolate_group(group):
            # Set time index
            group = group.set_index('date')
            
            # Interpolate numeric columns
            numeric_cols = group.select_dtypes(include=[np.number]).columns
            
            if method == 'linear':
                group[numeric_cols] = group[numeric_cols].interpolate(
                    method='time',
                    limit_direction='both'
                )
            elif method == 'spline':
                group[numeric_cols] = group[numeric_cols].interpolate(
                    method='spline',
                    order=3,
                    limit_direction='both'
                )
            elif method == 'seasonal':
                # Seasonal decomposition based interpolation
                for col in numeric_cols:
                    if group[col].notna().sum() > 24:  # Need sufficient data
                        group[col] = self._seasonal_interpolate(group[col])
            
            return group.reset_index()
        
        imputed = imputed.groupby(
            ['market_id', 'commodity']
        ).apply(interpolate_group).reset_index(drop=True)
        
        self._log_imputation(
            method=f'interpolation_{method}',
            n_imputed=self.data['price'].isna().sum() - imputed['price'].isna().sum()
        )
        
        return imputed
    
    def impute_mice(self, n_iterations: int = 10) -> pd.DataFrame:
        """
        Multiple Imputation by Chained Equations (MICE).
        
        Iterative imputation using conditional distributions.
        """
        from sklearn.experimental import enable_iterative_imputer
        from sklearn.impute import IterativeImputer
        
        imputed = self.data.copy()
        
        # Prepare data for MICE
        # Select numeric features
        numeric_features = [
            'price', 'conflict_intensity', 'year', 'month',
            'time_trend', 'global_price_index'
        ]
        
        # Add dummy variables for categories
        categorical_features = ['market_id', 'commodity']
        
        # Create feature matrix
        feature_data = pd.get_dummies(
            imputed[numeric_features + categorical_features],
            columns=categorical_features,
            drop_first=True
        )
        
        # Initialize MICE imputer
        imputer = IterativeImputer(
            estimator=RandomForestRegressor(
                n_estimators=10,
                max_depth=5,
                random_state=42
            ),
            max_iter=n_iterations,
            random_state=42,
            verbose=0
        )
        
        # Fit and transform
        imputed_values = imputer.fit_transform(feature_data)
        
        # Replace original values
        imputed['price'] = imputed_values[:, 0]
        
        # Recalculate log price
        imputed['log_price'] = np.log(imputed['price'])
        
        self._log_imputation(
            method=f'mice_iterations_{n_iterations}',
            n_imputed=self.data['price'].isna().sum() - imputed['price'].isna().sum()
        )
        
        return imputed
    
    def impute_kalman(self) -> pd.DataFrame:
        """Kalman filter/smoother imputation for time series."""
        from statsmodels.tsa.statespace.kalman_filter import KalmanFilter
        
        imputed = self.data.copy()
        
        def kalman_impute_group(group):
            """Apply Kalman filter to single time series."""
            # Extract price series
            prices = group.set_index('date')['price']
            
            if prices.notna().sum() < 10:
                return group
            
            # Simple state space model
            # Local level model: y_t = mu_t + e_t, mu_t = mu_{t-1} + eta_t
            
            # Create state space representation
            nobs = len(prices)
            
            # Design matrices
            design = np.array([[1.0]])
            transition = np.array([[1.0]])
            selection = np.array([[1.0]])
            
            # Initial state
            initial_state = prices.dropna().iloc[0]
            initial_state_cov = prices.dropna().var()
            
            # Observation and state covariances
            obs_cov = np.array([[prices.dropna().var() * 0.1]])
            state_cov = np.array([[prices.dropna().var() * 0.01]])
            
            # Create Kalman filter
            kf = KalmanFilter(
                k_endog=1,
                k_states=1,
                k_posdef=1,
                design=design,
                transition=transition,
                selection=selection,
                state_cov=state_cov,
                obs_cov=obs_cov
            )
            
            # Handle missing values
            endog = prices.values.reshape(-1, 1)
            endog[np.isnan(endog)] = 0  # Placeholder
            
            # Create missing data array
            missing = np.isnan(prices.values).reshape(-1, 1)
            
            # Filter and smooth
            kf.seek(0)
            results = kf.smooth(
                endog,
                missing=missing,
                initial_state=initial_state,
                initial_state_cov=initial_state_cov
            )
            
            # Extract smoothed values
            smoothed_prices = results[0].flatten()
            
            # Replace missing values
            group.loc[prices.index[prices.isna()], 'price'] = smoothed_prices[prices.isna()]
            
            return group
        
        # Apply to each market-commodity group
        imputed = imputed.groupby(
            ['market_id', 'commodity']
        ).apply(kalman_impute_group).reset_index(drop=True)
        
        # Recalculate log prices
        imputed['log_price'] = np.log(imputed['price'])
        
        self._log_imputation(
            method='kalman_filter',
            n_imputed=self.data['price'].isna().sum() - imputed['price'].isna().sum()
        )
        
        return imputed
    
    def impute_selection_model(self) -> pd.DataFrame:
        """
        Heckman-type selection model for MNAR data.
        
        Models the selection process explicitly.
        """
        from statsmodels.discrete.discrete_model import Probit
        from statsmodels.regression.linear_model import OLS
        import statsmodels.api as sm
        
        imputed = self.data.copy()
        
        # Step 1: Selection equation (probability of observing price)
        # Create selection indicator
        observed = (~imputed['price'].isna()).astype(int)
        
        # Selection variables (including exclusion restrictions)
        selection_vars = [
            'conflict_intensity', 'conflict_lag1', 'urban',
            'border_market', 'time_trend', 'month'
        ]
        
        # Prepare selection data
        X_select = imputed[selection_vars].fillna(0)
        X_select = sm.add_constant(X_select)
        
        # Estimate selection model
        selection_model = Probit(observed, X_select)
        selection_results = selection_model.fit(disp=0)
        
        # Calculate inverse Mills ratio
        from scipy.stats import norm
        
        fitted_probs = selection_results.predict(X_select)
        mills_ratio = norm.pdf(norm.ppf(fitted_probs)) / fitted_probs
        mills_ratio[fitted_probs > 0.999] = 0  # Avoid infinity
        
        # Step 2: Outcome equation (price model with Mills ratio)
        # Only for observed prices
        observed_mask = observed == 1
        
        outcome_vars = [
            'conflict_intensity', 'global_price_index',
            'urban', 'time_trend'
        ]
        
        X_outcome = imputed.loc[observed_mask, outcome_vars].fillna(0)
        X_outcome['mills_ratio'] = mills_ratio[observed_mask]
        X_outcome = sm.add_constant(X_outcome)
        
        y_outcome = imputed.loc[observed_mask, 'log_price']
        
        # Estimate outcome model
        outcome_model = OLS(y_outcome, X_outcome)
        outcome_results = outcome_model.fit()
        
        # Step 3: Predict missing values
        missing_mask = ~observed_mask
        
        if missing_mask.any():
            X_missing = imputed.loc[missing_mask, outcome_vars].fillna(0)
            X_missing['mills_ratio'] = mills_ratio[missing_mask]
            X_missing = sm.add_constant(X_missing)
            
            # Predict log prices
            predicted_log_prices = outcome_results.predict(X_missing)
            
            # Convert back to prices
            imputed.loc[missing_mask, 'log_price'] = predicted_log_prices
            imputed.loc[missing_mask, 'price'] = np.exp(predicted_log_prices)
            
            # Add uncertainty
            residual_std = np.sqrt(outcome_results.scale)
            noise = np.random.normal(0, residual_std * 0.5, size=missing_mask.sum())
            imputed.loc[missing_mask, 'price'] *= np.exp(noise)
        
        self._log_imputation(
            method='selection_model',
            n_imputed=missing_mask.sum(),
            selection_lambda=outcome_results.params.get('mills_ratio', np.nan)
        )
        
        return imputed
    
    def impute_ml(self, model_type: str = 'rf') -> pd.DataFrame:
        """Machine learning based imputation."""
        from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
        from sklearn.neural_network import MLPRegressor
        
        imputed = self.data.copy()
        
        # Feature engineering for ML
        features = self._engineer_features_for_imputation(imputed)
        
        # Select model
        if model_type == 'rf':
            model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                min_samples_leaf=5,
                random_state=42
            )
        elif model_type == 'gbm':
            model = GradientBoostingRegressor(
                n_estimators=100,
                max_depth=5,
                learning_rate=0.1,
                random_state=42
            )
        elif model_type == 'nn':
            model = MLPRegressor(
                hidden_layer_sizes=(100, 50),
                activation='relu',
                max_iter=500,
                random_state=42
            )
        
        # Prepare training data (observed prices)
        observed_mask = imputed['price'].notna()
        X_train = features[observed_mask]
        y_train = imputed.loc[observed_mask, 'log_price']
        
        # Fit model
        model.fit(X_train, y_train)
        
        # Predict missing values
        missing_mask = ~observed_mask
        if missing_mask.any():
            X_missing = features[missing_mask]
            predicted_log_prices = model.predict(X_missing)
            
            # Add prediction intervals
            if hasattr(model, 'estimators_'):  # Random Forest
                # Get predictions from each tree
                tree_predictions = np.array([
                    tree.predict(X_missing) for tree in model.estimators_
                ])
                
                # Calculate uncertainty
                prediction_std = np.std(tree_predictions, axis=0)
                
                # Add noise based on uncertainty
                noise = np.random.normal(0, prediction_std * 0.5)
                predicted_log_prices += noise
            
            # Update imputed values
            imputed.loc[missing_mask, 'log_price'] = predicted_log_prices
            imputed.loc[missing_mask, 'price'] = np.exp(predicted_log_prices)
        
        # Feature importance
        if hasattr(model, 'feature_importances_'):
            feature_importance = pd.DataFrame({
                'feature': X_train.columns,
                'importance': model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            self._log_imputation(
                method=f'ml_{model_type}',
                n_imputed=missing_mask.sum(),
                top_features=feature_importance.head(5).to_dict('records')
            )
        
        return imputed
    
    def _engineer_features_for_imputation(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create features for ML imputation."""
        features = pd.DataFrame()
        
        # Time features
        features['year'] = data['date'].dt.year
        features['month'] = data['date'].dt.month
        features['quarter'] = data['date'].dt.quarter
        features['days_from_start'] = (data['date'] - data['date'].min()).dt.days
        
        # Cyclical encoding of month
        features['month_sin'] = np.sin(2 * np.pi * features['month'] / 12)
        features['month_cos'] = np.cos(2 * np.pi * features['month'] / 12)
        
        # Market features (one-hot encoded)
        market_dummies = pd.get_dummies(data['market_id'], prefix='market')
        features = pd.concat([features, market_dummies], axis=1)
        
        # Commodity features (one-hot encoded)
        commodity_dummies = pd.get_dummies(data['commodity'], prefix='commodity')
        features = pd.concat([features, commodity_dummies], axis=1)
        
        # Conflict features
        features['conflict_intensity'] = data['conflict_intensity'].fillna(0)
        features['conflict_lag1'] = data['conflict_lag1'].fillna(0)
        features['conflict_ma3'] = data.groupby(['market_id'])['conflict_intensity'].transform(
            lambda x: x.rolling(3, min_periods=1).mean()
        ).fillna(0)
        
        # Market characteristics
        features['urban'] = data['urban'].fillna(0)
        features['border_market'] = data['border_market'].fillna(0)
        features['port_distance'] = data['port_distance'].fillna(data['port_distance'].median())
        
        # Global prices
        features['global_price_index'] = data['global_price_index'].fillna(
            data['global_price_index'].mean()
        )
        
        # Lagged prices (for same market-commodity)
        for lag in [1, 3, 6]:
            features[f'price_lag{lag}'] = data.groupby(
                ['market_id', 'commodity']
            )['price'].shift(lag).fillna(data['price'].mean())
        
        # Regional average prices
        features['regional_avg_price'] = data.groupby(
            ['governorate_id', 'commodity', 'date']
        )['price'].transform('mean').fillna(data['price'].mean())
        
        return features.fillna(0)
```

### Validation and Diagnostics

```python
class ImputationValidator:
    """Validate and assess imputation quality."""
    
    def __init__(self, original_data: pd.DataFrame, imputed_data: pd.DataFrame):
        """Initialize with original and imputed data."""
        self.original = original_data
        self.imputed = imputed_data
        
    def validate_imputation(self) -> Dict[str, any]:
        """Comprehensive imputation validation."""
        validation_results = {
            'distributional_checks': self._check_distributions(),
            'pattern_preservation': self._check_pattern_preservation(),
            'relationship_preservation': self._check_relationships(),
            'plausibility_checks': self._check_plausibility(),
            'cross_validation': self._cross_validate_imputation()
        }
        
        # Overall quality score
        validation_results['quality_score'] = self._calculate_quality_score(
            validation_results
        )
        
        return validation_results
    
    def _check_distributions(self) -> Dict[str, any]:
        """Check if imputed values preserve distributional properties."""
        results = {}
        
        # Compare distributions for key variables
        for var in ['price', 'log_price']:
            if var in self.original.columns:
                # Original distribution (non-missing only)
                orig_values = self.original[var].dropna()
                
                # All values after imputation
                imputed_values = self.imputed[var].dropna()
                
                # Newly imputed values only
                was_missing = self.original[var].isna()
                newly_imputed = self.imputed.loc[was_missing, var].dropna()
                
                # Statistical tests
                if len(newly_imputed) > 0:
                    # Kolmogorov-Smirnov test
                    ks_stat, ks_pvalue = stats.ks_2samp(orig_values, newly_imputed)
                    
                    # Compare moments
                    moments_comparison = {
                        'mean_diff': abs(newly_imputed.mean() - orig_values.mean()) / orig_values.mean(),
                        'std_diff': abs(newly_imputed.std() - orig_values.std()) / orig_values.std(),
                        'skew_diff': abs(stats.skew(newly_imputed) - stats.skew(orig_values)),
                        'kurt_diff': abs(stats.kurtosis(newly_imputed) - stats.kurtosis(orig_values))
                    }
                    
                    results[var] = {
                        'ks_statistic': ks_stat,
                        'ks_pvalue': ks_pvalue,
                        'distributions_similar': ks_pvalue > 0.05,
                        'moments_comparison': moments_comparison,
                        'n_imputed': len(newly_imputed)
                    }
        
        return results
    
    def _check_pattern_preservation(self) -> Dict[str, any]:
        """Check if temporal and spatial patterns are preserved."""
        results = {}
        
        # Temporal patterns
        temporal_corr_orig = []
        temporal_corr_imp = []
        
        for entity in self.original['entity'].unique()[:50]:  # Sample
            entity_orig = self.original[self.original['entity'] == entity]['price']
            entity_imp = self.imputed[self.imputed['entity'] == entity]['price']
            
            if len(entity_orig) > 10:
                # Autocorrelation at lag 1
                if entity_orig.notna().sum() > 10:
                    temporal_corr_orig.append(entity_orig.autocorr(lag=1))
                if entity_imp.notna().sum() > 10:
                    temporal_corr_imp.append(entity_imp.autocorr(lag=1))
        
        results['temporal_autocorr_preserved'] = abs(
            np.nanmean(temporal_corr_orig) - np.nanmean(temporal_corr_imp)
        ) < 0.1
        
        # Spatial patterns
        # Check correlation between markets
        market_corr_orig = self.original.pivot(
            index='date',
            columns='market_id',
            values='price'
        ).corr()
        
        market_corr_imp = self.imputed.pivot(
            index='date',
            columns='market_id',
            values='price'
        ).corr()
        
        # Compare correlation matrices
        corr_diff = np.abs(market_corr_orig - market_corr_imp).fillna(0)
        
        results['spatial_correlation_preserved'] = corr_diff.mean().mean() < 0.1
        results['max_correlation_change'] = corr_diff.max().max()
        
        return results
    
    def _check_plausibility(self) -> Dict[str, any]:
        """Check plausibility of imputed values."""
        results = {}
        
        # Check for impossible values
        impossible_prices = (self.imputed['price'] <= 0).sum()
        results['impossible_values'] = impossible_prices
        
        # Check for extreme values
        was_missing = self.original['price'].isna()
        newly_imputed = self.imputed.loc[was_missing, 'price']
        
        # Define extreme as > 3 std from commodity mean
        extreme_imputed = 0
        for commodity in self.imputed['commodity'].unique():
            commodity_data = self.imputed[self.imputed['commodity'] == commodity]
            commodity_mean = commodity_data['price'].mean()
            commodity_std = commodity_data['price'].std()
            
            commodity_imputed = newly_imputed[
                self.imputed.loc[was_missing, 'commodity'] == commodity
            ]
            
            extreme_mask = np.abs(commodity_imputed - commodity_mean) > 3 * commodity_std
            extreme_imputed += extreme_mask.sum()
        
        results['extreme_imputed_values'] = extreme_imputed
        results['extreme_imputed_pct'] = extreme_imputed / len(newly_imputed) * 100
        
        # Check monotonicity violations
        # Prices shouldn't jump too much between consecutive periods
        price_changes = self.imputed.groupby(
            ['market_id', 'commodity']
        )['price'].pct_change()
        
        extreme_changes = (np.abs(price_changes) > 0.5).sum()  # >50% change
        results['extreme_price_changes'] = extreme_changes
        
        return results
    
    def _cross_validate_imputation(self, n_folds: int = 5) -> Dict[str, float]:
        """Cross-validate imputation method on observed data."""
        from sklearn.model_selection import KFold
        from sklearn.metrics import mean_squared_error, mean_absolute_error
        
        # Use only complete observations for validation
        complete_mask = self.original['price'].notna()
        complete_data = self.original[complete_mask].copy()
        
        if len(complete_data) < 100:
            return {'error': 'Insufficient data for cross-validation'}
        
        kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)
        
        mse_scores = []
        mae_scores = []
        
        for train_idx, test_idx in kf.split(complete_data):
            # Create artificial missing data
            train_data = complete_data.copy()
            train_data.iloc[test_idx, train_data.columns.get_loc('price')] = np.nan
            
            # Impute using same method
            imputer = MissingDataImputer(train_data, method=self.method)
            imputed_data = imputer.impute()
            
            # Evaluate
            true_values = complete_data.iloc[test_idx]['price']
            imputed_values = imputed_data.iloc[test_idx]['price']
            
            mse = mean_squared_error(true_values, imputed_values)
            mae = mean_absolute_error(true_values, imputed_values)
            
            mse_scores.append(mse)
            mae_scores.append(mae)
        
        return {
            'cv_mse': np.mean(mse_scores),
            'cv_mse_std': np.std(mse_scores),
            'cv_mae': np.mean(mae_scores),
            'cv_mae_std': np.std(mae_scores),
            'cv_rmse': np.sqrt(np.mean(mse_scores))
        }
```

### Sensitivity Analysis

```python
def compare_imputation_methods(
    panel_data: pd.DataFrame,
    methods: List[str] = ['mean', 'forward_fill', 'interpolation', 'mice', 'ml']
) -> pd.DataFrame:
    """Compare multiple imputation methods."""
    results = []
    
    for method in methods:
        print(f"Testing {method} imputation...")
        
        # Impute
        imputer = MissingDataImputer(panel_data, method=method)
        imputed_data = imputer.impute()
        
        # Validate
        validator = ImputationValidator(panel_data, imputed_data)
        validation = validator.validate_imputation()
        
        # Run simple regression to check coefficient stability
        from yemen_market.models.three_tier import PooledPanelModel
        
        model = PooledPanelModel()
        regression_results = model.fit(
            imputed_data,
            outcome_var='log_price',
            treatment_var='conflict_intensity',
            control_vars=['log_global_price'],
            entity_effects=True,
            time_effects=True
        )
        
        results.append({
            'method': method,
            'missing_pct': panel_data['price'].isna().mean() * 100,
            'quality_score': validation.get('quality_score', np.nan),
            'coefficient': regression_results.params['conflict_intensity'],
            'std_error': regression_results.std_errors['conflict_intensity'],
            'cv_rmse': validation.get('cross_validation', {}).get('cv_rmse', np.nan)
        })
    
    comparison_df = pd.DataFrame(results)
    
    # Add ranking
    comparison_df['rank'] = comparison_df['quality_score'].rank(ascending=False)
    
    return comparison_df.sort_values('rank')
```

## See Also

- [Panel Construction](panel-construction.md) - Building complete panels
- [Outlier Detection](outlier-detection.md) - Identifying anomalies
- [Robustness Checks](../statistical-tests/robustness-checks.md) - Testing sensitivity
- [API Reference: Panel Builder](../../03-api-reference/data/panel_builder.md)