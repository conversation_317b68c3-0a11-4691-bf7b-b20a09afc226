# Outlier Detection Methodology

**Target Audience**: Data Scientists, Econometricians  
**Module**: `yemen_market.data.panel_builder`

## Overview

This document details comprehensive methods for detecting and handling outliers in the Yemen market price data. In conflict settings, distinguishing between genuine price shocks and data errors is crucial for valid econometric analysis.

## Outlier Detection Framework

### Multi-Method Detection Approach

```python
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.ensemble import IsolationForest
from sklearn.neighbors import LocalOutlierFactor
from sklearn.covariance import EllipticEnvelope
import warnings

class OutlierDetector:
    """
    Comprehensive outlier detection for panel price data.
    
    Combines statistical, machine learning, and domain-specific methods.
    """
    
    def __init__(
        self,
        panel_data: pd.DataFrame,
        price_var: str = 'price',
        sensitivity: float = 0.05
    ):
        """
        Initialize outlier detector.
        
        Parameters
        ----------
        panel_data : DataFrame
            Panel dataset
        price_var : str
            Price variable to analyze
        sensitivity : float
            Overall sensitivity level (0-1, higher = more outliers flagged)
        """
        self.data = panel_data.copy()
        self.price_var = price_var
        self.sensitivity = sensitivity
        self.outliers = {}
        self.detection_log = []
        
    def detect_outliers(self, methods: List[str] = None) -> pd.DataFrame:
        """
        Apply multiple outlier detection methods.
        
        Returns DataFrame with outlier flags and scores.
        """
        if methods is None:
            methods = [
                'statistical', 'isolation_forest', 'local_outlier_factor',
                'mahalanobis', 'time_series', 'economic', 'ensemble'
            ]
        
        # Initialize results
        outlier_results = pd.DataFrame(index=self.data.index)
        
        # Apply each method
        for method in methods:
            if method == 'statistical':
                outlier_results['statistical'] = self._statistical_outliers()
            elif method == 'isolation_forest':
                outlier_results['isolation_forest'] = self._isolation_forest_outliers()
            elif method == 'local_outlier_factor':
                outlier_results['lof'] = self._local_outlier_factor()
            elif method == 'mahalanobis':
                outlier_results['mahalanobis'] = self._mahalanobis_outliers()
            elif method == 'time_series':
                outlier_results['time_series'] = self._time_series_outliers()
            elif method == 'economic':
                outlier_results['economic'] = self._economic_outliers()
        
        # Ensemble approach
        if 'ensemble' in methods:
            outlier_results['ensemble'] = self._ensemble_outliers(outlier_results)
        
        # Add outlier scores
        outlier_results['outlier_score'] = self._calculate_outlier_scores(outlier_results)
        
        # Final classification
        threshold = self._calculate_threshold(outlier_results['outlier_score'])
        outlier_results['is_outlier'] = outlier_results['outlier_score'] > threshold
        
        # Log detection results
        self._log_detection_results(outlier_results)
        
        return outlier_results
    
    def _statistical_outliers(self) -> np.ndarray:
        """Statistical outlier detection using IQR and z-scores."""
        outliers = np.zeros(len(self.data), dtype=bool)
        
        # Group by commodity for commodity-specific thresholds
        for commodity in self.data['commodity'].unique():
            commodity_mask = self.data['commodity'] == commodity
            commodity_prices = self.data.loc[commodity_mask, self.price_var]
            
            if len(commodity_prices) < 10:
                continue
            
            # Method 1: Tukey's IQR method
            Q1 = commodity_prices.quantile(0.25)
            Q3 = commodity_prices.quantile(0.75)
            IQR = Q3 - Q1
            
            # Adjust bounds based on sensitivity
            multiplier = 1.5 + (1 - self.sensitivity) * 1.5  # 1.5 to 3.0
            lower_bound = Q1 - multiplier * IQR
            upper_bound = Q3 + multiplier * IQR
            
            iqr_outliers = (commodity_prices < lower_bound) | (commodity_prices > upper_bound)
            
            # Method 2: Modified Z-score (using median and MAD)
            median = commodity_prices.median()
            mad = np.median(np.abs(commodity_prices - median))
            
            if mad > 0:
                modified_z_scores = 0.6745 * (commodity_prices - median) / mad
                z_threshold = 3.5 - self.sensitivity * 1.5  # 2.0 to 3.5
                z_outliers = np.abs(modified_z_scores) > z_threshold
            else:
                z_outliers = pd.Series(False, index=commodity_prices.index)
            
            # Combine methods
            combined_outliers = iqr_outliers | z_outliers
            outliers[commodity_mask] = combined_outliers.values
        
        return outliers
    
    def _isolation_forest_outliers(self) -> np.ndarray:
        """Isolation Forest for multivariate outlier detection."""
        # Prepare features
        features = self._prepare_features_for_ml()
        
        # Handle missing values
        features_filled = features.fillna(features.median())
        
        # Initialize Isolation Forest
        contamination = self.sensitivity * 0.1  # Convert to contamination parameter
        iso_forest = IsolationForest(
            contamination=contamination,
            n_estimators=100,
            max_samples='auto',
            random_state=42
        )
        
        # Fit and predict
        outlier_labels = iso_forest.fit_predict(features_filled)
        
        # Convert to boolean (-1 = outlier, 1 = inlier)
        outliers = outlier_labels == -1
        
        # Get anomaly scores
        self.outliers['iso_forest_scores'] = iso_forest.score_samples(features_filled)
        
        return outliers
    
    def _local_outlier_factor(self) -> np.ndarray:
        """Local Outlier Factor for density-based detection."""
        features = self._prepare_features_for_ml()
        features_filled = features.fillna(features.median())
        
        # Number of neighbors based on data size
        n_neighbors = min(20, max(5, int(np.sqrt(len(features_filled)))))
        
        # Initialize LOF
        contamination = self.sensitivity * 0.1
        lof = LocalOutlierFactor(
            n_neighbors=n_neighbors,
            contamination=contamination,
            novelty=False
        )
        
        # Fit and predict
        outlier_labels = lof.fit_predict(features_filled)
        outliers = outlier_labels == -1
        
        # Store LOF scores
        self.outliers['lof_scores'] = lof.negative_outlier_factor_
        
        return outliers
    
    def _mahalanobis_outliers(self) -> np.ndarray:
        """Mahalanobis distance for multivariate outliers."""
        # Use numeric features only
        numeric_features = [
            self.price_var, 'conflict_intensity', 
            'time_trend', 'global_price_index'
        ]
        
        # Get available features
        available_features = [f for f in numeric_features if f in self.data.columns]
        feature_data = self.data[available_features].copy()
        
        # Handle missing values
        feature_data = feature_data.fillna(feature_data.median())
        
        # Standardize features
        feature_data_std = (feature_data - feature_data.mean()) / feature_data.std()
        
        # Calculate covariance matrix
        try:
            # Use robust covariance estimation
            robust_cov = EllipticEnvelope(
                contamination=self.sensitivity * 0.1,
                random_state=42
            )
            robust_cov.fit(feature_data_std)
            
            # Get Mahalanobis distances
            mahal_distances = robust_cov.mahalanobis(feature_data_std)
            
            # Threshold based on chi-square distribution
            df = len(available_features)
            threshold = stats.chi2.ppf(1 - self.sensitivity * 0.1, df)
            
            outliers = mahal_distances > threshold
            
            # Store distances
            self.outliers['mahalanobis_distances'] = mahal_distances
            
        except:
            # Fallback to simple method if robust estimation fails
            outliers = np.zeros(len(self.data), dtype=bool)
            
        return outliers
    
    def _time_series_outliers(self) -> np.ndarray:
        """Time series specific outlier detection."""
        outliers = np.zeros(len(self.data), dtype=bool)
        
        # Group by entity (market-commodity)
        for entity in self.data['entity'].unique():
            entity_mask = self.data['entity'] == entity
            entity_data = self.data[entity_mask].copy()
            
            if len(entity_data) < 10:
                continue
            
            # Sort by time
            entity_data = entity_data.sort_values('date')
            prices = entity_data[self.price_var].values
            
            # Method 1: STL decomposition outliers
            if len(prices) >= 24:  # Need sufficient data
                try:
                    from statsmodels.tsa.seasonal import STL
                    
                    # STL decomposition
                    stl = STL(prices, seasonal=13)  # 13 for monthly with yearly pattern
                    result = stl.fit()
                    
                    # Outliers in remainder component
                    remainder = result.resid
                    remainder_std = np.nanstd(remainder)
                    threshold = 3 - self.sensitivity
                    
                    stl_outliers = np.abs(remainder) > threshold * remainder_std
                    
                except:
                    stl_outliers = np.zeros(len(prices), dtype=bool)
            else:
                stl_outliers = np.zeros(len(prices), dtype=bool)
            
            # Method 2: Change point detection
            price_changes = np.diff(prices) / prices[:-1]
            change_threshold = 0.5 - self.sensitivity * 0.3  # 20% to 50% change
            
            large_changes = np.abs(price_changes) > change_threshold
            change_outliers = np.concatenate([[False], large_changes])
            
            # Method 3: ARIMA residuals (simplified)
            if len(prices) >= 20:
                try:
                    # Fit simple AR(1) model
                    from statsmodels.tsa.ar_model import AutoReg
                    
                    model = AutoReg(prices, lags=1, old_names=False)
                    results = model.fit()
                    residuals = results.resid
                    
                    # Outliers based on residuals
                    resid_std = np.nanstd(residuals)
                    resid_threshold = 3 - self.sensitivity
                    arima_outliers = np.abs(residuals) > resid_threshold * resid_std
                    
                    # Align with original data
                    arima_outliers = np.concatenate([[False], arima_outliers])
                    
                except:
                    arima_outliers = np.zeros(len(prices), dtype=bool)
            else:
                arima_outliers = np.zeros(len(prices), dtype=bool)
            
            # Combine time series methods
            combined_ts_outliers = stl_outliers | change_outliers | arima_outliers
            
            # Map back to original indices
            entity_indices = np.where(entity_mask)[0]
            outliers[entity_indices] = combined_ts_outliers
        
        return outliers
    
    def _economic_outliers(self) -> np.ndarray:
        """Economic theory based outlier detection."""
        outliers = np.zeros(len(self.data), dtype=bool)
        
        # 1. Law of One Price violations
        # Prices shouldn't differ too much across markets for same commodity/time
        for date in self.data['date'].unique():
            for commodity in self.data['commodity'].unique():
                mask = (self.data['date'] == date) & (self.data['commodity'] == commodity)
                
                if mask.sum() < 3:
                    continue
                
                period_prices = self.data.loc[mask, self.price_var]
                
                # Calculate deviation from median
                median_price = period_prices.median()
                price_deviations = np.abs(period_prices - median_price) / median_price
                
                # Flag extreme deviations
                deviation_threshold = 1.0 - self.sensitivity * 0.5  # 50% to 100%
                extreme_deviations = price_deviations > deviation_threshold
                
                outliers[mask] |= extreme_deviations.values
        
        # 2. Impossible price relationships
        # e.g., processed goods cheaper than raw materials
        if 'wheat' in self.data['commodity'].values and 'wheat_flour' in self.data['commodity'].values:
            for market in self.data['market_id'].unique():
                for date in self.data['date'].unique():
                    wheat_mask = (
                        (self.data['market_id'] == market) & 
                        (self.data['date'] == date) & 
                        (self.data['commodity'] == 'wheat')
                    )
                    flour_mask = (
                        (self.data['market_id'] == market) & 
                        (self.data['date'] == date) & 
                        (self.data['commodity'] == 'wheat_flour')
                    )
                    
                    if wheat_mask.sum() == 1 and flour_mask.sum() == 1:
                        wheat_price = self.data.loc[wheat_mask, self.price_var].iloc[0]
                        flour_price = self.data.loc[flour_mask, self.price_var].iloc[0]
                        
                        # Flour should be more expensive than wheat
                        if flour_price < wheat_price * 0.9:  # 10% margin
                            outliers[flour_mask] = True
        
        # 3. Conflict-price relationship anomalies
        # High conflict but unusually low prices (might indicate aid distribution)
        high_conflict = self.data['conflict_intensity'] > self.data['conflict_intensity'].quantile(0.75)
        
        for commodity in self.data['commodity'].unique():
            commodity_mask = self.data['commodity'] == commodity
            
            if (commodity_mask & high_conflict).sum() > 0:
                # Expected price in high conflict
                normal_price = self.data.loc[commodity_mask & ~high_conflict, self.price_var].median()
                conflict_prices = self.data.loc[commodity_mask & high_conflict, self.price_var]
                
                # Flag if price is much lower than expected
                unusually_low = conflict_prices < normal_price * 0.7
                
                outliers[commodity_mask & high_conflict] |= unusually_low.values
        
        return outliers
    
    def _ensemble_outliers(self, individual_results: pd.DataFrame) -> np.ndarray:
        """Ensemble method combining multiple outlier detectors."""
        # Get outlier columns (boolean)
        outlier_cols = [col for col in individual_results.columns 
                       if col not in ['outlier_score', 'is_outlier']]
        
        # Voting approach
        outlier_votes = individual_results[outlier_cols].sum(axis=1)
        
        # Dynamic threshold based on sensitivity
        min_votes = max(1, int(len(outlier_cols) * (0.3 - self.sensitivity * 0.2)))
        
        # Majority voting
        ensemble_outliers = outlier_votes >= min_votes
        
        # Store voting information
        self.outliers['ensemble_votes'] = outlier_votes
        self.outliers['ensemble_threshold'] = min_votes
        
        return ensemble_outliers.values
    
    def _prepare_features_for_ml(self) -> pd.DataFrame:
        """Prepare feature matrix for ML-based outlier detection."""
        features = pd.DataFrame()
        
        # Price features
        features['price'] = self.data[self.price_var]
        features['log_price'] = np.log1p(self.data[self.price_var])
        
        # Price dynamics
        features['price_change'] = self.data.groupby('entity')[self.price_var].pct_change()
        features['price_volatility'] = self.data.groupby('entity')['price_change'].transform(
            lambda x: x.rolling(3, min_periods=1).std()
        )
        
        # Market position
        features['price_vs_market_avg'] = self.data.groupby(['commodity', 'date'])[self.price_var].transform(
            lambda x: (x - x.mean()) / x.std() if x.std() > 0 else 0
        )
        
        # Time features
        features['month'] = self.data['date'].dt.month
        features['year'] = self.data['date'].dt.year
        features['time_trend'] = self.data.groupby('entity').cumcount()
        
        # Conflict features
        if 'conflict_intensity' in self.data.columns:
            features['conflict_intensity'] = self.data['conflict_intensity']
            features['conflict_ma3'] = self.data.groupby('market_id')['conflict_intensity'].transform(
                lambda x: x.rolling(3, min_periods=1).mean()
            )
        
        # Global prices
        if 'global_price_index' in self.data.columns:
            features['global_price_index'] = self.data['global_price_index']
            features['price_vs_global'] = (
                self.data[self.price_var] / self.data['global_price_index'] 
                if self.data['global_price_index'].min() > 0 else 0
            )
        
        return features
```

### Outlier Treatment Strategies

```python
class OutlierHandler:
    """Handle detected outliers with various treatment strategies."""
    
    def __init__(
        self,
        data: pd.DataFrame,
        outlier_flags: pd.DataFrame,
        price_var: str = 'price'
    ):
        """
        Initialize outlier handler.
        
        Parameters
        ----------
        data : DataFrame
            Original data
        outlier_flags : DataFrame
            Outlier detection results
        price_var : str
            Price variable to treat
        """
        self.data = data.copy()
        self.outlier_flags = outlier_flags
        self.price_var = price_var
        self.treatment_log = []
        
    def treat_outliers(
        self,
        method: str = 'winsorize',
        outlier_col: str = 'is_outlier'
    ) -> pd.DataFrame:
        """
        Apply outlier treatment method.
        
        Methods:
        - 'winsorize': Cap at percentiles
        - 'trim': Remove outliers
        - 'impute': Replace with imputed values
        - 'robust_transform': Transform to reduce influence
        - 'flag': Keep but flag for model
        """
        outlier_mask = self.outlier_flags[outlier_col]
        n_outliers = outlier_mask.sum()
        
        self._log_treatment(f"Treating {n_outliers} outliers using {method}")
        
        if method == 'winsorize':
            return self._winsorize_outliers(outlier_mask)
        elif method == 'trim':
            return self._trim_outliers(outlier_mask)
        elif method == 'impute':
            return self._impute_outliers(outlier_mask)
        elif method == 'robust_transform':
            return self._robust_transform(outlier_mask)
        elif method == 'flag':
            return self._flag_outliers(outlier_mask)
        else:
            raise ValueError(f"Unknown treatment method: {method}")
    
    def _winsorize_outliers(
        self,
        outlier_mask: pd.Series,
        limits: Tuple[float, float] = (0.01, 0.99)
    ) -> pd.DataFrame:
        """Winsorize outliers at specified percentiles."""
        treated_data = self.data.copy()
        
        # Winsorize by commodity
        for commodity in treated_data['commodity'].unique():
            commodity_mask = treated_data['commodity'] == commodity
            commodity_outliers = outlier_mask & commodity_mask
            
            if commodity_outliers.sum() > 0:
                # Calculate percentile limits
                commodity_prices = treated_data.loc[commodity_mask, self.price_var]
                lower_limit = commodity_prices.quantile(limits[0])
                upper_limit = commodity_prices.quantile(limits[1])
                
                # Apply winsorization
                outlier_values = treated_data.loc[commodity_outliers, self.price_var]
                treated_data.loc[commodity_outliers, self.price_var] = outlier_values.clip(
                    lower=lower_limit,
                    upper=upper_limit
                )
                
                # Recalculate log price
                treated_data.loc[commodity_outliers, 'log_price'] = np.log(
                    treated_data.loc[commodity_outliers, self.price_var]
                )
                
                # Log changes
                n_changed = (outlier_values != treated_data.loc[commodity_outliers, self.price_var]).sum()
                self._log_treatment(
                    f"Winsorized {n_changed} {commodity} prices to [{lower_limit:.2f}, {upper_limit:.2f}]"
                )
        
        return treated_data
    
    def _trim_outliers(self, outlier_mask: pd.Series) -> pd.DataFrame:
        """Remove outlier observations."""
        treated_data = self.data[~outlier_mask].copy()
        
        self._log_treatment(f"Removed {outlier_mask.sum()} outlier observations")
        
        # Check panel balance after trimming
        balance_before = self._calculate_panel_balance(self.data)
        balance_after = self._calculate_panel_balance(treated_data)
        
        self._log_treatment(
            f"Panel balance: {balance_before:.2%} -> {balance_after:.2%}"
        )
        
        return treated_data
    
    def _impute_outliers(self, outlier_mask: pd.Series) -> pd.DataFrame:
        """Replace outliers with imputed values."""
        from yemen_market.data.missing_data import MissingDataImputer
        
        treated_data = self.data.copy()
        
        # Set outliers to missing
        treated_data.loc[outlier_mask, self.price_var] = np.nan
        treated_data.loc[outlier_mask, 'log_price'] = np.nan
        
        # Impute using sophisticated method
        imputer = MissingDataImputer(treated_data, method='mice')
        treated_data = imputer.impute()
        
        self._log_treatment(f"Imputed {outlier_mask.sum()} outlier values")
        
        return treated_data
    
    def _robust_transform(self, outlier_mask: pd.Series) -> pd.DataFrame:
        """Apply robust transformation to reduce outlier influence."""
        treated_data = self.data.copy()
        
        # Use robust scaling for outliers
        for commodity in treated_data['commodity'].unique():
            commodity_mask = treated_data['commodity'] == commodity
            commodity_outliers = outlier_mask & commodity_mask
            
            if commodity_outliers.sum() > 0:
                # Get robust statistics
                commodity_prices = treated_data.loc[commodity_mask, self.price_var]
                median_price = commodity_prices.median()
                mad = np.median(np.abs(commodity_prices - median_price))
                
                # Transform outliers
                outlier_values = treated_data.loc[commodity_outliers, self.price_var]
                
                # Soft thresholding
                transformed = median_price + np.sign(outlier_values - median_price) * np.minimum(
                    np.abs(outlier_values - median_price),
                    3 * mad  # Cap at 3 MAD
                )
                
                treated_data.loc[commodity_outliers, self.price_var] = transformed
                treated_data.loc[commodity_outliers, 'log_price'] = np.log(transformed)
                
                self._log_treatment(
                    f"Robustly transformed {commodity_outliers.sum()} {commodity} outliers"
                )
        
        return treated_data
    
    def _flag_outliers(self, outlier_mask: pd.Series) -> pd.DataFrame:
        """Keep outliers but add indicator variables."""
        treated_data = self.data.copy()
        
        # Add outlier indicator
        treated_data['is_outlier'] = outlier_mask.astype(int)
        
        # Add outlier score if available
        if 'outlier_score' in self.outlier_flags.columns:
            treated_data['outlier_score'] = self.outlier_flags['outlier_score']
        
        # Create interaction terms
        treated_data['price_x_outlier'] = treated_data[self.price_var] * treated_data['is_outlier']
        
        self._log_treatment(
            f"Flagged {outlier_mask.sum()} outliers with indicator variables"
        )
        
        return treated_data
    
    def sensitivity_analysis(
        self,
        methods: List[str] = ['winsorize', 'trim', 'impute', 'flag']
    ) -> pd.DataFrame:
        """Compare different outlier treatment methods."""
        from yemen_market.models.three_tier import PooledPanelModel
        
        results = []
        
        for method in methods:
            # Treat outliers
            treated_data = self.treat_outliers(method=method)
            
            # Run baseline regression
            model = PooledPanelModel()
            regression_results = model.fit(
                treated_data,
                outcome_var='log_price',
                treatment_var='conflict_intensity',
                control_vars=['log_global_price'],
                entity_effects=True,
                time_effects=True
            )
            
            # Store results
            results.append({
                'method': method,
                'n_outliers': self.outlier_flags['is_outlier'].sum(),
                'n_obs_after': len(treated_data),
                'coefficient': regression_results.params['conflict_intensity'],
                'std_error': regression_results.std_errors['conflict_intensity'],
                't_statistic': regression_results.params['conflict_intensity'] / 
                              regression_results.std_errors['conflict_intensity'],
                'r_squared': regression_results.rsquared
            })
        
        comparison_df = pd.DataFrame(results)
        
        # Add relative changes
        baseline_coef = comparison_df.iloc[0]['coefficient']
        comparison_df['coef_change_pct'] = (
            (comparison_df['coefficient'] - baseline_coef) / baseline_coef * 100
        )
        
        return comparison_df
```

### Visualization and Reporting

```python
class OutlierVisualizer:
    """Visualization tools for outlier analysis."""
    
    def __init__(self, data: pd.DataFrame, outlier_results: pd.DataFrame):
        """Initialize visualizer."""
        self.data = data
        self.outlier_results = outlier_results
        
    def create_outlier_report(self, save_path: str = None) -> plt.Figure:
        """Create comprehensive outlier visualization report."""
        fig = plt.figure(figsize=(20, 15))
        
        # Create grid
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # 1. Outlier distribution by method
        ax1 = fig.add_subplot(gs[0, :2])
        self._plot_outlier_methods(ax1)
        
        # 2. Time series with outliers
        ax2 = fig.add_subplot(gs[1, :])
        self._plot_time_series_outliers(ax2)
        
        # 3. Outlier scores distribution
        ax3 = fig.add_subplot(gs[0, 2])
        self._plot_outlier_scores(ax3)
        
        # 4. Spatial distribution
        ax4 = fig.add_subplot(gs[2, 0])
        self._plot_spatial_outliers(ax4)
        
        # 5. Commodity breakdown
        ax5 = fig.add_subplot(gs[2, 1])
        self._plot_commodity_outliers(ax5)
        
        # 6. Economic context
        ax6 = fig.add_subplot(gs[2, 2])
        self._plot_economic_context(ax6)
        
        plt.suptitle('Outlier Detection Report', fontsize=16, y=0.98)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def _plot_outlier_methods(self, ax: plt.Axes):
        """Compare outlier detection methods."""
        method_cols = [col for col in self.outlier_results.columns 
                      if col not in ['outlier_score', 'is_outlier', 'ensemble_votes']]
        
        # Count outliers by method
        outlier_counts = self.outlier_results[method_cols].sum()
        
        # Create bar plot
        ax.bar(range(len(outlier_counts)), outlier_counts.values)
        ax.set_xticks(range(len(outlier_counts)))
        ax.set_xticklabels(outlier_counts.index, rotation=45)
        ax.set_ylabel('Number of Outliers')
        ax.set_title('Outliers Detected by Each Method')
        
        # Add percentage labels
        total_obs = len(self.outlier_results)
        for i, count in enumerate(outlier_counts.values):
            ax.text(i, count + 10, f'{count/total_obs:.1%}', 
                   ha='center', va='bottom')
    
    def _plot_time_series_outliers(self, ax: plt.Axes):
        """Plot price time series with outliers highlighted."""
        # Sample one commodity-market for clarity
        sample_entity = self.data['entity'].value_counts().index[0]
        entity_data = self.data[self.data['entity'] == sample_entity].copy()
        entity_outliers = self.outlier_results[self.data['entity'] == sample_entity]
        
        # Sort by date
        entity_data = entity_data.sort_values('date')
        entity_outliers = entity_outliers.loc[entity_data.index]
        
        # Plot price series
        ax.plot(entity_data['date'], entity_data['price'], 
               'b-', label='Price', alpha=0.7)
        
        # Highlight outliers
        outlier_mask = entity_outliers['is_outlier']
        ax.scatter(entity_data.loc[outlier_mask, 'date'],
                  entity_data.loc[outlier_mask, 'price'],
                  c='red', s=100, label='Outliers', zorder=5)
        
        # Add confidence bands
        rolling_mean = entity_data['price'].rolling(window=3, center=True).mean()
        rolling_std = entity_data['price'].rolling(window=3, center=True).std()
        
        ax.fill_between(entity_data['date'],
                       rolling_mean - 2*rolling_std,
                       rolling_mean + 2*rolling_std,
                       alpha=0.2, color='gray', label='±2 SD')
        
        ax.set_xlabel('Date')
        ax.set_ylabel('Price')
        ax.set_title(f'Time Series with Outliers: {sample_entity}')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_outlier_scores(self, ax: plt.Axes):
        """Distribution of outlier scores."""
        scores = self.outlier_results['outlier_score']
        
        # Histogram
        ax.hist(scores, bins=50, alpha=0.7, color='blue', edgecolor='black')
        
        # Add threshold line
        threshold = scores[self.outlier_results['is_outlier']].min()
        ax.axvline(threshold, color='red', linestyle='--', 
                  label=f'Threshold: {threshold:.2f}')
        
        ax.set_xlabel('Outlier Score')
        ax.set_ylabel('Frequency')
        ax.set_title('Distribution of Outlier Scores')
        ax.legend()
    
    def _plot_commodity_outliers(self, ax: plt.Axes):
        """Outliers by commodity."""
        outlier_by_commodity = self.data.groupby('commodity').apply(
            lambda x: self.outlier_results.loc[x.index, 'is_outlier'].sum()
        )
        
        # Sort and plot top 10
        top_commodities = outlier_by_commodity.nlargest(10)
        
        ax.barh(range(len(top_commodities)), top_commodities.values)
        ax.set_yticks(range(len(top_commodities)))
        ax.set_yticklabels(top_commodities.index)
        ax.set_xlabel('Number of Outliers')
        ax.set_title('Top 10 Commodities by Outlier Count')
        
        # Add counts
        for i, count in enumerate(top_commodities.values):
            ax.text(count + 1, i, str(int(count)), 
                   va='center', ha='left')
```

### Domain-Specific Outlier Rules

```python
def yemen_specific_outlier_rules(data: pd.DataFrame) -> pd.Series:
    """
    Apply Yemen market-specific outlier detection rules.
    
    Based on domain knowledge of conflict dynamics and market behavior.
    """
    outliers = pd.Series(False, index=data.index)
    
    # Rule 1: Fuel price spikes during blockades
    fuel_commodities = ['diesel', 'petrol', 'cooking_gas']
    
    for fuel in fuel_commodities:
        if fuel in data['commodity'].values:
            fuel_mask = data['commodity'] == fuel
            fuel_data = data[fuel_mask]
            
            # Check for sudden spikes (>100% increase)
            price_changes = fuel_data.groupby('market_id')['price'].pct_change()
            
            # During known blockade periods, higher tolerance
            blockade_periods = [
                ('2020-01-01', '2020-03-31'),
                ('2021-06-01', '2021-08-31')
            ]
            
            for start, end in blockade_periods:
                period_mask = (fuel_data['date'] >= start) & (fuel_data['date'] <= end)
                
                # Outside blockade: >100% change is outlier
                # During blockade: >200% change is outlier
                if (~period_mask).any():
                    normal_outliers = (price_changes > 1.0) & ~period_mask
                    outliers[fuel_mask] |= normal_outliers
                
                if period_mask.any():
                    blockade_outliers = (price_changes > 2.0) & period_mask
                    outliers[fuel_mask] |= blockade_outliers
    
    # Rule 2: Import-dependent commodities during port closures
    import_dependent = ['wheat', 'rice_imported', 'sugar', 'cooking_oil']
    port_cities = ['HODEIDAH', 'ADEN', 'MUKALLA']
    
    # Port closure periods
    port_closures = [
        ('HODEIDAH', '2018-06-01', '2018-12-31'),
        ('ADEN', '2019-08-01', '2019-09-30')
    ]
    
    for port, start, end in port_closures:
        closure_mask = (data['date'] >= start) & (data['date'] <= end)
        
        # Check prices in port city
        port_mask = (data['market_id'] == port) & closure_mask
        
        for commodity in import_dependent:
            commodity_mask = data['commodity'] == commodity
            
            if (port_mask & commodity_mask).any():
                # Compare to pre-closure prices
                pre_closure = data[
                    (data['market_id'] == port) & 
                    (data['commodity'] == commodity) &
                    (data['date'] < start) &
                    (data['date'] >= pd.Timestamp(start) - pd.DateOffset(months=3))
                ]['price'].mean()
                
                closure_prices = data.loc[port_mask & commodity_mask, 'price']
                
                # Flag if price more than doubles
                extreme_prices = closure_prices > pre_closure * 2.5
                outliers[port_mask & commodity_mask] |= extreme_prices
    
    # Rule 3: Aid distribution effects
    # Sudden price drops in known aid recipient areas
    aid_distributions = [
        ('TAIZ', '2020-03-15', 'wheat_flour'),
        ('IBB', '2021-01-10', 'cooking_oil')
    ]
    
    for market, date, commodity in aid_distributions:
        # Check for price drop in specific market/commodity
        mask = (
            (data['market_id'] == market) & 
            (data['commodity'] == commodity) &
            (data['date'] == pd.Timestamp(date))
        )
        
        if mask.any():
            # Compare to previous month
            prev_month = pd.Timestamp(date) - pd.DateOffset(months=1)
            prev_mask = (
                (data['market_id'] == market) & 
                (data['commodity'] == commodity) &
                (data['date'] == prev_month)
            )
            
            if prev_mask.any():
                prev_price = data.loc[prev_mask, 'price'].iloc[0]
                curr_price = data.loc[mask, 'price'].iloc[0]
                
                # Flag if price drops more than 40% (unusual even with aid)
                if curr_price < prev_price * 0.6:
                    outliers[mask] = True
    
    return outliers
```

## See Also

- [Missing Data Handling](missing-data.md) - Imputation methods
- [Panel Construction](panel-construction.md) - Building complete datasets
- [Robustness Checks](../statistical-tests/robustness-checks.md) - Sensitivity analysis
- [API Reference: Panel Builder](../../03-api-reference/data/panel_builder.md)