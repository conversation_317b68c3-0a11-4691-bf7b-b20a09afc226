# Conflict Spillover Effects on Market Integration

## Executive Summary

This case study examines how conflict events in Yemen create ripple effects across market networks, affecting prices and integration even in areas not directly experiencing violence. Using spatial econometric techniques and network analysis, we find that conflict impacts propagate through trade networks more strongly than through geographic proximity alone. A major battle in a trading hub can disrupt markets 300km away, while nearby markets connected through different trade routes may remain unaffected.

## Background and Context

### Yemen's Trade Network Structure
- **Pre-war hubs**: Sana'a, Aden, Taiz, Hudaydah dominated trade flows
- **Conflict disruption**: Major routes blocked, creating network fragmentation
- **Adaptation**: New routes emerged, some traditional paths abandoned
- **Current state**: Multiple overlapping networks with limited interconnection

### Types of Conflict Spillovers
1. **Direct effects**: Violence in the market itself
2. **Trade route disruption**: Conflict blocking supply chains
3. **Displacement effects**: Population movements affecting demand
4. **Uncertainty spillovers**: Anticipated conflict changing behavior
5. **Network cascade**: Hub disruption affecting spoke markets

## Research Questions

1. **How far do conflict spillovers extend through market networks?**
2. **Do trade networks or geographic distance better predict spillover intensity?**
3. **Which types of conflict events create the largest spillovers?**
4. **Can we identify resilient vs fragile market clusters?**

## Data and Methodology

### Comprehensive Conflict and Network Data
```python
from yemen_market.data import PanelBuilder, NetworkBuilder
from yemen_market.conflict import ACLEDProcessor
import pandas as pd
import numpy as np
import networkx as nx

# Load price and conflict data
panel_builder = PanelBuilder()
price_data = panel_builder.build_balanced_panel(
    commodities='all',
    start_date='2019-01-01',
    end_date='2023-12-31'
)

# Process ACLED conflict data with spatial information
acled_processor = ACLEDProcessor()
conflict_data = acled_processor.process_yemen_conflicts(
    start_date='2019-01-01',
    end_date='2023-12-31',
    event_types=['battles', 'explosions', 'violence_against_civilians'],
    include_fatalities=True
)

# Build trade network from multiple sources
network_builder = NetworkBuilder()

# Pre-conflict trade flows (baseline)
historical_network = network_builder.build_from_trade_data(
    source='2014_census_trade_flows'
)

# Current network from price correlations
correlation_network = network_builder.build_from_price_correlation(
    price_data,
    threshold=0.7,
    min_observations=24
)

# Road network
road_network = network_builder.build_from_infrastructure(
    roads_shapefile='data/boundaries/yemen_roads.shp',
    quality_weights=True
)

# Combine networks
combined_network = network_builder.combine_networks([
    historical_network,
    correlation_network,
    road_network
], weights=[0.3, 0.5, 0.2])
```

### Spatial Weight Matrices
```python
from pysal.lib import weights
import geopandas as gpd

# Load market locations
markets_gdf = gpd.read_file('data/processed/market_locations.geojson')

# Create different spatial weight matrices
# 1. Geographic distance
W_geographic = weights.DistanceBand.from_dataframe(
    markets_gdf, 
    threshold=200000,  # 200km
    binary=False,
    alpha=-2  # Distance decay
)

# 2. K-nearest neighbors
W_knn = weights.KNN.from_dataframe(markets_gdf, k=5)

# 3. Trade network weights
W_trade = weights.Network(combined_network).w

# 4. Hybrid (geographic + network)
W_hybrid = weights.Union([W_geographic, W_trade])
```

## Implementation

### Step 1: Measuring Spillover Effects

```python
from yemen_market.models.spatial import SpatialPanel
from yemen_market.models.three_tier import ThreeTierRunner

# Create conflict spillover variables
def create_spillover_variables(conflict_data, price_data, W):
    """Create spatially lagged conflict variables"""
    
    # Merge conflict to markets
    market_conflict = pd.merge(
        price_data[['market', 'date', 'latitude', 'longitude']].drop_duplicates(),
        conflict_data,
        on=['date'],
        how='left'
    )
    
    # Calculate distance-based assignment
    market_conflict['conflict_within_20km'] = 0
    market_conflict['conflict_within_50km'] = 0
    market_conflict['fatalities_within_50km'] = 0
    
    for idx, row in market_conflict.iterrows():
        # Find conflicts within distance bands
        distances = calculate_distances(
            row['latitude'], row['longitude'],
            conflict_data['latitude'], conflict_data['longitude']
        )
        
        within_20 = distances <= 20
        within_50 = distances <= 50
        
        market_conflict.loc[idx, 'conflict_within_20km'] = \
            conflict_data[within_20]['events'].sum()
        market_conflict.loc[idx, 'conflict_within_50km'] = \
            conflict_data[within_50]['events'].sum()
        market_conflict.loc[idx, 'fatalities_within_50km'] = \
            conflict_data[within_50]['fatalities'].sum()
    
    # Create network spillovers
    market_conflict['network_conflict'] = W.lag(
        market_conflict['conflict_within_50km']
    )
    
    return market_conflict

# Generate spillover variables for each weight matrix
spillovers_geographic = create_spillover_variables(
    conflict_data, price_data, W_geographic
)
spillovers_trade = create_spillover_variables(
    conflict_data, price_data, W_trade
)
```

### Step 2: Three-Tier Spillover Analysis

#### Tier 1: Average Spillover Effects
```python
runner = ThreeTierRunner()

# Model comparing spillover mechanisms
spillover_config = {
    'dependent_var': 'log_price',
    'independent_vars': [
        'conflict_own_market',           # Direct effect
        'conflict_geographic_neighbors', # Geographic spillover
        'conflict_trade_neighbors',      # Network spillover
        'conflict_own_market:fragile_route',  # Interaction with vulnerability
        'global_price',
        'season_dummies'
    ],
    'fixed_effects': ['market', 'commodity', 'month'],
    'cluster_var': 'market',
    'spatial_weights': W_hybrid
}

tier1_spillover = runner.run_tier1(
    price_data.merge(spillovers_trade),
    config=spillover_config
)

# Extract and compare spillover magnitudes
spillover_comparison = {
    'Direct effect': tier1_spillover.params['conflict_own_market'],
    'Geographic spillover': tier1_spillover.params['conflict_geographic_neighbors'],
    'Network spillover': tier1_spillover.params['conflict_trade_neighbors'],
    'Ratio network/geographic': (
        tier1_spillover.params['conflict_trade_neighbors'] /
        tier1_spillover.params['conflict_geographic_neighbors']
    )
}
```

#### Tier 2: Event-Study Design
```python
# Identify major conflict events for event study
major_events = conflict_data[
    (conflict_data['fatalities'] > 50) |
    (conflict_data['events'] > 10)
].sort_values('fatalities', ascending=False).head(10)

# Event study for each major conflict
event_studies = {}

for idx, event in major_events.iterrows():
    event_date = event['date']
    event_location = (event['latitude'], event['longitude'])
    
    # Create event window
    window_start = event_date - pd.DateOffset(months=3)
    window_end = event_date + pd.DateOffset(months=6)
    
    # Calculate distance and network distance to event
    price_data['distance_to_event'] = calculate_distances(
        price_data['latitude'], price_data['longitude'],
        event_location[0], event_location[1]
    )
    
    price_data['network_distance_to_event'] = nx.shortest_path_length(
        combined_network,
        source=find_nearest_market(event_location),
        weight='distance'
    )
    
    # Run event study regression
    event_config = {
        'dependent_var': 'log_price',
        'independent_vars': [
            f'months_since_event_{i}' for i in range(-3, 7)
        ] + [
            f'months_since_event_{i}:distance_to_event' for i in range(-3, 7)
        ] + [
            f'months_since_event_{i}:network_distance' for i in range(-3, 7)
        ],
        'fixed_effects': ['market', 'commodity'],
        'cluster_var': 'market'
    }
    
    event_result = runner.run_tier2(
        price_data[
            (price_data['date'] >= window_start) &
            (price_data['date'] <= window_end)
        ],
        config=event_config
    )
    
    event_studies[event['event_id']] = {
        'location': event['location'],
        'date': event_date,
        'fatalities': event['fatalities'],
        'results': event_result,
        'peak_effect_month': identify_peak_effect(event_result),
        'spatial_decay': calculate_spatial_decay(event_result)
    }
```

#### Tier 3: Network Resilience Analysis
```python
from yemen_market.models.three_tier.tier3_validation import NetworkResilience

# Analyze network structure and vulnerability
resilience_analyzer = NetworkResilience()

# Identify critical nodes (markets)
centrality_measures = {
    'degree': nx.degree_centrality(combined_network),
    'betweenness': nx.betweenness_centrality(combined_network),
    'closeness': nx.closeness_centrality(combined_network),
    'eigenvector': nx.eigenvector_centrality(combined_network)
}

# Simulate network disruptions
disruption_simulations = []

for market in combined_network.nodes():
    # Remove market and calculate impact
    disrupted_network = combined_network.copy()
    disrupted_network.remove_node(market)
    
    # Measure fragmentation
    n_components_before = nx.number_connected_components(combined_network)
    n_components_after = nx.number_connected_components(disrupted_network)
    
    # Calculate price impact through model
    disruption_impact = simulate_market_removal(
        price_data, market, runner, tier1_config
    )
    
    disruption_simulations.append({
        'market': market,
        'centrality': centrality_measures['betweenness'][market],
        'fragmentation_impact': n_components_after - n_components_before,
        'price_impact': disruption_impact['avg_price_increase'],
        'affected_markets': disruption_impact['n_affected_markets']
    })

disruption_df = pd.DataFrame(disruption_simulations)
```

### Step 3: Spillover Dynamics Analysis
```python
# Analyze how spillovers evolve over time
from yemen_market.models.spatial import SpatialVAR

# Estimate spatial VAR model
svar_model = SpatialVAR(
    data=price_data,
    variables=['log_price', 'conflict_events'],
    spatial_weights=W_trade,
    lags=3
)

svar_results = svar_model.fit()

# Calculate impulse response functions
irf_results = svar_model.impulse_response(
    periods=12,
    shock_variable='conflict_events',
    response_variable='log_price'
)

# Decompose spillover channels
spillover_decomposition = {
    'direct': irf_results['own_effect'],
    'first_order': irf_results['neighbor_effect'],
    'higher_order': irf_results['total_effect'] - 
                    irf_results['own_effect'] - 
                    irf_results['neighbor_effect']
}
```

## Results and Visualizations

### Finding 1: Network Spillovers Dominate Geographic Distance
```python
import matplotlib.pyplot as plt
import seaborn as sns

# Compare spillover mechanisms
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

# Panel A: Spillover magnitude by distance
distance_bins = [0, 50, 100, 150, 200, 250, 300]
geo_spillover = []
network_spillover = []

for i in range(len(distance_bins)-1):
    mask = (price_data['distance_to_conflict'] >= distance_bins[i]) & \
           (price_data['distance_to_conflict'] < distance_bins[i+1])
    
    geo_spillover.append(
        price_data[mask]['price_change_geographic'].mean()
    )
    network_spillover.append(
        price_data[mask]['price_change_network'].mean()
    )

x = [(distance_bins[i] + distance_bins[i+1])/2 for i in range(len(distance_bins)-1)]

ax1.plot(x, geo_spillover, 'b-o', linewidth=2, 
         markersize=8, label='Geographic spillover')
ax1.plot(x, network_spillover, 'r-s', linewidth=2, 
         markersize=8, label='Network spillover')
ax1.set_xlabel('Distance from conflict (km)')
ax1.set_ylabel('Average price increase (%)')
ax1.set_title('Conflict Spillovers by Distance Type')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Panel B: Network position matters
ax2.scatter(disruption_df['centrality'], 
           disruption_df['price_impact'],
           s=disruption_df['affected_markets']*10,
           alpha=0.6, c=disruption_df['fragmentation_impact'],
           cmap='Reds')

# Add market labels for top 10 central markets
top_markets = disruption_df.nlargest(10, 'centrality')
for idx, row in top_markets.iterrows():
    ax2.annotate(row['market'], 
                (row['centrality'], row['price_impact']),
                fontsize=8, ha='center')

ax2.set_xlabel('Market centrality (betweenness)')
ax2.set_ylabel('Price impact of market disruption (%)')
ax2.set_title('Market Importance in Trade Network')
cbar = plt.colorbar(ax2.collections[0], ax=ax2)
cbar.set_label('Network fragmentation impact')

plt.tight_layout()
plt.savefig('results/conflict_spillover_mechanisms.png', dpi=300)
```

### Finding 2: Event Study Results
```python
# Visualize event study for major battle
major_battle = list(event_studies.values())[0]  # Largest event

fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

# Extract coefficients
months = range(-3, 7)
effects_50km = [major_battle['results'].params[f'months_since_event_{m}:dist_50km'] 
                for m in months]
effects_100km = [major_battle['results'].params[f'months_since_event_{m}:dist_100km'] 
                 for m in months]
effects_200km = [major_battle['results'].params[f'months_since_event_{m}:dist_200km'] 
                 for m in months]

# Panel A: Temporal evolution
ax1.plot(months, effects_50km, 'r-o', linewidth=2, label='< 50km')
ax1.plot(months, effects_100km, 'b-s', linewidth=2, label='50-100km')
ax1.plot(months, effects_200km, 'g-^', linewidth=2, label='100-200km')
ax1.axvline(x=0, color='black', linestyle='--', alpha=0.5)
ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
ax1.fill_between(months, 0, effects_50km, alpha=0.2, color='red')
ax1.set_xlabel('Months since conflict event')
ax1.set_ylabel('Price effect (%)')
ax1.set_title(f'Event Study: {major_battle["location"]} Battle ({major_battle["date"].strftime("%Y-%m")})')
ax1.legend()
ax1.grid(True, alpha=0.3)

# Panel B: Spatial decay
distances = [25, 75, 150, 250, 350]
peak_effects = calculate_peak_effects_by_distance(major_battle['results'], distances)

ax2.plot(distances, peak_effects, 'bo-', linewidth=2, markersize=10)
ax2.set_xlabel('Distance from conflict (km)')
ax2.set_ylabel('Peak price effect (%)')
ax2.set_title('Spatial Decay of Conflict Impact')
ax2.grid(True, alpha=0.3)

# Add exponential decay fit
from scipy.optimize import curve_fit
def exp_decay(x, a, b):
    return a * np.exp(-b * x)

popt, _ = curve_fit(exp_decay, distances, peak_effects)
x_fit = np.linspace(0, 400, 100)
y_fit = exp_decay(x_fit, *popt)
ax2.plot(x_fit, y_fit, 'r--', alpha=0.7, 
         label=f'Decay rate: {popt[1]:.3f}/km')
ax2.legend()

plt.tight_layout()
plt.savefig('results/conflict_event_study.png', dpi=300)
```

### Finding 3: Network Vulnerability Map
```python
# Create geographic visualization of network vulnerability
import geopandas as gpd
from matplotlib.colors import LinearSegmentedColormap

# Load Yemen shapefile
yemen_shape = gpd.read_file('data/boundaries/yemen_admin2.shp')

# Create vulnerability scores
vulnerability_scores = pd.DataFrame({
    'market': disruption_df['market'],
    'vulnerability': (
        disruption_df['price_impact'] * 0.4 +
        disruption_df['fragmentation_impact'] * 0.3 +
        disruption_df['affected_markets'] / disruption_df['affected_markets'].max() * 0.3
    )
})

# Merge with geographic data
markets_gdf = markets_gdf.merge(vulnerability_scores, on='market')

# Create figure with network overlay
fig, ax = plt.subplots(figsize=(14, 10))

# Plot base map
yemen_shape.plot(ax=ax, color='lightgray', edgecolor='white')

# Plot markets colored by vulnerability
markets_plot = markets_gdf.plot(
    ax=ax,
    column='vulnerability',
    cmap='YlOrRd',
    markersize=markets_gdf['vulnerability'] * 100,
    legend=True,
    legend_kwds={'label': 'Market vulnerability score'}
)

# Add network edges
for edge in combined_network.edges():
    market1, market2 = edge
    if market1 in markets_gdf['market'].values and \
       market2 in markets_gdf['market'].values:
        
        coord1 = markets_gdf[markets_gdf['market'] == market1][['longitude', 'latitude']].values[0]
        coord2 = markets_gdf[markets_gdf['market'] == market2][['longitude', 'latitude']].values[0]
        
        # Edge width based on trade volume
        edge_weight = combined_network[market1][market2]['weight']
        ax.plot([coord1[0], coord2[0]], [coord1[1], coord2[1]], 
                'gray', alpha=0.3, linewidth=edge_weight*2)

# Add conflict hotspots
conflict_hotspots = identify_conflict_clusters(conflict_data)
for hotspot in conflict_hotspots:
    circle = plt.Circle((hotspot['longitude'], hotspot['latitude']), 
                       hotspot['intensity']/100, 
                       color='red', alpha=0.2)
    ax.add_patch(circle)

ax.set_title('Market Network Vulnerability and Conflict Hotspots', fontsize=16)
ax.set_xlabel('Longitude')
ax.set_ylabel('Latitude')

# Add annotations for critical markets
critical_markets = vulnerability_scores.nlargest(5, 'vulnerability')
for idx, market in critical_markets.iterrows():
    market_coords = markets_gdf[markets_gdf['market'] == market['market']][
        ['longitude', 'latitude']
    ].values[0]
    ax.annotate(market['market'], 
                xy=(market_coords[0], market_coords[1]),
                xytext=(market_coords[0]+0.1, market_coords[1]+0.1),
                fontsize=10, fontweight='bold',
                arrowprops=dict(arrowstyle='->', color='red'))

plt.tight_layout()
plt.savefig('results/conflict_network_vulnerability_map.png', dpi=300)
```

### Finding 4: Spillover Persistence
```python
# Analyze how long spillovers last
persistence_analysis = []

for event_id, event_data in event_studies.items():
    # Find when effect returns to baseline
    baseline = 0.01  # 1% threshold
    
    effects = [event_data['results'].params[f'months_since_event_{m}'] 
               for m in range(0, 7)]
    
    persistence_months = 0
    for i, effect in enumerate(effects):
        if abs(effect) > baseline:
            persistence_months = i
    
    persistence_analysis.append({
        'event_id': event_id,
        'initial_impact': effects[0],
        'persistence_months': persistence_months,
        'total_impact': sum(effects),
        'event_type': event_data.get('event_type', 'battle'),
        'fatalities': event_data['fatalities']
    })

persistence_df = pd.DataFrame(persistence_analysis)

# Visualize persistence patterns
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

# Panel A: Persistence by event severity
ax1.scatter(persistence_df['fatalities'], 
           persistence_df['persistence_months'],
           s=persistence_df['initial_impact']*100,
           alpha=0.6)
ax1.set_xlabel('Conflict fatalities')
ax1.set_ylabel('Months until price returns to baseline')
ax1.set_title('Spillover Persistence by Conflict Severity')
ax1.set_xscale('log')

# Add trend line
z = np.polyfit(np.log(persistence_df['fatalities']+1), 
               persistence_df['persistence_months'], 1)
p = np.poly1d(z)
x_trend = np.logspace(0, np.log10(persistence_df['fatalities'].max()), 100)
ax1.plot(x_trend, p(np.log(x_trend)), 'r--', alpha=0.7)

# Panel B: Cumulative impact distribution
ax2.hist(persistence_df['total_impact'], bins=20, alpha=0.7, edgecolor='black')
ax2.axvline(x=persistence_df['total_impact'].median(), 
            color='red', linestyle='--', 
            label=f'Median: {persistence_df["total_impact"].median():.1%}')
ax2.set_xlabel('Total cumulative price impact')
ax2.set_ylabel('Number of events')
ax2.set_title('Distribution of Total Spillover Impact')
ax2.legend()

plt.tight_layout()
plt.savefig('results/conflict_spillover_persistence.png', dpi=300)
```

## Policy Implications

### 1. Strategic Market Protection
- **Finding**: Protecting hub markets prevents cascade failures
- **Implication**: Security resources should prioritize network-critical markets
- **Recommendations**:
  - Identify and fortify top 10 hub markets
  - Establish security corridors for critical trade routes
  - Create rapid response teams for hub market threats

### 2. Supply Chain Redundancy
- **Finding**: Single-path dependencies amplify spillovers
- **Implication**: Alternative routes reduce vulnerability
- **Recommendations**:
  - Map and develop alternative trade routes
  - Subsidize infrastructure for route diversification
  - Maintain strategic reserves at network peripheries

### 3. Early Warning Systems
- **Finding**: Network metrics predict price impacts better than proximity
- **Implication**: Monitor trade flows, not just conflict locations
- **Recommendations**:
  - Real-time trade flow monitoring system
  - Network health dashboard for decision makers
  - Automated alerts for critical link disruptions

### 4. Targeted Stabilization
- **Finding**: Effects persist 3-6 months depending on severity
- **Implication**: Interventions needed for extended periods
- **Recommendations**:
  - 6-month stabilization packages post-conflict
  - Graduated support based on network position
  - Focus on restoring key trade links first

## Lessons Learned

### Analytical Insights
1. **Networks matter more than geography**: Trade relationships determine spillover patterns
2. **Hub vulnerability**: Losing one critical market can fragment entire regions
3. **Persistence varies**: Major battles have 3x longer impacts than skirmishes
4. **Adaptation happens**: Markets find alternative routes but at higher cost

### Methodological Advances
1. **Hybrid spatial weights**: Combining geographic and network distance improves fit
2. **Event studies**: Clean identification of causal spillover effects
3. **Network simulation**: Useful for identifying critical infrastructure
4. **Time-varying effects**: Spillovers decay non-linearly

### Data Requirements
1. **High-frequency conflict data**: Daily events crucial during active periods
2. **Trade flow information**: Historical flows help predict current networks
3. **Infrastructure mapping**: Road quality affects spillover speed
4. **Market characteristics**: Storage, size, connectivity all matter

## Code Implementation

```python
# Key functions for spillover analysis
from yemen_market.spillover_analysis import (
    build_trade_network,
    calculate_spillover_metrics,
    run_event_study,
    simulate_network_disruption,
    identify_critical_infrastructure,
    design_intervention_strategy
)

# Example workflow
spillover_results = analyze_conflict_spillovers(
    price_data=price_data,
    conflict_data=conflict_data,
    network=combined_network,
    methods=['spatial_lag', 'event_study', 'network_simulation'],
    output_dir='results/spillover_analysis/'
)
```

## Future Research Directions

1. **Dynamic networks**: How trade networks evolve during conflict
2. **Behavioral responses**: How traders anticipate and adapt to spillovers
3. **Optimal intervention**: Where to intervene for maximum stabilization
4. **Regional comparison**: Do spillover patterns differ across Yemen?

## References

1. Hsiang, S. M., Burke, M., & Miguel, E. (2013). "Quantifying the influence of climate on human conflict"
2. Bellemare, M. F. (2015). "Rising food prices, food price volatility, and social unrest"
3. Amodio, F., & Di Maio, M. (2018). "Making do with what you have: Conflict, input misallocation and firm performance"
4. World Bank (2022). "Yemen Dynamic Damage and Needs Assessment"