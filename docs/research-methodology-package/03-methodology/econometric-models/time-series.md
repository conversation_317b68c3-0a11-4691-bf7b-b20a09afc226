# Time Series Analysis Methods

**Target Audience**: Econometricians, Time Series Analysts  
**Module**: `yemen_market.models.time_series`

## Overview

This document details the time series methods implemented for analyzing price dynamics and market integration in Yemen. The methods handle non-stationary price series, cointegration relationships, and structural breaks due to conflict.

## Stationarity and Unit Root Testing

### Augmented <PERSON><PERSON><PERSON><PERSON> (ADF) Test

The ADF test checks for unit roots in individual time series:

$$\Delta y_t = \alpha + \beta t + \gamma y_{t-1} + \sum_{i=1}^{p} \delta_i \Delta y_{t-i} + \epsilon_t$$

H₀: γ = 0 (unit root)  
H₁: γ < 0 (stationary)

```python
import numpy as np
from statsmodels.tsa.stattools import adfuller
from statsmodels.regression.linear_model import <PERSON><PERSON>

def perform_adf_test(
    series: np.ndarray,
    max_lags: int = None,
    regression: str = 'ct',  # 'c': constant, 'ct': constant+trend, 'n': none
    autolag: str = 'AIC'
) -> ADFTestResult:
    """
    Perform Augmented <PERSON><PERSON><PERSON><PERSON> test with optimal lag selection.
    
    Implementation follows <PERSON><PERSON><PERSON><PERSON> (1994) critical values.
    """
    if max_lags is None:
        # Schwert criterion for max lags
        max_lags = int(12 * (len(series) / 100) ** (1/4))
    
    # Run ADF test
    adf_stat, p_value, used_lags, nobs, critical_values, icbest = adfuller(
        series,
        maxlag=max_lags,
        regression=regression,
        autolag=autolag,
        store=True,
        regresults=True
    )
    
    # Extract regression results for diagnostics
    regression_results = icbest
    
    # Check for serial correlation in residuals
    from statsmodels.stats.diagnostic import acorr_ljungbox
    lb_stat, lb_pvalue = acorr_ljungbox(
        regression_results.resid,
        lags=10,
        return_df=False
    )
    
    return ADFTestResult(
        test_statistic=adf_stat,
        p_value=p_value,
        lags_used=used_lags,
        critical_values=critical_values,
        reject_unit_root=p_value < 0.05,
        residual_autocorrelation=lb_pvalue[-1] < 0.05
    )
```

### Panel Unit Root Tests

For panel data, we implement the Im-Pesaran-Shin (IPS) test:

```python
def ips_panel_unit_root_test(
    panel_data: pd.DataFrame,
    var_name: str,
    entity_var: str = 'entity',
    time_var: str = 'date',
    max_lags: int = None
) -> IPSTestResult:
    """
    Im-Pesaran-Shin panel unit root test.
    
    H0: All panels have unit root
    H1: Some panels are stationary
    """
    entities = panel_data[entity_var].unique()
    n_entities = len(entities)
    
    # Run ADF for each entity
    adf_stats = []
    adf_pvalues = []
    
    for entity in entities:
        entity_data = panel_data[panel_data[entity_var] == entity]
        series = entity_data[var_name].values
        
        if len(series) < 20:  # Skip short series
            continue
        
        result = perform_adf_test(series, max_lags=max_lags)
        adf_stats.append(result.test_statistic)
        adf_pvalues.append(result.p_value)
    
    # Calculate IPS statistic
    t_bar = np.mean(adf_stats)
    
    # Standardization (using Im-Pesaran-Shin critical values)
    # These depend on T and inclusion of deterministics
    T = len(panel_data[time_var].unique())
    
    # Look up or calculate E[t_bar] and Var[t_bar]
    # For illustration, using approximations
    if T >= 25:
        E_t_bar = -1.52  # With constant
        Var_t_bar = 0.82
    else:
        E_t_bar = -1.50
        Var_t_bar = 0.90
    
    # Standardized statistic
    W_tbar = np.sqrt(n_entities) * (t_bar - E_t_bar) / np.sqrt(Var_t_bar)
    
    # W_tbar ~ N(0,1) under null
    from scipy import stats
    p_value = stats.norm.cdf(W_tbar)
    
    return IPSTestResult(
        w_stat=W_tbar,
        p_value=p_value,
        avg_adf_stat=t_bar,
        n_stationary=sum(p < 0.05 for p in adf_pvalues),
        n_panels=n_entities
    )
```

## Cointegration Analysis

### Engle-Granger Two-Step Procedure

For testing cointegration between two price series:

```python
def engle_granger_cointegration(
    y1: np.ndarray,
    y2: np.ndarray,
    max_lags: int = None
) -> EngleGrangerResult:
    """
    Engle-Granger two-step cointegration test.
    
    Step 1: Estimate cointegrating regression
    Step 2: Test residuals for unit root
    """
    # Step 1: Cointegrating regression
    # y1 = α + β*y2 + u
    X = sm.add_constant(y2)
    model = OLS(y1, X)
    results = model.fit()
    
    alpha = results.params[0]
    beta = results.params[1]
    residuals = results.resid
    
    # Step 2: Test residuals for stationarity
    adf_result = perform_adf_test(
        residuals,
        max_lags=max_lags,
        regression='n'  # No constant in ADF for residuals
    )
    
    # MacKinnon critical values for cointegration
    # These differ from standard ADF critical values
    n = len(y1)
    if n > 200:
        crit_value_5pct = -3.34
    else:
        # Approximate for smaller samples
        crit_value_5pct = -3.34 - 5.7/n - 8.98/n**2
    
    return EngleGrangerResult(
        alpha=alpha,
        beta=beta,
        residuals=residuals,
        adf_stat=adf_result.test_statistic,
        critical_value=crit_value_5pct,
        cointegrated=adf_result.test_statistic < crit_value_5pct,
        residual_variance=np.var(residuals)
    )
```

### Johansen Cointegration Test

For systems with multiple variables:

```python
def johansen_cointegration_test(
    data: pd.DataFrame,
    vars: list,
    det_order: int = 0,
    k_ar_diff: int = 1
) -> JohansenResult:
    """
    Johansen cointegration test for multivariate systems.
    
    det_order: -1 (no det), 0 (const), 1 (linear trend)
    k_ar_diff: Number of lags in VAR (in differences)
    """
    from statsmodels.tsa.vector_ar.vecm import coint_johansen
    
    # Prepare data matrix
    y = data[vars].values
    
    # Run Johansen test
    joh_result = coint_johansen(y, det_order, k_ar_diff)
    
    # Extract key results
    trace_stats = joh_result.lr1  # Trace statistics
    max_eig_stats = joh_result.lr2  # Max eigenvalue statistics
    critical_values_trace = joh_result.cvt  # Critical values for trace
    critical_values_max = joh_result.cvm  # Critical values for max eigenvalue
    
    # Determine cointegration rank
    rank = 0
    for i in range(len(vars)):
        if trace_stats[i] > critical_values_trace[i, 1]:  # 5% level
            rank = i + 1
        else:
            break
    
    # Extract cointegrating vectors
    eigenvectors = joh_result.evec
    cointegrating_vectors = eigenvectors[:, :rank]
    
    # Normalize cointegrating vectors
    for i in range(rank):
        cointegrating_vectors[:, i] /= cointegrating_vectors[0, i]
    
    return JohansenResult(
        rank=rank,
        trace_stats=trace_stats,
        max_eig_stats=max_eig_stats,
        critical_values_trace=critical_values_trace,
        critical_values_max=critical_values_max,
        eigenvectors=eigenvectors,
        cointegrating_vectors=cointegrating_vectors,
        eigenvalues=joh_result.eig
    )
```

## Vector Error Correction Models (VECM)

### Basic VECM Specification

For cointegrated systems, we use VECM:

$$\Delta y_t = \alpha \beta' y_{t-1} + \sum_{i=1}^{p-1} \Gamma_i \Delta y_{t-i} + \epsilon_t$$

Where:
- $\alpha$: Adjustment coefficients
- $\beta$: Cointegrating vectors
- $\Gamma_i$: Short-run dynamics

```python
def estimate_vecm(
    data: pd.DataFrame,
    endogenous_vars: list,
    exogenous_vars: list = None,
    coint_rank: int = 1,
    k_ar_diff: int = 1,
    deterministic: str = 'ci'  # const inside cointegration
) -> VECMResults:
    """
    Estimate Vector Error Correction Model.
    """
    from statsmodels.tsa.vector_ar.vecm import VECM
    
    # Prepare data
    y = data[endogenous_vars]
    
    if exogenous_vars:
        x = data[exogenous_vars]
    else:
        x = None
    
    # Estimate VECM
    model = VECM(
        endog=y,
        exog=x,
        k_ar_diff=k_ar_diff,
        coint_rank=coint_rank,
        deterministic=deterministic
    )
    
    results = model.fit()
    
    # Extract key components
    alpha = results.alpha  # Adjustment matrix
    beta = results.beta   # Cointegrating vectors
    gamma = results.gamma_df  # Short-run coefficients
    
    # Calculate half-lives of adjustment
    half_lives = {}
    for i, var in enumerate(endogenous_vars):
        adjustment_speed = abs(alpha[i, 0])
        if adjustment_speed > 0:
            half_life = np.log(0.5) / np.log(1 - adjustment_speed)
            half_lives[var] = half_life
    
    # Impulse response functions
    irf = results.irf(periods=20)
    
    # Forecast error variance decomposition
    fevd = results.fevd(periods=20)
    
    return VECMResults(
        alpha=alpha,
        beta=beta,
        gamma=gamma,
        half_lives=half_lives,
        residuals=results.resid,
        aic=results.aic,
        bic=results.bic,
        irf=irf,
        fevd=fevd
    )
```

### VECM with Structural Breaks

Accounting for conflict-induced breaks:

```python
def estimate_vecm_with_breaks(
    data: pd.DataFrame,
    break_dates: list,
    endogenous_vars: list
) -> VECMBreakResults:
    """
    VECM with known structural breaks.
    
    Allows for breaks in:
    - Cointegrating vectors
    - Adjustment speeds
    - Short-run dynamics
    """
    # Create break dummies
    break_dummies = []
    for i, break_date in enumerate(break_dates):
        dummy_name = f'break_{i}'
        data[dummy_name] = (data.index >= break_date).astype(int)
        break_dummies.append(dummy_name)
    
    # Interact breaks with key variables
    for var in endogenous_vars:
        for dummy in break_dummies:
            data[f'{var}_x_{dummy}'] = data[var] * data[dummy]
    
    # Estimate VECM with regime-specific parameters
    # Split sample approach
    regimes = []
    regime_starts = [data.index[0]] + break_dates
    regime_ends = break_dates + [data.index[-1]]
    
    for start, end in zip(regime_starts, regime_ends):
        regime_data = data[start:end]
        
        if len(regime_data) < 30:  # Skip short regimes
            continue
        
        # Estimate VECM for regime
        regime_model = estimate_vecm(
            regime_data,
            endogenous_vars=endogenous_vars
        )
        
        regimes.append({
            'period': (start, end),
            'model': regime_model,
            'n_obs': len(regime_data)
        })
    
    # Test for parameter stability
    stability_tests = test_parameter_stability(regimes)
    
    return VECMBreakResults(
        regimes=regimes,
        break_dates=break_dates,
        stability_tests=stability_tests
    )
```

## Granger Causality Testing

### Bivariate Granger Causality

```python
def granger_causality_test(
    data: pd.DataFrame,
    var1: str,
    var2: str,
    max_lags: int = 10
) -> GrangerCausalityResult:
    """
    Test for Granger causality between two variables.
    
    H0: var2 does not Granger-cause var1
    """
    from statsmodels.tsa.stattools import grangercausalitytests
    
    # Prepare data
    test_data = data[[var1, var2]].dropna()
    
    # Run tests for different lag lengths
    results = grangercausalitytests(
        test_data,
        maxlag=max_lags,
        verbose=False
    )
    
    # Extract F-statistics and p-values
    f_stats = {}
    p_values = {}
    
    for lag in range(1, max_lags + 1):
        f_stat = results[lag][0]['ssr_ftest'][0]
        p_value = results[lag][0]['ssr_ftest'][1]
        
        f_stats[lag] = f_stat
        p_values[lag] = p_value
    
    # Select optimal lag using BIC
    bic_values = {}
    for lag in range(1, max_lags + 1):
        # Fit VAR model
        from statsmodels.tsa.vector_ar.var_model import VAR
        model = VAR(test_data)
        var_result = model.fit(lag)
        bic_values[lag] = var_result.bic
    
    optimal_lag = min(bic_values, key=bic_values.get)
    
    return GrangerCausalityResult(
        f_statistics=f_stats,
        p_values=p_values,
        optimal_lag=optimal_lag,
        rejects_null_at_5pct=p_values[optimal_lag] < 0.05,
        direction=f"{var2} → {var1}"
    )
```

### Panel Granger Causality

```python
def panel_granger_causality(
    panel_data: pd.DataFrame,
    var1: str,
    var2: str,
    entity_var: str = 'entity',
    max_lags: int = 4
) -> PanelGrangerResult:
    """
    Panel Granger causality test using Dumitrescu-Hurlin (2012).
    
    Allows for heterogeneous coefficients across entities.
    """
    entities = panel_data[entity_var].unique()
    
    # Run Granger test for each entity
    entity_results = []
    
    for entity in entities:
        entity_data = panel_data[panel_data[entity_var] == entity]
        
        if len(entity_data) < 20:
            continue
        
        result = granger_causality_test(
            entity_data,
            var1,
            var2,
            max_lags
        )
        
        entity_results.append({
            'entity': entity,
            'f_stat': result.f_statistics[result.optimal_lag],
            'p_value': result.p_values[result.optimal_lag]
        })
    
    # Calculate Dumitrescu-Hurlin statistics
    n_entities = len(entity_results)
    
    # Average Wald statistic
    w_bar = np.mean([r['f_stat'] for r in entity_results])
    
    # Standardized statistic (assuming T > 5 + 2K)
    T = len(panel_data[entity_var].value_counts().iloc[0])
    K = max_lags
    
    z_bar = np.sqrt(n_entities / (2 * K)) * (w_bar - K)
    
    # Under null, z_bar ~ N(0,1) asymptotically
    from scipy import stats
    p_value = 2 * (1 - stats.norm.cdf(abs(z_bar)))
    
    return PanelGrangerResult(
        w_bar=w_bar,
        z_bar=z_bar,
        p_value=p_value,
        n_entities=n_entities,
        entity_results=entity_results,
        rejects_homogeneous_non_causality=p_value < 0.05
    )
```

## Spectral Analysis

### Periodogram for Seasonality Detection

```python
def analyze_seasonality(
    series: np.ndarray,
    sampling_freq: int = 12  # Monthly data
) -> SeasonalityAnalysis:
    """
    Detect seasonal patterns using spectral analysis.
    """
    from scipy import signal
    
    # Remove trend
    detrended = signal.detrend(series)
    
    # Calculate periodogram
    frequencies, power_spectrum = signal.periodogram(
        detrended,
        fs=sampling_freq,
        scaling='spectrum'
    )
    
    # Find dominant frequencies
    # Seasonal frequencies for monthly data: 12, 6, 4, 3, 2.4, 2
    seasonal_freqs = [1, 2, 3, 4, 6, 12]  # Cycles per year
    seasonal_periods = [12/f for f in seasonal_freqs]
    
    seasonal_power = {}
    for period in seasonal_periods:
        # Find closest frequency
        freq_annual = sampling_freq / period
        idx = np.argmin(np.abs(frequencies - freq_annual))
        seasonal_power[period] = power_spectrum[idx]
    
    # Test for significant seasonality
    # Using Fisher's test
    g_stat = max(seasonal_power.values()) / np.sum(power_spectrum)
    
    # Critical value (approximate)
    n = len(series)
    critical_value = -np.log(0.05) / n
    
    significant_seasons = {
        period: power 
        for period, power in seasonal_power.items()
        if power / np.sum(power_spectrum) > critical_value
    }
    
    return SeasonalityAnalysis(
        frequencies=frequencies,
        power_spectrum=power_spectrum,
        seasonal_power=seasonal_power,
        significant_seasons=significant_seasons,
        has_seasonality=len(significant_seasons) > 0
    )
```

## Filtering and Decomposition

### Hodrick-Prescott Filter

```python
def hp_filter(
    series: np.ndarray,
    lamb: float = 1600  # Standard for monthly data
) -> HPFilterResult:
    """
    Hodrick-Prescott filter for trend-cycle decomposition.
    
    Minimizes: sum(y_t - tau_t)^2 + lambda * sum(Δ²tau_t)^2
    """
    from statsmodels.tsa.filters.hp_filter import hpfilter
    
    cycle, trend = hpfilter(series, lamb=lamb)
    
    # Calculate smoothness measure
    trend_changes = np.diff(trend, n=2)
    smoothness = np.std(trend_changes)
    
    # Variance decomposition
    var_total = np.var(series)
    var_trend = np.var(trend)
    var_cycle = np.var(cycle)
    
    return HPFilterResult(
        trend=trend,
        cycle=cycle,
        lambda_param=lamb,
        smoothness=smoothness,
        variance_ratio_trend=var_trend / var_total,
        variance_ratio_cycle=var_cycle / var_total
    )
```

### Band-Pass Filter

```python
def band_pass_filter(
    series: np.ndarray,
    low_freq: int = 6,   # 6 months
    high_freq: int = 32  # 32 months
) -> BandPassResult:
    """
    Baxter-King band-pass filter for business cycle extraction.
    """
    from statsmodels.tsa.filters.bk_filter import bkfilter
    
    # Apply filter
    cycle = bkfilter(series, low=low_freq, high=high_freq, K=12)
    
    # Handle NaN values at ends
    valid_idx = ~np.isnan(cycle)
    cycle_clean = cycle[valid_idx]
    series_clean = series[valid_idx]
    
    # Calculate statistics
    cycle_std = np.std(cycle_clean)
    cycle_persistence = np.corrcoef(cycle_clean[:-1], cycle_clean[1:])[0, 1]
    
    return BandPassResult(
        cycle=cycle,
        valid_range=valid_idx,
        cycle_volatility=cycle_std,
        cycle_persistence=cycle_persistence,
        frequency_range=(low_freq, high_freq)
    )
```

## Structural Break Detection

### Chow Test for Known Break

```python
def chow_test(
    data: pd.DataFrame,
    formula: str,
    break_date: str
) -> ChowTestResult:
    """
    Chow test for structural break at known date.
    """
    # Split data
    data1 = data[data.index < break_date]
    data2 = data[data.index >= break_date]
    
    # Fit models
    model_full = smf.ols(formula, data=data).fit()
    model1 = smf.ols(formula, data=data1).fit()
    model2 = smf.ols(formula, data=data2).fit()
    
    # Calculate test statistic
    rss_full = model_full.ssr
    rss1 = model1.ssr
    rss2 = model2.ssr
    rss_unrestricted = rss1 + rss2
    
    k = model_full.df_model + 1  # Number of parameters
    n = len(data)
    
    f_stat = ((rss_full - rss_unrestricted) / k) / (
        rss_unrestricted / (n - 2*k)
    )
    
    # P-value
    from scipy import stats
    p_value = 1 - stats.f.cdf(f_stat, k, n - 2*k)
    
    return ChowTestResult(
        f_statistic=f_stat,
        p_value=p_value,
        break_date=break_date,
        reject_stability=p_value < 0.05
    )
```

### Bai-Perron Multiple Break Test

```python
def bai_perron_test(
    y: np.ndarray,
    X: np.ndarray,
    max_breaks: int = 5,
    min_segment: float = 0.15
) -> BaiPerronResult:
    """
    Bai-Perron test for multiple structural breaks.
    
    Tests for 0 vs 1, 1 vs 2, ..., m-1 vs m breaks.
    """
    n = len(y)
    min_obs = int(min_segment * n)
    
    # Dynamic programming for optimal breaks
    break_points = {}
    ssr_values = {}
    
    # No breaks
    model = OLS(y, X).fit()
    ssr_values[0] = model.ssr
    break_points[0] = []
    
    # Test for m breaks
    for m in range(1, max_breaks + 1):
        best_ssr = np.inf
        best_breaks = []
        
        # Use dynamic programming
        # Simplified version - full implementation is complex
        candidates = list(range(min_obs, n - min_obs, 10))
        
        from itertools import combinations
        for breaks in combinations(candidates, m):
            breaks = [0] + list(breaks) + [n]
            ssr_total = 0
            
            for i in range(len(breaks) - 1):
                start, end = breaks[i], breaks[i + 1]
                if end - start < min_obs:
                    ssr_total = np.inf
                    break
                
                segment_y = y[start:end]
                segment_X = X[start:end]
                
                try:
                    model = OLS(segment_y, segment_X).fit()
                    ssr_total += model.ssr
                except:
                    ssr_total = np.inf
                    break
            
            if ssr_total < best_ssr:
                best_ssr = ssr_total
                best_breaks = breaks[1:-1]
        
        ssr_values[m] = best_ssr
        break_points[m] = best_breaks
    
    # Sequential F-tests
    f_stats = {}
    for m in range(max_breaks):
        if m + 1 not in ssr_values:
            continue
        
        f_stat = ((ssr_values[m] - ssr_values[m + 1]) / X.shape[1]) / (
            ssr_values[m + 1] / (n - (m + 1) * X.shape[1])
        )
        f_stats[m] = f_stat
    
    # Select number of breaks using BIC
    bic_values = {}
    for m in range(max_breaks + 1):
        if m not in ssr_values:
            continue
        bic = n * np.log(ssr_values[m] / n) + m * X.shape[1] * np.log(n)
        bic_values[m] = bic
    
    optimal_breaks = min(bic_values, key=bic_values.get)
    
    return BaiPerronResult(
        n_breaks=optimal_breaks,
        break_dates=[data.index[i] for i in break_points[optimal_breaks]],
        f_statistics=f_stats,
        bic_values=bic_values,
        ssr_values=ssr_values
    )
```

## Volatility Modeling

### GARCH Models for Price Volatility

```python
def estimate_garch(
    returns: np.ndarray,
    p: int = 1,
    q: int = 1
) -> GARCHResults:
    """
    Estimate GARCH(p,q) model for volatility.
    
    r_t = μ + ε_t
    ε_t = σ_t * z_t, z_t ~ N(0,1)
    σ²_t = ω + Σα_i*ε²_{t-i} + Σβ_j*σ²_{t-j}
    """
    from arch import arch_model
    
    # Specify and fit model
    model = arch_model(
        returns,
        vol='Garch',
        p=p,
        q=q,
        dist='normal'
    )
    
    results = model.fit(disp='off')
    
    # Extract conditional volatility
    conditional_vol = results.conditional_volatility
    
    # Calculate volatility persistence
    alpha_sum = sum(results.params[f'alpha[{i}]'] for i in range(1, p + 1))
    beta_sum = sum(results.params[f'beta[{i}]'] for i in range(1, q + 1))
    persistence = alpha_sum + beta_sum
    
    # Half-life of volatility shocks
    if persistence < 1:
        half_life = np.log(0.5) / np.log(persistence)
    else:
        half_life = np.inf
    
    # Unconditional volatility
    omega = results.params['omega']
    if persistence < 1:
        uncond_vol = np.sqrt(omega / (1 - persistence))
    else:
        uncond_vol = np.nan
    
    return GARCHResults(
        params=results.params,
        conditional_volatility=conditional_vol,
        persistence=persistence,
        half_life=half_life,
        unconditional_volatility=uncond_vol,
        aic=results.aic,
        bic=results.bic
    )
```

## See Also

- [Panel Models](panel-models.md) - Panel data specifications
- [Cointegration](cointegration.md) - Detailed cointegration methods
- [Threshold Models](threshold-models.md) - Regime-switching models
- [Unit Root Tests](../statistical-tests/unit-root-tests.md) - Stationarity testing