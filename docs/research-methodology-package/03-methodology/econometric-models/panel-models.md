# Panel Data Model Implementations

**Target Audience**: Econometricians, Researchers  
**Module**: `yemen_market.models.three_tier.tier1_pooled`

## Overview

This document details the technical implementation of panel data models used in Tier 1 of the Yemen Market Integration analysis. The focus is on handling three-dimensional panel data (market × commodity × time) using fixed effects models with robust standard errors.

## Core Model Specification

### Basic Panel Model

The fundamental specification for the pooled panel model:

$$P_{i,j,t} = \alpha + \theta_i + \phi_j + \tau_t + \delta \cdot Conflict_{i,t} + \beta' X_{i,j,t} + \varepsilon_{i,j,t}$$

Where:
- $P_{i,j,t}$: Log price in market $i$, commodity $j$, time $t$
- $\theta_i$: Market fixed effects (28 markets)
- $\phi_j$: Commodity fixed effects (23 commodities)
- $\tau_t$: Time fixed effects (monthly)
- $Conflict_{i,t}$: Conflict intensity in market $i$ at time $t$
- $X_{i,j,t}$: Control variables vector

### Implementation Details

```python
from linearmodels import PanelOLS
import pandas as pd
import numpy as np

def estimate_pooled_panel_model(
    data: pd.DataFrame,
    outcome_var: str = 'log_price',
    treatment_var: str = 'conflict_intensity',
    control_vars: list = None,
    entity_effects: bool = True,
    time_effects: bool = True
) -> PanelModelResults:
    """
    Estimate pooled panel model with multi-way fixed effects.
    
    Implementation notes:
    - Entity defined as market-commodity pairs
    - Time effects at monthly level
    - Driscoll-Kraay standard errors for spatial correlation
    """
    # Create entity variable
    data['entity'] = data['market_id'] + '_' + data['commodity']
    
    # Set multi-index
    panel_data = data.set_index(['entity', 'date'])
    
    # Define model formula
    formula_parts = [outcome_var, '~', treatment_var]
    
    if control_vars:
        formula_parts.extend(['+'] + [f' + {var}' for var in control_vars])
    
    formula = ''.join(formula_parts)
    
    # Add fixed effects
    if entity_effects:
        formula += ' + EntityEffects'
    if time_effects:
        formula += ' + TimeEffects'
    
    # Estimate model
    model = PanelOLS.from_formula(
        formula=formula,
        data=panel_data,
        drop_absorbed=True
    )
    
    # Fit with Driscoll-Kraay standard errors
    results = model.fit(
        cov_type='kernel',  # Driscoll-Kraay
        kernel='bartlett',
        bandwidth=3  # 3 periods for monthly data
    )
    
    return results
```

## Fixed Effects Transformation

### Within Transformation

The within transformation removes entity-specific means:

$$\tilde{y}_{it} = y_{it} - \bar{y}_i$$

Where $\bar{y}_i = \frac{1}{T_i} \sum_{t=1}^{T_i} y_{it}$

```python
def within_transform(data: pd.DataFrame, vars: list, entity_var: str) -> pd.DataFrame:
    """
    Apply within transformation to remove entity fixed effects.
    
    This is more memory efficient than creating dummy variables
    for high-dimensional fixed effects.
    """
    transformed = data.copy()
    
    for var in vars:
        # Calculate entity means
        entity_means = data.groupby(entity_var)[var].transform('mean')
        
        # Demean the variable
        transformed[f'{var}_demeaned'] = data[var] - entity_means
    
    return transformed
```

### Two-Way Fixed Effects

For market and commodity fixed effects separately:

```python
def estimate_twoway_fe_model(data: pd.DataFrame) -> TwoWayFEResults:
    """
    Estimate model with separate market and commodity fixed effects.
    
    Specification:
    P_ijt = α + θ_i + φ_j + τ_t + δ*Conflict_it + β'X_ijt + ε_ijt
    """
    # Create dummy variables efficiently
    market_dummies = pd.get_dummies(
        data['market_id'], 
        prefix='market',
        drop_first=True  # Avoid dummy variable trap
    )
    
    commodity_dummies = pd.get_dummies(
        data['commodity'],
        prefix='commodity', 
        drop_first=True
    )
    
    time_dummies = pd.get_dummies(
        data['date'].dt.to_period('M'),
        prefix='month',
        drop_first=True
    )
    
    # Combine all variables
    X = pd.concat([
        data[['conflict_intensity'] + control_vars],
        market_dummies,
        commodity_dummies,
        time_dummies
    ], axis=1)
    
    y = data['log_price']
    
    # Use regularization for high-dimensional FE
    from sklearn.linear_model import Ridge
    
    model = Ridge(alpha=0.01)  # Small regularization
    model.fit(X, y)
    
    # Calculate robust standard errors
    residuals = y - model.predict(X)
    robust_se = calculate_robust_standard_errors(X, residuals)
    
    return TwoWayFEResults(
        coefficients=model.coef_,
        standard_errors=robust_se,
        feature_names=X.columns
    )
```

## Standard Error Calculations

### Driscoll-Kraay Standard Errors

Implementation of Driscoll-Kraay (1998) standard errors that are robust to:
- Heteroskedasticity
- Serial correlation up to lag m
- Cross-sectional dependence

```python
def calculate_driscoll_kraay_se(
    residuals: np.ndarray,
    X: np.ndarray,
    entity_var: np.ndarray,
    time_var: np.ndarray,
    kernel: str = 'bartlett',
    bandwidth: int = 3
) -> np.ndarray:
    """
    Calculate Driscoll-Kraay standard errors.
    
    Based on Driscoll & Kraay (1998) and Hoechle (2007).
    """
    n_entities = len(np.unique(entity_var))
    n_periods = len(np.unique(time_var))
    k = X.shape[1]  # Number of regressors
    
    # Step 1: Calculate time-averaged cross-sectional averages
    X_bar = np.zeros((n_periods, k))
    u_bar = np.zeros(n_periods)
    
    for t, period in enumerate(np.unique(time_var)):
        period_mask = time_var == period
        X_bar[t] = X[period_mask].mean(axis=0)
        u_bar[t] = residuals[period_mask].mean()
    
    # Step 2: Calculate kernel weights
    if kernel == 'bartlett':
        weights = bartlett_kernel(bandwidth)
    elif kernel == 'parzen':
        weights = parzen_kernel(bandwidth)
    else:
        raise ValueError(f"Unknown kernel: {kernel}")
    
    # Step 3: Calculate variance-covariance matrix
    V_DK = np.zeros((k, k))
    
    # Contemporaneous variance
    for t in range(n_periods):
        h_t = X_bar[t] * u_bar[t]
        V_DK += np.outer(h_t, h_t)
    
    # Add lagged covariances with kernel weights
    for lag in range(1, bandwidth + 1):
        w_lag = weights[lag]
        for t in range(lag, n_periods):
            h_t = X_bar[t] * u_bar[t]
            h_t_lag = X_bar[t-lag] * u_bar[t-lag]
            V_DK += w_lag * (np.outer(h_t, h_t_lag) + np.outer(h_t_lag, h_t))
    
    # Step 4: Calculate standard errors
    # Need (X'X)^{-1} from original regression
    XX_inv = np.linalg.inv(X.T @ X)
    
    # Final covariance matrix
    cov_matrix = n_periods * XX_inv @ V_DK @ XX_inv
    
    # Standard errors are square root of diagonal
    se = np.sqrt(np.diag(cov_matrix))
    
    return se

def bartlett_kernel(bandwidth: int) -> np.ndarray:
    """Bartlett (triangular) kernel weights."""
    weights = np.zeros(bandwidth + 1)
    for j in range(bandwidth + 1):
        weights[j] = 1 - j / (bandwidth + 1)
    return weights
```

### Clustered Standard Errors

For clustering at market level:

```python
def calculate_clustered_se(
    X: np.ndarray,
    residuals: np.ndarray,
    clusters: np.ndarray
) -> np.ndarray:
    """
    Calculate cluster-robust standard errors.
    
    Allows for arbitrary correlation within clusters.
    """
    n = len(residuals)
    k = X.shape[1]
    n_clusters = len(np.unique(clusters))
    
    # Calculate (X'X)^{-1}
    XX_inv = np.linalg.inv(X.T @ X)
    
    # Calculate clustered meat matrix
    B_clustered = np.zeros((k, k))
    
    for cluster in np.unique(clusters):
        cluster_mask = clusters == cluster
        X_c = X[cluster_mask]
        u_c = residuals[cluster_mask]
        
        # Cluster contribution
        score_c = X_c.T @ u_c
        B_clustered += np.outer(score_c, score_c)
    
    # Finite sample correction
    G = n_clusters
    N = n
    K = k
    correction = (G / (G - 1)) * ((N - 1) / (N - K))
    
    # Clustered variance-covariance matrix
    V_clustered = correction * XX_inv @ B_clustered @ XX_inv
    
    # Standard errors
    se_clustered = np.sqrt(np.diag(V_clustered))
    
    return se_clustered
```

## Dynamic Panel Models

### First-Difference Estimator

For models with lagged dependent variables:

$$\Delta P_{i,j,t} = \rho \Delta P_{i,j,t-1} + \delta \Delta Conflict_{i,t} + \beta' \Delta X_{i,j,t} + \Delta \varepsilon_{i,j,t}$$

```python
def estimate_first_difference_model(
    data: pd.DataFrame,
    lags: int = 1
) -> FirstDifferenceResults:
    """
    First-difference estimator for dynamic panels.
    
    Removes entity fixed effects and allows for
    lagged dependent variables.
    """
    # Sort data
    data = data.sort_values(['entity', 'date'])
    
    # Create first differences
    diff_vars = ['log_price', 'conflict_intensity'] + control_vars
    
    for var in diff_vars:
        data[f'd_{var}'] = data.groupby('entity')[var].diff()
    
    # Create lagged differences
    for lag in range(1, lags + 1):
        data[f'd_log_price_lag{lag}'] = (
            data.groupby('entity')['d_log_price'].shift(lag)
        )
    
    # Remove missing values from differencing
    data_clean = data.dropna()
    
    # Estimate model
    y = data_clean['d_log_price']
    X_vars = ['d_conflict_intensity'] + [f'd_{var}' for var in control_vars]
    X_vars += [f'd_log_price_lag{lag}' for lag in range(1, lags + 1)]
    
    X = data_clean[X_vars]
    
    # OLS on differences
    from statsmodels.regression.linear_model import OLS
    model = OLS(y, sm.add_constant(X))
    results = model.fit(cov_type='HC1')  # Robust SE
    
    return results
```

### Arellano-Bond GMM Estimator

For addressing endogeneity in dynamic panels:

```python
def estimate_arellano_bond_gmm(
    data: pd.DataFrame,
    lags: int = 2
) -> ArellanoBondResults:
    """
    Arellano-Bond GMM estimator for dynamic panels.
    
    Uses lagged levels as instruments for first differences.
    """
    from linearmodels.panel import FirstDifferenceOLS
    from linearmodels.iv import IV2SLS
    
    # Set up panel
    data = data.set_index(['entity', 'date'])
    
    # Define instruments
    # Use lags 2 and deeper as instruments
    instruments = []
    for lag in range(2, lags + 3):
        data[f'log_price_lag{lag}'] = (
            data.groupby(level='entity')['log_price'].shift(lag)
        )
        instruments.append(f'log_price_lag{lag}')
    
    # First-difference transformation with IV
    formula = 'd_log_price ~ d_log_price_lag1 + d_conflict_intensity'
    formula += ' + '.join([f'd_{var}' for var in control_vars])
    formula += f' | {" + ".join(instruments)}'
    
    # Estimate GMM
    model = IV2SLS.from_formula(formula, data)
    results = model.fit(
        cov_type='clustered',
        cluster_entity=True
    )
    
    # Test for valid instruments
    sargan_test = results.sargan
    first_stage_f = results.first_stage.fstat
    
    return ArellanoBondResults(
        coefficients=results.params,
        standard_errors=results.std_errors,
        sargan_p_value=sargan_test.pvalue,
        first_stage_f=first_stage_f
    )
```

## Unbalanced Panel Handling

### Balance Assessment

```python
def assess_panel_balance(data: pd.DataFrame) -> PanelBalanceReport:
    """
    Assess the balance of panel data.
    """
    # Create entity-time grid
    entities = data['entity'].unique()
    periods = data['date'].unique()
    
    full_grid = pd.MultiIndex.from_product(
        [entities, periods],
        names=['entity', 'date']
    )
    
    # Check actual vs potential observations
    actual_obs = set(zip(data['entity'], data['date']))
    potential_obs = set(full_grid)
    
    missing_obs = potential_obs - actual_obs
    
    # Calculate balance metrics
    n_entities = len(entities)
    n_periods = len(periods)
    n_potential = n_entities * n_periods
    n_actual = len(data)
    
    balance_ratio = n_actual / n_potential
    
    # Pattern of missingness
    missing_by_entity = data.groupby('entity').size()
    missing_by_time = data.groupby('date').size()
    
    return PanelBalanceReport(
        balance_ratio=balance_ratio,
        n_missing=len(missing_obs),
        entities_incomplete=sum(missing_by_entity < n_periods),
        periods_incomplete=sum(missing_by_time < n_entities),
        missing_pattern=analyze_missing_pattern(missing_obs)
    )
```

### Handling Unbalanced Panels

```python
def handle_unbalanced_panel(
    data: pd.DataFrame,
    method: str = 'listwise',
    min_obs_per_entity: int = 10
) -> pd.DataFrame:
    """
    Handle unbalanced panel data.
    
    Methods:
    - 'listwise': Keep only entities with sufficient observations
    - 'interpolate': Fill missing values with interpolation
    - 'forward_fill': Forward fill missing values
    """
    if method == 'listwise':
        # Keep entities with minimum observations
        entity_counts = data.groupby('entity').size()
        valid_entities = entity_counts[
            entity_counts >= min_obs_per_entity
        ].index
        
        return data[data['entity'].isin(valid_entities)]
    
    elif method == 'interpolate':
        # Interpolate within entities
        data = data.sort_values(['entity', 'date'])
        
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            data[col] = data.groupby('entity')[col].transform(
                lambda x: x.interpolate(method='time', limit=3)
            )
        
        return data
    
    elif method == 'forward_fill':
        # Forward fill within entities
        data = data.sort_values(['entity', 'date'])
        
        fill_cols = ['log_price'] + control_vars
        data[fill_cols] = data.groupby('entity')[fill_cols].ffill(limit=2)
        
        return data
```

## Testing and Diagnostics

### Poolability Test

Test whether pooling is appropriate:

```python
def test_poolability(data: pd.DataFrame) -> PoolabilityTestResult:
    """
    Test whether data can be pooled across entities.
    
    H0: Coefficients are the same across entities
    H1: Coefficients differ across entities
    """
    # Estimate pooled model
    pooled_results = estimate_pooled_panel_model(data)
    pooled_rss = pooled_results.resid_ss
    
    # Estimate separate models for each entity
    entity_rss = 0
    entity_params = []
    
    for entity in data['entity'].unique():
        entity_data = data[data['entity'] == entity]
        
        if len(entity_data) < 20:  # Skip small entities
            continue
            
        # Simple OLS for each entity
        X = entity_data[['conflict_intensity'] + control_vars]
        y = entity_data['log_price']
        
        model = OLS(y, sm.add_constant(X))
        results = model.fit()
        
        entity_rss += results.ssr
        entity_params.append(results.params)
    
    # Chow test statistic
    n = len(data)
    k = len(control_vars) + 2  # Including constant and treatment
    n_entities = len(entity_params)
    
    f_stat = ((pooled_rss - entity_rss) / ((n_entities - 1) * k)) / (
        entity_rss / (n - n_entities * k)
    )
    
    p_value = 1 - stats.f.cdf(f_stat, (n_entities - 1) * k, n - n_entities * k)
    
    return PoolabilityTestResult(
        f_statistic=f_stat,
        p_value=p_value,
        reject_pooling=p_value < 0.05
    )
```

## Performance Optimization

### Chunked Estimation

For very large panels:

```python
def estimate_large_panel_chunked(
    data_path: str,
    chunk_size: int = 100000
) -> ChunkedPanelResults:
    """
    Estimate panel model on large data using chunks.
    """
    # Initialize accumulators
    XX = None
    Xy = None
    n_obs = 0
    
    # Process in chunks
    for chunk in pd.read_csv(data_path, chunksize=chunk_size):
        # Prepare chunk data
        chunk = prepare_panel_data(chunk)
        
        # Create design matrix
        X = create_design_matrix(chunk)
        y = chunk['log_price'].values
        
        # Accumulate cross products
        if XX is None:
            XX = X.T @ X
            Xy = X.T @ y
        else:
            XX += X.T @ X
            Xy += X.T @ y
        
        n_obs += len(chunk)
    
    # Solve normal equations
    coefficients = np.linalg.solve(XX, Xy)
    
    # Calculate standard errors (requires second pass)
    se = calculate_chunked_standard_errors(
        data_path, coefficients, chunk_size
    )
    
    return ChunkedPanelResults(
        coefficients=coefficients,
        standard_errors=se,
        n_obs=n_obs
    )
```

## See Also

- [Time Series Models](time-series.md) - Time series specifications
- [Threshold Models](threshold-models.md) - Regime-switching models
- [Diagnostic Tests](../statistical-tests/diagnostic-tests.md) - Panel diagnostics
- [API Reference: PooledPanelModel](../../03-api-reference/models/three_tier/tier1_pooled.md)