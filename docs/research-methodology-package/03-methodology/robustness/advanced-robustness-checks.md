# Advanced Robustness Checks for Yemen Market Integration Analysis

**Target Audience**: Econometricians, Research Methodologists  
**Module**: `yemen_market.models.three_tier.diagnostics.advanced`

## Overview

This document details advanced robustness checks that complement the [standard robustness framework](../../04-implementation/diagnostics/robustness-checks.md). These methods specifically address the 3D panel structure (market × commodity × time) and conflict context of the Yemen market integration analysis.

## Theoretical Foundation

The baseline three-tier methodology assumes:
- **Additive fixed effects** capture unobserved heterogeneity
- **Conflict intensity** has homogeneous effects across markets
- **Spatial dependencies** are adequately controlled through HAC standard errors
- **Linear specifications** appropriately capture conflict-price relationships

These advanced checks test robustness to violations of these assumptions.

## 1. Interactive Fixed Effects (IFE) Models

### Motivation

Standard additive fixed effects (market + commodity + time) may inadequately control for unobserved common factors affecting prices across markets and commodities, especially complex shocks in conflict zones.

### Method

Interactive Fixed Effects models (Bai, 2009) estimate:

```
P_ict = α_i + λ_t + β·Conflict_it + γ·Controls_ict + f_t'·λ_i + ε_ict
```

Where:
- `f_t` = r×1 vector of unobserved common factors
- `λ_i` = r×1 vector of factor loadings (heterogeneous across entities)
- Factors and loadings estimated jointly with parameters

### Implementation Strategy

1. **PCA Proxy Method**: Extract principal components from FE model residuals
2. **Specialized Estimation**: Use PyHDFE or custom implementation
3. **Comparison**: Compare β estimates between standard FE and IFE models

### Robustness Criterion

Results are robust if:
- Conflict coefficient remains significant and similar magnitude
- Factor loadings are economically interpretable
- Model fit improves substantially

**Reference**: Bai, J. (2009). Panel data models with interactive fixed effects. *Econometrica*, 77(4), 1229-1279.

## 2. Explicit Spatial Econometric Models

### Motivation

Market integration is inherently spatial. While Tier 2 includes spatial lags and Tier 1 uses spatial HAC errors, explicit spatial modeling tests robustness to spatial dependencies in prices (SAR) or residuals (SEM).

### Spatial Autoregressive (SAR) Model

```
P_ict = ρ·W·P_ict + β·Conflict_it + γ·Controls_ict + α_i + λ_t + ε_ict
```

### Spatial Error Model (SEM)

```
P_ict = β·Conflict_it + γ·Controls_ict + α_i + λ_t + u_ict
u_ict = λ·W·u_ict + ε_ict
```

Where `W` is the spatial weights matrix based on market distances.

### Implementation Strategy

1. **Construct spatial weights**: Distance-based or contiguity-based
2. **Estimate SAR/SEM**: Use PySAL panel spatial models
3. **Decompose effects**: Direct vs. indirect (spillover) effects
4. **Compare specifications**: SAR vs. SEM vs. baseline

### Robustness Criterion

Results are robust if:
- Direct conflict effects remain significant
- Spatial parameters are economically meaningful
- Spillover effects align with theoretical expectations

**Reference**: Anselin, L. (2010). Spatial econometrics. In *Handbook of Applied Economic Statistics*.

## 3. Event Study around Major Conflict Escalations

### Motivation

Continuous conflict intensity measures may mask discrete shock effects. Event studies provide alternative identification using specific, major conflict escalations.

### Method

Dynamic difference-in-differences around major events:

```
P_ict = Σ_τ β_τ·(Treated_i × Post_t^τ) + γ·Controls_ict + α_i + λ_t + ε_ict
```

Where:
- `Treated_i` = markets highly affected by specific conflict event
- `Post_t^τ` = time relative to event (τ periods before/after)
- `β_τ` = dynamic treatment effects

### Implementation Strategy

1. **Identify major events**: Offensives, blockades, escalations
2. **Define treatment**: Geographic proximity or intensity thresholds
3. **Estimate dynamics**: Pre-trends and post-event evolution
4. **Validate parallel trends**: Test pre-event coefficient equality

### Robustness Criterion

Results are robust if:
- Event study effects align with continuous measure findings
- No significant pre-trends (parallel trends assumption)
- Effect magnitudes are economically consistent

**Reference**: Cunningham, S. (2021). *Causal Inference: The Mixtape*.

## 4. Disaggregated Conflict Type Analysis

### Motivation

Aggregate conflict measures may mask heterogeneity across conflict types, potentially introducing measurement error.

### Method

Decompose conflict into specific types:

```
P_ict = β₁·Battles_it + β₂·Airstrikes_it + β₃·Protests_it + γ·Controls_ict + α_i + λ_t + ε_ict
```

### Implementation Strategy

1. **Classify conflict events**: Use ACLED event type classifications
2. **Test joint significance**: F-test for all conflict types
3. **Compare magnitudes**: Which types drive aggregate effects?
4. **Validate aggregation**: Test if weighted sum equals aggregate coefficient

### Robustness Criterion

Results are robust if:
- Joint test confirms significant conflict effects
- Individual coefficients align with theoretical priors
- Aggregate effect is well-represented by weighted components

## 5. Random Coefficients Model

### Motivation

Baseline assumes homogeneous conflict effects across markets (beyond interactions). Random coefficients allow flexible heterogeneity.

### Method

```
P_ict = (β + u_i)·Conflict_it + γ·Controls_ict + α_i + λ_t + ε_ict
```

Where:
- `β` = average conflict effect
- `u_i ~ N(0, σ_u²)` = market-specific random deviation
- Estimated via mixed-effects models

### Implementation Strategy

1. **Estimate mixed model**: Allow random slope for conflict
2. **Test variance significance**: Is σ_u² significantly > 0?
3. **Examine heterogeneity**: Which markets have largest deviations?
4. **Compare averages**: Does β match fixed effects estimate?

### Robustness Criterion

Results are robust if:
- Average random coefficient ≈ fixed coefficient
- Heterogeneity is economically interpretable
- Main conclusions unchanged despite heterogeneity

**Reference**: Wooldridge, J. M. (2010). *Econometric Analysis of Cross Section and Panel Data*.

## Implementation Guidelines

### Data Requirements

- **Spatial weights matrix**: Market coordinates for distance calculations
- **Event database**: Major conflict escalations with dates/locations
- **Disaggregated conflict**: ACLED event type classifications
- **Auxiliary variables**: For factor analysis and heterogeneity exploration

### Computational Considerations

- **IFE models**: Computationally intensive, may require dimension reduction
- **Spatial models**: Memory requirements for large spatial weights matrices
- **Event studies**: Sufficient observations around event windows
- **Random coefficients**: Convergence issues with complex random structures

### Reporting Standards

For each robustness check, report:
1. **Method description**: Brief theoretical motivation
2. **Key results**: Coefficient estimates and standard errors
3. **Comparison**: How results differ from baseline
4. **Interpretation**: Economic significance of differences
5. **Robustness assessment**: Pass/fail based on criteria

## Integration with Main Analysis

These advanced checks complement the [standard robustness framework](../../04-implementation/diagnostics/robustness-checks.md) and should be implemented after:

1. **Baseline estimation**: Three-tier methodology complete
2. **Standard diagnostics**: Unit roots, cointegration, specification tests
3. **Basic robustness**: Specification, sample, measurement sensitivity

Results inform the overall robustness assessment and strengthen confidence in the revolutionary exchange rate mechanism discovery.

## See Also

- [Standard Robustness Checks](../../04-implementation/diagnostics/robustness-checks.md)
- [Instrumental Variables Strategy](../identification/instrumental-variables-strategy.md)
- [Python Implementation Examples](../../04-implementation/code-examples/advanced-robustness-implementations.py)
- [Three-Tier Methodology](../econometric-models/README.md)
