# Instrumental Variables Strategy for Conflict Endogeneity

**Target Audience**: Econometricians, Research Methodologists  
**Module**: `yemen_market.models.three_tier.identification`

## Overview

This document details instrumental variable strategies to address potential endogeneity of conflict intensity in the Yemen market integration analysis. Conflict may be endogenous due to reverse causality (prices affecting conflict) or omitted variables (factors affecting both conflict and markets).

## Endogeneity Concerns

### Reverse Causality

- **High prices → Social unrest**: Food price spikes can trigger protests and conflict
- **Market disruption → Conflict escalation**: Economic stress may intensify existing tensions
- **Resource competition**: Price changes affect resource availability and competition

### Omitted Variable Bias

- **Political instability**: Affects both conflict likelihood and market functioning
- **Governance quality**: Influences both security and economic institutions
- **External interventions**: Impact both conflict dynamics and market access

## Instrumental Variable Candidates

### 1. Rainfall Deviations

**Instrument**: Deviations from long-term rainfall averages in market regions

**Relevance**: 
- Rainfall affects agricultural productivity and water availability
- Resource scarcity triggers competition and displacement
- Well-documented in conflict literature (<PERSON> et al., 2004)

**Exclusion Restriction**:
Rainfall affects prices *only* through conflict, conditional on:
- Market and time fixed effects
- Agricultural season controls
- Global commodity price controls
- Regional weather patterns

**Data Source**: CHIRPS gridded rainfall data

**Implementation**:
```
Rainfall_Deviation_it = (Rainfall_it - Rainfall_LongTerm_i) / SD_Rainfall_i
```

### 2. Lagged Conflict in Neighboring Regions

**Instrument**: Spatial lag of conflict intensity in neighboring markets (t-1)

**Relevance**:
- Conflict exhibits strong spatial spillovers
- Past neighboring conflict predicts current local conflict
- Captures conflict diffusion processes

**Exclusion Restriction**:
Neighboring past conflict affects local current prices *only* through current local conflict, conditional on:
- Market fixed effects (control for persistent spatial factors)
- Time fixed effects (control for common shocks)
- Own lagged conflict (control for persistence)

**Implementation**:
```
Neighbor_Conflict_Lag_it = Σ_j w_ij × Conflict_j,t-1
```
Where `w_ij` is spatial weight (inverse distance)

### 3. Oil Price × Proximity to Oil Infrastructure

**Instrument**: Global oil price changes interacted with market proximity to oil infrastructure

**Relevance**:
- Oil price changes affect conflict financing and incentives
- Proximity to oil infrastructure amplifies these effects
- Global oil prices are exogenous to Yemen

**Exclusion Restriction**:
The interaction affects local prices *only* via local conflict, conditional on:
- Time fixed effects (control for average oil price effects)
- Market fixed effects (control for persistent proximity effects)
- Direct oil price effects

**Implementation**:
```
Oil_Proximity_it = Oil_Price_t × (1 / Distance_to_Oil_i)
```

### 4. Distance to Border × Cross-Border Events

**Instrument**: Market distance to international border interacted with major cross-border events

**Relevance**:
- Cross-border events (interventions, policy changes) affect conflict
- Effects vary by distance to border
- External events are plausibly exogenous

**Exclusion Restriction**:
The interaction affects local prices *only* via local conflict, conditional on:
- Market fixed effects (control for persistent border effects)
- Time fixed effects (control for average event effects)
- Direct border distance effects

**Implementation**:
```
Border_Event_it = (1 / Distance_to_Border_i) × Cross_Border_Event_t
```

## Estimation Strategy

### Panel IV 2SLS

**First Stage**:
```
Conflict_it = π₁·Rainfall_Deviation_it + π₂·Neighbor_Conflict_Lag_it 
            + π₃·Oil_Proximity_it + π₄·Border_Event_it
            + γ·Controls_ict + α_i + λ_t + v_it
```

**Second Stage**:
```
Price_ict = β·Conflict_it_hat + γ·Controls_ict + α_i + λ_t + ε_ict
```

### Diagnostic Tests

**First Stage Strength**:
- F-statistic > 10 (Stock-Yogo critical values)
- Individual instrument significance
- Partial R² of excluded instruments

**Overidentification**:
- Sargan-Hansen J-test (if multiple instruments)
- H₀: All instruments are valid

**Weak Instruments**:
- Stock-Yogo weak instrument tests
- Anderson-Rubin confidence intervals

## Implementation Guidelines

### Data Construction

**Rainfall Data**:
1. Download CHIRPS monthly precipitation (0.05° resolution)
2. Extract values for market coordinates
3. Calculate long-term means (1981-2010)
4. Compute standardized deviations

**Spatial Weights**:
1. Calculate great circle distances between markets
2. Create inverse distance weights (w_ij = 1/distance_ij)
3. Row-standardize weights matrix
4. Apply to lagged conflict measures

**Oil Infrastructure**:
1. Geocode oil fields, pipelines, refineries
2. Calculate minimum distance from each market
3. Interact with global oil price (Brent crude)

**Cross-Border Events**:
1. Identify major external interventions/policy changes
2. Create binary indicators for event periods
3. Interact with inverse distance to nearest border

### Robustness Checks

**Alternative Instruments**:
- Temperature deviations (alternative weather shock)
- Commodity price shocks × agricultural dependence
- Religious/cultural event timing × local importance

**Alternative Specifications**:
- Different lag structures for spatial instruments
- Alternative distance decay functions
- Threshold effects in instrument construction

**Subsample Analysis**:
- Exclude markets near borders (test border instrument)
- Exclude agricultural markets (test rainfall instrument)
- Different time periods (test temporal stability)

## Expected Results

### First Stage Predictions

**Rainfall**: Negative coefficient (drought increases conflict)
**Spatial lag**: Positive coefficient (conflict spillovers)
**Oil interaction**: Positive coefficient (higher prices increase conflict near infrastructure)
**Border interaction**: Positive coefficient (external events affect border areas more)

### Second Stage Implications

If conflict is endogenous due to:
- **Reverse causality**: IV estimates likely larger than OLS
- **Omitted variables**: Direction depends on bias direction

### Policy Implications

IV estimates provide causal interpretation for:
- Conflict prevention strategies
- Market stabilization policies
- Humanitarian intervention targeting

## Limitations and Caveats

### Exclusion Restrictions

- **Rainfall**: May directly affect agricultural prices
- **Spatial lags**: May capture market integration effects
- **Oil prices**: May have direct effects on transport costs
- **Border events**: May affect trade flows directly

### Data Quality

- **Rainfall measurement error**: Gridded data vs. local conditions
- **Conflict spillover timing**: Exact lag structure uncertain
- **Infrastructure proximity**: Static measures vs. dynamic importance

### External Validity

- **Yemen-specific factors**: Instruments may not generalize
- **Time period sensitivity**: Conflict dynamics may evolve
- **Market heterogeneity**: Effects may vary across market types

## Integration with Three-Tier Framework

### Tier 1 Enhancement

Replace endogenous conflict with IV estimates in pooled panel models

### Tier 2 Implications

Use IV-based conflict measures in threshold VECM specifications

### Tier 3 Validation

Compare IV results with factor-based approaches for consistency

## See Also

- [Advanced Robustness Checks](../robustness/advanced-robustness-checks.md)
- [Econometric Research Plan](ECONOMETRIC_RESEARCH_PLAN.md)
- [Python Implementation](../../04-implementation/code-examples/instrumental-variables-examples.py)
- [Diagnostic Tests](../../04-implementation/diagnostics/diagnostic-tests.md)
