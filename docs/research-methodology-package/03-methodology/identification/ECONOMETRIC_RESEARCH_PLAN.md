# Econometric Research Plan: The Yemen Conflict Price Puzzle

## Title
"When Conflict Lowers Prices: Evidence from Yemen's Fragmented Markets"

## Core Research Question
Why do high-conflict areas in Yemen show LOWER prices, contradicting standard economic theory?

## Theoretical Framework

### 1. Model Setup
Let price in market i at time t be:
```
P_it = α_i + β₁Supply_it + β₂Demand_it + β₃Aid_it + γConflict_it + ε_it
```

Where conflict affects all three channels:
- Supply effect (+): Conflict → Trade costs ↑ → Prices ↑
- Demand effect (-): Conflict → Population ↓ → Purchasing power ↓ → Prices ↓  
- Aid effect (-): Conflict → Humanitarian aid ↑ → Prices ↓

**Key Insight**: When β₂ + β₃ > β₁, conflict lowers prices

### 2. Testable Hypotheses
- H1: Negative price premiums stronger for non-essential goods (demand elastic)
- H2: Effect reverses in markets without aid access
- H3: Price transmission weakens with conflict intensity
- H4: Hyperinflation (292-540%) driven by monetary fragmentation

## Identification Strategy

### 1. Instrumental Variables Approach
```
First Stage: Conflict_it = π₀ + π₁Z_it + π₂X_it + ν_it
Second Stage: P_it = α_i + βConflict_it_hat + δX_it + ε_it
```

**Proposed Instruments**:
- Z1: Conflict in neighboring districts (spatial spillovers)
- Z2: Interaction of global oil prices × pre-war military bases
- Z3: Timing of territorial control changes (RDD approach)
- Z4: Distance to conflict frontlines × time

### 2. Panel Specifications

#### Specification 1: Fixed Effects Baseline
```
P_ict = α_ic + β₁Conflict_it + β₂Controls_it + θ_t + ε_ict
```
- α_ic: Market-commodity fixed effects
- θ_t: Time fixed effects
- Cluster SE at market level

#### Specification 2: First Differences (for non-stationary series)
```
ΔP_ict = β₁ΔConflict_it + β₂ΔControls_it + Δε_ict
```

#### Specification 3: Error Correction Model
```
ΔP_ict = ρ(P_ic,t-1 - γX_ic,t-1) + β₁ΔConflict_it + ε_ict
```

#### Specification 4: Threshold VECM (regime-switching)
```
ΔP_ict = {
  ρ₁ECM_t-1 + β₁ΔX_it if Conflict_it < τ
  ρ₂ECM_t-1 + β₂ΔX_it if Conflict_it ≥ τ
}
```

### 3. Mechanism Tests

#### Test 1: Demand Destruction Channel
- Use population displacement data as proxy
- Interact conflict with pre-war population density
- Expected: Stronger negative effect in densely populated areas

#### Test 2: Aid Distribution Channel  
- Exploit variation in humanitarian access
- Use distance to aid distribution centers
- Expected: Negative premiums only where aid present

#### Test 3: Supply Chain Channel
- Measure distance to ports/borders
- Interact with global commodity prices
- Expected: Positive premiums for import-dependent goods

## Robustness Battery

### 1. Sample Sensitivity
- [ ] Exclude capital cities (Sana'a, Aden)
- [ ] Exclude markets with >50% missing data
- [ ] Pre/post 2021 coalition change
- [ ] Winsorize at 1%, 5% levels

### 2. Alternative Conflict Measures
- [ ] Binary (control vs non-control areas)
- [ ] Continuous (conflict events count)
- [ ] Intensity (fatalities per capita)
- [ ] Duration (months under non-government control)

### 3. Specification Tests
- [ ] Ramsey RESET for functional form
- [ ] Chow test for structural breaks
- [ ] Hansen J-test for overidentification
- [ ] Weak instrument F-statistics

### 4. Placebo Tests
- [ ] Use pre-war data (2014-2018)
- [ ] Randomize conflict assignment
- [ ] Use non-food commodities
- [ ] Lag structure analysis

### 5. Standard Error Corrections
- [ ] Cluster by market
- [ ] Cluster by governorate
- [ ] Two-way cluster (market × time)
- [ ] Conley spatial HAC errors
- [ ] Wild cluster bootstrap

## Econometric Workflow

### Phase 1: Data Preparation (Week 1)
```python
# 1. Handle non-stationarity
python scripts/prepare_data_for_modeling.py --first-difference

# 2. Create instrument variables  
python scripts/create_instruments.py --spatial --oil-interaction

# 3. Generate mechanism variables
python scripts/create_mechanism_tests.py --population --aid --trade
```

### Phase 2: Core Estimation (Week 2)
```python
# 1. Run main specifications
python scripts/run_econometric_models.py --all-specifications

# 2. Perform diagnostic tests
python scripts/run_diagnostics.py --reset --chow --hansen

# 3. Generate tables
python scripts/generate_publication_tables.py --latex
```

### Phase 3: Robustness (Week 3)
```python
# 1. Sample sensitivity
python scripts/robustness_checks.py --sample-splits

# 2. Alternative measures
python scripts/robustness_checks.py --conflict-measures

# 3. Placebo tests
python scripts/robustness_checks.py --placebo
```

### Phase 4: Visualization (Week 4)
```python
# 1. Main results
python scripts/create_figure1_price_premiums.py

# 2. Mechanisms
python scripts/create_figure2_mechanisms.py

# 3. Event studies
python scripts/create_figure3_event_study.py
```

## Expected Results & Interpretation

### 1. Main Finding
- Conflict reduces prices by 15-25% (not 35% as claimed)
- Effect driven by demand collapse and aid flows
- Supply disruption effects dominated in extreme crisis

### 2. Heterogeneity
- Negative premiums for: Wheat, rice (aid commodities)
- Positive premiums for: Fuel, qat (non-aid items)
- No effect: Local vegetables (produced nearby)

### 3. Policy Implications
- Cash transfers may be more effective than assumed
- Market integration persists despite conflict
- Aid distribution affects price equilibria

## Paper Structure

### 1. Introduction
- Hook: Conflict lowers prices (puzzle)
- Context: Yemen's humanitarian crisis
- Contribution: Theory + identification + policy

### 2. Theoretical Framework
- Multi-channel conflict effects model
- Predictions for heterogeneous impacts
- Welfare implications

### 3. Context and Data
- Yemen's market structure
- Data sources and construction
- Descriptive evidence of puzzle

### 4. Empirical Strategy
- Identification challenges
- IV approach and instruments
- Multiple specifications

### 5. Results
- Main effects (negative premiums)
- Mechanisms (demand vs supply)
- Robustness battery

### 6. Policy Implications
- Humanitarian response design
- Cash vs in-kind transfers
- Market-based interventions

### 7. Conclusion
- Summary of findings
- Broader implications
- Future research

## Target Journals

### Tier 1 Options
1. **Journal of Development Economics** - Methods focus
2. **Journal of Conflict Resolution** - Conflict angle
3. **World Bank Economic Review** - Policy relevance

### Tier 2 Options
1. **World Development** - Interdisciplinary
2. **Food Policy** - Commodity focus
3. **Journal of African Economies** - Regional

## Critical Success Factors

1. **Nail the identification** - Reviewers will attack endogeneity
2. **Explain the mechanism** - Why demand dominates supply
3. **Policy relevance** - Connect to humanitarian response
4. **Robust, robust, robust** - Every conceivable check

## Timeline

- Week 1-2: Finalize empirical specifications
- Week 3-4: Complete all estimations
- Week 5-6: Write first draft
- Week 7-8: Internal review and revisions
- Week 9-10: Prepare submission package

This econometric backbone will support a significant contribution to both conflict economics and humanitarian policy literature.