# Working Sessions - Phase 2 Empirical Testing

This directory supports active Phase 2 empirical testing of the revolutionary exchange rate discovery.

## Phase 2 Focus: Testing the Revolutionary Discovery
**Core Discovery**: Exchange rate divergence (535 vs 2000+ YER/USD) explains negative price premiums in conflict zones.

**Primary Objective**: Validate H1 through empirical testing showing currency zone effects disappear in USD price analysis.

## Current Priorities
1. **H1 Exchange Rate Mechanism Testing** (Week 1-2)
2. **Advanced Robustness Methods Deployment** (Week 3-4) 
3. **Network & Political Economy Validation** (Week 5-6)

## Active Session Structure
Create subdirectories for major analyses:
- `2025-01-06-h1-exchange-rate-testing/`
- `2025-01-13-advanced-robustness-checks/`
- `2025-01-20-network-political-economy/`

## Implementation Resources
- **Implementation Guides**: `../04-implementation/code-mappings/`
- **Advanced Methods**: `external-reviews/`
- **Execution Plan**: `PHASE_2_EXECUTION_PLAN.md`

## Quick Start for H1 Testing
```python
# 1. Assign currency zones (535 vs 2000+ YER/USD)
df = assign_currency_zones(df)
df = map_exchange_rates(df) 
df = convert_prices_by_zone(df)

# 2. Run core test
results = test_exchange_rate_mechanism(df)

# 3. Validate: Currency zone coefficient ≈ 0 in USD terms
```

## Quick Commands
```bash
# Create new session
mkdir $(date +%Y-%m-%d)-topic-name

# Archive old sessions
mv 2024-* ../archive/working-sessions/
```