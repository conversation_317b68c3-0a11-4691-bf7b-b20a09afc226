# Complete Context Knowledge: Yemen Market Integration Project

## 🎯 Core Research Discovery

**The Paradox**: High-conflict areas in Yemen show LOWER prices, contradicting standard economic theory.

**The Solution**: Exchange rate divergence explains this:
- **Houthi areas**: 535-539 YER/USD (stable, controlled)
- **Government areas**: 2,000-2,150 YER/USD (4x depreciation!)
- When analyzed in USD, the "negative price premiums" disappear

## 📊 Project Overview

### Research Question Evolution
1. **Initial**: Why do conflict areas have lower prices?
2. **Refined**: How does currency fragmentation affect market integration?
3. **Current**: Can exchange rate mechanisms explain apparent market anomalies in conflict settings?

### Key Hypotheses (H1-H10)
- **H1**: Exchange Rate Mechanism - Negative premiums disappear in USD
- **H2**: Aid Distribution Channel - Cash aid depresses local prices
- **H3**: Demand Destruction - Population displacement reduces demand
- **H4**: Import Channels - Different access to foreign currency
- **H5**: Market Power - Trader concentration varies by control zone
- **H6**: Information Asymmetry - Price discovery limited across lines
- **H7**: Storage Constraints - Conflict reduces storage capacity
- **H8**: Transport Networks - Route availability affects arbitrage
- **H9**: Regulatory Divergence - Different price controls by authority
- **H10**: Expectations Channel - Future uncertainty affects pricing

## 🏗️ Technical Architecture

### Three-Tier Econometric Framework

#### Tier 1: Pooled Panel Analysis
```python
# Fixed effects with Driscoll-Kraay standard errors
# Handles cross-sectional dependence
# 44,000+ observations (50 markets × 23 commodities × 38 months)
```

#### Tier 2: Commodity-Specific Models
```python
# Threshold Vector Error Correction Models (TVECM)
# Tests for regime switching at conflict thresholds
# Cointegration analysis by currency zone
```

#### Tier 3: Policy Validation
```python
# Factor analysis for common shocks
# Conflict spillover analysis
# Natural experiment identification
```

## 📁 Current Project State

### Documentation Structure (Just Reorganized)
```
docs/
├── 00-getting-started/     ✅ Complete
├── 01-architecture/        ✅ Complete
├── 02-user-guides/        ✅ Complete
├── 03-api-reference/      ✅ Complete
├── 04-development/        ✅ Complete
├── 05-methodology/        ✅ Complete
├── 06-deployment/         ✅ Just created
├── 07-case-studies/       ✅ Just created
├── 08-results/           ✅ Just created
├── 09-troubleshooting/   ✅ Just created
├── 10-contributing/      ✅ Just created
```

### Key Findings
1. **Market Integration**: Declined by 35% since conflict began
2. **Price Increases**: 292-540% in local currency
3. **Exchange Rate Impact**: 4x differential between zones
4. **Aid Effectiveness**: 26% less effective in volatile currency zones
5. **Natural Experiments**: 
   - 2020 aid cuts → 35-42% price spike
   - 2021 fuel crisis → 3x price increases

## 🗂️ Data Sources

### Price Data
- **WFP VAM**: 3000+ market-commodity pairs
- **FAO GIEWS**: 119 market coverage
- **Local monitors**: High-frequency urban data

### Exchange Rates
- **CBY Aden**: Official government rate
- **CBY Sana'a**: Houthi-controlled rate  
- **Parallel market**: OCHA/UN operational rates
- **Money exchangers**: Ground truth validation

### Conflict Data
- **ACLED**: 10,000+ events geocoded
- **Yemen Data Project**: Civilian impact
- **CIMP**: Humanitarian access

### Aid Distribution
- **OCHA 3W**: Who-What-Where database
- **Cash Consortium**: Transfer amounts
- **Cluster data**: Sectoral distribution

## 🔧 Implementation Details

### v1 Architecture (Current Production)
- Location: `/src/yemen_market/`
- Three-tier models implemented
- Basic panel construction
- Working but needs currency zone support

### v2 Architecture (In Development)
- Location: `/v2/`
- Domain-driven design
- Multi-currency support
- Natural experiment framework
- Policy simulation capabilities

### Critical Code Paths
```python
# Panel construction
src/yemen_market/data/panel_builder.py

# Three-tier models
src/yemen_market/models/three_tier/

# V2 domain models
v2/src/core/domain/

# Analysis pipeline
v2/src/application/services/
```

## 📈 Analysis Pipeline

### 1. Data Preparation
- Currency alignment (YER vs USD)
- Balanced panel creation
- Missing data imputation
- Outlier detection

### 2. Econometric Analysis
- Unit root tests (panel-specific)
- Cointegration testing
- Fixed effects estimation
- Threshold modeling

### 3. Validation
- Robustness checks
- Cross-validation
- Natural experiments
- Policy simulations

## 🚨 Critical Rules (from CLAUDE.md)

1. **ALWAYS CHECK**: Are we analyzing in YER or USD?
2. **Never mix currencies** in the same regression
3. **Exchange rate timing** matters - match price and FX dates
4. **Missing data is non-random** - markets stop reporting during conflict
5. **Aid is endogenous** - requires instrumental variables
6. **Quality changes over time** - same commodity name ≠ same product
7. **Survivorship bias** - only resilient traders remain
8. **Control changes** - track market authority switches

## 🎓 Econometric Specifications

### Main Specification
```stata
* Local currency analysis
reg price_yer conflict exchange_rate global_price aid_pc i.market##i.commodity i.month, cluster(market)

* USD price analysis  
reg price_usd conflict global_price aid_pc i.market##i.commodity i.month, cluster(market)

* First differences (for non-stationarity)
reg D.price_yer D.conflict D.exchange_rate D.global_price D.aid_pc i.month, cluster(market)
```

### Identification Strategy
```stata
* IV for conflict (using spatial lags)
ivregress 2sls price_yer (conflict = conflict_neighbors) controls, cluster(market)

* RD at currency zone boundaries
rd price_yer distance_to_boundary if abs(distance) < bandwidth, fuzzy(houthi_control)
```

## 🏁 Current Status

### Completed
- Phase 1: Literature review, theory, data inventory
- Documentation reorganization (00-10 structure)
- Core three-tier implementation (v1)
- Initial findings and case studies

### In Progress
- v2 architecture finalization
- Multi-currency analysis framework
- Natural experiment identification
- Policy simulation tools

### Next Steps
1. Complete v2 domain models
2. Implement exchange rate mechanism tests
3. Run full hypothesis battery (H1-H10)
4. Prepare World Bank publication

## 💡 Key Insights

1. **Currency matters more than conflict** for price levels
2. **Market integration persists** even across front lines (in USD)
3. **Aid can be counterproductive** without currency adjustment
4. **Natural experiments abound** due to policy shocks
5. **Trader networks adapt** faster than formal institutions

## 📚 References

### Internal Documents
- `/CLAUDE.md` - Complete research overview
- `/docs/PRD_Yemen_Market_Integration.md` - Product requirements
- `/docs/research-methodology-package/` - Research methodology
- `/reports/` - Analysis results and findings

### Key Papers
- Atkin & Donaldson (2015) - Law of one price
- Burke et al. (2023) - Aid in conflict
- Cariolle et al. (2023) - Multi-currency zones

## 🔐 Security & Ethics

- Maintain political neutrality
- Protect market participant identities
- Follow UN data protection standards
- Consider humanitarian implications
- Avoid military/strategic analysis

This represents my complete understanding of the Yemen Market Integration project as of our current conversation.