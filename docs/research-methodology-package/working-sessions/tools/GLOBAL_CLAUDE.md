# Global Claude Configuration

## Code Quality Standards
- No synthetic data generation or fallback mechanisms are allowed
- Do not include placeholders - only code full implementations based on system architecture
- Replace all simplified implementations and examples with full production ready code
- No automated scripts to fix files (causes lint and coding errors)

## Testing Standards
- Always aim for 100% test coverage
- Mock external dependencies appropriately
- Use real test data where available
- Test both success and failure scenarios
- Include edge cases and error conditions

## Project Structure Best Practices
- Follow established architectural patterns
- Use consistent naming conventions
- Maintain clear separation of concerns
- Document complex logic inline
- Keep functions focused and testable

## Error Handling
- Use resilient_operation decorator for critical operations
- Implement proper circuit breakers
- Log errors with appropriate context
- Provide meaningful error messages
- Never swallow exceptions silently