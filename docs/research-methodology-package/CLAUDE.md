# CLAUDE.md - Yemen Market Integration Research Methodology Package

## 🎯 Project Overview
**Research Question**: Why do high-conflict areas in Yemen show LOWER prices, contradicting standard economic theory?

**Core Discovery**: Exchange rate divergence (535 YER/USD in Houthi areas vs 2000+ YER/USD in government areas) explains apparent negative price premiums in conflict zones.

## 💡 Key Research Insights

### Primary Mechanism: Exchange Rate Divergence
- **Houthi-controlled areas**: 535-539 YER/USD (stable, controlled exchange rate)
- **Government-controlled areas**: 2,000-2,150 YER/USD (4x depreciation)
- **USD Analysis**: When converted to USD, prices show expected positive premiums in conflict areas
- **Theoretical Validation**: <PERSON><PERSON><PERSON><PERSON> (1986) dual exchange rate theory confirms multiple equilibria can persist

### Supporting Mechanisms
1. **Humanitarian Aid Effects**: 26% efficiency loss in volatile currency zones (H7 validated)
2. **Demand Destruction**: 90% of traders can handle 100% demand increase - supply not the constraint

## 📊 Current Research Status

### Phase 1 - Foundation Understanding: ✅ COMPLETE
- **Literature Synthesis**: Exchange rates, humanitarian aid, demand destruction
- **Theoretical Framework**: 10 testable hypotheses (H1-H10) 
- **Data Inventory**: 40+ sources identified with access protocols
- **Comparative Analysis**: Syria, Rwanda, Mozambique, Lebanon cases

### Phase 2 - Methodology Development: 🔄 CURRENT
- **Hypothesis Operationalization**: Converting H1-H10 to regression specifications
- **Identification Strategies**: Natural experiments, IV, regression discontinuity
- **Code Implementation**: R, Stata, Python templates
- **Robustness Design**: Spatial HAC, sensitivity analysis, validation procedures

## 🧮 Core Variable Definitions

```python
# Always use these definitions
exchange_rate_houthi = 535  # YER/USD (stable)
exchange_rate_government = 2000+  # YER/USD (depreciated) 
price_usd = price_yer / exchange_rate  # Key transformation
conflict_intensity = log(1 + events)  # Log transformation for count data
aid_per_capita = total_aid / population
```

## 🔬 Testable Hypotheses Framework

### Primary Hypotheses (H1-H3)
- **H1**: Exchange Rate Mechanism - Negative premiums disappear in USD analysis
- **H2**: Aid Distribution Channel - Effects vary by modality and currency zone
- **H3**: Demand Destruction - Purchasing power collapse dominates supply constraints

### Extended Hypotheses (H4-H10)
- **H4**: Currency Zone Switching Effects - Discrete price jumps at boundaries
- **H5**: Cross-Border Arbitrage - Price differentials equal transport + exchange costs
- **H6**: Currency Substitution - USD pricing increases with volatility
- **H7**: Aid Effectiveness Differential - Varies by local currency stability ✅ VALIDATED
- **H8**: Information Spillover - Cross-zone price transmission
- **H9**: Threshold Effects - Non-linear relationship above 100% exchange differential
- **H10**: Long-run Convergence - USD prices converge to global, YER prices don't

## 📊 Data Sources Available

### Exchange Rate Data
- **CBY Aden**: Monthly bulletins with official and parallel rates
- **World Bank**: Governorate-level monthly estimates (2007-present)
- **WFP Economic Explorer**: Real-time visualization, downloadable datasets
- **UN/OCHA**: Integrated humanitarian response monitoring

### Price Data
- **WFP**: 3,000+ markets, March 2009-present, monthly updates
- **FAO**: 119 markets, near real-time, producer price indices
- **Local monitoring**: Inter-agency market monitoring with daily exchange rates

### Aid Distribution Data
- **OCHA 3W**: District-level monthly organizational presence
- **Cash Consortium Yemen**: Multi-purpose cash assistance tracking
- **Cluster coordination**: 244 datasets from 54 organizations

### Conflict Data
- **ACLED**: Weekly updates, forecasting tools, geographic coverage
- **Yemen Data Project**: Air raids, US-UK strikes, detailed incident data
- **CIMP**: Civilian impact monitoring (archived but valuable)

## 📈 Empirical Strategy

### Natural Experiments Identified
1. **2020 Aid Cuts**: Exogenous reduction → 35-42% price spike in 3 months
2. **Currency Zone Boundaries**: Regression discontinuity design
3. **Conflict Switching**: Markets changing territorial control
4. **COVID + Currency Crisis**: Multiple simultaneous shocks

### Key Methodological Innovations
- **Currency-adjusted price analysis**: First systematic approach
- **Triple-difference specification**: Post × Houthi × Import-dependent goods
- **Exchange rate discontinuity design**: At currency zone boundaries
- **Threshold VECM**: Regime switching above 100% exchange differential

## ⚠️ Critical Research Notes

### Always Check Currency Denomination
- Never mix YER and USD prices in same regression
- Exchange rate timing: Transaction rate ≠ Official rate on same date
- Black market premiums vary by location and time

### Common Pitfalls to Avoid
1. **Survivorship Bias**: Markets that stop reporting are systematically different
2. **Aid Endogeneity**: Cannot regress prices on aid without instruments
3. **Aggregation Error**: Don't average prices across different commodities
4. **Missing Data**: Imputation must account for conflict-driven missingness
5. **Seasonal Patterns**: Always include month fixed effects (Ramadan effects)

## 🎯 Policy Implications

### Currency-Aware Aid Strategy
- **Houthi areas**: YER cash aid (leverages stable exchange rate)
- **Government areas**: USD cash aid (hedges against depreciation)
- **Mixed areas**: Flexible denomination based on local preferences

### Market Integration Enhancement
- Focus interventions on contested areas (Zone 3) where marginal improvements have highest returns
- Reduce transaction costs and exchange rate uncertainty in mixed-control markets

## 📍 Directory Navigation

```
research-methodology-package/
├── 01-foundation/           # ✅ Complete literature & theory
├── 02-data/                # Data sources & preparation
├── 03-methodology/         # 🔄 Current - Econometric specifications  
├── 04-implementation/      # Code templates & diagnostics
├── 05-results/            # Findings & case studies
├── 06-paper/              # Publication materials
└── working-sessions/       # Active AI work & tracking
```

## 🚀 Quick Commands for AI Sessions

### Phase 2 Current Priorities
```bash
# Navigate to current work
cd 03-methodology/

# For hypothesis operationalization
"Convert H1 (Exchange Rate Mechanism) into specific regression equation with Yemen data structure"

# For identification strategy
"Design regression discontinuity approach for Yemen currency zone boundaries using distance as running variable"

# For robustness design  
"Specify complete battery of robustness checks for dual currency panel analysis"
```

## 📚 Key References
- **Obstfeld (1986)**: Dual exchange rate theory - multiple equilibria framework
- **World Bank (2022)**: Parallel exchange rate guidance methodology
- **Cunha et al. (2019)**: Cash vs in-kind effects with village randomization
- **Yemen case studies**: ODI (2021), Cash Consortium (2024), ACAPS (2025)

## 🎪 Research Significance
This research represents a **paradigm shift** in conflict economics by:
1. **First systematic integration** of parallel exchange rates with humanitarian market analysis
2. **Novel identification strategies** using currency discontinuities  
3. **Policy-relevant findings** for humanitarian aid effectiveness
4. **Applicable framework** for other fragmented monetary systems (Syria, Somalia, Lebanon)

---

**Remember**: This is groundbreaking econometric research on market integration in fragmented monetary systems. Focus on identification, robustness, and policy relevance for top-tier journal publication.