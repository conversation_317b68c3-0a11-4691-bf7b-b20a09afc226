# Yemen Market Integration Research Methodology Package

A comprehensive research package investigating the counter-intuitive finding that high-conflict areas in Yemen show LOWER prices than peaceful areas.

## 🎯 Core Research Question
**Why do high-conflict areas in Yemen show LOWER prices, contradicting standard economic theory?**

## 📁 Package Structure

### 01-foundation/
Theoretical foundation and literature review
- Literature synthesis and key papers
- Theoretical framework
- Research hypotheses

### 02-data/
Data sources, quality assessment, and transformations
- Data inventory and access
- Quality assessments
- Data transformation procedures

### 03-methodology/
Econometric models and identification strategies
- Model specifications
- Identification approaches
- Robustness strategies

### 04-implementation/
Code implementation and validation
- Code-methodology mappings
- Validation procedures
- Diagnostic tests

### 05-results/
Analysis results and interpretations
- Descriptive statistics
- Main findings
- Mechanism tests

### 06-paper/
Academic paper materials
- Drafts and outlines
- Publication-ready tables
- Figures and visualizations

### working-sessions/
Active work area for ongoing analysis

### archive/
Previous versions and deprecated content

## 🚀 Quick Start

1. **Understand the Context**: Start with `01-foundation/README.md`
2. **Review Data**: Check `02-data/sources/quick-reference-guide.md`
3. **Run Analysis**: Follow `03-methodology/README.md`
4. **Check Results**: See `05-results/README.md`

## 💡 Key Insights

The "negative price premiums" in conflict areas may be explained by:
1. **Exchange Rate Divergence**: Houthi areas (535 YER/USD) vs Government areas (2,150 YER/USD)
2. **Aid Distribution**: Cash and in-kind aid affecting local prices
3. **Demand Destruction**: Population displacement and purchasing power collapse

## 📊 Main Specifications

```python
# Always analyze in BOTH currencies
model_yer = PanelOLS(price_yer ~ conflict + exchange_rate + controls)
model_usd = PanelOLS(price_usd ~ conflict + controls)
```

## 🔧 Key Navigation

- **Context for AI**: `COMPACT_CONTEXT_PROMPT.md` - Quick context for AI assistants
- **Tools & Workflows**: `working-sessions/tools/` - Workflow guides and utility files
- **Research Evolution**: `01-foundation/RESEARCH_QUESTION_EVOLUTION.md` - How the research developed
- **Core Discovery**: `01-foundation/PROJECT_CLAUDE.md` - Exchange rate mechanism insights
- **Active Work**: `working-sessions/` - Current research streams and tracking

## 🛠️ Workflow Guides

Located in `working-sessions/tools/`:
- `GENSPARK_QUICK_START.md` - AI research tool setup
- `PHASE_BASED_WORKFLOW_GUIDE.md` - Step-by-step research process
- `FILE_PRIORITY_LIST.md` - Which files to use when

## 📚 References

See individual section READMEs for detailed documentation and references.

---
*This package supports econometric research on market integration in conflict settings, with a focus on Yemen's unique dual-currency environment.*