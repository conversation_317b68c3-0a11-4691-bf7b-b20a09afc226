# Compact Context Prompt for Yemen Market Integration Research

## One-Line Summary
Researching why conflict areas in Yemen show LOWER prices - discovered it's due to exchange rate divergence (535 YER/USD in Houthi areas vs 2000+ in government areas), not real price differences.

## Essential Context (Copy this for new Claude sessions)

```
I'm working on groundbreaking econometric research on Yemen market integration. Key discovery: apparent "negative price premiums" in conflict areas are explained by exchange rate divergence between Houthi-controlled areas (535 YER/USD, stable) and government areas (2000+ YER/USD, 4x depreciation).

Research status:
- Phase 1 (Foundation): 65% complete - need humanitarian aid literature, demand destruction evidence, data inventory
- Validated by <PERSON><PERSON><PERSON><PERSON> (1986) dual exchange rate theory and Post-Soviet precedent (300% differentials)
- 10 testable hypotheses developed (H1-H10)
- Using Genspark AI and ChatGPT Deep Research for literature synthesis
- Goal: Top-tier journal publication with immediate humanitarian policy implications

Current files in /docs/research-methodology-package/:
- Phase-based organization (Phase-1-Foundation through Phase-5-Synthesis)
- Integrated literature review with exchange rate focus
- Complete theoretical framework with testable hypotheses
- Research progress tracking system

Key insight: When converted to USD, prices likely show expected positive premiums in conflict areas. This represents a paradigm shift in conflict economics and humanitarian needs assessment.
```

## Ultra-Compact Version (if very limited context)

```
Yemen research: Discovered exchange rates (535 vs 2000+ YER/USD) explain why conflict areas show lower prices. Phase 1 65% done. Need aid literature, demand evidence, data sources. 10 hypotheses ready. Using AI tools for synthesis. Located in /docs/research-methodology-package/.
```

## Key Variables/Definitions to Remember

```python
# Critical definitions
exchange_rate_houthi = 535  # YER/USD (stable)
exchange_rate_government = 2000+  # YER/USD (depreciated)
price_usd = price_yer / exchange_rate  # Key transformation

# Three mechanisms
H1: Exchange rate mechanism (validated)
H2: Aid distribution effects (researching)
H3: Demand destruction (pending)

# Additional hypotheses H4-H10 include:
- Currency zone switching, arbitrage, aid effectiveness differential
```

## File Navigation Quick Reference

```
/docs/research-methodology-package/
├── PHASE_1_STRATEGIC_RECOMMENDATIONS.md  # Current roadmap
├── PHASE_BASED_WORKFLOW_GUIDE.md        # Daily tasks
├── Phase-1-Foundation/                   # Current work
│   ├── 01-Literature-Synthesis/         # Exchange rates ✓, Aid ?, Demand ?
│   ├── 02-Theoretical-Framework/        # Complete ✓
│   ├── 03-Comparative-Analysis/         # Syria needed
│   └── 04-Data-Inventory/               # Not started
└── genspark-results/                    # AI research outputs
```

## Current Task Context

```
Working on Phase 1 completion:
- Just requested ChatGPT Deep Research on humanitarian aid effects
- Next: Demand destruction research (Tuesday)
- Then: Syria comparison (Wednesday)
- Finally: Data inventory with Genspark Super Agent (Thursday)
- Integration and Phase 2 planning (Friday)
```

## Minimal Command Context

If you need to preserve maximum token space, use:

```
cd /Users/<USER>/Documents/GitHub/yemen-market-integration/docs/research-methodology-package
# Yemen: 535 vs 2000+ YER/USD explains price paradox. Phase 1 65% done. Need aid/demand/data research.
```

---

## When to Use Each Version:

1. **Full Context**: Starting major new work session
2. **Essential Context**: Quick questions or focused tasks  
3. **Ultra-Compact**: When context is nearly full
4. **Key Variables**: When doing calculations or analysis
5. **File Navigation**: When you need to find specific documents
6. **Current Task**: Resuming interrupted work
7. **Minimal Command**: Emergency context preservation

Save this file and use appropriate version based on available context window!