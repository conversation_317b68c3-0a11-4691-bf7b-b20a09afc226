"""
Instrumental Variables Implementation Examples for Yemen Market Integration Analysis

This module provides Python implementations for the instrumental variables
strategies described in the identification methodology.

Author: Yemen Market Integration Research Team
Date: 2024
"""

import pandas as pd
import numpy as np
import linearmodels as lm
from linearmodels.iv import IV2SLS
import statsmodels.api as sm
from scipy import stats
import warnings

warnings.filterwarnings('ignore')


class InstrumentalVariablesEstimator:
    """
    Comprehensive IV estimation for conflict endogeneity in panel data.
    
    Implements multiple instruments and diagnostic tests for identification.
    """
    
    def __init__(self):
        """Initialize IV estimator."""
        self.first_stage_results = {}
        self.second_stage_results = None
        self.diagnostic_tests = {}
        
    def prepare_rainfall_instrument(self, panel_data, rainfall_data, 
                                  entity_col='market_id', time_col='date'):
        """
        Prepare rainfall deviation instrument.
        
        Parameters
        ----------
        panel_data : DataFrame
            Main panel dataset
        rainfall_data : DataFrame
            CHIRPS rainfall data with coordinates
        entity_col : str
            Entity identifier
        time_col : str
            Time identifier
            
        Returns
        -------
        DataFrame
            Panel data with rainfall instrument
        """
        # Calculate long-term rainfall means (1981-2010 baseline)
        baseline_period = (rainfall_data[time_col] >= '1981-01-01') & \
                         (rainfall_data[time_col] <= '2010-12-31')
        
        rainfall_baseline = rainfall_data[baseline_period].groupby(
            [entity_col, rainfall_data[time_col].dt.month]
        )['rainfall_mm'].mean().reset_index()
        rainfall_baseline.columns = [entity_col, 'month', 'rainfall_longterm']
        
        # Merge with current data
        rainfall_current = rainfall_data.copy()
        rainfall_current['month'] = rainfall_current[time_col].dt.month
        
        rainfall_merged = rainfall_current.merge(
            rainfall_baseline, on=[entity_col, 'month']
        )
        
        # Calculate standardized deviations
        rainfall_std = rainfall_data.groupby(entity_col)['rainfall_mm'].std()
        rainfall_merged = rainfall_merged.merge(
            rainfall_std.rename('rainfall_std'), on=entity_col
        )
        
        rainfall_merged['rainfall_deviation'] = (
            (rainfall_merged['rainfall_mm'] - rainfall_merged['rainfall_longterm']) /
            rainfall_merged['rainfall_std']
        )
        
        # Merge with panel data
        result = panel_data.merge(
            rainfall_merged[[entity_col, time_col, 'rainfall_deviation']],
            on=[entity_col, time_col], how='left'
        )
        
        return result
    
    def prepare_spatial_lag_instrument(self, panel_data, spatial_weights,
                                     conflict_var='conflict_intensity',
                                     entity_col='market_id', time_col='date'):
        """
        Prepare spatial lag of conflict instrument.
        
        Parameters
        ----------
        panel_data : DataFrame
            Panel data
        spatial_weights : DataFrame
            Spatial weights matrix (entity x entity)
        conflict_var : str
            Conflict variable name
        entity_col : str
            Entity identifier
        time_col : str
            Time identifier
            
        Returns
        -------
        DataFrame
            Panel data with spatial lag instrument
        """
        # Sort data for consistent ordering
        data_sorted = panel_data.sort_values([time_col, entity_col])
        
        # Create lagged conflict variable
        data_sorted['conflict_lag1'] = data_sorted.groupby(entity_col)[conflict_var].shift(1)
        
        # Calculate spatial lag for each time period
        spatial_lags = []
        
        for date in data_sorted[time_col].unique():
            date_data = data_sorted[data_sorted[time_col] == date]
            
            if len(date_data) > 0:
                # Get conflict values for this period (lagged)
                conflict_values = date_data.set_index(entity_col)['conflict_lag1']
                
                # Calculate spatial lag
                entities = conflict_values.index
                W_subset = spatial_weights.loc[entities, entities]
                
                # Row-standardize weights
                W_subset = W_subset.div(W_subset.sum(axis=1), axis=0).fillna(0)
                
                # Calculate spatial lag
                spatial_lag = W_subset.dot(conflict_values.fillna(0))
                
                # Store results
                lag_df = pd.DataFrame({
                    entity_col: spatial_lag.index,
                    time_col: date,
                    'neighbor_conflict_lag': spatial_lag.values
                })
                spatial_lags.append(lag_df)
        
        # Combine all periods
        if spatial_lags:
            spatial_lag_df = pd.concat(spatial_lags, ignore_index=True)
            
            # Merge with panel data
            result = panel_data.merge(
                spatial_lag_df, on=[entity_col, time_col], how='left'
            )
        else:
            result = panel_data.copy()
            result['neighbor_conflict_lag'] = np.nan
            
        return result
    
    def prepare_oil_proximity_instrument(self, panel_data, oil_infrastructure,
                                       oil_prices, entity_col='market_id'):
        """
        Prepare oil price × proximity instrument.
        
        Parameters
        ----------
        panel_data : DataFrame
            Panel data
        oil_infrastructure : DataFrame
            Oil infrastructure coordinates
        oil_prices : DataFrame
            Global oil price data
        entity_col : str
            Entity identifier
            
        Returns
        -------
        DataFrame
            Panel data with oil proximity instrument
        """
        # Calculate distance to nearest oil infrastructure
        def haversine_distance(lat1, lon1, lat2, lon2):
            """Calculate great circle distance."""
            R = 6371  # Earth radius in km
            
            lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
            dlat = lat2 - lat1
            dlon = lon2 - lon1
            
            a = np.sin(dlat/2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon/2)**2
            c = 2 * np.arcsin(np.sqrt(a))
            
            return R * c
        
        # Get market coordinates
        market_coords = panel_data[[entity_col, 'latitude', 'longitude']].drop_duplicates()
        
        # Calculate minimum distance to oil infrastructure
        distances = []
        for _, market in market_coords.iterrows():
            market_distances = []
            for _, oil_site in oil_infrastructure.iterrows():
                dist = haversine_distance(
                    market['latitude'], market['longitude'],
                    oil_site['latitude'], oil_site['longitude']
                )
                market_distances.append(dist)
            
            min_distance = min(market_distances) if market_distances else np.inf
            distances.append({
                entity_col: market[entity_col],
                'distance_to_oil': min_distance
            })
        
        distance_df = pd.DataFrame(distances)
        
        # Merge distances and oil prices
        result = panel_data.merge(distance_df, on=entity_col)
        result = result.merge(oil_prices, on='date', how='left')
        
        # Create interaction instrument
        result['oil_proximity_instrument'] = (
            result['oil_price_brent'] / (1 + result['distance_to_oil'])
        )
        
        return result
    
    def estimate_iv_model(self, data, dependent_var, endogenous_vars, 
                         instruments, exogenous_vars=None, 
                         entity_effects=True, time_effects=True):
        """
        Estimate IV model with panel structure.
        
        Parameters
        ----------
        data : DataFrame
            Panel data with all variables
        dependent_var : str
            Dependent variable name
        endogenous_vars : list
            List of endogenous variables
        instruments : list
            List of instrumental variables
        exogenous_vars : list
            List of exogenous control variables
        entity_effects : bool
            Include entity fixed effects
        time_effects : bool
            Include time fixed effects
            
        Returns
        -------
        IV2SLS results object
        """
        # Prepare data
        if isinstance(data.index, pd.MultiIndex):
            estimation_data = data.reset_index()
        else:
            estimation_data = data.copy()
        
        # Build variable lists
        y = estimation_data[dependent_var]
        
        # Exogenous variables (controls + fixed effects)
        X_vars = exogenous_vars if exogenous_vars else []
        if entity_effects:
            X_vars.append('EntityEffects')
        if time_effects:
            X_vars.append('TimeEffects')
            
        # Endogenous variables
        endog = estimation_data[endogenous_vars]
        
        # Instruments
        Z = estimation_data[instruments]
        
        # Exogenous controls
        if X_vars:
            X = estimation_data[X_vars] if X_vars else None
        else:
            X = None
        
        # Estimate IV model
        iv_model = IV2SLS(
            dependent=y,
            exog=X,
            endog=endog,
            instruments=Z
        )
        
        self.second_stage_results = iv_model.fit(
            cov_type='clustered',
            cluster_entity=True,
            cluster_time=True
        )
        
        return self.second_stage_results
    
    def first_stage_diagnostics(self, data, endogenous_vars, instruments, 
                               exogenous_vars=None):
        """
        Run first stage diagnostics for instrument strength.
        
        Parameters
        ----------
        data : DataFrame
            Panel data
        endogenous_vars : list
            Endogenous variables
        instruments : list
            Instrumental variables
        exogenous_vars : list
            Exogenous controls
            
        Returns
        -------
        dict
            First stage diagnostic results
        """
        diagnostics = {}
        
        for endog_var in endogenous_vars:
            # First stage regression
            y_first = data[endog_var]
            
            # Build regressor matrix
            X_first_vars = instruments.copy()
            if exogenous_vars:
                X_first_vars.extend(exogenous_vars)
                
            X_first = data[X_first_vars]
            X_first = sm.add_constant(X_first)
            
            # Estimate first stage
            first_stage = sm.OLS(y_first, X_first).fit(
                cov_type='cluster',
                cov_kwds={'groups': data['market_id']}
            )
            
            # F-test for excluded instruments
            instrument_params = [f for f in first_stage.params.index 
                               if any(inst in f for inst in instruments)]
            
            f_stat = first_stage.f_test(instrument_params).fvalue
            f_pvalue = first_stage.f_test(instrument_params).pvalue
            
            # Partial R-squared
            ssr_restricted = sm.OLS(
                y_first, 
                data[exogenous_vars] if exogenous_vars else np.ones(len(y_first))
            ).fit().ssr
            
            partial_r2 = 1 - (first_stage.ssr / ssr_restricted)
            
            diagnostics[endog_var] = {
                'first_stage_results': first_stage,
                'f_statistic': f_stat,
                'f_pvalue': f_pvalue,
                'partial_r_squared': partial_r2,
                'weak_instrument_test': 'Pass' if f_stat > 10 else 'Fail'
            }
            
        self.first_stage_results = diagnostics
        return diagnostics
    
    def overidentification_test(self):
        """
        Sargan-Hansen overidentification test.
        
        Returns
        -------
        dict
            Overidentification test results
        """
        if self.second_stage_results is None:
            raise ValueError("Must estimate second stage first")
            
        # Get test statistic from linearmodels
        try:
            sargan_stat = self.second_stage_results.sargan.stat
            sargan_pvalue = self.second_stage_results.sargan.pval
            
            return {
                'sargan_statistic': sargan_stat,
                'sargan_pvalue': sargan_pvalue,
                'overidentification_test': 'Pass' if sargan_pvalue > 0.05 else 'Fail'
            }
        except AttributeError:
            return {'error': 'Overidentification test not available'}
    
    def comprehensive_iv_analysis(self, data, dependent_var='log_price_usd',
                                endogenous_vars=['conflict_intensity'],
                                exogenous_vars=None):
        """
        Run comprehensive IV analysis with all instruments.
        
        Parameters
        ----------
        data : DataFrame
            Panel data with all instruments prepared
        dependent_var : str
            Dependent variable
        endogenous_vars : list
            Endogenous variables
        exogenous_vars : list
            Exogenous controls
            
        Returns
        -------
        dict
            Comprehensive IV results
        """
        # Define instruments
        instruments = [
            'rainfall_deviation',
            'neighbor_conflict_lag', 
            'oil_proximity_instrument'
        ]
        
        # Check instrument availability
        available_instruments = [inst for inst in instruments if inst in data.columns]
        
        if len(available_instruments) == 0:
            raise ValueError("No instruments available in data")
        
        # First stage diagnostics
        first_stage_diag = self.first_stage_diagnostics(
            data, endogenous_vars, available_instruments, exogenous_vars
        )
        
        # Second stage estimation
        iv_results = self.estimate_iv_model(
            data, dependent_var, endogenous_vars, 
            available_instruments, exogenous_vars
        )
        
        # Overidentification test (if overidentified)
        if len(available_instruments) > len(endogenous_vars):
            overid_test = self.overidentification_test()
        else:
            overid_test = {'note': 'Model is exactly identified'}
        
        return {
            'first_stage_diagnostics': first_stage_diag,
            'second_stage_results': iv_results,
            'overidentification_test': overid_test,
            'instruments_used': available_instruments,
            'sample_size': len(data)
        }


def compare_ols_iv_results(ols_results, iv_results, parameter='conflict_intensity'):
    """
    Compare OLS and IV estimates for endogeneity assessment.
    
    Parameters
    ----------
    ols_results : regression results
        OLS estimation results
    iv_results : regression results
        IV estimation results
    parameter : str
        Parameter to compare
        
    Returns
    -------
    dict
        Comparison results
    """
    ols_coef = ols_results.params[parameter]
    ols_se = ols_results.std_errors[parameter]
    
    iv_coef = iv_results.params[parameter]
    iv_se = iv_results.std_errors[parameter]
    
    # Hausman test statistic
    diff = iv_coef - ols_coef
    var_diff = iv_se**2 - ols_se**2
    
    if var_diff > 0:
        hausman_stat = diff**2 / var_diff
        hausman_pvalue = 1 - stats.chi2.cdf(hausman_stat, df=1)
    else:
        hausman_stat = np.nan
        hausman_pvalue = np.nan
    
    return {
        'ols_estimate': ols_coef,
        'ols_std_error': ols_se,
        'iv_estimate': iv_coef,
        'iv_std_error': iv_se,
        'difference': diff,
        'relative_change': diff / ols_coef if ols_coef != 0 else np.inf,
        'hausman_statistic': hausman_stat,
        'hausman_pvalue': hausman_pvalue,
        'endogeneity_test': 'Reject exogeneity' if hausman_pvalue < 0.05 else 'Cannot reject exogeneity'
    }
