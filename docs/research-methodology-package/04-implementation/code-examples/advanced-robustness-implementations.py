"""
Advanced Robustness Check Implementations for Yemen Market Integration Analysis

This module provides Python implementations for the advanced robustness checks
described in the methodology documentation.

Author: Yemen Market Integration Research Team
Date: 2024
"""

import pandas as pd
import numpy as np
import linearmodels as lm
from sklearn.decomposition import PCA
import statsmodels.api as sm
import statsmodels.formula.api as smf
from scipy import stats
import warnings

# Suppress convergence warnings for demonstration
warnings.filterwarnings('ignore')


class InteractiveFixedEffectsModel:
    """
    Interactive Fixed Effects (IFE) model implementation using PCA proxy method.
    
    Based on Bai (2009) Panel data models with interactive fixed effects.
    """
    
    def __init__(self, n_factors=3):
        """
        Initialize IFE model.
        
        Parameters
        ----------
        n_factors : int
            Number of common factors to extract
        """
        self.n_factors = n_factors
        self.factors = None
        self.loadings = None
        self.baseline_model = None
        self.ife_model = None
        
    def fit(self, panel_data, formula, entity_col='market_id', time_col='date'):
        """
        Estimate IFE model using PCA proxy method.
        
        Parameters
        ----------
        panel_data : DataFrame
            Panel data with MultiIndex [entity, time] or separate columns
        formula : str
            Model formula (without factor terms)
        entity_col : str
            Entity identifier column
        time_col : str
            Time identifier column
        """
        # Step 1: Estimate baseline fixed effects model
        if not isinstance(panel_data.index, pd.MultiIndex):
            panel_data = panel_data.set_index([entity_col, time_col])
            
        self.baseline_model = lm.PanelOLS.from_formula(
            formula, data=panel_data
        ).fit(cov_type='clustered', cluster_entity=True, cluster_time=True)
        
        # Step 2: Extract residuals and reshape for PCA
        residuals = self.baseline_model.resids
        residuals_wide = residuals.unstack(level=0)  # Time x Entity
        residuals_wide = residuals_wide.fillna(residuals_wide.mean())
        
        # Step 3: Apply PCA to extract common factors
        pca = PCA(n_components=self.n_factors)
        self.factors = pca.fit_transform(residuals_wide.values)
        self.loadings = pca.components_.T
        
        # Create factor DataFrame
        factor_df = pd.DataFrame(
            self.factors,
            index=residuals_wide.index,
            columns=[f'factor_{i+1}' for i in range(self.n_factors)]
        )
        
        # Step 4: Merge factors back to panel data
        panel_with_factors = panel_data.reset_index().merge(
            factor_df.reset_index(), on=time_col
        ).set_index([entity_col, time_col])
        
        # Step 5: Re-estimate with factors
        factor_terms = ' + '.join(factor_df.columns)
        ife_formula = f"{formula} + {factor_terms}"
        
        self.ife_model = lm.PanelOLS.from_formula(
            ife_formula, data=panel_with_factors
        ).fit(cov_type='clustered', cluster_entity=True, cluster_time=True)
        
        return self
    
    def compare_results(self, parameter='Conflict_it'):
        """
        Compare baseline and IFE model results for key parameter.
        
        Parameters
        ----------
        parameter : str
            Parameter name to compare
            
        Returns
        -------
        dict
            Comparison results
        """
        baseline_coef = self.baseline_model.params[parameter]
        baseline_se = self.baseline_model.std_errors[parameter]
        
        ife_coef = self.ife_model.params[parameter]
        ife_se = self.ife_model.std_errors[parameter]
        
        return {
            'baseline': {
                'coefficient': baseline_coef,
                'std_error': baseline_se,
                'p_value': self.baseline_model.pvalues[parameter]
            },
            'ife': {
                'coefficient': ife_coef,
                'std_error': ife_se,
                'p_value': self.ife_model.pvalues[parameter]
            },
            'difference': ife_coef - baseline_coef,
            'relative_change': (ife_coef - baseline_coef) / baseline_coef,
            'explained_variance': np.sum(self.pca.explained_variance_ratio_)
        }


class SpatialEconometricModel:
    """
    Spatial econometric model implementation using PySAL.
    
    Implements Spatial Autoregressive (SAR) and Spatial Error Model (SEM).
    """
    
    def __init__(self, spatial_weights):
        """
        Initialize spatial model.
        
        Parameters
        ----------
        spatial_weights : array-like
            Spatial weights matrix (N x N)
        """
        self.w = spatial_weights
        self.sar_model = None
        self.sem_model = None
        
    def fit_sar(self, y, X, entity_effects=True):
        """
        Estimate Spatial Autoregressive (SAR) model.
        
        Parameters
        ----------
        y : array-like
            Dependent variable
        X : array-like
            Independent variables
        entity_effects : bool
            Include entity fixed effects
        """
        try:
            import pysal
            from pysal.model import spreg
            
            if entity_effects:
                # Add entity dummies to X
                pass  # Implementation depends on data structure
                
            self.sar_model = spreg.ML_Lag(
                y, X, w=self.w, name_y='price', 
                name_x=['conflict', 'controls']
            )
            
        except ImportError:
            raise ImportError("PySAL required for spatial models. Install with: pip install pysal")
            
        return self
    
    def fit_sem(self, y, X, entity_effects=True):
        """
        Estimate Spatial Error Model (SEM).
        
        Parameters
        ----------
        y : array-like
            Dependent variable
        X : array-like
            Independent variables
        entity_effects : bool
            Include entity fixed effects
        """
        try:
            import pysal
            from pysal.model import spreg
            
            self.sem_model = spreg.ML_Error(
                y, X, w=self.w, name_y='price',
                name_x=['conflict', 'controls']
            )
            
        except ImportError:
            raise ImportError("PySAL required for spatial models. Install with: pip install pysal")
            
        return self
    
    def decompose_effects(self, model_type='sar'):
        """
        Decompose direct and indirect (spillover) effects.
        
        Parameters
        ----------
        model_type : str
            'sar' or 'sem'
            
        Returns
        -------
        dict
            Direct and indirect effects
        """
        if model_type == 'sar' and self.sar_model is not None:
            # Calculate direct and indirect effects for SAR model
            rho = self.sar_model.rho
            beta = self.sar_model.betas
            
            # Direct effects: diagonal elements of (I - ρW)^(-1)
            I = np.eye(self.w.shape[0])
            S = np.linalg.inv(I - rho * self.w)
            direct_effects = np.mean(np.diag(S)) * beta
            
            # Indirect effects: off-diagonal elements
            indirect_effects = (np.sum(S) - np.trace(S)) / self.w.shape[0] * beta
            
            return {
                'direct_effects': direct_effects,
                'indirect_effects': indirect_effects,
                'total_effects': direct_effects + indirect_effects
            }
        else:
            return {'error': 'Model not fitted or type not supported'}


class EventStudyModel:
    """
    Event study implementation using dynamic difference-in-differences.
    
    Based on Cunningham (2021) Causal Inference: The Mixtape.
    """
    
    def __init__(self, event_date, treated_entities, pre_periods=6, post_periods=12):
        """
        Initialize event study.
        
        Parameters
        ----------
        event_date : str or datetime
            Date of the event
        treated_entities : list
            List of treated entity identifiers
        pre_periods : int
            Number of pre-event periods
        post_periods : int
            Number of post-event periods
        """
        self.event_date = pd.to_datetime(event_date)
        self.treated_entities = treated_entities
        self.pre_periods = pre_periods
        self.post_periods = post_periods
        self.model = None
        
    def prepare_data(self, panel_data, entity_col='market_id', time_col='date'):
        """
        Prepare data for event study estimation.
        
        Parameters
        ----------
        panel_data : DataFrame
            Panel data
        entity_col : str
            Entity identifier column
        time_col : str
            Time identifier column
            
        Returns
        -------
        DataFrame
            Data with event study variables
        """
        data = panel_data.copy()
        
        # Create treatment indicator
        data['treated'] = data[entity_col].isin(self.treated_entities).astype(int)
        
        # Create relative time variable
        data['event_time'] = (
            (pd.to_datetime(data[time_col]) - self.event_date) / 
            pd.Timedelta(days=30)  # Convert to months
        ).round().astype(int)
        
        # Clip to analysis window
        data = data[
            (data['event_time'] >= -self.pre_periods) & 
            (data['event_time'] <= self.post_periods)
        ]
        
        # Create relative time dummies (omit -1 as reference)
        for t in range(-self.pre_periods, self.post_periods + 1):
            if t != -1:  # Omit reference period
                data[f'rel_time_{t}'] = (data['event_time'] == t).astype(int)
                data[f'treated_x_rel_time_{t}'] = (
                    data['treated'] * data[f'rel_time_{t}']
                )
        
        return data
    
    def fit(self, data, outcome_var='log_price', controls=None):
        """
        Estimate event study model.
        
        Parameters
        ----------
        data : DataFrame
            Prepared event study data
        outcome_var : str
            Outcome variable name
        controls : list
            List of control variables
        """
        # Build formula
        interaction_terms = [
            f'treated_x_rel_time_{t}' 
            for t in range(-self.pre_periods, self.post_periods + 1)
            if t != -1
        ]
        
        formula_parts = [outcome_var, '~'] + interaction_terms
        
        if controls:
            formula_parts.extend(['+'] + controls)
            
        # Add fixed effects
        formula_parts.extend(['+', 'EntityEffects', '+', 'TimeEffects'])
        
        formula = ' '.join(formula_parts)
        
        # Estimate model
        self.model = lm.PanelOLS.from_formula(formula, data=data).fit(
            cov_type='clustered', cluster_entity=True, cluster_time=True
        )
        
        return self
    
    def plot_event_study(self):
        """
        Plot event study coefficients with confidence intervals.
        """
        import matplotlib.pyplot as plt
        
        # Extract coefficients and standard errors
        coeffs = []
        ses = []
        periods = []
        
        for t in range(-self.pre_periods, self.post_periods + 1):
            if t == -1:
                coeffs.append(0)  # Reference period
                ses.append(0)
            else:
                param_name = f'treated_x_rel_time_{t}'
                coeffs.append(self.model.params[param_name])
                ses.append(self.model.std_errors[param_name])
            periods.append(t)
        
        # Create plot
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Plot coefficients with confidence intervals
        ax.errorbar(periods, coeffs, yerr=1.96*np.array(ses), 
                   fmt='o-', capsize=5, capthick=2)
        
        # Add reference lines
        ax.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        ax.axvline(x=0, color='red', linestyle='--', alpha=0.5)
        
        ax.set_xlabel('Periods Relative to Event')
        ax.set_ylabel('Treatment Effect')
        ax.set_title('Event Study: Dynamic Treatment Effects')
        ax.grid(True, alpha=0.3)
        
        return fig


def estimate_random_coefficients_model(panel_data, formula, random_vars, 
                                     group_var='market_id'):
    """
    Estimate random coefficients model using mixed effects.
    
    Parameters
    ----------
    panel_data : DataFrame
        Panel data
    formula : str
        Fixed effects formula
    random_vars : list
        Variables with random coefficients
    group_var : str
        Grouping variable for random effects
        
    Returns
    -------
    statsmodels MixedLMResults
        Fitted mixed effects model
    """
    # Prepare random effects formula
    random_formula = f"~ 1 + {' + '.join(random_vars)}"
    
    # Convert to long format if needed
    if isinstance(panel_data.index, pd.MultiIndex):
        data_long = panel_data.reset_index()
    else:
        data_long = panel_data.copy()
    
    # Estimate mixed effects model
    model = smf.mixedlm(
        formula, data_long, 
        groups=data_long[group_var],
        re_formula=random_formula
    )
    
    results = model.fit()
    
    return results


def run_comprehensive_robustness_checks(panel_data, base_formula, 
                                      conflict_var='Conflict_it'):
    """
    Run all advanced robustness checks and compile results.
    
    Parameters
    ----------
    panel_data : DataFrame
        Panel data for analysis
    base_formula : str
        Base model formula
    conflict_var : str
        Conflict variable name
        
    Returns
    -------
    dict
        Comprehensive robustness results
    """
    results = {}
    
    # 1. Interactive Fixed Effects
    try:
        ife_model = InteractiveFixedEffectsModel(n_factors=3)
        ife_model.fit(panel_data, base_formula)
        results['ife'] = ife_model.compare_results(conflict_var)
    except Exception as e:
        results['ife'] = {'error': str(e)}
    
    # 2. Random Coefficients
    try:
        rc_model = estimate_random_coefficients_model(
            panel_data, base_formula, [conflict_var]
        )
        results['random_coefficients'] = {
            'fixed_effect': rc_model.fe_params[conflict_var],
            'random_variance': rc_model.cov_re.iloc[1, 1],
            'significance': rc_model.pvalues[conflict_var]
        }
    except Exception as e:
        results['random_coefficients'] = {'error': str(e)}
    
    # 3. Event Study (if event data available)
    # This would require specific event dates and treated units
    # results['event_study'] = {...}
    
    return results
