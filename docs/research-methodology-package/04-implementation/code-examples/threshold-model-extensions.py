"""
Advanced Threshold Model Extensions for Yemen Market Integration Analysis

This module provides Python implementations for advanced threshold models
including STAR-VECM, multiple thresholds, and panel threshold regression.

Author: Yemen Market Integration Research Team
Date: 2024
"""

import pandas as pd
import numpy as np
import statsmodels.api as sm
from scipy.optimize import minimize
from scipy import stats
import warnings

warnings.filterwarnings('ignore')


class SmoothTransitionThresholdVECM:
    """
    Smooth Transition Threshold Vector Error Correction Model (STAR-VECM).
    
    Implements logistic smooth transition between regimes instead of
    sharp threshold switches.
    """
    
    def __init__(self, transition_speed_bounds=(0.1, 100)):
        """
        Initialize STAR-VECM model.
        
        Parameters
        ----------
        transition_speed_bounds : tuple
            Bounds for transition speed parameter gamma
        """
        self.gamma = None  # Transition speed
        self.c = None      # Location parameter
        self.alpha_low = None   # Adjustment speeds in low regime
        self.alpha_high = None  # Adjustment speeds in high regime
        self.transition_bounds = transition_speed_bounds
        self.fitted = False
        
    def transition_function(self, q, gamma, c):
        """
        Logistic transition function.
        
        Parameters
        ----------
        q : array-like
            Transition variable values
        gamma : float
            Transition speed parameter
        c : float
            Location parameter
            
        Returns
        -------
        array-like
            Transition function values (0 to 1)
        """
        return 1 / (1 + np.exp(-gamma * (q - c)))
    
    def fit(self, data, endog_vars, transition_var='conflict_intensity',
            max_iter=1000):
        """
        Estimate STAR-VECM using nonlinear optimization.
        
        Parameters
        ----------
        data : DataFrame
            Panel data
        endog_vars : list
            Endogenous variables for VECM
        transition_var : str
            Transition variable
        max_iter : int
            Maximum optimization iterations
            
        Returns
        -------
        self
            Fitted model
        """
        # Prepare data
        y = data[endog_vars].values
        q = data[transition_var].values
        
        # Remove missing values
        valid_idx = ~(np.isnan(y).any(axis=1) | np.isnan(q))
        y_clean = y[valid_idx]
        q_clean = q[valid_idx]
        
        if len(y_clean) < 50:
            raise ValueError("Insufficient data for STAR-VECM estimation")
        
        # First differences for VECM
        dy = np.diff(y_clean, axis=0)
        q_lag = q_clean[:-1]
        
        # Estimate linear VECM for initial values
        try:
            from statsmodels.tsa.vector_ar.vecm import VECM
            linear_vecm = VECM(y_clean, k_ar_diff=1, coint_rank=1).fit()
            initial_alpha = linear_vecm.alpha.flatten()
        except:
            # Fallback to simple initial values
            initial_alpha = np.random.normal(0, 0.1, len(endog_vars))
        
        # Define objective function
        def objective(params):
            gamma, c = params[:2]
            alpha_low = params[2:2+len(endog_vars)]
            alpha_high = params[2+len(endog_vars):2+2*len(endog_vars)]
            
            try:
                # Transition function
                G = self.transition_function(q_lag, gamma, c)
                
                # Smooth transition adjustment speeds
                alpha_smooth = np.outer((1 - G), alpha_low) + np.outer(G, alpha_high)
                
                # Calculate residuals (simplified VECM)
                residuals = []
                for i in range(len(endog_vars)):
                    # Simplified: dy_i = alpha_i(smooth) * y_{i,t-1} + error
                    predicted = alpha_smooth[:, i] * y_clean[:-1, i]
                    actual = dy[:, i]
                    residuals.extend(actual - predicted)
                
                return np.sum(np.array(residuals)**2)
                
            except:
                return np.inf
        
        # Initial parameter values
        initial_params = [
            10.0,  # gamma (transition speed)
            np.median(q_clean),  # c (location)
            *initial_alpha,  # alpha_low
            *initial_alpha   # alpha_high
        ]
        
        # Parameter bounds
        bounds = [
            self.transition_bounds,  # gamma
            (q_clean.min(), q_clean.max()),  # c
        ] + [(None, None)] * (2 * len(endog_vars))  # alphas
        
        # Optimize
        try:
            result = minimize(
                objective,
                initial_params,
                method='L-BFGS-B',
                bounds=bounds,
                options={'maxiter': max_iter}
            )
            
            if result.success:
                self.gamma = result.x[0]
                self.c = result.x[1]
                self.alpha_low = result.x[2:2+len(endog_vars)]
                self.alpha_high = result.x[2+len(endog_vars):2+2*len(endog_vars)]
                self.fitted = True
                
                # Store additional results
                self.optimization_result = result
                self.endog_vars = endog_vars
                self.transition_var = transition_var
                
            else:
                raise ValueError(f"Optimization failed: {result.message}")
                
        except Exception as e:
            raise ValueError(f"STAR-VECM estimation failed: {str(e)}")
        
        return self
    
    def predict_transition_function(self, q_values):
        """
        Predict transition function values.
        
        Parameters
        ----------
        q_values : array-like
            Transition variable values
            
        Returns
        -------
        array-like
            Transition function values
        """
        if not self.fitted:
            raise ValueError("Model must be fitted first")
            
        return self.transition_function(q_values, self.gamma, self.c)
    
    def plot_transition_function(self, q_range=None):
        """
        Plot the estimated transition function.
        
        Parameters
        ----------
        q_range : array-like, optional
            Range of transition variable values to plot
        """
        if not self.fitted:
            raise ValueError("Model must be fitted first")
        
        import matplotlib.pyplot as plt
        
        if q_range is None:
            q_range = np.linspace(0, 100, 1000)
        
        G = self.predict_transition_function(q_range)
        
        plt.figure(figsize=(10, 6))
        plt.plot(q_range, G, linewidth=2, label='Transition Function')
        plt.axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='Midpoint')
        plt.axvline(x=self.c, color='green', linestyle='--', alpha=0.5, 
                   label=f'Location (c={self.c:.2f})')
        
        plt.xlabel(f'{self.transition_var}')
        plt.ylabel('Transition Function G(q)')
        plt.title(f'STAR-VECM Transition Function (γ={self.gamma:.2f})')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()
    
    def regime_probabilities(self, data):
        """
        Calculate regime probabilities for each observation.
        
        Parameters
        ----------
        data : DataFrame
            Data with transition variable
            
        Returns
        -------
        DataFrame
            Data with regime probabilities
        """
        if not self.fitted:
            raise ValueError("Model must be fitted first")
        
        result = data.copy()
        q_values = data[self.transition_var].values
        
        G = self.predict_transition_function(q_values)
        result['prob_low_regime'] = 1 - G
        result['prob_high_regime'] = G
        result['dominant_regime'] = np.where(G > 0.5, 'high', 'low')
        
        return result


class MultipleThresholdModel:
    """
    Multiple threshold model using sequential estimation.
    
    Implements Bai & Perron (2003) methodology for multiple structural breaks.
    """
    
    def __init__(self, max_thresholds=3, min_regime_size=30):
        """
        Initialize multiple threshold model.
        
        Parameters
        ----------
        max_thresholds : int
            Maximum number of thresholds to test
        min_regime_size : int
            Minimum observations per regime
        """
        self.max_thresholds = max_thresholds
        self.min_regime_size = min_regime_size
        self.thresholds = []
        self.regime_models = {}
        self.information_criteria = {}
        self.fitted = False
        
    def estimate_single_threshold(self, y, x, threshold_var, trim_pct=0.15):
        """
        Estimate single threshold using grid search.
        
        Parameters
        ----------
        y : array-like
            Dependent variable
        x : array-like
            Independent variables
        threshold_var : array-like
            Threshold variable
        trim_pct : float
            Trimming percentage for threshold search
            
        Returns
        -------
        dict
            Single threshold estimation results
        """
        # Sort threshold variable for grid search
        sorted_threshold = np.sort(threshold_var)
        n = len(sorted_threshold)
        trim_n = int(n * trim_pct)
        
        search_range = sorted_threshold[trim_n:-trim_n]
        ssr_values = []
        
        for tau in search_range:
            # Split sample
            regime1_idx = threshold_var <= tau
            regime2_idx = threshold_var > tau
            
            # Check minimum observations
            if np.sum(regime1_idx) < self.min_regime_size or \
               np.sum(regime2_idx) < self.min_regime_size:
                ssr_values.append(np.inf)
                continue
            
            # Estimate models for each regime
            ssr_total = 0
            
            # Regime 1
            try:
                X1 = sm.add_constant(x[regime1_idx])
                model1 = sm.OLS(y[regime1_idx], X1).fit()
                ssr_total += model1.ssr
            except:
                ssr_values.append(np.inf)
                continue
            
            # Regime 2
            try:
                X2 = sm.add_constant(x[regime2_idx])
                model2 = sm.OLS(y[regime2_idx], X2).fit()
                ssr_total += model2.ssr
            except:
                ssr_values.append(np.inf)
                continue
            
            ssr_values.append(ssr_total)
        
        # Find optimal threshold
        if not np.all(np.isinf(ssr_values)):
            optimal_idx = np.argmin(ssr_values)
            optimal_threshold = search_range[optimal_idx]
            optimal_ssr = ssr_values[optimal_idx]
            
            return {
                'threshold': optimal_threshold,
                'ssr': optimal_ssr,
                'search_range': search_range,
                'ssr_values': ssr_values
            }
        else:
            return None
    
    def sequential_threshold_estimation(self, y, x, threshold_var):
        """
        Sequential estimation of multiple thresholds.
        
        Parameters
        ----------
        y : array-like
            Dependent variable
        x : array-like
            Independent variables
        threshold_var : array-like
            Threshold variable
            
        Returns
        -------
        dict
            Sequential estimation results
        """
        results = {}
        current_thresholds = []
        
        # Start with no threshold (linear model)
        X_linear = sm.add_constant(x)
        linear_model = sm.OLS(y, X_linear).fit()
        
        results[0] = {
            'thresholds': [],
            'ssr': linear_model.ssr,
            'aic': linear_model.aic,
            'bic': linear_model.bic,
            'n_params': len(linear_model.params)
        }
        
        # Sequential threshold testing
        for n_thresh in range(1, self.max_thresholds + 1):
            best_ssr = np.inf
            best_thresholds = None
            
            if n_thresh == 1:
                # First threshold
                single_result = self.estimate_single_threshold(y, x, threshold_var)
                if single_result:
                    best_thresholds = [single_result['threshold']]
                    best_ssr = single_result['ssr']
            else:
                # Additional thresholds
                # For each existing regime, try to split it
                prev_thresholds = results[n_thresh - 1]['thresholds']
                
                # Define current regimes
                regimes = self._define_regimes(threshold_var, prev_thresholds)
                
                for regime_idx, regime_mask in enumerate(regimes):
                    if np.sum(regime_mask) < 2 * self.min_regime_size:
                        continue
                    
                    # Try to split this regime
                    regime_y = y[regime_mask]
                    regime_x = x[regime_mask]
                    regime_threshold = threshold_var[regime_mask]
                    
                    split_result = self.estimate_single_threshold(
                        regime_y, regime_x, regime_threshold
                    )
                    
                    if split_result:
                        # Test new threshold configuration
                        test_thresholds = sorted(prev_thresholds + [split_result['threshold']])
                        test_ssr = self._calculate_total_ssr(y, x, threshold_var, test_thresholds)
                        
                        if test_ssr < best_ssr:
                            best_ssr = test_ssr
                            best_thresholds = test_thresholds
            
            # Store results if improvement found
            if best_thresholds and best_ssr < results[n_thresh - 1]['ssr']:
                n_params = (n_thresh + 1) * (x.shape[1] + 1)  # Approximate
                n_obs = len(y)
                
                results[n_thresh] = {
                    'thresholds': best_thresholds,
                    'ssr': best_ssr,
                    'aic': n_obs * np.log(best_ssr / n_obs) + 2 * n_params,
                    'bic': n_obs * np.log(best_ssr / n_obs) + np.log(n_obs) * n_params,
                    'n_params': n_params
                }
            else:
                break  # No improvement, stop adding thresholds
        
        return results
    
    def _define_regimes(self, threshold_var, thresholds):
        """Define regime indicators based on threshold values."""
        if not thresholds:
            return [np.ones(len(threshold_var), dtype=bool)]
        
        sorted_thresholds = sorted(thresholds)
        regimes = []
        
        # First regime: q <= tau_1
        regimes.append(threshold_var <= sorted_thresholds[0])
        
        # Middle regimes: tau_{i-1} < q <= tau_i
        for i in range(1, len(sorted_thresholds)):
            regime_mask = (threshold_var > sorted_thresholds[i-1]) & \
                         (threshold_var <= sorted_thresholds[i])
            regimes.append(regime_mask)
        
        # Last regime: q > tau_k
        regimes.append(threshold_var > sorted_thresholds[-1])
        
        return regimes
    
    def _calculate_total_ssr(self, y, x, threshold_var, thresholds):
        """Calculate total SSR for given threshold configuration."""
        regimes = self._define_regimes(threshold_var, thresholds)
        total_ssr = 0
        
        for regime_mask in regimes:
            if np.sum(regime_mask) < 5:  # Minimum for regression
                return np.inf
            
            try:
                X_regime = sm.add_constant(x[regime_mask])
                model = sm.OLS(y[regime_mask], X_regime).fit()
                total_ssr += model.ssr
            except:
                return np.inf
        
        return total_ssr
    
    def fit(self, data, dependent_var, independent_vars, threshold_var):
        """
        Fit multiple threshold model.
        
        Parameters
        ----------
        data : DataFrame
            Panel data
        dependent_var : str
            Dependent variable name
        independent_vars : list
            Independent variable names
        threshold_var : str
            Threshold variable name
            
        Returns
        -------
        self
            Fitted model
        """
        # Prepare data
        y = data[dependent_var].values
        x = data[independent_vars].values
        threshold_values = data[threshold_var].values
        
        # Remove missing values
        valid_idx = ~(np.isnan(y) | np.isnan(x).any(axis=1) | np.isnan(threshold_values))
        y_clean = y[valid_idx]
        x_clean = x[valid_idx]
        threshold_clean = threshold_values[valid_idx]
        
        # Sequential estimation
        self.estimation_results = self.sequential_threshold_estimation(
            y_clean, x_clean, threshold_clean
        )
        
        # Select optimal number of thresholds using BIC
        if self.estimation_results:
            bics = {k: v['bic'] for k, v in self.estimation_results.items()}
            optimal_n = min(bics, key=bics.get)
            
            self.optimal_n_thresholds = optimal_n
            self.thresholds = self.estimation_results[optimal_n]['thresholds']
            self.information_criteria = bics
            self.fitted = True
        
        return self
    
    def summary(self):
        """Print model summary."""
        if not self.fitted:
            print("Model not fitted yet.")
            return
        
        print("Multiple Threshold Model Summary")
        print("=" * 40)
        print(f"Optimal number of thresholds: {self.optimal_n_thresholds}")
        
        if self.thresholds:
            print(f"Threshold values: {self.thresholds}")
        
        print("\nInformation Criteria:")
        for n_thresh, bic in self.information_criteria.items():
            marker = " *" if n_thresh == self.optimal_n_thresholds else ""
            print(f"  {n_thresh} thresholds: BIC = {bic:.2f}{marker}")


def demonstrate_advanced_threshold_models(panel_data):
    """
    Demonstrate advanced threshold model implementations.
    
    Parameters
    ----------
    panel_data : DataFrame
        Panel data for analysis
        
    Returns
    -------
    dict
        Demonstration results
    """
    results = {}
    
    # 1. STAR-VECM demonstration
    try:
        star_model = SmoothTransitionThresholdVECM()
        star_model.fit(
            panel_data, 
            endog_vars=['log_price_local', 'log_price_global'],
            transition_var='conflict_intensity'
        )
        
        results['star_vecm'] = {
            'gamma': star_model.gamma,
            'location': star_model.c,
            'alpha_low': star_model.alpha_low,
            'alpha_high': star_model.alpha_high,
            'fitted': star_model.fitted
        }
        
    except Exception as e:
        results['star_vecm'] = {'error': str(e)}
    
    # 2. Multiple threshold demonstration
    try:
        multi_threshold = MultipleThresholdModel(max_thresholds=3)
        multi_threshold.fit(
            panel_data,
            dependent_var='log_price_usd',
            independent_vars=['conflict_intensity', 'global_price'],
            threshold_var='conflict_intensity'
        )
        
        results['multiple_threshold'] = {
            'optimal_n_thresholds': multi_threshold.optimal_n_thresholds,
            'thresholds': multi_threshold.thresholds,
            'information_criteria': multi_threshold.information_criteria
        }
        
    except Exception as e:
        results['multiple_threshold'] = {'error': str(e)}
    
    return results
