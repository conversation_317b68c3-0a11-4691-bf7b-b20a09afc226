"""
Missing Data Implementation Examples for Yemen Market Integration Analysis

This module provides Python implementations for advanced missing data methods
specifically designed for conflict settings where missingness is often non-random.

Author: Yemen Market Integration Research Team
Date: 2024
"""

import pandas as pd
import numpy as np
import statsmodels.api as sm
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import IterativeImputer
from sklearn.ensemble import RandomForestRegressor
import warnings

warnings.filterwarnings('ignore')


class ConflictMissingDataHandler:
    """
    Comprehensive missing data handler for conflict economics analysis.
    
    Implements MICE, selection models, and pattern mixture approaches
    specifically designed for conflict-related missingness patterns.
    """
    
    def __init__(self):
        """Initialize missing data handler."""
        self.mice_imputer = None
        self.selection_model = None
        self.pattern_analysis = {}
        self.sensitivity_results = {}
        
    def analyze_missing_patterns(self, data, conflict_var='conflict_intensity'):
        """
        Analyze missing data patterns in conflict context.
        
        Parameters
        ----------
        data : DataFrame
            Panel data with missing values
        conflict_var : str
            Conflict intensity variable
            
        Returns
        -------
        dict
            Missing pattern analysis results
        """
        # Calculate missingness indicators
        missing_indicators = data.isnull()
        
        # Overall missingness rates
        missing_rates = missing_indicators.mean()
        
        # Missingness by conflict level
        if conflict_var in data.columns:
            # Create conflict intensity bins
            data['conflict_bin'] = pd.cut(
                data[conflict_var], 
                bins=[0, 0.1, 1, 5, np.inf], 
                labels=['None', 'Low', 'Medium', 'High']
            )
            
            missing_by_conflict = {}
            for var in data.select_dtypes(include=[np.number]).columns:
                if var != conflict_var:
                    missing_by_conflict[var] = data.groupby('conflict_bin')[var].apply(
                        lambda x: x.isnull().mean()
                    )
        else:
            missing_by_conflict = {}
        
        # Temporal patterns
        if 'date' in data.columns:
            data['year_month'] = pd.to_datetime(data['date']).dt.to_period('M')
            missing_temporal = {}
            for var in data.select_dtypes(include=[np.number]).columns:
                missing_temporal[var] = data.groupby('year_month')[var].apply(
                    lambda x: x.isnull().mean()
                )
        else:
            missing_temporal = {}
        
        # Missing patterns (combinations)
        pattern_matrix = missing_indicators.astype(int)
        patterns = pattern_matrix.value_counts()
        
        self.pattern_analysis = {
            'overall_rates': missing_rates,
            'by_conflict': missing_by_conflict,
            'temporal': missing_temporal,
            'patterns': patterns,
            'total_patterns': len(patterns)
        }
        
        return self.pattern_analysis
    
    def test_missing_mechanism(self, data, target_var, predictors=None):
        """
        Test missing data mechanism (MCAR vs MAR vs MNAR).
        
        Parameters
        ----------
        data : DataFrame
            Data with missing values
        target_var : str
            Variable to test missingness for
        predictors : list
            Predictor variables for missingness
            
        Returns
        -------
        dict
            Test results for missing mechanism
        """
        if predictors is None:
            predictors = ['conflict_intensity', 'market_accessibility', 
                         'distance_to_capital', 'population_density']
        
        # Available predictors
        available_predictors = [p for p in predictors if p in data.columns]
        
        if not available_predictors:
            return {'error': 'No predictor variables available'}
        
        # Create missingness indicator
        missing_indicator = data[target_var].isnull().astype(int)
        
        # Logistic regression for missingness
        X = data[available_predictors].fillna(data[available_predictors].mean())
        X = sm.add_constant(X)
        
        try:
            logit_model = sm.Logit(missing_indicator, X).fit(disp=0)
            
            # Test joint significance
            test_statistic = logit_model.llr
            p_value = logit_model.llr_pvalue
            
            # Individual predictor significance
            predictor_pvalues = logit_model.pvalues[available_predictors]
            
            return {
                'test_statistic': test_statistic,
                'p_value': p_value,
                'mechanism_assessment': 'MAR' if p_value < 0.05 else 'MCAR',
                'predictor_significance': predictor_pvalues.to_dict(),
                'model_summary': logit_model.summary()
            }
            
        except Exception as e:
            return {'error': f'Model fitting failed: {str(e)}'}
    
    def mice_imputation(self, data, target_vars=None, auxiliary_vars=None, 
                       n_imputations=20, max_iter=10):
        """
        Multiple Imputation using Chained Equations (MICE).
        
        Parameters
        ----------
        data : DataFrame
            Data with missing values
        target_vars : list
            Variables to impute
        auxiliary_vars : list
            Auxiliary variables for imputation models
        n_imputations : int
            Number of imputed datasets
        max_iter : int
            Maximum iterations for convergence
            
        Returns
        -------
        list
            List of imputed datasets
        """
        # Default target variables (numeric with missing data)
        if target_vars is None:
            numeric_cols = data.select_dtypes(include=[np.number]).columns
            target_vars = [col for col in numeric_cols if data[col].isnull().any()]
        
        # Default auxiliary variables
        if auxiliary_vars is None:
            auxiliary_vars = [
                'conflict_intensity', 'market_accessibility', 'distance_to_capital',
                'population_density', 'infrastructure_quality'
            ]
        
        # Available variables
        available_aux = [var for var in auxiliary_vars if var in data.columns]
        imputation_vars = list(set(target_vars + available_aux))
        
        # Prepare data for imputation
        imputation_data = data[imputation_vars].copy()
        
        # Configure MICE imputer
        self.mice_imputer = IterativeImputer(
            estimator=RandomForestRegressor(n_estimators=10, random_state=42),
            max_iter=max_iter,
            random_state=42
        )
        
        # Generate multiple imputations
        imputed_datasets = []
        
        for i in range(n_imputations):
            # Set different random seed for each imputation
            imputer = IterativeImputer(
                estimator=RandomForestRegressor(n_estimators=10, random_state=42+i),
                max_iter=max_iter,
                random_state=42+i
            )
            
            # Fit and transform
            imputed_values = imputer.fit_transform(imputation_data)
            
            # Create imputed dataset
            imputed_df = data.copy()
            imputed_df[imputation_vars] = imputed_values
            
            imputed_datasets.append(imputed_df)
        
        return imputed_datasets
    
    def selection_model_imputation(self, data, outcome_var, selection_vars, 
                                 exclusion_restriction=None):
        """
        Selection model (Heckman-type) for MNAR missingness.
        
        Parameters
        ----------
        data : DataFrame
            Data with missing values
        outcome_var : str
            Outcome variable with missing data
        selection_vars : list
            Variables predicting selection (missingness)
        exclusion_restriction : str
            Variable affecting selection but not outcome
            
        Returns
        -------
        dict
            Selection model results and imputed values
        """
        try:
            from statsmodels.discrete.discrete_model import Probit
            from scipy.stats import norm
            
            # Create selection indicator (1 = observed, 0 = missing)
            selection_indicator = (~data[outcome_var].isnull()).astype(int)
            
            # Prepare selection equation variables
            if exclusion_restriction and exclusion_restriction in data.columns:
                selection_predictors = selection_vars + [exclusion_restriction]
            else:
                selection_predictors = selection_vars
                
            available_predictors = [p for p in selection_predictors if p in data.columns]
            
            if not available_predictors:
                return {'error': 'No selection predictors available'}
            
            # Selection equation (Probit)
            X_selection = data[available_predictors].fillna(data[available_predictors].mean())
            X_selection = sm.add_constant(X_selection)
            
            selection_model = Probit(selection_indicator, X_selection).fit(disp=0)
            
            # Calculate inverse Mills ratio
            linear_pred = selection_model.predict(X_selection)
            mills_ratio = norm.pdf(linear_pred) / norm.cdf(linear_pred)
            
            # Outcome equation (only for observed cases)
            observed_data = data[selection_indicator == 1].copy()
            observed_data['mills_ratio'] = mills_ratio[selection_indicator == 1]
            
            # Outcome model
            outcome_predictors = [p for p in selection_vars if p in data.columns]
            if outcome_predictors:
                X_outcome = observed_data[outcome_predictors + ['mills_ratio']]
                X_outcome = sm.add_constant(X_outcome)
                y_outcome = observed_data[outcome_var]
                
                outcome_model = sm.OLS(y_outcome, X_outcome).fit()
                
                # Predict for missing cases
                missing_data = data[selection_indicator == 0].copy()
                if len(missing_data) > 0:
                    missing_data['mills_ratio'] = mills_ratio[selection_indicator == 0]
                    X_missing = missing_data[outcome_predictors + ['mills_ratio']]
                    X_missing = sm.add_constant(X_missing)
                    
                    predicted_values = outcome_model.predict(X_missing)
                    
                    # Create imputed dataset
                    imputed_data = data.copy()
                    imputed_data.loc[selection_indicator == 0, outcome_var] = predicted_values
                else:
                    imputed_data = data.copy()
                    predicted_values = []
                
                return {
                    'selection_model': selection_model,
                    'outcome_model': outcome_model,
                    'imputed_data': imputed_data,
                    'predicted_values': predicted_values,
                    'mills_ratio': mills_ratio,
                    'selection_bias_test': outcome_model.pvalues['mills_ratio']
                }
            else:
                return {'error': 'No outcome predictors available'}
                
        except ImportError:
            return {'error': 'Selection models require additional dependencies'}
        except Exception as e:
            return {'error': f'Selection model failed: {str(e)}'}
    
    def pattern_mixture_analysis(self, data, outcome_var, pattern_vars=None):
        """
        Pattern mixture model analysis.
        
        Parameters
        ----------
        data : DataFrame
            Data with missing values
        outcome_var : str
            Outcome variable
        pattern_vars : list
            Variables defining missing patterns
            
        Returns
        -------
        dict
            Pattern mixture analysis results
        """
        if pattern_vars is None:
            pattern_vars = ['conflict_intensity', 'market_accessibility']
        
        available_vars = [var for var in pattern_vars if var in data.columns]
        
        if not available_vars:
            return {'error': 'No pattern variables available'}
        
        # Create missing pattern indicator
        missing_pattern = data[available_vars].isnull().any(axis=1)
        
        # Analyze outcome by pattern
        pattern_results = {}
        
        for pattern in [True, False]:  # Missing vs Complete
            pattern_data = data[missing_pattern == pattern]
            
            if len(pattern_data) > 0 and outcome_var in pattern_data.columns:
                pattern_outcome = pattern_data[outcome_var].dropna()
                
                if len(pattern_outcome) > 0:
                    pattern_results[f'pattern_{pattern}'] = {
                        'n_obs': len(pattern_outcome),
                        'mean': pattern_outcome.mean(),
                        'std': pattern_outcome.std(),
                        'median': pattern_outcome.median(),
                        'min': pattern_outcome.min(),
                        'max': pattern_outcome.max()
                    }
        
        # Test for differences between patterns
        if len(pattern_results) == 2:
            from scipy.stats import ttest_ind
            
            complete_data = data[missing_pattern == False][outcome_var].dropna()
            missing_data = data[missing_pattern == True][outcome_var].dropna()
            
            if len(complete_data) > 0 and len(missing_data) > 0:
                t_stat, p_value = ttest_ind(complete_data, missing_data)
                pattern_results['difference_test'] = {
                    't_statistic': t_stat,
                    'p_value': p_value,
                    'significant_difference': p_value < 0.05
                }
        
        return pattern_results
    
    def comprehensive_sensitivity_analysis(self, data, outcome_var, 
                                         predictor_vars=None):
        """
        Comprehensive sensitivity analysis across missing data methods.
        
        Parameters
        ----------
        data : DataFrame
            Data with missing values
        outcome_var : str
            Outcome variable for analysis
        predictor_vars : list
            Predictor variables for models
            
        Returns
        -------
        dict
            Sensitivity analysis results
        """
        if predictor_vars is None:
            predictor_vars = ['conflict_intensity', 'global_price', 'aid_per_capita']
        
        available_predictors = [p for p in predictor_vars if p in data.columns]
        
        results = {}
        
        # 1. Complete case analysis
        complete_data = data.dropna(subset=[outcome_var] + available_predictors)
        if len(complete_data) > 10:  # Minimum sample size
            X_complete = sm.add_constant(complete_data[available_predictors])
            y_complete = complete_data[outcome_var]
            
            try:
                model_complete = sm.OLS(y_complete, X_complete).fit()
                results['complete_case'] = {
                    'n_obs': len(complete_data),
                    'coefficients': model_complete.params.to_dict(),
                    'pvalues': model_complete.pvalues.to_dict(),
                    'r_squared': model_complete.rsquared
                }
            except:
                results['complete_case'] = {'error': 'Model fitting failed'}
        
        # 2. MICE analysis
        try:
            imputed_datasets = self.mice_imputation(
                data, target_vars=[outcome_var], 
                auxiliary_vars=available_predictors, n_imputations=5
            )
            
            mice_results = []
            for imputed_data in imputed_datasets:
                X_mice = sm.add_constant(imputed_data[available_predictors])
                y_mice = imputed_data[outcome_var]
                
                try:
                    model_mice = sm.OLS(y_mice, X_mice).fit()
                    mice_results.append({
                        'coefficients': model_mice.params.to_dict(),
                        'pvalues': model_mice.pvalues.to_dict(),
                        'r_squared': model_mice.rsquared
                    })
                except:
                    continue
            
            if mice_results:
                # Pool results using Rubin's rules (simplified)
                pooled_coefs = {}
                for param in mice_results[0]['coefficients'].keys():
                    coef_values = [r['coefficients'][param] for r in mice_results]
                    pooled_coefs[param] = np.mean(coef_values)
                
                results['mice'] = {
                    'n_imputations': len(mice_results),
                    'pooled_coefficients': pooled_coefs,
                    'individual_results': mice_results
                }
        except Exception as e:
            results['mice'] = {'error': str(e)}
        
        # 3. Pattern mixture analysis
        try:
            pattern_results = self.pattern_mixture_analysis(data, outcome_var)
            results['pattern_mixture'] = pattern_results
        except Exception as e:
            results['pattern_mixture'] = {'error': str(e)}
        
        self.sensitivity_results = results
        return results


def demonstrate_missing_data_workflow(panel_data):
    """
    Demonstrate complete missing data workflow for Yemen analysis.
    
    Parameters
    ----------
    panel_data : DataFrame
        Panel data with missing values
        
    Returns
    -------
    dict
        Complete workflow results
    """
    handler = ConflictMissingDataHandler()
    
    # Step 1: Analyze missing patterns
    pattern_analysis = handler.analyze_missing_patterns(panel_data)
    
    # Step 2: Test missing mechanism
    mechanism_test = handler.test_missing_mechanism(
        panel_data, 'price_usd', 
        ['conflict_intensity', 'market_accessibility']
    )
    
    # Step 3: Comprehensive sensitivity analysis
    sensitivity_results = handler.comprehensive_sensitivity_analysis(
        panel_data, 'log_price_usd',
        ['conflict_intensity', 'global_price']
    )
    
    return {
        'pattern_analysis': pattern_analysis,
        'mechanism_test': mechanism_test,
        'sensitivity_analysis': sensitivity_results,
        'recommendations': {
            'primary_method': 'MICE' if mechanism_test.get('mechanism_assessment') == 'MAR' else 'Selection Model',
            'robustness_check': 'Pattern mixture analysis',
            'reporting': 'Compare across all methods for sensitivity'
        }
    }
