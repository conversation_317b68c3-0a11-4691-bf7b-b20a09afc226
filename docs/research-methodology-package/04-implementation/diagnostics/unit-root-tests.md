# Unit Root Test Implementations

**Target Audience**: Econometricians, Time Series Analysts  
**Module**: `yemen_market.models.three_tier.diagnostics`

## Overview

This document provides comprehensive implementation details for unit root tests used to assess stationarity in price series. These tests are essential prerequisites for cointegration analysis and proper model specification in the Yemen Market Integration study.

## Theoretical Foundation

### Stationarity vs Unit Root

A time series is stationary if its statistical properties (mean, variance, autocorrelation) are constant over time. A unit root process has the form:

$$y_t = \rho y_{t-1} + \epsilon_t$$

Where:
- $\rho = 1$: Unit root (non-stationary)
- $|\rho| < 1$: Stationary
- $\rho > 1$: Explosive (rare in economic data)

## Augmented Dickey-Fuller (ADF) Test

### Standard Implementation

```python
import numpy as np
import pandas as pd
from statsmodels.regression.linear_model import OLS
from statsmodels.tsa.stattools import adfuller
from scipy import stats
import statsmodels.api as sm

class AugmentedDickeyFullerTest:
    """
    Comprehensive ADF test implementation with multiple specifications.
    
    Tests the null hypothesis of a unit root against stationarity.
    """
    
    def __init__(self, max_lags: int = None):
        """
        Initialize ADF test.
        
        Parameters
        ----------
        max_lags : int, optional
            Maximum number of lags to consider.
            If None, uses Schwert criterion.
        """
        self.max_lags = max_lags
        self.critical_values = self._load_mackinnon_critical_values()
    
    def test(
        self,
        series: np.ndarray,
        regression: str = 'c',
        autolag: str = 'AIC',
        store: bool = True
    ) -> dict:
        """
        Perform ADF test with comprehensive diagnostics.
        
        Parameters
        ----------
        series : array-like
            Time series to test
        regression : str
            'c': constant only (default)
            'ct': constant and trend
            'ctt': constant, trend, and trend²
            'n': no constant, no trend
        autolag : str
            Method for lag selection: 'AIC', 'BIC', 'HQ', 't-stat'
        store : bool
            Whether to store detailed results
        """
        n = len(series)
        
        # Determine maximum lags if not specified
        if self.max_lags is None:
            # Schwert (1989) criterion
            self.max_lags = int(12 * (n / 100) ** (1/4))
        
        # Select optimal lag length
        optimal_lag = self._select_lag_length(
            series, regression, autolag
        )
        
        # Estimate ADF regression
        adf_result = self._estimate_adf_regression(
            series, optimal_lag, regression
        )
        
        # Calculate test statistic
        test_stat = adf_result['rho_tstat']
        
        # Get critical values
        crit_vals = self._get_critical_values(n, regression)
        
        # MacKinnon approximate p-value
        p_value = self._mackinnon_p_value(test_stat, n, regression)
        
        # Diagnostic tests on residuals
        diagnostics = self._residual_diagnostics(
            adf_result['residuals'], optimal_lag
        )
        
        return {
            'test_statistic': test_stat,
            'p_value': p_value,
            'lags_used': optimal_lag,
            'n_obs': n - optimal_lag - 1,
            'critical_values': crit_vals,
            'regression_type': regression,
            'reject_unit_root': test_stat < crit_vals['5%'],
            'diagnostics': diagnostics,
            'adf_regression': adf_result if store else None
        }
    
    def _estimate_adf_regression(
        self,
        series: np.ndarray,
        lags: int,
        regression: str
    ) -> dict:
        """Estimate the ADF regression equation."""
        # Create differences
        y = series[1:]
        y_lag1 = series[:-1]
        dy = np.diff(series)
        
        # Build design matrix
        X = [y_lag1[lags:]]  # y_{t-1}
        
        # Add lagged differences
        for lag in range(1, lags + 1):
            if lag < len(dy):
                X.append(dy[lags-lag:-lag])
        
        # Add deterministic terms
        n = len(X[0])
        
        if regression in ['c', 'ct', 'ctt']:
            X.append(np.ones(n))  # Constant
        
        if regression in ['ct', 'ctt']:
            X.append(np.arange(1, n + 1))  # Linear trend
        
        if regression == 'ctt':
            X.append(np.arange(1, n + 1) ** 2)  # Quadratic trend
        
        # Stack and transpose
        X = np.column_stack(X)
        y_final = dy[lags:]
        
        # OLS estimation
        model = OLS(y_final, X)
        results = model.fit()
        
        # Extract coefficient on y_{t-1}
        rho_hat = results.params[0]
        rho_se = results.bse[0]
        rho_tstat = rho_hat / rho_se
        
        return {
            'rho_hat': rho_hat,
            'rho_se': rho_se,
            'rho_tstat': rho_tstat,
            'residuals': results.resid,
            'full_results': results
        }
    
    def _select_lag_length(
        self,
        series: np.ndarray,
        regression: str,
        method: str
    ) -> int:
        """Select optimal lag length using information criteria."""
        ic_values = {}
        
        for lag in range(self.max_lags + 1):
            adf_result = self._estimate_adf_regression(
                series, lag, regression
            )
            
            n = len(adf_result['residuals'])
            k = lag + 1  # Number of parameters
            
            # Calculate information criteria
            log_l = -n/2 * (np.log(2*np.pi) + 1) - n/2 * np.log(
                np.sum(adf_result['residuals']**2) / n
            )
            
            if method == 'AIC':
                ic = -2 * log_l + 2 * k
            elif method == 'BIC':
                ic = -2 * log_l + k * np.log(n)
            elif method == 'HQ':
                ic = -2 * log_l + 2 * k * np.log(np.log(n))
            elif method == 't-stat':
                # Use t-stat on last lag
                if lag > 0:
                    t_stat = adf_result['full_results'].tvalues[lag]
                    ic = -abs(t_stat)  # Want to maximize |t|
                else:
                    ic = np.inf
            
            ic_values[lag] = ic
        
        # Select lag with minimum IC
        optimal_lag = min(ic_values, key=ic_values.get)
        
        return optimal_lag
    
    def _residual_diagnostics(
        self,
        residuals: np.ndarray,
        lags_used: int
    ) -> dict:
        """Perform diagnostic tests on ADF residuals."""
        from statsmodels.stats.diagnostic import acorr_ljungbox
        from statsmodels.stats.stattools import jarque_bera
        
        # Ljung-Box test for autocorrelation
        lb_result = acorr_ljungbox(
            residuals,
            lags=min(10, len(residuals)//5),
            return_df=True
        )
        
        # Jarque-Bera test for normality
        jb_stat, jb_pvalue = jarque_bera(residuals)
        
        # ARCH test for heteroskedasticity
        squared_resid = residuals ** 2
        arch_result = acorr_ljungbox(
            squared_resid,
            lags=min(5, len(residuals)//5),
            return_df=True
        )
        
        return {
            'ljung_box': {
                'statistic': lb_result['lb_stat'].iloc[-1],
                'p_value': lb_result['lb_pvalue'].iloc[-1],
                'no_autocorrelation': lb_result['lb_pvalue'].iloc[-1] > 0.05
            },
            'jarque_bera': {
                'statistic': jb_stat,
                'p_value': jb_pvalue,
                'is_normal': jb_pvalue > 0.05
            },
            'arch_test': {
                'statistic': arch_result['lb_stat'].iloc[-1],
                'p_value': arch_result['lb_pvalue'].iloc[-1],
                'no_arch': arch_result['lb_pvalue'].iloc[-1] > 0.05
            }
        }
    
    def _mackinnon_p_value(
        self,
        test_stat: float,
        n: int,
        regression: str
    ) -> float:
        """
        Calculate MacKinnon (1994, 2010) p-values.
        
        Based on response surface regressions.
        """
        # MacKinnon (2010) coefficients
        # Format: [const, 1/n, 1/n²]
        if regression == 'n':
            tau_coef = [-1.04, -0.40, 0]
            tau_star = -0.65
        elif regression == 'c':
            tau_coef = [-2.57, -2.86, -9.52]
            tau_star = -13.7
        elif regression == 'ct':
            tau_coef = [-3.12, -2.72, -3.19]
            tau_star = -21.5
        else:  # 'ctt'
            tau_coef = [-3.55, -3.68, -5.48]
            tau_star = -27.4
        
        # Response surface approximation
        if n > 200:
            tau_approx = tau_coef[0]
        else:
            tau_approx = (tau_coef[0] + 
                         tau_coef[1] / n + 
                         tau_coef[2] / (n ** 2))
        
        # Approximate p-value using normal approximation
        # This is simplified - full implementation uses numerical integration
        if test_stat < tau_star:
            p_value = 0.0
        elif test_stat > tau_approx:
            p_value = 1.0
        else:
            # Linear interpolation (simplified)
            p_value = (test_stat - tau_star) / (tau_approx - tau_star)
        
        return p_value
```

## Phillips-Perron (PP) Test

### Non-parametric Correction Implementation

```python
class PhillipsPerronTest:
    """
    Phillips-Perron unit root test.
    
    Non-parametric correction for serial correlation and heteroskedasticity.
    """
    
    def test(
        self,
        series: np.ndarray,
        regression: str = 'c',
        lags: int = None,
        kernel: str = 'bartlett'
    ) -> dict:
        """
        Perform Phillips-Perron test.
        
        Parameters
        ----------
        series : array-like
            Time series to test
        regression : str
            Type of regression ('n', 'c', 'ct')
        lags : int
            Number of lags for Newey-West variance
        kernel : str
            Kernel for HAC variance ('bartlett', 'parzen')
        """
        n = len(series)
        
        # Automatic bandwidth selection
        if lags is None:
            lags = int(4 * (n / 100) ** (2/9))
        
        # Run basic DF regression (no augmentation)
        y = series[1:]
        y_lag = series[:-1]
        dy = np.diff(series)
        
        # Build design matrix
        if regression == 'n':
            X = y_lag.reshape(-1, 1)
        elif regression == 'c':
            X = sm.add_constant(y_lag)
        elif regression == 'ct':
            trend = np.arange(1, len(y) + 1)
            X = np.column_stack([np.ones(len(y)), y_lag, trend])
        
        # OLS estimation
        model = OLS(dy, X)
        results = model.fit()
        
        # Extract coefficient and t-stat
        if regression == 'n':
            rho_idx = 0
        else:
            rho_idx = 1
        
        rho_hat = results.params[rho_idx]
        t_rho = results.tvalues[rho_idx]
        
        # Calculate long-run variance
        residuals = results.resid
        gamma_0 = np.var(residuals)
        
        # Newey-West long-run variance
        s_squared = self._newey_west_variance(
            residuals, lags, kernel
        )
        
        # Phillips-Perron corrections
        # Correction factor
        lambda_squared = s_squared - gamma_0
        
        # Corrected statistics
        n_usable = len(dy)
        
        # Z_rho statistic
        se_rho = results.bse[rho_idx]
        z_rho = n_usable * rho_hat - (n_usable**2 * se_rho**2 * lambda_squared) / (2 * s_squared)
        
        # Z_t statistic  
        z_t = (gamma_0 / s_squared) * t_rho - (lambda_squared / (2 * np.sqrt(s_squared))) * (n_usable * se_rho / np.sqrt(gamma_0))
        
        # Critical values (same as ADF)
        crit_vals = self._get_pp_critical_values(n, regression)
        
        # P-value approximation
        p_value = self._calculate_pp_pvalue(z_t, n, regression)
        
        return {
            'z_t_statistic': z_t,
            'z_rho_statistic': z_rho,
            'p_value': p_value,
            'lags_used': lags,
            'critical_values': crit_vals,
            'reject_unit_root': z_t < crit_vals['5%'],
            'long_run_variance': s_squared,
            'gamma_0': gamma_0
        }
    
    def _newey_west_variance(
        self,
        residuals: np.ndarray,
        lags: int,
        kernel: str
    ) -> float:
        """Calculate Newey-West HAC variance estimator."""
        n = len(residuals)
        
        # Autocovariances
        gamma = np.zeros(lags + 1)
        for j in range(lags + 1):
            if j == 0:
                gamma[j] = np.mean(residuals ** 2)
            else:
                gamma[j] = np.mean(residuals[j:] * residuals[:-j])
        
        # Apply kernel weights
        if kernel == 'bartlett':
            weights = 1 - np.arange(lags + 1) / (lags + 1)
        elif kernel == 'parzen':
            z = np.arange(lags + 1) / (lags + 1)
            weights = np.where(
                z <= 0.5,
                1 - 6 * z**2 + 6 * z**3,
                2 * (1 - z)**3
            )
        else:
            weights = np.ones(lags + 1)
        
        # Long-run variance
        s_squared = gamma[0] + 2 * np.sum(weights[1:] * gamma[1:])
        
        return s_squared
```

## KPSS Test

### Testing Stationarity as Null

```python
class KPSSTest:
    """
    Kwiatkowski-Phillips-Schmidt-Shin test.
    
    Tests stationarity (null) against unit root (alternative).
    """
    
    def test(
        self,
        series: np.ndarray,
        regression: str = 'c',
        lags: int = None
    ) -> dict:
        """
        Perform KPSS test.
        
        Parameters
        ----------
        series : array-like
            Time series to test
        regression : str
            'c': level stationarity
            'ct': trend stationarity
        lags : int
            Lags for long-run variance
        """
        n = len(series)
        
        # Automatic lag selection
        if lags is None:
            lags = int(10 * np.sqrt(n) / 14)
        
        # Demean or detrend
        if regression == 'c':
            # Test for level stationarity
            X = np.ones((n, 1))
        else:  # 'ct'
            # Test for trend stationarity
            X = np.column_stack([
                np.ones(n),
                np.arange(1, n + 1)
            ])
        
        # OLS residuals
        model = OLS(series, X)
        results = model.fit()
        residuals = results.resid
        
        # Partial sums
        s_t = np.cumsum(residuals)
        
        # Test statistic
        lm_stat = np.sum(s_t ** 2) / (n ** 2)
        
        # Long-run variance
        s_squared = self._bartlett_long_run_variance(residuals, lags)
        
        # Normalized test statistic
        eta = lm_stat / s_squared
        
        # Critical values
        if regression == 'c':
            crit_vals = {
                '1%': 0.739,
                '5%': 0.463,
                '10%': 0.347
            }
        else:  # 'ct'
            crit_vals = {
                '1%': 0.216,
                '5%': 0.146,
                '10%': 0.119
            }
        
        return {
            'test_statistic': eta,
            'critical_values': crit_vals,
            'lags_used': lags,
            'reject_stationarity': eta > crit_vals['5%'],
            'long_run_variance': s_squared,
            'regression_type': regression
        }
    
    def _bartlett_long_run_variance(
        self,
        residuals: np.ndarray,
        lags: int
    ) -> float:
        """Estimate long-run variance with Bartlett kernel."""
        n = len(residuals)
        
        # Calculate autocovariances
        acov = np.zeros(lags + 1)
        for k in range(lags + 1):
            if k == 0:
                acov[k] = np.mean(residuals ** 2)
            else:
                acov[k] = np.mean(residuals[k:] * residuals[:-k])
        
        # Bartlett weights
        weights = 1 - np.arange(lags + 1) / (lags + 1)
        
        # Long-run variance
        s_squared = acov[0] + 2 * np.sum(weights[1:] * acov[1:])
        
        return s_squared
```

## Panel Unit Root Tests

### Im-Pesaran-Shin (IPS) Test

```python
class ImPesaranShinTest:
    """
    Im, Pesaran and Shin (2003) panel unit root test.
    
    Allows for heterogeneous autoregressive coefficients.
    """
    
    def test(
        self,
        panel_data: pd.DataFrame,
        var_name: str,
        entity_var: str = 'entity',
        time_var: str = 'date',
        lags: int = None,
        trend: bool = True
    ) -> dict:
        """
        Perform IPS test on panel data.
        
        H0: All series have unit root
        H1: Some series are stationary
        """
        entities = panel_data[entity_var].unique()
        n_entities = len(entities)
        time_periods = panel_data[time_var].unique()
        T = len(time_periods)
        
        # Run ADF for each entity
        adf_stats = []
        entity_results = []
        
        for entity in entities:
            entity_data = panel_data[
                panel_data[entity_var] == entity
            ][var_name].values
            
            if len(entity_data) < 20:
                continue
            
            # Entity-specific ADF test
            adf_test = AugmentedDickeyFullerTest()
            result = adf_test.test(
                entity_data,
                regression='ct' if trend else 'c',
                autolag='AIC'
            )
            
            adf_stats.append(result['test_statistic'])
            entity_results.append({
                'entity': entity,
                'adf_stat': result['test_statistic'],
                'p_value': result['p_value'],
                'lags': result['lags_used']
            })
        
        # Calculate IPS statistics
        t_bar = np.mean(adf_stats)
        
        # Expected value and variance under null
        # From IPS (2003) Table 2
        if trend:
            if T >= 25:
                E_t = -2.14
                V_t = 0.88
            else:
                E_t = -2.10
                V_t = 0.95
        else:
            if T >= 25:
                E_t = -1.52
                V_t = 0.82
            else:
                E_t = -1.50
                V_t = 0.90
        
        # W-bar statistic
        W_bar = np.sqrt(n_entities) * (t_bar - E_t) / np.sqrt(V_t)
        
        # P-value (standard normal)
        p_value = stats.norm.cdf(W_bar)
        
        # Z-statistic (alternative formulation)
        Z_bar = np.sqrt(n_entities / (2 * 3)) * (t_bar + 3)
        z_p_value = stats.norm.cdf(Z_bar)
        
        return {
            'W_bar_statistic': W_bar,
            'p_value': p_value,
            'Z_bar_statistic': Z_bar,
            'z_p_value': z_p_value,
            't_bar': t_bar,
            'n_entities': n_entities,
            'n_stationary': sum(r['p_value'] < 0.05 for r in entity_results),
            'reject_all_unit_roots': p_value < 0.05,
            'entity_results': entity_results
        }
```

### Levin-Lin-Chu (LLC) Test

```python
class LevinLinChuTest:
    """
    Levin, Lin and Chu (2002) panel unit root test.
    
    Assumes homogeneous autoregressive coefficient.
    """
    
    def test(
        self,
        panel_data: pd.DataFrame,
        var_name: str,
        entity_var: str = 'entity',
        time_var: str = 'date',
        kernel: str = 'bartlett'
    ) -> dict:
        """
        Perform LLC test.
        
        H0: Common unit root process
        H1: Common stationary process
        """
        # Step 1: Run ADF regressions for each entity
        entities = panel_data[entity_var].unique()
        n = len(entities)
        
        # Collect residuals and coefficients
        all_dy = []
        all_y_lag = []
        all_residuals = []
        
        for entity in entities:
            entity_data = panel_data[
                panel_data[entity_var] == entity
            ]
            
            y = entity_data[var_name].values
            if len(y) < 20:
                continue
            
            # First differences and lags
            dy = np.diff(y)
            y_lag = y[:-1]
            
            # Remove entity-specific means
            dy_demean = dy - np.mean(dy)
            y_lag_demean = y_lag - np.mean(y_lag)
            
            all_dy.extend(dy_demean)
            all_y_lag.extend(y_lag_demean)
            
            # Run auxiliary regression for each entity
            model = OLS(dy_demean, sm.add_constant(y_lag_demean))
            results = model.fit()
            all_residuals.extend(results.resid)
        
        # Step 2: Pooled regression
        all_dy = np.array(all_dy)
        all_y_lag = np.array(all_y_lag)
        
        pooled_model = OLS(all_dy, all_y_lag.reshape(-1, 1))
        pooled_results = pooled_model.fit()
        
        rho_hat = pooled_results.params[0]
        se_rho = pooled_results.bse[0]
        
        # Step 3: Bias correction
        # LLC provide bias correction factors
        T = len(panel_data[time_var].unique())
        
        # Simplified bias correction
        if T >= 25:
            mu_T = -1.52
            sigma_T = 1.07
        else:
            mu_T = -1.50
            sigma_T = 1.10
        
        # Step 4: Adjusted t-statistic
        t_star = (rho_hat / se_rho - n * T * mu_T) / (n * T * sigma_T)
        
        # P-value
        p_value = stats.norm.cdf(t_star)
        
        return {
            'test_statistic': t_star,
            'p_value': p_value,
            'rho_pooled': rho_hat,
            'se_pooled': se_rho,
            'n_entities': n,
            'T_periods': T,
            'reject_unit_root': p_value < 0.05
        }
```

### Fisher-Type Tests

```python
def fisher_panel_unit_root_test(
    panel_data: pd.DataFrame,
    var_name: str,
    method: str = 'adf',
    combination: str = 'pvalue'
) -> dict:
    """
    Fisher-type panel unit root tests (Maddala & Wu, 1999).
    
    Combines p-values from individual unit root tests.
    
    Parameters
    ----------
    method : str
        'adf' or 'pp' for individual tests
    combination : str
        'pvalue': Fisher's inverse chi-square
        'logit': Inverse logit
        'normal': Inverse normal
    """
    entities = panel_data['entity'].unique()
    p_values = []
    
    # Run individual tests
    for entity in entities:
        entity_data = panel_data[
            panel_data['entity'] == entity
        ][var_name].values
        
        if len(entity_data) < 20:
            continue
        
        if method == 'adf':
            test = AugmentedDickeyFullerTest()
            result = test.test(entity_data)
        else:  # 'pp'
            test = PhillipsPerronTest()
            result = test.test(entity_data)
        
        p_values.append(result['p_value'])
    
    n = len(p_values)
    
    # Combine p-values
    if combination == 'pvalue':
        # Fisher's inverse chi-square
        statistic = -2 * np.sum(np.log(p_values))
        df = 2 * n
        combined_pvalue = 1 - stats.chi2.cdf(statistic, df)
        
    elif combination == 'logit':
        # Inverse logit (Choi, 2001)
        logit_sum = np.sum(np.log(p_values / (1 - p_values)))
        statistic = -logit_sum / np.sqrt(n * np.pi**2 / 3)
        combined_pvalue = stats.norm.cdf(statistic)
        
    elif combination == 'normal':
        # Inverse normal (Choi, 2001)
        z_values = [stats.norm.ppf(p) for p in p_values]
        statistic = -np.sum(z_values) / np.sqrt(n)
        combined_pvalue = stats.norm.cdf(statistic)
    
    return {
        'test_statistic': statistic,
        'p_value': combined_pvalue,
        'n_entities': n,
        'individual_pvalues': p_values,
        'reject_all_unit_roots': combined_pvalue < 0.05,
        'method': f"Fisher-{combination}",
        'proportion_stationary': sum(p < 0.05 for p in p_values) / n
    }
```

## Seasonal Unit Root Tests

### HEGY Test

```python
class HEGYSeasonalUnitRootTest:
    """
    Hylleberg-Engle-Granger-Yoo test for seasonal unit roots.
    
    Tests for unit roots at different seasonal frequencies.
    """
    
    def test(
        self,
        series: np.ndarray,
        frequency: int = 12,
        regression: str = 'c'
    ) -> dict:
        """
        Perform HEGY test for seasonal unit roots.
        
        Parameters
        ----------
        series : array-like
            Time series data
        frequency : int
            Seasonal frequency (12 for monthly, 4 for quarterly)
        regression : str
            Deterministic components
        """
        n = len(series)
        
        if frequency == 4:
            # Quarterly case
            return self._hegy_quarterly(series, regression)
        elif frequency == 12:
            # Monthly case
            return self._hegy_monthly(series, regression)
        else:
            raise ValueError(f"Frequency {frequency} not implemented")
    
    def _hegy_quarterly(
        self,
        series: np.ndarray,
        regression: str
    ) -> dict:
        """HEGY test for quarterly data."""
        # Create filters for different frequencies
        # y1: Zero frequency (1-L^4)
        # y2: Nyquist frequency (1+L^4)
        # y3, y4: Seasonal frequencies
        
        n = len(series)
        L = 4  # Quarterly
        
        # Apply filters
        y1 = np.zeros(n - L)
        y2 = np.zeros(n - L)
        y3 = np.zeros(n - L)
        y4 = np.zeros(n - L)
        
        for t in range(L, n):
            y1[t-L] = (series[t] + series[t-1] + series[t-2] + series[t-3])
            y2[t-L] = -(series[t] - series[t-1] + series[t-2] - series[t-3])
            y3[t-L] = -(series[t] - series[t-2])
            y4[t-L] = -(series[t-1] - series[t-3])
        
        # Create lagged variables
        y1_lag = y1[:-1]
        y2_lag = y2[:-1]
        y3_lag = y3[:-1]
        y4_lag = y4[:-1]
        
        # Dependent variable: Δ₄y_t
        dy4 = series[L+1:] - series[1:-L]
        
        # Build regression
        X = np.column_stack([
            y1_lag,
            y2_lag,
            y3_lag[:-1],
            y4_lag[:-1],
            y3_lag[1:],
            y4_lag[1:]
        ])
        
        # Add deterministics
        if regression in ['c', 'ct']:
            X = sm.add_constant(X)
        
        # Estimate model
        model = OLS(dy4[1:], X)
        results = model.fit()
        
        # Test statistics
        t_stats = results.tvalues
        
        # Extract relevant statistics
        pi1_tstat = t_stats[0]  # Zero frequency
        pi2_tstat = t_stats[1]  # Nyquist frequency
        
        # F-test for seasonal frequencies
        # H0: π3 = π4 = 0
        R = np.array([
            [0, 0, 1, 0, 0, 0],
            [0, 0, 0, 1, 0, 0]
        ])
        
        if regression in ['c', 'ct']:
            R = np.column_stack([np.zeros((2, 1)), R])
        
        f_stat = results.f_test(R).fvalue
        
        # Critical values (approximate)
        crit_vals_t = {
            '1%': -3.48,
            '5%': -2.88,
            '10%': -2.58
        }
        
        crit_vals_f = {
            '1%': 8.73,
            '5%': 6.42,
            '10%': 5.47
        }
        
        return {
            'zero_freq_tstat': pi1_tstat,
            'nyquist_freq_tstat': pi2_tstat,
            'seasonal_freq_fstat': f_stat,
            'critical_values_t': crit_vals_t,
            'critical_values_f': crit_vals_f,
            'unit_roots': {
                'zero_freq': pi1_tstat > crit_vals_t['5%'],
                'nyquist_freq': pi2_tstat > crit_vals_t['5%'],
                'seasonal_freq': f_stat < crit_vals_f['5%']
            }
        }
```

## Structural Break Unit Root Tests

### Zivot-Andrews Test

```python
class ZivotAndrewsTest:
    """
    Zivot-Andrews test with endogenous structural break.
    
    Tests unit root against trend-break stationary alternative.
    """
    
    def test(
        self,
        series: np.ndarray,
        model: str = 'C',
        trim: float = 0.15
    ) -> dict:
        """
        Perform Zivot-Andrews test.
        
        Parameters
        ----------
        series : array-like
            Time series data
        model : str
            'A': Break in intercept
            'B': Break in trend
            'C': Break in both
        trim : float
            Trimming proportion
        """
        n = len(series)
        trim_start = int(n * trim)
        trim_end = int(n * (1 - trim))
        
        # Store results for each break point
        results = []
        
        for break_point in range(trim_start, trim_end):
            # Create break dummy variables
            DU = np.zeros(n)  # Level shift
            DT = np.zeros(n)  # Trend shift
            
            DU[break_point:] = 1
            DT[break_point:] = np.arange(1, n - break_point + 1)
            
            # Set up regression based on model
            y = series[1:]
            y_lag = series[:-1]
            dy = np.diff(series)
            trend = np.arange(1, n)
            
            if model == 'A':
                # Break in intercept only
                X = np.column_stack([
                    np.ones(n-1),
                    trend[:-1],
                    DU[:-1],
                    y_lag
                ])
            elif model == 'B':
                # Break in trend only
                X = np.column_stack([
                    np.ones(n-1),
                    trend[:-1],
                    DT[:-1],
                    y_lag
                ])
            else:  # model == 'C'
                # Break in both
                X = np.column_stack([
                    np.ones(n-1),
                    trend[:-1],
                    DU[:-1],
                    DT[:-1],
                    y_lag
                ])
            
            # Add lagged differences (ADF-type regression)
            max_lags = int(12 * (n/100)**(1/4))
            
            # Select lags by AIC
            best_aic = np.inf
            best_lag = 0
            
            for lag in range(max_lags + 1):
                X_aug = X.copy()
                
                for j in range(1, lag + 1):
                    if j < len(dy):
                        lag_diff = np.concatenate([[np.nan]*j, dy[:-j]])
                        X_aug = np.column_stack([X_aug, lag_diff[1:]])
                
                # Remove NaN rows
                valid = ~np.any(np.isnan(X_aug), axis=1)
                
                if np.sum(valid) < X_aug.shape[1] + 10:
                    continue
                
                model = OLS(dy[valid], X_aug[valid])
                res = model.fit()
                
                aic = res.aic
                if aic < best_aic:
                    best_aic = aic
                    best_lag = lag
                    best_results = res
            
            # Extract t-statistic on y_{t-1}
            t_stat = best_results.tvalues[-best_lag-1]
            
            results.append({
                'break_point': break_point,
                't_statistic': t_stat,
                'lags': best_lag
            })
        
        # Find minimum t-statistic
        min_result = min(results, key=lambda x: x['t_statistic'])
        
        # Critical values from Zivot-Andrews (1992)
        if model == 'A':
            crit_vals = {'1%': -5.34, '5%': -4.80, '10%': -4.58}
        elif model == 'B':
            crit_vals = {'1%': -4.93, '5%': -4.42, '10%': -4.11}
        else:  # 'C'
            crit_vals = {'1%': -5.57, '5%': -5.08, '10%': -4.82}
        
        return {
            'test_statistic': min_result['t_statistic'],
            'break_date': min_result['break_point'],
            'break_fraction': min_result['break_point'] / n,
            'critical_values': crit_vals,
            'lags_used': min_result['lags'],
            'reject_unit_root': min_result['t_statistic'] < crit_vals['5%'],
            'model': model
        }
```

## Application Examples

### Yemen Price Series Testing

```python
def test_yemen_price_stationarity(
    price_data: pd.DataFrame,
    commodity: str = 'wheat'
) -> dict:
    """
    Comprehensive stationarity testing for Yemen price data.
    """
    results = {}
    
    # Extract price series
    price_series = price_data[
        price_data['commodity'] == commodity
    ]['log_price'].values
    
    # 1. Standard ADF test
    adf = AugmentedDickeyFullerTest()
    results['adf'] = adf.test(price_series, regression='ct')
    
    # 2. Phillips-Perron test
    pp = PhillipsPerronTest()
    results['pp'] = pp.test(price_series, regression='ct')
    
    # 3. KPSS test (null = stationary)
    kpss = KPSSTest()
    results['kpss'] = kpss.test(price_series, regression='ct')
    
    # 4. Zivot-Andrews test (structural break)
    za = ZivotAndrewsTest()
    results['zivot_andrews'] = za.test(price_series, model='C')
    
    # 5. Summary interpretation
    results['summary'] = {
        'likely_stationary': (
            results['adf']['reject_unit_root'] and
            results['pp']['reject_unit_root'] and
            not results['kpss']['reject_stationarity']
        ),
        'structural_break_detected': results['zivot_andrews']['reject_unit_root'],
        'break_date': results['zivot_andrews']['break_date'] if results['zivot_andrews']['reject_unit_root'] else None
    }
    
    return results
```

## See Also

- [Cointegration Tests](../econometric-models/cointegration.md) - Next step after unit root testing
- [Time Series Methods](../econometric-models/time-series.md) - General time series analysis
- [Diagnostic Tests](diagnostic-tests.md) - Other diagnostic procedures
- [API Reference: Diagnostics](../../03-api-reference/models/three_tier/diagnostics.md)