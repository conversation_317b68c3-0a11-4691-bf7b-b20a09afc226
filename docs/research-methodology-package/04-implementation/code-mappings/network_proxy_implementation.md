# Network Proxy Implementation Guide (N1)

## Overview

This document provides implementation guidance for calculating network proxy measures from existing data to test hypothesis N1: Network effects moderate price transmission within currency zones.

## Network Proxy Measures

Based on the theoretical framework, we identify three proxy measures calculable from existing data:

1. **Market Reporting Frequency** (trader density proxy)
2. **Distance to Distribution Hubs** (supply chain integration)
3. **Pre-war Road Density** (infrastructure persistence)

## Implementation

### 1. Market Reporting Frequency

```python
def calculate_market_reporting_frequency(df):
    """
    Calculate market reporting frequency as proxy for trader density.
    
    Markets with consistent reporting likely have more active traders.
    """
    
    # Calculate reporting frequency by market and period
    reporting_freq = df.groupby(['market_id', 'year_month']).agg({
        'price_yer': 'count',  # Number of price observations
        'commodity': 'nunique'  # Number of commodities reported
    }).reset_index()
    
    reporting_freq.columns = ['market_id', 'year_month', 'obs_count', 'commodity_count']
    
    # Calculate reporting consistency (rolling)
    reporting_freq['reporting_consistency'] = (
        reporting_freq.groupby('market_id')['obs_count']
        .rolling(window=3, min_periods=1)
        .mean()
        .reset_index(drop=True)
    )
    
    # Normalize to 0-1 scale
    reporting_freq['market_activity_index'] = (
        reporting_freq['reporting_consistency'] / 
        reporting_freq['reporting_consistency'].max()
    )
    
    return reporting_freq
```

### 2. Distance to Distribution Hubs

```python
def calculate_hub_distances(df):
    """
    Calculate distance to major distribution hubs.
    
    Hubs identified as governorate capitals and major ports.
    """
    
    from geopy.distance import geodesic
    
    # Define major distribution hubs
    DISTRIBUTION_HUBS = {
        'Aden': (12.7855, 45.0187),          # Major port
        'Al Hudaydah': (14.7978, 42.9545),   # Major port
        "Sana'a": (15.3694, 44.1910),        # Capital
        'Taiz': (13.5789, 44.0178),          # Major city
        'Mukalla': (14.5424, 49.1242),       # Eastern port
        'Marib': (15.4725, 45.3258)          # Energy hub
    }
    
    # Calculate minimum distance to any hub
    def min_distance_to_hub(lat, lon):
        if pd.isna(lat) or pd.isna(lon):
            return np.nan
        
        market_loc = (lat, lon)
        distances = [
            geodesic(market_loc, hub_loc).km 
            for hub_loc in DISTRIBUTION_HUBS.values()
        ]
        return min(distances)
    
    # Apply to unique markets
    markets = df[['market_id', 'lat', 'lon']].drop_duplicates()
    markets['distance_to_hub_km'] = markets.apply(
        lambda x: min_distance_to_hub(x['lat'], x['lon']), 
        axis=1
    )
    
    # Create supply chain integration index (inverse of distance)
    markets['supply_chain_integration'] = (
        1 / (1 + markets['distance_to_hub_km'] / 100)
    )
    
    return markets[['market_id', 'distance_to_hub_km', 'supply_chain_integration']]
```

### 3. Pre-war Road Density (Requires External Data)

```python
def estimate_infrastructure_persistence(df):
    """
    Estimate pre-war infrastructure quality using governorate-level proxies.
    
    In absence of detailed road data, use population density and 
    urban classification as proxies.
    """
    
    # Governorate-level infrastructure scores (based on pre-war development)
    INFRASTRUCTURE_SCORES = {
        'Aden': 0.9,            # Major port city
        'Sana\'a': 0.85,        # Capital
        'Taiz': 0.8,            # Major city
        'Al Hudaydah': 0.75,    # Port city
        'Ibb': 0.7,             # Agricultural center
        'Dhamar': 0.6,          # Secondary city
        'Hajjah': 0.5,          # Rural
        'Sa\'ada': 0.4,         # Remote
        'Al Maharah': 0.3,      # Very remote
        'Socotra': 0.2          # Island
    }
    
    # Map to markets
    markets = df[['market_id', 'governorate']].drop_duplicates()
    markets['infrastructure_score'] = (
        markets['governorate'].map(INFRASTRUCTURE_SCORES).fillna(0.5)
    )
    
    return markets[['market_id', 'infrastructure_score']]
```

### 4. Composite Network Index

```python
def create_network_composite_index(df):
    """
    Create composite network index from all three measures.
    """
    
    # Calculate individual components
    reporting = calculate_market_reporting_frequency(df)
    hub_dist = calculate_hub_distances(df)
    infrastructure = estimate_infrastructure_persistence(df)
    
    # Get latest values for each market
    latest_reporting = (
        reporting.sort_values('year_month')
        .groupby('market_id')
        .last()
        ['market_activity_index']
        .reset_index()
    )
    
    # Merge components
    network_df = (
        latest_reporting
        .merge(hub_dist[['market_id', 'supply_chain_integration']], on='market_id')
        .merge(infrastructure[['market_id', 'infrastructure_score']], on='market_id')
    )
    
    # Create composite index (equal weights)
    network_df['network_index'] = (
        network_df['market_activity_index'] * 0.4 +
        network_df['supply_chain_integration'] * 0.3 +
        network_df['infrastructure_score'] * 0.3
    )
    
    # Categorize into tertiles
    network_df['network_category'] = pd.qcut(
        network_df['network_index'], 
        q=3, 
        labels=['low', 'medium', 'high']
    )
    
    return network_df
```

### 5. Testing N1: Network Effects on Price Transmission

```python
def test_network_moderation(df):
    """
    Test N1: Network effects moderate price transmission within zones.
    """
    
    from linearmodels import PanelOLS
    
    # Add network measures
    network_df = create_network_composite_index(df)
    df = df.merge(network_df, on='market_id')
    
    # Create interaction terms
    df['conflict_x_network'] = df['conflict_intensity'] * df['network_index']
    df['currency_zone_x_network'] = (
        df['currency_zone_houthi'] * df['network_index']
    )
    
    # Model with network interactions
    model = PanelOLS(
        dependent=df['log_price_usd_adjusted'],
        exog=df[[
            'conflict_intensity',
            'network_index',
            'conflict_x_network',
            'currency_zone_houthi',
            'currency_zone_x_network',
            'global_price'
        ]],
        entity_effects=True,
        time_effects=True
    )
    
    results = model.fit(cov_type='clustered', cluster_entity=True)
    
    # Test hypothesis: Network effects reduce conflict impact
    n1_validated = (
        results.params['conflict_x_network'] < 0 and 
        results.pvalues['conflict_x_network'] < 0.05
    )
    
    return {
        'model_results': results,
        'n1_validated': n1_validated,
        'network_effect_size': results.params['conflict_x_network']
    }
```

### 6. Price Transmission Analysis

```python
def analyze_price_transmission_by_network(df):
    """
    Analyze how network strength affects price transmission between markets.
    """
    
    # Calculate price correlations within currency zones
    correlations = []
    
    for zone in ['houthi', 'government']:
        zone_markets = df[df['currency_zone'] == zone]['market_id'].unique()
        
        for market1 in zone_markets:
            for market2 in zone_markets:
                if market1 >= market2:  # Avoid duplicates
                    continue
                
                # Get price series
                prices1 = df[df['market_id'] == market1].set_index('date')['log_price_usd_adjusted']
                prices2 = df[df['market_id'] == market2].set_index('date')['log_price_usd_adjusted']
                
                # Calculate correlation
                corr = prices1.corr(prices2)
                
                # Get network indices
                net1 = df[df['market_id'] == market1]['network_index'].iloc[0]
                net2 = df[df['market_id'] == market2]['network_index'].iloc[0]
                
                correlations.append({
                    'market1': market1,
                    'market2': market2,
                    'currency_zone': zone,
                    'price_correlation': corr,
                    'avg_network_index': (net1 + net2) / 2,
                    'network_difference': abs(net1 - net2)
                })
    
    corr_df = pd.DataFrame(correlations)
    
    # Test: Higher network integration → stronger price transmission
    from scipy.stats import pearsonr
    r, p = pearsonr(corr_df['avg_network_index'], corr_df['price_correlation'])
    
    return {
        'correlation_data': corr_df,
        'network_transmission_correlation': r,
        'p_value': p,
        'hypothesis_supported': r > 0 and p < 0.05
    }
```

## Integration Checklist

1. ✅ Calculate market reporting frequency from price data
2. ✅ Compute distances to distribution hubs using coordinates
3. ✅ Estimate infrastructure scores by governorate
4. ✅ Create composite network index
5. ✅ Test interaction effects with conflict
6. ✅ Analyze price transmission patterns

## Expected Results

If N1 is validated:
- Markets with higher network indices show:
  - More stable prices during conflict
  - Stronger price correlation with other markets
  - Faster price adjustment to shocks
- Network effects are stronger within currency zones than across

## Quick Implementation

```python
# Add to feature_engineering.py
from yemen_market.features.feature_engineering import FeatureEngineer

class NetworkFeatureEngineer(FeatureEngineer):
    def add_network_features(self, df):
        """Add network proxy features to dataset."""
        
        # Calculate all network measures
        network_df = create_network_composite_index(df)
        
        # Merge with main data
        df = df.merge(network_df, on='market_id', how='left')
        
        # Add time-varying network effects
        df['network_x_time'] = df['network_index'] * df['time_trend']
        
        return df

# Usage
engineer = NetworkFeatureEngineer()
enhanced_df = engineer.add_network_features(commodity_df)
results = test_network_moderation(enhanced_df)
```