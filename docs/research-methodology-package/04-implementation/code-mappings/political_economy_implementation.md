# Political Economy Implementation Guide (P1)

## Overview

This document provides implementation guidance for testing hypothesis P1: Seigniorage incentives prevent currency reunification, explaining the persistence of dual exchange rate systems.

## Core Calculations

### 1. Seigniorage Revenue Estimation

```python
def calculate_seigniorage_revenues():
    """
    Calculate seigniorage revenues for both currency zones.
    
    Based on IMF methodology: Seigniorage = Money Supply × Inflation Rate
    """
    
    # Northern Authority (Houthi-controlled)
    north_params = {
        'money_supply_yer': 500e9,     # M2 estimate in YER
        'inflation_rate': 0.05,        # 5% annual (controlled)
        'exchange_rate': 535,          # YER/USD
        'gdp_estimate_usd': 2e9        # ~$2 billion GDP estimate
    }
    
    north_seigniorage_yer = north_params['money_supply_yer'] * north_params['inflation_rate']
    north_seigniorage_usd = north_seigniorage_yer / north_params['exchange_rate']
    north_seigniorage_pct_gdp = north_seigniorage_usd / north_params['gdp_estimate_usd'] * 100
    
    # Southern Authority (Government-controlled)
    south_params = {
        'money_supply_yer': 1500e9,    # M2 estimate in YER
        'inflation_rate': 0.30,        # 30% annual (high depreciation)
        'exchange_rate': 2000,         # YER/USD
        'gdp_estimate_usd': 5e9        # ~$5 billion GDP estimate
    }
    
    south_seigniorage_yer = south_params['money_supply_yer'] * south_params['inflation_rate']
    south_seigniorage_usd = south_seigniorage_yer / south_params['exchange_rate']
    south_seigniorage_pct_gdp = south_seigniorage_usd / south_params['gdp_estimate_usd'] * 100
    
    return {
        'north': {
            'seigniorage_usd': north_seigniorage_usd,
            'seigniorage_pct_gdp': north_seigniorage_pct_gdp,
            'annual_revenue_usd': f"${north_seigniorage_usd/1e6:.1f} million"
        },
        'south': {
            'seigniorage_usd': south_seigniorage_usd,
            'seigniorage_pct_gdp': south_seigniorage_pct_gdp,
            'annual_revenue_usd': f"${south_seigniorage_usd/1e6:.1f} million"
        },
        'total_seigniorage_usd': north_seigniorage_usd + south_seigniorage_usd
    }
```

### 2. Economic Cost of Fragmentation

```python
def calculate_fragmentation_costs(df):
    """
    Estimate economic costs of market fragmentation due to dual currencies.
    """
    
    # Calculate trade reduction
    # Compare cross-zone vs within-zone trade volumes
    cross_zone_markets = df[df['currency_zone'] == 'contested']['market_id'].unique()
    within_zone_markets = df[df['currency_zone'] != 'contested']['market_id'].unique()
    
    # Price variance as proxy for market integration
    cross_zone_variance = df[df['market_id'].isin(cross_zone_markets)].groupby('commodity')['price_usd_adjusted'].var().mean()
    within_zone_variance = df[df['market_id'].isin(within_zone_markets)].groupby('commodity')['price_usd_adjusted'].var().mean()
    
    # Higher variance indicates less integration
    integration_loss = (cross_zone_variance / within_zone_variance - 1) * 100
    
    # Transaction cost increase (based on exchange rate spreads)
    avg_spread = df.groupby('currency_zone')['exchange_rate'].std().mean()
    transaction_cost_increase = avg_spread / df['exchange_rate'].mean() * 100
    
    # Welfare loss estimation
    total_trade_value = 10e9  # $10 billion estimate
    trade_reduction_pct = 0.40  # 40% reduction estimate
    welfare_loss = total_trade_value * trade_reduction_pct
    
    return {
        'integration_loss_pct': integration_loss,
        'transaction_cost_increase_pct': transaction_cost_increase,
        'estimated_welfare_loss_usd': welfare_loss,
        'welfare_loss_formatted': f"${welfare_loss/1e9:.1f} billion"
    }
```

### 3. Testing P1: Policy Correlation Analysis

```python
def test_political_economy_hypothesis(df):
    """
    Test P1: Policy decisions correlate with seigniorage maximization.
    """
    
    # Create monthly policy indicators
    policy_events = {
        '2021-01': 'north_currency_reform',
        '2021-06': 'south_import_restrictions',
        '2022-03': 'north_forex_controls',
        '2022-09': 'south_money_printing',
        '2023-01': 'north_rate_fixing',
        '2023-06': 'south_devaluation'
    }
    
    # Map policy events to data
    df['policy_event'] = df['year_month'].astype(str).map(policy_events).fillna('none')
    df['north_policy'] = df['policy_event'].str.contains('north').astype(int)
    df['south_policy'] = df['policy_event'].str.contains('south').astype(int)
    
    # Test correlation with exchange rate changes
    north_data = df[df['currency_zone'] == 'houthi'].copy()
    south_data = df[df['currency_zone'] == 'government'].copy()
    
    # Calculate exchange rate changes around policy events
    north_data['exchange_rate_change'] = north_data.groupby('market_id')['exchange_rate'].pct_change()
    south_data['exchange_rate_change'] = south_data.groupby('market_id')['exchange_rate'].pct_change()
    
    # Event study: Average effect of policy on exchange rates
    north_effect = north_data[north_data['north_policy'] == 1]['exchange_rate_change'].mean()
    south_effect = south_data[south_data['south_policy'] == 1]['exchange_rate_change'].mean()
    
    return {
        'north_policy_effect': north_effect,
        'south_policy_effect': south_effect,
        'hypothesis_supported': (
            abs(north_effect) < 0.05 and  # North maintains stability
            south_effect > 0.10            # South allows depreciation
        )
    }
```

### 4. Reunification Incentive Analysis

```python
def analyze_reunification_incentives():
    """
    Compare costs and benefits of currency reunification.
    """
    
    # Calculate revenues
    seigniorage = calculate_seigniorage_revenues()
    
    # Political economy benefits (beyond seigniorage)
    political_benefits = {
        'import_license_rents': 100e6,      # $100M from preferential rates
        'forex_allocation_rents': 150e6,    # $150M from controlled allocation
        'inflation_tax_on_opponents': 200e6  # $200M effective transfer
    }
    
    total_benefits = (
        seigniorage['north']['seigniorage_usd'] +
        seigniorage['south']['seigniorage_usd'] +
        sum(political_benefits.values())
    )
    
    # Economic costs
    economic_costs = {
        'welfare_loss': 650e6,              # Mid-point estimate
        'reduced_investment': 200e6,        # FDI reduction
        'financial_disintermediation': 150e6 # Banking sector losses
    }
    
    total_costs = sum(economic_costs.values())
    
    # Net political economy calculation
    net_benefit = total_benefits - total_costs
    
    return {
        'total_political_benefits_usd': total_benefits,
        'total_economic_costs_usd': total_costs,
        'net_benefit_usd': net_benefit,
        'reunification_rational': net_benefit < 0,
        'benefit_cost_ratio': total_benefits / total_costs,
        'summary': f"Political benefits (${total_benefits/1e6:.0f}M) {'exceed' if net_benefit > 0 else 'are less than'} economic costs (${total_costs/1e6:.0f}M)"
    }
```

### 5. Integration with Market Analysis

```python
def add_political_economy_features(df):
    """
    Add political economy indicators to market data.
    """
    
    # Seigniorage pressure indicator
    df['seigniorage_pressure'] = df['currency_zone'].map({
        'houthi': 0.05,      # Low pressure (stable rate)
        'government': 0.30,   # High pressure (depreciation)
        'contested': 0.15     # Medium pressure
    })
    
    # Policy uncertainty index
    df['policy_uncertainty'] = df.groupby(['currency_zone', 'year_month'])[
        'exchange_rate'
    ].transform('std') / df['exchange_rate']
    
    # Rent-seeking opportunity indicator
    df['rent_opportunity'] = (
        df['exchange_rate_differential'] * df['import_dependency']
    )
    
    return df
```

## Implementation Checklist

1. ✅ Calculate seigniorage revenues for both zones
2. ✅ Estimate economic costs of fragmentation
3. ✅ Test policy correlation with exchange rate changes
4. ✅ Analyze reunification incentives
5. ✅ Add political economy features to data

## Expected Results

If P1 is validated:
- Seigniorage revenues exceed 2% of GDP in both zones
- Political benefits outweigh economic costs
- Policy events correlate with revenue maximization
- No convergence in exchange rates over time

## Quick Implementation

```python
# Standalone analysis
results = {
    'seigniorage': calculate_seigniorage_revenues(),
    'fragmentation_costs': calculate_fragmentation_costs(commodity_df),
    'policy_test': test_political_economy_hypothesis(commodity_df),
    'reunification': analyze_reunification_incentives()
}

# Print key findings
print(f"North seigniorage: {results['seigniorage']['north']['annual_revenue_usd']}")
print(f"South seigniorage: {results['seigniorage']['south']['annual_revenue_usd']}")
print(f"Welfare loss: {results['fragmentation_costs']['welfare_loss_formatted']}")
print(f"Reunification rational: {results['reunification']['reunification_rational']}")
```

## Policy Implications

The analysis demonstrates why the dual currency system persists:
1. Both authorities gain significant revenue (2-5% of GDP)
2. Political benefits exceed economic costs
3. Reunification would require external guarantees/compensation
4. Aid policy must account for these incentives