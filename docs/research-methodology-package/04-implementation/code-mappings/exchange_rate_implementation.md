# Exchange Rate Mechanism Implementation Guide

## Overview

This document provides the implementation mapping for testing the core exchange rate mechanism hypothesis (H1) that explains negative price premiums in conflict zones through currency divergence.

## Core Discovery Recap

- **Houthi areas**: 535-539 YER/USD (stable, controlled)
- **Government areas**: 2,000-2,150 YER/USD (4x depreciation)
- **Hypothesis H1**: Negative premiums disappear when analyzing prices in USD terms

## Implementation Requirements

### 1. Currency Zone Variable Creation

```python
def assign_currency_zones(df):
    """Assign currency zones based on control areas."""
    
    HOUTHI_GOVERNORATES = [
        "Sana'a", "Sa'ada", "Hajjah", "Al Mahwit", "Dhamar",
        "Raymah", "Ibb", "Amran", "Al Hudaydah"
    ]
    
    GOVERNMENT_GOVERNORATES = [
        "Aden", "Lahj", "Abyan", "Shabwah", "Hadramaut",
        "Al Maharah", "Socotra", "Al Dhale'e", "<PERSON><PERSON>", "<PERSON> J<PERSON>f"
    ]
    
    # Create currency zone variable
    df['currency_zone'] = 'contested'  # Default
    df.loc[df['governorate'].isin(HOUTHI_GOVERNORATES), 'currency_zone'] = 'houthi'
    df.loc[df['governorate'].isin(GOVERNMENT_GOVERNORATES), 'currency_zone'] = 'government'
    
    return df
```

### 2. Exchange Rate Mapping

```python
def map_exchange_rates(df):
    """Map zone-specific exchange rates."""
    
    # Base rates (can be made time-varying)
    EXCHANGE_RATES = {
        'houthi': 535,      # Stable controlled rate
        'government': 2000,  # Depreciated market rate
        'contested': 1200    # Average for contested areas
    }
    
    # Map rates
    df['zone_exchange_rate'] = df['currency_zone'].map(EXCHANGE_RATES)
    
    # Calculate differential
    df['exchange_rate_differential'] = (
        df['zone_exchange_rate'] / EXCHANGE_RATES['houthi']
    )
    
    return df
```

### 3. Price Conversion with Zone Rates

```python
def convert_prices_by_zone(df):
    """Convert YER prices to USD using zone-specific rates."""
    
    # Ensure we have zone rates
    if 'zone_exchange_rate' not in df.columns:
        df = map_exchange_rates(df)
    
    # Convert prices
    df['price_usd_zone_adjusted'] = df['price_yer'] / df['zone_exchange_rate']
    
    # Also calculate using uniform rate for comparison
    df['price_usd_uniform'] = df['price_yer'] / 1000  # Arbitrary midpoint
    
    return df
```

### 4. Core H1 Test Implementation

```python
def test_exchange_rate_mechanism(df):
    """Test H1: Exchange rate mechanism explains price differentials."""
    
    from linearmodels import PanelOLS
    import numpy as np
    
    # Ensure proper panel structure
    df = df.set_index(['market_id', 'date'])
    
    # Log transform prices
    df['log_price_yer'] = np.log(df['price_yer'])
    df['log_price_usd_adjusted'] = np.log(df['price_usd_zone_adjusted'])
    
    # Model 1: YER prices (should show negative premium in conflict zones)
    model_yer = PanelOLS(
        dependent=df['log_price_yer'],
        exog=df[['currency_zone_houthi', 'conflict_intensity', 'global_price']],
        entity_effects=True,
        time_effects=True
    )
    results_yer = model_yer.fit(cov_type='clustered', cluster_entity=True)
    
    # Model 2: USD prices (premium should disappear)
    model_usd = PanelOLS(
        dependent=df['log_price_usd_adjusted'],
        exog=df[['currency_zone_houthi', 'conflict_intensity', 'global_price']],
        entity_effects=True,
        time_effects=True
    )
    results_usd = model_usd.fit(cov_type='clustered', cluster_entity=True)
    
    return {
        'yer_model': results_yer,
        'usd_model': results_usd,
        'h1_validated': abs(results_usd.params['currency_zone_houthi']) < 0.05
    }
```

### 5. Integration with Existing Pipeline

```python
# In WFPProcessor or new ExchangeRateProcessor
def enhance_with_currency_zones(self, commodity_df, exchange_df):
    """Enhance commodity data with currency zone information."""
    
    # Assign currency zones
    commodity_df = assign_currency_zones(commodity_df)
    
    # Map exchange rates
    commodity_df = map_exchange_rates(commodity_df)
    
    # Convert prices
    commodity_df = convert_prices_by_zone(commodity_df)
    
    # Add exchange rate volatility from actual data if available
    if not exchange_df.empty:
        volatility = exchange_df.groupby(['market_id', 'year_month'])[
            'exchange_rate'
        ].std().reset_index()
        volatility.columns = ['market_id', 'year_month', 'exchange_volatility']
        
        commodity_df = commodity_df.merge(
            volatility,
            on=['market_id', 'year_month'],
            how='left'
        )
    
    return commodity_df
```

### 6. Spatial Weights with Currency Adjustment

```python
def calculate_currency_adjusted_spatial_weights(df, theta=50):
    """Calculate spatial weights incorporating currency zones (S1)."""
    
    from scipy.spatial import distance_matrix
    
    # Get unique markets with coordinates
    markets = df[['market_id', 'lat', 'lon', 'currency_zone']].drop_duplicates()
    
    # Calculate distance matrix
    coords = markets[['lat', 'lon']].values
    distances = distance_matrix(coords, coords)
    
    # Create same zone indicator
    zones = markets['currency_zone'].values
    same_zone = zones[:, np.newaxis] == zones[np.newaxis, :]
    
    # Apply formula: W_ij = exp(-d_ij/θ) × I(same_zone)
    W = np.exp(-distances / theta) * same_zone
    
    # Normalize rows to sum to 1
    W = W / W.sum(axis=1, keepdims=True)
    
    return pd.DataFrame(W, index=markets['market_id'], columns=markets['market_id'])
```

## Testing Checklist

Before running the core H1 test, ensure:

1. ✅ Currency zones assigned to all markets
2. ✅ Exchange rates mapped (535 for Houthi, 2000+ for Government)
3. ✅ Prices converted using zone-specific rates
4. ✅ Panel structure properly set up
5. ✅ Control variables included (conflict, global prices)
6. ✅ Fixed effects for market and time
7. ✅ Clustered standard errors by market

## Expected Results

If H1 is validated:
- **YER Model**: Significant negative coefficient on `currency_zone_houthi`
- **USD Model**: Insignificant coefficient on `currency_zone_houthi` (near zero)
- **Interpretation**: Price differentials are driven by exchange rate divergence, not real economic factors

## Quick Implementation Path

For immediate testing in existing codebase:

```python
# Add to data_preparation.py or create new exchange_rate_utils.py
from yemen_market.data.wfp_processor import WFPProcessor

# Process data
processor = WFPProcessor()
commodity_df, exchange_df = processor.process_wfp_data('path/to/data.csv')

# Enhance with currency zones
commodity_df = assign_currency_zones(commodity_df)
commodity_df = map_exchange_rates(commodity_df)
commodity_df = convert_prices_by_zone(commodity_df)

# Run core test
results = test_exchange_rate_mechanism(commodity_df)

# Validate H1
if results['h1_validated']:
    print("H1 VALIDATED: Exchange rate mechanism explains price differentials")
else:
    print("H1 NOT VALIDATED: Other factors at play")
```

## Next Steps

1. Implement these functions in appropriate modules
2. Test with actual WFP data
3. Validate results match theoretical predictions
4. Extend to test H4 (currency zone switching) and H5 (cross-border arbitrage)
5. Document findings for Phase 2 methodology development