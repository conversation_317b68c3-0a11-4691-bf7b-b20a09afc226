# Factor Analysis Methodology

**Target Audience**: Econometricians, Machine Learning Practitioners  
**Module**: `yemen_market.models.three_tier.tier3_validation`

## Overview

This document details factor analysis and dimensionality reduction techniques used to validate the Yemen market integration models. Factor analysis helps identify latent structures in price movements and validates whether observed patterns align with economic theory.

## Factor Analysis Framework

### Principal Component Analysis (PCA)

```python
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
from sklearn.decomposition import PCA, FactorAnalysis
from sklearn.preprocessing import StandardScaler
import statsmodels.api as sm
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns
from factor_analyzer import FactorAnalyzer
from factor_analyzer.factor_analyzer import calculate_bartlett_sphericity, calculate_kmo

class PCAAnalysis:
    """
    Principal Component Analysis for market price data.
    
    Identifies common factors driving price movements across
    markets and commodities.
    """
    
    def __init__(self, panel_data: pd.DataFrame):
        """
        Initialize PCA analysis.
        
        Parameters
        ----------
        panel_data : DataFrame
            Panel data with prices
        """
        self.data = panel_data.copy()
        self.results = {}
        
    def analyze_price_components(
        self,
        commodity: str = None,
        n_components: int = None,
        min_variance_explained: float = 0.90
    ) -> Dict[str, any]:
        """
        Perform PCA on price data.
        
        Parameters
        ----------
        commodity : str, optional
            Specific commodity to analyze, or None for all
        n_components : int, optional
            Number of components to extract
        min_variance_explained : float
            Minimum cumulative variance to explain
        """
        # Prepare price matrix
        if commodity:
            price_data = self.data[self.data['commodity'] == commodity]
            price_matrix = price_data.pivot(
                index='date',
                columns='market_id',
                values='log_price'
            )
        else:
            # Stack all commodities
            price_matrix = self._create_stacked_price_matrix()
        
        # Handle missing values
        price_matrix_filled = self._handle_missing_for_pca(price_matrix)
        
        # Test suitability for factor analysis
        suitability = self._test_factorability(price_matrix_filled)
        
        if not suitability['suitable']:
            return {
                'error': 'Data not suitable for factor analysis',
                'suitability_tests': suitability
            }
        
        # Standardize data
        scaler = StandardScaler()
        price_scaled = scaler.fit_transform(price_matrix_filled)
        
        # Perform PCA
        if n_components is None:
            # Determine optimal number of components
            n_components = self._determine_n_components(
                price_scaled,
                min_variance_explained
            )
        
        pca = PCA(n_components=n_components, random_state=42)
        components = pca.fit_transform(price_scaled)
        
        # Analyze results
        results = {
            'pca_model': pca,
            'components': components,
            'loadings': self._calculate_loadings(pca, price_matrix_filled.columns),
            'variance_explained': pca.explained_variance_ratio_,
            'cumulative_variance': np.cumsum(pca.explained_variance_ratio_),
            'n_components': n_components,
            'interpretation': self._interpret_components(
                pca, price_matrix_filled.columns
            ),
            'market_contributions': self._analyze_market_contributions(
                pca, price_matrix_filled.columns
            ),
            'time_series': self._create_component_time_series(
                components, price_matrix_filled.index
            ),
            'suitability_tests': suitability
        }
        
        # Test economic interpretability
        results['economic_validation'] = self._validate_economic_interpretation(
            results
        )
        
        return results
    
    def _test_factorability(self, data: pd.DataFrame) -> Dict[str, any]:
        """Test if data is suitable for factor analysis."""
        # Convert to numpy array
        data_array = data.values
        
        # Bartlett's test of sphericity
        try:
            chi_square, p_value = calculate_bartlett_sphericity(data_array)
            bartlett_suitable = p_value < 0.05
        except:
            chi_square, p_value = np.nan, np.nan
            bartlett_suitable = False
        
        # Kaiser-Meyer-Olkin (KMO) test
        try:
            kmo_all, kmo_model = calculate_kmo(data_array)
            kmo_suitable = kmo_model > 0.6
        except:
            kmo_model = np.nan
            kmo_suitable = False
        
        # Correlation matrix determinant
        corr_matrix = data.corr()
        det = np.linalg.det(corr_matrix)
        det_suitable = det > 0.00001
        
        return {
            'suitable': bartlett_suitable and kmo_suitable and det_suitable,
            'bartlett_test': {
                'chi_square': chi_square,
                'p_value': p_value,
                'suitable': bartlett_suitable
            },
            'kmo_test': {
                'kmo': kmo_model,
                'suitable': kmo_suitable,
                'interpretation': self._interpret_kmo(kmo_model)
            },
            'determinant': {
                'value': det,
                'suitable': det_suitable
            }
        }
    
    def _interpret_kmo(self, kmo: float) -> str:
        """Interpret KMO value."""
        if np.isnan(kmo):
            return "Unable to calculate"
        elif kmo >= 0.9:
            return "Marvelous"
        elif kmo >= 0.8:
            return "Meritorious"
        elif kmo >= 0.7:
            return "Middling"
        elif kmo >= 0.6:
            return "Mediocre"
        elif kmo >= 0.5:
            return "Miserable"
        else:
            return "Unacceptable"
    
    def _determine_n_components(
        self,
        data_scaled: np.ndarray,
        min_variance: float
    ) -> int:
        """Determine optimal number of components."""
        # Full PCA
        pca_full = PCA(random_state=42)
        pca_full.fit(data_scaled)
        
        # Cumulative variance
        cumsum = np.cumsum(pca_full.explained_variance_ratio_)
        
        # Find number of components for min variance
        n_components = np.argmax(cumsum >= min_variance) + 1
        
        # Also check Kaiser criterion (eigenvalue > 1)
        eigenvalues = pca_full.explained_variance_
        n_kaiser = np.sum(eigenvalues > 1)
        
        # Use minimum of both criteria
        return min(n_components, n_kaiser)
    
    def _calculate_loadings(
        self,
        pca: PCA,
        variable_names: List[str]
    ) -> pd.DataFrame:
        """Calculate and format component loadings."""
        # Loadings = eigenvectors * sqrt(eigenvalues)
        loadings = pca.components_.T * np.sqrt(pca.explained_variance_)
        
        # Create DataFrame
        loadings_df = pd.DataFrame(
            loadings,
            index=variable_names,
            columns=[f'PC{i+1}' for i in range(pca.n_components_)]
        )
        
        return loadings_df
    
    def _interpret_components(
        self,
        pca: PCA,
        variable_names: List[str]
    ) -> Dict[str, Dict]:
        """Interpret principal components economically."""
        loadings_df = self._calculate_loadings(pca, variable_names)
        interpretations = {}
        
        for i in range(pca.n_components_):
            pc_name = f'PC{i+1}'
            loadings = loadings_df[pc_name]
            
            # Find dominant loadings
            abs_loadings = np.abs(loadings)
            threshold = abs_loadings.mean() + abs_loadings.std()
            dominant = abs_loadings[abs_loadings > threshold].sort_values(ascending=False)
            
            # Analyze pattern
            interpretation = self._interpret_loading_pattern(
                loadings[dominant.index],
                variable_names
            )
            
            interpretations[pc_name] = {
                'variance_explained': pca.explained_variance_ratio_[i],
                'dominant_variables': dominant.index.tolist(),
                'dominant_loadings': dominant.to_dict(),
                'interpretation': interpretation,
                'all_loadings': loadings.to_dict()
            }
        
        return interpretations
    
    def _interpret_loading_pattern(
        self,
        loadings: pd.Series,
        variable_names: List[str]
    ) -> str:
        """Interpret economic meaning of loading pattern."""
        # Check if all loadings have same sign (common factor)
        if (loadings > 0).all():
            return "Common positive factor (e.g., general price level)"
        elif (loadings < 0).all():
            return "Common negative factor (unusual)"
        
        # Check for geographic patterns
        if any('capital' in var.lower() for var in loadings.index):
            if loadings[loadings.index.str.contains('capital', case=False)].mean() > 0:
                return "Capital city premium factor"
        
        # Check for commodity patterns
        commodities = [var.split('_')[-1] for var in loadings.index if '_' in var]
        if commodities:
            commodity_signs = {c: [] for c in set(commodities)}
            for var, load in loadings.items():
                if '_' in var:
                    commodity = var.split('_')[-1]
                    if commodity in commodity_signs:
                        commodity_signs[commodity].append(load)
            
            # Check if commodities load differently
            avg_signs = {c: np.mean(signs) for c, signs in commodity_signs.items() if signs}
            if len(set(np.sign(list(avg_signs.values())))) > 1:
                return "Commodity-specific factor (different effects by commodity)"
        
        # Check for urban/rural pattern
        urban_markets = [var for var in loadings.index if 'urban' in str(var).lower()]
        rural_markets = [var for var in loadings.index if 'rural' in str(var).lower()]
        
        if urban_markets and rural_markets:
            urban_avg = loadings[urban_markets].mean()
            rural_avg = loadings[rural_markets].mean()
            if np.sign(urban_avg) != np.sign(rural_avg):
                return "Urban-rural differentiation factor"
        
        # Default interpretation based on loading magnitudes
        positive_pct = (loadings > 0).mean()
        if positive_pct > 0.7:
            return "Mostly positive factor (price increase pressure)"
        elif positive_pct < 0.3:
            return "Mostly negative factor (price decrease pressure)"
        else:
            return "Mixed factor (market segmentation)"
    
    def _validate_economic_interpretation(
        self,
        pca_results: Dict
    ) -> Dict[str, any]:
        """Validate if PCA results align with economic theory."""
        validations = {}
        
        # Test 1: First component should represent general price level
        pc1_loadings = pca_results['loadings']['PC1']
        pc1_positive_pct = (pc1_loadings > 0).mean()
        validations['pc1_is_price_level'] = pc1_positive_pct > 0.8
        
        # Test 2: Components should be stationary (prices are I(1), components should be I(0))
        components_df = pd.DataFrame(
            pca_results['components'],
            columns=[f'PC{i+1}' for i in range(pca_results['n_components'])]
        )
        
        stationarity_tests = {}
        for col in components_df.columns:
            # ADF test
            adf_result = stats.adfuller(components_df[col].dropna())
            stationarity_tests[col] = {
                'adf_statistic': adf_result[0],
                'p_value': adf_result[1],
                'is_stationary': adf_result[1] < 0.05
            }
        
        validations['components_stationary'] = stationarity_tests
        validations['all_stationary'] = all(
            test['is_stationary'] for test in stationarity_tests.values()
        )
        
        # Test 3: Components should relate to economic fundamentals
        if 'conflict_intensity' in self.data.columns:
            conflict_correlations = self._test_component_correlations(
                components_df,
                self.data.groupby('date')['conflict_intensity'].mean()
            )
            validations['conflict_correlations'] = conflict_correlations
        
        return validations
    
    def analyze_market_integration_factors(
        self,
        method: str = 'pca'
    ) -> Dict[str, any]:
        """
        Analyze factors driving market integration.
        
        Uses price differentials to identify integration patterns.
        """
        # Create price differential matrix
        price_diffs = self._create_price_differential_matrix()
        
        if method == 'pca':
            # PCA on price differentials
            results = self.analyze_price_components(
                data_override=price_diffs,
                n_components=5
            )
        else:
            # Factor analysis for latent variables
            results = self._perform_factor_analysis(price_diffs)
        
        # Relate factors to market characteristics
        results['market_characteristics'] = self._relate_factors_to_characteristics(
            results
        )
        
        return results
```

### Dynamic Factor Models

```python
class DynamicFactorModel:
    """
    Dynamic Factor Model for time-varying market integration.
    
    Allows factors and loadings to evolve over time.
    """
    
    def __init__(self, panel_data: pd.DataFrame):
        """Initialize dynamic factor model."""
        self.data = panel_data
        self.results = {}
        
    def estimate_dynamic_factors(
        self,
        n_factors: int = 3,
        n_lags: int = 1,
        window_size: int = 36
    ) -> Dict[str, any]:
        """
        Estimate dynamic factor model using rolling windows.
        
        Parameters
        ----------
        n_factors : int
            Number of dynamic factors
        n_lags : int
            Number of lags in factor dynamics
        window_size : int
            Rolling window size (months)
        """
        # Prepare data
        price_matrix = self.data.pivot(
            index='date',
            columns=['market_id', 'commodity'],
            values='log_price'
        )
        
        # Rolling estimation
        rolling_results = []
        dates = price_matrix.index[window_size:]
        
        for i, end_date in enumerate(dates):
            start_idx = i
            end_idx = i + window_size
            
            window_data = price_matrix.iloc[start_idx:end_idx]
            
            # Estimate factors for this window
            window_result = self._estimate_window_factors(
                window_data,
                n_factors,
                n_lags
            )
            
            window_result['date'] = end_date
            rolling_results.append(window_result)
        
        # Compile results
        results = {
            'rolling_factors': self._compile_rolling_factors(rolling_results),
            'time_varying_loadings': self._extract_time_varying_loadings(rolling_results),
            'factor_stability': self._test_factor_stability(rolling_results),
            'integration_index': self._calculate_integration_index(rolling_results),
            'structural_breaks': self._detect_structural_breaks(rolling_results)
        }
        
        return results
    
    def _estimate_window_factors(
        self,
        window_data: pd.DataFrame,
        n_factors: int,
        n_lags: int
    ) -> Dict[str, any]:
        """Estimate factors for a single window."""
        # Handle missing values
        window_filled = window_data.fillna(method='ffill').fillna(method='bfill')
        
        # Standardize
        scaler = StandardScaler()
        data_scaled = scaler.fit_transform(window_filled)
        
        # Extract factors using PCA
        pca = PCA(n_components=n_factors, random_state=42)
        factors = pca.fit_transform(data_scaled)
        
        # Estimate factor dynamics (VAR model)
        if n_lags > 0 and len(factors) > n_lags + 5:
            factor_df = pd.DataFrame(
                factors,
                columns=[f'F{i+1}' for i in range(n_factors)]
            )
            
            # Simple VAR estimation
            var_results = self._estimate_var(factor_df, n_lags)
        else:
            var_results = None
        
        return {
            'factors': factors,
            'loadings': pca.components_,
            'variance_explained': pca.explained_variance_ratio_,
            'var_results': var_results,
            'n_obs': len(window_data)
        }
    
    def _estimate_var(self, factors: pd.DataFrame, n_lags: int) -> Dict:
        """Estimate VAR model for factor dynamics."""
        from statsmodels.tsa.vector_ar.var_model import VAR
        
        try:
            model = VAR(factors)
            results = model.fit(maxlags=n_lags)
            
            return {
                'coefficients': results.params,
                'stability': self._check_var_stability(results),
                'granger_causality': self._test_granger_causality(results),
                'irf': results.irf(10)  # 10-period impulse responses
            }
        except:
            return None
    
    def _calculate_integration_index(
        self,
        rolling_results: List[Dict]
    ) -> pd.DataFrame:
        """
        Calculate time-varying market integration index.
        
        Based on proportion of variance explained by common factors.
        """
        integration_scores = []
        
        for result in rolling_results:
            # Integration = variance explained by first k factors
            var_explained = result['variance_explained']
            
            # Different measures
            scores = {
                'date': result['date'],
                'pc1_dominance': var_explained[0],  # First PC dominance
                'common_variance': sum(var_explained[:2]),  # First 2 PCs
                'concentration': self._calculate_herfindahl(var_explained),
                'n_factors_80pct': self._n_factors_for_variance(var_explained, 0.8)
            }
            
            integration_scores.append(scores)
        
        integration_df = pd.DataFrame(integration_scores)
        
        # Add trend and smoothed series
        integration_df['trend'] = self._extract_trend(
            integration_df['common_variance']
        )
        
        integration_df['smoothed'] = integration_df['common_variance'].rolling(
            window=3, center=True
        ).mean()
        
        return integration_df
    
    def _calculate_herfindahl(self, variance_explained: np.ndarray) -> float:
        """Calculate Herfindahl index of variance concentration."""
        return np.sum(variance_explained ** 2)
    
    def _n_factors_for_variance(
        self,
        variance_explained: np.ndarray,
        threshold: float
    ) -> int:
        """Number of factors needed to explain threshold variance."""
        cumsum = np.cumsum(variance_explained)
        return np.argmax(cumsum >= threshold) + 1 if any(cumsum >= threshold) else len(variance_explained)
```

### Factor Model Validation

```python
class FactorModelValidation:
    """Validate factor models for economic interpretability."""
    
    def __init__(self, factor_results: Dict[str, any]):
        """Initialize validation with factor analysis results."""
        self.results = factor_results
        
    def validate_factor_structure(self) -> Dict[str, any]:
        """
        Comprehensive validation of factor structure.
        
        Tests if factors align with economic theory.
        """
        validations = {
            'statistical_tests': self._statistical_validation(),
            'economic_tests': self._economic_validation(),
            'stability_tests': self._stability_validation(),
            'predictive_tests': self._predictive_validation()
        }
        
        # Overall assessment
        validations['overall_validity'] = self._assess_overall_validity(validations)
        
        return validations
    
    def _statistical_validation(self) -> Dict[str, any]:
        """Statistical tests for factor model validity."""
        tests = {}
        
        # 1. Sampling adequacy (already done in PCA)
        tests['sampling_adequacy'] = self.results.get('suitability_tests', {})
        
        # 2. Factor correlation matrix
        if 'components' in self.results:
            factors = self.results['components']
            factor_corr = np.corrcoef(factors.T)
            
            # Should be close to identity (orthogonal factors)
            off_diagonal = factor_corr[np.triu_indices_from(factor_corr, k=1)]
            tests['factor_orthogonality'] = {
                'max_correlation': np.max(np.abs(off_diagonal)),
                'mean_correlation': np.mean(np.abs(off_diagonal)),
                'orthogonal': np.max(np.abs(off_diagonal)) < 0.3
            }
        
        # 3. Residual analysis
        if 'loadings' in self.results and 'components' in self.results:
            # Reconstruct data
            loadings = self.results['loadings'].values
            factors = self.results['components']
            reconstructed = factors @ loadings.T
            
            # Calculate residuals (would need original data)
            # This is simplified
            tests['reconstruction_quality'] = {
                'variance_explained': self.results['cumulative_variance'][-1],
                'adequate': self.results['cumulative_variance'][-1] > 0.7
            }
        
        return tests
    
    def _economic_validation(self) -> Dict[str, any]:
        """Test economic interpretability of factors."""
        tests = {}
        
        # 1. Law of One Price test
        # First factor should represent common price movements
        if 'PC1' in self.results.get('interpretation', {}):
            pc1_interp = self.results['interpretation']['PC1']
            tests['law_of_one_price'] = {
                'pc1_variance': pc1_interp['variance_explained'],
                'common_movement': 'common' in pc1_interp['interpretation'].lower(),
                'consistent': pc1_interp['variance_explained'] > 0.3
            }
        
        # 2. Geographic patterns
        # Should find factors related to distance/location
        geographic_factors = []
        for pc, interp in self.results.get('interpretation', {}).items():
            if any(word in interp['interpretation'].lower() 
                   for word in ['geographic', 'spatial', 'regional', 'urban', 'rural']):
                geographic_factors.append(pc)
        
        tests['geographic_structure'] = {
            'found': len(geographic_factors) > 0,
            'factors': geographic_factors
        }
        
        # 3. Commodity patterns
        commodity_factors = []
        for pc, interp in self.results.get('interpretation', {}).items():
            if 'commodity' in interp['interpretation'].lower():
                commodity_factors.append(pc)
        
        tests['commodity_structure'] = {
            'found': len(commodity_factors) > 0,
            'factors': commodity_factors
        }
        
        # 4. Integration with conflict
        if 'conflict_correlations' in self.results.get('economic_validation', {}):
            conflict_corrs = self.results['economic_validation']['conflict_correlations']
            
            # At least one factor should relate to conflict
            max_corr = max(abs(corr) for corr in conflict_corrs.values())
            tests['conflict_relevance'] = {
                'max_correlation': max_corr,
                'significant': max_corr > 0.3
            }
        
        return tests
    
    def _stability_validation(self) -> Dict[str, any]:
        """Test stability of factor structure over time."""
        tests = {}
        
        if 'factor_stability' in self.results:
            stability = self.results['factor_stability']
            
            # Loading stability
            if 'loading_changes' in stability:
                tests['loading_stability'] = {
                    'mean_change': np.mean(stability['loading_changes']),
                    'max_change': np.max(stability['loading_changes']),
                    'stable': np.max(stability['loading_changes']) < 0.3
                }
            
            # Number of factors stability
            if 'n_factors_over_time' in stability:
                n_factors = stability['n_factors_over_time']
                tests['dimension_stability'] = {
                    'std_dev': np.std(n_factors),
                    'range': np.ptp(n_factors),
                    'stable': np.std(n_factors) < 1
                }
        
        return tests
    
    def _predictive_validation(self) -> Dict[str, any]:
        """Test predictive validity of factors."""
        tests = {}
        
        # Factors should help predict future price movements
        if 'components' in self.results:
            factors = pd.DataFrame(
                self.results['components'],
                columns=[f'PC{i+1}' for i in range(self.results['n_components'])]
            )
            
            # Simple predictive test (would need price data)
            # This is a placeholder for the concept
            tests['predictive_power'] = {
                'method': 'out-of-sample forecast',
                'metric': 'RMSE reduction',
                'improvement': 'Not calculated'  # Would need actual implementation
            }
        
        return tests
```

### Visualization Methods

```python
class FactorVisualization:
    """Visualization tools for factor analysis results."""
    
    def __init__(self):
        """Initialize visualization settings."""
        self.fig_size = (15, 10)
        
    def plot_factor_analysis_results(
        self,
        factor_results: Dict[str, any],
        save_path: str = None
    ) -> plt.Figure:
        """Comprehensive visualization of factor analysis."""
        fig = plt.figure(figsize=(20, 15))
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # 1. Scree plot
        ax1 = fig.add_subplot(gs[0, 0])
        self._plot_scree(factor_results, ax1)
        
        # 2. Loading heatmap
        ax2 = fig.add_subplot(gs[0, 1:])
        self._plot_loading_heatmap(factor_results, ax2)
        
        # 3. Factor time series
        ax3 = fig.add_subplot(gs[1, :])
        self._plot_factor_time_series(factor_results, ax3)
        
        # 4. Biplot
        ax4 = fig.add_subplot(gs[2, 0])
        self._plot_biplot(factor_results, ax4)
        
        # 5. Factor correlation
        ax5 = fig.add_subplot(gs[2, 1])
        self._plot_factor_correlation(factor_results, ax5)
        
        # 6. Integration index
        ax6 = fig.add_subplot(gs[2, 2])
        self._plot_integration_index(factor_results, ax6)
        
        plt.suptitle('Factor Analysis Results', fontsize=16)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def _plot_scree(self, results: Dict, ax: plt.Axes):
        """Scree plot showing variance explained."""
        var_explained = results['variance_explained']
        cumvar = results['cumulative_variance']
        
        x = range(1, len(var_explained) + 1)
        
        # Individual variance
        ax.bar(x, var_explained, alpha=0.7, label='Individual')
        
        # Cumulative variance
        ax2 = ax.twinx()
        ax2.plot(x, cumvar, 'r-o', label='Cumulative')
        ax2.set_ylabel('Cumulative Variance Explained', color='r')
        ax2.tick_params(axis='y', labelcolor='r')
        
        # Add threshold lines
        ax2.axhline(y=0.8, color='g', linestyle='--', alpha=0.5)
        ax2.axhline(y=0.9, color='g', linestyle='--', alpha=0.5)
        
        ax.set_xlabel('Principal Component')
        ax.set_ylabel('Variance Explained', color='b')
        ax.tick_params(axis='y', labelcolor='b')
        ax.set_title('Scree Plot')
        ax.set_xticks(x)
        
        # Add Kaiser criterion line
        ax.axhline(y=1/len(var_explained), color='orange', linestyle=':', 
                   label='Kaiser threshold')
        
        ax.legend(loc='upper left')
        ax2.legend(loc='upper right')
    
    def _plot_loading_heatmap(self, results: Dict, ax: plt.Axes):
        """Heatmap of factor loadings."""
        loadings = results['loadings']
        
        # Sort variables by first component loading
        sorted_idx = loadings['PC1'].abs().sort_values(ascending=False).index
        loadings_sorted = loadings.loc[sorted_idx]
        
        # Plot heatmap
        sns.heatmap(
            loadings_sorted.T,
            cmap='RdBu_r',
            center=0,
            cbar_kws={'label': 'Loading'},
            ax=ax,
            vmin=-1,
            vmax=1
        )
        
        ax.set_xlabel('Variables')
        ax.set_ylabel('Components')
        ax.set_title('Factor Loadings Heatmap')
        
        # Rotate x labels if many variables
        if len(loadings) > 20:
            ax.set_xticklabels(ax.get_xticklabels(), rotation=90, ha='center')
    
    def _plot_factor_time_series(self, results: Dict, ax: plt.Axes):
        """Plot factor scores over time."""
        if 'time_series' not in results:
            ax.text(0.5, 0.5, 'No time series data', 
                   transform=ax.transAxes, ha='center')
            return
        
        time_series = results['time_series']
        
        # Plot first 3 components
        n_components = min(3, len(time_series.columns))
        
        for i in range(n_components):
            pc = f'PC{i+1}'
            if pc in time_series.columns:
                ax.plot(
                    time_series.index,
                    time_series[pc],
                    label=f'{pc} ({results["variance_explained"][i]:.1%} var)',
                    linewidth=2
                )
        
        # Add events or regime changes if available
        ax.set_xlabel('Date')
        ax.set_ylabel('Factor Score')
        ax.set_title('Principal Component Time Series')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Add zero line
        ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    
    def _plot_biplot(self, results: Dict, ax: plt.Axes):
        """Biplot showing variables and observations in PC space."""
        loadings = results['loadings']
        scores = results['components']
        
        # Use first two components
        pc1_scores = scores[:, 0]
        pc2_scores = scores[:, 1]
        
        # Plot observations (subsample if too many)
        n_obs = len(pc1_scores)
        if n_obs > 100:
            idx = np.random.choice(n_obs, 100, replace=False)
            ax.scatter(pc1_scores[idx], pc2_scores[idx], alpha=0.5, s=20)
        else:
            ax.scatter(pc1_scores, pc2_scores, alpha=0.5, s=20)
        
        # Plot variable vectors
        scale = 3  # Scale factor for visibility
        
        for var in loadings.index[:10]:  # Top 10 variables
            x = loadings.loc[var, 'PC1'] * scale
            y = loadings.loc[var, 'PC2'] * scale
            
            ax.arrow(0, 0, x, y, 
                    head_width=0.05, head_length=0.05,
                    fc='red', ec='red', alpha=0.7)
            
            ax.text(x*1.1, y*1.1, var, 
                   fontsize=8, ha='center', va='center')
        
        ax.set_xlabel(f'PC1 ({results["variance_explained"][0]:.1%})')
        ax.set_ylabel(f'PC2 ({results["variance_explained"][1]:.1%})')
        ax.set_title('Biplot')
        ax.grid(True, alpha=0.3)
        
        # Add origin lines
        ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        ax.axvline(x=0, color='k', linestyle='-', alpha=0.3)
```

### Applied Factor Analysis

```python
def analyze_market_integration_factors(
    panel_data: pd.DataFrame,
    conflict_data: pd.DataFrame = None
) -> Dict[str, any]:
    """
    Complete factor analysis of market integration.
    
    Combines static and dynamic approaches.
    """
    results = {}
    
    # 1. Static PCA
    print("Performing static PCA...")
    pca_analyzer = PCAAnalysis(panel_data)
    static_results = pca_analyzer.analyze_price_components()
    results['static_pca'] = static_results
    
    # 2. Commodity-specific analysis
    print("Analyzing commodity-specific factors...")
    commodity_results = {}
    
    for commodity in ['wheat', 'rice_imported', 'sugar']:
        if commodity in panel_data['commodity'].values:
            commodity_pca = pca_analyzer.analyze_price_components(
                commodity=commodity
            )
            commodity_results[commodity] = commodity_pca
    
    results['commodity_factors'] = commodity_results
    
    # 3. Dynamic factor model
    print("Estimating dynamic factors...")
    dfm = DynamicFactorModel(panel_data)
    dynamic_results = dfm.estimate_dynamic_factors()
    results['dynamic_factors'] = dynamic_results
    
    # 4. Integration with conflict
    if conflict_data is not None:
        print("Analyzing conflict interaction...")
        results['conflict_factors'] = analyze_conflict_factor_interaction(
            static_results,
            conflict_data
        )
    
    # 5. Validation
    print("Validating factor structure...")
    validator = FactorModelValidation(static_results)
    validation_results = validator.validate_factor_structure()
    results['validation'] = validation_results
    
    # 6. Create summary report
    results['summary'] = create_factor_summary(results)
    
    return results

def analyze_conflict_factor_interaction(
    factor_results: Dict,
    conflict_data: pd.DataFrame
) -> Dict[str, any]:
    """Analyze how factors relate to conflict dynamics."""
    if 'components' not in factor_results:
        return {'error': 'No factor scores available'}
    
    # Get factor scores
    factor_df = pd.DataFrame(
        factor_results['components'],
        columns=[f'PC{i+1}' for i in range(factor_results['n_components'])]
    )
    
    # Add dates from time series
    if 'time_series' in factor_results:
        factor_df.index = factor_results['time_series'].index
    
    # Merge with conflict data
    conflict_avg = conflict_data.groupby('date')['conflict_intensity'].mean()
    
    analysis = {}
    
    # Correlations
    correlations = {}
    for col in factor_df.columns:
        if len(factor_df[col]) == len(conflict_avg):
            corr = factor_df[col].corr(conflict_avg)
            correlations[col] = corr
    
    analysis['correlations'] = correlations
    
    # Granger causality tests
    granger_results = {}
    for col in factor_df.columns[:3]:  # First 3 PCs
        try:
            # Test both directions
            gc_result = test_granger_causality(
                factor_df[col],
                conflict_avg,
                maxlag=4
            )
            granger_results[col] = gc_result
        except:
            continue
    
    analysis['granger_causality'] = granger_results
    
    # Identify conflict-sensitive factors
    sensitive_factors = [
        factor for factor, corr in correlations.items()
        if abs(corr) > 0.3
    ]
    
    analysis['conflict_sensitive_factors'] = sensitive_factors
    
    return analysis

def create_factor_summary(results: Dict) -> Dict[str, any]:
    """Create executive summary of factor analysis."""
    summary = {
        'main_findings': [],
        'integration_assessment': '',
        'policy_implications': []
    }
    
    # Main findings
    if 'static_pca' in results:
        static = results['static_pca']
        n_factors = static['n_components']
        var_explained = static['cumulative_variance'][-1]
        
        summary['main_findings'].append(
            f"{n_factors} factors explain {var_explained:.1%} of price variance"
        )
        
        # First component interpretation
        if 'interpretation' in static:
            pc1_interp = static['interpretation']['PC1']['interpretation']
            summary['main_findings'].append(
                f"Primary factor represents: {pc1_interp}"
            )
    
    # Integration assessment
    if 'dynamic_factors' in results:
        integration_index = results['dynamic_factors'].get('integration_index')
        if integration_index is not None:
            recent_integration = integration_index['common_variance'].iloc[-6:].mean()
            trend = 'increasing' if integration_index['trend'].iloc[-1] > integration_index['trend'].iloc[-12] else 'decreasing'
            
            summary['integration_assessment'] = (
                f"Market integration is {trend}. "
                f"Common factors explain {recent_integration:.1%} of price variance."
            )
    
    # Policy implications
    if 'validation' in results:
        validation = results['validation']
        
        if validation.get('overall_validity', False):
            summary['policy_implications'].append(
                "Factor structure supports targeted interventions by market segment"
            )
        
        if 'economic_tests' in validation:
            if validation['economic_tests'].get('geographic_structure', {}).get('found'):
                summary['policy_implications'].append(
                    "Geographic factors suggest need for region-specific policies"
                )
            
            if validation['economic_tests'].get('commodity_structure', {}).get('found'):
                summary['policy_implications'].append(
                    "Commodity-specific factors indicate differentiated market dynamics"
                )
    
    return summary
```

## See Also

- [Cross-Validation](cross-validation.md) - Model validation framework
- [Conflict Validation](conflict-validation.md) - Conflict-specific analysis
- [PCA Implementation](../../03-api-reference/models/three_tier/tier3_validation/pca_analysis.md)
- [Time Series Models](../econometric-models/time-series.md) - Dynamic modeling