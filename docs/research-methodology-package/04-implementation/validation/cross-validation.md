# Cross-Validation Methodology

**Target Audience**: Econometricians, Machine Learning Practitioners  
**Module**: `yemen_market.models.three_tier.integration`

## Overview

This document details cross-validation techniques adapted for panel data and time series econometric models in the Yemen Market Integration analysis. Standard cross-validation must be modified to respect the temporal and spatial structure of the data.

## Panel Data Cross-Validation

### Time Series Split Strategy

```python
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Iterator
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass
import warnings

@dataclass
class CVResults:
    """Container for cross-validation results."""
    train_scores: List[float]
    val_scores: List[float]
    test_scores: List[float]
    predictions: Dict[str, np.ndarray]
    coefficients: Dict[str, pd.DataFrame]
    fold_info: List[Dict]
    
class PanelCrossValidator:
    """
    Cross-validation for panel data models.
    
    Handles temporal dependencies and panel structure.
    """
    
    def __init__(
        self,
        n_splits: int = 5,
        test_size: int = 12,
        gap: int = 1,
        strategy: str = 'expanding'
    ):
        """
        Initialize panel cross-validator.
        
        Parameters
        ----------
        n_splits : int
            Number of CV folds
        test_size : int
            Size of test set (months)
        gap : int
            Gap between train and test (months)
        strategy : str
            'expanding': Expanding window
            'sliding': Sliding window
            'blocked': Block time series split
        """
        self.n_splits = n_splits
        self.test_size = test_size
        self.gap = gap
        self.strategy = strategy
        
    def split(
        self,
        data: pd.DataFrame,
        entity_col: str = 'entity',
        time_col: str = 'date'
    ) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """
        Generate train/test indices for panel data.
        
        Ensures no data leakage across time.
        """
        # Get unique time periods
        time_periods = sorted(data[time_col].unique())
        n_periods = len(time_periods)
        
        if self.strategy == 'expanding':
            yield from self._expanding_window_split(
                data, time_periods, entity_col, time_col
            )
        elif self.strategy == 'sliding':
            yield from self._sliding_window_split(
                data, time_periods, entity_col, time_col
            )
        elif self.strategy == 'blocked':
            yield from self._blocked_time_split(
                data, time_periods, entity_col, time_col
            )
        else:
            raise ValueError(f"Unknown strategy: {self.strategy}")
    
    def _expanding_window_split(
        self,
        data: pd.DataFrame,
        time_periods: List,
        entity_col: str,
        time_col: str
    ) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """Expanding window cross-validation."""
        n_periods = len(time_periods)
        
        # Minimum training size
        min_train_size = max(24, n_periods // (self.n_splits + 1))
        
        for i in range(self.n_splits):
            # Calculate split points
            test_end_idx = n_periods - i * self.test_size
            test_start_idx = test_end_idx - self.test_size
            train_end_idx = test_start_idx - self.gap
            
            if train_end_idx < min_train_size:
                break
            
            # Get time periods for train and test
            train_periods = time_periods[:train_end_idx]
            test_periods = time_periods[test_start_idx:test_end_idx]
            
            # Get indices
            train_mask = data[time_col].isin(train_periods)
            test_mask = data[time_col].isin(test_periods)
            
            train_idx = np.where(train_mask)[0]
            test_idx = np.where(test_mask)[0]
            
            yield train_idx, test_idx
    
    def _sliding_window_split(
        self,
        data: pd.DataFrame,
        time_periods: List,
        entity_col: str,
        time_col: str
    ) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """Sliding window cross-validation."""
        n_periods = len(time_periods)
        
        # Fixed training window size
        train_size = max(24, (n_periods - self.test_size) // 2)
        
        for i in range(self.n_splits):
            # Calculate split points
            test_end_idx = n_periods - i * (self.test_size // 2)
            test_start_idx = test_end_idx - self.test_size
            train_end_idx = test_start_idx - self.gap
            train_start_idx = train_end_idx - train_size
            
            if train_start_idx < 0 or test_start_idx < train_size:
                break
            
            # Get time periods
            train_periods = time_periods[train_start_idx:train_end_idx]
            test_periods = time_periods[test_start_idx:test_end_idx]
            
            # Get indices
            train_mask = data[time_col].isin(train_periods)
            test_mask = data[time_col].isin(test_periods)
            
            yield np.where(train_mask)[0], np.where(test_mask)[0]
    
    def _blocked_time_split(
        self,
        data: pd.DataFrame,
        time_periods: List,
        entity_col: str,
        time_col: str
    ) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """Block time series split with gap."""
        # Use sklearn's TimeSeriesSplit as base
        tscv = TimeSeriesSplit(
            n_splits=self.n_splits,
            test_size=self.test_size,
            gap=self.gap
        )
        
        # Create time index mapping
        time_to_idx = {t: i for i, t in enumerate(time_periods)}
        data['time_idx'] = data[time_col].map(time_to_idx)
        
        # Group by entity to maintain panel structure
        for train_time_idx, test_time_idx in tscv.split(range(len(time_periods))):
            train_periods = [time_periods[i] for i in train_time_idx]
            test_periods = [time_periods[i] for i in test_time_idx]
            
            train_mask = data[time_col].isin(train_periods)
            test_mask = data[time_col].isin(test_periods)
            
            yield np.where(train_mask)[0], np.where(test_mask)[0]
    
    def cross_validate_model(
        self,
        model_class,
        data: pd.DataFrame,
        feature_cols: List[str],
        target_col: str,
        model_params: Dict = None,
        scoring_funcs: Dict[str, callable] = None
    ) -> CVResults:
        """
        Perform cross-validation on panel model.
        
        Parameters
        ----------
        model_class : class
            Model class to instantiate
        data : DataFrame
            Panel data
        feature_cols : list
            Feature column names
        target_col : str
            Target variable name
        model_params : dict
            Parameters for model initialization
        scoring_funcs : dict
            Scoring functions to evaluate
        """
        if model_params is None:
            model_params = {}
        
        if scoring_funcs is None:
            scoring_funcs = {
                'mse': mean_squared_error,
                'mae': mean_absolute_error,
                'mape': self._mean_absolute_percentage_error
            }
        
        # Initialize results storage
        results = {
            'train_scores': {name: [] for name in scoring_funcs},
            'val_scores': {name: [] for name in scoring_funcs},
            'predictions': {},
            'coefficients': [],
            'fold_info': []
        }
        
        # Perform cross-validation
        for fold, (train_idx, test_idx) in enumerate(self.split(data)):
            print(f"Processing fold {fold + 1}/{self.n_splits}...")
            
            # Split data
            train_data = data.iloc[train_idx]
            test_data = data.iloc[test_idx]
            
            # Further split train into train/validation
            train_periods = sorted(train_data['date'].unique())
            val_size = min(self.test_size, len(train_periods) // 5)
            
            val_periods = train_periods[-val_size:]
            train_periods = train_periods[:-val_size]
            
            final_train = train_data[train_data['date'].isin(train_periods)]
            val_data = train_data[train_data['date'].isin(val_periods)]
            
            # Train model
            model = model_class(**model_params)
            
            if hasattr(model, 'fit'):
                model.fit(
                    final_train,
                    feature_cols=feature_cols,
                    target_col=target_col
                )
            else:
                # For econometric models
                model.estimate(final_train)
            
            # Make predictions
            sets = {
                'train': final_train,
                'val': val_data,
                'test': test_data
            }
            
            for set_name, set_data in sets.items():
                if len(set_data) > 0:
                    if hasattr(model, 'predict'):
                        pred = model.predict(set_data[feature_cols])
                    else:
                        pred = model.forecast(set_data)
                    
                    actual = set_data[target_col].values
                    
                    # Calculate scores
                    for score_name, score_func in scoring_funcs.items():
                        score = score_func(actual, pred)
                        
                        if set_name == 'train':
                            results['train_scores'][score_name].append(score)
                        elif set_name == 'val':
                            results['val_scores'][score_name].append(score)
                    
                    # Store test predictions
                    if set_name == 'test':
                        results['predictions'][f'fold_{fold}'] = {
                            'actual': actual,
                            'predicted': pred,
                            'dates': set_data['date'].values,
                            'entities': set_data['entity'].values
                        }
            
            # Store coefficients if available
            if hasattr(model, 'params'):
                results['coefficients'].append(model.params)
            elif hasattr(model, 'coef_'):
                results['coefficients'].append(
                    pd.Series(model.coef_, index=feature_cols)
                )
            
            # Store fold information
            results['fold_info'].append({
                'fold': fold,
                'train_size': len(final_train),
                'val_size': len(val_data),
                'test_size': len(test_data),
                'train_periods': (train_periods[0], train_periods[-1]),
                'test_periods': (test_periods[0], test_periods[-1])
            })
        
        # Create results object
        return self._compile_results(results, scoring_funcs)
    
    def _compile_results(
        self,
        results: Dict,
        scoring_funcs: Dict
    ) -> CVResults:
        """Compile cross-validation results."""
        # Average scores across folds
        train_scores = {
            name: np.mean(scores) 
            for name, scores in results['train_scores'].items()
        }
        
        val_scores = {
            name: np.mean(scores) 
            for name, scores in results['val_scores'].items()
        }
        
        # Calculate test scores from predictions
        test_scores = {}
        all_actual = []
        all_predicted = []
        
        for fold_pred in results['predictions'].values():
            all_actual.extend(fold_pred['actual'])
            all_predicted.extend(fold_pred['predicted'])
        
        for name, func in scoring_funcs.items():
            test_scores[name] = func(all_actual, all_predicted)
        
        # Compile coefficients
        if results['coefficients']:
            coef_df = pd.DataFrame(results['coefficients'])
            coef_df['fold'] = range(len(results['coefficients']))
        else:
            coef_df = pd.DataFrame()
        
        return CVResults(
            train_scores=train_scores,
            val_scores=val_scores,
            test_scores=test_scores,
            predictions=results['predictions'],
            coefficients=coef_df,
            fold_info=results['fold_info']
        )
    
    def _mean_absolute_percentage_error(self, y_true, y_pred):
        """Calculate MAPE avoiding division by zero."""
        mask = y_true != 0
        return np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100
```

### Spatial Cross-Validation

```python
class SpatialCrossValidator:
    """
    Cross-validation that respects spatial structure.
    
    Prevents spatial autocorrelation from inflating performance.
    """
    
    def __init__(
        self,
        n_splits: int = 5,
        buffer_size: float = 50.0,
        strategy: str = 'random'
    ):
        """
        Initialize spatial cross-validator.
        
        Parameters
        ----------
        n_splits : int
            Number of folds
        buffer_size : float
            Buffer around test locations (km)
        strategy : str
            'random': Random spatial blocks
            'systematic': Systematic spatial sampling
            'cluster': Cluster-based splitting
        """
        self.n_splits = n_splits
        self.buffer_size = buffer_size
        self.strategy = strategy
        
    def split(
        self,
        data: pd.DataFrame,
        coordinates: pd.DataFrame
    ) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """
        Generate spatially-aware train/test splits.
        
        Parameters
        ----------
        data : DataFrame
            Data to split
        coordinates : DataFrame
            Location coordinates (longitude, latitude)
        """
        if self.strategy == 'random':
            yield from self._random_spatial_split(data, coordinates)
        elif self.strategy == 'systematic':
            yield from self._systematic_spatial_split(data, coordinates)
        elif self.strategy == 'cluster':
            yield from self._cluster_based_split(data, coordinates)
    
    def _random_spatial_split(
        self,
        data: pd.DataFrame,
        coordinates: pd.DataFrame
    ) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """Random spatial block cross-validation."""
        from sklearn.model_selection import KFold
        
        # Get unique locations
        unique_locations = coordinates.drop_duplicates(
            subset=['longitude', 'latitude']
        )
        
        # Create spatial folds
        kf = KFold(n_splits=self.n_splits, shuffle=True, random_state=42)
        
        for train_locs, test_locs in kf.split(unique_locations):
            # Get test location coordinates
            test_coords = unique_locations.iloc[test_locs]
            
            # Create buffer around test locations
            test_mask = self._create_spatial_mask(
                coordinates,
                test_coords,
                buffer_km=0
            )
            
            # Exclude buffer zone from training
            buffer_mask = self._create_spatial_mask(
                coordinates,
                test_coords,
                buffer_km=self.buffer_size
            )
            
            train_mask = ~buffer_mask
            
            # Get indices
            train_idx = np.where(train_mask)[0]
            test_idx = np.where(test_mask)[0]
            
            if len(train_idx) > 0 and len(test_idx) > 0:
                yield train_idx, test_idx
    
    def _systematic_spatial_split(
        self,
        data: pd.DataFrame,
        coordinates: pd.DataFrame
    ) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """Systematic spatial sampling (checkerboard pattern)."""
        # Get bounding box
        min_lon = coordinates['longitude'].min()
        max_lon = coordinates['longitude'].max()
        min_lat = coordinates['latitude'].min()
        max_lat = coordinates['latitude'].max()
        
        # Create grid
        n_cells = int(np.sqrt(self.n_splits))
        lon_step = (max_lon - min_lon) / n_cells
        lat_step = (max_lat - min_lat) / n_cells
        
        # Assign locations to grid cells
        coordinates['grid_x'] = (
            (coordinates['longitude'] - min_lon) // lon_step
        ).astype(int)
        
        coordinates['grid_y'] = (
            (coordinates['latitude'] - min_lat) // lat_step
        ).astype(int)
        
        coordinates['grid_cell'] = (
            coordinates['grid_x'] * n_cells + coordinates['grid_y']
        )
        
        # Create checkerboard pattern
        for i in range(self.n_splits):
            # Test cells
            test_cells = [
                c for c in range(n_cells * n_cells)
                if (c % self.n_splits) == i
            ]
            
            test_mask = coordinates['grid_cell'].isin(test_cells)
            train_mask = ~test_mask
            
            yield np.where(train_mask)[0], np.where(test_mask)[0]
    
    def _cluster_based_split(
        self,
        data: pd.DataFrame,
        coordinates: pd.DataFrame
    ) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """Cluster-based spatial cross-validation."""
        from sklearn.cluster import KMeans
        
        # Cluster locations
        coords_array = coordinates[['longitude', 'latitude']].values
        
        kmeans = KMeans(n_clusters=self.n_splits, random_state=42)
        cluster_labels = kmeans.fit_predict(coords_array)
        
        # Use clusters as folds
        for test_cluster in range(self.n_splits):
            test_mask = cluster_labels == test_cluster
            train_mask = ~test_mask
            
            yield np.where(train_mask)[0], np.where(test_mask)[0]
    
    def _create_spatial_mask(
        self,
        all_coords: pd.DataFrame,
        target_coords: pd.DataFrame,
        buffer_km: float
    ) -> np.ndarray:
        """Create mask for locations within buffer of targets."""
        from scipy.spatial import cKDTree
        
        # Build KDTree for efficient search
        tree = cKDTree(
            all_coords[['longitude', 'latitude']].values
        )
        
        # Find all points within buffer
        mask = np.zeros(len(all_coords), dtype=bool)
        
        for _, target in target_coords.iterrows():
            target_point = [target['longitude'], target['latitude']]
            
            # Convert km to degrees (approximate)
            buffer_deg = buffer_km / 111
            
            # Find neighbors
            indices = tree.query_ball_point(target_point, buffer_deg)
            mask[indices] = True
        
        return mask
```

### Model-Specific Cross-Validation

```python
class EconometricCrossValidator:
    """
    Cross-validation for econometric models with special requirements.
    """
    
    def __init__(self, base_cv=None):
        """Initialize with base cross-validator."""
        self.base_cv = base_cv or PanelCrossValidator()
        
    def cross_validate_three_tier(
        self,
        data: pd.DataFrame,
        tier1_params: Dict = None,
        tier2_params: Dict = None,
        tier3_params: Dict = None
    ) -> Dict[str, CVResults]:
        """
        Cross-validate the complete three-tier model system.
        """
        from yemen_market.models.three_tier import (
            PooledPanelModel,
            CommoditySpecificModel,
            ConflictValidation
        )
        
        results = {}
        
        # Tier 1: Pooled Panel Model
        print("Cross-validating Tier 1: Pooled Panel Model...")
        tier1_cv = self._cv_tier1(data, PooledPanelModel, tier1_params)
        results['tier1'] = tier1_cv
        
        # Tier 2: Commodity-Specific Models
        print("\nCross-validating Tier 2: Commodity Models...")
        tier2_cv = self._cv_tier2(data, CommoditySpecificModel, tier2_params)
        results['tier2'] = tier2_cv
        
        # Tier 3: Validation Models
        print("\nCross-validating Tier 3: Validation Models...")
        tier3_cv = self._cv_tier3(data, ConflictValidation, tier3_params)
        results['tier3'] = tier3_cv
        
        # Combined assessment
        results['combined'] = self._assess_combined_performance(results)
        
        return results
    
    def _cv_tier1(
        self,
        data: pd.DataFrame,
        model_class,
        params: Dict
    ) -> CVResults:
        """Cross-validate Tier 1 pooled model."""
        # Custom scoring for panel models
        def panel_r2(y_true, y_pred, groups=None):
            """Panel R-squared accounting for fixed effects."""
            if groups is not None:
                # Within R-squared
                total_ss = 0
                residual_ss = 0
                
                for group in np.unique(groups):
                    mask = groups == group
                    y_true_g = y_true[mask]
                    y_pred_g = y_pred[mask]
                    
                    # Demean within group
                    y_true_dm = y_true_g - y_true_g.mean()
                    y_pred_dm = y_pred_g - y_pred_g.mean()
                    
                    total_ss += np.sum(y_true_dm ** 2)
                    residual_ss += np.sum((y_true_dm - y_pred_dm) ** 2)
                
                return 1 - residual_ss / total_ss
            else:
                # Standard R-squared
                ss_res = np.sum((y_true - y_pred) ** 2)
                ss_tot = np.sum((y_true - y_true.mean()) ** 2)
                return 1 - ss_res / ss_tot
        
        # Run cross-validation
        results = self.base_cv.cross_validate_model(
            model_class,
            data,
            feature_cols=['conflict_intensity', 'log_global_price'],
            target_col='log_price',
            model_params=params or {},
            scoring_funcs={
                'mse': mean_squared_error,
                'mae': mean_absolute_error,
                'panel_r2': panel_r2
            }
        )
        
        return results
    
    def _cv_tier2(
        self,
        data: pd.DataFrame,
        model_class,
        params: Dict
    ) -> Dict[str, CVResults]:
        """Cross-validate commodity-specific models."""
        commodity_results = {}
        
        # Major commodities to test
        commodities = ['wheat', 'rice_imported', 'sugar', 'diesel']
        
        for commodity in commodities:
            print(f"  Processing {commodity}...")
            
            commodity_data = data[data['commodity'] == commodity]
            
            if len(commodity_data) < 100:
                continue
            
            # Commodity-specific cross-validation
            cv_results = self.base_cv.cross_validate_model(
                model_class,
                commodity_data,
                feature_cols=['conflict_intensity', 'log_global_price'],
                target_col='log_price',
                model_params={
                    'commodity': commodity,
                    **(params or {})
                }
            )
            
            commodity_results[commodity] = cv_results
        
        return commodity_results
    
    def _cv_tier3(
        self,
        data: pd.DataFrame,
        model_class,
        params: Dict
    ) -> CVResults:
        """Cross-validate validation models."""
        # For conflict validation, need special handling
        # as it tests model predictions vs. actual conflict effects
        
        results_list = []
        
        for fold, (train_idx, test_idx) in enumerate(self.base_cv.split(data)):
            # Train base model on training data
            train_data = data.iloc[train_idx]
            test_data = data.iloc[test_idx]
            
            # Get base model predictions
            from yemen_market.models.three_tier import PooledPanelModel
            base_model = PooledPanelModel()
            base_model.fit(train_data)
            
            base_predictions = base_model.predict(test_data)
            
            # Run validation model
            validation_model = model_class()
            validation_results = validation_model.validate(
                test_data,
                base_predictions,
                actual_prices=test_data['log_price'].values
            )
            
            results_list.append(validation_results)
        
        # Compile results
        return self._compile_validation_results(results_list)
```

### Nested Cross-Validation

```python
class NestedCrossValidator:
    """
    Nested cross-validation for hyperparameter tuning and model selection.
    """
    
    def __init__(
        self,
        outer_cv=None,
        inner_cv=None,
        param_grid: Dict = None
    ):
        """
        Initialize nested CV.
        
        Parameters
        ----------
        outer_cv : cross-validator
            Outer loop for model evaluation
        inner_cv : cross-validator
            Inner loop for hyperparameter tuning
        param_grid : dict
            Parameter grid for tuning
        """
        self.outer_cv = outer_cv or PanelCrossValidator(n_splits=5)
        self.inner_cv = inner_cv or PanelCrossValidator(n_splits=3)
        self.param_grid = param_grid or {}
        
    def nested_cross_validate(
        self,
        model_class,
        data: pd.DataFrame,
        feature_cols: List[str],
        target_col: str,
        scoring: str = 'mse'
    ) -> Dict:
        """
        Perform nested cross-validation.
        
        Returns
        -------
        dict
            Results including best params per fold and final scores
        """
        outer_scores = []
        best_params_per_fold = []
        
        # Outer loop
        for fold, (train_idx, test_idx) in enumerate(
            self.outer_cv.split(data)
        ):
            print(f"Outer fold {fold + 1}...")
            
            train_data = data.iloc[train_idx]
            test_data = data.iloc[test_idx]
            
            # Inner loop for hyperparameter tuning
            best_score = float('inf')
            best_params = None
            
            # Grid search
            param_combinations = self._generate_param_combinations()
            
            for params in param_combinations:
                inner_scores = []
                
                # Inner cross-validation
                for inner_train_idx, inner_val_idx in self.inner_cv.split(train_data):
                    inner_train = train_data.iloc[inner_train_idx]
                    inner_val = train_data.iloc[inner_val_idx]
                    
                    # Train model with current parameters
                    model = model_class(**params)
                    model.fit(
                        inner_train,
                        feature_cols=feature_cols,
                        target_col=target_col
                    )
                    
                    # Evaluate on validation set
                    predictions = model.predict(inner_val[feature_cols])
                    score = mean_squared_error(
                        inner_val[target_col],
                        predictions
                    )
                    inner_scores.append(score)
                
                # Average score across inner folds
                avg_inner_score = np.mean(inner_scores)
                
                if avg_inner_score < best_score:
                    best_score = avg_inner_score
                    best_params = params
            
            # Train final model with best parameters
            best_model = model_class(**best_params)
            best_model.fit(
                train_data,
                feature_cols=feature_cols,
                target_col=target_col
            )
            
            # Evaluate on outer test set
            test_predictions = best_model.predict(test_data[feature_cols])
            outer_score = mean_squared_error(
                test_data[target_col],
                test_predictions
            )
            
            outer_scores.append(outer_score)
            best_params_per_fold.append(best_params)
            
            print(f"  Best params: {best_params}")
            print(f"  Outer score: {outer_score:.4f}")
        
        return {
            'outer_scores': outer_scores,
            'mean_outer_score': np.mean(outer_scores),
            'std_outer_score': np.std(outer_scores),
            'best_params_per_fold': best_params_per_fold,
            'most_common_params': self._get_most_common_params(
                best_params_per_fold
            )
        }
    
    def _generate_param_combinations(self) -> List[Dict]:
        """Generate all parameter combinations from grid."""
        from itertools import product
        
        keys = list(self.param_grid.keys())
        values = [self.param_grid[key] for key in keys]
        
        combinations = []
        for combo in product(*values):
            combinations.append(dict(zip(keys, combo)))
        
        return combinations
    
    def _get_most_common_params(
        self,
        params_list: List[Dict]
    ) -> Dict:
        """Find most commonly selected parameters."""
        from collections import Counter
        
        param_counts = {}
        
        for param_name in params_list[0].keys():
            values = [params[param_name] for params in params_list]
            counter = Counter(values)
            most_common_value = counter.most_common(1)[0][0]
            param_counts[param_name] = most_common_value
        
        return param_counts
```

### Cross-Validation Visualization

```python
class CVVisualizer:
    """Visualization tools for cross-validation results."""
    
    def __init__(self):
        """Initialize visualizer."""
        self.fig_size = (15, 10)
        
    def plot_cv_results(
        self,
        cv_results: Union[CVResults, Dict[str, CVResults]],
        save_path: str = None
    ) -> plt.Figure:
        """Create comprehensive CV results visualization."""
        if isinstance(cv_results, dict):
            # Multiple models
            return self._plot_multi_model_results(cv_results, save_path)
        else:
            # Single model
            return self._plot_single_model_results(cv_results, save_path)
    
    def _plot_single_model_results(
        self,
        results: CVResults,
        save_path: str = None
    ) -> plt.Figure:
        """Plot results for single model CV."""
        fig = plt.figure(figsize=self.fig_size)
        gs = fig.add_gridspec(2, 3, hspace=0.3, wspace=0.3)
        
        # 1. Score comparison across folds
        ax1 = fig.add_subplot(gs[0, 0])
        self._plot_fold_scores(results, ax1)
        
        # 2. Predictions vs actual
        ax2 = fig.add_subplot(gs[0, 1:])
        self._plot_predictions(results, ax2)
        
        # 3. Coefficient stability
        ax3 = fig.add_subplot(gs[1, 0])
        self._plot_coefficient_stability(results, ax3)
        
        # 4. Residual analysis
        ax4 = fig.add_subplot(gs[1, 1])
        self._plot_residuals(results, ax4)
        
        # 5. Time series of predictions
        ax5 = fig.add_subplot(gs[1, 2])
        self._plot_time_series_predictions(results, ax5)
        
        plt.suptitle('Cross-Validation Results', fontsize=16)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def _plot_fold_scores(self, results: CVResults, ax: plt.Axes):
        """Plot scores across CV folds."""
        metrics = list(results.train_scores.keys())
        n_metrics = len(metrics)
        
        x = np.arange(n_metrics)
        width = 0.25
        
        # Plot bars
        train_scores = [results.train_scores[m] for m in metrics]
        val_scores = [results.val_scores[m] for m in metrics]
        test_scores = [results.test_scores[m] for m in metrics]
        
        ax.bar(x - width, train_scores, width, label='Train', alpha=0.8)
        ax.bar(x, val_scores, width, label='Validation', alpha=0.8)
        ax.bar(x + width, test_scores, width, label='Test', alpha=0.8)
        
        ax.set_xlabel('Metric')
        ax.set_ylabel('Score')
        ax.set_title('CV Scores by Dataset')
        ax.set_xticks(x)
        ax.set_xticklabels(metrics)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_predictions(self, results: CVResults, ax: plt.Axes):
        """Plot predicted vs actual values."""
        all_actual = []
        all_predicted = []
        
        for fold_pred in results.predictions.values():
            all_actual.extend(fold_pred['actual'])
            all_predicted.extend(fold_pred['predicted'])
        
        all_actual = np.array(all_actual)
        all_predicted = np.array(all_predicted)
        
        # Scatter plot
        ax.scatter(all_actual, all_predicted, alpha=0.5, s=10)
        
        # Perfect prediction line
        min_val = min(all_actual.min(), all_predicted.min())
        max_val = max(all_actual.max(), all_predicted.max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)
        
        # Add R²
        from sklearn.metrics import r2_score
        r2 = r2_score(all_actual, all_predicted)
        
        ax.text(
            0.05, 0.95,
            f'R² = {r2:.3f}',
            transform=ax.transAxes,
            verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='white', alpha=0.8)
        )
        
        ax.set_xlabel('Actual')
        ax.set_ylabel('Predicted')
        ax.set_title('Predictions vs Actual (All Folds)')
        ax.grid(True, alpha=0.3)
    
    def _plot_coefficient_stability(self, results: CVResults, ax: plt.Axes):
        """Plot coefficient values across folds."""
        if results.coefficients.empty:
            ax.text(0.5, 0.5, 'No coefficients available',
                   transform=ax.transAxes, ha='center')
            return
        
        # Get coefficient columns (exclude 'fold')
        coef_cols = [col for col in results.coefficients.columns if col != 'fold']
        
        # Plot each coefficient
        for col in coef_cols[:5]:  # Limit to top 5
            values = results.coefficients[col]
            folds = results.coefficients.get('fold', range(len(values)))
            
            ax.plot(folds, values, marker='o', label=col)
        
        ax.set_xlabel('Fold')
        ax.set_ylabel('Coefficient Value')
        ax.set_title('Coefficient Stability Across Folds')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
    
    def plot_learning_curves(
        self,
        data: pd.DataFrame,
        model_class,
        cv,
        train_sizes: np.ndarray = None
    ) -> plt.Figure:
        """Plot learning curves to diagnose bias/variance."""
        from sklearn.model_selection import learning_curve
        
        if train_sizes is None:
            train_sizes = np.linspace(0.1, 1.0, 10)
        
        # Calculate learning curves
        train_sizes_abs, train_scores, val_scores = learning_curve(
            model_class(),
            data[['conflict_intensity', 'log_global_price']],
            data['log_price'],
            cv=cv,
            train_sizes=train_sizes,
            scoring='neg_mean_squared_error',
            n_jobs=-1
        )
        
        # Plot
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Calculate mean and std
        train_mean = -train_scores.mean(axis=1)
        train_std = train_scores.std(axis=1)
        val_mean = -val_scores.mean(axis=1)
        val_std = val_scores.std(axis=1)
        
        # Plot with confidence bands
        ax.plot(train_sizes_abs, train_mean, 'b-', label='Training score')
        ax.fill_between(
            train_sizes_abs,
            train_mean - train_std,
            train_mean + train_std,
            alpha=0.1,
            color='blue'
        )
        
        ax.plot(train_sizes_abs, val_mean, 'r-', label='Validation score')
        ax.fill_between(
            train_sizes_abs,
            val_mean - val_std,
            val_mean + val_std,
            alpha=0.1,
            color='red'
        )
        
        ax.set_xlabel('Training Set Size')
        ax.set_ylabel('MSE')
        ax.set_title('Learning Curves')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Diagnose bias/variance
        final_train = train_mean[-1]
        final_val = val_mean[-1]
        
        if final_train > 0.1:  # High training error
            diagnosis = "High Bias (Underfitting)"
        elif final_val - final_train > 0.05:  # Large gap
            diagnosis = "High Variance (Overfitting)"
        else:
            diagnosis = "Good Fit"
        
        ax.text(
            0.5, 0.95,
            f'Diagnosis: {diagnosis}',
            transform=ax.transAxes,
            ha='center',
            va='top',
            bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.5)
        )
        
        return fig
```

## See Also

- [Conflict Validation](conflict-validation.md) - Tier 3 validation methods
- [Factor Analysis](factor-analysis.md) - Dimensionality reduction validation
- [Robustness Checks](../statistical-tests/robustness-checks.md) - Model stability
- [API Reference: Cross-Validation](../../03-api-reference/models/three_tier/integration.md)