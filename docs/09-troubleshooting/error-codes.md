# Error Code Reference

This reference provides detailed information about error codes, their meanings, and solutions.

## Data Loading Errors (1000-1999)

### ERROR-1001: Missing Required Data File
```
Error Code: ERROR-1001
Message: Required data file not found: {filepath}
```

**Cause:** Expected data file is missing from the specified location.

**Solution:**
```python
# Check if file exists and download if missing
from yemen_market.data.hdx_client import HDXClient

if not os.path.exists(filepath):
    client = HDXClient()
    client.download_dataset('dataset-name')
```

### ERROR-1002: Invalid Data Format
```
Error Code: ERROR-1002
Message: Invalid data format in {file}: Expected {expected}, got {actual}
```

**Cause:** Data file format doesn't match expected structure.

**Solution:**
```python
# Validate data format before processing
def validate_data_format(df, expected_columns):
    missing = set(expected_columns) - set(df.columns)
    if missing:
        raise ValueError(f"Missing columns: {missing}")
    return True
```

### ERROR-1003: Encoding Error
```
Error Code: ERROR-1003
Message: Unable to decode file {file}: {encoding_error}
```

**Cause:** File encoding doesn't match expected encoding.

**Solution:**
```python
import chardet

# Detect and use correct encoding
with open(filepath, 'rb') as f:
    result = chardet.detect(f.read())
    encoding = result['encoding']

df = pd.read_csv(filepath, encoding=encoding)
```

### ERROR-1004: Data Validation Failed
```
Error Code: ERROR-1004
Message: Data validation failed: {validation_errors}
```

**Cause:** Loaded data doesn't pass validation checks.

**Solution:**
```python
# Implement comprehensive validation
from yemen_market.models.three_tier.core import DataValidator

validator = DataValidator()
is_valid, errors = validator.validate_panel_data(df)
if not is_valid:
    print("Validation errors:", errors)
    # Fix or filter data based on errors
```

## Panel Construction Errors (2000-2999)

### ERROR-2001: Insufficient Data Points
```
Error Code: ERROR-2001
Message: Insufficient data points for {market}-{commodity}: {n_obs} < {min_required}
```

**Cause:** Not enough observations to construct valid panel.

**Solution:**
```python
# Filter markets/commodities with sufficient data
min_obs = 24  # 2 years monthly
sufficient_data = df.groupby(['market', 'commodity']).size()
valid_pairs = sufficient_data[sufficient_data >= min_obs].index
df_filtered = df.set_index(['market', 'commodity']).loc[valid_pairs].reset_index()
```

### ERROR-2002: Duplicate Panel Entries
```
Error Code: ERROR-2002
Message: Duplicate entries found for {market}, {commodity}, {date}
```

**Cause:** Multiple observations for same market-commodity-date combination.

**Solution:**
```python
# Handle duplicates appropriately
# Option 1: Average duplicates
df_clean = df.groupby(['market', 'commodity', 'date']).agg({
    'price': 'mean',
    'quantity': 'sum'
}).reset_index()

# Option 2: Keep most recent
df_clean = df.sort_values('collection_date').drop_duplicates(
    subset=['market', 'commodity', 'date'],
    keep='last'
)
```

### ERROR-2003: Unbalanced Panel Warning
```
Error Code: ERROR-2003
Message: Panel is severely unbalanced: {percent_missing}% observations missing
```

**Cause:** High proportion of missing observations in panel.

**Solution:**
```python
# Create balanced panel with interpolation
from yemen_market.data.panel_builder import PanelBuilder

builder = PanelBuilder()
balanced_panel = builder.create_balanced_panel(
    interpolation_method='linear',
    max_gap=2,
    min_markets_per_commodity=5
)
```

### ERROR-2004: Time Series Gap Too Large
```
Error Code: ERROR-2004
Message: Gap in time series exceeds maximum: {gap_months} months at {location}
```

**Cause:** Extended period with no observations.

**Solution:**
```python
# Identify and handle large gaps
def check_time_gaps(df, max_gap_months=3):
    gaps = []
    for (market, commodity), group in df.groupby(['market', 'commodity']):
        dates = pd.to_datetime(group['date']).sort_values()
        date_diff = dates.diff().dt.days / 30  # Approximate months
        large_gaps = date_diff[date_diff > max_gap_months]
        if len(large_gaps) > 0:
            gaps.append({
                'market': market,
                'commodity': commodity,
                'max_gap': large_gaps.max(),
                'gap_periods': len(large_gaps)
            })
    return pd.DataFrame(gaps)
```

## Model Fitting Errors (3000-3999)

### ERROR-3001: Singular Matrix
```
Error Code: ERROR-3001
Message: Singular matrix in regression: Check for perfect multicollinearity
```

**Cause:** Perfect multicollinearity in explanatory variables.

**Solution:**
```python
# Check and remove multicollinear variables
from statsmodels.stats.outliers_influence import variance_inflation_factor

def check_multicollinearity(X):
    vif = pd.DataFrame()
    vif["Variable"] = X.columns
    vif["VIF"] = [variance_inflation_factor(X.values, i) 
                  for i in range(X.shape[1])]
    
    # Remove variables with VIF > 10
    high_vif = vif[vif['VIF'] > 10]['Variable'].tolist()
    return X.drop(columns=high_vif)
```

### ERROR-3002: Convergence Failed
```
Error Code: ERROR-3002
Message: Model failed to converge after {max_iter} iterations
```

**Cause:** Optimization algorithm didn't converge.

**Solution:**
```python
# Try different optimization options
from scipy.optimize import minimize

# Option 1: Increase iterations
result = minimize(
    objective_function,
    x0=initial_params,
    method='L-BFGS-B',
    options={'maxiter': 10000, 'ftol': 1e-8}
)

# Option 2: Try different algorithm
for method in ['L-BFGS-B', 'TNC', 'SLSQP']:
    try:
        result = minimize(objective_function, x0, method=method)
        if result.success:
            break
    except:
        continue
```

### ERROR-3003: Insufficient Degrees of Freedom
```
Error Code: ERROR-3003
Message: Insufficient degrees of freedom: {n_obs} observations, {n_params} parameters
```

**Cause:** More parameters than observations.

**Solution:**
```python
# Reduce model complexity
def check_degrees_of_freedom(n_obs, n_params, min_ratio=10):
    if n_obs < n_params * min_ratio:
        # Reduce parameters
        # Option 1: Use fewer fixed effects
        # Option 2: Aggregate time periods
        # Option 3: Use regularization
        return False
    return True
```

### ERROR-3004: Cointegration Test Failed
```
Error Code: ERROR-3004
Message: No cointegration found between {var1} and {var2}
```

**Cause:** Variables don't have long-run relationship.

**Solution:**
```python
# Test for cointegration with multiple methods
from statsmodels.tsa.stattools import coint
from statsmodels.tsa.vector_ar.vecm import coint_johansen

def comprehensive_cointegration_test(y1, y2):
    # Engle-Granger test
    eg_stat, eg_pvalue, _ = coint(y1, y2)
    
    # Johansen test
    data = pd.DataFrame({'y1': y1, 'y2': y2})
    joh_result = coint_johansen(data, det_order=0, k_ar_diff=1)
    
    # Check both tests
    if eg_pvalue < 0.05 or (joh_result.lr1[0] > joh_result.cvt[0, 0]):
        return True, "Cointegration found"
    else:
        return False, "No cointegration - consider VAR instead of VECM"
```

## External API Errors (4000-4999)

### ERROR-4001: API Rate Limit Exceeded
```
Error Code: ERROR-4001
Message: Rate limit exceeded: {requests}/{limit} in {period}
```

**Cause:** Too many requests to external API.

**Solution:**
```python
import time
from functools import wraps

def rate_limit(calls_per_minute=60):
    min_interval = 60.0 / calls_per_minute
    last_called = [0.0]
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = min_interval - elapsed
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            ret = func(*args, **kwargs)
            last_called[0] = time.time()
            return ret
        return wrapper
    return decorator

@rate_limit(calls_per_minute=30)
def api_call(endpoint):
    return requests.get(endpoint)
```

### ERROR-4002: API Authentication Failed
```
Error Code: ERROR-4002
Message: Authentication failed for {service}: {reason}
```

**Cause:** Invalid or missing API credentials.

**Solution:**
```python
# Secure credential management
import os
from dotenv import load_dotenv

load_dotenv()

class APIClient:
    def __init__(self):
        self.api_key = os.getenv('API_KEY')
        if not self.api_key:
            raise ValueError("API_KEY not found in environment")
    
    def authenticate(self):
        headers = {'Authorization': f'Bearer {self.api_key}'}
        response = requests.get('https://api.example.com/auth', headers=headers)
        if response.status_code != 200:
            raise AuthenticationError(f"Auth failed: {response.text}")
```

### ERROR-4003: API Timeout
```
Error Code: ERROR-4003
Message: Request timeout after {timeout} seconds: {url}
```

**Cause:** API response took too long.

**Solution:**
```python
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

def create_session_with_retries():
    session = requests.Session()
    retry = Retry(
        total=3,
        read=3,
        connect=3,
        backoff_factor=0.3,
        status_forcelist=(500, 502, 504)
    )
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('http://', adapter)
    session.mount('https://', adapter)
    return session

# Use with timeout
session = create_session_with_retries()
response = session.get(url, timeout=30)
```

### ERROR-4004: Invalid API Response
```
Error Code: ERROR-4004
Message: Invalid response from {service}: {response_summary}
```

**Cause:** API returned unexpected format or error.

**Solution:**
```python
def validate_api_response(response):
    """Validate and parse API response safely"""
    
    # Check status code
    if response.status_code != 200:
        raise APIError(f"HTTP {response.status_code}: {response.text}")
    
    # Try to parse JSON
    try:
        data = response.json()
    except json.JSONDecodeError:
        raise APIError(f"Invalid JSON: {response.text[:200]}")
    
    # Validate structure
    required_fields = ['status', 'data']
    missing = [f for f in required_fields if f not in data]
    if missing:
        raise APIError(f"Missing fields: {missing}")
    
    if data['status'] != 'success':
        raise APIError(f"API error: {data.get('message', 'Unknown error')}")
    
    return data['data']
```

## System Errors (5000-5999)

### ERROR-5001: Insufficient Memory
```
Error Code: ERROR-5001
Message: Insufficient memory: Required {required}GB, Available {available}GB
```

**Cause:** Not enough RAM for operation.

**Solution:**
```python
import psutil

def check_memory_availability(required_gb):
    available = psutil.virtual_memory().available / (1024**3)
    if available < required_gb:
        suggestions = [
            "1. Use chunked processing",
            "2. Reduce data precision (float64 -> float32)",
            "3. Use sparse matrices",
            "4. Process subsets sequentially"
        ]
        raise MemoryError(
            f"Need {required_gb}GB, have {available:.1f}GB. "
            f"Try: {', '.join(suggestions)}"
        )
```

### ERROR-5002: Disk Space Exhausted
```
Error Code: ERROR-5002
Message: Insufficient disk space: {required} needed, {available} available
```

**Cause:** Not enough disk space for output.

**Solution:**
```python
import shutil

def check_disk_space(path, required_bytes):
    stat = shutil.disk_usage(path)
    if stat.free < required_bytes:
        # Clean up temporary files
        import tempfile
        temp_dir = tempfile.gettempdir()
        for file in os.listdir(temp_dir):
            if file.startswith('yemen_market_'):
                os.remove(os.path.join(temp_dir, file))
        
        # Check again
        stat = shutil.disk_usage(path)
        if stat.free < required_bytes:
            raise IOError("Insufficient disk space after cleanup")
```

### ERROR-5003: Permission Denied
```
Error Code: ERROR-5003
Message: Permission denied: {path}
```

**Cause:** Insufficient permissions for file operation.

**Solution:**
```python
import stat

def ensure_writable(path):
    """Ensure path is writable"""
    
    if os.path.exists(path):
        # Check current permissions
        current = os.stat(path).st_mode
        if not os.access(path, os.W_OK):
            # Try to add write permission
            try:
                os.chmod(path, current | stat.S_IWUSR)
            except PermissionError:
                # Use alternative location
                alt_path = os.path.join(
                    os.path.expanduser('~'), 
                    '.yemen_market', 
                    os.path.basename(path)
                )
                os.makedirs(os.path.dirname(alt_path), exist_ok=True)
                return alt_path
    return path
```

### ERROR-5004: Process Timeout
```
Error Code: ERROR-5004
Message: Process exceeded timeout of {timeout} seconds
```

**Cause:** Long-running process exceeded time limit.

**Solution:**
```python
import signal
from contextlib import contextmanager

@contextmanager
def timeout(seconds):
    def signal_handler(signum, frame):
        raise TimeoutError(f"Timed out after {seconds} seconds")
    
    # Set the signal handler
    signal.signal(signal.SIGALRM, signal_handler)
    signal.alarm(seconds)
    
    try:
        yield
    finally:
        signal.alarm(0)  # Disable alarm

# Usage
try:
    with timeout(300):  # 5 minutes
        result = long_running_function()
except TimeoutError:
    # Handle timeout - maybe process smaller chunk
    result = process_in_chunks()
```

## Configuration Errors (6000-6999)

### ERROR-6001: Missing Configuration
```
Error Code: ERROR-6001
Message: Required configuration missing: {config_key}
```

**Cause:** Required configuration parameter not set.

**Solution:**
```python
# Provide defaults and validation
class Config:
    REQUIRED_KEYS = ['DATA_DIR', 'MODEL_DIR', 'LOG_LEVEL']
    DEFAULTS = {
        'DATA_DIR': './data',
        'MODEL_DIR': './models',
        'LOG_LEVEL': 'INFO'
    }
    
    def __init__(self):
        for key in self.REQUIRED_KEYS:
            value = os.getenv(key, self.DEFAULTS.get(key))
            if not value:
                raise ValueError(f"Missing required config: {key}")
            setattr(self, key, value)
```

### ERROR-6002: Invalid Configuration Value
```
Error Code: ERROR-6002
Message: Invalid value for {config_key}: {value}
```

**Cause:** Configuration value doesn't meet requirements.

**Solution:**
```python
# Validate configuration values
def validate_config(config):
    validators = {
        'LOG_LEVEL': lambda x: x in ['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        'BATCH_SIZE': lambda x: isinstance(x, int) and 0 < x <= 10000,
        'N_JOBS': lambda x: isinstance(x, int) and -1 <= x <= os.cpu_count()
    }
    
    for key, validator in validators.items():
        if hasattr(config, key):
            value = getattr(config, key)
            if not validator(value):
                raise ValueError(f"Invalid {key}: {value}")
```