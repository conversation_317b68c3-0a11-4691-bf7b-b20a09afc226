# Model Convergence Issues

This guide addresses problems with model fitting, convergence failures, and numerical instabilities in econometric models.

## Fixed Effects Model Issues

### Problem: Singular matrix error
```python
numpy.linalg.LinAlgError: Singular matrix
```

**Solution:**
```python
from yemen_market.models.three_tier import Tier1PooledModel
import numpy as np
import pandas as pd

# Check for perfect multicollinearity
def check_multicollinearity(X):
    # Correlation matrix
    corr_matrix = X.corr()
    
    # Find highly correlated pairs
    high_corr = np.where(np.abs(corr_matrix) > 0.95)
    high_corr_pairs = [(corr_matrix.index[x], corr_matrix.columns[y]) 
                       for x, y in zip(*high_corr) if x != y and x < y]
    
    if high_corr_pairs:
        print("Highly correlated variables:")
        for var1, var2 in high_corr_pairs:
            print(f"  {var1} - {var2}: {corr_matrix.loc[var1, var2]:.3f}")
    
    # Check rank
    rank = np.linalg.matrix_rank(X.values)
    if rank < X.shape[1]:
        print(f"Matrix rank ({rank}) < number of columns ({X.shape[1]})")
        print("Model is not identified!")
    
    return high_corr_pairs

# Remove problematic variables
X_clean = X.drop(columns=['problematic_var'])

# Or use regularization
from sklearn.linear_model import Ridge
model = Ridge(alpha=0.01)  # Small regularization
```

### Problem: Fixed effects consuming too much memory
```python
MemoryError: Unable to allocate 50GB for dummy variables
```

**Solution:**
```python
# Method 1: Use within-transformation instead of dummies
def demean_panel(df, entity_var='market', time_var='date'):
    """Perform within-transformation without creating dummies"""
    # Calculate entity means
    entity_means = df.groupby(entity_var).transform('mean')
    
    # Demean variables
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    df_demeaned = df.copy()
    df_demeaned[numeric_cols] = df[numeric_cols] - entity_means[numeric_cols]
    
    return df_demeaned

# Method 2: Use sparse matrices
from scipy.sparse import csr_matrix
from sklearn.preprocessing import LabelEncoder

def create_sparse_dummies(df, categorical_cols):
    """Create sparse dummy matrix"""
    encoders = {}
    sparse_matrices = []
    
    for col in categorical_cols:
        le = LabelEncoder()
        encoded = le.fit_transform(df[col])
        n_categories = len(le.classes_)
        
        # Create sparse dummy matrix
        row_indices = np.arange(len(df))
        col_indices = encoded
        data = np.ones(len(df))
        
        sparse_dummy = csr_matrix(
            (data, (row_indices, col_indices)),
            shape=(len(df), n_categories)
        )
        sparse_matrices.append(sparse_dummy)
        encoders[col] = le
    
    # Combine all sparse matrices
    return hstack(sparse_matrices), encoders

# Method 3: Use econometric packages with efficient FE
import linearmodels as lm

# PanelOLS handles fixed effects efficiently
model = lm.PanelOLS(
    dependent=df['price'],
    exog=df[['conflict', 'global_price']],
    entity_effects=True,
    time_effects=True
)
results = model.fit(cov_type='clustered', cluster_entity=True)
```

## VECM Convergence Issues

### Problem: Johansen test fails
```python
ValueError: The eigenvalues are not all positive, check your data
```

**Solution:**
```python
from statsmodels.tsa.vector_ar.vecm import coint_johansen
import numpy as np

def safe_johansen_test(data, det_order=0, k_ar_diff=1):
    """Johansen test with data validation"""
    
    # Check for stationarity
    from statsmodels.tsa.stattools import adfuller
    for col in data.columns:
        adf_result = adfuller(data[col], regression='ct')
        if adf_result[1] < 0.05:  # Already stationary
            print(f"Warning: {col} appears stationary (p={adf_result[1]:.3f})")
    
    # Check for perfect correlation
    corr = data.corr()
    if (corr.abs() > 0.99).sum().sum() > len(corr):
        print("Warning: Near-perfect correlation detected")
        # Add small noise to break perfect correlation
        data = data + np.random.normal(0, 1e-6, data.shape)
    
    # Scale data to avoid numerical issues
    data_scaled = (data - data.mean()) / data.std()
    
    try:
        result = coint_johansen(data_scaled, det_order, k_ar_diff)
        return result
    except np.linalg.LinAlgError:
        # Try with different lag order
        for lag in range(1, 5):
            try:
                result = coint_johansen(data_scaled, det_order, lag)
                print(f"Succeeded with lag order {lag}")
                return result
            except:
                continue
        raise ValueError("Johansen test failed with all lag orders")

# Use robust version
result = safe_johansen_test(price_data[['aden_price', 'sanaa_price']])
```

### Problem: VECM estimation fails
```python
# VECM fit() throws convergence warnings or errors
```

**Solution:**
```python
from statsmodels.tsa.vector_ar.vecm import VECM
import warnings

def fit_vecm_robust(endog, exog=None, k_ar_diff=1, coint_rank=1):
    """Fit VECM with fallback options"""
    
    # Try standard fitting first
    try:
        model = VECM(endog, exog=exog, k_ar_diff=k_ar_diff, 
                     coint_rank=coint_rank, deterministic='ci')
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            results = model.fit()
        return results
    except:
        pass
    
    # Try with different deterministics
    for det in ['co', 'ci', 'lo', 'li']:
        try:
            model = VECM(endog, exog=exog, k_ar_diff=k_ar_diff,
                        coint_rank=coint_rank, deterministic=det)
            results = model.fit()
            print(f"Succeeded with deterministic={det}")
            return results
        except:
            continue
    
    # Try reduced rank
    if coint_rank > 1:
        print(f"Reducing cointegration rank from {coint_rank} to 1")
        return fit_vecm_robust(endog, exog, k_ar_diff, coint_rank=1)
    
    # Fall back to VAR
    print("VECM failed, falling back to VAR")
    from statsmodels.tsa.vector_ar.var_model import VAR
    model = VAR(endog)
    results = model.fit(maxlags=k_ar_diff+1)
    return results
```

## Threshold Model Issues

### Problem: Grid search for threshold gets stuck
```python
# Threshold estimation takes hours or never completes
```

**Solution:**
```python
from yemen_market.models.three_tier.tier2_commodity import ThresholdVECM
import numpy as np

def estimate_threshold_efficient(data, y_col, x_col, threshold_var):
    """Efficient threshold estimation with optimization"""
    
    # Step 1: Reduce grid points
    threshold_values = np.percentile(
        data[threshold_var], 
        np.linspace(15, 85, 20)  # Only test 20 points between 15th-85th percentile
    )
    
    # Step 2: Parallel grid search
    from joblib import Parallel, delayed
    
    def compute_ssr(threshold):
        low_regime = data[data[threshold_var] <= threshold]
        high_regime = data[data[threshold_var] > threshold]
        
        if len(low_regime) < 30 or len(high_regime) < 30:
            return np.inf  # Not enough observations
        
        # Fit models
        ssr_low = np.sum((low_regime[y_col] - 
                         low_regime[x_col] * 
                         (low_regime[y_col] @ low_regime[x_col]) / 
                         (low_regime[x_col] @ low_regime[x_col]))**2)
        ssr_high = np.sum((high_regime[y_col] - 
                          high_regime[x_col] * 
                          (high_regime[y_col] @ high_regime[x_col]) / 
                          (high_regime[x_col] @ high_regime[x_col]))**2)
        
        return ssr_low + ssr_high
    
    # Parallel computation
    ssr_values = Parallel(n_jobs=-1)(
        delayed(compute_ssr)(t) for t in threshold_values
    )
    
    # Find optimal threshold
    optimal_idx = np.argmin(ssr_values)
    optimal_threshold = threshold_values[optimal_idx]
    
    # Step 3: Refine around optimal
    if optimal_idx > 0 and optimal_idx < len(threshold_values) - 1:
        # Search finer grid around optimum
        fine_grid = np.linspace(
            threshold_values[optimal_idx-1],
            threshold_values[optimal_idx+1],
            10
        )
        fine_ssr = [compute_ssr(t) for t in fine_grid]
        optimal_threshold = fine_grid[np.argmin(fine_ssr)]
    
    return optimal_threshold
```

### Problem: Hansen test for threshold significance fails
```python
# Bootstrap takes too long or crashes
```

**Solution:**
```python
def hansen_test_fast(data, threshold, n_bootstrap=300):
    """Fast Hansen test using parallel bootstrap"""
    from joblib import Parallel, delayed
    import numpy as np
    
    # Original model SSR
    ssr_threshold = compute_model_ssr(data, threshold)
    ssr_linear = compute_model_ssr(data, None)  # No threshold
    lr_stat = len(data) * np.log(ssr_linear / ssr_threshold)
    
    # Parallel bootstrap
    def bootstrap_iteration(seed):
        np.random.seed(seed)
        # Generate data under null (no threshold)
        boot_data = generate_bootstrap_data(data, has_threshold=False)
        # Find threshold in bootstrap data
        boot_threshold = estimate_threshold_efficient(boot_data)
        # Compute LR statistic
        boot_ssr_thresh = compute_model_ssr(boot_data, boot_threshold)
        boot_ssr_linear = compute_model_ssr(boot_data, None)
        return len(boot_data) * np.log(boot_ssr_linear / boot_ssr_thresh)
    
    # Run bootstrap in parallel
    bootstrap_stats = Parallel(n_jobs=-1)(
        delayed(bootstrap_iteration)(i) for i in range(n_bootstrap)
    )
    
    # Compute p-value
    p_value = np.mean(np.array(bootstrap_stats) > lr_stat)
    return p_value, lr_stat
```

## Diagnostic Test Failures

### Problem: Pesaran CD test returns NaN
```python
# Cross-sectional dependence test fails
stat, pvalue = pesaran_cd_test(residuals)
# stat = nan, pvalue = nan
```

**Solution:**
```python
def pesaran_cd_test_robust(residuals):
    """Robust Pesaran CD test handling edge cases"""
    T, N = residuals.shape
    
    if T < 2 or N < 2:
        raise ValueError(f"Insufficient dimensions: T={T}, N={N}")
    
    # Check for zero variance
    variances = residuals.var(axis=0)
    if (variances == 0).any():
        print("Warning: Dropping zero-variance cross-sections")
        mask = variances > 0
        residuals = residuals[:, mask]
        N = residuals.shape[1]
    
    # Compute correlation matrix
    corr_matrix = np.corrcoef(residuals.T)
    
    # Handle perfect correlations
    np.fill_diagonal(corr_matrix, 0)
    corr_matrix = np.nan_to_num(corr_matrix, nan=0)
    
    # CD statistic
    cd_stat = np.sqrt(2*T/(N*(N-1))) * np.sum(
        np.triu(corr_matrix, k=1)
    )
    
    # P-value from normal distribution
    from scipy import stats
    p_value = 2 * (1 - stats.norm.cdf(np.abs(cd_stat)))
    
    return cd_stat, p_value
```

### Problem: Unit root tests contradictory results
```python
# ADF says stationary, KPSS says non-stationary
```

**Solution:**
```python
def comprehensive_stationarity_test(series, name="Series"):
    """Run multiple unit root tests and summarize"""
    from statsmodels.tsa.stattools import adfuller, kpss
    from arch.unitroot import PhillipsPerron, DFGLS
    
    results = {}
    
    # ADF test
    adf_result = adfuller(series, regression='ct', autolag='AIC')
    results['ADF'] = {
        'statistic': adf_result[0],
        'p-value': adf_result[1],
        'stationary': adf_result[1] < 0.05
    }
    
    # KPSS test (null = stationary)
    kpss_result = kpss(series, regression='ct')
    results['KPSS'] = {
        'statistic': kpss_result[0],
        'p-value': kpss_result[1],
        'stationary': kpss_result[1] > 0.05  # Fail to reject null
    }
    
    # Phillips-Perron
    pp = PhillipsPerron(series)
    pp_result = pp.summary().as_text()
    results['PP'] = {
        'statistic': pp.stat,
        'p-value': pp.pvalue,
        'stationary': pp.pvalue < 0.05
    }
    
    # DF-GLS (more powerful)
    dfgls = DFGLS(series)
    results['DF-GLS'] = {
        'statistic': dfgls.stat,
        'p-value': dfgls.pvalue,
        'stationary': dfgls.pvalue < 0.05
    }
    
    # Summary
    stationary_count = sum(r['stationary'] for r in results.values())
    consensus = stationary_count >= 3  # Majority rule
    
    print(f"\nStationarity tests for {name}:")
    print("-" * 50)
    for test, result in results.items():
        print(f"{test:8} | stat: {result['statistic']:7.3f} | "
              f"p-val: {result['p-value']:6.3f} | "
              f"{'Stationary' if result['stationary'] else 'Non-stationary'}")
    print("-" * 50)
    print(f"Consensus: {'Stationary' if consensus else 'Non-stationary'} "
          f"({stationary_count}/4 tests)")
    
    return consensus, results
```

## Performance Issues

### Problem: Model estimation extremely slow
```python
# Fixed effects model taking hours for moderate data
```

**Solution:**
```python
# Method 1: Use compiled implementations
import numba

@numba.jit(nopython=True)
def demean_numba(X, groups):
    """Fast within-transformation using Numba"""
    n, k = X.shape
    X_demeaned = np.zeros_like(X)
    
    for g in np.unique(groups):
        mask = groups == g
        group_mean = X[mask].mean(axis=0)
        X_demeaned[mask] = X[mask] - group_mean
    
    return X_demeaned

# Method 2: Use optimized libraries
from linearmodels import PanelOLS
# LinearModels uses optimized C code

# Method 3: Reduce precision for initial estimates
def two_stage_estimation(data, full_precision=np.float64):
    # Stage 1: Get initial estimates with float32
    data_32 = data.astype(np.float32)
    initial_model = fit_model(data_32)
    
    # Stage 2: Refine with full precision
    # Use initial estimates as starting values
    final_model = fit_model(
        data.astype(full_precision),
        start_params=initial_model.params
    )
    
    return final_model
```