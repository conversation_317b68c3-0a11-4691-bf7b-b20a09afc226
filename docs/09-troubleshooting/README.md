# Troubleshooting Guide

This directory contains comprehensive troubleshooting guides for common issues encountered when using the Yemen Market Integration Platform.

## Quick Navigation

- **[Common Issues](common-issues.md)** - Frequently encountered problems and their solutions
- **[Data Issues](data-issues.md)** - Problems with data loading, processing, and validation
- **[Model Convergence](model-convergence.md)** - Issues with model fitting and convergence
- **[Performance](performance.md)** - Memory usage and performance optimization
- **[Error Codes](error-codes.md)** - Reference for error codes and their meanings

## Before You Start Troubleshooting

1. **Check the logs** - Most issues are logged with detailed error messages:
   ```bash
   tail -f logs/yemen_market_*.log
   ```

2. **Verify your environment** - Ensure all dependencies are installed:
   ```bash
   pip install -r requirements.txt
   ```

3. **Check data availability** - Confirm required data files exist:
   ```bash
   ls -la data/raw/
   ls -la data/processed/
   ```

## Getting Help

If you can't find a solution in these guides:

1. Check the [GitHub Issues](https://github.com/mohammadalakkaoui/yemen-market-integration/issues)
2. Review the [API Documentation](../03-api-reference/)
3. Contact the development team

## Common Quick Fixes

### Memory Issues
```bash
# Increase Python's memory limit
export PYTHONMALLOC=malloc
ulimit -v unlimited
```

### Missing Dependencies
```bash
# Reinstall with all extras
pip install -e ".[all]"
```

### Data Download Failures
```bash
# Clear cache and re-download
rm -rf data/cache/*
python scripts/data_collection/download_data.py --force
```

## Diagnostic Commands

### Check System Status
```bash
# Run diagnostics
python -m yemen_market.utils.diagnostics

# Verify data integrity
python scripts/data_processing/validate_data.py
```

### Test Model Components
```bash
# Test individual tiers
python -m pytest tests/integration/test_tier1_integration.py -v
python -m pytest tests/integration/test_tier2_integration.py -v
```

## Log Levels

Set logging level for more detailed output:
```python
import logging
logging.getLogger('yemen_market').setLevel(logging.DEBUG)
```

Or via environment variable:
```bash
export YEMEN_MARKET_LOG_LEVEL=DEBUG
```