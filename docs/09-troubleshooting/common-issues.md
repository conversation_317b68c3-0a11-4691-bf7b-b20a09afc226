# Common Issues and Solutions

This guide covers the most frequently encountered problems when using the Yemen Market Integration Platform.

## Installation Issues

### Problem: Import errors after installation
```python
ImportError: No module named 'yemen_market'
```

**Solution:**
```bash
# Install in development mode
pip install -e .

# Or add to PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
```

### Problem: Dependency conflicts
```
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed
```

**Solution:**
```bash
# Use a fresh virtual environment
python -m venv venv_fresh
source venv_fresh/bin/activate
pip install -r requirements.txt
```

## Data Loading Issues

### Problem: FileNotFoundError when loading data
```python
FileNotFoundError: [Errno 2] No such file or directory: 'data/raw/wfp/wfp_food_prices_yem.csv'
```

**Solution:**
```python
# Check if data has been downloaded
import os
from yemen_market.config import settings

# Download missing data
if not os.path.exists(settings.WFP_RAW_PATH):
    from yemen_market.data.hdx_client import HDXClient
    client = HDXClient()
    client.download_dataset('wfp-food-prices-for-yemen')
```

### Problem: Empty DataFrame after loading
```python
# DataFrame has 0 rows
df = wfp_processor.load_raw_data()
print(len(df))  # 0
```

**Solution:**
```python
# Check file encoding and format
import pandas as pd
import chardet

# Detect encoding
with open('data/raw/wfp/wfp_food_prices_yem.csv', 'rb') as f:
    result = chardet.detect(f.read())
    
# Load with correct encoding
df = pd.read_csv(
    'data/raw/wfp/wfp_food_prices_yem.csv',
    encoding=result['encoding']
)
```

## Panel Construction Issues

### Problem: Unbalanced panel warnings
```
UserWarning: Panel is unbalanced. Missing observations: 15%
```

**Solution:**
```python
from yemen_market.data.panel_builder import PanelBuilder

# Create balanced panel with interpolation
builder = PanelBuilder()
panel = builder.create_balanced_panel(
    start_date='2019-01-01',
    end_date='2023-12-31',
    interpolation_method='linear',
    max_gap=2  # Maximum months to interpolate
)

# Or require minimum observations
panel = builder.create_panel(
    min_observations_per_market=36,  # At least 3 years
    min_markets_per_commodity=10
)
```

### Problem: Duplicate entries in panel
```
ValueError: Index contains duplicate entries, cannot reshape
```

**Solution:**
```python
# Remove duplicates before panel construction
df_clean = df.drop_duplicates(
    subset=['market', 'commodity', 'date'],
    keep='last'  # Keep most recent observation
)

# Aggregate if multiple price types
df_agg = df.groupby(['market', 'commodity', 'date']).agg({
    'price': 'mean',
    'price_type': 'first',
    'currency': 'first'
}).reset_index()
```

## Model Fitting Issues

### Problem: Singular matrix in fixed effects model
```
LinAlgError: Singular matrix
```

**Solution:**
```python
from yemen_market.models.three_tier import Tier1PooledModel

# Check for multicollinearity
model = Tier1PooledModel()

# Use variance inflation factor
from statsmodels.stats.outliers_influence import variance_inflation_factor
vif = pd.DataFrame()
vif["variables"] = X.columns
vif["VIF"] = [variance_inflation_factor(X.values, i) 
              for i in range(X.shape[1])]

# Remove high VIF variables (>10)
high_vif = vif[vif['VIF'] > 10]['variables'].tolist()
X_reduced = X.drop(columns=high_vif)
```

### Problem: Memory error with large panels
```
MemoryError: Unable to allocate array with shape (1000000, 500)
```

**Solution:**
```python
# Use chunked processing
from yemen_market.models.three_tier import Tier1PooledModel

model = Tier1PooledModel(
    use_sparse_matrices=True,
    chunk_size=10000
)

# Or process by commodity
results = {}
for commodity in df['commodity'].unique():
    commodity_data = df[df['commodity'] == commodity]
    results[commodity] = model.fit(commodity_data)
```

## API and External Data Issues

### Problem: HDX API timeout
```
requests.exceptions.Timeout: Request timed out
```

**Solution:**
```python
from yemen_market.data.hdx_client import HDXClient
import time

# Implement retry logic
client = HDXClient()
max_retries = 3
retry_delay = 5

for attempt in range(max_retries):
    try:
        data = client.download_dataset('wfp-food-prices-for-yemen')
        break
    except Exception as e:
        if attempt < max_retries - 1:
            time.sleep(retry_delay * (attempt + 1))
            continue
        else:
            raise
```

### Problem: Rate limiting from external APIs
```
HTTPError: 429 Too Many Requests
```

**Solution:**
```python
# Use rate limiting
from yemen_market.utils.performance import rate_limit
import time

@rate_limit(calls=10, period=60)  # 10 calls per minute
def fetch_data(url):
    response = requests.get(url)
    return response.json()

# Or use exponential backoff
def fetch_with_backoff(url, max_retries=5):
    for i in range(max_retries):
        try:
            response = requests.get(url)
            response.raise_for_status()
            return response
        except requests.HTTPError as e:
            if e.response.status_code == 429:
                wait_time = 2 ** i  # Exponential backoff
                time.sleep(wait_time)
            else:
                raise
```

## Visualization Issues

### Problem: Plots not displaying in Jupyter
```python
# No output from plotting commands
model.plot_diagnostics()
```

**Solution:**
```python
# Enable inline plotting
%matplotlib inline

# Or use explicit backend
import matplotlib
matplotlib.use('TkAgg')  # or 'Qt5Agg'
import matplotlib.pyplot as plt

# Force display
plt.show()
```

### Problem: Maps showing incorrect boundaries
```
ValueError: Cannot find matching regions for markets
```

**Solution:**
```python
# Update spatial data
from yemen_market.data.spatial_joins import SpatialJoiner

joiner = SpatialJoiner()
joiner.download_boundaries(force=True)

# Verify market names match
markets_in_data = df['market'].unique()
markets_in_shapes = gdf['admin2_name'].unique()

# Find fuzzy matches
from fuzzywuzzy import process
unmatched = []
for market in markets_in_data:
    match, score = process.extractOne(market, markets_in_shapes)
    if score < 80:
        unmatched.append((market, match, score))
```

## Configuration Issues

### Problem: Settings not loading correctly
```python
AttributeError: 'Settings' object has no attribute 'WFP_RAW_PATH'
```

**Solution:**
```python
# Check environment variables
import os
print(os.environ.get('YEMEN_MARKET_ENV'))

# Load settings explicitly
from yemen_market.config import Settings
settings = Settings()

# Or use environment file
# Create .env file
with open('.env', 'w') as f:
    f.write('YEMEN_MARKET_ENV=development\n')
    f.write('DATA_DIR=/path/to/data\n')
```

## Testing Issues

### Problem: Tests failing due to missing fixtures
```
FileNotFoundError: tests/fixtures/sample_panel.csv
```

**Solution:**
```bash
# Generate test fixtures
python scripts/generate_test_fixtures.py

# Or create minimal fixtures
mkdir -p tests/fixtures
echo "market,commodity,date,price" > tests/fixtures/sample_panel.csv
echo "Sana'a,Wheat,2023-01-01,100" >> tests/fixtures/sample_panel.csv
```

### Problem: Integration tests timing out
```
TimeoutError: Test exceeded 300 second timeout
```

**Solution:**
```python
# Skip slow tests locally
import pytest

@pytest.mark.slow
def test_full_pipeline():
    pass

# Run without slow tests
# pytest -m "not slow"

# Or increase timeout
@pytest.mark.timeout(600)  # 10 minutes
def test_full_pipeline():
    pass
```