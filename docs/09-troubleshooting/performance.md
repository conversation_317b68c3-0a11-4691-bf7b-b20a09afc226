# Performance Optimization Guide

This guide covers performance issues, memory optimization, and computational efficiency improvements.

## Memory Issues

### Problem: Out of memory with large panels
```python
MemoryError: Unable to allocate 32.0 GiB for an array
```

**Solution:**
```python
import pandas as pd
import numpy as np
import gc

# Method 1: Use chunked processing
def process_large_panel_chunked(filepath, chunk_size=50000):
    """Process large datasets in chunks"""
    
    results = []
    for chunk in pd.read_csv(filepath, chunksize=chunk_size):
        # Process each chunk
        chunk_result = process_chunk(chunk)
        results.append(chunk_result)
        
        # Explicit garbage collection
        del chunk
        gc.collect()
    
    # Combine results
    final_result = pd.concat(results, ignore_index=True)
    return final_result

# Method 2: Use efficient data types
def optimize_dataframe_dtypes(df):
    """Reduce memory usage by optimizing data types"""
    
    initial_memory = df.memory_usage().sum() / 1024**2  # MB
    
    # Optimize numeric columns
    for col in df.select_dtypes(include=['int']).columns:
        df[col] = pd.to_numeric(df[col], downcast='integer')
    
    for col in df.select_dtypes(include=['float']).columns:
        df[col] = pd.to_numeric(df[col], downcast='float')
    
    # Convert strings to categories
    for col in df.select_dtypes(include=['object']).columns:
        num_unique = df[col].nunique()
        num_total = len(df[col])
        if num_unique / num_total < 0.5:  # Less than 50% unique
            df[col] = df[col].astype('category')
    
    final_memory = df.memory_usage().sum() / 1024**2  # MB
    print(f"Memory reduced from {initial_memory:.2f} MB to {final_memory:.2f} MB "
          f"({(1 - final_memory/initial_memory)*100:.1f}% reduction)")
    
    return df

# Method 3: Use Polars for large datasets
import polars as pl

def process_with_polars(filepath):
    """Use Polars for memory-efficient processing"""
    
    # Polars uses lazy evaluation and columnar storage
    df = pl.scan_csv(filepath)  # Lazy loading
    
    # Define operations (not executed yet)
    result = (
        df.filter(pl.col("price") > 0)
        .groupby(["market", "commodity"])
        .agg([
            pl.col("price").mean().alias("avg_price"),
            pl.col("price").std().alias("price_std"),
            pl.count().alias("n_obs")
        ])
    )
    
    # Execute and collect results
    return result.collect()
```

### Problem: Memory leaks in long-running processes
```python
# Memory usage keeps growing during analysis
```

**Solution:**
```python
import tracemalloc
import gc
from functools import wraps

# Memory profiling decorator
def profile_memory(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        tracemalloc.start()
        
        result = func(*args, **kwargs)
        
        current, peak = tracemalloc.get_traced_memory()
        print(f"{func.__name__} memory usage:")
        print(f"  Current: {current / 1024**2:.2f} MB")
        print(f"  Peak: {peak / 1024**2:.2f} MB")
        
        tracemalloc.stop()
        return result
    return wrapper

# Clear references in loops
def analyze_markets_memory_safe(markets, data):
    results = {}
    
    for market in markets:
        # Process market
        market_data = data[data['market'] == market].copy()
        result = analyze_market(market_data)
        results[market] = result
        
        # Clear references
        del market_data
        gc.collect()
    
    return results

# Use context managers for resources
class DataProcessor:
    def __enter__(self):
        self.data = load_large_dataset()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        del self.data
        gc.collect()
    
    def process(self):
        # Processing logic
        pass

# Usage
with DataProcessor() as processor:
    results = processor.process()
# Memory automatically cleaned up
```

## Computation Speed Issues

### Problem: Panel regression taking hours
```python
# Fixed effects model with 1000 markets takes 3+ hours
```

**Solution:**
```python
# Method 1: Use optimized implementations
from linearmodels import PanelOLS
import numpy as np

def fast_panel_regression(data, y_var, x_vars):
    """Use linearmodels for fast panel regression"""
    
    # Set multi-index
    data = data.set_index(['market', 'date'])
    
    # Use PanelOLS (optimized C implementation)
    model = PanelOLS(
        dependent=data[y_var],
        exog=data[x_vars],
        entity_effects=True,
        time_effects=True,
        drop_absorbed=True  # Drop absorbed variables to save memory
    )
    
    # Fit with optimized covariance
    results = model.fit(
        cov_type='clustered',
        cluster_entity=True,
        debiased=False  # Faster without debiasing
    )
    
    return results

# Method 2: Parallel processing for by-group analysis
from joblib import Parallel, delayed
import multiprocessing

def parallel_commodity_analysis(data, commodities, n_jobs=-1):
    """Run commodity-specific models in parallel"""
    
    if n_jobs == -1:
        n_jobs = multiprocessing.cpu_count()
    
    def analyze_commodity(commodity):
        commodity_data = data[data['commodity'] == commodity]
        model = fit_model(commodity_data)
        return commodity, model.params
    
    # Run in parallel
    results = Parallel(n_jobs=n_jobs)(
        delayed(analyze_commodity)(c) for c in commodities
    )
    
    return dict(results)

# Method 3: Use GPU acceleration
try:
    import cupy as cp
    import cudf
    
    def gpu_accelerated_regression(X, y):
        """Use GPU for matrix operations"""
        
        # Transfer to GPU
        X_gpu = cp.asarray(X)
        y_gpu = cp.asarray(y)
        
        # Solve normal equations on GPU
        XtX = cp.dot(X_gpu.T, X_gpu)
        Xty = cp.dot(X_gpu.T, y_gpu)
        beta = cp.linalg.solve(XtX, Xty)
        
        # Transfer back to CPU
        return cp.asnumpy(beta)
except ImportError:
    print("GPU acceleration not available")
```

### Problem: Slow data transformations
```python
# Creating lagged variables takes forever
```

**Solution:**
```python
# Method 1: Vectorized operations
def create_lags_vectorized(df, columns, lags):
    """Efficiently create lagged variables"""
    
    # Sort once
    df = df.sort_values(['market', 'commodity', 'date'])
    
    # Create all lags at once
    for col in columns:
        for lag in lags:
            df[f'{col}_lag{lag}'] = df.groupby(
                ['market', 'commodity']
            )[col].shift(lag)
    
    return df

# Method 2: Use numba for custom operations
import numba

@numba.jit(nopython=True)
def rolling_window_numba(arr, window):
    """Fast rolling window calculations"""
    n = len(arr)
    result = np.zeros(n)
    result[:window-1] = np.nan
    
    for i in range(window-1, n):
        result[i] = np.mean(arr[i-window+1:i+1])
    
    return result

# Method 3: Use Polars for transformations
import polars as pl

def fast_transformations_polars(df):
    """Use Polars for fast data transformations"""
    
    # Convert to Polars
    df_pl = pl.from_pandas(df)
    
    # Chain transformations efficiently
    result = (
        df_pl.lazy()  # Lazy evaluation
        .sort(['market', 'commodity', 'date'])
        .groupby(['market', 'commodity'])
        .agg([
            # Moving averages
            pl.col('price').rolling_mean(window_size=3).alias('price_ma3'),
            pl.col('price').rolling_mean(window_size=12).alias('price_ma12'),
            
            # Lags
            pl.col('price').shift(1).alias('price_lag1'),
            pl.col('price').shift(12).alias('price_lag12'),
            
            # Cumulative stats
            pl.col('price').cum_sum().alias('price_cumsum'),
            pl.col('price').cum_max().alias('price_cummax')
        ])
        .collect()  # Execute
    )
    
    return result.to_pandas()
```

## I/O Performance

### Problem: Slow data loading
```python
# Reading large CSV takes 10+ minutes
```

**Solution:**
```python
# Method 1: Use efficient file formats
def convert_to_parquet(csv_path, parquet_path):
    """Convert CSV to Parquet for faster I/O"""
    
    # Read CSV in chunks
    chunks = []
    for chunk in pd.read_csv(csv_path, chunksize=100000):
        chunks.append(chunk)
    
    # Combine and save as Parquet
    df = pd.concat(chunks, ignore_index=True)
    df.to_parquet(parquet_path, compression='snappy')
    
    print(f"File size reduced from {os.path.getsize(csv_path)/1024**2:.1f} MB "
          f"to {os.path.getsize(parquet_path)/1024**2:.1f} MB")

# Method 2: Use data caching
from functools import lru_cache
import pickle

class DataCache:
    def __init__(self, cache_dir='cache'):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
    
    def get_or_compute(self, key, compute_func, *args, **kwargs):
        """Cache expensive computations"""
        cache_path = os.path.join(self.cache_dir, f"{key}.pkl")
        
        if os.path.exists(cache_path):
            print(f"Loading from cache: {key}")
            with open(cache_path, 'rb') as f:
                return pickle.load(f)
        
        print(f"Computing: {key}")
        result = compute_func(*args, **kwargs)
        
        with open(cache_path, 'wb') as f:
            pickle.dump(result, f)
        
        return result

# Usage
cache = DataCache()
processed_data = cache.get_or_compute(
    'processed_panel_2023',
    process_raw_data,
    'data/raw/prices_2023.csv'
)

# Method 3: Parallel I/O for multiple files
from concurrent.futures import ThreadPoolExecutor

def load_multiple_files_parallel(file_paths):
    """Load multiple files in parallel"""
    
    def load_file(path):
        if path.endswith('.parquet'):
            return pd.read_parquet(path)
        elif path.endswith('.csv'):
            return pd.read_csv(path)
        else:
            raise ValueError(f"Unknown file type: {path}")
    
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = {executor.submit(load_file, path): path 
                  for path in file_paths}
        
        dataframes = {}
        for future in futures:
            path = futures[future]
            try:
                dataframes[path] = future.result()
            except Exception as e:
                print(f"Error loading {path}: {e}")
    
    return dataframes
```

## Database Performance

### Problem: Slow database queries
```python
# Fetching price data from PostgreSQL takes minutes
```

**Solution:**
```python
import psycopg2
from psycopg2.extras import execute_values

# Method 1: Use indexes effectively
def create_indexes(conn):
    """Create indexes for common queries"""
    
    queries = [
        "CREATE INDEX idx_prices_market_date ON prices(market, date);",
        "CREATE INDEX idx_prices_commodity_date ON prices(commodity, date);",
        "CREATE INDEX idx_prices_date ON prices(date);",
        "CREATE INDEX idx_conflict_admin2_date ON conflict(admin2, date);"
    ]
    
    with conn.cursor() as cur:
        for query in queries:
            try:
                cur.execute(query)
                conn.commit()
                print(f"Created index: {query.split()[2]}")
            except psycopg2.errors.DuplicateTable:
                conn.rollback()
                print(f"Index already exists: {query.split()[2]}")

# Method 2: Batch inserts
def batch_insert_prices(conn, prices_df, batch_size=1000):
    """Efficiently insert large amounts of data"""
    
    with conn.cursor() as cur:
        # Prepare data
        data = [tuple(row) for row in prices_df.values]
        
        # Use execute_values for fast bulk insert
        query = """
            INSERT INTO prices (market, commodity, date, price, currency)
            VALUES %s
            ON CONFLICT (market, commodity, date) 
            DO UPDATE SET price = EXCLUDED.price;
        """
        
        for i in range(0, len(data), batch_size):
            batch = data[i:i+batch_size]
            execute_values(cur, query, batch)
            conn.commit()
            print(f"Inserted batch {i//batch_size + 1}")

# Method 3: Use materialized views
def create_materialized_views(conn):
    """Create materialized views for complex queries"""
    
    queries = {
        'monthly_avg_prices': """
            CREATE MATERIALIZED VIEW monthly_avg_prices AS
            SELECT 
                market,
                commodity,
                DATE_TRUNC('month', date) as month,
                AVG(price) as avg_price,
                COUNT(*) as n_obs
            FROM prices
            GROUP BY market, commodity, DATE_TRUNC('month', date);
        """,
        
        'market_stats': """
            CREATE MATERIALIZED VIEW market_stats AS
            SELECT 
                market,
                commodity,
                MIN(date) as first_obs,
                MAX(date) as last_obs,
                COUNT(*) as total_obs,
                AVG(price) as avg_price,
                STDDEV(price) as price_std
            FROM prices
            GROUP BY market, commodity;
        """
    }
    
    with conn.cursor() as cur:
        for name, query in queries.items():
            try:
                cur.execute(f"DROP MATERIALIZED VIEW IF EXISTS {name};")
                cur.execute(query)
                cur.execute(f"CREATE INDEX idx_{name}_market ON {name}(market);")
                conn.commit()
                print(f"Created materialized view: {name}")
            except Exception as e:
                conn.rollback()
                print(f"Error creating view {name}: {e}")
```

## Profiling and Optimization

### Problem: Not sure what's slow
```python
# Analysis takes long but unclear why
```

**Solution:**
```python
import cProfile
import pstats
from line_profiler import LineProfiler
import time

# Method 1: Function profiling
def profile_analysis():
    """Profile entire analysis pipeline"""
    
    profiler = cProfile.Profile()
    profiler.enable()
    
    # Run analysis
    results = run_full_analysis()
    
    profiler.disable()
    
    # Print stats
    stats = pstats.Stats(profiler)
    stats.strip_dirs()
    stats.sort_stats('cumulative')
    stats.print_stats(20)  # Top 20 functions
    
    return results

# Method 2: Line-by-line profiling
def profile_critical_function():
    """Profile specific function line by line"""
    
    lp = LineProfiler()
    lp.add_function(process_panel_data)  # Function to profile
    
    # Run with profiler
    lp_wrapper = lp(process_panel_data)
    result = lp_wrapper(data)
    
    # Print results
    lp.print_stats()
    
    return result

# Method 3: Memory profiling
from memory_profiler import profile

@profile
def memory_intensive_function(data):
    """Track memory usage line by line"""
    
    # Process data
    transformed = data.copy()  # Memory spike here
    transformed['new_col'] = transformed['price'] * 2
    
    # More operations
    grouped = transformed.groupby('market').mean()
    
    return grouped

# Method 4: Custom timing decorator
def timeit(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        print(f"{func.__name__} took {end - start:.2f} seconds")
        return result
    return wrapper

@timeit
def slow_function():
    # Function code
    pass
```