# Data Issues Troubleshooting

This guide covers data-specific problems including loading, processing, validation, and quality issues.

## Data Quality Issues

### Problem: Extreme outliers in price data
```python
# Prices ranging from 0.01 to 1,000,000
df['price'].describe()
# min: 0.01, max: 1000000.0
```

**Solution:**
```python
import numpy as np
from scipy import stats

# Method 1: IQR-based outlier removal
Q1 = df['price'].quantile(0.25)
Q3 = df['price'].quantile(0.75)
IQR = Q3 - Q1
lower_bound = Q1 - 1.5 * IQR
upper_bound = Q3 + 1.5 * IQR

df_clean = df[(df['price'] >= lower_bound) & (df['price'] <= upper_bound)]

# Method 2: Z-score based (for normally distributed data)
df['z_score'] = np.abs(stats.zscore(df['price']))
df_clean = df[df['z_score'] < 3]

# Method 3: Domain-specific bounds
# Based on Yemen market knowledge
price_bounds = {
    'Wheat': (50, 500),
    'Rice': (100, 800),
    'Fuel (Petrol-Gasoline)': (200, 2000)
}

for commodity, (min_p, max_p) in price_bounds.items():
    mask = df['commodity'] == commodity
    df.loc[mask & (df['price'] < min_p), 'price'] = np.nan
    df.loc[mask & (df['price'] > max_p), 'price'] = np.nan
```

### Problem: Currency confusion (YER vs USD)
```python
# Mixed currencies in same dataset
df['currency'].value_counts()
# YER: 50000, USD: 30000
```

**Solution:**
```python
from yemen_market.data.wfp_processor import WFPProcessor

# Convert all to common currency
processor = WFPProcessor()

# Method 1: Use official exchange rates
df_usd = processor.convert_to_usd(
    df,
    exchange_rate_source='official'
)

# Method 2: Use parallel market rates
exchange_rates = pd.read_csv('data/raw/exchange_rates.csv')
df_merged = df.merge(
    exchange_rates,
    on=['date', 'region'],
    how='left'
)

# Convert YER to USD
mask = df_merged['currency'] == 'YER'
df_merged.loc[mask, 'price_usd'] = (
    df_merged.loc[mask, 'price'] / 
    df_merged.loc[mask, 'parallel_rate']
)
df_merged.loc[~mask, 'price_usd'] = df_merged.loc[~mask, 'price']
```

### Problem: Inconsistent market names
```python
# Same market with different spellings
df['market'].value_counts()
# Sana'a: 1000, Sanaa: 500, Sana: 200
```

**Solution:**
```python
from fuzzywuzzy import process
import pandas as pd

# Create mapping of variations
market_mapping = {
    "Sana'a": ["Sanaa", "Sana", "Sana'a City", "Amanat Al Asimah"],
    "Aden": ["Adan", "Aden City", "Aden Gov"],
    "Ta'iz": ["Taiz", "Taizz", "Ta'izz"]
}

# Apply mapping
def standardize_market(market_name):
    for standard, variations in market_mapping.items():
        if market_name in variations or market_name == standard:
            return standard
    # Fuzzy match for unmapped markets
    matches = process.extract(market_name, market_mapping.keys(), limit=1)
    if matches[0][1] > 80:  # 80% similarity threshold
        return matches[0][0]
    return market_name

df['market_clean'] = df['market'].apply(standardize_market)
```

### Problem: Missing temporal coverage
```python
# Gaps in time series
market_coverage = df.groupby('market')['date'].agg(['min', 'max', 'count'])
# Many markets missing months
```

**Solution:**
```python
from yemen_market.data.panel_builder import PanelBuilder

# Method 1: Forward fill for small gaps
def fill_temporal_gaps(df, max_gap_months=2):
    # Create complete date range
    date_range = pd.date_range(
        start=df['date'].min(),
        end=df['date'].max(),
        freq='MS'  # Month start
    )
    
    # Reindex each market-commodity pair
    filled_dfs = []
    for (market, commodity), group in df.groupby(['market', 'commodity']):
        group_indexed = group.set_index('date').reindex(date_range)
        
        # Fill small gaps
        group_indexed = group_indexed.fillna(method='ffill', limit=max_gap_months)
        
        # Add back identifiers
        group_indexed['market'] = market
        group_indexed['commodity'] = commodity
        filled_dfs.append(group_indexed.reset_index())
    
    return pd.concat(filled_dfs, ignore_index=True)

# Method 2: Interpolation for price trends
def interpolate_prices(df, method='linear'):
    df_sorted = df.sort_values(['market', 'commodity', 'date'])
    df_sorted['price_interpolated'] = df_sorted.groupby(
        ['market', 'commodity']
    )['price'].transform(
        lambda x: x.interpolate(method=method, limit_direction='both')
    )
    return df_sorted
```

### Problem: Conflict data spatial mismatch
```python
# ACLED events don't match market locations
merge_result = pd.merge(
    price_df, 
    conflict_df, 
    on=['admin2', 'date'], 
    how='left'
)
# 70% of prices have no conflict data
```

**Solution:**
```python
from yemen_market.data.spatial_joins import SpatialJoiner
import geopandas as gpd

# Use spatial buffer for conflict assignment
joiner = SpatialJoiner()

# Method 1: Buffer around markets (e.g., 25km radius)
market_gdf = gpd.GeoDataFrame(
    markets,
    geometry=gpd.points_from_xy(markets.longitude, markets.latitude)
)
market_buffers = market_gdf.copy()
market_buffers['geometry'] = market_buffers.buffer(0.25)  # ~25km

# Spatial join conflict events
conflict_gdf = gpd.GeoDataFrame(
    conflict_df,
    geometry=gpd.points_from_xy(conflict_df.longitude, conflict_df.latitude)
)
markets_with_conflict = gpd.sjoin(
    market_buffers,
    conflict_gdf,
    how='left',
    predicate='contains'
)

# Method 2: Use district-level aggregation
conflict_by_district = conflict_df.groupby(
    ['admin2', 'year', 'month']
).agg({
    'events': 'sum',
    'fatalities': 'sum'
}).reset_index()
```

### Problem: Seasonal patterns obscuring trends
```python
# Strong Ramadan effects in food prices
# Harvest seasons affecting grain prices
```

**Solution:**
```python
from statsmodels.tsa.seasonal import seasonal_decompose
import pandas as pd

# Method 1: Seasonal decomposition
def deseasonalize_prices(df, commodity):
    commodity_df = df[df['commodity'] == commodity].copy()
    commodity_df['date'] = pd.to_datetime(commodity_df['date'])
    commodity_df = commodity_df.set_index('date').sort_index()
    
    # Ensure regular frequency
    commodity_df = commodity_df.asfreq('MS')
    
    # Decompose
    decomposition = seasonal_decompose(
        commodity_df['price'],
        model='multiplicative',
        period=12
    )
    
    # Remove seasonal component
    commodity_df['price_deseasonalized'] = (
        commodity_df['price'] / decomposition.seasonal
    )
    
    return commodity_df

# Method 2: Include seasonal dummies
def add_seasonal_features(df):
    df['month'] = pd.to_datetime(df['date']).dt.month
    df['is_ramadan'] = df['date'].apply(
        lambda x: is_ramadan_period(x)  # Custom function
    )
    df['is_harvest'] = df['month'].isin([4, 5, 10, 11])  # Yemen harvest months
    
    # Create month dummies
    month_dummies = pd.get_dummies(df['month'], prefix='month')
    df = pd.concat([df, month_dummies], axis=1)
    
    return df
```

### Problem: Unit inconsistencies
```python
# Prices for different unit sizes
df[df['commodity'] == 'Rice']['unit'].value_counts()
# KG: 5000, 50 KG: 2000, MT: 100
```

**Solution:**
```python
# Standardize to price per KG
unit_conversions = {
    'KG': 1,
    '50 KG': 50,
    'MT': 1000,
    'LT': 1,
    'GAL': 3.78541,  # Gallons to liters for fuel
    '100 G': 0.1,
    'EA': None  # Each - cannot convert
}

def standardize_units(row):
    if row['unit'] in unit_conversions:
        conversion = unit_conversions[row['unit']]
        if conversion:
            return row['price'] / conversion
    return np.nan

df['price_per_kg'] = df.apply(standardize_units, axis=1)

# Remove non-convertible units
df_clean = df.dropna(subset=['price_per_kg'])
```

### Problem: Data source integration conflicts
```python
# WFP and FAO report different prices for same market/date
wfp_price = 150  # USD/MT
fao_price = 180  # USD/MT
```

**Solution:**
```python
# Method 1: Prioritize by data quality
def integrate_price_sources(wfp_df, fao_df, acaps_df):
    # Define priority order based on data quality assessment
    priority = ['wfp', 'fao', 'acaps']
    
    # Merge all sources
    all_data = pd.concat([
        wfp_df.assign(source='wfp'),
        fao_df.assign(source='fao'),
        acaps_df.assign(source='acaps')
    ])
    
    # Sort by priority
    all_data['priority'] = all_data['source'].map(
        {s: i for i, s in enumerate(priority)}
    )
    all_data = all_data.sort_values('priority')
    
    # Keep first (highest priority) observation
    integrated = all_data.drop_duplicates(
        subset=['market', 'commodity', 'date'],
        keep='first'
    )
    
    return integrated

# Method 2: Average if sources are reliable
def average_reliable_sources(df, reliability_threshold=0.1):
    grouped = df.groupby(['market', 'commodity', 'date'])
    
    def smart_average(group):
        prices = group['price'].values
        # Check if prices are within 10% of each other
        if (prices.max() - prices.min()) / prices.mean() < reliability_threshold:
            return pd.Series({
                'price': prices.mean(),
                'source': 'integrated',
                'n_sources': len(prices)
            })
        else:
            # Too much variation - need manual review
            return pd.Series({
                'price': np.nan,
                'source': 'conflict',
                'n_sources': len(prices)
            })
    
    return grouped.apply(smart_average).reset_index()
```

### Problem: Geographic coordinate issues
```python
# Markets with invalid or missing coordinates
df[['latitude', 'longitude']].isnull().sum()
# latitude: 500, longitude: 500
```

**Solution:**
```python
# Load reference coordinates
market_coords = pd.read_csv('data/external/yemen_markets_coordinates.csv')

# Method 1: Merge with reference
df_with_coords = df.merge(
    market_coords[['market', 'latitude', 'longitude']],
    on='market',
    how='left',
    suffixes=('', '_ref')
)

# Fill missing with reference
df_with_coords['latitude'] = df_with_coords['latitude'].fillna(
    df_with_coords['latitude_ref']
)
df_with_coords['longitude'] = df_with_coords['longitude'].fillna(
    df_with_coords['longitude_ref']
)

# Method 2: Geocode missing locations
from geopy.geocoders import Nominatim
geolocator = Nominatim(user_agent="yemen_market_analysis")

def geocode_market(market_name, country='Yemen'):
    try:
        location = geolocator.geocode(f"{market_name}, {country}")
        if location:
            return location.latitude, location.longitude
    except:
        pass
    return None, None

# Apply to missing coordinates
missing_mask = df['latitude'].isnull()
df.loc[missing_mask, ['latitude', 'longitude']] = df.loc[
    missing_mask, 'market'
].apply(lambda x: pd.Series(geocode_market(x)))
```