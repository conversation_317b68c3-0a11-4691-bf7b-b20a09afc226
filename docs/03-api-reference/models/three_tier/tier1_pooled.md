# Tier 1: Pooled Panel Models API Reference

**Target Audience**: Econometricians, Researchers  
**Module**: `yemen_market.models.three_tier.tier1_pooled`

## Overview

Tier 1 implements pooled panel regression models with multi-way fixed effects and robust standard errors. This tier captures overall market integration patterns across all commodities while accounting for cross-sectional dependence and serial correlation.

## Classes

### PooledPanelModel

```python
class PooledPanelModel(BaseThreeTierModel):
    """Pooled panel regression with fixed effects and robust standard errors."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize pooled panel model with configuration."""
```

#### Configuration

```python
config = {
    # Data specification
    'outcome_var': 'log_price',              # Dependent variable
    'treatment_var': 'conflict_intensity',    # Main variable of interest
    'control_vars': [                         # Control variables
        'rainfall', 'temperature',
        'log_global_price', 'exchange_rate'
    ],
    
    # Fixed effects specification
    'fixed_effects': ['market_id', 'commodity', 'month'],
    'time_effects': True,                     # Include time FE
    'entity_effects': True,                   # Include entity FE
    'two_way': True,                         # Two-way FE
    
    # Standard errors
    'cluster_var': 'market_id',              # Clustering variable
    'cov_type': 'kernel',                    # Driscoll-Kraay
    'kernel_bandwidth': 3,                   # Bandwidth for kernel
    
    # Model options
    'drop_singletons': True,                 # Drop singleton groups
    'check_rank': True,                      # Check for multicollinearity
    'weighted': False,                       # Use weights
    'weights_var': None                      # Weight variable
}
```

## Core Methods

### fit

```python
def fit(
    self,
    data: pd.DataFrame,
    formula: Optional[str] = None
) -> ResultsContainer:
    """Estimate pooled panel model with fixed effects."""
```

#### Parameters
- **data** (`pd.DataFrame`): Panel data with required columns
- **formula** (`str`, optional): Patsy formula override

#### Returns
- **ResultsContainer**: Standardized results object

#### Example
```python
model = PooledPanelModel(config)
results = model.fit(panel_data)

print(f"Conflict effect: {results.coefficients.loc['conflict_intensity', 'coef']:.3f}")
print(f"Robust SE: {results.standard_errors.loc['conflict_intensity', 'se']:.3f}")
print(f"P-value: {results.pvalues.loc['conflict_intensity', 'pvalue']:.4f}")
```

### predict

```python
def predict(
    self,
    data: pd.DataFrame,
    include_fe: bool = True
) -> pd.Series:
    """Generate predictions from fitted model."""
```

#### Parameters
- **data** (`pd.DataFrame`): Data for prediction
- **include_fe** (`bool`): Include fixed effects in predictions

#### Returns
- **pd.Series**: Predicted values

### get_coefficients

```python
def get_coefficients(
    self,
    variable: Optional[str] = None
) -> Union[pd.DataFrame, float]:
    """Extract coefficient estimates."""
```

Returns coefficient estimates with confidence intervals.

### test_hypotheses

```python
def test_hypotheses(
    self,
    restrictions: Union[str, np.ndarray]
) -> Dict[str, Any]:
    """Test linear restrictions on coefficients."""
```

Performs Wald tests on coefficient restrictions.

## Fixed Effects Utilities

### FixedEffectsTransformer

```python
class FixedEffectsTransformer:
    """Apply within-transformation for fixed effects."""
    
    def fit_transform(
        self,
        X: np.ndarray,
        groups: np.ndarray
    ) -> np.ndarray:
        """Demean variables within groups."""
```

Handles the within-transformation for fixed effects estimation.

### Functions

#### absorb_fixed_effects

```python
def absorb_fixed_effects(
    data: pd.DataFrame,
    outcome_var: str,
    regressors: List[str],
    fe_vars: List[str]
) -> Tuple[np.ndarray, np.ndarray]:
    """Absorb fixed effects using within-transformation."""
```

Efficiently absorbs high-dimensional fixed effects.

#### create_fe_dummies

```python
def create_fe_dummies(
    data: pd.DataFrame,
    fe_vars: List[str],
    drop_singletons: bool = True
) -> pd.DataFrame:
    """Create dummy variables for fixed effects."""
```

Generates dummy variables when explicit FE are needed.

## Standard Error Calculations

### DriscollKraayCovariance

```python
class DriscollKraayCovariance:
    """Calculate Driscoll-Kraay standard errors."""
    
    def __init__(
        self,
        bandwidth: int = 3,
        kernel: str = 'bartlett'
    ):
        """Initialize with bandwidth and kernel type."""
    
    def calculate(
        self,
        residuals: np.ndarray,
        X: np.ndarray,
        time_var: np.ndarray,
        entity_var: np.ndarray
    ) -> np.ndarray:
        """Calculate robust covariance matrix."""
```

Implements Driscoll-Kraay standard errors robust to:
- Cross-sectional dependence
- Serial correlation
- Heteroskedasticity

### Functions

#### calculate_clustered_se

```python
def calculate_clustered_se(
    residuals: np.ndarray,
    X: np.ndarray,
    clusters: np.ndarray
) -> np.ndarray:
    """Calculate cluster-robust standard errors."""
```

#### calculate_robust_se

```python
def calculate_robust_se(
    residuals: np.ndarray,
    X: np.ndarray,
    cov_type: str = 'HC1'
) -> np.ndarray:
    """Calculate heteroskedasticity-robust standard errors."""
```

Supports HC0, HC1, HC2, HC3 corrections.

## Time-Varying Coefficients

### TimeVaryingPooledModel

```python
class TimeVaryingPooledModel(PooledPanelModel):
    """Pooled panel model with time-varying coefficients."""
    
    def fit(
        self,
        data: pd.DataFrame,
        time_interact_vars: List[str]
    ) -> ResultsContainer:
        """Estimate model with time interactions."""
```

Allows coefficients to vary over time through interactions.

#### Example
```python
# Test if conflict effect changes over time
time_model = TimeVaryingPooledModel(config)
results = time_model.fit(
    data,
    time_interact_vars=['conflict_intensity']
)

# Plot time-varying coefficients
time_effects = results.get_time_varying_effects('conflict_intensity')
time_effects.plot(title='Conflict Effect Over Time')
```

## Results and Diagnostics

### Results Structure

```python
@dataclass
class Tier1Results(ResultsContainer):
    """Extended results for Tier 1 models."""
    
    # Basic results
    coefficients: pd.DataFrame      # Coefficient estimates
    standard_errors: pd.DataFrame   # Robust standard errors
    pvalues: pd.DataFrame          # P-values
    conf_int: pd.DataFrame         # Confidence intervals
    
    # Model fit
    rsquared: float                # R-squared
    rsquared_within: float         # Within R-squared
    rsquared_between: float        # Between R-squared
    rsquared_overall: float        # Overall R-squared
    
    # Fixed effects
    n_groups: Dict[str, int]       # Number of groups per FE
    fe_removed: Dict[str, int]     # Singletons removed
    
    # Diagnostics
    serial_correlation: Dict       # Wooldridge test
    cross_sectional_dep: Dict      # Pesaran CD test
    heteroskedasticity: Dict       # Modified Wald test
```

### Summary Methods

```python
# Generate regression table
summary = results.summary()
print(summary)

# LaTeX output
latex_table = results.to_latex(
    caption="Pooled Panel Regression Results",
    label="tab:tier1",
    star_levels={0.01: '***', 0.05: '**', 0.1: '*'}
)

# Export to Excel
results.to_excel('tier1_results.xlsx')
```

## Complete Example

```python
from yemen_market.models.three_tier.tier1_pooled import PooledPanelModel
import pandas as pd
import numpy as np

# Load and prepare data
data = pd.read_parquet('data/processed/panel.parquet')

# Configure model
config = {
    'outcome_var': 'log_price',
    'treatment_var': 'conflict_intensity',
    'control_vars': [
        'log_global_price',
        'exchange_rate',
        'rainfall_deviation',
        'temperature_deviation'
    ],
    'fixed_effects': ['market_id', 'commodity', 'month'],
    'cluster_var': 'market_id',
    'cov_type': 'kernel',  # Driscoll-Kraay
    'kernel_bandwidth': 3
}

# Initialize and fit model
model = PooledPanelModel(config)
results = model.fit(data)

# Examine results
print("\n=== TIER 1: POOLED PANEL RESULTS ===")
print(f"\nObservations: {results.nobs:,}")
print(f"Markets: {results.n_groups['market_id']}")
print(f"Commodities: {results.n_groups['commodity']}")
print(f"Time periods: {results.n_groups['date']}")

print("\nMain Results:")
print(f"Conflict effect: {results.coefficients.loc['conflict_intensity', 'coef']:.4f}")
print(f"Standard error: {results.standard_errors.loc['conflict_intensity', 'se']:.4f}")
print(f"P-value: {results.pvalues.loc['conflict_intensity', 'pvalue']:.4f}")
print(f"95% CI: [{results.conf_int.loc['conflict_intensity', 'lower']:.4f}, "
      f"{results.conf_int.loc['conflict_intensity', 'upper']:.4f}]")

print(f"\nWithin R²: {results.rsquared_within:.3f}")
print(f"Overall R²: {results.rsquared_overall:.3f}")

# Diagnostic tests
print("\nDiagnostic Tests:")
if results.diagnostics['serial_correlation']['p_value'] < 0.05:
    print("⚠️  Serial correlation detected (Wooldridge test)")
if results.diagnostics['cross_sectional_dep']['p_value'] < 0.05:
    print("⚠️  Cross-sectional dependence detected (Pesaran CD test)")
if results.diagnostics['heteroskedasticity']['p_value'] < 0.05:
    print("✓ Heteroskedasticity detected (but using robust SE)")

# Test economic hypotheses
print("\nHypothesis Tests:")

# Test 1: Is conflict effect statistically significant?
print(f"H0: β_conflict = 0")
print(f"Result: {'Reject' if results.pvalues.loc['conflict_intensity', 'pvalue'] < 0.05 else 'Fail to reject'}")

# Test 2: Joint significance of weather controls
weather_vars = ['rainfall_deviation', 'temperature_deviation']
wald_test = model.test_hypotheses(f"{' = '.join(weather_vars)} = 0")
print(f"\nH0: Weather variables jointly = 0")
print(f"F-statistic: {wald_test['statistic']:.2f}")
print(f"P-value: {wald_test['p_value']:.4f}")

# Generate predictions
predictions = model.predict(data)
residuals = data[config['outcome_var']] - predictions

# Plot residuals
import matplotlib.pyplot as plt

fig, axes = plt.subplots(2, 2, figsize=(12, 10))

# Residual distribution
axes[0, 0].hist(residuals, bins=50, edgecolor='black')
axes[0, 0].set_title('Residual Distribution')
axes[0, 0].set_xlabel('Residuals')

# Q-Q plot
from scipy import stats
stats.probplot(residuals, dist="norm", plot=axes[0, 1])
axes[0, 1].set_title('Q-Q Plot')

# Residuals over time
time_data = data.groupby('date')[config['outcome_var']].mean()
time_resid = data.groupby('date').apply(lambda x: residuals.loc[x.index].mean())
axes[1, 0].plot(time_data.index, time_resid.values)
axes[1, 0].set_title('Average Residuals Over Time')
axes[1, 0].set_xlabel('Date')

# Residuals vs fitted
axes[1, 1].scatter(predictions, residuals, alpha=0.5)
axes[1, 1].axhline(y=0, color='r', linestyle='--')
axes[1, 1].set_title('Residuals vs Fitted Values')
axes[1, 1].set_xlabel('Fitted Values')
axes[1, 1].set_ylabel('Residuals')

plt.tight_layout()
plt.savefig('results/tier1_diagnostics.png')

# Save results
results.to_csv('results/tier1_coefficients.csv')
results.save('results/tier1_full_results.pkl')
print("\nResults saved to results/tier1_full_results.pkl")
```

## Common Issues and Solutions

### Multicollinearity
```python
# Check VIF before fitting
from statsmodels.stats.outliers_influence import variance_inflation_factor

X = data[config['control_vars']]
vif = pd.DataFrame()
vif["Variable"] = X.columns
vif["VIF"] = [variance_inflation_factor(X.values, i) for i in range(X.shape[1])]
print(vif[vif["VIF"] > 10])  # Flag high VIF variables
```

### Convergence Issues
```python
# Use regularization for difficult cases
model = PooledPanelModel(config)
model.fit_options = {
    'maxiter': 1000,
    'tolerance': 1e-8,
    'regularization': 0.01  # L2 penalty
}
```

### Memory Management
```python
# For large panels, use chunking
model = PooledPanelModel(config)
model.use_chunking = True
model.chunk_size = 100000
results = model.fit(data)
```

## See Also

- [`tier2_commodity.md`](tier2_commodity.md) - Commodity-specific models
- [`tier3_validation.md`](tier3_validation.md) - Validation models
- [`../diagnostics/`](../README.md#diagnostics) - Diagnostic tests
- [User Guide: Panel Models](../../../02-user-guides/running-analyses.md#tier1)
- [Methodology: Fixed Effects](../../../05-methodology/econometric-models/fixed-effects.md)