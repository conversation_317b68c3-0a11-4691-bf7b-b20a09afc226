# Tier 2: Commodity-Specific Models API Reference

**Target Audience**: Econometricians, Time Series Analysts  
**Module**: `yemen_market.models.three_tier.tier2_commodity`

## Overview

Tier 2 implements commodity-specific threshold vector error correction models (TVECM) to analyze price transmission dynamics under different conflict regimes. This tier captures how market integration varies by commodity and identifies regime-switching behavior.

## Classes

### CommoditySpecificModel

```python
class CommoditySpecificModel(BaseThreeTierModel):
    """Commodity-specific threshold VECM with regime-switching dynamics."""
    
    def __init__(
        self,
        commodity: str,
        config: Optional[Dict[str, Any]] = None
    ):
        """Initialize model for specific commodity."""
```

#### Parameters
- **commodity** (`str`): Commodity name to analyze
- **config** (`dict`): Model configuration

#### Configuration

```python
config = {
    # Model specification
    'price_vars': ['price_local', 'price_global'],  # Price series
    'threshold_var': 'conflict_intensity',           # Threshold variable
    'control_vars': ['exchange_rate', 'fuel_price'], # Controls
    
    # Cointegration testing
    'test_cointegration': True,       # <PERSON> test
    'coint_method': 'johansen',       # or 'engle_granger'
    'max_rank': 1,                    # Maximum cointegration rank
    
    # VECM specification
    'max_lags': 4,                    # Maximum lag order
    'lag_selection': 'aic',           # Lag selection criterion
    'include_constant': True,         # Include constant
    'include_trend': False,           # Include trend
    
    # Threshold specification
    'n_regimes': 2,                   # Number of regimes
    'threshold_method': 'grid_search', # Search method
    'trim_pct': 0.15,                 # Trimming percentage
    'n_boot': 1000,                   # Bootstrap replications
    
    # Estimation options
    'use_robust_cov': True,           # Robust covariance
    'convergence_tol': 1e-5,          # Convergence tolerance
    'max_iter': 100                   # Maximum iterations
}
```

## Core Methods

### fit

```python
def fit(
    self,
    data: pd.DataFrame,
    test_specification: bool = True
) -> ResultsContainer:
    """Estimate threshold VECM for commodity."""
```

#### Parameters
- **data** (`pd.DataFrame`): Time series data for commodity
- **test_specification** (`bool`): Run specification tests

#### Returns
- **ResultsContainer**: Results with regime-specific parameters

#### Workflow
1. Test for cointegration
2. Select optimal lag order
3. Estimate threshold value
4. Fit regime-specific VECMs
5. Test regime significance

### predict

```python
def predict(
    self,
    data: pd.DataFrame,
    steps_ahead: int = 1,
    dynamic: bool = False
) -> pd.DataFrame:
    """Generate forecasts from fitted model."""
```

#### Parameters
- **data** (`pd.DataFrame`): Data for prediction
- **steps_ahead** (`int`): Forecast horizon
- **dynamic** (`bool`): Use dynamic forecasting

### identify_regimes

```python
def identify_regimes(
    self,
    data: pd.DataFrame
) -> pd.Series:
    """Classify observations into regimes."""
```

Returns regime assignments based on threshold variable.

## Threshold VECM Implementation

### ThresholdVECM

```python
class ThresholdVECM:
    """Threshold Vector Error Correction Model."""
    
    def __init__(
        self,
        n_regimes: int = 2,
        threshold_var: str = 'conflict_intensity',
        delay: int = 1
    ):
        """Initialize threshold VECM."""
```

#### Methods

##### estimate_threshold

```python
def estimate_threshold(
    self,
    y: np.ndarray,
    threshold_var: np.ndarray,
    trim_pct: float = 0.15
) -> float:
    """Estimate threshold value using grid search."""
```

Finds optimal threshold minimizing sum of squared residuals.

##### fit_regime_models

```python
def fit_regime_models(
    self,
    y: np.ndarray,
    threshold: float,
    regime_indicators: np.ndarray
) -> Dict[int, VECMResults]:
    """Fit separate VECM for each regime."""
```

##### test_threshold_significance

```python
def test_threshold_significance(
    self,
    y: np.ndarray,
    threshold_var: np.ndarray,
    n_boot: int = 1000
) -> Dict[str, Any]:
    """Test if threshold effect is significant."""
```

Uses Hansen (1999) bootstrap procedure.

## Cointegration Testing

### JohansenTest

```python
class JohansenTest:
    """Johansen cointegration test implementation."""
    
    def test(
        self,
        data: np.ndarray,
        det_order: int = 0,
        k_ar_diff: int = 1
    ) -> Dict[str, Any]:
        """Perform Johansen test."""
```

#### Returns
```python
{
    'trace_stat': np.ndarray,      # Trace statistics
    'max_eig_stat': np.ndarray,    # Max eigenvalue statistics
    'crit_vals_trace': np.ndarray, # Critical values (trace)
    'crit_vals_max': np.ndarray,   # Critical values (max eig)
    'rank': int,                   # Cointegration rank
    'eigenvectors': np.ndarray,    # Cointegrating vectors
    'eigenvalues': np.ndarray      # Eigenvalues
}
```

### EngleGrangerTest

```python
class EngleGrangerTest:
    """Two-step Engle-Granger cointegration test."""
    
    def test(
        self,
        y1: np.ndarray,
        y2: np.ndarray,
        trend: str = 'c'
    ) -> Dict[str, Any]:
        """Test cointegration between two series."""
```

## Commodity Extraction

### CommodityExtractor

```python
class CommodityExtractor:
    """Extract and prepare commodity-specific data."""
    
    def extract(
        self,
        panel_data: pd.DataFrame,
        commodity: str,
        markets: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Extract time series for specific commodity."""
```

#### Features
- Handles missing data interpolation
- Creates spatial price indices
- Calculates price spreads
- Generates market pair data

## Results Structure

### Tier2Results

```python
@dataclass
class Tier2Results(ResultsContainer):
    """Results container for Tier 2 models."""
    
    # Basic info
    commodity: str                    # Commodity name
    n_obs: int                       # Observations
    
    # Cointegration results
    cointegration_rank: int          # Number of cointegrating relations
    cointegration_test: Dict         # Test statistics
    cointegrating_vectors: np.ndarray # Normalized vectors
    
    # Threshold results
    threshold_value: float           # Estimated threshold
    threshold_ci: Tuple[float, float] # Confidence interval
    threshold_pvalue: float          # Significance test
    n_regimes: int                   # Number of regimes
    regime_counts: Dict[int, int]    # Obs per regime
    
    # Regime-specific results
    regime_models: Dict[int, Dict]   # VECM results by regime
    adjustment_speeds: Dict[int, np.ndarray] # Alpha by regime
    
    # Price transmission
    transmission_elasticities: Dict[int, float] # By regime
    half_lives: Dict[int, float]     # Price adjustment half-lives
    
    # Diagnostics
    regime_diagnostics: Dict[int, Dict] # Tests by regime
    transition_matrix: np.ndarray     # Regime transitions
```

## Complete Example

```python
from yemen_market.models.three_tier.tier2_commodity import CommoditySpecificModel
from yemen_market.models.three_tier.tier2_commodity import CommodityExtractor
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Load panel data
panel = pd.read_parquet('data/processed/panel.parquet')

# Extract wheat data
extractor = CommodityExtractor()
wheat_data = extractor.extract(
    panel,
    commodity='wheat',
    markets=['SANA\'A', 'ADEN', 'TAIZ', 'HODEIDAH']
)

print(f"Wheat data shape: {wheat_data.shape}")
print(f"Date range: {wheat_data['date'].min()} to {wheat_data['date'].max()}")

# Configure model
config = {
    'price_vars': ['log_price_local', 'log_price_global'],
    'threshold_var': 'conflict_intensity',
    'control_vars': ['exchange_rate', 'fuel_price'],
    'test_cointegration': True,
    'max_lags': 4,
    'n_regimes': 2,
    'threshold_method': 'grid_search',
    'n_boot': 1000
}

# Initialize and fit model
wheat_model = CommoditySpecificModel('wheat', config)
results = wheat_model.fit(wheat_data)

# Display results
print("\n=== TIER 2: WHEAT MARKET ANALYSIS ===")
print(f"\nCointegration Results:")
print(f"Rank: {results.cointegration_rank}")
print(f"Test statistic: {results.cointegration_test['trace_stat'][0]:.2f}")
print(f"Critical value (5%): {results.cointegration_test['crit_vals_trace'][0, 1]:.2f}")

print(f"\nThreshold Results:")
print(f"Threshold value: {results.threshold_value:.3f}")
print(f"95% CI: [{results.threshold_ci[0]:.3f}, {results.threshold_ci[1]:.3f}]")
print(f"P-value: {results.threshold_pvalue:.4f}")

print(f"\nRegime Distribution:")
for regime, count in results.regime_counts.items():
    pct = count / results.n_obs * 100
    print(f"Regime {regime}: {count} obs ({pct:.1f}%)")

print(f"\nPrice Transmission Elasticities:")
for regime, elasticity in results.transmission_elasticities.items():
    print(f"Regime {regime}: {elasticity:.3f}")
    print(f"  Half-life: {results.half_lives[regime]:.1f} periods")

# Visualize results
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 1. Price series with regimes
ax = axes[0, 0]
regimes = wheat_model.identify_regimes(wheat_data)
for regime in [0, 1]:
    mask = regimes == regime
    ax.scatter(
        wheat_data.loc[mask, 'date'],
        wheat_data.loc[mask, 'log_price_local'],
        label=f'Regime {regime}',
        alpha=0.6
    )
ax.axhline(y=results.threshold_value, color='red', linestyle='--', label='Threshold')
ax.set_title('Log Prices by Regime')
ax.set_xlabel('Date')
ax.set_ylabel('Log Price')
ax.legend()

# 2. Threshold variable over time
ax = axes[0, 1]
ax.plot(wheat_data['date'], wheat_data['conflict_intensity'])
ax.axhline(y=results.threshold_value, color='red', linestyle='--')
ax.fill_between(
    wheat_data['date'],
    results.threshold_ci[0],
    results.threshold_ci[1],
    alpha=0.3,
    color='red',
    label='95% CI'
)
ax.set_title('Conflict Intensity and Threshold')
ax.set_xlabel('Date')
ax.set_ylabel('Conflict Intensity')
ax.legend()

# 3. Impulse Response Functions
ax = axes[1, 0]
for regime in [0, 1]:
    # Calculate IRF for price shock
    irf = wheat_model.calculate_irf(
        shock_var='log_price_global',
        response_var='log_price_local',
        regime=regime,
        periods=20
    )
    ax.plot(irf, label=f'Regime {regime}', linewidth=2)
    
ax.set_title('Price Transmission: Global to Local')
ax.set_xlabel('Periods')
ax.set_ylabel('Response')
ax.legend()
ax.grid(True, alpha=0.3)

# 4. Regime transition probabilities
ax = axes[1, 1]
transition_matrix = results.transition_matrix
im = ax.imshow(transition_matrix, cmap='Blues', aspect='auto')
ax.set_xticks([0, 1])
ax.set_yticks([0, 1])
ax.set_xticklabels(['Low Conflict', 'High Conflict'])
ax.set_yticklabels(['Low Conflict', 'High Conflict'])
ax.set_title('Regime Transition Probabilities')

# Add probability values
for i in range(2):
    for j in range(2):
        ax.text(j, i, f'{transition_matrix[i, j]:.2f}',
                ha='center', va='center', color='black')

plt.colorbar(im, ax=ax)
plt.tight_layout()
plt.savefig('results/tier2_wheat_analysis.png', dpi=300)

# Test for asymmetric adjustment
print("\n=== Asymmetric Adjustment Test ===")
asymmetry_test = wheat_model.test_asymmetric_adjustment()
print(f"H0: Symmetric adjustment")
print(f"Test statistic: {asymmetry_test['statistic']:.3f}")
print(f"P-value: {asymmetry_test['p_value']:.4f}")
if asymmetry_test['p_value'] < 0.05:
    print("Result: Reject H0 - Evidence of asymmetric adjustment")
else:
    print("Result: Fail to reject H0 - No evidence of asymmetry")

# Forecast comparison
print("\n=== Out-of-Sample Forecasting ===")

# Split data
train_end = '2023-01-01'
train_data = wheat_data[wheat_data['date'] < train_end]
test_data = wheat_data[wheat_data['date'] >= train_end]

# Refit on training data
wheat_model_train = CommoditySpecificModel('wheat', config)
wheat_model_train.fit(train_data)

# Generate forecasts
forecasts = wheat_model_train.predict(
    test_data,
    steps_ahead=len(test_data),
    dynamic=True
)

# Calculate forecast errors
actual = test_data['log_price_local'].values
predicted = forecasts['forecast'].values
rmse = np.sqrt(np.mean((actual - predicted)**2))
mape = np.mean(np.abs((actual - predicted) / actual)) * 100

print(f"RMSE: {rmse:.4f}")
print(f"MAPE: {mape:.2f}%")

# Compare with linear VECM
linear_config = config.copy()
linear_config['n_regimes'] = 1  # Force linear model
linear_model = CommoditySpecificModel('wheat', linear_config)
linear_model.fit(train_data)
linear_forecasts = linear_model.predict(test_data, steps_ahead=len(test_data))

linear_rmse = np.sqrt(np.mean((actual - linear_forecasts['forecast'].values)**2))
print(f"\nLinear VECM RMSE: {linear_rmse:.4f}")
print(f"Improvement: {((linear_rmse - rmse) / linear_rmse * 100):.1f}%")

# Save results
results.to_json(f'results/tier2_{commodity}_results.json')
print(f"\nResults saved to results/tier2_{commodity}_results.json")

# Export for paper
latex_table = wheat_model.generate_latex_table(
    results,
    caption="Threshold VECM Results for Wheat",
    label="tab:wheat_tvecm"
)
with open('results/tables/wheat_tvecm.tex', 'w') as f:
    f.write(latex_table)
```

## Commodity Comparison

```python
from yemen_market.models.three_tier.tier2_commodity import run_all_commodities

# Run analysis for all commodities
commodities = ['wheat', 'rice', 'sugar', 'cooking_oil', 'fuel_diesel']

all_results = run_all_commodities(
    panel_data=panel,
    commodities=commodities,
    config=config,
    parallel=True,
    n_jobs=4
)

# Compare threshold values
thresholds = pd.DataFrame({
    'commodity': commodities,
    'threshold': [r.threshold_value for r in all_results.values()],
    'low_regime_pct': [r.regime_counts[0]/r.n_obs*100 for r in all_results.values()],
    'transmission_low': [r.transmission_elasticities[0] for r in all_results.values()],
    'transmission_high': [r.transmission_elasticities[1] for r in all_results.values()]
})

print(thresholds.to_string(index=False))

# Plot comparison
fig, ax = plt.subplots(figsize=(10, 6))
x = np.arange(len(commodities))
width = 0.35

ax.bar(x - width/2, thresholds['transmission_low'], width, label='Low Conflict')
ax.bar(x + width/2, thresholds['transmission_high'], width, label='High Conflict')

ax.set_xlabel('Commodity')
ax.set_ylabel('Price Transmission Elasticity')
ax.set_title('Price Transmission by Commodity and Conflict Regime')
ax.set_xticks(x)
ax.set_xticklabels(commodities)
ax.legend()
plt.tight_layout()
plt.savefig('results/tier2_commodity_comparison.png')
```

## Advanced Features

### Smooth Transition Models

```python
from yemen_market.models.three_tier.tier2_commodity import SmoothTransitionVECM

# Use smooth transition instead of discrete threshold
smooth_model = SmoothTransitionVECM(
    commodity='wheat',
    transition_speed=10,  # Gamma parameter
    transition_var='conflict_intensity'
)

smooth_results = smooth_model.fit(wheat_data)
print(f"Transition speed: {smooth_results.transition_speed:.2f}")
```

### Multiple Thresholds

```python
# Three-regime model
config_3regime = config.copy()
config_3regime['n_regimes'] = 3

model_3regime = CommoditySpecificModel('wheat', config_3regime)
results_3regime = model_3regime.fit(wheat_data)

print(f"Threshold 1: {results_3regime.thresholds[0]:.3f}")
print(f"Threshold 2: {results_3regime.thresholds[1]:.3f}")
```

## Diagnostics

```python
# Run comprehensive diagnostics
diagnostics = wheat_model.run_diagnostics(results)

print("\n=== Model Diagnostics ===")
for test_name, test_result in diagnostics.items():
    print(f"\n{test_name}:")
    print(f"  Statistic: {test_result['statistic']:.3f}")
    print(f"  P-value: {test_result['p_value']:.4f}")
    print(f"  Result: {test_result['conclusion']}")
```

## See Also

- [`tier1_pooled.md`](tier1_pooled.md) - Pooled panel models
- [`tier3_validation.md`](tier3_validation.md) - Validation models
- [`cointegration_tests.py`](../../../api/models/three_tier/) - Cointegration test details
- [User Guide: Threshold Models](../../../02-user-guides/running-analyses.md#tier2)
- [Methodology: TVECM](../../../05-methodology/econometric-models/threshold-models.md)