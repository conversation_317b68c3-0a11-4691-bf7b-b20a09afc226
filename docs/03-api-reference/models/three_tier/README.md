# Three-Tier Econometric Framework API

**Target Audience**: Econometricians, Researchers  
**Module**: `yemen_market.models.three_tier`

## Overview

The three-tier econometric framework provides a comprehensive approach to analyzing market integration in conflict-affected settings. Each tier addresses different aspects of price transmission and market dynamics.

## Framework Architecture

```
yemen_market.models.three_tier/
├── core/                    # Base classes and utilities
│   ├── base_model.py       # Abstract base class
│   ├── data_validator.py   # Input validation
│   ├── panel_data_handler.py # 3D panel management
│   └── results_container.py # Standardized results
├── tier1_pooled/           # Pooled panel models
│   ├── pooled_panel_model.py # Main implementation
│   ├── fixed_effects.py    # FE utilities
│   └── standard_errors.py  # Robust SE calculation
├── tier2_commodity/        # Commodity-specific models
│   ├── commodity_specific_model.py # Main implementation
│   ├── threshold_vecm.py   # Threshold models
│   └── cointegration_tests.py # Testing utilities
├── tier3_validation/       # Validation models
│   ├── conflict_validation.py # External validation
│   ├── factor_models.py    # Factor analysis
│   └── pca_analysis.py     # PCA implementation
├── diagnostics/            # Diagnostic testing
│   ├── panel_diagnostics.py # Test orchestrator
│   └── test_implementations.py # Individual tests
└── integration/            # Framework integration
    ├── three_tier_runner.py # Main runner
    ├── cross_tier_validation.py # Consistency checks
    └── results_analyzer.py  # Results aggregation
```

## Core Components

### BaseThreeTierModel

Abstract base class that all tier models inherit from:

```python
from abc import ABC, abstractmethod

class BaseThreeTierModel(ABC):
    """Abstract base class for three-tier models."""
    
    @abstractmethod
    def fit(self, data: pd.DataFrame) -> 'ResultsContainer':
        """Fit the model to data."""
        pass
    
    @abstractmethod
    def predict(self, data: pd.DataFrame) -> pd.Series:
        """Generate predictions."""
        pass
```

### ResultsContainer

Standardized container for model results:

```python
@dataclass
class ResultsContainer:
    """Standardized results container."""
    coefficients: pd.DataFrame
    standard_errors: pd.DataFrame
    pvalues: pd.DataFrame
    rsquared: float
    nobs: int
    diagnostics: Dict[str, Any]
    metadata: Dict[str, Any]
    tier: int
    model_type: str
```

### PanelDataHandler

Manages 3D panel data structures:

```python
class PanelDataHandler:
    """Handle 3D panel data (market × commodity × time)."""
    
    def validate_panel(self, data: pd.DataFrame) -> bool:
        """Validate panel structure."""
        
    def balance_panel(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create balanced panel."""
        
    def extract_dimensions(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Extract panel dimensions."""
```

## Tier Implementations

### Tier 1: Pooled Panel Models

See detailed documentation: [`tier1_pooled.md`](tier1_pooled.md)

Key features:
- Multi-way fixed effects (market × commodity)
- Driscoll-Kraay standard errors
- Time-varying coefficients
- Handles unbalanced panels

### Tier 2: Commodity-Specific Models

See detailed documentation: [`tier2_commodity.md`](tier2_commodity.md)

Key features:
- Threshold vector error correction models
- Regime-switching dynamics
- Commodity-specific price transmission
- Cointegration testing

### Tier 3: Validation Models

See detailed documentation: [`tier3_validation.md`](tier3_validation.md)

Key features:
- External validation with conflict data
- Factor models for common shocks
- Principal component analysis
- Cross-validation procedures

## Diagnostic Testing

The framework includes comprehensive diagnostic testing following World Bank standards:

```python
from yemen_market.models.three_tier.diagnostics import PanelDiagnostics

diagnostics = PanelDiagnostics()
results = diagnostics.run_all_tests(model, data)

# Available tests
tests = {
    'serial_correlation': 'Wooldridge test',
    'cross_sectional_dependence': 'Pesaran CD test',
    'heteroskedasticity': 'Modified Wald test',
    'unit_roots': 'Im-Pesaran-Shin test',
    'specification': 'Ramsey RESET',
    'structural_breaks': 'Chow test'
}
```

## Integration and Orchestration

### ThreeTierRunner

Main orchestrator for running the complete analysis:

```python
from yemen_market.models.three_tier.integration import ThreeTierRunner

runner = ThreeTierRunner(config)
results = runner.run_all_tiers(data)

# Results structure
results = {
    'tier1': ResultsContainer,
    'tier2': Dict[str, ResultsContainer],  # By commodity
    'tier3': ResultsContainer,
    'cross_validation': Dict[str, Any],
    'summary': Dict[str, Any]
}
```

### Configuration

```python
config = {
    # Tier 1 configuration
    'tier1_config': {
        'outcome_var': 'log_price',
        'treatment_var': 'conflict_intensity',
        'control_vars': ['rainfall', 'temperature'],
        'fixed_effects': ['market_id', 'commodity', 'month'],
        'cluster_var': 'market_id',
        'cov_type': 'kernel'  # Driscoll-Kraay
    },
    
    # Tier 2 configuration
    'tier2_config': {
        'commodities': ['wheat', 'rice', 'sugar'],
        'test_cointegration': True,
        'max_lags': 4,
        'threshold_var': 'conflict_intensity',
        'n_regimes': 2
    },
    
    # Tier 3 configuration
    'tier3_config': {
        'external_vars': ['conflict_events', 'distance_to_border'],
        'n_factors': 3,
        'factor_method': 'pca',  # or 'fa'
        'validation_method': 'kfold',
        'n_folds': 5
    },
    
    # General configuration
    'run_diagnostics': True,
    'parallel': True,
    'n_jobs': -1,
    'output_dir': 'results/three_tier',
    'save_intermediate': True
}
```

## Usage Examples

### Complete Analysis

```python
from yemen_market.models.three_tier import ThreeTierRunner
import pandas as pd

# Load data
data = pd.read_parquet('data/processed/panel.parquet')

# Run complete analysis
runner = ThreeTierRunner(config)
results = runner.run_all_tiers(data)

# Generate report
runner.generate_report(results, 'results/analysis_report.html')
```

### Individual Tier Analysis

```python
# Tier 1 only
from yemen_market.models.three_tier.tier1_pooled import PooledPanelModel

tier1_model = PooledPanelModel(config['tier1_config'])
tier1_results = tier1_model.fit(data)

# Tier 2 for specific commodity
from yemen_market.models.three_tier.tier2_commodity import CommoditySpecificModel

wheat_model = CommoditySpecificModel('wheat', config['tier2_config'])
wheat_results = wheat_model.fit(data[data.commodity == 'wheat'])

# Tier 3 validation
from yemen_market.models.three_tier.tier3_validation import ConflictValidation

validator = ConflictValidation(config['tier3_config'])
validation_results = validator.validate(tier1_results, external_data)
```

### Custom Extensions

```python
from yemen_market.models.three_tier.core import BaseThreeTierModel

class CustomModel(BaseThreeTierModel):
    """Custom model implementation."""
    
    def __init__(self, config):
        super().__init__(config)
        self.tier = 4  # Custom tier
        
    def fit(self, data):
        # Custom implementation
        pass
```

## Output Format

All models produce standardized outputs:

```python
# Access results
tier1_results = results['tier1']
print(f"Conflict effect: {tier1_results.coefficients.loc['conflict_intensity', 'coef']:.3f}")
print(f"P-value: {tier1_results.pvalues.loc['conflict_intensity', 'pvalue']:.3f}")

# Commodity-specific results
wheat_results = results['tier2']['wheat']
print(f"Threshold: {wheat_results.threshold:.2f}")
print(f"Low regime effect: {wheat_results.regime_effects['low']:.3f}")
print(f"High regime effect: {wheat_results.regime_effects['high']:.3f}")

# Validation results
validation = results['tier3']
print(f"External R²: {validation.external_r2:.3f}")
print(f"Main factors: {validation.factor_loadings.index.tolist()}")
```

## Performance Tips

1. **Large Datasets**: Use chunking for panels >1M observations
2. **Parallel Processing**: Enable with `parallel=True` in config
3. **Memory Management**: Use `save_intermediate=True` to save memory
4. **Convergence**: Adjust `max_iter` and `tolerance` for difficult models

## See Also

- Individual tier documentation:
  - [`tier1_pooled.md`](tier1_pooled.md)
  - [`tier2_commodity.md`](tier2_commodity.md)
  - [`tier3_validation.md`](tier3_validation.md)
- [User Guide: Modeling](../../02-user-guides/running-analyses.md)
- [Methodology: Econometric Models](../../05-methodology/econometric-models/)