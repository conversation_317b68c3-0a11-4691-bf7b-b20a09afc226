# Tier 3: Validation Models API Reference

**Target Audience**: Econometricians, Research Scientists  
**Module**: `yemen_market.models.three_tier.tier3_validation`

## Overview

Tier 3 provides external validation and robustness testing using conflict data, factor models, and cross-validation procedures. This tier ensures the reliability of Tier 1 and Tier 2 findings through multiple validation approaches.

## Classes

### ConflictValidation

```python
class ConflictValidation(BaseThreeTierModel):
    """External validation using conflict event data."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize conflict validation model."""
```

#### Configuration

```python
config = {
    # Data specification
    'conflict_vars': [
        'n_conflict_events',
        'n_battles',
        'n_explosions',
        'fatalities'
    ],
    'distance_vars': [
        'distance_to_port',
        'distance_to_capital',
        'distance_to_border'
    ],
    'external_shocks': [
        'global_oil_price',
        'usd_yer_rate',
        'wheat_futures'
    ],
    
    # Validation approach
    'validation_method': 'instrumental_variables',  # or 'reduced_form'
    'instruments': ['neighboring_conflict', 'historical_violence'],
    
    # Model specification
    'include_spatial_lags': True,
    'spatial_weight_matrix': 'inverse_distance',
    'include_time_trends': True,
    
    # Cross-validation
    'cv_method': 'time_series_split',  # or 'kfold', 'leave_one_out'
    'n_splits': 5,
    'test_size': 0.2
}
```

## Core Methods

### validate

```python
def validate(
    self,
    model_results: ResultsContainer,
    external_data: pd.DataFrame
) -> ValidationResults:
    """Validate model results using external data."""
```

#### Parameters
- **model_results** (`ResultsContainer`): Results from Tier 1 or 2
- **external_data** (`pd.DataFrame`): External validation dataset

#### Returns
- **ValidationResults**: Comprehensive validation metrics

### run_robustness_checks

```python
def run_robustness_checks(
    self,
    base_model: BaseThreeTierModel,
    data: pd.DataFrame,
    checks: List[str] = None
) -> Dict[str, Any]:
    """Run battery of robustness checks."""
```

#### Available Checks
- `'subsample_stability'`: Stability across time periods
- `'leave_region_out'`: Geographic robustness
- `'alternative_measures'`: Different variable definitions
- `'placebo_tests'`: Falsification tests
- `'permutation_tests'`: Randomization inference

## Factor Models

### FactorModels

```python
class FactorModels:
    """Static and dynamic factor models for market integration."""
    
    def __init__(
        self,
        n_factors: int = 3,
        factor_method: str = 'static',  # or 'dynamic'
        rotation: str = 'varimax'
    ):
        """Initialize factor model."""
```

#### Methods

##### fit

```python
def fit(
    self,
    price_matrix: pd.DataFrame,
    normalize: bool = True
) -> FactorResults:
    """Estimate factor model on price data."""
```

##### extract_common_shocks

```python
def extract_common_shocks(
    self,
    price_data: pd.DataFrame
) -> pd.DataFrame:
    """Extract common shock components from prices."""
```

##### calculate_integration_index

```python
def calculate_integration_index(
    self,
    loadings: np.ndarray,
    time_varying: bool = True
) -> pd.Series:
    """Calculate market integration index from factor loadings."""
```

### DynamicFactorModel

```python
class DynamicFactorModel(FactorModels):
    """Dynamic factor model with time-varying loadings."""
    
    def __init__(
        self,
        n_factors: int = 3,
        n_lags: int = 2,
        use_em: bool = True
    ):
        """Initialize dynamic factor model."""
```

## Principal Component Analysis

### PCAAnalysis

```python
class PCAAnalysis:
    """Principal component analysis for price integration."""
    
    def __init__(
        self,
        n_components: Optional[int] = None,
        min_variance_explained: float = 0.9
    ):
        """Initialize PCA analysis."""
```

#### Methods

##### analyze_integration

```python
def analyze_integration(
    self,
    price_matrix: pd.DataFrame,
    commodity: Optional[str] = None
) -> PCAResults:
    """Analyze market integration using PCA."""
```

##### test_structural_breaks

```python
def test_structural_breaks(
    self,
    price_matrix: pd.DataFrame,
    potential_break_dates: List[str]
) -> Dict[str, Any]:
    """Test for structural breaks in integration patterns."""
```

##### plot_integration_evolution

```python
def plot_integration_evolution(
    self,
    results: PCAResults,
    window_size: int = 12
) -> plt.Figure:
    """Plot evolution of market integration over time."""
```

## Cross-Validation

### CrossTierValidation

```python
class CrossTierValidation:
    """Cross-validate results across all three tiers."""
    
    def validate_consistency(
        self,
        tier1_results: ResultsContainer,
        tier2_results: Dict[str, ResultsContainer],
        tier3_results: ValidationResults
    ) -> ConsistencyReport:
        """Check consistency across tiers."""
```

#### Consistency Checks
1. Coefficient sign consistency
2. Magnitude comparisons
3. Statistical significance alignment
4. Economic interpretation coherence

## Results Structure

### ValidationResults

```python
@dataclass
class ValidationResults:
    """Results from Tier 3 validation."""
    
    # External validation
    external_r2: float                    # R² using external data
    external_rmse: float                  # RMSE
    iv_results: Optional[Dict]            # IV estimation results
    
    # Factor analysis
    n_factors: int                        # Number of factors
    variance_explained: np.ndarray        # By factor
    factor_loadings: pd.DataFrame         # Market loadings
    factor_scores: pd.DataFrame           # Time series of factors
    integration_index: pd.Series          # Overall integration measure
    
    # PCA results
    pca_components: np.ndarray            # Principal components
    pca_explained_variance: np.ndarray    # Variance by component
    market_contributions: pd.DataFrame    # Market contributions to PC1
    
    # Robustness
    robustness_checks: Dict[str, Dict]    # Results by check type
    stability_metrics: Dict[str, float]   # Coefficient stability
    
    # Cross-validation
    cv_scores: np.ndarray                 # Out-of-sample scores
    cv_predictions: pd.DataFrame          # OOS predictions
    best_model_params: Dict               # Optimal parameters
```

## Complete Example

```python
from yemen_market.models.three_tier.tier3_validation import (
    ConflictValidation, FactorModels, PCAAnalysis
)
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Load data
panel_data = pd.read_parquet('data/processed/panel.parquet')
conflict_data = pd.read_parquet('data/processed/conflict_panel.parquet')
tier1_results = pd.read_pickle('results/tier1_results.pkl')

# 1. CONFLICT VALIDATION
print("=== TIER 3: EXTERNAL VALIDATION ===")

# Configure validation
validation_config = {
    'conflict_vars': ['n_conflict_events', 'n_battles', 'fatalities'],
    'distance_vars': ['distance_to_port', 'distance_to_capital'],
    'validation_method': 'instrumental_variables',
    'instruments': ['neighboring_conflict', 'historical_violence'],
    'cv_method': 'time_series_split',
    'n_splits': 5
}

# Initialize validator
validator = ConflictValidation(validation_config)

# Merge panel with conflict data
validation_data = panel_data.merge(
    conflict_data,
    on=['market_id', 'date'],
    how='left'
)

# Run validation
validation_results = validator.validate(tier1_results, validation_data)

print(f"\nExternal Validation R²: {validation_results.external_r2:.3f}")
print(f"RMSE: {validation_results.external_rmse:.4f}")

# IV results
if validation_results.iv_results:
    print(f"\nInstrumental Variables Results:")
    print(f"First-stage F-stat: {validation_results.iv_results['first_stage_f']:.2f}")
    print(f"Wu-Hausman test p-value: {validation_results.iv_results['wu_hausman_p']:.4f}")
    print(f"Sargan test p-value: {validation_results.iv_results['sargan_p']:.4f}")

# 2. FACTOR ANALYSIS
print("\n=== FACTOR MODEL ANALYSIS ===")

# Prepare price matrix (markets × time)
price_matrix = panel_data.pivot_table(
    index='date',
    columns='market_id',
    values='log_price',
    aggfunc='mean'
)

# Static factor model
factor_model = FactorModels(
    n_factors=3,
    factor_method='static',
    rotation='varimax'
)

factor_results = factor_model.fit(price_matrix)

print(f"\nFactor Analysis Results:")
print(f"Factors extracted: {factor_results.n_factors}")
print(f"Total variance explained: {factor_results.variance_explained.sum():.1f}%")

for i, var_exp in enumerate(factor_results.variance_explained):
    print(f"Factor {i+1}: {var_exp:.1f}% variance")

# Extract common shocks
common_shocks = factor_model.extract_common_shocks(price_matrix)
print(f"\nCommon shock component: {common_shocks.shape}")

# Calculate integration index
integration_index = factor_model.calculate_integration_index(
    factor_results.factor_loadings.values,
    time_varying=True
)

# 3. PCA ANALYSIS
print("\n=== PCA INTEGRATION ANALYSIS ===")

# Initialize PCA
pca_analyzer = PCAAnalysis(
    n_components=None,  # Determine automatically
    min_variance_explained=0.9
)

# Analyze integration
pca_results = pca_analyzer.analyze_integration(price_matrix)

print(f"\nPCA Results:")
print(f"Components needed for 90% variance: {pca_results.n_components_90}")
print(f"PC1 variance explained: {pca_results.pca_explained_variance[0]:.1f}%")

# Test for structural breaks
break_dates = ['2020-01-01', '2021-06-01', '2022-03-01']  # Key events
break_tests = pca_analyzer.test_structural_breaks(price_matrix, break_dates)

print(f"\nStructural Break Tests:")
for date, test in break_tests.items():
    print(f"{date}: Chow test statistic = {test['statistic']:.2f}, p-value = {test['p_value']:.4f}")

# 4. ROBUSTNESS CHECKS
print("\n=== ROBUSTNESS ANALYSIS ===")

# Run comprehensive robustness checks
robustness_checks = validator.run_robustness_checks(
    base_model=tier1_model,  # Assume we have the original model
    data=panel_data,
    checks=[
        'subsample_stability',
        'leave_region_out',
        'alternative_measures',
        'placebo_tests'
    ]
)

for check_name, results in robustness_checks.items():
    print(f"\n{check_name}:")
    print(f"  Coefficient range: [{results['coef_min']:.4f}, {results['coef_max']:.4f}]")
    print(f"  Stability ratio: {results['stability_ratio']:.3f}")
    print(f"  Significant in all: {results['always_significant']}")

# 5. VISUALIZATION
fig, axes = plt.subplots(2, 3, figsize=(18, 12))

# Plot 1: Factor loadings heatmap
ax = axes[0, 0]
im = ax.imshow(factor_results.factor_loadings.T, cmap='RdBu_r', aspect='auto')
ax.set_title('Factor Loadings by Market')
ax.set_xlabel('Market')
ax.set_ylabel('Factor')
ax.set_yticks([0, 1, 2])
ax.set_yticklabels(['Factor 1', 'Factor 2', 'Factor 3'])
plt.colorbar(im, ax=ax)

# Plot 2: Integration index over time
ax = axes[0, 1]
integration_index.plot(ax=ax, linewidth=2)
ax.set_title('Market Integration Index')
ax.set_xlabel('Date')
ax.set_ylabel('Integration Level')
ax.grid(True, alpha=0.3)

# Plot 3: PCA scree plot
ax = axes[0, 2]
ax.bar(range(1, 11), pca_results.pca_explained_variance[:10])
ax.set_title('PCA Scree Plot')
ax.set_xlabel('Principal Component')
ax.set_ylabel('Variance Explained (%)')
ax.set_xticks(range(1, 11))

# Plot 4: Cross-validation scores
ax = axes[1, 0]
cv_scores = validation_results.cv_scores
ax.boxplot(cv_scores)
ax.set_title('Cross-Validation Performance')
ax.set_ylabel('R² Score')
ax.set_xlabel('CV Folds')

# Plot 5: Rolling window integration
ax = axes[1, 1]
window_size = 12
rolling_integration = pca_analyzer.calculate_rolling_integration(
    price_matrix,
    window_size=window_size
)
rolling_integration.plot(ax=ax, linewidth=2)
ax.set_title(f'Rolling Integration (window={window_size})')
ax.set_xlabel('Date')
ax.set_ylabel('Integration Measure')
ax.grid(True, alpha=0.3)

# Plot 6: External validation scatter
ax = axes[1, 2]
ax.scatter(
    validation_results.cv_predictions['actual'],
    validation_results.cv_predictions['predicted'],
    alpha=0.5
)
ax.plot([0, 1], [0, 1], 'r--', transform=ax.transAxes)
ax.set_title('External Validation: Actual vs Predicted')
ax.set_xlabel('Actual Values')
ax.set_ylabel('Predicted Values')
ax.text(
    0.05, 0.95,
    f'R² = {validation_results.external_r2:.3f}',
    transform=ax.transAxes,
    verticalalignment='top',
    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5)
)

plt.tight_layout()
plt.savefig('results/tier3_validation_summary.png', dpi=300)

# 6. CROSS-TIER VALIDATION
print("\n=== CROSS-TIER CONSISTENCY CHECK ===")

from yemen_market.models.three_tier.tier3_validation import CrossTierValidation

# Load all tier results
tier2_results = {}
for commodity in ['wheat', 'rice', 'sugar']:
    tier2_results[commodity] = pd.read_pickle(f'results/tier2_{commodity}_results.pkl')

# Check consistency
cross_validator = CrossTierValidation()
consistency_report = cross_validator.validate_consistency(
    tier1_results,
    tier2_results,
    validation_results
)

print(f"\nOverall consistency score: {consistency_report.overall_score:.2f}/100")
print(f"\nInconsistencies found:")
for issue in consistency_report.issues:
    print(f"- {issue}")

# 7. GENERATE FINAL REPORT
print("\n=== GENERATING VALIDATION REPORT ===")

# Create comprehensive report
validation_report = {
    'external_validation': {
        'r_squared': validation_results.external_r2,
        'rmse': validation_results.external_rmse,
        'iv_diagnostics': validation_results.iv_results
    },
    'factor_analysis': {
        'n_factors': factor_results.n_factors,
        'variance_explained': factor_results.variance_explained.tolist(),
        'integration_trend': 'increasing' if integration_index.iloc[-1] > integration_index.iloc[0] else 'decreasing'
    },
    'pca_analysis': {
        'dominant_component_variance': pca_results.pca_explained_variance[0],
        'markets_most_integrated': pca_results.market_contributions.nlargest(5).index.tolist()
    },
    'robustness': {
        'coefficient_stability': robustness_checks['subsample_stability']['stability_ratio'],
        'geographic_robustness': robustness_checks['leave_region_out']['always_significant'],
        'placebo_pass_rate': robustness_checks['placebo_tests']['pass_rate']
    },
    'cross_tier_consistency': consistency_report.overall_score
}

# Save report
import json
with open('results/tier3_validation_report.json', 'w') as f:
    json.dump(validation_report, f, indent=2)

# Generate LaTeX summary
latex_summary = validator.generate_latex_summary(
    validation_results,
    factor_results,
    pca_results,
    consistency_report
)

with open('results/tables/tier3_validation.tex', 'w') as f:
    f.write(latex_summary)

print("\nValidation complete. Results saved to:")
print("- results/tier3_validation_report.json")
print("- results/tier3_validation_summary.png")
print("- results/tables/tier3_validation.tex")
```

## Advanced Validation Techniques

### Spatial Validation

```python
from yemen_market.models.three_tier.tier3_validation import SpatialValidation

# Validate spatial spillovers
spatial_validator = SpatialValidation(
    weight_matrix='inverse_distance',
    max_neighbors=10
)

spatial_results = spatial_validator.validate_spatial_effects(
    model_results=tier1_results,
    spatial_data=panel_data
)

print(f"Moran's I: {spatial_results['morans_i']:.3f}")
print(f"Spatial autocorrelation p-value: {spatial_results['p_value']:.4f}")
```

### Machine Learning Validation

```python
from yemen_market.models.three_tier.tier3_validation import MLValidation
from sklearn.ensemble import RandomForestRegressor

# Use ML for robustness check
ml_validator = MLValidation(
    base_model=RandomForestRegressor(n_estimators=100, random_state=42),
    feature_importance_method='permutation'
)

ml_results = ml_validator.validate_with_ml(
    econometric_results=tier1_results,
    data=panel_data
)

print(f"ML validation R²: {ml_results['r2_score']:.3f}")
print("\nTop 5 important features:")
for feat, imp in ml_results['feature_importance'][:5]:
    print(f"  {feat}: {imp:.3f}")
```

## See Also

- [`tier1_pooled.md`](tier1_pooled.md) - Tier 1 pooled models
- [`tier2_commodity.md`](tier2_commodity.md) - Tier 2 commodity models
- [`../diagnostics/`](../README.md#diagnostics) - Diagnostic testing
- [User Guide: Validation](../../../02-user-guides/interpreting-results.md#validation)
- [Methodology: External Validation](../../../05-methodology/econometric-models/validation.md)