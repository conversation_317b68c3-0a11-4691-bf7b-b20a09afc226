# Models API Reference

**Target Audience**: Econometricians, Data Scientists, Researchers  
**Module**: `yemen_market.models`

## Overview

The models package implements a **three-tier econometric framework** for analyzing market integration in conflict-affected settings. This framework is designed to capture different aspects of price transmission and market dynamics in Yemen's fragmented economy.

## Three-Tier Framework

### Architecture

```
Three-Tier Framework
├── Tier 1: Pooled Panel Analysis
│   └── Fixed effects with Driscoll-Kraay standard errors
├── Tier 2: Commodity-Specific Models  
│   └── Threshold VECM for regime-switching dynamics
└── Tier 3: Validation & Factor Analysis
    └── External validation with conflict data
```

### Model Hierarchy

1. **Tier 1 - Pooled Panel Models**
   - Captures overall market integration patterns
   - Controls for market and time fixed effects
   - Robust to cross-sectional dependence

2. **Tier 2 - Commodity-Specific Models**
   - Analyzes commodity-specific dynamics
   - Tests for threshold effects in price transmission
   - Identifies regime-switching behavior

3. **Tier 3 - Validation Models**
   - External validation using conflict data
   - Factor analysis for common shocks
   - Robustness checks

## Package Structure

### Core Components

#### [three_tier/core/](three_tier/README.md)
- `BaseThreeTierModel` - Abstract base class for all models
- `ResultsContainer` - Standardized results storage
- `PanelDataHandler` - 3D panel data management
- `DataValidator` - Input validation and checks

### Model Implementations

#### [three_tier/tier1_pooled/](three_tier/tier1_pooled.md)
- `PooledPanelModel` - Main Tier 1 implementation
- Fixed effects estimation utilities
- Driscoll-Kraay standard error calculation

#### [three_tier/tier2_commodity/](three_tier/tier2_commodity.md)
- `CommoditySpecificModel` - Main Tier 2 implementation
- `ThresholdVECM` - Threshold vector error correction
- Cointegration testing utilities

#### [three_tier/tier3_validation/](three_tier/tier3_validation.md)
- `ConflictValidation` - External validation models
- `FactorModels` - Static and dynamic factor analysis
- `PCAAnalysis` - Principal component analysis

### Integration & Diagnostics

#### [three_tier/integration/](three_tier/README.md#integration)
- `ThreeTierRunner` - Orchestrates full analysis
- `CrossTierValidation` - Ensures consistency across tiers
- `ResultsAnalyzer` - Aggregates and summarizes results

#### [three_tier/diagnostics/](three_tier/README.md#diagnostics)
- `PanelDiagnostics` - Comprehensive diagnostic testing
- World Bank-standard econometric tests
- Automatic correction recommendations

## Quick Start

### Running Full Three-Tier Analysis

```python
from yemen_market.models.three_tier import ThreeTierRunner
import pandas as pd

# Load data
panel_data = pd.read_parquet('data/processed/panel.parquet')

# Configure analysis
config = {
    'tier1_config': {
        'include_time_effects': True,
        'include_entity_effects': True,
        'cluster_var': 'market_id'
    },
    'tier2_config': {
        'test_cointegration': True,
        'max_lags': 4,
        'threshold_var': 'conflict_intensity'
    },
    'tier3_config': {
        'n_factors': 3,
        'validation_vars': ['conflict_events', 'distance_to_border']
    },
    'run_diagnostics': True,
    'output_dir': 'results/three_tier_analysis'
}

# Run analysis
runner = ThreeTierRunner(config)
results = runner.run_all_tiers(panel_data)

# Access results
print(f"Tier 1 R²: {results.tier1_results.rsquared:.3f}")
print(f"Commodities with threshold effects: {results.tier2_results.threshold_commodities}")
print(f"Conflict validation R²: {results.tier3_results.validation_r2:.3f}")
```

### Using Individual Tiers

```python
# Tier 1 only
from yemen_market.models.three_tier.tier1_pooled import PooledPanelModel

model = PooledPanelModel()
tier1_results = model.fit(panel_data)
print(tier1_results.summary())

# Tier 2 for specific commodity
from yemen_market.models.three_tier.tier2_commodity import CommoditySpecificModel

wheat_model = CommoditySpecificModel(commodity='wheat')
wheat_results = wheat_model.fit(panel_data)
print(f"Threshold value: {wheat_results.threshold}")

# Tier 3 validation
from yemen_market.models.three_tier.tier3_validation import ConflictValidation

validator = ConflictValidation()
validation = validator.validate(
    model_results=tier1_results,
    external_data=conflict_data
)
```

## Diagnostic Testing

All models include comprehensive diagnostic testing:

```python
from yemen_market.models.three_tier.diagnostics import PanelDiagnostics

# Run full diagnostic suite
diagnostics = PanelDiagnostics()
test_results = diagnostics.run_all_tests(model, data)

# Individual tests
serial_corr = diagnostics.test_serial_correlation(residuals)
cross_dep = diagnostics.test_cross_sectional_dependence(residuals)
hetero = diagnostics.test_heteroskedasticity(residuals)

# Get recommendations
if not test_results['serial_correlation']['passed']:
    corrections = diagnostics.get_correction_recommendations('serial_correlation')
    print(f"Recommended: {corrections}")
```

## Results Format

All models return standardized results:

```python
# ResultsContainer structure
results = {
    'coefficients': pd.DataFrame,      # Coefficient estimates
    'standard_errors': pd.DataFrame,   # Standard errors
    'pvalues': pd.DataFrame,          # P-values
    'diagnostics': dict,              # Test results
    'metadata': dict,                 # Model configuration
    'summary': str                    # Text summary
}

# Access results
print(results.coefficients['conflict_intensity'])
print(f"R-squared: {results.rsquared}")
print(f"Observations: {results.nobs}")
```

## Model Comparison

```python
from yemen_market.models import ModelComparison

# Compare different specifications
comparison = ModelComparison()

# Add models
comparison.add_model('baseline', baseline_results)
comparison.add_model('with_conflict', conflict_results)
comparison.add_model('with_controls', full_results)

# Generate comparison
comparison_table = comparison.generate_comparison_table()
comparison.plot_coefficients(['conflict_intensity'])
```

## Advanced Features

### Custom Model Configuration

```python
# Detailed configuration
config = {
    'data': {
        'outcome_var': 'log_price',
        'treatment_var': 'conflict_intensity',
        'control_vars': ['rainfall', 'temperature', 'distance_to_port'],
        'fixed_effects': ['market_id', 'commodity', 'month'],
        'cluster_var': 'market_id'
    },
    'model': {
        'estimator': 'within',  # or 'random'
        'cov_type': 'kernel',   # Driscoll-Kraay
        'kernel_bandwidth': 3
    },
    'diagnostics': {
        'tests': ['serial_correlation', 'cross_sectional_dependence'],
        'alpha': 0.05
    }
}

model = PooledPanelModel(config)
```

### Handling Unbalanced Panels

```python
# The framework automatically handles unbalanced panels
unbalanced_data = panel_data[panel_data['market_id'] != 'YE_TAIZ']

# Run analysis - will adjust for unbalanced structure
results = runner.run_all_tiers(unbalanced_data)
print(f"Panel balance: {results.panel_info['balance_pct']:.1f}%")
```

## Performance Considerations

- **Memory**: Large panels (>1M observations) may require chunking
- **Speed**: Tier 2 threshold search can be slow; use parallel processing
- **Convergence**: Some threshold models may need tuning for convergence

## See Also

- [Three-Tier Methodology](../../05-methodology/econometric-models/panel-models.md)
- [User Guide: Running Analyses](../../02-user-guides/running-analyses.md)
- [Development: Testing Models](../../04-development/testing/unit-testing.md)