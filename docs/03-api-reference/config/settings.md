# Settings API Reference

**Target Audience**: <PERSON><PERSON><PERSON>, System Administrators  
**Module**: `yemen_market.config.settings`

## Overview

The settings module provides centralized configuration management for the Yemen Market Integration Platform. It defines project paths, analysis parameters, and environment-specific settings.

## Module Import

```python
from yemen_market.config.settings import (
    PROJECT_ROOT, DATA_DIR, RAW_DATA_DIR, 
    PROCESSED_DATA_DIR, ANALYSIS_CONFIG
)
```

## Directory Paths

### Project Structure Constants

```python
PROJECT_ROOT: Path      # Root directory of the project
DATA_DIR: Path         # Main data directory (PROJECT_ROOT / 'data')
SRC_DIR: Path          # Source code directory (PROJECT_ROOT / 'src')
TESTS_DIR: Path        # Tests directory (PROJECT_ROOT / 'tests')
NOTEBOOKS_DIR: Path    # Jupyter notebooks (PROJECT_ROOT / 'notebooks')
REPORTS_DIR: Path      # Reports and outputs (PROJECT_ROOT / 'reports')
LOGS_DIR: Path         # Log files (PROJECT_ROOT / 'logs')
```

### Data Directory Structure

```python
RAW_DATA_DIR: Path       # data/raw/ - Original, immutable data
INTERIM_DATA_DIR: Path   # data/interim/ - Intermediate transformations
PROCESSED_DATA_DIR: Path # data/processed/ - Final processed data
EXTERNAL_DATA_DIR: Path  # data/external/ - External reference data
```

### Output Directories

```python
FIGURES_DIR: Path  # reports/figures/ - Generated plots
TABLES_DIR: Path   # reports/tables/ - Generated tables
MODELS_DIR: Path   # models/ - Saved model files
```

## Configuration Dictionary

### ANALYSIS_CONFIG

Main configuration dictionary containing analysis parameters:

```python
ANALYSIS_CONFIG: Dict[str, Any] = {
    'start_date': '2019-01-01',
    'end_date': '2024-12-31',
    'commodities': [
        'Wheat', 'Wheat flour', 'Rice (imported)', 'Sugar',
        'Oil (vegetable)', 'Beans (kidney red)', 'Salt',
        'Fuel (diesel)', 'Fuel (petrol-gasoline)'
    ],
    'buffer_km': 50,              # Buffer for spatial matching
    'threshold_events': 50,       # High conflict threshold
    'min_market_coverage': 0.3,   # Minimum commodity coverage
    'panel_frequency': 'MS',      # Month start frequency
    'interpolation_method': 'linear',
    'min_price_observations': 3
}
```

## Environment Variables

### API Credentials

```python
# ACLED API access
ACLED_API_KEY: Optional[str] = os.getenv('ACLED_API_KEY')
ACLED_API_EMAIL: Optional[str] = os.getenv('ACLED_API_EMAIL')

# HDX configuration
HDX_API_KEY: Optional[str] = os.getenv('HDX_API_KEY')
HDX_SITE: str = os.getenv('HDX_SITE', 'prod')  # 'prod' or 'test'
```

### Setting Environment Variables

Create a `.env` file in the project root:
```bash
ACLED_API_KEY=your_key_here
ACLED_API_EMAIL=<EMAIL>
HDX_API_KEY=your_hdx_key
HDX_SITE=prod
```

Or export directly:
```bash
export ACLED_API_KEY=your_key_here
export ACLED_API_EMAIL=<EMAIL>
```

## File Pattern Constants

### Raw Data Patterns

```python
# File glob patterns for data discovery
WFP_PATTERN: str = "wfp_food_prices_*.csv"
ACAPS_PATTERN: str = "*Yemen Analysis Hub - Areas of control.zip"
ACLED_PATTERN: str = "acled_yemen_events_*.csv"
```

### Processed Data Paths

```python
# Standard processed data locations
INTEGRATED_PANEL: Path = PROCESSED_DATA_DIR / 'panels/integrated_panel.parquet'
SMART_PANEL: Path = PROCESSED_DATA_DIR / 'wfp_smart_panel.parquet'
CONFLICT_METRICS: Path = PROCESSED_DATA_DIR / 'conflict/conflict_metrics.parquet'

# Spatial data
MARKET_ZONES: Path = PROCESSED_DATA_DIR / 'spatial/market_zones_temporal.parquet'
BOUNDARY_MARKETS: Path = PROCESSED_DATA_DIR / 'spatial/boundary_markets.csv'
```

## Logging Configuration

```python
LOG_CONFIG: Dict[str, Any] = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s %(filename)s:%(lineno)d: %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'standard',
            'level': 'INFO'
        },
        'file': {
            'class': 'logging.FileHandler',
            'filename': str(LOGS_DIR / 'yemen_market.log'),
            'formatter': 'detailed',
            'level': 'DEBUG'
        }
    },
    'root': {
        'handlers': ['console', 'file'],
        'level': 'INFO'
    }
}
```

## Examples

### Basic Usage

```python
from yemen_market.config.settings import (
    RAW_DATA_DIR, PROCESSED_DATA_DIR, ANALYSIS_CONFIG
)

# Construct file paths
wfp_raw = RAW_DATA_DIR / 'hdx/wfp-food-prices-for-yemen/wfp_food_prices_202505.csv'
panel_output = PROCESSED_DATA_DIR / 'panels/integrated_panel.parquet'

# Use configuration
start_date = ANALYSIS_CONFIG['start_date']
commodities = ANALYSIS_CONFIG['commodities']

# Check if paths exist
if not wfp_raw.exists():
    print(f"WFP data not found at {wfp_raw}")

# Create directories if needed
panel_output.parent.mkdir(parents=True, exist_ok=True)
```

### Configuration Access

```python
from yemen_market.config.settings import ANALYSIS_CONFIG

# Get specific values with defaults
buffer_km = ANALYSIS_CONFIG.get('buffer_km', 50)
min_coverage = ANALYSIS_CONFIG.get('min_market_coverage', 0.3)

# Iterate commodities
for commodity in ANALYSIS_CONFIG['commodities']:
    print(f"Processing {commodity}")
```

### Environment Variable Usage

```python
from yemen_market.config.settings import ACLED_API_KEY, ACLED_API_EMAIL

if not ACLED_API_KEY:
    raise ValueError("ACLED_API_KEY environment variable not set")

# Use in API client
client = ACLEDClient(api_key=ACLED_API_KEY, email=ACLED_API_EMAIL)
```

## Extending Configuration

To add new configuration parameters:

1. **Update settings.py**:
   ```python
   # Add to ANALYSIS_CONFIG
   ANALYSIS_CONFIG['new_parameter'] = 'value'
   
   # Add new directory
   NEW_DATA_DIR = DATA_DIR / 'new_data'
   NEW_DATA_DIR.mkdir(exist_ok=True)
   ```

2. **Use in code**:
   ```python
   from yemen_market.config.settings import ANALYSIS_CONFIG
   
   new_param = ANALYSIS_CONFIG.get('new_parameter', 'default')
   ```

3. **Update this documentation** with the new parameters

## See Also

- [Environment Setup](../04-development/setup/environment-setup.md) - Development environment configuration
- [Data Pipeline Guide](../02-user-guides/data-pipeline.md) - Using data directories
- [Logging Guide](../utils/logging.md) - Enhanced logging configuration