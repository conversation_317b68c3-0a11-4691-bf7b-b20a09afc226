# Data Preparation API Reference

**Target Audience**: Data Scientists, Econometricians  
**Module**: `yemen_market.features.data_preparation`

## Overview

The `data_preparation` module provides functions for preparing panel data for econometric analysis, including data quality assessment, outlier treatment, stationarity testing, and feature engineering. It handles common data preparation tasks needed before running econometric models.

## Functions

### generate_data_quality_report

```python
def generate_data_quality_report(
    panel: pd.DataFrame
) -> Dict[str, Any]:
    """Generate comprehensive data quality metrics for the panel dataset."""
```

Analyzes panel data quality including coverage, extreme values, and conflict statistics.

#### Parameters
- **panel** (`pd.DataFrame`): The panel DataFrame to analyze

#### Returns
- **Dict[str, Any]**: Quality metrics including:
  - `price_coverage`: Percentage of non-missing price observations
  - `conflict_coverage`: Percentage of observations with conflict data
  - `extreme_values_count`: Number of potential outliers
  - `market_coverage`: Markets with sufficient data
  - `commodity_coverage`: Commodities with sufficient data

### winsorize_prices

```python
def winsorize_prices(
    panel: pd.DataFrame,
    limits: Tuple[float, float] = (0.01, 0.01)
) -> pd.DataFrame:
    """Winsorize extreme price values by commodity to handle outliers."""
```

Applies winsorization to price data, capping extreme values at specified percentiles.

#### Parameters
- **panel** (`pd.DataFrame`): Panel DataFrame with price data
- **limits** (`Tuple[float, float]`): Lower and upper percentiles for winsorization (default: 1% tails)

#### Returns
- **pd.DataFrame**: Panel with winsorized price columns added (`price_usd_winsorized`)

#### Example
```python
# Winsorize at 1% and 99% percentiles
panel_winsorized = winsorize_prices(panel, limits=(0.01, 0.01))

# More aggressive winsorization for volatile data
panel_winsorized = winsorize_prices(panel, limits=(0.05, 0.05))
```

### test_panel_stationarity

```python
def test_panel_stationarity(
    panel: pd.DataFrame,
    min_obs: int = 30
) -> Tuple[pd.DataFrame, Dict[str, float]]:
    """Run panel unit root tests on price series."""
```

Performs panel unit root tests to check stationarity of price series.

#### Parameters
- **panel** (`pd.DataFrame`): Panel DataFrame with price data
- **min_obs** (`int`): Minimum observations required for testing (default: 30)

#### Returns
- **Tuple[pd.DataFrame, Dict[str, float]]**: 
  - Detailed results by market-commodity pair
  - Summary statistics including percentage of stationary series

### define_conflict_regimes

```python
def define_conflict_regimes(
    panel: pd.DataFrame
) -> pd.DataFrame:
    """Define conflict intensity regimes based on distribution quantiles."""
```

Creates categorical conflict intensity variables based on quantile thresholds.

#### Parameters
- **panel** (`pd.DataFrame`): Panel DataFrame with conflict data

#### Returns
- **pd.DataFrame**: Panel with conflict regime indicators:
  - `conflict_regime`: Categorical (low, medium, high)
  - `high_conflict`: Binary indicator for high conflict periods

### add_econometric_features

```python
def add_econometric_features(
    panel: pd.DataFrame
) -> pd.DataFrame:
    """Add standard econometric features including log transformations and time trends."""
```

Adds common features needed for econometric analysis.

#### Parameters
- **panel** (`pd.DataFrame`): Panel DataFrame

#### Returns
- **pd.DataFrame**: Panel with added features:
  - `log_price`: Natural log of prices
  - `price_change`: First difference of log prices
  - `time_trend`: Linear time trend
  - `time_trend_squared`: Quadratic time trend

### add_spatial_features

```python
def add_spatial_features(
    panel: pd.DataFrame,
    k: int = 3,
    distance_matrix: Optional[pd.DataFrame] = None
) -> pd.DataFrame:
    """Add K-nearest neighbor spatial features to panel data."""
```

Constructs spatial lag variables using nearest neighbor averaging.

#### Parameters
- **panel** (`pd.DataFrame`): Panel data with market locations
- **k** (`int`): Number of nearest neighbors (default: 3)
- **distance_matrix** (`pd.DataFrame`, optional): Pre-calculated distance matrix

#### Returns
- **pd.DataFrame**: Panel with spatial features:
  - `spatial_lag_price`: Average price of k nearest neighbors
  - `spatial_lag_conflict`: Average conflict of k nearest neighbors
  - `inverse_distance_weighted_price`: Distance-weighted average price
  - `spatial_price_deviation`: Deviation from spatial average

#### Example
```python
# Add spatial features with 3 nearest neighbors
panel_spatial = add_spatial_features(panel, k=3)

# Use pre-calculated distances for efficiency
distances = calculate_market_distances(panel)
panel_spatial = add_spatial_features(panel, k=5, distance_matrix=distances)
```

### calculate_market_distances

```python
def calculate_market_distances(
    markets_df: pd.DataFrame
) -> pd.DataFrame:
    """Calculate haversine distances between all market pairs."""
```

Computes great circle distances between markets using coordinates.

#### Parameters
- **markets_df** (`pd.DataFrame`): DataFrame with columns: market, latitude, longitude

#### Returns
- **pd.DataFrame**: Distance matrix with markets as both index and columns

### add_exchange_rate_features

```python
def add_exchange_rate_features(
    panel: pd.DataFrame
) -> pd.DataFrame:
    """Add dual exchange rate system features for Yemen's parallel currency markets."""
```

Creates features capturing Yemen's dual exchange rate dynamics.

#### Parameters
- **panel** (`pd.DataFrame`): Panel data with exchange rate information

#### Returns
- **pd.DataFrame**: Panel with exchange rate features:
  - `er_premium`: Premium of parallel over official rate (%)
  - `er_premium_x_DFA`: Interaction with DFA control zones
  - `er_premium_ma3`: 3-month moving average of premium
  - `er_volatility`: Rolling standard deviation of exchange rate
  - `log_parallel_rate`, `log_official_rate`: Log transformations

#### Example
```python
# Add exchange rate features
panel_er = add_exchange_rate_features(panel)

# Analyze exchange rate dynamics
print(f"Average premium: {panel_er['er_premium'].mean():.1f}%")
print(f"Premium in DFA areas: {panel_er[panel_er['DFA_control']==1]['er_premium'].mean():.1f}%")
```

### validate_for_modeling

```python
def validate_for_modeling(
    panel: pd.DataFrame,
    strict: bool = False
) -> Dict[str, Any]:
    """Validate panel is ready for econometric modeling."""
```

Performs comprehensive validation checks before modeling.

#### Parameters
- **panel** (`pd.DataFrame`): Panel DataFrame to validate
- **strict** (`bool`): Whether to use strict academic standards (default: False)

#### Returns
- **Dict[str, Any]**: Validation results:
  - `valid`: Whether panel passes all checks
  - `errors`: List of critical errors
  - `warnings`: List of non-critical issues
  - `summary`: Summary statistics

### save_prepared_data

```python
def save_prepared_data(
    panel: pd.DataFrame,
    output_dir: Path
) -> Dict[str, Path]:
    """Save prepared panel data and metadata."""
```

Saves prepared data with versioning and metadata.

#### Parameters
- **panel** (`pd.DataFrame`): Prepared panel DataFrame
- **output_dir** (`Path`): Output directory for saving files

#### Returns
- **Dict[str, Path]**: Paths to saved files

## Complete Workflow Example

```python
from yemen_market.features.data_preparation import (
    generate_data_quality_report,
    winsorize_prices,
    test_panel_stationarity,
    add_spatial_features,
    add_exchange_rate_features,
    add_econometric_features,
    validate_for_modeling,
    save_prepared_data
)
from pathlib import Path

# Load raw panel
panel = pd.read_parquet("data/processed/panel.parquet")

# 1. Quality assessment
quality = generate_data_quality_report(panel)
print(f"Initial quality - Price coverage: {quality['price_coverage']:.1%}")

# 2. Handle outliers
panel = winsorize_prices(panel, limits=(0.01, 0.01))

# 3. Test stationarity
stationarity_results, summary = test_panel_stationarity(panel)
print(f"Stationary series: {summary['stationary_pct']:.1%}")

# 4. Add features
panel = add_spatial_features(panel, k=3)
panel = add_exchange_rate_features(panel)
panel = add_econometric_features(panel)

# 5. Validate
validation = validate_for_modeling(panel, strict=False)
if validation['valid']:
    print("✓ Panel ready for modeling")
else:
    print("✗ Issues found:", validation['errors'])

# 6. Save
output_dir = Path("data/prepared")
saved_files = save_prepared_data(panel, output_dir)
print(f"Saved to: {saved_files['panel_path']}")
```

## Performance Considerations

- **Spatial Features**: K-NN calculations scale as O(n²). Use pre-calculated distance matrix for repeated operations.
- **Winsorization**: Performed by commodity group to maintain relative price differences.
- **Stationarity Tests**: Can be slow for large panels. Consider parallel processing.

## See Also

- [`features/feature_engineer.md`](feature_engineer.md) - Additional feature engineering
- [`data/panel_builder.md`](../data/panel_builder.md) - Panel construction
- [User Guide: Data Pipeline](../../02-user-guides/data-pipeline.md)