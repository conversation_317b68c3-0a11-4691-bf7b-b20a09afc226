# FeatureEngineer API Reference

**Target Audience**: Data Scientists, Machine Learning Engineers  
**Module**: `yemen_market.features.feature_engineering`

## Overview

The `FeatureEngineer` class creates derived features for econometric analysis including temporal, spatial, interaction, and threshold features. It provides a comprehensive feature engineering pipeline optimized for panel data analysis.

## Classes

### FeatureEngineer

```python
class FeatureEngineer:
    """Create derived features for econometric and machine learning models."""
    
    def __init__(
        self,
        temporal_lags: List[int] = [1, 2, 3],
        rolling_windows: List[int] = [3, 6],
        spatial_neighbors: int = 5
    ):
        """Initialize feature engineer with configuration."""
```

#### Parameters

- **temporal_lags** (`List[int]`): Lag periods for temporal features (default: [1, 2, 3])
- **rolling_windows** (`List[int]`): Window sizes for rolling statistics (default: [3, 6])
- **spatial_neighbors** (`int`): Number of spatial neighbors to consider (default: 5)

## Methods

### fit_transform

```python
def fit_transform(
    self,
    df: pd.DataFrame
) -> pd.DataFrame:
    """Create all features for the input dataset."""
```

Applies the complete feature engineering pipeline to the input data.

#### Parameters
- **df** (`pd.DataFrame`): Input panel data with required columns

#### Returns
- **pd.DataFrame**: Data with all engineered features added

### create_all_features

```python
def create_all_features(
    self,
    df: pd.DataFrame
) -> pd.DataFrame:
    """Alias for fit_transform()."""
```

Alternative method name for consistency with scikit-learn API.

## Feature Creation Functions

### create_temporal_features

```python
def create_temporal_features(
    df: pd.DataFrame,
    lags: List[int] = [1, 2, 3],
    windows: List[int] = [3, 6]
) -> pd.DataFrame:
    """Create time-based features including lags and rolling statistics."""
```

Generates temporal features for time series analysis.

#### Features Created
- **Lags**: `{variable}_lag{n}` for each numeric variable
- **Differences**: `{variable}_diff`, `{variable}_pct_change`
- **Rolling statistics**: 
  - `{variable}_ma{n}`: Moving average
  - `{variable}_std{n}`: Rolling standard deviation
  - `{variable}_cv{n}`: Coefficient of variation
- **Time trends**: `time_trend`, `time_trend_squared`

#### Example
```python
df_temporal = create_temporal_features(
    df,
    lags=[1, 2, 3, 6, 12],  # Include seasonal lags
    windows=[3, 6, 12]       # Multiple window sizes
)
```

### create_interaction_features

```python
def create_interaction_features(
    df: pd.DataFrame
) -> pd.DataFrame:
    """Create interaction terms between key variables."""
```

Generates multiplicative interaction terms to capture joint effects.

#### Features Created
- **Price × Conflict**: `price_x_conflict`, `price_x_high_conflict`
- **Zone × Time**: `zone_{zone}_x_trend` for each control zone
- **Exchange × Zone**: `exchange_rate_x_{zone}` for each zone
- **Conflict × Zone**: `conflict_x_{zone}` for each zone
- **Commodity × Zone**: Commodity-specific zone effects

### create_threshold_indicators

```python
def create_threshold_indicators(
    df: pd.DataFrame
) -> pd.DataFrame:
    """Create binary threshold indicators for regime switching."""
```

Generates binary indicators for threshold models and structural breaks.

#### Features Created
- **Conflict regimes**: 
  - `conflict_regime_low`: Below 25th percentile
  - `conflict_regime_medium`: 25th-75th percentile
  - `conflict_regime_high`: Above 75th percentile
- **Conflict threshold**: `high_conflict_threshold` (>50 events)
- **Exchange rate thresholds**: 
  - `rate_diff_threshold_10`: >10% difference
  - `rate_diff_threshold_20`: >20% difference
- **Volatility**: `high_volatility` (>75th percentile of rolling std)
- **Zone indicators**: `is_contested` (control changed in last 6 months)

### create_spatial_features

```python
def create_spatial_features(
    df: pd.DataFrame,
    n_neighbors: int = 5
) -> pd.DataFrame:
    """Create spatial relationship features using K-nearest neighbors."""
```

Generates spatial lag variables and distance-based features.

#### Features Created
- **Spatial lags**: 
  - `price_spatial_lag`: Average price of k nearest neighbors
  - `conflict_spatial_lag`: Average conflict of k nearest neighbors
- **Spatial differences**: 
  - `price_spatial_diff`: Price minus spatial average
  - `conflict_spatial_diff`: Conflict minus spatial average
- **Distance features**: 
  - `distance_to_other_zone`: Distance to nearest different control zone
  - `n_neighbors_same_zone`: Count of neighbors in same control zone

#### Requirements
- Requires `lat` and `lon` columns in the dataframe
- Needs at least 2 unique markets

### create_conflict_features

```python
def create_conflict_features(
    df: pd.DataFrame
) -> pd.DataFrame:
    """Create conflict-specific analytical features."""
```

Generates detailed conflict dynamics features.

#### Features Created
- **Intensity measures**:
  - `conflict_quartile`: Quartile ranking (1-4)
  - `conflict_zscore`: Standardized conflict intensity
- **Event composition**:
  - `n_battles_ratio`: Battles as proportion of total events
  - `n_explosions_ratio`: Explosions/remote violence ratio
  - `n_protests_ratio`: Protests ratio
- **Temporal patterns**:
  - `conflict_persistent`: High conflict for 3+ consecutive periods
  - `conflict_shock`: Large increase from previous period (>2 std)
  - `conflict_trend`: Linear trend coefficient over 6 periods
- **Actor analysis**:
  - `dominant_actor`: Actor with most events in period
  - `actor_diversity`: Number of unique actors

## Complete Example

```python
from yemen_market.features import FeatureEngineer
import pandas as pd

# Load panel data
panel = pd.read_parquet('data/processed/panels/integrated_panel.parquet')

# Initialize feature engineer with custom configuration
engineer = FeatureEngineer(
    temporal_lags=[1, 2, 3, 6, 12],  # Include seasonal lags
    rolling_windows=[3, 6, 12, 24],   # Multiple time horizons
    spatial_neighbors=10              # More spatial context
)

# Create all features
features = engineer.fit_transform(panel)

# Analyze feature creation
original_cols = panel.shape[1]
new_cols = features.shape[1] - original_cols
print(f"Original columns: {original_cols}")
print(f"New features created: {new_cols}")
print(f"Total columns: {features.shape[1]}")

# Feature categories
feature_cols = features.columns.tolist()
temporal = [col for col in feature_cols if any(x in col for x in ['lag', 'ma', 'diff', 'std'])]
interaction = [col for col in feature_cols if '_x_' in col]
threshold = [col for col in feature_cols if any(x in col for x in ['threshold', 'regime'])]
spatial = [col for col in feature_cols if 'spatial' in col]
conflict = [col for col in feature_cols if 'conflict' in col and col not in panel.columns]

print(f"\nFeature breakdown:")
print(f"Temporal features: {len(temporal)}")
print(f"Interaction features: {len(interaction)}")
print(f"Threshold features: {len(threshold)}")
print(f"Spatial features: {len(spatial)}")
print(f"Conflict features: {len(conflict)}")

# Save engineered features
features.to_parquet('data/processed/panels/features_panel.parquet')
```

## Feature Selection

With many features created, consider feature selection:

```python
from sklearn.feature_selection import SelectKBest, f_classif
from sklearn.preprocessing import StandardScaler

# Prepare features for selection
feature_cols = [col for col in features.columns if col not in ['date', 'market_id', 'commodity']]
X = features[feature_cols]
y = features['high_conflict_threshold']  # Example target

# Standardize features
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X.fillna(0))

# Select top features
selector = SelectKBest(f_classif, k=50)
selector.fit(X_scaled, y)

# Get selected features
selected_features = X.columns[selector.get_support()].tolist()
print(f"Top 50 features: {selected_features[:10]}...")  # Show first 10
```

## Performance Considerations

- **Memory Usage**: Feature engineering can significantly increase memory usage. Consider chunking for large datasets.
- **Computation Time**: Spatial features are most computationally intensive (O(n²) for distances).
- **Missing Data**: Features handle missing data gracefully but may propagate NaNs through calculations.

## Best Practices

1. **Feature Creation Order**: Create temporal features before interaction features
2. **Multicollinearity**: Check VIF after feature creation
3. **Economic Interpretation**: Ensure features have meaningful interpretation
4. **Standardization**: Standardize features before modeling if using regularization
5. **Documentation**: Document custom feature definitions

## See Also

- [`features/data_preparation.md`](data_preparation.md) - Data preparation utilities
- [`models/three_tier/`](../models/three_tier/) - Models using these features
- [User Guide: Feature Engineering](../../02-user-guides/running-analyses.md)