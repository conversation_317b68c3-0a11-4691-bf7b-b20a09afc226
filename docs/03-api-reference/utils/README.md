# Utilities API Reference

**Target Audience**: <PERSON><PERSON><PERSON>, DevOps Engineers  
**Module**: `yemen_market.utils`

## Overview

The utilities package provides essential support functions for logging, performance monitoring, security, and other cross-cutting concerns throughout the Yemen Market Integration Platform.

## Available Modules

### [logging.md](logging.md) - Logging Utilities
- Structured logging with JSON support
- Performance tracking and metrics
- Error handling with context
- Integration with monitoring systems (Sentry, Prometheus, Datadog)
- Async and rotating file handlers

### performance.py - Performance Monitoring
- Execution time tracking
- Memory usage monitoring
- Resource utilization metrics
- Performance decorators
- Profiling utilities

### security.py - Security Utilities
- Input validation and sanitization
- API key management
- Rate limiting helpers
- Encryption/decryption utilities
- Security headers management

## Quick Examples

### Logging
```python
from yemen_market.utils.logging import setup_logging, StructuredLogger

# Setup logging
logger = setup_logging(
    name="yemen_market",
    level="INFO",
    json_format=True
)

# Use structured logging
struct_logger = StructuredLogger("analysis")
struct_logger.log_event(
    "model_completed",
    "Analysis finished successfully",
    model="tier1",
    duration=12.5
)
```

### Performance Monitoring
```python
from yemen_market.utils.performance import track_performance

@track_performance("data_processing")
def process_large_dataset(df):
    # Automatically tracks execution time and memory
    return df.groupby(['market', 'commodity']).agg('mean')
```

### Security
```python
from yemen_market.utils.security import validate_input, sanitize_sql

# Validate user input
clean_market_id = validate_input(
    user_input,
    pattern=r'^[A-Z]{2}_[A-Z]+$',
    max_length=50
)

# Sanitize SQL parameters
safe_query = sanitize_sql(user_query)
```

## See Also

- [User Guide: Logging & Monitoring](../../02-user-guides/logging-monitoring.md)
- [Development: Best Practices](../../04-development/best-practices.md)
- [Architecture: Security](../../01-architecture/security.md)