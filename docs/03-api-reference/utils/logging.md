# Logging Utilities API Reference

**Target Audience**: <PERSON><PERSON><PERSON>, DevOps Engineers  
**Module**: `yemen_market.utils.logging`

## Overview

The logging utilities provide structured, performance-aware logging with automatic context enrichment, error tracking, and integration with monitoring systems. The system supports both development and production environments with appropriate log levels and outputs.

## Core Components

### setup_logging

```python
def setup_logging(
    name: str = "yemen_market",
    level: str = "INFO",
    log_file: Optional[str] = None,
    json_format: bool = False,
    enable_performance: bool = True,
    enable_sentry: bool = False
) -> logging.Logger:
    """Configure and return application logger with enhanced features."""
```

#### Parameters
- **name** (`str`): Logger name/namespace
- **level** (`str`): Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- **log_file** (`str`, optional): Path to log file
- **json_format** (`bool`): Use JSON formatting for structured logs
- **enable_performance** (`bool`): Enable performance logging
- **enable_sentry** (`bool`): Enable Sentry error tracking

#### Returns
- **logging.Logger**: Configured logger instance

#### Example
```python
from yemen_market.utils.logging import setup_logging

# Development setup
logger = setup_logging(
    name="yemen_market.analysis",
    level="DEBUG",
    log_file="logs/analysis.log"
)

# Production setup
logger = setup_logging(
    name="yemen_market",
    level="INFO",
    json_format=True,
    enable_sentry=True
)
```

### StructuredLogger

```python
class StructuredLogger:
    """Enhanced logger with structured logging and context management."""
    
    def __init__(
        self,
        name: str,
        context: Optional[Dict[str, Any]] = None
    ):
        """Initialize structured logger with optional context."""
```

#### Methods

##### with_context

```python
def with_context(self, **kwargs) -> 'StructuredLogger':
    """Add context that will be included in all log messages."""
```

##### log_event

```python
def log_event(
    self,
    event_type: str,
    message: str,
    level: str = "INFO",
    **kwargs
):
    """Log structured event with type and additional fields."""
```

##### log_metric

```python
def log_metric(
    self,
    metric_name: str,
    value: float,
    unit: str = None,
    tags: Dict[str, str] = None
):
    """Log performance metric."""
```

#### Example
```python
from yemen_market.utils.logging import StructuredLogger

# Create logger with context
logger = StructuredLogger(
    "analysis",
    context={"run_id": "abc123", "environment": "production"}
)

# Log events with structure
logger.log_event(
    "model_training",
    "Started training Tier 1 model",
    model_type="pooled_panel",
    n_observations=50000
)

# Log metrics
logger.log_metric(
    "model_fit_time",
    value=12.5,
    unit="seconds",
    tags={"model": "tier1", "commodity": "wheat"}
)

# Add temporary context
with logger.with_context(commodity="rice"):
    logger.info("Processing rice data")  # Includes commodity in log
```

## Performance Logging

### PerformanceLogger

```python
class PerformanceLogger:
    """Track and log performance metrics."""
    
    def __init__(
        self,
        logger: logging.Logger,
        enable_memory_tracking: bool = True
    ):
        """Initialize performance logger."""
```

#### Context Managers

##### log_execution_time

```python
@contextmanager
def log_execution_time(
    operation: str,
    log_memory: bool = True,
    threshold_seconds: float = 1.0
):
    """Log execution time for code block."""
```

##### track_memory_usage

```python
@contextmanager
def track_memory_usage(operation: str):
    """Track memory usage during operation."""
```

#### Example
```python
from yemen_market.utils.logging import PerformanceLogger

perf_logger = PerformanceLogger(logger)

# Track execution time
with perf_logger.log_execution_time("data_loading"):
    data = pd.read_parquet("large_file.parquet")
    # Automatically logs: "data_loading completed in 2.34s"

# Track memory usage
with perf_logger.track_memory_usage("model_training"):
    model.fit(data)
    # Logs memory delta: "+125.3 MB"

# Conditional logging based on threshold
with perf_logger.log_execution_time("quick_operation", threshold_seconds=5.0):
    result = quick_function()  # Only logs if takes > 5 seconds
```

### Method Decorators

```python
from yemen_market.utils.logging import log_execution, log_errors

class DataProcessor:
    @log_execution("process_data")  # Automatically logs execution time
    def process_data(self, df: pd.DataFrame) -> pd.DataFrame:
        # Processing logic
        return processed_df
    
    @log_errors(reraise=True)  # Logs exceptions before re-raising
    def risky_operation(self):
        # Code that might fail
        pass
```

## Error Tracking

### ErrorLogger

```python
class ErrorLogger:
    """Enhanced error logging with context and recovery suggestions."""
    
    def log_error(
        self,
        error: Exception,
        context: Dict[str, Any] = None,
        suggestions: List[str] = None,
        notify: bool = False
    ):
        """Log error with full context and recovery suggestions."""
```

#### Example
```python
from yemen_market.utils.logging import ErrorLogger

error_logger = ErrorLogger(logger)

try:
    result = risky_calculation(data)
except ValueError as e:
    error_logger.log_error(
        e,
        context={
            "function": "risky_calculation",
            "data_shape": data.shape,
            "data_type": str(data.dtype)
        },
        suggestions=[
            "Check for missing values in input data",
            "Ensure all values are numeric",
            "Verify data meets minimum size requirements"
        ],
        notify=True  # Send alert to monitoring
    )
```

## Log Formatting

### JSONFormatter

```python
class JSONFormatter(logging.Formatter):
    """Format logs as JSON for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON."""
```

#### Output Example
```json
{
    "timestamp": "2024-01-15T10:30:45.123Z",
    "level": "INFO",
    "logger": "yemen_market.models",
    "message": "Model training completed",
    "context": {
        "model_type": "tier1_pooled",
        "n_observations": 50000,
        "r_squared": 0.823,
        "execution_time": 12.5
    },
    "host": "worker-01",
    "process_id": 12345
}
```

### ColoredFormatter

```python
class ColoredFormatter(logging.Formatter):
    """Colorized console output for development."""
    
    COLORS = {
        'DEBUG': 'cyan',
        'INFO': 'green',
        'WARNING': 'yellow',
        'ERROR': 'red',
        'CRITICAL': 'red,bold'
    }
```

## Log Handlers

### RotatingFileHandler

```python
def setup_rotating_file_handler(
    filename: str,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    formatter: Optional[logging.Formatter] = None
) -> logging.Handler:
    """Create rotating file handler with size limit."""
```

### AsyncHandler

```python
class AsyncHandler(logging.Handler):
    """Non-blocking asynchronous log handler."""
    
    def __init__(
        self,
        target_handler: logging.Handler,
        queue_size: int = 1000
    ):
        """Initialize async handler with target."""
```

## Configuration Management

### LogConfig

```python
@dataclass
class LogConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "json"  # or "text"
    outputs: List[str] = field(default_factory=lambda: ["console"])
    file_path: Optional[str] = None
    max_file_size: int = 10_000_000  # 10MB
    backup_count: int = 5
    enable_performance: bool = True
    enable_sentry: bool = False
    sentry_dsn: Optional[str] = None
    
    @classmethod
    def from_yaml(cls, path: str) -> 'LogConfig':
        """Load configuration from YAML file."""
```

#### Example Configuration
```yaml
# config/logging.yaml
level: INFO
format: json
outputs:
  - console
  - file
file_path: logs/yemen_market.log
max_file_size: 50000000  # 50MB
backup_count: 10
enable_performance: true
enable_sentry: true
sentry_dsn: ${SENTRY_DSN}  # From environment
```

## Complete Example

```python
from yemen_market.utils.logging import (
    setup_logging,
    StructuredLogger,
    PerformanceLogger,
    log_execution,
    log_errors
)
import pandas as pd
import numpy as np

# Setup logging
base_logger = setup_logging(
    name="yemen_market.analysis",
    level="INFO",
    log_file="logs/analysis.log",
    json_format=True
)

# Create structured logger
logger = StructuredLogger(
    "analysis",
    context={"session_id": "2024-01-15-001"}
)

# Create performance logger
perf_logger = PerformanceLogger(base_logger)

class MarketAnalyzer:
    def __init__(self):
        self.logger = logger.with_context(component="analyzer")
        self.perf_logger = perf_logger
    
    @log_execution("load_data")
    @log_errors()
    def load_data(self, path: str) -> pd.DataFrame:
        """Load market data with logging."""
        self.logger.info(f"Loading data from {path}")
        
        with self.perf_logger.track_memory_usage("data_loading"):
            df = pd.read_parquet(path)
            
        self.logger.log_event(
            "data_loaded",
            f"Loaded {len(df):,} records",
            rows=len(df),
            columns=len(df.columns),
            memory_usage=df.memory_usage(deep=True).sum() / 1024**2
        )
        
        return df
    
    @log_execution("analyze_markets")
    def analyze_markets(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze market integration."""
        results = {}
        
        # Log analysis start
        self.logger.log_event(
            "analysis_started",
            "Beginning market integration analysis",
            n_markets=df['market_id'].nunique(),
            n_commodities=df['commodity'].nunique(),
            date_range=f"{df['date'].min()} to {df['date'].max()}"
        )
        
        # Perform analysis with performance tracking
        with self.perf_logger.log_execution_time("calculate_integration"):
            try:
                # Integration calculations
                integration_scores = self._calculate_integration(df)
                results['integration'] = integration_scores
                
                # Log metrics
                self.logger.log_metric(
                    "avg_integration_score",
                    value=np.mean(integration_scores),
                    tags={"analysis": "market_integration"}
                )
                
            except Exception as e:
                self.logger.error(
                    "Integration calculation failed",
                    exc_info=True,
                    data_shape=df.shape
                )
                raise
        
        # Log completion
        self.logger.log_event(
            "analysis_completed",
            "Market integration analysis finished",
            duration=perf_logger.get_operation_time("analyze_markets"),
            results_keys=list(results.keys())
        )
        
        return results
    
    def _calculate_integration(self, df: pd.DataFrame) -> np.ndarray:
        """Calculate integration scores."""
        # Implementation
        return np.random.rand(df['market_id'].nunique())

# Usage
analyzer = MarketAnalyzer()

try:
    # This will log:
    # - Execution start
    # - Memory usage
    # - Data statistics
    # - Execution time
    data = analyzer.load_data("data/processed/panel.parquet")
    
    # This will log:
    # - Analysis progress
    # - Performance metrics
    # - Results summary
    results = analyzer.analyze_markets(data)
    
    # Log final summary
    logger.log_event(
        "session_completed",
        "Analysis session completed successfully",
        level="INFO",
        total_time=perf_logger.get_total_time(),
        peak_memory=perf_logger.get_peak_memory()
    )
    
except Exception as e:
    logger.log_event(
        "session_failed",
        f"Analysis session failed: {str(e)}",
        level="ERROR",
        error_type=type(e).__name__
    )
    raise
```

## Monitoring Integration

### Prometheus Metrics

```python
from yemen_market.utils.logging import PrometheusLogger

# Setup Prometheus metrics
metrics_logger = PrometheusLogger()

# Log metrics for Prometheus
metrics_logger.record_histogram(
    "model_fit_duration_seconds",
    value=12.5,
    labels={"model": "tier1", "commodity": "wheat"}
)

metrics_logger.increment_counter(
    "model_fits_total",
    labels={"model": "tier1", "status": "success"}
)
```

### Datadog Integration

```python
from yemen_market.utils.logging import DatadogLogger

# Setup Datadog
dd_logger = DatadogLogger(api_key="your-api-key")

# Send custom metrics
dd_logger.gauge("market.integration.score", 0.85, tags=["market:sanaa"])
dd_logger.histogram("api.response_time", 0.125, tags=["endpoint:/analyze"])
```

## Best Practices

1. **Use Structured Logging**: Always use structured logging for production
2. **Include Context**: Add relevant context to all log messages
3. **Log Levels**: Use appropriate log levels (DEBUG for development only)
4. **Performance**: Use performance logging for operations > 1 second
5. **Error Context**: Include full context when logging errors
6. **Metrics**: Log business metrics separately from operational logs
7. **Rotation**: Always use log rotation in production
8. **Async Logging**: Use async handlers for high-throughput applications

## See Also

- [`utils/performance.md`](performance.md) - Performance monitoring
- [User Guide: Logging](../../02-user-guides/logging-monitoring.md)
- [Development: Debugging](../../04-development/debugging.md)
- [Deployment: Monitoring](../../deployment/monitoring.md)