# PanelBuilder API Reference

**Target Audience**: <PERSON><PERSON><PERSON>, Data Scientists  
**Module**: `yemen_market.data.panel_builder`

## Overview

The `PanelBuilder` class builds integrated panel datasets for econometric analysis by combining price, conflict, and spatial data. It provides methods for creating balanced panels, integrating multiple data sources, and generating specialized datasets for different analytical approaches.

## Classes

### PanelBuilder

```python
class PanelBuilder:
    """Build integrated panel datasets for econometric analysis."""
    
    def __init__(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        commodities: Optional[List[str]] = None,
        use_smart_panel: bool = True
    ):
        """Initialize panel builder with configuration."""
```

#### Parameters

- **start_date** (`str`, optional): Start date for panel in 'YYYY-MM-DD' format. Defaults to config.
- **end_date** (`str`, optional): End date for panel in 'YYYY-MM-DD' format. Defaults to config.
- **commodities** (`List[str]`, optional): List of commodities to include. Defaults to all available.
- **use_smart_panel** (`bool`): Use smart panel data with 88.4% coverage if available (default: True).

## Methods

### create_core_balanced_panel

```python
def create_core_balanced_panel(
    self,
    min_coverage_pct: float = 85.0,
    min_markets: int = 20,
    min_commodities: int = 15
) -> pd.DataFrame:
    """Create a perfectly balanced panel by selecting core markets and commodities."""
```

Creates a balanced panel with no missing observations by selecting markets and commodities that meet coverage thresholds.

#### Parameters
- **min_coverage_pct** (`float`): Minimum coverage percentage for selection (default: 85.0)
- **min_markets** (`int`): Minimum number of markets for a commodity (default: 20)
- **min_commodities** (`int`): Minimum number of commodities for a market (default: 15)

#### Returns
- **pd.DataFrame**: Perfectly balanced panel (typically 21×16×75 = 25,200 observations)

#### Example
```python
balanced_panel = builder.create_core_balanced_panel(
    min_coverage_pct=85.0,
    min_markets=20,
    min_commodities=15
)
print(f"Shape: {balanced_panel.shape}")  # (25200, 15)
```

### integrate_panel_data

```python
def integrate_panel_data(
    self,
    panel: pd.DataFrame
) -> pd.DataFrame:
    """Integrate conflict, control zones, and geographic data into panel."""
```

Enriches a base panel with conflict metrics, control zone indicators, and spatial features.

#### Parameters
- **panel** (`pd.DataFrame`): Base panel DataFrame with price data

#### Returns
- **pd.DataFrame**: Panel with integrated conflict, control, and spatial features

### build_integrated_panel

```python
def build_integrated_panel(
    self,
    include_conflict: bool = True,
    include_spatial: bool = True,
    save_output: bool = True
) -> pd.DataFrame:
    """Build the main integrated panel dataset."""
```

Builds a comprehensive panel dataset combining all available data sources.

#### Parameters
- **include_conflict** (`bool`): Include conflict event metrics (default: True)
- **include_spatial** (`bool`): Include spatial relationship features (default: True)
- **save_output** (`bool`): Save output to disk (default: True)

#### Returns
- **pd.DataFrame**: Integrated panel dataset with all features

### build_threshold_panel

```python
def build_threshold_panel(
    self,
    price_pairs: Optional[List[Tuple[str, str]]] = None,
    save_output: bool = True
) -> pd.DataFrame:
    """Build panel specifically for threshold cointegration analysis."""
```

Creates a panel structured for pairwise market analysis and threshold models.

#### Parameters
- **price_pairs** (`List[Tuple[str, str]]`, optional): Specific market pairs to analyze
- **save_output** (`bool`): Save output to disk (default: True)

#### Returns
- **pd.DataFrame**: Panel structured for threshold analysis

### build_spatial_panel

```python
def build_spatial_panel(
    self,
    max_distance_km: float = 200.0,
    save_output: bool = True
) -> pd.DataFrame:
    """Build panel for spatial price transmission analysis."""
```

Creates a panel with spatial relationships and distance metrics.

#### Parameters
- **max_distance_km** (`float`): Maximum distance between markets to include (default: 200)
- **save_output** (`bool`): Save output to disk (default: True)

#### Returns
- **pd.DataFrame**: Panel with spatial relationships

### load_integrated_panel

```python
def load_integrated_panel(self) -> pd.DataFrame:
    """Load the fully integrated balanced panel with conflict and control zone data."""
```

Loads a pre-created integrated panel from disk.

#### Returns
- **pd.DataFrame**: Integrated balanced panel DataFrame

### validate_balanced_panel

```python
def validate_balanced_panel(
    self,
    panel: pd.DataFrame
) -> Dict[str, Any]:
    """Validate the balanced panel structure and quality."""
```

Performs comprehensive validation checks on panel structure and data quality.

#### Parameters
- **panel** (`pd.DataFrame`): Panel DataFrame to validate

#### Returns
- **Dict[str, Any]**: Validation results including:
  - `is_balanced`: Whether panel is perfectly balanced
  - `missing_pct`: Percentage of missing values
  - `conflict_coverage`: Conflict data coverage
  - `market_count`: Number of unique markets
  - `commodity_count`: Number of unique commodities

## Data Structures

### Integrated Panel Schema

The integrated panel includes the following columns:

```python
# Identifiers
date: pd.Timestamp          # Date of observation
market_id: str              # Unique market identifier
commodity: str              # Commodity name

# Price data
price_usd: float           # Price in USD
price_local: float         # Price in local currency
exchange_rate: float       # Exchange rate (YER/USD)

# Conflict metrics
conflict_events: int       # Number of conflict events
conflict_intensity: float  # Log-transformed events
conflict_fatalities: int   # Total fatalities

# Spatial features
control_zone: str          # Current control zone
is_boundary_market: bool   # Boundary market indicator
distance_to_boundary: float # Distance to nearest boundary (km)

# Temporal features
month: int                 # Month of year
quarter: int              # Quarter of year
year: int                 # Year
is_ramadan: bool          # Ramadan indicator
```

### Threshold Panel Schema

For pairwise market analysis:

```python
# Identifiers
date: pd.Timestamp         # Date
market_pair: str          # Market pair ID (e.g., "Market_A-Market_B")
commodity: str            # Commodity

# Prices
price_origin: float       # Price in origin market
price_destination: float  # Price in destination market
price_differential: float # Price difference

# Threshold variables
conflict_threshold: bool  # High conflict indicator
distance_km: float       # Distance between markets
same_zone: bool          # Same control zone indicator
```

## Examples

### Basic Usage

```python
from yemen_market.data import PanelBuilder

# Initialize builder
builder = PanelBuilder(
    start_date='2019-01-01',
    end_date='2024-12-31',
    use_smart_panel=True
)

# Build integrated panel
panel = builder.build_integrated_panel(
    include_conflict=True,
    include_spatial=True
)

print(f"Panel shape: {panel.shape}")
print(f"Markets: {panel['market_id'].nunique()}")
print(f"Commodities: {panel['commodity'].nunique()}")
print(f"Date range: {panel['date'].min()} to {panel['date'].max()}")
```

### Creating a Balanced Panel

```python
# Create perfectly balanced panel for econometric analysis
balanced_panel = builder.create_core_balanced_panel(
    min_coverage_pct=85.0,
    min_markets=20,
    min_commodities=15
)

# Integrate additional features
integrated_panel = builder.integrate_panel_data(balanced_panel)

# Validate the panel
validation = builder.validate_balanced_panel(integrated_panel)
print(f"Is balanced: {validation['is_balanced']}")
print(f"Missing percentage: {validation['missing_pct']:.2f}%")

# Save for future use
saved_paths = builder.save_balanced_panels(integrated_panel)
```

### Building Specialized Panels

```python
# For threshold models
threshold_panel = builder.build_threshold_panel()
print(f"Market pairs: {threshold_panel['market_pair'].nunique()}")

# For spatial analysis
spatial_panel = builder.build_spatial_panel(max_distance_km=200)
print(f"Spatial observations: {len(spatial_panel)}")
```

## Performance Considerations

- **Memory Usage**: Large panels can consume significant memory. Use chunking for very large datasets.
- **Processing Time**: Integration operations are optimized but can take 1-2 minutes for full datasets.
- **Caching**: Intermediate results are cached to speed up repeated operations.

## See Also

- [`data/wfp_processor.md`](wfp_processor.md) - WFP price data processing
- [`data/acled_processor.md`](acled_processor.md) - Conflict data processing
- [`data/spatial_joiner.md`](spatial_joiner.md) - Spatial joining utilities
- [User Guide: Balanced Panel Creation](../../02-user-guides/balanced-panel-creation.md)