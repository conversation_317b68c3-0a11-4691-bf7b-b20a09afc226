# WFPProcessor API Reference

**Target Audience**: <PERSON><PERSON><PERSON>, Data Scientists  
**Module**: `yemen_market.data.wfp_processor`

## Overview

The `WFPProcessor` class handles World Food Programme (WFP) price data processing with enhanced features including pcode integration, smart panel creation, and comprehensive exchange rate handling.

## Classes

### WFPProcessor

```python
class WFPProcessor(
    commodities: Optional[List[str]] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    min_market_coverage: float = 0.3
)
```

Process WFP food price data for Yemen market analysis.

#### Parameters

- `commodities` (List[str], optional): List of commodities to analyze. If None, auto-selects based on coverage.
- `start_date` (str, optional): Start date for analysis (format: 'YYYY-MM-DD'). Defaults to config value.
- `end_date` (str, optional): End date for analysis. Defaults to config value.  
- `min_market_coverage` (float): Minimum fraction of markets a commodity must appear in (default: 0.3).

#### Methods

##### process_price_data

```python
def process_price_data(
    raw_data_path: Optional[Union[str, Path]] = None
) -> Tuple[pd.DataFrame, pd.DataFrame]
```

Process WFP price data with enhanced filtering and pcode support.

**Parameters:**
- `raw_data_path` (Union[str, Path], optional): Path to raw WFP CSV file. If None, searches in configured location.

**Returns:**
- `Tuple[pd.DataFrame, pd.DataFrame]`: Tuple of (commodity_prices, exchange_rates)

**Example:**
```python
processor = WFPProcessor()
commodity_prices, exchange_rates = processor.process_price_data()
```

##### create_smart_panels

```python
def create_smart_panels(
    commodity_df: pd.DataFrame
) -> pd.DataFrame
```

Create smart panel data that respects commodity availability patterns.

**Parameters:**
- `commodity_df` (pd.DataFrame): DataFrame with commodity prices

**Returns:**
- `pd.DataFrame`: Panel data with better coverage (typically 88.4%+)

**Example:**
```python
smart_panel = processor.create_smart_panels(commodity_prices)
print(f"Coverage: {smart_panel['price_usd'].notna().mean():.1%}")
```

##### extract_commodity_prices

```python
def extract_commodity_prices(
    df: pd.DataFrame
) -> pd.DataFrame
```

Extract raw commodity price data for panel building.

**Parameters:**
- `df` (pd.DataFrame): Cleaned WFP data

**Returns:**
- `pd.DataFrame`: Commodity price data only

##### extract_exchange_rates

```python
def extract_exchange_rates(
    df: pd.DataFrame
) -> pd.DataFrame
```

Extract exchange rate data from price data.

**Parameters:**
- `df` (pd.DataFrame): Cleaned WFP data  

**Returns:**
- `pd.DataFrame`: Exchange rate data by market and date

## Constants

### Governorate Mappings

```python
GOVERNORATE_MAPPINGS = {
    "Al Dhale'e": "Ad Dale'",
    "Al Hudaydah": "Al Hodeidah",
    "Amanat Al Asimah": "Sana'a City",
    "Hadramaut": "Hadramawt",
    "Sa'ada": "Sa'dah",
    "Taizz": "Ta'iz"
}
```

### Zone Classifications

```python
HOUTHI_GOVERNORATES = [
    "Sana'a", "Sa'ada", "Hajjah", "Al Mahwit", "Dhamar",
    "Raymah", "Ibb", "Amran", "Al Hudaydah"
]

GOVERNMENT_GOVERNORATES = [
    "Aden", "Lahj", "Abyan", "Shabwah", "Hadramaut",
    "Al Maharah", "Socotra", "Al Dhale'e", "Marib", "Al Jawf"
]
```

### Key Commodities

```python
KEY_COMMODITIES = [
    'Wheat', 'Wheat Flour', 'Rice (Imported)', 'Sugar', 
    'Oil (Vegetable)', 'Beans (Kidney Red)', 'Beans (White)', 
    'Salt', 'Fuel (Diesel)', 'Fuel (Petrol-Gasoline)'
]
```

## Examples

### Basic Usage

```python
from yemen_market.data import WFPProcessor

# Initialize processor
processor = WFPProcessor(
    min_market_coverage=0.5,  # Only include widely-available commodities
    start_date='2019-01-01'
)

# Process data
commodity_prices, exchange_rates = processor.process_price_data()

# Create smart panels
smart_panel = processor.create_smart_panels(commodity_prices)

# Check coverage
coverage = smart_panel['price_usd'].notna().sum() / len(smart_panel) * 100
print(f"Price coverage: {coverage:.1f}%")  # Should be ~88.4%
```

### Custom Commodity Selection

```python
# Process only specific commodities
processor = WFPProcessor(
    commodities=['Wheat', 'Sugar', 'Fuel (Diesel)'],
    start_date='2020-01-01',
    end_date='2023-12-31'
)

prices, rates = processor.process_price_data()
```

### Working with Exchange Rates

```python
# Extract and analyze exchange rates
processor = WFPProcessor()
_, exchange_rates = processor.process_price_data()

# Analyze exchange rate differences by zone
houthi_rates = exchange_rates[
    exchange_rates['governorate'].isin(WFPProcessor.HOUTHI_GOVERNORATES)
]
govt_rates = exchange_rates[
    exchange_rates['governorate'].isin(WFPProcessor.GOVERNMENT_GOVERNORATES)
]
```

## Data Processing Pipeline

1. **Load raw data** from CSV with appropriate dtypes
2. **Standardize governorate names** using pcode mappings  
3. **Filter by date range** and clean data
4. **Separate exchange rates** from commodity prices
5. **Auto-select commodities** based on market coverage
6. **Create smart panels** respecting actual availability
7. **Save processed data** with comprehensive summaries

## Output Files

The processor generates the following files in `data/processed/`:

- `wfp_commodity_prices.parquet`: Raw processed commodity prices
- `wfp_exchange_rates.parquet`: Exchange rate data
- `wfp_smart_panel.parquet`: Smart panel with ~88.4% coverage
- `wfp_processing_summary.json`: Processing metadata and statistics

## Error Handling

The processor includes robust error handling:

```python
try:
    prices, rates = processor.process_price_data()
except FileNotFoundError:
    print("WFP data file not found. Please download from HDX.")
except ValueError as e:
    print(f"Data validation error: {e}")
```

## See Also

- [Data Pipeline Guide](../../02-user-guides/data-pipeline.md) - Complete data processing workflow
- [PanelBuilder API](panel_builder.md) - Building panel datasets
- [HDXClient API](hdx_client.md) - Downloading WFP data from HDX