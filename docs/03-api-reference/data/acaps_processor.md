# ACAPSProcessor API Reference

**Target Audience**: Developers, GIS Analysts  
**Module**: `yemen_market.data.acaps_processor`

## Overview

The `ACAPSProcessor` class handles ACAPS (Assessment Capacities Project) control zone data, extracting temporal territorial control information from shapefiles for Yemen conflict analysis.

## Classes

### ACAPSProcessor

```python
class ACAPSProcessor()
```

Process ACAPS territorial control data from shapefiles and ZIP archives.

#### Methods

##### process_control_zones

```python
def process_control_zones(
    file_path: Union[str, Path],
    extract_nested: bool = True
) -> gpd.GeoDataFrame
```

Process ACAPS control zone data from a shapefile or ZIP archive.

**Parameters:**
- `file_path` (Union[str, Path]): Path to shapefile or ZIP file
- `extract_nested` (bool): Handle nested ZIP files (default: True)

**Returns:**
- `gpd.GeoDataFrame`: Processed control zones with standardized columns

**Example:**
```python
processor = ACAPSProcessor()
zones = processor.process_control_zones('data/raw/acaps/20240327_control.zip')
print(f"Loaded {len(zones)} control zones")
```

##### extract_date_from_filename

```python
def extract_date_from_filename(
    filename: str
) -> Tuple[datetime, str]
```

Extract date from ACAPS filename format (YYYYMMDD pattern).

**Parameters:**
- `filename` (str): File name to parse

**Returns:**
- `Tuple[datetime, str]`: Tuple of (datetime object, formatted date string)

**Example:**
```python
date, formatted = processor.extract_date_from_filename(
    "20240327 Yemen Analysis Hub - Areas of control.zip"
)
# date = datetime(2024, 3, 27)
# formatted = "2024-03-27"
```

##### process_all_acaps_files

```python
def process_all_acaps_files(
    directory_path: Union[str, Path]
) -> pd.DataFrame
```

Process all ACAPS files in a directory to create temporal control dataset.

**Parameters:**
- `directory_path` (Union[str, Path]): Directory containing ACAPS files

**Returns:**
- `pd.DataFrame`: Combined control zones from all files with temporal information

**Example:**
```python
all_zones = processor.process_all_acaps_files('data/raw/acaps/')
print(f"Processed {all_zones['date'].nunique()} time periods")
```

##### create_monthly_control_zones

```python
def create_monthly_control_zones(
    all_zones_df: pd.DataFrame
) -> pd.DataFrame
```

Create monthly control zone summaries from daily snapshots.

**Parameters:**
- `all_zones_df` (pd.DataFrame): Combined zones from all dates

**Returns:**
- `pd.DataFrame`: Monthly aggregated control data

**Example:**
```python
monthly = processor.create_monthly_control_zones(all_zones)
print(f"Created {len(monthly)} monthly control records")
```

## Constants

### Control Group Mappings

```python
CONTROL_GROUP_MAPPING = {
    'Defacto Authorities (DFA)': 'DFA',
    'De-facto authorities': 'DFA',
    'DFA': 'DFA',
    'Internationally Recognized Government (IRG)': 'IRG',
    'Intl Recognized Govt': 'IRG',
    'IRG': 'IRG',
    'Southern Transitional Council (STC)': 'STC',
    'STC': 'STC',
    'Al-Qaeda in the Arabian Peninsula (AQAP)': 'AQAP',
    'AQAP': 'AQAP'
}
```

### Standard Column Names

```python
STANDARD_COLUMNS = {
    'governorate': ['governorate', 'admin1', 'gov'],
    'district': ['district', 'admin2', 'dist'],
    'controlling_group': ['controlling_group', 'control', 'authority'],
    'date': ['date', 'snapshot_date'],
    'geometry': ['geometry']
}
```

## Examples

### Basic Usage

```python
from yemen_market.data import ACAPSProcessor

# Initialize processor
processor = ACAPSProcessor()

# Process single file
zones = processor.process_control_zones('path/to/acaps_zones.zip')

# Process all files in directory
all_zones = processor.process_all_acaps_files('data/raw/acaps/')

# Create monthly summaries
monthly = processor.create_monthly_control_zones(all_zones)

# Check results
print(f"Processed {len(zones)} zones")
print(f"Control groups: {zones['controlling_group'].unique()}")
```

### Temporal Analysis

```python
# Track control changes over time
all_zones = processor.process_all_acaps_files('data/raw/acaps/')

# Find districts that changed control
changes = all_zones.groupby(['governorate', 'district'])['controlling_group'].nunique()
contested = changes[changes > 1]
print(f"Districts that changed control: {len(contested)}")

# Track DFA expansion
dfa_control = all_zones[all_zones['controlling_group'] == 'DFA']
dfa_by_date = dfa_control.groupby('date')['district'].count()
dfa_by_date.plot(title='DFA-Controlled Districts Over Time')
```

### Spatial Analysis

```python
# Get most recent control snapshot
latest_date = all_zones['date'].max()
current_control = all_zones[all_zones['date'] == latest_date]

# Calculate area under each group's control
current_control['area_km2'] = current_control.geometry.area / 1e6
control_summary = current_control.groupby('controlling_group')['area_km2'].sum()
print(control_summary)
```

## Data Processing Pipeline

1. **Extract shapefiles** from ZIP archives (handles nested ZIPs)
2. **Standardize column names** across different ACAPS formats
3. **Map control groups** to standard abbreviations
4. **Add temporal information** extracted from filenames
5. **Validate geometries** and fix invalid ones
6. **Aggregate to monthly** summaries for analysis

## Output Schema

The processed GeoDataFrame contains:

| Column | Type | Description |
|--------|------|-------------|
| governorate | str | Governorate name |
| district | str | District name |
| controlling_group | str | Standardized control group (DFA, IRG, STC, AQAP) |
| date | datetime | Date of control snapshot |
| year_month | str | Year-month for aggregation (YYYY-MM) |
| geometry | Polygon | Spatial polygon geometry |

## Error Handling

The processor includes robust error handling:

```python
try:
    zones = processor.process_control_zones('path/to/file.zip')
except FileNotFoundError:
    print("ACAPS file not found")
except ValueError as e:
    print(f"Invalid file format: {e}")
except Exception as e:
    print(f"Processing error: {e}")
```

## Performance Considerations

- Processing nested ZIPs can be memory intensive
- Use `extract_nested=False` for simple ZIP files
- Geometry validation can be slow for complex polygons
- Consider caching processed results for repeated analyses

## See Also

- [Spatial Joins](spatial_joiner.md) - Joining control zones with market data
- [Data Pipeline Guide](../../02-user-guides/data-pipeline.md) - Complete workflow
- [Conflict Analysis](../../05-methodology/validation/conflict-validation.md) - Using control data