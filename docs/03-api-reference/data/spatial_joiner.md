# SpatialJoiner API Reference

**Target Audience**: Developers, GIS Analysts  
**Module**: `yemen_market.data.spatial_joins`

## Overview

The `SpatialJoiner` class performs spatial joins between markets and control zones, identifying boundary markets and calculating distances. It handles coordinate transformations, temporal zone changes, and provides utilities for spatial analysis.

## Classes

### SpatialJoiner

```python
class SpatialJoiner:
    """Perform spatial joins between markets and control zones."""
    
    def __init__(self):
        """Initialize spatial joiner with default configuration."""
```

## Methods

### load_market_data

```python
def load_market_data(
    self,
    market_data_path: Optional[str] = None
) -> gpd.GeoDataFrame:
    """Load market location data with coordinates."""
```

Loads market data and converts to GeoDataFrame with point geometries.

#### Parameters
- **market_data_path** (`str`, optional): Path to market data file. Defaults to config path.

#### Returns
- **gpd.GeoDataFrame**: Markets with point geometries in WGS84 (EPSG:4326)

### load_control_zones

```python
def load_control_zones(
    self,
    control_zone_path: Optional[str] = None
) -> gpd.GeoDataFrame:
    """Load control zone polygons."""
```

Loads control zone boundaries as polygon geometries.

#### Parameters
- **control_zone_path** (`str`, optional): Path to control zone data. Defaults to config path.

#### Returns
- **gpd.GeoDataFrame**: Control zones with polygon geometries

### map_markets_to_zones

```python
def map_markets_to_zones(
    self,
    markets_gdf: Optional[gpd.GeoDataFrame] = None,
    zones_gdf: Optional[gpd.GeoDataFrame] = None,
    save_output: bool = True
) -> pd.DataFrame:
    """Map markets to their control zones using spatial join."""
```

Performs point-in-polygon spatial join to assign markets to control zones.

#### Parameters
- **markets_gdf** (`gpd.GeoDataFrame`, optional): Market locations. If None, loads from file.
- **zones_gdf** (`gpd.GeoDataFrame`, optional): Control zone polygons. If None, loads from file.
- **save_output** (`bool`): Save results to disk (default: True)

#### Returns
- **pd.DataFrame**: Markets with assigned control zones and spatial attributes

### identify_boundary_markets

```python
def identify_boundary_markets(
    self,
    market_zones_gdf: gpd.GeoDataFrame,
    threshold_km: float = 20.0
) -> pd.DataFrame:
    """Identify markets near control zone boundaries."""
```

Identifies markets within a specified distance of control zone boundaries.

#### Parameters
- **market_zones_gdf** (`gpd.GeoDataFrame`): Markets with zones assigned
- **threshold_km** (`float`): Distance threshold in kilometers (default: 20)

#### Returns
- **pd.DataFrame**: Markets identified as boundary markets with distance metrics

### calculate_market_distances

```python
def calculate_market_distances(
    self,
    markets_gdf: gpd.GeoDataFrame,
    zones_gdf: Optional[gpd.GeoDataFrame] = None
) -> pd.DataFrame:
    """Calculate distances between markets and to zone boundaries."""
```

Computes pairwise distances between markets and distances to zone boundaries.

#### Parameters
- **markets_gdf** (`gpd.GeoDataFrame`): Market locations
- **zones_gdf** (`gpd.GeoDataFrame`, optional): Control zones for boundary calculations

#### Returns
- **pd.DataFrame**: Distance matrix with market pairs and distances in kilometers

### create_temporal_market_zones

```python
def create_temporal_market_zones(
    self,
    markets_gdf: Optional[gpd.GeoDataFrame] = None,
    monthly_zones_path: Optional[str] = None
) -> pd.DataFrame:
    """Create time-varying market-zone mappings."""
```

Creates temporal mappings of markets to control zones as boundaries change over time.

#### Parameters
- **markets_gdf** (`gpd.GeoDataFrame`, optional): Market locations
- **monthly_zones_path** (`str`, optional): Path to monthly zone boundary data

#### Returns
- **pd.DataFrame**: Market-zone mappings with temporal dimension

## Data Structures

### Market Zone Mapping

```python
# Market identifiers
market_id: str              # Unique market identifier
market_name: str            # Market display name
governorate: str            # Governorate name
district: str               # District name

# Spatial attributes
latitude: float             # Latitude (WGS84)
longitude: float            # Longitude (WGS84)
control_zone: str           # Assigned control zone
zone_confidence: float      # Confidence in zone assignment (0-1)

# Distance metrics
distance_to_boundary: float # Distance to nearest boundary (km)
distance_to_other_zone: float # Distance to nearest different zone (km)
nearest_other_zone: str     # ID of nearest different control zone

# Classification
is_boundary_market: bool    # True if within threshold of boundary
boundary_type: str          # Type of boundary (inter-zone, international)
```

### Distance Matrix

```python
# Market pair identifiers
market_1_id: str            # First market ID
market_2_id: str            # Second market ID

# Distance metrics
distance_km: float          # Great circle distance in kilometers
travel_time_hours: float    # Estimated travel time (if available)

# Zone relationship
same_zone: bool             # Whether markets are in same control zone
zone_1: str                 # Control zone of market 1
zone_2: str                 # Control zone of market 2
```

## Examples

### Basic Spatial Join

```python
from yemen_market.data import SpatialJoiner

# Initialize joiner
joiner = SpatialJoiner()

# Load data and perform spatial join
markets_gdf = joiner.load_market_data()
zones_gdf = joiner.load_control_zones()

# Map markets to zones
market_zones = joiner.map_markets_to_zones(
    markets_gdf=markets_gdf,
    zones_gdf=zones_gdf,
    save_output=True
)

print(f"Mapped {len(market_zones)} markets to control zones")
print(f"Zone distribution:\n{market_zones['control_zone'].value_counts()}")
```

### Boundary Market Analysis

```python
# Identify boundary markets within 20km of zone boundaries
boundary_markets = joiner.identify_boundary_markets(
    market_zones,
    threshold_km=20
)

print(f"Found {len(boundary_markets)} boundary markets")
print(f"Average distance to boundary: {boundary_markets['distance_to_boundary'].mean():.1f} km")

# Analyze boundary markets by zone
for zone in boundary_markets['control_zone'].unique():
    zone_markets = boundary_markets[boundary_markets['control_zone'] == zone]
    print(f"{zone}: {len(zone_markets)} boundary markets")
```

### Distance Calculations

```python
# Calculate all pairwise market distances
distances = joiner.calculate_market_distances(
    markets_gdf,
    zones_gdf=zones_gdf
)

# Filter for nearby markets
nearby_pairs = distances[distances['distance_km'] <= 50]
print(f"Found {len(nearby_pairs)} market pairs within 50km")

# Analyze cross-zone connections
cross_zone = nearby_pairs[~nearby_pairs['same_zone']]
print(f"Cross-zone connections: {len(cross_zone)}")
```

### Temporal Zone Mapping

```python
# Create temporal mappings for changing control zones
temporal_zones = joiner.create_temporal_market_zones()

# Analyze zone changes over time
zone_changes = temporal_zones.groupby('market_id')['control_zone'].nunique()
markets_with_changes = zone_changes[zone_changes > 1]
print(f"Markets that changed zones: {len(markets_with_changes)}")

# Get timeline for a specific market
market_timeline = temporal_zones[
    temporal_zones['market_id'] == 'YE_ADEN_CITY'
].sort_values('date')
print(f"Zone changes for Aden:\n{market_timeline[['date', 'control_zone']]}")
```

## Coordinate Reference Systems

- **Input CRS**: WGS84 (EPSG:4326) - Standard lat/lon coordinates
- **Calculation CRS**: Yemen TM (EPSG:2090) - For accurate distance calculations
- **Output CRS**: WGS84 (EPSG:4326) - Maintains compatibility

The class handles automatic reprojection between coordinate systems as needed.

## Performance Considerations

- **Large Datasets**: Spatial joins can be memory-intensive. The class uses spatial indexing for efficiency.
- **Distance Calculations**: Pairwise distance matrix grows as O(n²). Consider filtering by maximum distance.
- **Temporal Data**: Monthly zone data can be large. Process in chunks if needed.

## Quality Assurance

The class includes automatic validation:
- Checks for invalid coordinates
- Identifies markets outside any control zone
- Validates polygon geometries
- Logs assignment statistics and warnings

## See Also

- [`data/panel_builder.md`](panel_builder.md) - Integration with panel construction
- [User Guide: Spatial Analysis](../../02-user-guides/spatial-analysis.md)
- [Methodology: Spatial Matching](../../05-methodology/data-processing/spatial-matching.md)