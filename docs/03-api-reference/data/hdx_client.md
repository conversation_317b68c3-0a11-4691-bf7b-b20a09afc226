# HDXClient API Reference

**Target Audience**: <PERSON><PERSON><PERSON>, Data Engineers  
**Module**: `yemen_market.data.hdx_client`

## Overview

The `HDXClient` class provides a Python interface for downloading humanitarian datasets from the Humanitarian Data Exchange (HDX) platform, with specific support for Yemen-related datasets.

## Classes

### HDXClient

```python
class HDXClient(
    download_dir: Optional[Path] = None
)
```

Client for accessing HDX (Humanitarian Data Exchange) datasets.

#### Parameters

- `download_dir` (Path, optional): Directory to save downloaded files. Defaults to `RAW_DATA_DIR` from config.

#### Methods

##### download_dataset

```python
def download_dataset(
    dataset_id: str,
    force_download: bool = False
) -> List[Path]
```

Download a complete dataset from HDX.

**Parameters:**
- `dataset_id` (str): HDX dataset identifier (e.g., "wfp-food-prices-for-yemen")
- `force_download` (bool): Force re-download even if files exist locally

**Returns:**
- `List[Path]`: List of downloaded file paths

**Example:**
```python
client = HDXClient()
files = client.download_dataset("wfp-food-prices-for-yemen")
for file in files:
    print(f"Downloaded: {file.name}")
```

##### download_resource

```python
def download_resource(
    dataset_id: str,
    resource_name: str,
    force_download: bool = False
) -> Path
```

Download a specific resource from an HDX dataset.

**Parameters:**
- `dataset_id` (str): HDX dataset identifier
- `resource_name` (str): Name pattern to match resource (supports wildcards)
- `force_download` (bool): Force re-download

**Returns:**
- `Path`: Path to downloaded file

**Example:**
```python
# Download specific CSV file
file_path = client.download_resource(
    "wfp-food-prices-for-yemen",
    "wfp_food_prices_*.csv"
)
```

##### get_wfp_food_prices

```python
def get_wfp_food_prices(
    force_download: bool = False
) -> pd.DataFrame
```

Get WFP food price data for Yemen.

**Parameters:**
- `force_download` (bool): Force re-download of data

**Returns:**
- `pd.DataFrame`: WFP price data with standardized columns

**Example:**
```python
df = client.get_wfp_food_prices()
print(f"Loaded {len(df)} price records")
print(f"Date range: {df['date'].min()} to {df['date'].max()}")
```

##### get_acaps_control_data

```python
def get_acaps_control_data(
    date_str: Optional[str] = None,
    force_download: bool = False
) -> gpd.GeoDataFrame
```

Get ACAPS areas of control data.

**Parameters:**
- `date_str` (str, optional): Specific date version (format: 'YYYYMMDD')
- `force_download` (bool): Force re-download

**Returns:**
- `gpd.GeoDataFrame`: Control zone geometries with attributes

**Example:**
```python
# Get latest control data
control_zones = client.get_acaps_control_data()

# Get specific date
control_zones = client.get_acaps_control_data(date_str='20241024')
```

##### search_datasets

```python
def search_datasets(
    query: str,
    filters: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]
```

Search for datasets on HDX.

**Parameters:**
- `query` (str): Search query
- `filters` (Dict[str, Any], optional): Additional filters

**Returns:**
- `List[Dict[str, Any]]`: List of matching datasets

**Example:**
```python
# Search for Yemen-related datasets
results = client.search_datasets("yemen", filters={"tags": "prices"})
for dataset in results:
    print(f"{dataset['name']}: {dataset['title']}")
```

## Constants

### Common Dataset IDs

```python
# Key HDX dataset identifiers for Yemen
WFP_DATASET = "wfp-food-prices-for-yemen"
ACAPS_DATASET = "yemen-areas-of-control"
ADMIN_BOUNDARIES = "cod-ab-yem"
ACLED_DATASET = "acled-data-for-yemen"
IOM_DTM_DATASET = "yemen-displacement-tracking-matrix"
```

### Resource Patterns

```python
# Common resource name patterns
WFP_CSV_PATTERN = "wfp_food_prices_*.csv"
ACAPS_ZIP_PATTERN = "*Yemen Analysis Hub*.zip"
BOUNDARIES_GPKG = "yem_adm_*_GPKG.zip"
```

## Examples

### Basic Usage

```python
from yemen_market.data import HDXClient

# Initialize client
client = HDXClient()

# Download WFP price data
wfp_df = client.get_wfp_food_prices()
print(f"Downloaded {len(wfp_df)} price records")

# Download specific ACAPS control data
control_zones = client.get_acaps_control_data(date_str='20241024')

# Download administrative boundaries
boundaries = client.download_dataset('cod-ab-yem')
```

### Custom Download Directory

```python
from pathlib import Path

# Use custom download directory
custom_dir = Path("./my_data/hdx")
client = HDXClient(download_dir=custom_dir)

# Downloads will be saved to custom directory
files = client.download_dataset("wfp-food-prices-for-yemen")
```

### Handling Updates

```python
# Check for updates and download if newer
client = HDXClient()

# Force refresh of cached data
fresh_data = client.get_wfp_food_prices(force_download=True)

# Compare with existing
if Path("processed/wfp_latest.parquet").exists():
    old_data = pd.read_parquet("processed/wfp_latest.parquet")
    new_records = len(fresh_data) - len(old_data)
    print(f"Found {new_records} new records")
```

## Error Handling

The client includes comprehensive error handling:

```python
from hdx.api.configuration import ConfigurationError

try:
    client = HDXClient()
    data = client.get_wfp_food_prices()
except ConfigurationError:
    print("HDX not configured. Check HDX_SITE environment variable.")
except ConnectionError:
    print("Cannot connect to HDX. Check internet connection.")
except FileNotFoundError as e:
    print(f"Resource not found: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## Caching Strategy

- Downloads are cached in `download_dir` (default: `data/raw/hdx/`)
- Cache key: `{dataset_id}/{resource_name}`
- Metadata stored with downloads for versioning
- Use `force_download=True` to bypass cache
- Automatic cleanup of partial downloads

## Performance Considerations

- Large datasets are downloaded with progress bars
- Implements retry logic (3 attempts with exponential backoff)
- Validates checksums when available
- Streams large files to avoid memory issues
- Parallel downloads for multiple resources

## Configuration

HDX client can be configured via environment variables:

```bash
# Set HDX site (prod or test)
export HDX_SITE=prod

# Set API key for private datasets (if needed)
export HDX_API_KEY=your-api-key
```

## See Also

- [Data Pipeline Guide](../../02-user-guides/data-pipeline.md) - Using downloaded data
- [WFP Processor](wfp_processor.md) - Processing WFP price data
- [ACAPS Processor](acaps_processor.md) - Processing control zone data