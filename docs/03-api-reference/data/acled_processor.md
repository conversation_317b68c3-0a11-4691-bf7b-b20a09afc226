# ACLEDProcessor API Reference

**Target Audience**: <PERSON><PERSON><PERSON>, Conflict Researchers  
**Module**: `yemen_market.data.acled_processor`

## Overview

The `ACLEDProcessor` class processes Armed Conflict Location & Event Data (ACLED) for Yemen, creating market-level conflict metrics through spatial matching and temporal aggregation.

## Classes

### ACLEDProcessor

```python
class ACLEDProcessor(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    buffer_km: float = 50
)
```

Process ACLED conflict event data for Yemen market analysis.

#### Parameters

- `start_date` (str, optional): Start date for filtering events (format: 'YYYY-MM-DD'). Defaults to config value.
- `end_date` (str, optional): End date for filtering. Defaults to config value.
- `buffer_km` (float): Buffer radius in kilometers for spatial matching (default: 50).

#### Methods

##### load_acled_data

```python
def load_acled_data(
    file_path: Optional[Union[str, Path]] = None
) -> gpd.GeoDataFrame
```

Load and preprocess ACLED event data.

**Parameters:**
- `file_path` (Union[str, Path], optional): Path to ACLED CSV file. If None, searches in configured location.

**Returns:**
- `gpd.GeoDataFrame`: Preprocessed ACLED events with geometries

**Example:**
```python
processor = ACLEDProcessor()
events_gdf = processor.load_acled_data()
print(f"Loaded {len(events_gdf)} conflict events")
```

##### calculate_conflict_metrics

```python
def calculate_conflict_metrics(
    events_gdf: gpd.GeoDataFrame,
    markets_gdf: gpd.GeoDataFrame,
    spatial_method: str = 'buffer'
) -> pd.DataFrame
```

Calculate comprehensive conflict metrics at market-month level.

**Parameters:**
- `events_gdf` (gpd.GeoDataFrame): ACLED events with geometries
- `markets_gdf` (gpd.GeoDataFrame): Market locations with geometries
- `spatial_method` (str): Method for spatial matching ('buffer' or 'nearest')

**Returns:**
- `pd.DataFrame`: Conflict metrics by market and month

**Example:**
```python
metrics = processor.calculate_conflict_metrics(
    events_gdf,
    markets_gdf,
    spatial_method='buffer'
)
```

##### process_acled_data

```python
def process_acled_data(
    wfp_markets_path: Optional[Union[str, Path]] = None,
    save_output: bool = True
) -> pd.DataFrame
```

Main processing pipeline for ACLED data.

**Parameters:**
- `wfp_markets_path` (Union[str, Path], optional): Path to WFP markets data
- `save_output` (bool): Whether to save processed data to disk (default: True)

**Returns:**
- `pd.DataFrame`: Complete conflict metrics dataset

## Conflict Metrics

The processor generates the following metrics for each market-month:

### Event Counts
```python
conflict_events: int       # Total conflict events
conflict_fatalities: int   # Total fatalities
n_battles: int            # Battle events
n_explosions: int         # Explosion/remote violence events
n_protests: int           # Protest events
n_riots: int             # Riot events
n_strategic: int         # Strategic development events
n_violence_civilians: int # Violence against civilians
```

### Actor-Specific Counts
```python
n_events_houthis: int     # Houthi-involved events
n_events_government: int  # Government-involved events
n_events_stc: int        # STC-involved events
n_events_aqap: int       # AQAP-involved events
n_events_isis: int       # ISIS-involved events
n_events_other: int      # Other actors
```

### Derived Metrics
```python
conflict_intensity: float  # log(events + 1)
fatality_rate: float      # fatalities per event
```

## Constants

### Event Type Mapping

```python
EVENT_TYPE_MAPPING = {
    'Battles': 'battles',
    'Explosions/Remote violence': 'explosions',
    'Protests': 'protests',
    'Riots': 'riots',
    'Strategic developments': 'strategic',
    'Violence against civilians': 'violence_civilians'
}
```

### Actor Classifications

```python
HOUTHI_KEYWORDS = ['houthi', 'ansar allah', 'ansarallah']
GOVERNMENT_KEYWORDS = ['government', 'hadi', 'presidential', 'saudi coalition']
STC_KEYWORDS = ['stc', 'southern transitional', 'southern resistance']
AQAP_KEYWORDS = ['aqap', 'al qaeda', 'al-qaeda', 'ansar al-sharia']
ISIS_KEYWORDS = ['isis', 'islamic state', 'is-yemen']
```

## Examples

### Basic Usage

```python
from yemen_market.data import ACLEDProcessor

# Initialize processor
processor = ACLEDProcessor(
    start_date='2019-01-01',
    buffer_km=50  # 50km buffer around markets
)

# Process ACLED data
conflict_metrics = processor.process_acled_data()

# Check results
print(f"Processed {len(conflict_metrics)} market-month observations")
print(f"Conflict coverage: {conflict_metrics['conflict_events'].notna().mean():.1%}")

# Get high-conflict markets
high_conflict = conflict_metrics[
    conflict_metrics['conflict_intensity'] > 
    conflict_metrics['conflict_intensity'].quantile(0.9)
]
print(f"High conflict observations: {len(high_conflict)}")
```

### Custom Spatial Matching

```python
# Load data separately for custom processing
events = processor.load_acled_data()
markets = gpd.read_file('path/to/markets.geojson')

# Use nearest neighbor matching instead of buffer
metrics_nearest = processor.calculate_conflict_metrics(
    events,
    markets,
    spatial_method='nearest'
)
```

### Analyzing Conflict Patterns

```python
# Get conflict metrics
metrics = processor.process_acled_data()

# Analyze by actor
houthi_active = metrics[metrics['n_events_houthis'] > 0]
govt_active = metrics[metrics['n_events_government'] > 0]

# Temporal patterns
monthly_events = metrics.groupby('date')['conflict_events'].sum()
monthly_events.plot(title='Conflict Events Over Time')

# Spatial patterns
market_intensity = metrics.groupby('market_id')['conflict_intensity'].mean()
high_conflict_markets = market_intensity.nlargest(10)
```

## Spatial Matching Methods

### Buffer Method (Default)
- Creates circular buffers of `buffer_km` radius around each market
- Counts all events falling within buffer
- More accurate for capturing area effects
- Computationally intensive for large datasets

### Nearest Method
- Assigns each event to the nearest market
- Filters by maximum distance threshold
- Faster processing
- May miss area-wide effects

## Output Files

The processor generates:
- `conflict_metrics.parquet`: Main conflict metrics dataset
- `acled_events_processed.parquet`: Cleaned event data
- `conflict_processing_summary.json`: Processing statistics

## Error Handling

```python
try:
    metrics = processor.process_acled_data()
except FileNotFoundError:
    print("ACLED data not found. Download from ACLED website.")
except ValueError as e:
    print(f"Data validation error: {e}")
```

## Performance Considerations

- Buffer matching is O(n*m) for n events and m markets
- Use smaller buffer radius for faster processing
- Consider chunking for very large datasets
- Spatial indexing improves performance significantly

## See Also

- [Spatial Joins](spatial_joiner.md) - Spatial matching utilities
- [Data Pipeline Guide](../../02-user-guides/data-pipeline.md) - Complete workflow
- [Conflict Validation](../../05-methodology/validation/conflict-validation.md) - Validation methods