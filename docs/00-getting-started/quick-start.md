# Quick Start Tutorial

Get up and running with Yemen Market Integration analysis in 5 minutes!

## 🎯 What You'll Accomplish

In this tutorial, you'll:
1. Process market price data
2. Run a basic integration analysis  
3. View preliminary results
4. Generate a simple report

## 📋 Prerequisites

- Completed [Installation Guide](./installation.md)
- Virtual environment activated
- Initial data downloaded

## 🚀 5-Minute Quickstart

### Step 1: Process the Data (1 minute)

```bash
# Process WFP price data
python scripts/data_processing/process_wfp_data.py

# Process territorial control data
python scripts/data_processing/process_acaps_data.py

# Process conflict data
python scripts/data_processing/process_acled_data.py

# Create spatial relationships
python scripts/data_processing/run_spatial_joins.py
```

### Step 2: Prepare for Analysis (30 seconds)

```bash
# Create analysis-ready dataset
python scripts/analysis/prepare_data_for_modeling.py
```

### Step 3: Run Basic Analysis (2 minutes)

```python
# Create a file: quick_analysis.py
from yemen_market.data import PanelBuilder
from yemen_market.models.three_tier.integration import ThreeTierRunner

# Load data
builder = PanelBuilder()
panel_data = builder.load_modeling_panel()

# Run simplified analysis
runner = ThreeTierRunner(config={'quick_mode': True})
results = runner.run_analysis(panel_data)

# Print summary
print("=== Quick Analysis Results ===")
print(f"Markets analyzed: {results.n_markets}")
print(f"Commodities: {results.n_commodities}")
print(f"Time period: {results.start_date} to {results.end_date}")
print(f"\nKey finding: {results.summary}")
```

Run it:
```bash
python quick_analysis.py
```

### Step 4: Visualize Results (1 minute)

```python
# Create a file: quick_viz.py
import matplotlib.pyplot as plt
from yemen_market.visualization import plot_price_trends

# Load results from previous step
results = load_results('results/quick_analysis/')

# Create basic visualization
fig = plot_price_trends(
    results.price_data,
    commodities=['Wheat', 'Rice', 'Sugar'],
    title="Price Trends in Key Markets"
)
plt.show()
```

### Step 5: Generate Report (30 seconds)

```bash
# Generate a basic HTML report
python scripts/generate_report.py --quick --output reports/quick_report.html
```

## 📊 Understanding Your Results

Your quick analysis provides:

1. **Market Coverage**: Number of markets and their connectivity
2. **Price Integration**: How closely prices move together
3. **Conflict Impact**: Basic assessment of conflict on prices
4. **Key Commodities**: Which goods show strongest/weakest integration

## 🎉 Congratulations!

You've successfully:
- ✅ Processed real market data
- ✅ Run econometric analysis
- ✅ Generated visualizations
- ✅ Created a basic report

## 🔍 What's Next?

### Dive Deeper
- [First Analysis Walkthrough](./first-analysis.md) - Comprehensive guide
- [Data Pipeline Guide](../02-user-guides/data-pipeline.md) - Understand the data
- [Model Documentation](../03-api-reference/models/) - Technical details

### Run Full Analysis
```bash
# Run complete three-tier analysis
python scripts/analysis/run_three_tier_models_updated.py

# Or use the enhanced pipeline
python scripts/analysis/enhanced_analysis_pipeline.py
```

### Explore Notebooks
Check out example analyses:
- `notebooks/01-data-validation.ipynb` - Data quality checks
- `notebooks/02-price-patterns.ipynb` - Price dynamics
- `notebooks/04_models/01_three_tier_implementation.ipynb` - Full model walkthrough

## 💡 Quick Tips

1. **Speed vs Accuracy**: Quick mode trades some accuracy for speed
2. **Data Currency**: Update data regularly with `download_data.py`
3. **Memory Usage**: For large analyses, use `--chunk-size` parameter
4. **Visualization**: Export to various formats with `--format png|pdf|svg`

## 🐛 Troubleshooting

### No Data Found
```bash
# Ensure data is downloaded
ls data/processed/  # Should show .parquet files
```

### Memory Error
```python
# Use subset of data
panel_data = builder.load_modeling_panel(
    start_date='2023-01-01',
    commodities=['Wheat', 'Rice']
)
```

### Import Error
```bash
# Ensure virtual environment is activated
which python  # Should show .../venv/bin/python
```

---

*Ready for more? Continue to the [First Analysis Walkthrough](./first-analysis.md) for an in-depth guide!*