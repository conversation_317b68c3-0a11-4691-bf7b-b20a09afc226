# Getting Started with Yemen Market Integration Platform

Welcome! This guide will help you get up and running with the Yemen Market Integration Platform in under 30 minutes.

## Who Should Read This

- **Researchers** wanting to analyze Yemen market data
- **Developers** setting up the development environment
- **Policy Analysts** needing to generate reports
- **Data Scientists** exploring the econometric models

## What You'll Learn

1. How to install the platform
2. How to run your first analysis
3. How to interpret the results
4. Where to go next

## Prerequisites

Before starting, ensure you have:

- Python 3.9 or higher installed
- Git for version control
- 8GB RAM (16GB recommended)
- Basic command line knowledge
- API keys for data sources (we'll help you get these)

## In This Section

### 📦 [Installation Guide](./installation.md)
Complete setup instructions including:
- System requirements
- Python environment setup
- Dependency installation
- Configuration
- Troubleshooting

### 🚀 [Quick Start](./quick-start.md)
Get running in 10 minutes:
- Download sample data
- Run a basic analysis
- View results
- Generate your first report

### 📊 [First Analysis Tutorial](./first-analysis.md)
Step-by-step walkthrough:
- Understanding the data pipeline
- Selecting analysis parameters
- Running the three-tier model
- Interpreting outputs
- Creating visualizations

## Quick Installation

For the impatient, here's the fastest way to get started:

```bash
# Clone and enter the repository
git clone https://github.com/your-org/yemen-market-integration.git
cd yemen-market-integration

# Set up Python environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your API keys

# Run sample analysis
make week5-models
```

## Getting Help

- **Installation Issues**: See [installation troubleshooting](./installation.md#troubleshooting)
- **Conceptual Questions**: Read the [methodology](../05-methodology/)
- **Technical Support**: Open an [issue on GitHub](https://github.com/your-org/yemen-market-integration/issues)

## Next Steps

After completing this guide:

1. **For Researchers**: Continue to [User Guides](../02-user-guides/) to learn advanced features
2. **For Developers**: Explore the [Architecture](../01-architecture/) and [API Reference](../03-api-reference/)
3. **For Policy Makers**: See [Interpreting Results](../02-user-guides/interpreting-results.md) and [Policy Briefs](../02-user-guides/policy-briefs.md)

---

*Ready to begin? Start with the [Installation Guide](./installation.md) →*