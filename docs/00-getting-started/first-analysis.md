# First Analysis Walkthrough

A comprehensive guide to conducting your first market integration analysis with detailed explanations.

## 🎯 Learning Objectives

By the end of this walkthrough, you'll understand:
- How market integration analysis works
- The three-tier econometric framework
- How to interpret results
- How to customize analyses for specific needs

## 📚 Background

### What is Market Integration?

Market integration measures how closely prices in different markets move together. In conflict settings like Yemen, understanding market integration helps:
- Identify isolated markets needing intervention
- Assess food security risks
- Design targeted aid programs
- Monitor economic fragmentation

### The Three-Tier Framework

1. **Tier 1 - Pooled Analysis**: Overall market dynamics
2. **Tier 2 - Commodity-Specific**: Individual commodity patterns  
3. **Tier 3 - Validation**: Conflict and policy impacts

## 🔄 Complete Analysis Workflow

### Step 1: Understanding the Data

```python
import pandas as pd
import numpy as np
from yemen_market.data import PanelBuilder
from yemen_market.utils.logging import setup_logging

# Initialize logging
logger = setup_logging()

# Load and explore data
builder = PanelBuilder()
logger.info("Loading integrated panel data...")

# Load the prepared panel
panel_data = builder.load_integrated_panel()

# Explore the data structure
print(f"Panel shape: {panel_data.shape}")
print(f"Time period: {panel_data['date'].min()} to {panel_data['date'].max()}")
print(f"Markets: {panel_data['market'].nunique()}")
print(f"Commodities: {panel_data['commodity'].nunique()}")

# Check data quality
print("\nMissing data by column:")
print(panel_data.isnull().sum())

# View sample data
print("\nSample data:")
print(panel_data.head())
```

### Step 2: Data Validation and Cleaning

```python
from yemen_market.features.data_preparation import DataPreparation

# Initialize data preparation
prep = DataPreparation()

# Validate and clean data
logger.info("Validating data quality...")
clean_data, validation_report = prep.prepare_modeling_data(
    panel_data,
    validate=True,
    handle_outliers=True
)

# Review validation report
print("Data Validation Report:")
print(f"- Original observations: {validation_report['original_obs']}")
print(f"- After cleaning: {validation_report['final_obs']}")
print(f"- Outliers removed: {validation_report['outliers_removed']}")
print(f"- Markets with sufficient data: {validation_report['valid_markets']}")
```

### Step 3: Feature Engineering

```python
from yemen_market.features.feature_engineering import FeatureEngineer

# Create features for analysis
engineer = FeatureEngineer()
logger.info("Engineering features...")

# Add spatial features
data_with_features = engineer.add_spatial_features(
    clean_data,
    n_neighbors=5  # Consider 5 nearest markets
)

# Add temporal features
data_with_features = engineer.add_temporal_features(data_with_features)

# Add interaction terms
data_with_features = engineer.add_interaction_terms(
    data_with_features,
    interactions=['zone_time', 'conflict_commodity']
)

print(f"Features added: {len(data_with_features.columns) - len(clean_data.columns)}")
```

### Step 4: Running the Three-Tier Analysis

```python
from yemen_market.models.three_tier.integration import ThreeTierRunner

# Configure analysis
config = {
    'tier1': {
        'model_type': 'fixed_effects',
        'fe_type': 'twoway',  # Market and time fixed effects
        'cluster_var': 'market',
        'include_trends': True
    },
    'tier2': {
        'commodities': ['Wheat', 'Rice', 'Sugar', 'Fuel (Petrol-Gasoline)'],
        'test_cointegration': True,
        'threshold_vecm': True  # Test for regime changes
    },
    'tier3': {
        'n_factors': 3,
        'include_conflict': True,
        'validation_tests': ['granger', 'variance_decomposition']
    },
    'diagnostics': {
        'run_all': True,
        'save_results': True
    },
    'output_dir': 'results/first_analysis'
}

# Initialize and run analysis
runner = ThreeTierRunner(config)
logger.info("Starting three-tier analysis...")

results = runner.run_analysis(data_with_features)
```

### Step 5: Understanding Tier 1 Results

```python
# Extract Tier 1 results
tier1_results = results.tier1_results

print("=== Tier 1: Pooled Panel Results ===")
print(f"Model: {tier1_results.model_type}")
print(f"Observations: {tier1_results.nobs}")
print(f"R-squared: {tier1_results.rsquared:.3f}")

# Key coefficients
print("\nKey Coefficients:")
for var in ['conflict_intensity', 'distance_to_border', 'exchange_rate_premium']:
    if var in tier1_results.params:
        coef = tier1_results.params[var]
        pval = tier1_results.pvalues[var]
        print(f"{var}: {coef:.4f} (p={pval:.3f})")

# Fixed effects
print(f"\nNumber of market fixed effects: {len(tier1_results.market_effects)}")
print(f"Number of time fixed effects: {len(tier1_results.time_effects)}")
```

### Step 6: Understanding Tier 2 Results

```python
# Examine commodity-specific results
print("\n=== Tier 2: Commodity-Specific Results ===")

for commodity, comm_results in results.tier2_results.items():
    print(f"\n{commodity}:")
    print(f"  - Markets analyzed: {comm_results['n_markets']}")
    print(f"  - Cointegrated pairs: {comm_results['n_cointegrated']}/{comm_results['n_pairs']}")
    
    if comm_results['threshold_vecm']:
        print(f"  - Threshold detected: {comm_results['threshold_value']:.2f}")
        print(f"  - High regime obs: {comm_results['high_regime_pct']:.1%}")
        print(f"  - Regime difference significant: {comm_results['regime_diff_pval'] < 0.05}")
```

### Step 7: Understanding Tier 3 Results

```python
# Validation and factor analysis results
tier3_results = results.tier3_results

print("\n=== Tier 3: Validation Results ===")

# Factor analysis
print(f"Factors extracted: {tier3_results.n_factors}")
print(f"Variance explained: {tier3_results.variance_explained:.1%}")

print("\nFactor loadings (top variables):")
for i, factor in enumerate(tier3_results.factors[:3]):
    print(f"\nFactor {i+1}:")
    top_vars = factor.get_top_variables(n=5)
    for var, loading in top_vars:
        print(f"  - {var}: {loading:.3f}")

# Granger causality results
if 'granger_results' in tier3_results:
    print("\nGranger Causality Tests:")
    for test in tier3_results.granger_results:
        if test['pvalue'] < 0.05:
            print(f"  - {test['cause']} → {test['effect']} (p={test['pvalue']:.3f})")
```

### Step 8: Generating Visualizations

```python
from yemen_market.visualization import (
    plot_market_network,
    plot_price_convergence,
    plot_impulse_response
)

# Market network visualization
fig_network = plot_market_network(
    results.market_pairs,
    integration_threshold=0.7,
    title="Market Integration Network"
)
fig_network.savefig('results/first_analysis/market_network.png', dpi=300)

# Price convergence over time
fig_convergence = plot_price_convergence(
    results.tier2_results['Wheat'],
    market_pairs=[('Sana\'a', 'Aden'), ('Sana\'a', 'Taiz')],
    title="Wheat Price Convergence"
)
fig_convergence.savefig('results/first_analysis/wheat_convergence.png', dpi=300)

# Impulse response functions
if 'irf_results' in results.tier3_results:
    fig_irf = plot_impulse_response(
        results.tier3_results.irf_results,
        shock_var='conflict_events',
        response_var='price_index',
        title="Conflict Shock Impact on Prices"
    )
    fig_irf.savefig('results/first_analysis/conflict_irf.png', dpi=300)
```

### Step 9: Creating a Summary Report

```python
# Generate comprehensive report
from yemen_market.reporting import ReportGenerator

generator = ReportGenerator()
report = generator.create_analysis_report(
    results,
    include_sections=[
        'executive_summary',
        'methodology',
        'key_findings',
        'commodity_analysis',
        'policy_implications',
        'technical_appendix'
    ],
    output_format='html'
)

# Save report
report.save('results/first_analysis/analysis_report.html')
print(f"Report saved to: results/first_analysis/analysis_report.html")
```

### Step 10: Interpreting Results

```python
# Create interpretation summary
interpretation = results.create_interpretation()

print("\n=== Key Findings ===")
print(interpretation.summary)

print("\n=== Policy Implications ===")
for implication in interpretation.policy_implications:
    print(f"- {implication}")

print("\n=== Technical Notes ===")
for note in interpretation.technical_notes:
    print(f"- {note}")
```

## 📊 Understanding Your Results

### Key Metrics to Focus On

1. **Integration Coefficient**: Closer to 1 = stronger integration
2. **Half-life**: Time for price shocks to dissipate (lower = better integrated)
3. **Threshold Effects**: Different integration in high vs low conflict periods
4. **Spatial Spillovers**: How shocks spread geographically

### Common Patterns

- **Fuel products**: Usually most integrated (standardized product)
- **Perishables**: Least integrated (transport constraints)
- **Conflict zones**: Weaker integration, higher price volatility
- **Border markets**: May show international price influence

## 🔧 Customizing Your Analysis

### Focus on Specific Regions

```python
# Analyze only northern governorates
northern_data = data_with_features[
    data_with_features['governorate'].isin(['Sana\'a', 'Sa\'ada', 'Hajjah'])
]
```

### Adjust Time Periods

```python
# Focus on recent period
recent_data = data_with_features[
    data_with_features['date'] >= '2023-01-01'
]
```

### Change Model Specifications

```python
# Use different standard errors
config['tier1']['se_type'] = 'HC3'  # Heteroskedasticity-robust

# Add control variables
config['tier1']['additional_controls'] = ['rainfall', 'temperature']
```

## 🎓 Next Steps

1. **Explore Notebooks**: See `notebooks/04_models/` for advanced examples
2. **Read Methodology**: Understand the [econometric framework](../05-methodology/econometric-framework.md)
3. **API Documentation**: Learn to build [custom models](../03-api-reference/models/)
4. **Real Analysis**: Run the full pipeline on complete data

## 💡 Pro Tips

1. **Start Small**: Test with one commodity before running all
2. **Check Diagnostics**: Always review diagnostic test results
3. **Validate Findings**: Cross-reference with field reports
4. **Document Assumptions**: Keep notes on parameter choices
5. **Version Control**: Save configuration files with results

---

*Congratulations on completing your first analysis! For more advanced topics, see the [User Guides](../02-user-guides/).*