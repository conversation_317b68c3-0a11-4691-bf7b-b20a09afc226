# Installation Guide

This guide walks you through setting up the Yemen Market Integration Platform on your local machine.

## 📋 System Requirements

### Minimum Requirements
- **Operating System**: macOS, Linux, or Windows 10+
- **Python**: 3.10 or higher (3.11 recommended)
- **RAM**: 8GB minimum (16GB recommended for large analyses)
- **Storage**: 10GB free space
- **Internet**: Required for data downloads

### Software Prerequisites
- Git
- Python 3.10+
- pip (Python package manager)
- Virtual environment support

## 🔧 Installation Steps

### 1. Clone the Repository

```bash
# Clone the repository
git clone https://github.com/worldbank/yemen-market-integration.git
cd yemen-market-integration
```

### 2. Set Up Python Environment

#### Option A: Using the Setup Script (Recommended)

```bash
# Run the automated setup script
./setup_venv.sh
```

This script will:
- Create a virtual environment named `venv`
- Activate the environment
- Install all required dependencies

#### Option B: Manual Setup

```bash
# Create virtual environment
python3.10 -m venv venv  # Note: Use venv, not .venv

# Activate the virtual environment
# On macOS/Linux:
source venv/bin/activate

# On Windows:
# venv\\Scripts\\activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt
```

### 3. Verify Installation

```bash
# Check Python version
python --version  # Should show 3.10 or higher

# Verify key packages
python -c "import pandas; print(f'Pandas: {pandas.__version__}')"
python -c "import numpy; print(f'NumPy: {numpy.__version__}')"
python -c "import statsmodels; print(f'Statsmodels: {statsmodels.__version__}')"

# Run tests to ensure everything works
pytest tests/unit/test_logging.py -v
```

### 4. Download Initial Data

```bash
# Download required datasets
python scripts/data_collection/download_data.py
python scripts/data_collection/download_acled_data.py
```

## 🐳 Docker Installation (Alternative)

For containerized deployment:

```bash
# Build Docker image
docker build -t yemen-market-integration .

# Run container
docker run -it -v $(pwd)/data:/app/data yemen-market-integration
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Python Version Errors
**Problem**: `python3.10: command not found`

**Solution**:
```bash
# Install Python 3.10+ using your package manager
# macOS (using Homebrew):
brew install python@3.11

# Ubuntu/Debian:
sudo apt update
sudo apt install python3.11 python3.11-venv

# Windows: Download from python.org
```

#### 2. Package Installation Failures
**Problem**: `error: Microsoft Visual C++ 14.0 is required` (Windows)

**Solution**:
- Install Visual Studio Build Tools
- Or use pre-compiled wheels: `pip install --only-binary :all: package_name`

#### 3. Memory Errors During Installation
**Problem**: `MemoryError` when installing large packages

**Solution**:
```bash
# Install packages individually
pip install numpy
pip install pandas
pip install -r requirements.txt --no-cache-dir
```

#### 4. Permission Errors
**Problem**: `Permission denied` when installing packages

**Solution**:
- Never use `sudo pip install`
- Ensure virtual environment is activated
- Check file permissions: `chmod +x setup_venv.sh`

### Platform-Specific Notes

#### macOS
- Ensure Xcode Command Line Tools are installed: `xcode-select --install`
- For M1/M2 Macs, some packages may need Rosetta 2

#### Linux
- Install development headers: `sudo apt-get install python3.10-dev`
- May need additional system packages for geospatial operations

#### Windows
- Use PowerShell or WSL2 for better compatibility
- Path separators: Use forward slashes in Python code

## ✅ Post-Installation Checklist

- [ ] Virtual environment created and activated
- [ ] All dependencies installed without errors
- [ ] Basic import test passes
- [ ] Data download scripts run successfully
- [ ] Unit tests pass

## 🚀 Next Steps

Now that installation is complete:

1. Continue to [Quick Start Tutorial](./quick-start.md) for a 5-minute introduction
2. Or dive into [First Analysis Walkthrough](./first-analysis.md) for a comprehensive guide
3. Review [Data Pipeline Guide](../02-user-guides/data-pipeline.md) to understand data flow

## 🤝 Getting Help

If you encounter issues:

1. Check the [FAQ](../02-user-guides/faq.md)
2. Review closed [GitHub Issues](https://github.com/worldbank/yemen-market-integration/issues?q=is%3Aissue+is%3Aclosed)
3. Open a new issue with:
   - Your operating system and version
   - Python version (`python --version`)
   - Complete error message
   - Steps to reproduce

---

*Installation complete? Continue to the [Quick Start Tutorial](./quick-start.md)!*