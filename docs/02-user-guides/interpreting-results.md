# Interpreting Results Guide

## 📊 Overview

This guide helps you understand and interpret the outputs from Yemen Market Integration analyses. Learn what the numbers mean, how to assess statistical significance, and how to derive policy insights.

## 🎯 Quick Interpretation Checklist

Before diving into details, check these key points:

- ✅ **Statistical Significance**: Are p-values < 0.05?
- ✅ **Economic Significance**: Are effect sizes meaningful?
- ✅ **Model Fit**: Is R² reasonable for panel data?
- ✅ **Diagnostics**: Did the model pass diagnostic tests?
- ✅ **Robustness**: Are results consistent across specifications?

## 📈 Tier 1: Pooled Panel Results

### Understanding the Output

```json
{
  "model_type": "fixed_effects_twoway",
  "observations": 44289,
  "r_squared": 0.743,
  "r_squared_within": 0.682,
  "coefficients": {
    "conflict_intensity": -0.0234,
    "distance_to_border": -0.0156,
    "exchange_rate_premium": -0.0412,
    "zone_irg_x_time": 0.0089
  },
  "p_values": {
    "conflict_intensity": 0.001,
    "distance_to_border": 0.023,
    "exchange_rate_premium": 0.000,
    "zone_irg_x_time": 0.156
  }
}
```

### Key Metrics Explained

#### R-squared (R²)
- **Overall R²**: 0.743 = Model explains 74.3% of price variation
- **Within R²**: 0.682 = Explains 68.2% after removing fixed effects
- **Benchmark**: R² > 0.5 is good for panel data

#### Coefficient Interpretation

**Conflict Intensity**: -0.0234 (p=0.001)
- 📊 **Meaning**: Each additional conflict event reduces market integration by 2.34%
- ⚠️ **Significance**: Highly significant (p < 0.01)
- 💡 **Policy**: Conflict disrupts market functioning

**Exchange Rate Premium**: -0.0412 (p=0.000)
- 📊 **Meaning**: 10% increase in premium reduces integration by 4.12%
- ⚠️ **Significance**: Extremely significant
- 💡 **Policy**: Dual exchange rates fragment markets

### Fixed Effects Interpretation

```python
# Market fixed effects capture:
- Geographic isolation
- Infrastructure quality
- Local market characteristics

# Time fixed effects capture:
- National shocks
- Seasonal patterns
- Policy changes
```

## 📊 Tier 2: Commodity-Specific Results

### Cointegration Results

```json
{
  "commodity": "Wheat",
  "market_pairs_tested": 156,
  "cointegrated_pairs": 89,
  "cointegration_rate": 0.571,
  "average_half_life_days": 12.3,
  "threshold_detected": true,
  "threshold_value": 8.5,
  "threshold_variable": "conflict_events"
}
```

#### Integration Metrics

**Cointegration Rate**: 57.1%
- 📊 **Meaning**: 57% of wheat market pairs move together long-term
- 📈 **Benchmark**: >50% indicates reasonable integration
- 🌾 **Context**: Higher for staples, lower for perishables

**Half-Life**: 12.3 days
- 📊 **Meaning**: Price shocks dissipate 50% in 12.3 days
- ⚡ **Speed**: Faster = better integrated
- 📏 **Benchmark**: <30 days is well-integrated

### Threshold Effects

```json
{
  "low_regime": {
    "conflict_events": "< 8.5",
    "observations": 0.72,
    "integration_coefficient": 0.84,
    "half_life_days": 8.7
  },
  "high_regime": {
    "conflict_events": ">= 8.5",
    "observations": 0.28,
    "integration_coefficient": 0.41,
    "half_life_days": 24.6
  }
}
```

**Interpretation**:
- 🕊️ **Low conflict**: Strong integration (0.84), fast adjustment (8.7 days)
- ⚔️ **High conflict**: Weak integration (0.41), slow adjustment (24.6 days)
- 📊 **Regime split**: 72% low conflict, 28% high conflict periods

## 🔍 Tier 3: Validation Results

### Factor Analysis Results

```json
{
  "factors_extracted": 3,
  "variance_explained": {
    "factor1": 0.423,
    "factor2": 0.187,
    "factor3": 0.094,
    "total": 0.704
  },
  "factor_interpretation": {
    "factor1": "Market accessibility",
    "factor2": "Conflict intensity", 
    "factor3": "Exchange rate dynamics"
  }
}
```

**Understanding Factors**:
- **Factor 1** (42.3%): Primary driver is physical market access
- **Factor 2** (18.7%): Conflict explains significant variation
- **Factor 3** (9.4%): Currency issues matter but less dominant

### Granger Causality Tests

```json
{
  "test_results": [
    {
      "cause": "conflict_events",
      "effect": "price_dispersion",
      "f_statistic": 8.34,
      "p_value": 0.0001,
      "lags": 4,
      "conclusion": "Conflict Granger-causes price dispersion"
    }
  ]
}
```

**Interpretation**:
- ✅ **Significant**: p < 0.05 indicates causality
- 📅 **Timing**: Effect occurs with 4-period lag
- 🎯 **Direction**: Conflict → Price dispersion (not reverse)

### Impulse Response Functions

```
Response of Prices to Conflict Shock:
Period 0: 0.00 (shock occurs)
Period 1: 0.23 [0.18, 0.28]
Period 2: 0.19 [0.14, 0.24]
Period 3: 0.15 [0.10, 0.20]
Period 4: 0.12 [0.07, 0.17]
...
Period 10: 0.02 [-0.01, 0.05]
```

**Reading IRFs**:
- 📈 **Peak impact**: Period 1 (23% price increase)
- 📉 **Decay rate**: Effect halves every ~3 periods
- 🎯 **Persistence**: Significant for ~8 periods
- 📊 **Confidence**: 95% intervals in brackets

## 📋 Diagnostic Test Results

### Essential Diagnostics

```json
{
  "serial_correlation": {
    "test": "Wooldridge",
    "statistic": 2.34,
    "p_value": 0.234,
    "conclusion": "No serial correlation"
  },
  "heteroskedasticity": {
    "test": "Modified Wald",
    "statistic": 834.2,
    "p_value": 0.000,
    "conclusion": "Heteroskedasticity present"
  },
  "cross_sectional_dependence": {
    "test": "Pesaran CD",
    "statistic": 12.45,
    "p_value": 0.000,
    "conclusion": "Cross-sectional dependence detected"
  }
}
```

### Diagnostic Actions

| Test Failed | Implication | Correction Applied |
|------------|-------------|-------------------|
| Serial Correlation | Biased standard errors | Cluster-robust SEs |
| Heteroskedasticity | Inefficient estimates | HC3 robust SEs |
| Cross-sectional Dependence | Correlation across units | Driscoll-Kraay SEs |

## 💡 Policy Implications

### Translating Results to Policy

#### Market Integration Status

```python
if cointegration_rate > 0.7:
    status = "Well integrated"
    policy = "Maintain current infrastructure"
elif cointegration_rate > 0.5:
    status = "Moderately integrated"
    policy = "Targeted improvements needed"
else:
    status = "Poorly integrated"
    policy = "Major interventions required"
```

#### Conflict Impact Assessment

```python
# Calculate economic cost
conflict_coefficient = -0.0234
average_conflicts = 5.2
price_impact = conflict_coefficient * average_conflicts
# Result: 12.2% higher prices due to conflict
```

#### Exchange Rate Effects

```python
# Dual rate impact
premium = 0.68  # 68% premium
coefficient = -0.0412
integration_loss = premium * coefficient
# Result: 2.8% reduction in market integration
```

## 📊 Comparing Across Commodities

### Integration Hierarchy

```
Strong Integration (Half-life < 10 days):
├── Fuel (Diesel): 7.2 days
├── Sugar: 8.9 days
└── Wheat Flour: 9.8 days

Moderate Integration (10-20 days):
├── Rice: 12.3 days
├── Wheat: 14.1 days
└── Oil (Vegetable): 16.7 days

Weak Integration (> 20 days):
├── Vegetables: 23.4 days
├── Meat (Chicken): 28.9 days
└── Eggs: 31.2 days
```

### Factors Affecting Integration

1. **Product Characteristics**
   - Perishability
   - Standardization
   - Value-to-weight ratio

2. **Market Structure**
   - Number of traders
   - Storage capacity
   - Transport infrastructure

3. **External Shocks**
   - Conflict intensity
   - Exchange rate volatility
   - Border closures

## 🎯 Key Takeaways

### For Policymakers

1. **Priority Markets**: Focus on poorly integrated markets
2. **Timing**: Interventions most effective in low-conflict periods
3. **Exchange Rates**: Unification would improve integration by ~3%
4. **Infrastructure**: Border markets need better connections

### For Researchers

1. **Model Performance**: R² > 0.7 indicates good fit
2. **Robustness**: Results consistent across specifications
3. **Mechanisms**: Conflict operates through transport disruption
4. **Further Research**: Investigate seasonal patterns

## 📈 Visualization Tips

### Creating Effective Charts

```python
# Integration network map
plot_integration_network(
    cointegration_matrix,
    threshold=0.7,  # Show only strong links
    node_size='market_size',
    edge_width='integration_strength'
)

# Time series with events
plot_price_series_with_events(
    prices,
    conflict_events,
    highlight_periods='high_conflict',
    add_trend=True
)
```

## 🔍 Robustness Checks

### Verifying Results

1. **Alternative Specifications**
   ```python
   # Try different fixed effects
   results_fe_market = run_model(fe_type='market')
   results_fe_time = run_model(fe_type='time')
   results_fe_both = run_model(fe_type='twoway')
   ```

2. **Sample Sensitivity**
   ```python
   # Exclude potential outliers
   results_no_fuel = run_model(exclude=['Fuel'])
   results_no_2020 = run_model(exclude_year=2020)
   ```

3. **Method Comparison**
   ```python
   # Compare estimation methods
   results_ols = run_model(method='ols')
   results_fe = run_model(method='fixed_effects')
   results_re = run_model(method='random_effects')
   ```

## 📚 Further Reading

- [Visualization Guide](./visualization.md) - Creating compelling visualizations
- [Model Documentation](../03-api-reference/models/) - Technical specifications
- [Methodology](../05-methodology/) - Theoretical background

---

*Questions about interpretation? See the [FAQ](./faq.md) or consult the [methodology documentation](../05-methodology/).*