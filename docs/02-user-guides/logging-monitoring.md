# Enhanced Logging System for Yemen Market Integration

## Overview

Our custom logging solution combines the best features from modern Python logging libraries, specifically optimized for econometric analysis and data science workflows. It provides structured logging, experiment tracking, performance monitoring, and beautiful console output with minimal setup.

## Key Features

### 🎯 **Unified Interface**

- Single logger instance with intuitive API
- Combines **loguru** (simplicity) + **structlog** (structure) + **rich** (beauty)
- Compatible with Jupyter notebooks and terminal environments

### 📊 **Experiment Tracking Integration**

- Automatic MLflow/Neptune/Weights & Biases integration
- Log metrics, parameters, and artifacts seamlessly
- Track model performance across experiments

### ⚡ **Performance Monitoring**

- Built-in timers and performance tracking
- Automatic duration logging for operations
- Performance metrics stored separately for analysis

### 🎨 **Beautiful Output**

- Rich console formatting with colors
- Progress bars for long-running operations
- Pretty exception tracebacks with local variables

### 📁 **Smart File Management**

- Automatic log rotation (size/time based)
- Compression of old logs
- Separate files for different log types

### 🔍 **Structured Logging**

- JSON serialization for machine readability
- Context injection for all log entries
- Easy filtering and searching

## Installation

The logging dependencies are included in the main project:

```bash
pip install -e .

# For experiment tracking support:
pip install -e ".[experiment]"
```

## Quick Start

```python
from yemen_market.utils.logging import info, warning, error, timer, progress

# Basic logging
info("Starting analysis")
warning("Missing data detected")

# Performance tracking
with timer("data_processing"):
    # Your code here
    pass

# Progress tracking
with progress("Processing markets", total=100) as update:
    for i in range(100):
        # Process market
        update(1)
```

## Usage Examples

### 1. Basic Logging with Context

```python
from yemen_market.utils.logging import info, bind, context

# Bind persistent context
bind(analysis="threshold_vecm", country="Yemen")
info("Starting analysis")  # Will include analysis and country in all logs

# Temporary context
with context(market="Sana'a"):
    info("Processing market data")  # Includes market only within this block
```

### 2. Performance Tracking

```python
from yemen_market.utils.logging import timer, log_execution

# Context manager approach
with timer("model_fitting"):
    model.fit(X, y)
    
# Decorator approach
@log_execution(level="INFO")
def complex_calculation():
    # Function execution will be automatically timed and logged
    return result
```

### 3. Experiment Tracking

```python
from yemen_market.utils.logging import log_param, log_metric, log_model_performance

# Log hyperparameters
log_param("learning_rate", 0.01)
log_param("n_estimators", 100)

# Log metrics during training
for epoch in range(100):
    log_metric("loss", loss_value, step=epoch)
    log_metric("accuracy", acc_value, step=epoch)

# Log final model performance
log_model_performance("random_forest", {
    "rmse": 0.23,
    "r2": 0.89,
    "mae": 0.15
})
```

### 4. Data Pipeline Logging

```python
from yemen_market.utils.logging import log_data_shape, info

# Automatically log DataFrame shapes
df = pd.read_csv("prices.csv")
log_data_shape("raw_prices", df)  # Logs: "Data shape - raw_prices: (1000, 20) (DataFrame)"

# Log processing steps
info("Data quality metrics", 
     quality={
         "missing_pct": 2.3,
         "outliers": 15,
         "date_range": "2015-2023"
     })
```

### 5. Progress Tracking

```python
from yemen_market.utils.logging import progress

# Beautiful progress bars for long operations
with progress("Bootstrap simulation", total=1000) as update:
    for i in range(1000):
        # Run simulation
        result = run_bootstrap_iteration(i)
        
        # Update progress
        update(1, f"Iteration {i+1}/1000")
        
        # Log intermediate results
        if i % 100 == 0:
            info(f"Completed {i} iterations, mean: {result.mean():.4f}")
```

### 6. Error Handling

```python
from yemen_market.utils.logging import error, exception

try:
    result = risky_calculation()
except ValueError as e:
    error("Calculation failed", 
          input_shape=data.shape,
          error_type=type(e).__name__)
    
    # Log full exception with beautiful traceback
    exception("Detailed error information")
```

## Configuration

### Default Configuration

The logger comes with sensible defaults:

- Console output: INFO level with colors
- File output: DEBUG level with rotation at 100MB
- Log retention: 30 days
- Compression: ZIP for archived logs

### Custom Configuration

```python
from yemen_market.utils.logging import EconometricLogger, LogConfig
from pathlib import Path

# Create custom configuration
config = LogConfig(
    log_dir=Path("custom_logs"),
    console_level="DEBUG",
    file_level="TRACE",
    rotation="50 MB",
    retention="7 days",
    compression="gzip",
    track_experiments=True,
    track_performance=True
)

# Create custom logger
logger = EconometricLogger(config)
```

## Log File Structure

```
logs/
├── yemen_market_2025-05-28.log      # Main application logs
├── yemen_market_2025-05-27.log.zip  # Compressed old logs
├── performance.jsonl                 # Performance metrics
└── experiments/                      # MLflow experiment tracking
    └── mlruns/
```

## Integration with Existing Code

The logger is designed to integrate seamlessly with your existing codebase:

```python
# In your data processing module
from yemen_market.utils.logging import info, timer, log_data_shape

def process_price_data(df: pd.DataFrame) -> pd.DataFrame:
    with timer("price_processing"):
        info("Starting price data processing")
        log_data_shape("input", df)
        
        # Your processing logic
        processed = df.copy()
        # ... processing steps ...
        
        log_data_shape("output", processed)
        info("Price processing complete")
        return processed
```

## Best Practices

1. **Use Context Binding**: Bind relevant context at the start of major operations
2. **Log Data Shapes**: Always log shapes when transforming data
3. **Time Critical Operations**: Use timers for operations that might be slow
4. **Structure Your Logs**: Use keyword arguments for structured data
5. **Log Exceptions Properly**: Use `exception()` to get full tracebacks
6. **Track Experiments**: Log all hyperparameters and metrics

## Performance Considerations

- Logging is asynchronous (enqueued) for file operations
- Minimal overhead for console output
- Performance logs stored separately to avoid cluttering main logs
- JSON serialization only when needed

## Advantages Over Standard Logging

1. **Zero Configuration**: Works out of the box with sensible defaults
2. **Beautiful Output**: Rich formatting, colors, and progress bars
3. **Structured by Default**: All logs are structured for easy parsing
4. **Integrated Experiment Tracking**: No separate setup for MLflow
5. **Performance Monitoring**: Built-in timing and profiling
6. **Context Management**: Easy context injection and propagation
7. **Type Hints**: Full type hints for better IDE support

## Use Cases in Econometric Analysis

### Time Series Analysis

```python
with context(analysis="time_series", method="ARIMA"):
    with timer("model_selection"):
        best_model = auto_arima(data)
    log_model_performance("ARIMA", {"aic": best_model.aic()})
```

### Panel Data Analysis

```python
bind(model_type="fixed_effects", n_entities=50, n_periods=100)
with progress("Panel regression", total=n_bootstraps) as update:
    for i in range(n_bootstraps):
        results = run_panel_regression(sample_with_replacement(data))
        update(1)
```

### Threshold Models

```python
info("Starting threshold estimation")
log_param("grid_points", 100)
with timer("grid_search"):
    threshold = estimate_threshold(data)
log_metric("optimal_threshold", threshold)
```

## Troubleshooting

### Common Issues

1. **No console output**: Check console level in configuration
2. **Large log files**: Adjust rotation settings
3. **Missing experiments**: Ensure MLflow is installed with `pip install -e ".[experiment]"`

### Debug Mode

Enable debug mode for maximum verbosity:

```python
config = LogConfig(console_level="DEBUG", file_level="TRACE")
```

## Future Enhancements

- Remote logging support (Elasticsearch, CloudWatch)
- Distributed tracing for parallel computations
- Custom dashboard for real-time monitoring
- Integration with more experiment tracking platforms

---

The enhanced logging system is designed to make your econometric analysis more transparent, reproducible, and easier to debug. It provides powerful features while maintaining simplicity in everyday use.
