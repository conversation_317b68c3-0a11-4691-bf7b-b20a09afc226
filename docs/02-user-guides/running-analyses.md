# Running Analyses Guide

## 🎯 Overview

This guide walks you through running market integration analyses using the Yemen Market Integration Platform. From basic analysis to advanced configurations, you'll learn how to execute and customize the three-tier econometric framework.

## 🚀 Quick Start

### Basic Analysis

```bash
# Run complete three-tier analysis
python scripts/analysis/run_three_tier_models_updated.py

# Or use the enhanced pipeline
python scripts/analysis/enhanced_analysis_pipeline.py
```

This will:
1. Load the prepared data
2. Run all three tiers of analysis
3. Generate diagnostic reports
4. Save results to `results/`

## 📋 Prerequisites

Before running analyses, ensure:

- ✅ Data pipeline completed ([see Data Pipeline Guide](./data-pipeline.md))
- ✅ Virtual environment activated
- ✅ At least 8GB RAM available
- ✅ Output directories exist

```bash
# Verify data exists
ls data/processed/panels/

# Create output directories
mkdir -p results/{tier1,tier2,tier3,diagnostics,reports}
```

## 🔧 Configuration Options

### Default Configuration

```python
# config/model_config.yaml
tier1:
  model_type: fixed_effects
  fe_type: twoway
  cluster_var: market
  include_trends: true
  
tier2:
  test_cointegration: true
  threshold_vecm: true
  max_lags: 12
  
tier3:
  n_factors: 3
  include_conflict: true
  validation_tests: [granger, irf, variance_decomp]
```

### Custom Configuration

Create your own configuration:

```python
# my_config.py
config = {
    'tier1': {
        'model_type': 'fixed_effects',
        'fe_type': 'market',  # Only market fixed effects
        'controls': ['conflict_intensity', 'exchange_rate_premium'],
        'se_type': 'driscoll-kraay'
    },
    'tier2': {
        'commodities': ['Wheat', 'Rice'],  # Specific commodities
        'markets': ['Sana\'a', 'Aden'],   # Specific markets
        'threshold_var': 'conflict_events'
    },
    'tier3': {
        'n_factors': 5,
        'factor_method': 'ml',  # Maximum likelihood
        'rotation': 'varimax'
    }
}
```

## 📊 Three-Tier Analysis Framework

### Tier 1: Pooled Panel Analysis

**Purpose**: Aggregate market integration patterns

```python
from yemen_market.models.three_tier import PooledPanelModel

# Initialize model
model = PooledPanelModel(config=tier1_config)

# Fit model
results = model.fit(panel_data)

# View results
print(results.summary())
```

**Key Outputs**:
- Overall integration coefficients
- Fixed effects estimates
- Robust standard errors
- Diagnostic test results

### Tier 2: Commodity-Specific Analysis

**Purpose**: Individual commodity dynamics

```python
from yemen_market.models.three_tier import CommoditySpecificModel

# Analyze specific commodity
wheat_model = CommoditySpecificModel(commodity='Wheat')
wheat_results = wheat_model.fit(panel_data)

# Check for threshold effects
if wheat_results.has_threshold:
    print(f"Threshold detected at: {wheat_results.threshold_value}")
    print(f"Low regime: {wheat_results.low_regime_stats}")
    print(f"High regime: {wheat_results.high_regime_stats}")
```

**Key Features**:
- Cointegration testing
- Threshold VECM estimation
- Market pair analysis
- Regime-specific parameters

### Tier 3: Validation & Policy Analysis

**Purpose**: Validate results and extract policy insights

```python
from yemen_market.models.three_tier import ValidationModel

# Run validation
validator = ValidationModel()
validation_results = validator.validate(
    tier1_results, 
    tier2_results,
    include_tests=['granger', 'irf', 'factor']
)

# Extract policy implications
policy_insights = validator.get_policy_implications()
```

**Validation Tests**:
- Granger causality
- Impulse response functions
- Variance decomposition
- Factor analysis

## 🎛️ Advanced Options

### Parallel Processing

Speed up analysis with parallel execution:

```python
# Run commodities in parallel
from yemen_market.utils import parallel_run

results = parallel_run(
    CommoditySpecificModel,
    commodities=['Wheat', 'Rice', 'Sugar', 'Oil'],
    n_jobs=4
)
```

### Memory-Efficient Processing

For large datasets:

```python
# Process in chunks
from yemen_market.data import ChunkedPanelBuilder

builder = ChunkedPanelBuilder(chunk_size=10000)
for chunk in builder.iter_chunks():
    results = model.fit_partial(chunk)
```

### Custom Models

Extend the framework:

```python
from yemen_market.models.three_tier import BaseModel

class MyCustomModel(BaseModel):
    def fit(self, data):
        # Custom implementation
        pass
    
    def predict(self, data):
        # Custom predictions
        pass
```

## 📈 Running Specific Analyses

### Exchange Rate Analysis

```bash
python scripts/analysis/analyze_price_transmission.py \
    --focus exchange_rate \
    --zones "IRG,Defacto Authority"
```

### Conflict Impact Assessment

```python
from yemen_market.analysis import ConflictAnalysis

analyzer = ConflictAnalysis(
    conflict_threshold=5,  # Events per month
    window_size=90        # Days
)

impact = analyzer.assess_integration_impact(panel_data)
```

### Spatial Analysis

```python
from yemen_market.analysis import SpatialAnalysis

spatial = SpatialAnalysis(
    spatial_weight_type='distance',
    cutoff_km=100
)

spillovers = spatial.estimate_spatial_effects(panel_data)
```

## 🔍 Monitoring Progress

### Using Progress Bars

```python
from yemen_market.utils.logging import progress

with progress("Running analysis", total=len(commodities)) as pbar:
    for commodity in commodities:
        analyze_commodity(commodity)
        pbar.update(1)
```

### Logging Configuration

```python
# Set logging level
import logging
logging.getLogger('yemen_market').setLevel(logging.DEBUG)

# Or use environment variable
export YEMEN_LOG_LEVEL=DEBUG
```

## 📊 Output Files

### Results Structure

```
results/
├── tier1/
│   ├── pooled_results.json
│   ├── coefficients.csv
│   └── diagnostics.json
├── tier2/
│   ├── wheat_results.json
│   ├── rice_results.json
│   └── threshold_tests.csv
├── tier3/
│   ├── validation_results.json
│   ├── factor_loadings.csv
│   └── policy_implications.md
└── reports/
    ├── executive_summary.html
    └── technical_appendix.pdf
```

### Reading Results

```python
import json
import pandas as pd

# Load JSON results
with open('results/tier1/pooled_results.json') as f:
    tier1_results = json.load(f)

# Load CSV coefficients
coefficients = pd.read_csv('results/tier1/coefficients.csv')

# Load diagnostic results
diagnostics = pd.read_json('results/tier1/diagnostics.json')
```

## 🐛 Error Handling

### Common Errors

1. **Insufficient Data**
   ```
   Error: Not enough observations for market pair
   Solution: Increase min_observations or exclude market
   ```

2. **Convergence Issues**
   ```
   Error: Model failed to converge
   Solution: Adjust optimization parameters or simplify model
   ```

3. **Memory Errors**
   ```
   Error: Unable to allocate memory
   Solution: Use chunked processing or reduce dataset size
   ```

### Debug Mode

```bash
# Run with detailed debugging
python scripts/analysis/run_three_tier_models_updated.py --debug

# Save intermediate results
python scripts/analysis/run_three_tier_models_updated.py --save-intermediate
```

## ✅ Validation Checks

### Pre-Analysis Validation

```python
from yemen_market.data import DataValidator

validator = DataValidator()
issues = validator.check_panel(panel_data)

if issues:
    print("Data issues found:")
    for issue in issues:
        print(f"- {issue}")
```

### Post-Analysis Validation

```python
# Verify results consistency
assert results.tier1.nobs == len(panel_data)
assert all(results.tier2[commodity].converged 
          for commodity in results.tier2)
```

## 🎯 Best Practices

### 1. Start Simple
Begin with basic models before adding complexity:
```python
# Start with
config = {'tier1': {'model_type': 'pooled_ols'}}

# Then advance to
config = {'tier1': {'model_type': 'fixed_effects', 'fe_type': 'twoway'}}
```

### 2. Check Diagnostics
Always review diagnostic tests:
```python
if results.diagnostics['serial_correlation']['p_value'] < 0.05:
    print("Warning: Serial correlation detected")
    # Apply correction
```

### 3. Document Choices
Keep track of modeling decisions:
```python
metadata = {
    'date': datetime.now(),
    'config': config,
    'data_version': panel_data.attrs['version'],
    'notes': 'Excluded fuel due to price controls'
}
```

## 📚 Next Steps

- [Interpreting Results](./interpreting-results.md) - Understanding outputs
- [Visualization Guide](./visualization.md) - Creating plots
- [Model Documentation](../03-api-reference/models/) - Technical details

---

*Need help? Check the [FAQ](./faq.md) or see [troubleshooting](./troubleshooting.md).*