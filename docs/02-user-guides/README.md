# User Guides

## 📚 Overview

This section contains comprehensive guides for using the Yemen Market Integration Platform. Whether you're analyzing data, running models, or interpreting results, you'll find step-by-step instructions here.

## 🗺️ Guide Navigation

### Core Workflows

1. **[Data Pipeline Guide](./data-pipeline.md)**
   - Understanding data sources
   - Running data processing
   - Managing data updates
   - Quality assurance

2. **[Running Analyses](./running-analyses.md)**
   - Preparing data for analysis
   - Configuring models
   - Executing analysis pipeline
   - Handling errors

3. **[Interpreting Results](./interpreting-results.md)**
   - Understanding output formats
   - Key metrics explained
   - Statistical significance
   - Policy implications

4. **[Visualization Guide](./visualization.md)**
   - Creating plots and charts
   - Customizing visualizations
   - Exporting for publications
   - Interactive dashboards

### Specialized Guides

5. **[Balanced Panel Creation](./balanced-panel-creation.md)**
   - When to use balanced panels
   - Creation process
   - Handling missing data
   - Validation steps

6. **[Diagnostic Testing](./diagnostic-testing.md)**
   - Available diagnostic tests
   - Interpreting test results
   - Automatic corrections
   - Reporting diagnostics

7. **[Logging and Monitoring](./logging-monitoring.md)**
   - Using the logging system
   - Performance monitoring
   - Debugging with logs
   - Log analysis

## 🎯 Quick Start by Task

### "I want to..."

#### Run a Basic Analysis
1. Read [Running Analyses](./running-analyses.md)
2. Follow the quick start example
3. Check [Interpreting Results](./interpreting-results.md)

#### Update the Data
1. See [Data Pipeline Guide](./data-pipeline.md#updating-data)
2. Run update scripts
3. Validate new data

#### Create Custom Visualizations
1. Review [Visualization Guide](./visualization.md)
2. Check example notebooks
3. Use provided templates

#### Debug an Error
1. Check [Troubleshooting](./troubleshooting.md)
2. Review [Logging Guide](./logging-monitoring.md)
3. Search error in FAQ

## 📋 Common Workflows

### Weekly Analysis Update

```bash
# 1. Update data
python scripts/data_collection/download_data.py

# 2. Process new data
python scripts/data_processing/process_wfp_data.py

# 3. Rebuild panel
python scripts/analysis/create_integrated_balanced_panel.py

# 4. Run analysis
python scripts/analysis/run_three_tier_models_updated.py

# 5. Generate report
python scripts/generate_report.py
```

### Custom Commodity Analysis

```python
# Focus on specific commodities
from yemen_market.analysis import CommodityAnalysis

analysis = CommodityAnalysis(
    commodities=['Wheat', 'Rice', 'Sugar'],
    regions=['Sana\'a', 'Aden', 'Taiz']
)

results = analysis.run()
analysis.create_report('reports/wheat_rice_sugar.html')
```

### Conflict Impact Assessment

```python
# Analyze conflict effects
from yemen_market.analysis import ConflictImpactAnalysis

impact = ConflictImpactAnalysis(
    conflict_threshold=10,  # Events per month
    pre_period='2018-01-01',
    post_period='2020-01-01'
)

results = impact.assess_market_integration()
impact.visualize_results()
```

## 💡 Best Practices

### Data Management
✅ Always validate data after updates  
✅ Keep raw data unchanged  
✅ Document data transformations  
✅ Use version control for configs  

### Analysis
✅ Start with exploratory analysis  
✅ Check diagnostic test results  
✅ Validate against known patterns  
✅ Document assumptions  

### Reporting
✅ Include confidence intervals  
✅ Explain limitations  
✅ Provide policy context  
✅ Use clear visualizations  

## 🔧 Advanced Topics

### Performance Optimization
- [Chunked Processing](./data-pipeline.md#performance)
- [Parallel Execution](./running-analyses.md#parallel)
- [Caching Strategies](./performance-tips.md)

### Customization
- [Custom Models](../03-api-reference/models/custom-models.md)
- [Feature Engineering](../03-api-reference/features/)
- [Pipeline Extensions](../04-development/extending.md)

### Integration
- [API Usage](../03-api-reference/)
- [Notebook Integration](./notebook-usage.md)
- [Automation](./automation.md)

## 📊 Output Formats

### Analysis Results
- **JSON**: Structured results for programming
- **CSV**: Tabular data for Excel
- **HTML**: Interactive reports
- **PDF**: Publication-ready documents

### Visualizations
- **PNG**: High-resolution images
- **SVG**: Scalable vector graphics
- **Interactive**: HTML/JavaScript plots
- **LaTeX**: Publication figures

## 🆘 Getting Help

### Resources
1. [FAQ](./faq.md) - Frequently asked questions
2. [Troubleshooting](./troubleshooting.md) - Common issues
3. [Examples](../notebooks/) - Working examples
4. [API Docs](../03-api-reference/) - Technical reference

### Support Channels
- GitHub Issues - Bug reports and features
- Discussions - General questions
- Email - Direct support

## 🎓 Learning Path

### Beginner
1. [Getting Started](../00-getting-started/)
2. [Data Pipeline Basics](./data-pipeline.md#basics)
3. [Simple Analysis](./running-analyses.md#simple)

### Intermediate
1. [Full Pipeline](./data-pipeline.md)
2. [Model Configuration](./running-analyses.md#configuration)
3. [Custom Visualizations](./visualization.md)

### Advanced
1. [Custom Models](../03-api-reference/models/)
2. [Pipeline Extensions](../04-development/extending.md)
3. [Performance Tuning](./performance-tips.md)

---

*Can't find what you need? Check the [FAQ](./faq.md) or open an issue.*