# Troubleshooting Guide

## 🚨 Common Issues and Solutions

This guide helps you resolve common problems when using the Yemen Market Integration Platform.

## 🔧 Installation Issues

### Python Version Errors

**Error**: `python3.10: command not found`

**Solution**:
```bash
# Check Python version
python --version

# Install Python 3.10+ using pyenv
curl https://pyenv.run | bash
pyenv install 3.11.0
pyenv global 3.11.0

# Or use conda
conda create -n yemen python=3.11
conda activate yemen
```

### Package Installation Failures

**Error**: `error: Microsoft Visual C++ 14.0 is required` (Windows)

**Solution**:
1. Install [Visual Studio Build Tools](https://visualstudio.microsoft.com/downloads/)
2. Or use pre-compiled wheels:
   ```bash
   pip install --only-binary :all: statsmodels
   ```

**Error**: `No matching distribution found for package`

**Solution**:
```bash
# Update pip first
pip install --upgrade pip

# Try alternative index
pip install -i https://pypi.org/simple/ package_name

# Install from requirements
pip install -r requirements.txt --no-cache-dir
```

### Virtual Environment Issues

**Error**: `venv/bin/activate: No such file or directory`

**Solution**:
```bash
# Ensure you're in project directory
cd /path/to/yemen-market-integration

# Recreate virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

## 💾 Data Issues

### Missing Data Files

**Error**: `FileNotFoundError: data/raw/acaps/control_zones.shp`

**Solution**:
1. Download manually from [ACAPS Yemen Hub](https://www.acaps.org/country/yemen)
2. Place files in correct directory:
   ```bash
   mkdir -p data/raw/acaps
   # Copy downloaded files here
   ```

### Data Download Failures

**Error**: `HTTPError: 403 Forbidden` or `429 Too Many Requests`

**Solution**:
```python
# Add retry logic
import time
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

session = requests.Session()
retry = Retry(total=5, backoff_factor=1)
adapter = HTTPAdapter(max_retries=retry)
session.mount('http://', adapter)
session.mount('https://', adapter)
```

### Corrupt Data Files

**Error**: `ParserError: Unable to parse file`

**Solution**:
```bash
# Remove corrupt file
rm data/processed/corrupt_file.parquet

# Re-run processing
python scripts/data_processing/process_wfp_data.py

# Verify file integrity
python -c "import pandas as pd; pd.read_parquet('file.parquet')"
```

## 🧮 Analysis Errors

### Memory Errors

**Error**: `MemoryError` or `Unable to allocate array`

**Solutions**:

1. **Use chunking**:
   ```python
   # Process in chunks
   chunk_size = 10000
   for chunk in pd.read_csv(file, chunksize=chunk_size):
       process_chunk(chunk)
   ```

2. **Reduce dataset**:
   ```python
   # Filter to specific period
   data = data[data['date'] >= '2023-01-01']
   
   # Sample commodities
   data = data[data['commodity'].isin(['Wheat', 'Rice'])]
   ```

3. **Increase memory**:
   ```bash
   # Set memory limit (Linux/Mac)
   ulimit -v unlimited
   
   # Use memory mapping
   data = pd.read_csv(file, low_memory=False)
   ```

### Model Convergence Issues

**Error**: `ConvergenceWarning: Maximum iterations reached`

**Solutions**:

1. **Increase iterations**:
   ```python
   model = Model(max_iter=1000, tolerance=1e-6)
   ```

2. **Simplify model**:
   ```python
   # Remove problematic variables
   simple_model = Model(controls=['conflict', 'distance'])
   ```

3. **Check data issues**:
   ```python
   # Look for perfect collinearity
   corr_matrix = data.corr()
   high_corr = corr_matrix[corr_matrix > 0.95]
   
   # Remove constants
   data = data.loc[:, data.std() > 0]
   ```

### Estimation Failures

**Error**: `LinAlgError: Singular matrix`

**Solutions**:
```python
# Check for multicollinearity
from statsmodels.stats.outliers_influence import variance_inflation_factor

vif = pd.DataFrame()
vif["Variable"] = X.columns
vif["VIF"] = [variance_inflation_factor(X.values, i) 
              for i in range(X.shape[1])]

# Remove variables with VIF > 10
high_vif = vif[vif['VIF'] > 10]['Variable']
X = X.drop(columns=high_vif)
```

## 📊 Visualization Problems

### Plot Display Issues

**Error**: Plots not showing in Jupyter

**Solution**:
```python
# Enable inline plotting
%matplotlib inline

# Or for interactive
%matplotlib widget

# Force display
import matplotlib.pyplot as plt
plt.show()
```

### Export Quality Problems

**Error**: Blurry or low-quality exports

**Solution**:
```python
# Increase DPI
plt.savefig('figure.png', dpi=300, bbox_inches='tight')

# Use vector format
plt.savefig('figure.pdf', format='pdf')

# Set figure size before plotting
plt.figure(figsize=(10, 6))
```

### Missing Fonts

**Error**: `findfont: Font family 'serif' not found`

**Solution**:
```python
# Clear font cache
import matplotlib
matplotlib.font_manager._rebuild()

# Use available fonts
plt.rcParams['font.family'] = 'DejaVu Sans'

# Or install fonts
# Ubuntu: sudo apt-get install fonts-dejavu
# Mac: brew install font-dejavu
```

## 🔐 Permission Errors

### File Access Denied

**Error**: `PermissionError: [Errno 13] Permission denied`

**Solutions**:
```bash
# Check file permissions
ls -la data/

# Fix permissions
chmod 644 data/processed/*.parquet

# Change ownership
sudo chown -R $USER:$USER data/
```

### API Authentication

**Error**: `401 Unauthorized` when accessing APIs

**Solution**:
```bash
# Set environment variables
export HDX_API_KEY="your-key-here"
export ACLED_API_KEY="your-key-here"

# Or use .env file
echo "HDX_API_KEY=your-key-here" >> .env
```

## 🐛 Debugging Techniques

### Enable Debug Logging

```python
# Set logging level
import logging
logging.basicConfig(level=logging.DEBUG)

# Or use environment variable
export YEMEN_LOG_LEVEL=DEBUG
```

### Print Intermediate Results

```python
# Add debug prints
def process_data(data):
    print(f"Input shape: {data.shape}")
    
    # Processing step 1
    result1 = step1(data)
    print(f"After step 1: {result1.shape}")
    
    # Processing step 2
    result2 = step2(result1)
    print(f"After step 2: {result2.shape}")
    
    return result2
```

### Use Python Debugger

```python
# Add breakpoint
import pdb; pdb.set_trace()

# Or use IPython debugger
from IPython.core.debugger import set_trace
set_trace()

# In Jupyter
%debug  # After an error occurs
```

## 🔄 Recovery Procedures

### Corrupted Installation

```bash
# Complete reinstall
rm -rf venv
rm -rf __pycache__
rm -rf *.egg-info

# Recreate environment
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### Data Pipeline Reset

```bash
# Clean all processed data
rm -rf data/interim/*
rm -rf data/processed/*

# Keep raw data
# Rerun pipeline
python scripts/data_processing/process_wfp_data.py
python scripts/data_processing/process_acaps_data.py
python scripts/analysis/create_integrated_balanced_panel.py
```

### Git Repository Issues

```bash
# Reset to clean state
git stash  # Save local changes
git fetch origin
git reset --hard origin/main

# Restore stashed changes if needed
git stash pop
```

## 🆘 Getting Additional Help

### Diagnostic Information

When reporting issues, include:

```bash
# System info
python -c "import sys; print(sys.version)"
pip list | grep -E "pandas|numpy|statsmodels"

# Error traceback
python script.py 2>&1 | tee error.log

# Data sample
python -c "import pandas as pd; df = pd.read_parquet('data.parquet'); print(df.info())"
```

### Creating Minimal Examples

```python
# Minimal reproducible example
import pandas as pd
import numpy as np

# Create sample data
np.random.seed(42)
data = pd.DataFrame({
    'price': np.random.randn(100),
    'market': np.random.choice(['A', 'B'], 100),
    'date': pd.date_range('2023-01-01', periods=100)
})

# Reproduce error
# Your code that causes the error
```

### Contact Support

1. **GitHub Issues**: For bugs and feature requests
2. **Discussions**: For general questions
3. **Email**: For sensitive issues

Always include:
- Complete error message
- Steps to reproduce
- System information
- What you've already tried

---

*Still stuck? Check the [FAQ](./faq.md) or open a [GitHub issue](https://github.com/project/issues/new).*