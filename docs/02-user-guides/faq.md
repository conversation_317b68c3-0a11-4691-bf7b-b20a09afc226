# Frequently Asked Questions (FAQ)

## 📋 General Questions

### What is market integration?

Market integration measures how closely prices in different markets move together. High integration means:
- Price shocks in one market quickly affect others
- Efficient flow of goods between markets
- Similar price levels after accounting for transport costs

### Why study Yemen specifically?

Yemen presents a unique case:
- Active conflict affecting market access
- Dual exchange rate system
- Fragmented territorial control
- Humanitarian crisis requiring targeted interventions

### What data sources are used?

Primary sources:
- **WFP**: Weekly commodity prices from 169 markets
- **ACAPS**: Bi-weekly territorial control maps
- **ACLED**: Daily conflict event data
- **HDX**: Administrative boundaries and infrastructure

## 💻 Technical Questions

### What are the system requirements?

**Minimum**:
- Python 3.10+
- 8GB RAM
- 10GB disk space

**Recommended**:
- Python 3.11
- 16GB RAM
- SSD storage

### How do I update the data?

```bash
# Quick update
python scripts/data_collection/download_data.py --update

# Full refresh
python scripts/data_collection/download_data.py --force-refresh
```

### Why do I get memory errors?

Common causes:
1. Processing entire dataset at once
2. Insufficient RAM
3. Memory leaks in long-running processes

Solutions:
```python
# Use chunking
for chunk in pd.read_csv(file, chunksize=10000):
    process(chunk)

# Clear memory
import gc
gc.collect()
```

## 📊 Analysis Questions

### What is the three-tier framework?

**Tier 1**: Pooled analysis across all markets and commodities
- Overall integration patterns
- Common factors affecting all markets

**Tier 2**: Commodity-specific analysis
- Individual commodity dynamics
- Threshold effects for regime changes

**Tier 3**: Validation and policy analysis
- Robustness testing
- Policy simulations
- Factor extraction

### How is cointegration interpreted?

Cointegration indicates long-term price relationships:
- **Cointegrated**: Prices move together despite short-term deviations
- **Not cointegrated**: No stable long-term relationship
- **Threshold cointegration**: Relationship changes based on conditions

### What are threshold effects?

Markets may integrate differently under different conditions:
```
Low conflict regime: Strong integration (fast price transmission)
High conflict regime: Weak integration (slow/no transmission)
```

### How are fixed effects chosen?

Fixed effects control for unobserved heterogeneity:
- **Market FE**: Control for time-invariant market characteristics
- **Time FE**: Control for common temporal shocks
- **Two-way FE**: Control for both

Selection based on:
- Hausman test results
- Theory and context
- Diagnostic test outcomes

## 🔧 Troubleshooting Questions

### Why does model estimation fail?

Common reasons:
1. **Insufficient variation**: Not enough price changes
2. **Perfect collinearity**: Redundant variables
3. **Convergence issues**: Numerical problems

Debug steps:
```python
# Check data variation
data.groupby('market')['price'].std()

# Check collinearity
pd.DataFrame(data).corr()

# Simplify model
basic_model = Model(controls=['conflict'])
```

### How do I handle missing data?

The pipeline handles missing data automatically:
1. **Smart panel**: Only includes existing commodity-market pairs
2. **Interpolation**: For small gaps (<3 periods)
3. **Exclusion**: For systematic missing patterns

Manual handling:
```python
# Check missing patterns
panel.isnull().sum()

# Forward fill for small gaps
panel.fillna(method='ffill', limit=2)
```

### Why are my results different from the paper?

Possible reasons:
1. **Data updates**: New observations change results
2. **Configuration**: Different model specifications
3. **Random seeds**: Some methods have randomness

Ensure reproducibility:
```python
# Set random seed
np.random.seed(42)

# Use same data version
data = load_panel(version='2024-05-31')

# Match configuration
config = load_config('paper_replication.yaml')
```

## 📈 Results Interpretation

### What R-squared is "good"?

For panel data:
- **> 0.7**: Excellent fit
- **0.5-0.7**: Good fit
- **0.3-0.5**: Moderate fit
- **< 0.3**: Poor fit (but may still be meaningful)

Consider:
- Panel data typically has lower R² than cross-sectional
- Within R² more relevant for fixed effects models
- Economic significance matters more than R²

### How do I know if effects are meaningful?

Consider both statistical and economic significance:

**Statistical**: p-value < 0.05
**Economic**: Effect size matters for policy

Example:
```
Coefficient: -0.002 (p = 0.001)
Statistical: ✓ Significant
Economic: ? 0.2% effect may be too small to matter
```

### What do diagnostic test failures mean?

| Test Failed | Implication | Action |
|------------|-------------|---------|
| Serial Correlation | Biased SEs | Use clustered SEs |
| Heteroskedasticity | Inefficient estimates | Use robust SEs |
| Cross-sectional Dependence | Correlation across units | Use Driscoll-Kraay SEs |
| Non-normality | Inference issues | Check outliers, transform data |

## 🚀 Advanced Questions

### Can I add custom data sources?

Yes! Create a new processor:
```python
class MyDataProcessor:
    def process(self, raw_data):
        # Custom processing logic
        return processed_data

# Register with pipeline
pipeline.register_processor('mydata', MyDataProcessor())
```

### How do I modify the models?

Extend existing classes:
```python
from yemen_market.models import BaseModel

class CustomModel(BaseModel):
    def fit(self, data):
        # Custom estimation
        pass
```

### Can I run distributed processing?

Future versions will support:
- Ray for distributed computing
- Dask for out-of-memory processing
- GPU acceleration for large models

Current workaround:
```python
# Manual parallelization
from multiprocessing import Pool

with Pool(4) as p:
    results = p.map(analyze_commodity, commodities)
```

## 🤝 Getting Help

### Where can I report bugs?

1. Check existing [GitHub Issues](https://github.com/project/issues)
2. Create new issue with:
   - Error message
   - Minimal reproducible example
   - System information

### How can I contribute?

See [Contributing Guide](../../CONTRIBUTING.md):
1. Fork repository
2. Create feature branch
3. Add tests
4. Submit pull request

### Is there a mailing list?

Join discussions:
- GitHub Discussions for general questions
- Issues for bugs/features
- Email maintainers for sensitive topics

## 📚 Additional Resources

### Academic Papers
- Original methodology paper
- Technical appendix
- Replication materials

### Video Tutorials
- [Getting Started](https://youtube.com/...)
- [Running Analysis](https://youtube.com/...)
- [Interpreting Results](https://youtube.com/...)

### Example Notebooks
- `notebooks/01-data-validation.ipynb`
- `notebooks/02-price-patterns.ipynb`
- `notebooks/04_models/01_three_tier_implementation.ipynb`

---

*Can't find your answer? Open a [new discussion](https://github.com/project/discussions) or check the [troubleshooting guide](./troubleshooting.md).*