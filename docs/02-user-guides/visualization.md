# Visualization Guide

## 🎨 Overview

This guide covers creating effective visualizations for Yemen market integration analysis. Learn to create publication-quality figures, interactive dashboards, and compelling data stories.

## 🚀 Quick Start

### Basic Plotting

```python
from yemen_market.visualization import plot_price_series, plot_market_map

# Simple time series
fig = plot_price_series(
    data=panel_data,
    commodity='Wheat',
    markets=['Sana\'a', 'Aden'],
    title='Wheat Prices in Major Markets'
)
fig.savefig('wheat_prices.png', dpi=300, bbox_inches='tight')
```

### Interactive Dashboard

```bash
# Launch Streamlit dashboard
streamlit run scripts/yemen_market_dashboard.py

# Or run simplified version
python scripts/streamlit_dashboard.py
```

## 📊 Core Visualizations

### 1. Time Series Plots

#### Price Trends with Events

```python
from yemen_market.visualization import plot_price_with_events

fig, ax = plot_price_with_events(
    prices=panel_data,
    events=conflict_data,
    commodity='Rice',
    market='Taiz',
    event_threshold=5,  # Show events with >5 incidents
    figsize=(12, 6)
)

# Customize appearance
ax.set_ylabel('Price (USD/kg)')
ax.legend(['Price', 'Conflict Events'], loc='upper left')

# Add annotations
ax.annotate('Border closure', 
            xy=('2023-03-15', 4.5),
            xytext=('2023-01-01', 5.0),
            arrowprops=dict(arrowstyle='->'))
```

#### Multi-Market Comparison

```python
from yemen_market.visualization import plot_market_comparison

fig = plot_market_comparison(
    data=panel_data,
    commodity='Wheat Flour',
    markets=['Sana\'a', 'Aden', 'Taiz', 'Hodeidah'],
    normalize=True,  # Index to 100
    shade_periods=conflict_periods,  # Highlight conflict
    colors='Set1'
)

# Add period markers
for period in policy_changes:
    ax.axvline(period['date'], color='red', 
               linestyle='--', alpha=0.5)
```

### 2. Spatial Visualizations

#### Market Integration Network

```python
from yemen_market.visualization import plot_integration_network

# Create network plot
fig = plot_integration_network(
    cointegration_matrix=results.tier2_cointegration,
    coordinates=market_coordinates,
    threshold=0.7,  # Show links with >70% integration
    node_size='market_volume',
    edge_width='integration_strength',
    node_color='control_zone'
)

# Customize network
fig.update_layout(
    title='Market Integration Network',
    showlegend=True,
    height=800,
    width=1000
)
```

#### Choropleth Maps

```python
from yemen_market.visualization import create_choropleth

# Price level map
map_fig = create_choropleth(
    data=average_prices,
    geometry=yemen_boundaries,
    value_col='avg_price',
    id_col='governorate',
    title='Average Wheat Prices by Governorate',
    colorscale='RdYlBu_r',
    range_color=[2, 6]
)

# Control zone map
zone_map = create_choropleth(
    data=control_zones,
    geometry=zone_boundaries,
    value_col='zone_id',
    categorical=True,
    title='Territorial Control',
    color_map={
        'IRG': '#1f77b4',
        'Defacto Authority': '#ff7f0e',
        'STC': '#2ca02c'
    }
)
```

### 3. Statistical Plots

#### Distribution Comparisons

```python
from yemen_market.visualization import plot_distribution_comparison

# Compare price distributions
fig = plot_distribution_comparison(
    data=panel_data,
    variable='price_usd',
    groups=['control_zone'],
    plot_type='violin',  # or 'box', 'kde'
    title='Price Distribution by Control Zone'
)

# Add statistical annotations
from scipy import stats
for i, zone in enumerate(zones):
    mean = data[data.zone == zone]['price'].mean()
    ax.text(i, mean, f'μ={mean:.2f}', ha='center')
```

#### Correlation Heatmaps

```python
from yemen_market.visualization import plot_correlation_heatmap

# Market correlation matrix
corr_fig = plot_correlation_heatmap(
    data=price_matrix,
    title='Market Price Correlations',
    mask_threshold=0.3,  # Hide weak correlations
    annotate=True,
    cmap='coolwarm',
    center=0
)

# Feature correlation
feature_corr = plot_correlation_heatmap(
    data=features_df,
    cluster=True,  # Hierarchical clustering
    figsize=(10, 8)
)
```

### 4. Model Diagnostics

#### Residual Plots

```python
from yemen_market.visualization import create_diagnostic_plots

# Generate diagnostic plot suite
diag_fig = create_diagnostic_plots(
    model_results=results.tier1,
    plot_types=['residuals', 'qq', 'leverage', 'cooks']
)

# Individual residual plot
residual_fig = plot_residuals(
    residuals=results.residuals,
    fitted=results.fitted_values,
    title='Residuals vs Fitted Values'
)
```

#### Impulse Response Functions

```python
from yemen_market.visualization import plot_irf

# Plot IRF with confidence bands
irf_fig = plot_irf(
    irf_results=results.tier3.irf,
    impulse='conflict_shock',
    response='price_index',
    periods=20,
    alpha=0.05,  # 95% confidence
    title='Response to Conflict Shock'
)

# Multiple IRFs
multi_irf = plot_irf_grid(
    irf_results=results.tier3.irf,
    impulses=['conflict', 'exchange_rate'],
    responses=['price', 'volatility'],
    figsize=(12, 8)
)
```

## 🎨 Customization Options

### Theme Configuration

```python
# Set consistent theme
from yemen_market.visualization import set_theme

set_theme('publication')  # Clean, minimal
# or 'presentation' - larger fonts
# or 'web' - interactive elements

# Custom theme
custom_theme = {
    'font_family': 'Arial',
    'font_size': 12,
    'colors': ['#1f77b4', '#ff7f0e', '#2ca02c'],
    'grid': True,
    'spine_visibility': {'top': False, 'right': False}
}
set_theme(custom_theme)
```

### Color Palettes

```python
# Predefined palettes
from yemen_market.visualization import COLOR_PALETTES

# Categorical
zones_colors = COLOR_PALETTES['zones']  
# {'IRG': '#1f77b4', 'STC': '#2ca02c', ...}

# Sequential
price_colors = COLOR_PALETTES['sequential']
# For continuous data

# Diverging
correlation_colors = COLOR_PALETTES['diverging']
# For data with meaningful center
```

### Export Options

```python
# High-resolution export
fig.savefig('figure.png', dpi=300, bbox_inches='tight')

# Vector format for publications
fig.savefig('figure.pdf', format='pdf', bbox_inches='tight')

# Web-optimized
fig.savefig('figure.svg', format='svg', transparent=True)

# LaTeX integration
fig.savefig('figure.pgf', format='pgf')
```

## 📊 Interactive Visualizations

### Plotly Interactive Charts

```python
import plotly.graph_objects as go
from yemen_market.visualization import create_interactive_plot

# Interactive time series
interactive_fig = create_interactive_plot(
    data=panel_data,
    plot_type='scatter',
    x='date',
    y='price',
    color='commodity',
    hover_data=['market', 'control_zone'],
    title='Interactive Price Explorer'
)

# Add range slider
interactive_fig.update_xaxes(rangeslider_visible=True)

# Save as HTML
interactive_fig.write_html('interactive_prices.html')
```

### Dashboard Components

```python
# Streamlit components
import streamlit as st

# Sidebar filters
commodity = st.sidebar.selectbox(
    'Select Commodity',
    options=panel_data['commodity'].unique()
)

date_range = st.sidebar.date_input(
    'Date Range',
    value=(start_date, end_date)
)

# Main plot
filtered_data = filter_data(panel_data, commodity, date_range)
fig = create_plot(filtered_data)
st.plotly_chart(fig, use_container_width=True)

# Metrics
col1, col2, col3 = st.columns(3)
col1.metric("Average Price", f"${avg_price:.2f}")
col2.metric("Integration", f"{integration:.1%}")
col3.metric("Volatility", f"{volatility:.1%}")
```

## 📈 Publication-Quality Figures

### Academic Paper Requirements

```python
# Set up for publication
import matplotlib.pyplot as plt
plt.rcParams.update({
    'font.size': 10,
    'font.family': 'serif',
    'text.usetex': True,  # LaTeX rendering
    'figure.dpi': 300,
    'savefig.dpi': 300
})

# Create figure with precise dimensions
fig, (ax1, ax2) = plt.subplots(
    2, 1, 
    figsize=(6.5, 8),  # Standard column width
    gridspec_kw={'height_ratios': [2, 1]}
)

# Add subfigure labels
ax1.text(-0.1, 1.05, '(a)', transform=ax1.transAxes, 
         fontsize=12, fontweight='bold')
ax2.text(-0.1, 1.05, '(b)', transform=ax2.transAxes,
         fontsize=12, fontweight='bold')
```

### Policy Brief Graphics

```python
# Simplified, high-impact visualizations
from yemen_market.visualization import create_policy_graphic

# Key finding highlight
highlight_fig = create_policy_graphic(
    data=key_results,
    message="Conflict increases prices by 23%",
    graphic_type='comparison',
    before_after=True
)

# Infographic elements
infographic = create_infographic(
    stats={
        'Markets Analyzed': 169,
        'Price Increase': '23%',
        'Integration Loss': '41%',
        'Affected Population': '2.3M'
    },
    icons=True,
    colors='accessible'  # Color-blind friendly
)
```

## 🎯 Best Practices

### 1. Choose the Right Chart

| Data Type | Recommended Charts |
|-----------|-------------------|
| Time Series | Line plot, area chart |
| Distributions | Histogram, violin plot |
| Correlations | Scatter plot, heatmap |
| Geographic | Choropleth, point map |
| Networks | Node-link diagram |

### 2. Accessibility

```python
# Color-blind friendly palettes
from yemen_market.visualization import COLORBLIND_SAFE

colors = COLORBLIND_SAFE['categorical']

# Add patterns for clarity
for i, bar in enumerate(ax.patches):
    if i % 2 == 0:
        bar.set_hatch('//')
```

### 3. Annotations

```python
# Clear labeling
ax.set_xlabel('Time Period', fontsize=12)
ax.set_ylabel('Price (USD per kg)', fontsize=12)
ax.set_title('Wheat Price Trends During Conflict', 
             fontsize=14, fontweight='bold')

# Data source
fig.text(0.99, 0.01, 'Source: WFP Yemen', 
         ha='right', va='bottom', fontsize=8, 
         transform=fig.transFigure)
```

## 🔧 Troubleshooting

### Common Issues

1. **Memory errors with large plots**
   ```python
   # Downsample for visualization
   plot_data = data.resample('W').mean()  # Weekly average
   ```

2. **Overlapping labels**
   ```python
   # Rotate labels
   plt.xticks(rotation=45, ha='right')
   
   # Or use every nth label
   ax.xaxis.set_major_locator(plt.MaxNLocator(10))
   ```

3. **Export quality issues**
   ```python
   # Ensure high DPI
   plt.savefig('figure.png', dpi=300, bbox_inches='tight',
               pad_inches=0.1, facecolor='white')
   ```

## 📚 Gallery Examples

### Example 1: Market Integration Dashboard

```python
# Complete dashboard example
create_integration_dashboard(
    data=panel_data,
    results=analysis_results,
    output='dashboard.html',
    include=['map', 'network', 'trends', 'stats']
)
```

### Example 2: Report Figure Set

```python
# Generate all figures for report
from yemen_market.visualization import generate_report_figures

figures = generate_report_figures(
    data=panel_data,
    results=results,
    output_dir='figures/',
    formats=['png', 'pdf'],
    style='publication'
)
```

## 🔗 Additional Resources

- [Matplotlib Gallery](https://matplotlib.org/stable/gallery/index.html)
- [Plotly Examples](https://plotly.com/python/)
- [Color Brewer](https://colorbrewer2.org/) - Color schemes
- [Data Viz Catalog](https://datavizcatalogue.com/) - Chart types

---

*For more examples, see the [notebooks](../../notebooks/) directory.*