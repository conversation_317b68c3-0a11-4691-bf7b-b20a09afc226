# Balanced Panel Workflow Guide

## Overview

The Yemen Market Integration project uses a two-stage data preparation process to create analysis-ready datasets:

1. **Standard Panel Creation**: Flexible panels with varying coverage
2. **Balanced Panel Creation**: Perfectly balanced panels for econometric analysis

This guide explains when and how to use each approach.

## Why Two Approaches?

### Standard Panels (Original Workflow)
- **Coverage**: Maximizes data usage (88.4% coverage)
- **Flexibility**: Includes all available market-commodity pairs
- **Use Cases**: 
  - Exploratory data analysis
  - Summary statistics
  - Visualization
  - Methods that handle unbalanced panels

### Balanced Panels (New Workflow)
- **Coverage**: 96.6% after interpolation
- **Structure**: Perfect balance (21 markets × 16 commodities × 75 months)
- **Use Cases**:
  - Fixed effects regression
  - Threshold models
  - Factor analysis
  - Any method requiring balanced panels

## Workflow Steps

### Stage 1: Data Collection and Processing

```bash
# 1. Download raw data
python scripts/data_collection/download_data.py
python scripts/data_collection/download_acled_data.py

# 2. Process individual data sources
python scripts/data_processing/process_wfp_data.py
python scripts/data_processing/process_acaps_data.py
python scripts/data_processing/process_acled_data.py
python scripts/data_processing/run_spatial_joins.py
```

### Stage 2A: Standard Panel Creation (Original)

```bash
# Create standard panels with maximum coverage
python scripts/analysis/build_panel_datasets.py
```

**Output files:**
- `data/processed/panels/integrated_panel.parquet`
- `data/processed/panels/price_transmission_panel.parquet`
- `data/processed/panels/threshold_coint_panel.parquet`
- `data/processed/panels/spatial_panel.parquet`

### Stage 2B: Balanced Panel Creation (Recommended for Econometrics)

```bash
# Step 1: Create perfectly balanced panel
python scripts/analysis/create_balanced_panel.py

# Step 2: Integrate conflict and control zone data
python scripts/analysis/create_integrated_balanced_panel.py
```

**Output files:**
- `data/processed/balanced_panels/balanced_panel_filled.parquet`
- `data/processed/integrated_panel/yemen_integrated_balanced_panel.parquet`

**Note**: These scripts are thin wrappers around `PanelBuilder` methods. You can also create balanced panels programmatically:

```python
from yemen_market.data import PanelBuilder

# Create balanced panel directly
builder = PanelBuilder()
balanced_panel = builder.create_core_balanced_panel()
integrated_panel = builder.integrate_panel_data(balanced_panel)
builder.save_balanced_panels(integrated_panel)
```

### Stage 3: Validation

```bash
# Validate the balanced panel
jupyter notebook notebooks/01b-balanced-panel-validation.ipynb
```

## Using the Balanced Panel in Analysis

### Loading in Python

```python
from yemen_market.data import PanelBuilder

# Initialize builder
builder = PanelBuilder()

# Load integrated balanced panel (recommended)
panel = builder.load_integrated_panel()

# Or load basic balanced panel
balanced_panel = builder.load_balanced_panel(panel_type='filled')

# Validate the panel
validation_results = builder.validate_balanced_panel(panel)
```

### Key Features of the Integrated Panel

The integrated balanced panel includes:

1. **Price Data**: Local and USD prices with 96.6% coverage
2. **Conflict Data**: ACLED events, fatalities, and intensity measures
3. **Control Zones**: ACAPS territorial control (IRG, DFA)
4. **Geographic Data**: Coordinates and distance calculations
5. **Derived Features**: Price volatility, conflict lags, time trends

### Panel Structure

```
Dimensions: 21 markets × 16 commodities × 75 months = 25,200 observations

Markets (21):
- Major cities: Aden, Sana'a, Taiz, Mukalla, etc.
- Coverage across all major governorates

Commodities (16):
- Cereals: Wheat, Wheat flour, Rice (imported)
- Pulses: Beans (kidney red/white), Lentils, Peas
- Vegetables: Onions, Tomatoes, Potatoes
- Other: Oil, Sugar, Salt, Eggs
- Fuel: Diesel, Gas, Petrol-gasoline

Time Period:
- January 2019 to March 2025 (75 months)
```

## Model-Specific Usage

### Three-Tier Econometric Analysis

```python
from yemen_market.models.three_tier import ThreeTierAnalysis

# Load balanced panel
panel = builder.load_integrated_panel()

# Configure analysis
config = {
    'tier1': {'fixed_effects': True, 'cluster': 'market'},
    'tier2': {'commodities': ['Wheat', 'Rice (imported)', 'Sugar']},
    'tier3': {'n_factors': 3}
}

# Run analysis
analysis = ThreeTierAnalysis(config)
results = analysis.run(panel)
```

### Fixed Effects Regression

```python
import linearmodels as lm

# Prepare panel
panel = panel.set_index(['market', 'commodity', 'date'])

# Run pooled OLS with multi-way fixed effects
model = lm.PanelOLS(
    dependent=panel['price_log'],
    exog=panel[['events_total', 'control_zone_DFA']],
    entity_effects=True,
    time_effects=True
)
results = model.fit(cov_type='clustered', cluster_entity=True)
```

## Data Quality Considerations

### Missing Data Handling

The balanced panel uses sophisticated interpolation:
- **Linear interpolation**: For gaps ≤ 2 months
- **Forward/backward fill**: For gaps ≤ 3 months  
- **Remaining gaps**: 3.4% missing after interpolation

### Outlier Treatment

The balanced panel retains outliers as they often represent real conflict-induced shocks. For sensitivity analysis, you can:

```python
# Winsorize extreme values
from scipy.stats import mstats
panel['price_winsorized'] = panel.groupby('commodity')['price'].transform(
    lambda x: mstats.winsorize(x, limits=[0.01, 0.01])
)
```

### Control Zone Coverage

- 90.5% of observations have control zone data
- Missing zones are primarily from early 2019
- Use forward-fill within markets for consistency

## Troubleshooting

### FileNotFoundError

If you get file not found errors:
```bash
# Run the creation scripts in order
python scripts/analysis/create_balanced_panel.py
python scripts/analysis/create_integrated_balanced_panel.py
```

### Memory Issues

For large operations, process by commodity:
```python
# Process commodities separately
for commodity in panel['commodity'].unique():
    comm_data = panel[panel['commodity'] == commodity]
    # Run analysis on comm_data
```

### Validation Failures

Check the validation report:
```python
validation = builder.validate_balanced_panel(panel)
print(validation)
```

## Best Practices

1. **Always validate** the panel before analysis
2. **Document** any additional transformations
3. **Check balance** after any filtering operations
4. **Use appropriate** standard errors (clustered, Driscoll-Kraay)
5. **Test sensitivity** to interpolation methods

## References

- [Panel Data Econometrics](../methodology/world_bank_approach.md)
- [Three-Tier Methodology](../../METHODOLOGY.md)
- [Data Pipeline Documentation](../data/data_pipeline_detailed.md)