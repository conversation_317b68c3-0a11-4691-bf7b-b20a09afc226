# Diagnostic Requirements for World Bank Publication Standards

## Overview

This guide outlines the diagnostic testing requirements for econometric analyses intended for World Bank publication or policy use. All models must pass critical diagnostic tests to ensure valid inference and reliable policy recommendations.

## Core Principles

1. **Transparency**: All diagnostic test results must be reported
2. **Robustness**: Failed tests require appropriate corrections
3. **Replicability**: Diagnostic procedures must be automated
4. **Standards**: Follow international best practices

## Required Tests by Model Type

### Panel Data Models (Tier 1)

#### Critical Tests (Must Pass or Correct)

1. **Serial Correlation**
   - Test: <PERSON><PERSON><PERSON> (2002) test for panel data
   - Null hypothesis: No first-order serial correlation
   - Threshold: p > 0.05
   - Correction if failed: Cluster-robust or HAC standard errors
   
2. **Cross-sectional Dependence**
   - Test: <PERSON><PERSON>aran (2004) CD test
   - Null hypothesis: No cross-sectional correlation
   - Threshold: p > 0.05
   - Correction if failed: Driscoll-Kraay standard errors

3. **Unit Roots**
   - Test: Im-<PERSON><PERSON><PERSON>-<PERSON> (2003) panel unit root test
   - Null hypothesis: All panels contain unit roots
   - Threshold: p < 0.05 (reject null)
   - Correction if failed: First-difference transformation

#### Important Tests (Should Pass)

4. **Heteroskedasticity**
   - Test: Modified Wald test for groupwise heteroskedasticity
   - Threshold: p > 0.05
   - Correction: Robust standard errors (HC3)

5. **Specification**
   - Test: Hausman test (FE vs RE)
   - Threshold: p < 0.05 favors FE
   - Action: Use appropriate estimator

### Time Series Models (Tier 2)

#### Critical Tests

1. **Cointegration**
   - Test: Johansen trace/eigenvalue tests
   - Threshold: At least one cointegrating relationship
   - Correction if failed: VAR in differences

2. **Threshold Effects**
   - Test: Hansen (1999) threshold test
   - Threshold: p < 0.05 indicates threshold
   - Action: Use threshold model if significant

3. **Regime Stability**
   - Test: Parameter constancy within regimes
   - Threshold: Stable parameters
   - Correction: Time-varying parameters

#### Important Tests

4. **Residual Autocorrelation**
   - Test: Ljung-Box test
   - Threshold: p > 0.05
   - Correction: Add lags

5. **ARCH Effects**
   - Test: ARCH-LM test
   - Threshold: p > 0.05
   - Correction: GARCH modeling if needed

### Factor Models (Tier 3)

#### Critical Tests

1. **Factor Adequacy**
   - Test: Scree plot and eigenvalue criteria
   - Threshold: Eigenvalue > 1
   - Action: Select appropriate number of factors

2. **Sphericity**
   - Test: Bartlett's test
   - Threshold: p < 0.05 (reject sphericity)
   - Action: Confirms factor analysis appropriate

#### Important Tests

3. **Factor Stability**
   - Test: Cross-validation
   - Threshold: Stable loadings across samples
   - Action: Report instability

## Interpretation Guidelines

### Test Result Categories

1. **PASS**: Test passed at 5% significance level
2. **FAIL - CORRECTED**: Test failed but appropriate correction applied
3. **FAIL - UNCORRECTED**: Test failed and no correction possible (model invalid)
4. **WARNING**: Test marginally failed or requires caution

### Reporting Requirements

```markdown
## Diagnostic Test Results

### Panel Regression (Tier 1)
- Wooldridge Serial Correlation: PASS (p = 0.234)
- Pesaran CD Test: FAIL - CORRECTED (p = 0.001, using Driscoll-Kraay SEs)
- Im-Pesaran-Shin Unit Root: PASS (p = 0.012)
- Modified Wald Heteroskedasticity: FAIL - CORRECTED (p = 0.001, using robust SEs)
- Hausman Specification: PASS (p = 0.001, FE appropriate)

### Overall Assessment
Model valid with corrected standard errors.
```

## Remediation Strategies

### Serial Correlation

If Wooldridge test fails (p < 0.05):

1. **First choice**: Cluster-robust standard errors
   ```python
   model.fit(cov_type='clustered', cluster_entity=True)
   ```

2. **Alternative**: Newey-West HAC standard errors
   ```python
   model.fit(cov_type='kernel', kernel='bartlett')
   ```

3. **Last resort**: Add lagged dependent variable

### Cross-sectional Dependence

If Pesaran CD test fails (p < 0.05):

1. **First choice**: Driscoll-Kraay standard errors
   ```python
   model.fit(cov_type='driscoll-kraay')
   ```

2. **Alternative**: Spatial panel methods
3. **Consider**: Common factors or time effects

### Non-stationarity

If unit root tests indicate I(1) series:

1. **First choice**: Cointegration analysis (VECM)
2. **Alternative**: First-difference model
3. **Check**: Spurious regression possibility

## Automated Diagnostic Workflow

```python
def run_diagnostic_battery(model, data):
    """Run complete diagnostic battery with automatic corrections."""
    
    diagnostics = ThreeTierPanelDiagnostics(tier=1)
    report = diagnostics.run_diagnostics(model.results, data)
    
    # Check critical failures
    if report.has_critical_failures():
        # Apply corrections
        if 'serial_correlation' in report.failures:
            model.apply_robust_se('clustered')
        
        if 'cross_sectional_dependence' in report.failures:
            model.apply_robust_se('driscoll-kraay')
        
        # Re-run diagnostics
        report = diagnostics.run_diagnostics(model.results, data)
    
    return report
```

## Publication Checklist

Before submitting for publication:

- [ ] All critical tests passed or corrected
- [ ] Diagnostic results table included
- [ ] Corrections documented
- [ ] Robustness checks performed
- [ ] Code reproduces diagnostics

## Special Considerations for Conflict Data

### Additional Tests

1. **Structural Breaks**
   - Test: Chow test at conflict events
   - Action: Split sample or dummy variables

2. **Sample Selection**
   - Test: Heckman selection test
   - Action: Selection correction if needed

3. **Outliers**
   - Test: Conflict-aware outlier detection
   - Action: Robust estimation methods

### Conflict-Specific Corrections

- Use robust estimators for conflict-affected observations
- Consider trimming extreme conflict periods
- Test sensitivity to conflict measurement

## References

1. Wooldridge, J.M. (2002). *Econometric Analysis of Cross Section and Panel Data*. MIT Press.

2. Pesaran, M.H. (2004). "General Diagnostic Tests for Cross Section Dependence in Panels." Cambridge Working Papers in Economics 0435.

3. Im, K.S., Pesaran, M.H., & Shin, Y. (2003). "Testing for Unit Roots in Heterogeneous Panels." Journal of Econometrics, 115(1), 53-74.

4. Driscoll, J.C., & Kraay, A.C. (1998). "Consistent Covariance Matrix Estimation with Spatially Dependent Panel Data." Review of Economics and Statistics, 80(4), 549-560.

5. World Bank (2022). *Technical Standards for Economic Analysis*. World Bank Group.

## See Also

- [Diagnostic Testing Framework](./.claude/models/diagnostic_testing_framework.md)
- [Three-Tier Methodology](../../METHODOLOGY.md)
- [Panel Data Guide](./.claude/models/yemen-panel-guide.md)