# Data Pipeline Guide

## 📊 Overview

The Yemen Market Integration data pipeline transforms raw humanitarian data into analysis-ready datasets. This guide covers the complete pipeline from data collection to final panel creation.

## 🔄 Pipeline Architecture

```
Data Sources → Collection → Processing → Integration → Validation → Output
     ↓            ↓           ↓            ↓            ↓          ↓
   HDX/APIs    Download    Clean/Transform  Join      Quality    Panels
```

## 📥 Data Collection

### Step 1: Download Raw Data

```bash
# Download all required datasets
python scripts/data_collection/download_data.py

# Download conflict data separately (requires API key)
python scripts/data_collection/download_acled_data.py
```

### Data Sources

| Source | Type | Frequency | Content |
|--------|------|-----------|---------|
| WFP | Prices | Weekly | Commodity prices, exchange rates |
| ACAPS | Control zones | Bi-weekly | Territorial control |
| ACLED | Conflict | Daily | Conflict events |
| HDX | Admin boundaries | Static | Geographic data |

### Manual Downloads

Some ACAPS files require manual download:
1. Visit [ACAPS Yemen Analysis Hub](https://www.acaps.org/country/yemen)
2. Download control zone shapefiles
3. Place in `data/raw/acaps/`

## 🔧 Data Processing

### Step 2: Process Individual Sources

```bash
# Process WFP price data
python scripts/data_processing/process_wfp_data.py

# Process ACAPS control zones
python scripts/data_processing/process_acaps_data.py

# Process ACLED conflict data
python scripts/data_processing/process_acled_data.py
```

### WFP Processing Details

The WFP processor performs:

1. **Pcode Standardization**
   ```python
   # Maps market names to standard pcodes
   "Sana'a City" → "YE1301"  # Amanat Al Asimah
   ```

2. **Currency Conversion**
   ```python
   # Converts local prices to USD
   usd_price = local_price / exchange_rate
   ```

3. **Quality Validation**
   - Removes extreme outliers (>5 SD)
   - Flags suspicious price changes
   - Validates units consistency

**Coverage Achievement**: 88.4% (up from 62%)

### ACAPS Processing

Extracts territorial control from shapefiles:

1. **Temporal Tracking**
   - Bi-weekly snapshots
   - Control zone transitions
   - Territory size calculations

2. **Zone Standardization**
   ```python
   zones = ['IRG', 'STC', 'Defacto Authority', 'Contested']
   ```

### ACLED Processing

Aggregates conflict events by market:

1. **Event Aggregation**
   - Monthly event counts
   - Fatality statistics
   - Event type classification

2. **Spatial Matching**
   - 25km radius from markets
   - Distance-weighted intensity

## 🗺️ Spatial Integration

### Step 3: Run Spatial Joins

```bash
python scripts/data_processing/run_spatial_joins.py
```

This creates market-zone-time mappings:

```python
# Example output
{
    'market': 'Sana\'a',
    'date': '2024-01-15',
    'control_zone': 'Defacto Authority',
    'zone_duration_days': 730,
    'previous_zone': None
}
```

## 📊 Panel Construction

### Step 4: Create Analysis Panels

```bash
# Create integrated balanced panel
python scripts/analysis/create_integrated_balanced_panel.py

# Or create custom panel
python scripts/analysis/create_balanced_panel.py \
    --start-date 2023-01-01 \
    --end-date 2024-12-31 \
    --min-observations 100
```

### Panel Types

1. **Balanced Panel**
   - Complete observations only
   - No missing values
   - ~44K observations

2. **Smart Panel**
   - Only existing commodity-market pairs
   - Reduces false missings
   - Better for econometric analysis

3. **Integrated Panel**
   - Combines all data sources
   - Includes derived features
   - Ready for modeling

### Panel Structure

```python
# Final panel columns
[
    # Identifiers
    'market', 'commodity', 'date',
    
    # Price data
    'price_usd', 'price_local', 'exchange_rate',
    
    # Spatial data
    'control_zone', 'zone_duration', 'distance_to_border',
    
    # Conflict data
    'conflict_events', 'fatalities', 'conflict_intensity',
    
    # Features
    'price_lag_1', 'ma_7', 'seasonal_index'
]
```

## ✅ Data Validation

### Quality Checks

The pipeline performs automatic validation:

1. **Completeness**
   ```python
   assert panel.isnull().sum().sum() == 0  # No missing values
   ```

2. **Consistency**
   ```python
   # Price changes within reasonable bounds
   assert (panel['price_change'].abs() < 2).all()
   ```

3. **Coverage**
   ```python
   # Minimum observations per market
   assert (panel.groupby('market').size() >= 100).all()
   ```

### Validation Report

After processing:
```
Data Quality Report:
- Markets covered: 169/180 (93.9%)
- Commodities: 18/18 (100%)
- Time coverage: 2019-01-01 to 2024-12-31
- Missing data: 0%
- Outliers removed: 127 (0.3%)
```

## 🔄 Updating Data

### Regular Updates

```bash
# Weekly update script
./scripts/weekly_update.sh

# Or manually:
python scripts/data_collection/download_data.py --update
python scripts/data_processing/process_wfp_data.py --incremental
python scripts/analysis/create_integrated_balanced_panel.py --append
```

### Incremental Processing

Only process new data:
```python
# Check last processed date
last_date = pd.read_parquet('data/processed/metadata.parquet')['last_update']

# Process only new data
new_data = raw_data[raw_data['date'] > last_date]
```

## 🚀 Performance Tips

### Large Dataset Handling

```python
# Use chunking for memory efficiency
for chunk in pd.read_csv('large_file.csv', chunksize=10000):
    process_chunk(chunk)

# Use Parquet for faster I/O
df.to_parquet('output.parquet', compression='snappy')
```

### Parallel Processing

```python
# Process markets in parallel
from multiprocessing import Pool

with Pool(4) as p:
    results = p.map(process_market, markets)
```

## 🐛 Troubleshooting

### Common Issues

1. **Missing ACAPS Files**
   - Error: `FileNotFoundError: ACAPS shapefile not found`
   - Solution: Download manually from ACAPS website

2. **API Rate Limits**
   - Error: `HTTPError: 429 Too Many Requests`
   - Solution: Wait or reduce request frequency

3. **Memory Errors**
   - Error: `MemoryError during panel creation`
   - Solution: Use chunking or increase RAM

### Debug Mode

```bash
# Run with detailed logging
python scripts/data_processing/process_wfp_data.py --debug

# Check intermediate outputs
ls -la data/interim/
```

## 📁 Output Files

### Directory Structure

```
data/
├── raw/                    # Original downloads
├── interim/                # Processing stages
│   ├── cleaned/           # Basic cleaning
│   ├── standardized/      # Name standardization
│   └── merged/            # Combined datasets
├── processed/             # Final outputs
│   ├── panels/           # Analysis panels
│   ├── spatial/          # Geospatial data
│   └── metadata/         # Processing metadata
```

### Key Output Files

| File | Description | Format |
|------|-------------|--------|
| `integrated_panel.parquet` | Main analysis dataset | Parquet |
| `market_zones_temporal.csv` | Zone assignments | CSV |
| `conflict_metrics.parquet` | Conflict indicators | Parquet |
| `processing_log.json` | Processing metadata | JSON |

## 📚 Next Steps

- [Running Analyses](./running-analyses.md) - Use the processed data
- [Data Documentation](../data/) - Detailed specifications
- [API Reference](../03-api-reference/data/) - Programming interface

---

*Questions? See [FAQ](./faq.md) or check the [troubleshooting guide](./troubleshooting.md).*