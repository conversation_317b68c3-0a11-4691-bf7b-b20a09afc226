# Technical Methodology Documentation

**Target Audience**: Researchers, Econometricians, Data Scientists  
**Purpose**: Technical implementation details of econometric and statistical methods

## Overview

This section provides detailed technical documentation of the methodological approaches implemented in the Yemen Market Integration Platform. Unlike the high-level conceptual overview in the root `METHODOLOGY.md`, this documentation focuses on implementation details, mathematical specifications, and code examples.

## Directory Structure

### [econometric-models/](econometric-models/) - Econometric Model Implementations
- **[panel-models.md](econometric-models/panel-models.md)** - Panel data model specifications and implementation
- **[time-series.md](econometric-models/time-series.md)** - Time series analysis methods
- **[cointegration.md](econometric-models/cointegration.md)** - Cointegration testing procedures
- **[threshold-models.md](econometric-models/threshold-models.md)** - Threshold and regime-switching models

### [statistical-tests/](statistical-tests/) - Statistical Test Implementations
- **[diagnostic-tests.md](statistical-tests/diagnostic-tests.md)** - Panel data diagnostic tests
- **[unit-root-tests.md](statistical-tests/unit-root-tests.md)** - Stationarity testing procedures
- **[robustness-checks.md](statistical-tests/robustness-checks.md)** - Robustness validation methods

### [data-processing/](data-processing/) - Data Processing Methodology
- **[panel-construction.md](data-processing/panel-construction.md)** - Building balanced panels
- **[missing-data.md](data-processing/missing-data.md)** - Missing data imputation strategies
- **[outlier-detection.md](data-processing/outlier-detection.md)** - Outlier identification and treatment
- **[spatial-matching.md](data-processing/spatial-matching.md)** - Spatial data integration methods

### [validation/](validation/) - Model Validation Techniques
- **[cross-validation.md](validation/cross-validation.md)** - Time series cross-validation
- **[conflict-validation.md](validation/conflict-validation.md)** - External validation with conflict data
- **[factor-analysis.md](validation/factor-analysis.md)** - Factor models for validation

## Core Methodological Framework

### Three-Tier Approach

The Yemen Market Integration analysis employs a three-tier econometric framework:

1. **Tier 1: Pooled Panel Analysis**
   - Multi-way fixed effects regression
   - Driscoll-Kraay standard errors
   - Entity: market-commodity pairs

2. **Tier 2: Commodity-Specific Models**
   - Threshold Vector Error Correction Models (TVECM)
   - Regime-switching dynamics
   - Commodity-level heterogeneity

3. **Tier 3: External Validation**
   - Factor analysis for common shocks
   - Conflict event validation
   - Cross-validation procedures

### Key Innovations

1. **3D Panel Handling**: Novel approach to market × commodity × time data
2. **Conflict-Aware Modeling**: Integration of conflict intensity in price transmission
3. **Spatial Considerations**: Accounting for geographic spillovers
4. **Robust Inference**: Multiple layers of robustness checks

## Mathematical Foundations

### Panel Model Specification

The core specification for Tier 1:

$$P_{i,j,t} = \alpha + \theta_i + \phi_j + \tau_t + \delta \cdot Conflict_{i,t} + \beta' X_{i,j,t} + \varepsilon_{i,j,t}$$

Where:
- $P_{i,j,t}$: Log price in market $i$, commodity $j$, time $t$
- $\theta_i$: Market fixed effects
- $\phi_j$: Commodity fixed effects
- $\tau_t$: Time fixed effects
- $Conflict_{i,t}$: Conflict intensity measure
- $X_{i,j,t}$: Control variables

### Threshold Model Specification

For Tier 2 commodity-specific analysis:

$$\Delta p_{i,t} = \begin{cases}
\alpha^L + \beta^L ECT_{t-1} + \gamma^L \Delta p_{i,t-1} + \epsilon_t & \text{if } q_t \leq \tau \\
\alpha^H + \beta^H ECT_{t-1} + \gamma^H \Delta p_{i,t-1} + \epsilon_t & \text{if } q_t > \tau
\end{cases}$$

Where:
- $q_t$: Threshold variable (conflict intensity)
- $\tau$: Estimated threshold value
- $ECT_{t-1}$: Error correction term
- Superscripts $L$, $H$: Low and high regimes

## Implementation Architecture

```python
# Core implementation structure
from yemen_market.models.three_tier import (
    PooledPanelModel,      # Tier 1
    ThresholdVECM,         # Tier 2
    ConflictValidation     # Tier 3
)

# Data processing pipeline
from yemen_market.data import (
    PanelBuilder,          # Panel construction
    SpatialJoiner,         # Spatial matching
    OutlierDetector        # Outlier handling
)

# Statistical testing
from yemen_market.diagnostics import (
    PanelDiagnostics,      # Diagnostic tests
    UnitRootTests,         # Stationarity tests
    RobustnessChecker      # Robustness validation
)
```

## Usage Example

```python
# Complete methodological workflow
import pandas as pd
from yemen_market.models.three_tier import ThreeTierRunner

# Load data
data = pd.read_parquet('data/processed/panel.parquet')

# Configure analysis
config = {
    'tier1_config': {
        'fixed_effects': ['market_id', 'commodity', 'month'],
        'cluster_var': 'market_id',
        'cov_type': 'kernel'  # Driscoll-Kraay
    },
    'tier2_config': {
        'threshold_var': 'conflict_intensity',
        'n_regimes': 2,
        'test_cointegration': True
    },
    'tier3_config': {
        'external_data': 'conflict_events',
        'n_factors': 3
    }
}

# Run three-tier analysis
runner = ThreeTierRunner(config)
results = runner.run_all_tiers(data)

# Access results
print(f"Tier 1 - Conflict effect: {results.tier1.coefficients['conflict_intensity']:.3f}")
print(f"Tier 2 - Threshold value: {results.tier2['wheat'].threshold:.2f}")
print(f"Tier 3 - Validation R²: {results.tier3.external_r2:.3f}")
```

## Key Methodological Considerations

### 1. Panel Structure
- Unbalanced panels due to missing observations
- Multi-dimensional structure (market × commodity × time)
- Minimum coverage requirements for validity

### 2. Identification Strategy
- Fixed effects for unobserved heterogeneity
- Instrumental variables for endogeneity
- Threshold models for non-linearities

### 3. Inference
- Clustered standard errors
- Spatial correlation corrections
- Bootstrap for threshold inference

### 4. Validation
- Out-of-sample prediction
- External data validation
- Robustness to specification changes

## Quality Standards

All implementations follow:

1. **World Bank Standards**: Diagnostic tests and robustness checks
2. **Reproducibility**: Fixed random seeds and versioned dependencies
3. **Performance**: Optimized for large panel datasets
4. **Documentation**: Comprehensive docstrings and examples

## References

Key methodological references:

1. Wooldridge, J.M. (2010). *Econometric Analysis of Cross Section and Panel Data*
2. Hansen, B.E. (1999). "Threshold effects in non-dynamic panels"
3. Driscoll, J.C. & Kraay, A.C. (1998). "Consistent covariance matrix estimation"
4. Pesaran, M.H. (2007). "A simple panel unit root test"

## Navigation

- For conceptual overview: See root [`METHODOLOGY.md`](../../METHODOLOGY.md)
- For implementation: See [`API Reference`](../03-api-reference/models/)
- For usage: See [`User Guides`](../02-user-guides/running-analyses.md)