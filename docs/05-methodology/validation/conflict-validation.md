# Conflict Validation Methodology

**Target Audience**: Econometricians, Conflict Researchers  
**Module**: `yemen_market.models.three_tier.tier3_validation`

## Overview

This document details the Tier 3 validation methods that assess how conflict dynamics affect price transmission and market integration in Yemen. These validation techniques are crucial for understanding the econometric results in the context of ongoing conflict.

## Conflict Validation Framework

### Theoretical Foundation

Conflict affects markets through multiple channels:
- **Supply disruption**: Damaged infrastructure, unsafe transport routes
- **Demand shocks**: Population displacement, income loss
- **Transaction costs**: Security expenses, bribes, risk premiums
- **Information asymmetry**: Limited price discovery across conflict lines

### Core Validation Model

```python
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union
import statsmodels.api as sm
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import PolynomialFeatures

class ConflictValidation:
    """
    Tier 3 validation: Conflict effects on market integration.
    
    Tests whether price transmission varies with conflict intensity
    and validates the economic mechanisms.
    """
    
    def __init__(self, panel_data: pd.DataFrame):
        """
        Initialize conflict validation.
        
        Parameters
        ----------
        panel_data : DataFrame
            Panel data with prices and conflict measures
        """
        self.data = panel_data.copy()
        self.results = {}
        self._prepare_conflict_variables()
        
    def _prepare_conflict_variables(self):
        """Prepare conflict intensity measures."""
        # Log transformation for count data
        self.data['log_conflict'] = np.log1p(self.data['conflict_events'])
        
        # Conflict stock (accumulated with depreciation)
        self.data['conflict_stock'] = self._calculate_conflict_stock(
            self.data, 
            depreciation_rate=0.1
        )
        
        # Binary high/low conflict
        threshold = self.data['conflict_intensity'].quantile(0.75)
        self.data['high_conflict'] = (
            self.data['conflict_intensity'] > threshold
        ).astype(int)
        
        # Conflict zones
        self.data['conflict_zone'] = self._identify_conflict_zones()
        
    def validate_price_transmission(
        self,
        source_market: str = 'ADEN',  # Main port
        target_markets: List[str] = None,
        commodity: str = 'wheat'
    ) -> Dict[str, any]:
        """
        Validate how conflict affects price transmission.
        
        Tests whether prices in conflict-affected markets respond
        differently to price shocks in source markets.
        """
        if target_markets is None:
            target_markets = self.data['market_id'].unique()
            target_markets = [m for m in target_markets if m != source_market]
        
        results = {}
        
        # Get source market prices
        source_data = self.data[
            (self.data['market_id'] == source_market) &
            (self.data['commodity'] == commodity)
        ].set_index('date')['log_price']
        
        for target in target_markets:
            # Get target market data
            target_data = self.data[
                (self.data['market_id'] == target) &
                (self.data['commodity'] == commodity)
            ].set_index('date')
            
            if len(target_data) < 24:
                continue
            
            # Estimate transmission with conflict interaction
            transmission_result = self._estimate_conflict_transmission(
                source_prices=source_data,
                target_data=target_data
            )
            
            results[target] = transmission_result
        
        # Aggregate results
        summary = self._summarize_transmission_results(results)
        
        return {
            'market_results': results,
            'summary': summary,
            'visualization': self._plot_transmission_heterogeneity(results)
        }
    
    def _estimate_conflict_transmission(
        self,
        source_prices: pd.Series,
        target_data: pd.DataFrame
    ) -> Dict[str, any]:
        """
        Estimate price transmission with conflict interactions.
        
        Model:
        Δp_target = α + β₁Δp_source + β₂Conflict + β₃(Δp_source × Conflict) + ε
        """
        # Align data
        merged = pd.DataFrame({
            'target_price': target_data['log_price'],
            'conflict': target_data['conflict_intensity'],
            'high_conflict': target_data['high_conflict']
        })
        
        merged['source_price'] = source_prices
        merged = merged.dropna()
        
        # First differences
        merged['d_target'] = merged['target_price'].diff()
        merged['d_source'] = merged['source_price'].diff()
        
        # Interaction term
        merged['d_source_x_conflict'] = (
            merged['d_source'] * merged['conflict']
        )
        
        # Remove missing
        estimation_data = merged.dropna()
        
        if len(estimation_data) < 20:
            return {'error': 'Insufficient data'}
        
        # Estimate model
        X = sm.add_constant(estimation_data[[
            'd_source', 'conflict', 'd_source_x_conflict'
        ]])
        y = estimation_data['d_target']
        
        model = sm.OLS(y, X)
        results = model.fit(cov_type='HAC', cov_kwds={'maxlags': 4})
        
        # Calculate marginal effects
        marginal_effects = self._calculate_marginal_effects(
            results,
            estimation_data['conflict']
        )
        
        # Test for threshold effects
        threshold_test = self._test_threshold_effects(
            estimation_data
        )
        
        return {
            'regression_results': results,
            'base_transmission': results.params['d_source'],
            'conflict_effect': results.params['d_source_x_conflict'],
            'marginal_effects': marginal_effects,
            'threshold_test': threshold_test,
            'n_obs': len(estimation_data),
            'r_squared': results.rsquared
        }
    
    def _calculate_marginal_effects(
        self,
        results,
        conflict_values: pd.Series
    ) -> Dict[str, float]:
        """Calculate price transmission at different conflict levels."""
        # Marginal effect: ∂(Δp_target)/∂(Δp_source) = β₁ + β₃ × Conflict
        
        beta_source = results.params['d_source']
        beta_interaction = results.params['d_source_x_conflict']
        
        # Calculate at different percentiles
        percentiles = [0, 25, 50, 75, 90, 95, 99]
        marginal_effects = {}
        
        for p in percentiles:
            conflict_level = conflict_values.quantile(p / 100)
            effect = beta_source + beta_interaction * conflict_level
            
            # Standard error using delta method
            var_beta1 = results.cov_params().loc['d_source', 'd_source']
            var_beta3 = results.cov_params().loc['d_source_x_conflict', 'd_source_x_conflict']
            cov_beta13 = results.cov_params().loc['d_source', 'd_source_x_conflict']
            
            se = np.sqrt(
                var_beta1 + 
                conflict_level**2 * var_beta3 + 
                2 * conflict_level * cov_beta13
            )
            
            marginal_effects[f'p{p}'] = {
                'conflict_level': conflict_level,
                'transmission': effect,
                'std_error': se,
                't_stat': effect / se,
                'significant': abs(effect / se) > 1.96
            }
        
        return marginal_effects
    
    def _test_threshold_effects(
        self,
        data: pd.DataFrame
    ) -> Dict[str, any]:
        """Test for threshold effects in conflict-transmission relationship."""
        # Hansen (1999) threshold test
        conflict_values = np.sort(data['conflict'].unique())
        
        if len(conflict_values) < 10:
            return {'error': 'Insufficient variation in conflict'}
        
        # Test each potential threshold
        threshold_results = []
        
        for threshold in conflict_values[5:-5]:  # Trim extremes
            # Split sample
            low_conflict = data[data['conflict'] <= threshold]
            high_conflict = data[data['conflict'] > threshold]
            
            if len(low_conflict) < 10 or len(high_conflict) < 10:
                continue
            
            # Estimate separate models
            try:
                # Low conflict regime
                X_low = sm.add_constant(low_conflict['d_source'])
                y_low = low_conflict['d_target']
                model_low = sm.OLS(y_low, X_low).fit()
                
                # High conflict regime
                X_high = sm.add_constant(high_conflict['d_source'])
                y_high = high_conflict['d_target']
                model_high = sm.OLS(y_high, X_high).fit()
                
                # Combined RSS
                rss_split = model_low.ssr + model_high.ssr
                
                # Full sample model
                X_full = sm.add_constant(data['d_source'])
                y_full = data['d_target']
                model_full = sm.OLS(y_full, X_full).fit()
                rss_full = model_full.ssr
                
                # LR statistic
                lr_stat = len(data) * np.log(rss_full / rss_split)
                
                threshold_results.append({
                    'threshold': threshold,
                    'lr_statistic': lr_stat,
                    'low_transmission': model_low.params['d_source'],
                    'high_transmission': model_high.params['d_source'],
                    'n_low': len(low_conflict),
                    'n_high': len(high_conflict)
                })
                
            except:
                continue
        
        if not threshold_results:
            return {'error': 'Threshold estimation failed'}
        
        # Find optimal threshold
        threshold_df = pd.DataFrame(threshold_results)
        optimal_idx = threshold_df['lr_statistic'].idxmax()
        optimal_threshold = threshold_df.iloc[optimal_idx]
        
        # Bootstrap p-value (simplified)
        p_value = self._bootstrap_threshold_test(
            data,
            optimal_threshold['threshold'],
            n_bootstrap=100
        )
        
        return {
            'optimal_threshold': optimal_threshold['threshold'],
            'lr_statistic': optimal_threshold['lr_statistic'],
            'p_value': p_value,
            'low_regime_transmission': optimal_threshold['low_transmission'],
            'high_regime_transmission': optimal_threshold['high_transmission'],
            'transmission_reduction': (
                1 - optimal_threshold['high_transmission'] / 
                optimal_threshold['low_transmission']
            ) * 100
        }
    
    def test_conflict_mechanisms(
        self,
        commodity: str = 'wheat'
    ) -> Dict[str, any]:
        """
        Test specific mechanisms through which conflict affects prices.
        """
        results = {}
        
        # 1. Supply chain disruption
        supply_test = self._test_supply_disruption(commodity)
        results['supply_disruption'] = supply_test
        
        # 2. Market segmentation
        segmentation_test = self._test_market_segmentation(commodity)
        results['market_segmentation'] = segmentation_test
        
        # 3. Risk premium
        risk_test = self._test_risk_premium(commodity)
        results['risk_premium'] = risk_test
        
        # 4. Information flow
        info_test = self._test_information_flow(commodity)
        results['information_flow'] = info_test
        
        return results
    
    def _test_supply_disruption(
        self,
        commodity: str
    ) -> Dict[str, any]:
        """
        Test if conflict disrupts supply chains.
        
        Hypothesis: Conflict increases price volatility and persistence.
        """
        commodity_data = self.data[self.data['commodity'] == commodity].copy()
        
        # Calculate rolling volatility
        commodity_data['price_volatility'] = commodity_data.groupby('market_id')['log_price'].transform(
            lambda x: x.rolling(window=6, min_periods=3).std()
        )
        
        # Model volatility as function of conflict
        volatility_model = sm.OLS(
            commodity_data['price_volatility'],
            sm.add_constant(commodity_data[[
                'conflict_intensity',
                'log_conflict',
                'urban',
                'border_market'
            ]])
        )
        
        volatility_results = volatility_model.fit(
            cov_type='cluster',
            cov_kwds={'groups': commodity_data['market_id']}
        )
        
        # Price persistence test
        persistence_results = []
        
        for market in commodity_data['market_id'].unique():
            market_data = commodity_data[
                commodity_data['market_id'] == market
            ].sort_values('date')
            
            if len(market_data) < 24:
                continue
            
            # AR(1) model with conflict interaction
            market_data['price_lag'] = market_data['log_price'].shift(1)
            market_data['price_lag_x_conflict'] = (
                market_data['price_lag'] * market_data['conflict_intensity']
            )
            
            ar_data = market_data.dropna()
            
            if len(ar_data) > 20:
                ar_model = sm.OLS(
                    ar_data['log_price'],
                    sm.add_constant(ar_data[[
                        'price_lag',
                        'conflict_intensity',
                        'price_lag_x_conflict'
                    ]])
                )
                
                ar_results = ar_model.fit()
                
                persistence_results.append({
                    'market': market,
                    'base_persistence': ar_results.params['price_lag'],
                    'conflict_effect': ar_results.params['price_lag_x_conflict'],
                    'avg_conflict': market_data['conflict_intensity'].mean()
                })
        
        persistence_df = pd.DataFrame(persistence_results)
        
        return {
            'volatility_model': volatility_results,
            'conflict_increases_volatility': volatility_results.params['conflict_intensity'] > 0 and 
                                           volatility_results.pvalues['conflict_intensity'] < 0.05,
            'persistence_results': persistence_df,
            'avg_persistence_increase': persistence_df['conflict_effect'].mean()
        }
    
    def _test_market_segmentation(
        self,
        commodity: str
    ) -> Dict[str, any]:
        """
        Test if conflict segments markets.
        
        Method: Examine price correlation between market pairs.
        """
        commodity_data = self.data[self.data['commodity'] == commodity]
        
        # Create price matrix
        price_matrix = commodity_data.pivot(
            index='date',
            columns='market_id',
            values='log_price'
        )
        
        # Calculate pairwise correlations
        market_pairs = []
        
        markets = price_matrix.columns
        for i, market1 in enumerate(markets):
            for market2 in markets[i+1:]:
                # Get conflict intensity for both markets
                conflict1 = commodity_data[
                    commodity_data['market_id'] == market1
                ]['conflict_intensity'].mean()
                
                conflict2 = commodity_data[
                    commodity_data['market_id'] == market2
                ]['conflict_intensity'].mean()
                
                # Calculate correlation
                valid_dates = price_matrix[[market1, market2]].dropna().index
                
                if len(valid_dates) > 24:
                    corr = price_matrix.loc[valid_dates, market1].corr(
                        price_matrix.loc[valid_dates, market2]
                    )
                    
                    # Check if markets are in same control area
                    control1 = self.data[
                        self.data['market_id'] == market1
                    ]['control_area'].iloc[0]
                    
                    control2 = self.data[
                        self.data['market_id'] == market2
                    ]['control_area'].iloc[0]
                    
                    same_control = control1 == control2
                    
                    market_pairs.append({
                        'market1': market1,
                        'market2': market2,
                        'correlation': corr,
                        'avg_conflict': (conflict1 + conflict2) / 2,
                        'conflict_diff': abs(conflict1 - conflict2),
                        'same_control': same_control,
                        'n_obs': len(valid_dates)
                    })
        
        pairs_df = pd.DataFrame(market_pairs)
        
        # Model correlation as function of conflict
        correlation_model = sm.OLS(
            pairs_df['correlation'],
            sm.add_constant(pairs_df[[
                'avg_conflict',
                'conflict_diff',
                'same_control'
            ]])
        )
        
        correlation_results = correlation_model.fit()
        
        # Calculate segmentation index
        high_conflict_pairs = pairs_df[pairs_df['avg_conflict'] > pairs_df['avg_conflict'].median()]
        low_conflict_pairs = pairs_df[pairs_df['avg_conflict'] <= pairs_df['avg_conflict'].median()]
        
        segmentation_index = (
            1 - high_conflict_pairs['correlation'].mean() / 
            low_conflict_pairs['correlation'].mean()
        )
        
        return {
            'correlation_model': correlation_results,
            'segmentation_index': segmentation_index,
            'conflict_reduces_correlation': correlation_results.params['avg_conflict'] < 0,
            'cross_line_correlation': pairs_df[~pairs_df['same_control']]['correlation'].mean(),
            'within_area_correlation': pairs_df[pairs_df['same_control']]['correlation'].mean()
        }
    
    def _test_risk_premium(
        self,
        commodity: str
    ) -> Dict[str, any]:
        """
        Test if conflict creates risk premiums in prices.
        
        Method: Decompose price into expected and risk components.
        """
        commodity_data = self.data[self.data['commodity'] == commodity].copy()
        
        # Calculate expected prices using neighboring peaceful markets
        expected_prices = []
        
        for idx, row in commodity_data.iterrows():
            market = row['market_id']
            date = row['date']
            
            # Find peaceful neighbors (low conflict)
            peaceful_markets = commodity_data[
                (commodity_data['date'] == date) &
                (commodity_data['market_id'] != market) &
                (commodity_data['conflict_intensity'] < 
                 commodity_data['conflict_intensity'].quantile(0.25))
            ]
            
            if len(peaceful_markets) >= 3:
                expected_price = peaceful_markets['log_price'].mean()
            else:
                expected_price = np.nan
            
            expected_prices.append(expected_price)
        
        commodity_data['expected_price'] = expected_prices
        commodity_data['price_premium'] = (
            commodity_data['log_price'] - commodity_data['expected_price']
        )
        
        # Model premium as function of conflict
        premium_data = commodity_data.dropna(subset=['price_premium'])
        
        if len(premium_data) > 50:
            # Non-linear specification
            poly = PolynomialFeatures(degree=2, include_bias=False)
            conflict_poly = poly.fit_transform(
                premium_data[['conflict_intensity']]
            )
            
            X = sm.add_constant(np.column_stack([
                conflict_poly,
                premium_data[['urban', 'border_market']].values
            ]))
            
            premium_model = sm.OLS(premium_data['price_premium'], X)
            premium_results = premium_model.fit(
                cov_type='cluster',
                cov_kwds={'groups': premium_data['market_id']}
            )
            
            # Calculate average risk premium at different conflict levels
            conflict_levels = np.linspace(
                premium_data['conflict_intensity'].min(),
                premium_data['conflict_intensity'].max(),
                100
            )
            
            risk_premiums = []
            for c in conflict_levels:
                c_poly = poly.transform([[c]])
                X_pred = [1] + list(c_poly[0]) + [0, 0]  # Set urban, border to 0
                premium = premium_results.predict([X_pred])[0]
                risk_premiums.append(premium)
            
            return {
                'premium_model': premium_results,
                'has_risk_premium': any(p > 0 for p in premium_results.params[1:3]),
                'max_risk_premium': max(risk_premiums),
                'conflict_level_for_max': conflict_levels[np.argmax(risk_premiums)],
                'premium_curve': {
                    'conflict_levels': conflict_levels.tolist(),
                    'risk_premiums': risk_premiums
                }
            }
        else:
            return {'error': 'Insufficient data for risk premium analysis'}
```

### Conflict Spillover Effects

```python
class ConflictSpilloverAnalysis:
    """Analyze spatial spillovers of conflict on prices."""
    
    def __init__(self, panel_data: pd.DataFrame, spatial_weights: np.ndarray):
        """
        Initialize spillover analysis.
        
        Parameters
        ----------
        panel_data : DataFrame
            Panel data
        spatial_weights : array
            Spatial weights matrix
        """
        self.data = panel_data
        self.W = spatial_weights
        
    def test_spatial_spillovers(self) -> Dict[str, any]:
        """
        Test for spatial spillovers of conflict on prices.
        
        Model: p_it = ρW·p_t + βX_it + γW·Conflict_t + ε_it
        """
        from linearmodels.panel import PanelOLS
        import spreg
        
        # Create spatial lags
        self.data['spatial_lag_price'] = self._create_spatial_lag(
            self.data, 'log_price'
        )
        
        self.data['spatial_lag_conflict'] = self._create_spatial_lag(
            self.data, 'conflict_intensity'
        )
        
        # Spatial Durbin model
        model_data = self.data.dropna(subset=[
            'log_price', 'spatial_lag_price', 'spatial_lag_conflict'
        ])
        
        # Set up panel
        model_data = model_data.set_index(['entity', 'date'])
        
        # Estimate model
        exog_vars = [
            'conflict_intensity',
            'spatial_lag_conflict',
            'log_global_price',
            'urban'
        ]
        
        spatial_model = PanelOLS(
            dependent=model_data['log_price'],
            exog=model_data[exog_vars],
            entity_effects=True,
            time_effects=True
        )
        
        results = spatial_model.fit(
            cov_type='clustered',
            cluster_entity=True
        )
        
        # Direct and indirect effects
        direct_effect = results.params['conflict_intensity']
        indirect_effect = results.params['spatial_lag_conflict']
        
        # Moran's I test for residuals
        residuals = results.resids
        morans_i = self._calculate_morans_i(residuals)
        
        return {
            'model_results': results,
            'direct_effect': direct_effect,
            'indirect_effect': indirect_effect,
            'total_effect': direct_effect + indirect_effect,
            'spillover_ratio': indirect_effect / (direct_effect + indirect_effect),
            'morans_i': morans_i,
            'spatial_dependence': morans_i['p_value'] < 0.05
        }
    
    def _create_spatial_lag(self, data: pd.DataFrame, variable: str) -> pd.Series:
        """Create spatial lag of variable."""
        # This is simplified - actual implementation needs proper alignment
        spatial_lag = []
        
        for date in data['date'].unique():
            date_data = data[data['date'] == date]
            
            # Create vector of values
            values = date_data.set_index('market_id')[variable].reindex(
                self.market_order
            ).fillna(0).values
            
            # Calculate spatial lag
            lag_values = self.W @ values
            
            # Map back to data
            lag_dict = dict(zip(self.market_order, lag_values))
            date_lags = date_data['market_id'].map(lag_dict)
            
            spatial_lag.extend(date_lags)
        
        return pd.Series(spatial_lag, index=data.index)
```

### Validation Visualizations

```python
class ConflictValidationVisualizer:
    """Visualization tools for conflict validation results."""
    
    def __init__(self):
        """Initialize visualizer."""
        self.fig_size = (15, 10)
        
    def plot_transmission_heterogeneity(
        self,
        validation_results: Dict[str, any],
        save_path: str = None
    ) -> plt.Figure:
        """Plot how price transmission varies with conflict."""
        fig = plt.figure(figsize=self.fig_size)
        gs = fig.add_gridspec(2, 2, hspace=0.3, wspace=0.3)
        
        # 1. Marginal effects plot
        ax1 = fig.add_subplot(gs[0, 0])
        self._plot_marginal_effects(
            validation_results['summary']['marginal_effects'],
            ax1
        )
        
        # 2. Market-specific transmission
        ax2 = fig.add_subplot(gs[0, 1])
        self._plot_market_transmission(
            validation_results['market_results'],
            ax2
        )
        
        # 3. Threshold effects
        ax3 = fig.add_subplot(gs[1, 0])
        self._plot_threshold_effects(
            validation_results['summary']['threshold_results'],
            ax3
        )
        
        # 4. Mechanism decomposition
        ax4 = fig.add_subplot(gs[1, 1])
        self._plot_mechanism_decomposition(
            validation_results['mechanisms'],
            ax4
        )
        
        plt.suptitle('Conflict Effects on Price Transmission', fontsize=16)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def _plot_marginal_effects(self, marginal_effects: Dict, ax: plt.Axes):
        """Plot marginal effects of price transmission."""
        # Extract data
        conflict_levels = []
        transmissions = []
        lower_bounds = []
        upper_bounds = []
        
        for percentile, effects in marginal_effects.items():
            conflict_levels.append(effects['conflict_level'])
            transmissions.append(effects['transmission'])
            
            # 95% confidence interval
            se = effects['std_error']
            lower_bounds.append(effects['transmission'] - 1.96 * se)
            upper_bounds.append(effects['transmission'] + 1.96 * se)
        
        # Plot
        ax.plot(conflict_levels, transmissions, 'b-', linewidth=2, label='Transmission')
        ax.fill_between(
            conflict_levels,
            lower_bounds,
            upper_bounds,
            alpha=0.3,
            label='95% CI'
        )
        
        # Add horizontal line at zero
        ax.axhline(y=0, color='r', linestyle='--', alpha=0.5)
        
        # Add vertical lines at key percentiles
        for p in [25, 50, 75]:
            idx = int(p / 100 * len(conflict_levels))
            ax.axvline(
                x=conflict_levels[idx],
                color='gray',
                linestyle=':',
                alpha=0.5
            )
            ax.text(
                conflict_levels[idx],
                ax.get_ylim()[1] * 0.95,
                f'P{p}',
                ha='center'
            )
        
        ax.set_xlabel('Conflict Intensity')
        ax.set_ylabel('Price Transmission Elasticity')
        ax.set_title('Marginal Effect of Price Transmission')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_market_transmission(self, market_results: Dict, ax: plt.Axes):
        """Plot transmission coefficients by market."""
        # Extract market-level results
        markets = []
        base_transmission = []
        conflict_effect = []
        avg_conflict = []
        
        for market, results in market_results.items():
            if 'error' not in results:
                markets.append(market)
                base_transmission.append(results['base_transmission'])
                conflict_effect.append(results['conflict_effect'])
                avg_conflict.append(
                    results['regression_results'].model.exog[:, 2].mean()
                )
        
        # Create scatter plot
        scatter = ax.scatter(
            avg_conflict,
            base_transmission,
            c=conflict_effect,
            s=100,
            cmap='RdBu_r',
            alpha=0.7,
            edgecolors='black',
            linewidth=1
        )
        
        # Add colorbar
        cbar = plt.colorbar(scatter, ax=ax)
        cbar.set_label('Conflict Interaction Effect')
        
        # Add labels for key markets
        for i, market in enumerate(markets):
            if market in ['SANA', 'ADEN', 'TAIZ', 'HODEIDAH']:
                ax.annotate(
                    market,
                    (avg_conflict[i], base_transmission[i]),
                    xytext=(5, 5),
                    textcoords='offset points',
                    fontsize=8
                )
        
        # Add trend line
        z = np.polyfit(avg_conflict, base_transmission, 1)
        p = np.poly1d(z)
        x_trend = np.linspace(min(avg_conflict), max(avg_conflict), 100)
        ax.plot(x_trend, p(x_trend), 'r--', alpha=0.5, label='Trend')
        
        ax.set_xlabel('Average Conflict Intensity')
        ax.set_ylabel('Base Price Transmission')
        ax.set_title('Price Transmission by Market')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def plot_conflict_mechanisms(
        self,
        mechanism_results: Dict[str, any],
        save_path: str = None
    ) -> plt.Figure:
        """Visualize different conflict mechanisms."""
        fig, axes = plt.subplots(2, 2, figsize=self.fig_size)
        axes = axes.flatten()
        
        # 1. Supply disruption
        self._plot_supply_disruption(
            mechanism_results['supply_disruption'],
            axes[0]
        )
        
        # 2. Market segmentation
        self._plot_market_segmentation(
            mechanism_results['market_segmentation'],
            axes[1]
        )
        
        # 3. Risk premium
        self._plot_risk_premium(
            mechanism_results['risk_premium'],
            axes[2]
        )
        
        # 4. Summary
        self._plot_mechanism_summary(
            mechanism_results,
            axes[3]
        )
        
        plt.suptitle('Conflict Mechanisms Analysis', fontsize=16)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
```

### Dynamic Conflict Effects

```python
class DynamicConflictAnalysis:
    """Analyze dynamic effects of conflict on markets."""
    
    def __init__(self, panel_data: pd.DataFrame):
        """Initialize dynamic analysis."""
        self.data = panel_data
        
    def estimate_impulse_responses(
        self,
        commodity: str = 'wheat',
        shock_size: float = 1.0,
        horizon: int = 12
    ) -> Dict[str, any]:
        """
        Estimate impulse responses of prices to conflict shocks.
        
        Uses local projections method (Jordà, 2005).
        """
        commodity_data = self.data[
            self.data['commodity'] == commodity
        ].copy()
        
        # Prepare data
        commodity_data = commodity_data.sort_values(['market_id', 'date'])
        
        # Calculate shocks
        commodity_data['conflict_shock'] = commodity_data.groupby('market_id')['conflict_intensity'].transform(
            lambda x: x - x.rolling(window=12, min_periods=6).mean()
        )
        
        # Estimate impulse responses
        impulse_responses = {}
        
        for h in range(horizon + 1):
            # Create h-period ahead outcome
            commodity_data[f'price_h{h}'] = commodity_data.groupby('market_id')['log_price'].shift(-h)
            
            # Local projection regression
            regression_data = commodity_data.dropna(subset=[f'price_h{h}', 'conflict_shock'])
            
            if len(regression_data) > 100:
                # Include controls and fixed effects
                X = regression_data[[
                    'conflict_shock',
                    'log_price',
                    'log_global_price',
                    'urban'
                ]]
                
                # Add market and time fixed effects
                X = pd.get_dummies(
                    X,
                    columns=['market_id', 'year_month'],
                    drop_first=True
                )
                
                y = regression_data[f'price_h{h}']
                
                model = sm.OLS(y, sm.add_constant(X))
                results = model.fit(
                    cov_type='HAC',
                    cov_kwds={'maxlags': 4}
                )
                
                impulse_responses[h] = {
                    'coefficient': results.params['conflict_shock'],
                    'std_error': results.std_errors['conflict_shock'],
                    't_stat': results.tvalues['conflict_shock'],
                    'conf_low': results.conf_int().loc['conflict_shock', 0],
                    'conf_high': results.conf_int().loc['conflict_shock', 1]
                }
        
        # Calculate cumulative effects
        cumulative_effect = sum(
            ir['coefficient'] for ir in impulse_responses.values()
        )
        
        # Test persistence
        half_life = self._calculate_half_life(impulse_responses)
        
        return {
            'impulse_responses': impulse_responses,
            'cumulative_effect': cumulative_effect,
            'half_life': half_life,
            'peak_response': max(
                impulse_responses.items(),
                key=lambda x: abs(x[1]['coefficient'])
            ),
            'significant_periods': sum(
                1 for ir in impulse_responses.values()
                if abs(ir['t_stat']) > 1.96
            )
        }
    
    def test_state_dependence(
        self,
        commodity: str = 'wheat'
    ) -> Dict[str, any]:
        """
        Test if conflict effects depend on initial conflict state.
        
        Estimates separate impulse responses for high/low conflict states.
        """
        results = {}
        
        # Define high/low conflict states
        conflict_threshold = self.data['conflict_intensity'].median()
        
        for state in ['low', 'high']:
            if state == 'low':
                state_data = self.data[
                    self.data['conflict_intensity'] <= conflict_threshold
                ]
            else:
                state_data = self.data[
                    self.data['conflict_intensity'] > conflict_threshold
                ]
            
            # Estimate impulse responses for this state
            state_ir = self.estimate_impulse_responses(
                commodity=commodity,
                data_subset=state_data
            )
            
            results[f'{state}_conflict'] = state_ir
        
        # Test for asymmetry
        asymmetry_test = self._test_asymmetry(
            results['low_conflict']['impulse_responses'],
            results['high_conflict']['impulse_responses']
        )
        
        results['asymmetry_test'] = asymmetry_test
        
        return results
```

## See Also

- [Cross-Validation](cross-validation.md) - Model validation techniques
- [Factor Analysis](factor-analysis.md) - Dimensionality reduction
- [Tier 3 Models](../../03-api-reference/models/three_tier/tier3_validation) - Implementation details
- [Conflict Data Processing](../data-processing/spatial-matching.md) - Spatial conflict data