# Threshold Model Specifications

**Target Audience**: Econometricians, Researchers  
**Module**: `yemen_market.models.three_tier.tier2_commodity`

## Overview

This document details the implementation of threshold models used in Tier 2 of the Yemen Market Integration analysis. These models capture regime-switching behavior in price transmission when conflict intensity crosses critical thresholds.

## Theoretical Framework

### Threshold Autoregressive (TAR) Models

The basic TAR model allows parameters to switch based on a threshold variable:

$$y_t = \begin{cases}
\phi_0^{(1)} + \phi_1^{(1)} y_{t-1} + \epsilon_t^{(1)} & \text{if } q_t \leq \tau \\
\phi_0^{(2)} + \phi_1^{(2)} y_{t-1} + \epsilon_t^{(2)} & \text{if } q_t > \tau
\end{cases}$$

Where:
- $q_t$: Threshold variable (e.g., conflict intensity)
- $\tau$: Threshold value
- Superscripts indicate regime-specific parameters

### Threshold Vector Error Correction Model (TVECM)

For cointegrated price series with regime-switching adjustment:

$$\Delta p_t = \begin{cases}
\alpha^L ECT_{t-1} + \Gamma^L(L)\Delta p_{t-1} + \epsilon_t & \text{if } q_t \leq \tau \\
\alpha^H ECT_{t-1} + \Gamma^H(L)\Delta p_{t-1} + \epsilon_t & \text{if } q_t > \tau
\end{cases}$$

## Implementation

### Basic Threshold Model

```python
import numpy as np
import pandas as pd
from scipy import optimize
from statsmodels.regression.linear_model import OLS
import statsmodels.api as sm

class ThresholdAutoregressiveModel:
    """
    Threshold Autoregressive (TAR) model implementation.
    
    Follows Hansen (1999) for threshold estimation and inference.
    """
    
    def __init__(
        self,
        n_regimes: int = 2,
        delay: int = 1,
        trim_pct: float = 0.15
    ):
        """
        Initialize TAR model.
        
        Parameters
        ----------
        n_regimes : int
            Number of regimes (currently supports 2)
        delay : int
            Delay parameter for threshold variable
        trim_pct : float
            Trimming percentage for threshold search
        """
        self.n_regimes = n_regimes
        self.delay = delay
        self.trim_pct = trim_pct
        self.threshold = None
        self.regime_models = {}
        
    def fit(
        self,
        y: np.ndarray,
        threshold_var: np.ndarray,
        exog: np.ndarray = None,
        method: str = 'grid_search'
    ):
        """
        Estimate TAR model.
        
        Parameters
        ----------
        y : array-like
            Dependent variable
        threshold_var : array-like
            Variable determining regime switches
        exog : array-like, optional
            Exogenous variables
        method : str
            'grid_search' or 'sequential'
        """
        # Apply delay to threshold variable
        if self.delay > 0:
            threshold_var_delayed = np.concatenate([
                [np.nan] * self.delay,
                threshold_var[:-self.delay]
            ])
        else:
            threshold_var_delayed = threshold_var
        
        # Remove NaN observations
        valid_idx = ~np.isnan(threshold_var_delayed)
        y_clean = y[valid_idx]
        threshold_clean = threshold_var_delayed[valid_idx]
        
        if exog is not None:
            exog_clean = exog[valid_idx]
        else:
            exog_clean = None
        
        # Find optimal threshold
        if method == 'grid_search':
            self.threshold = self._grid_search_threshold(
                y_clean, threshold_clean, exog_clean
            )
        else:
            self.threshold = self._sequential_threshold(
                y_clean, threshold_clean, exog_clean
            )
        
        # Estimate regime-specific models
        self._estimate_regime_models(y_clean, threshold_clean, exog_clean)
        
        # Calculate diagnostics
        self.diagnostics = self._calculate_diagnostics()
        
        return self
    
    def _grid_search_threshold(
        self,
        y: np.ndarray,
        threshold_var: np.ndarray,
        exog: np.ndarray
    ) -> float:
        """
        Find threshold using grid search minimizing SSR.
        """
        # Define search range
        sorted_threshold = np.sort(threshold_var)
        n = len(sorted_threshold)
        trim_n = int(n * self.trim_pct)
        
        search_range = sorted_threshold[trim_n:-trim_n]
        
        # Grid search
        ssr_values = []
        
        for tau in search_range:
            # Split sample
            regime1_idx = threshold_var <= tau
            regime2_idx = threshold_var > tau
            
            # Check minimum observations
            if np.sum(regime1_idx) < 20 or np.sum(regime2_idx) < 20:
                ssr_values.append(np.inf)
                continue
            
            # Estimate models and calculate SSR
            ssr = 0
            
            # Regime 1
            y1 = y[regime1_idx]
            if exog is not None:
                X1 = sm.add_constant(exog[regime1_idx])
            else:
                X1 = np.ones((len(y1), 1))
            
            model1 = OLS(y1, X1).fit()
            ssr += model1.ssr
            
            # Regime 2
            y2 = y[regime2_idx]
            if exog is not None:
                X2 = sm.add_constant(exog[regime2_idx])
            else:
                X2 = np.ones((len(y2), 1))
            
            model2 = OLS(y2, X2).fit()
            ssr += model2.ssr
            
            ssr_values.append(ssr)
        
        # Find minimum SSR
        optimal_idx = np.argmin(ssr_values)
        optimal_threshold = search_range[optimal_idx]
        
        return optimal_threshold
    
    def _estimate_regime_models(
        self,
        y: np.ndarray,
        threshold_var: np.ndarray,
        exog: np.ndarray
    ):
        """Estimate separate models for each regime."""
        # Split by regime
        regime1_idx = threshold_var <= self.threshold
        regime2_idx = threshold_var > self.threshold
        
        # Regime 1 (Low conflict)
        y1 = y[regime1_idx]
        if exog is not None:
            X1 = sm.add_constant(exog[regime1_idx])
        else:
            X1 = np.ones((len(y1), 1))
        
        self.regime_models[1] = OLS(y1, X1).fit()
        
        # Regime 2 (High conflict)
        y2 = y[regime2_idx]
        if exog is not None:
            X2 = sm.add_constant(exog[regime2_idx])
        else:
            X2 = np.ones((len(y2), 1))
        
        self.regime_models[2] = OLS(y2, X2).fit()
        
        # Store regime assignments
        self.regime_assignments = np.where(regime1_idx, 1, 2)
    
    def test_threshold_significance(
        self,
        n_bootstrap: int = 1000,
        seed: int = 42
    ) -> dict:
        """
        Test significance of threshold effect using Hansen (1999) bootstrap.
        
        H0: Linear model (no threshold effect)
        H1: Threshold model
        """
        np.random.seed(seed)
        
        # Calculate likelihood ratio statistic
        # LR = n * (SSR0 - SSR1) / SSR1
        
        # Fit linear model (null hypothesis)
        y_all = np.concatenate([
            self.regime_models[1].model.endog,
            self.regime_models[2].model.endog
        ])
        
        if hasattr(self.regime_models[1].model, 'exog'):
            X_all = np.vstack([
                self.regime_models[1].model.exog,
                self.regime_models[2].model.exog
            ])
        else:
            X_all = np.ones((len(y_all), 1))
        
        linear_model = OLS(y_all, X_all).fit()
        
        ssr0 = linear_model.ssr
        ssr1 = self.regime_models[1].ssr + self.regime_models[2].ssr
        n = len(y_all)
        
        lr_stat = n * (ssr0 - ssr1) / ssr1
        
        # Bootstrap distribution under null
        lr_bootstrap = []
        
        for _ in range(n_bootstrap):
            # Generate data under null (linear model)
            y_boot = linear_model.fittedvalues + np.random.normal(
                0, 
                np.sqrt(linear_model.scale),
                size=n
            )
            
            # Estimate threshold model on bootstrap data
            # (Simplified - should reconstruct full dataset)
            boot_threshold = self._grid_search_threshold(
                y_boot,
                self.threshold_var_full,  # Need to store this
                X_all[:, 1:] if X_all.shape[1] > 1 else None
            )
            
            # Calculate bootstrap LR statistic
            # ... (similar to above)
            lr_bootstrap.append(lr_stat)  # Placeholder
        
        # Calculate p-value
        p_value = np.mean(np.array(lr_bootstrap) >= lr_stat)
        
        return {
            'lr_statistic': lr_stat,
            'p_value': p_value,
            'threshold': self.threshold,
            'reject_linearity': p_value < 0.05,
            'bootstrap_distribution': lr_bootstrap
        }
```

### Threshold Vector Error Correction Model

```python
class ThresholdVECM:
    """
    Threshold Vector Error Correction Model.
    
    Allows for regime-specific adjustment to long-run equilibrium.
    """
    
    def __init__(
        self,
        coint_rank: int = 1,
        n_regimes: int = 2,
        p_order: int = 1
    ):
        """
        Initialize TVECM.
        
        Parameters
        ----------
        coint_rank : int
            Number of cointegrating relationships
        n_regimes : int
            Number of regimes
        p_order : int
            VAR order (lags of differences)
        """
        self.coint_rank = coint_rank
        self.n_regimes = n_regimes
        self.p_order = p_order
        self.threshold = None
        self.regime_models = {}
        
    def fit(
        self,
        data: pd.DataFrame,
        endog_vars: list,
        threshold_var: str,
        exog_vars: list = None
    ):
        """
        Estimate TVECM.
        
        Parameters
        ----------
        data : DataFrame
            Panel data
        endog_vars : list
            Endogenous variables (prices)
        threshold_var : str
            Threshold variable name
        exog_vars : list
            Exogenous variables
        """
        # Step 1: Test for cointegration
        from statsmodels.tsa.vector_ar.vecm import coint_johansen
        
        y = data[endog_vars].values
        joh_result = coint_johansen(y, det_order=0, k_ar_diff=self.p_order)
        
        if joh_result.lr1[0] < joh_result.cvt[0, 1]:  # 5% level
            raise ValueError("No cointegration found. Cannot estimate TVECM.")
        
        # Extract cointegrating vector
        beta = joh_result.evec[:, 0]
        beta = beta / beta[0]  # Normalize
        
        # Calculate error correction term
        ect = y @ beta
        
        # Step 2: Find threshold
        threshold_values = data[threshold_var].values
        
        self.threshold = self._estimate_threshold_vecm(
            y, ect, threshold_values
        )
        
        # Step 3: Estimate regime-specific VECMs
        self._estimate_regime_vecms(
            data, endog_vars, threshold_var, ect, beta
        )
        
        return self
    
    def _estimate_threshold_vecm(
        self,
        y: np.ndarray,
        ect: np.ndarray,
        threshold_var: np.ndarray
    ) -> float:
        """
        Estimate threshold for VECM using grid search.
        """
        # Create differences
        dy = np.diff(y, axis=0)
        ect_lag = ect[:-1]
        threshold_lag = threshold_var[:-1]
        
        # Grid search
        sorted_threshold = np.sort(threshold_lag)
        n = len(sorted_threshold)
        trim_n = int(n * 0.15)
        
        search_range = sorted_threshold[trim_n:-trim_n]
        ssr_values = []
        
        for tau in search_range:
            # Split regimes
            regime1 = threshold_lag <= tau
            regime2 = threshold_lag > tau
            
            if np.sum(regime1) < 30 or np.sum(regime2) < 30:
                ssr_values.append(np.inf)
                continue
            
            ssr_total = 0
            
            # Regime 1 VECM
            dy1 = dy[regime1]
            ect1 = ect_lag[regime1]
            
            for i in range(dy.shape[1]):  # For each variable
                # Δy_i = α_i * ECT + Γ * Δy_lag + ε
                X1 = np.column_stack([ect1, np.ones(len(ect1))])
                model1 = OLS(dy1[:, i], X1).fit()
                ssr_total += model1.ssr
            
            # Regime 2 VECM
            dy2 = dy[regime2]
            ect2 = ect_lag[regime2]
            
            for i in range(dy.shape[1]):
                X2 = np.column_stack([ect2, np.ones(len(ect2))])
                model2 = OLS(dy2[:, i], X2).fit()
                ssr_total += model2.ssr
            
            ssr_values.append(ssr_total)
        
        # Find optimal threshold
        optimal_idx = np.argmin(ssr_values)
        return search_range[optimal_idx]
    
    def _estimate_regime_vecms(
        self,
        data: pd.DataFrame,
        endog_vars: list,
        threshold_var: str,
        ect: np.ndarray,
        beta: np.ndarray
    ):
        """Estimate VECM for each regime."""
        # Prepare data
        y = data[endog_vars].values
        dy = np.diff(y, axis=0)
        ect_lag = ect[:-1]
        threshold_lag = data[threshold_var].values[:-1]
        
        # Add lagged differences
        dy_lags = []
        for lag in range(1, self.p_order + 1):
            if lag < len(dy):
                dy_lag = np.concatenate([
                    np.full((lag, dy.shape[1]), np.nan),
                    dy[:-lag]
                ])
                dy_lags.append(dy_lag)
        
        # Estimate for each regime
        for regime in [1, 2]:
            if regime == 1:
                mask = threshold_lag <= self.threshold
                regime_name = 'low'
            else:
                mask = threshold_lag > self.threshold
                regime_name = 'high'
            
            # Extract regime data
            dy_regime = dy[mask]
            ect_regime = ect_lag[mask]
            
            # Build design matrix
            X = [ect_regime.reshape(-1, 1)]
            
            # Add lagged differences
            for dy_lag in dy_lags:
                dy_lag_regime = dy_lag[1:][mask]  # Align with dy
                if not np.any(np.isnan(dy_lag_regime)):
                    X.append(dy_lag_regime)
            
            X = np.hstack(X)
            X = sm.add_constant(X)
            
            # Store regime model
            regime_results = {
                'n_obs': np.sum(mask),
                'models': {},
                'adjustment_speeds': {},
                'half_lives': {}
            }
            
            # Estimate equation for each variable
            for i, var in enumerate(endog_vars):
                model = OLS(dy_regime[:, i], X).fit()
                regime_results['models'][var] = model
                
                # Extract adjustment speed
                alpha = model.params[1]  # Coefficient on ECT
                regime_results['adjustment_speeds'][var] = alpha
                
                # Calculate half-life
                if -1 < alpha < 0:
                    half_life = np.log(0.5) / np.log(1 + alpha)
                else:
                    half_life = np.nan
                regime_results['half_lives'][var] = half_life
            
            self.regime_models[regime_name] = regime_results
    
    def test_asymmetric_adjustment(self) -> dict:
        """
        Test whether adjustment speeds differ across regimes.
        
        H0: α^L = α^H (symmetric adjustment)
        H1: α^L ≠ α^H (asymmetric adjustment)
        """
        results = {}
        
        for var in self.regime_models['low']['adjustment_speeds']:
            alpha_low = self.regime_models['low']['adjustment_speeds'][var]
            alpha_high = self.regime_models['high']['adjustment_speeds'][var]
            
            # Get standard errors
            se_low = self.regime_models['low']['models'][var].bse[1]
            se_high = self.regime_models['high']['models'][var].bse[1]
            
            # Wald test statistic
            diff = alpha_low - alpha_high
            se_diff = np.sqrt(se_low**2 + se_high**2)
            
            wald_stat = (diff / se_diff) ** 2
            
            # Chi-square test with 1 df
            from scipy import stats
            p_value = 1 - stats.chi2.cdf(wald_stat, df=1)
            
            results[var] = {
                'alpha_low': alpha_low,
                'alpha_high': alpha_high,
                'difference': diff,
                'wald_statistic': wald_stat,
                'p_value': p_value,
                'reject_symmetry': p_value < 0.05
            }
        
        return results
```

### Smooth Transition Models

```python
class SmoothTransitionVECM:
    """
    Smooth Transition VECM allowing gradual regime changes.
    
    Instead of discrete regimes, uses smooth transition function:
    G(q_t; γ, c) = [1 + exp(-γ(q_t - c))]^(-1)
    """
    
    def __init__(self, transition_var: str = 'conflict_intensity'):
        self.transition_var = transition_var
        self.gamma = None  # Transition speed
        self.c = None      # Location parameter
        
    def fit(self, data: pd.DataFrame, endog_vars: list):
        """
        Estimate STVECM using nonlinear least squares.
        """
        # Prepare data
        y = data[endog_vars].values
        q = data[self.transition_var].values
        
        # Initial values from linear VECM
        from statsmodels.tsa.vector_ar.vecm import VECM
        linear_vecm = VECM(y, k_ar_diff=1, coint_rank=1).fit()
        
        # Define objective function
        def objective(params):
            gamma, c = params[:2]
            alpha_1 = params[2:2+len(endog_vars)]
            alpha_2 = params[2+len(endog_vars):]
            
            # Transition function
            G = 1 / (1 + np.exp(-gamma * (q - c)))
            
            # Calculate residuals
            residuals = []
            for i in range(len(endog_vars)):
                # Smooth transition between regimes
                alpha_smooth = (1 - G) * alpha_1[i] + G * alpha_2[i]
                
                # Simplified - should include full VECM specification
                resid = np.diff(y[:, i]) - alpha_smooth[:-1] * linear_vecm.det_coef_coint.T[0]
                residuals.extend(resid)
            
            return np.sum(np.array(residuals)**2)
        
        # Initial parameter values
        init_params = [
            10.0,  # gamma (transition speed)
            np.median(q),  # c (location)
            *linear_vecm.alpha.flatten(),  # Regime 1 adjustment
            *linear_vecm.alpha.flatten()   # Regime 2 adjustment
        ]
        
        # Optimize
        from scipy.optimize import minimize
        result = minimize(
            objective,
            init_params,
            method='L-BFGS-B',
            bounds=[(0.1, 100), (q.min(), q.max())] + 
                   [(None, None)] * (2 * len(endog_vars))
        )
        
        self.gamma = result.x[0]
        self.c = result.x[1]
        self.alpha_1 = result.x[2:2+len(endog_vars)]
        self.alpha_2 = result.x[2+len(endog_vars):]
        
        return self
    
    def plot_transition_function(self, q_range=None):
        """Plot the smooth transition function."""
        import matplotlib.pyplot as plt
        
        if q_range is None:
            q_range = np.linspace(0, 100, 1000)
        
        G = 1 / (1 + np.exp(-self.gamma * (q_range - self.c)))
        
        plt.figure(figsize=(10, 6))
        plt.plot(q_range, G, linewidth=2)
        plt.axhline(y=0.5, color='red', linestyle='--', alpha=0.5)
        plt.axvline(x=self.c, color='red', linestyle='--', alpha=0.5)
        plt.xlabel('Conflict Intensity')
        plt.ylabel('Transition Function G(q)')
        plt.title(f'Smooth Transition Function (γ={self.gamma:.2f}, c={self.c:.2f})')
        plt.grid(True, alpha=0.3)
        plt.show()
```

### Multiple Threshold Models

```python
def estimate_multiple_thresholds(
    y: np.ndarray,
    threshold_var: np.ndarray,
    max_thresholds: int = 3
) -> dict:
    """
    Estimate models with multiple thresholds.
    
    Uses sequential estimation following Bai (1997).
    """
    results = {}
    
    # Start with single threshold
    model_1 = ThresholdAutoregressiveModel(n_regimes=2)
    model_1.fit(y, threshold_var)
    results[1] = {
        'model': model_1,
        'thresholds': [model_1.threshold],
        'ssr': sum(m.ssr for m in model_1.regime_models.values()),
        'aic': calculate_aic(model_1)
    }
    
    # Test for additional thresholds
    for n_thresh in range(2, max_thresholds + 1):
        # For each existing regime, test for split
        best_split = None
        best_ssr = np.inf
        
        current_thresholds = results[n_thresh - 1]['thresholds']
        
        # Define regimes based on current thresholds
        regimes = []
        for i in range(len(current_thresholds) + 1):
            if i == 0:
                mask = threshold_var <= current_thresholds[0]
            elif i == len(current_thresholds):
                mask = threshold_var > current_thresholds[-1]
            else:
                mask = (threshold_var > current_thresholds[i-1]) & \
                       (threshold_var <= current_thresholds[i])
            regimes.append(mask)
        
        # Try splitting each regime
        for regime_idx, regime_mask in enumerate(regimes):
            if np.sum(regime_mask) < 50:  # Minimum observations
                continue
            
            # Find optimal threshold within regime
            regime_y = y[regime_mask]
            regime_threshold = threshold_var[regime_mask]
            
            # Grid search within regime
            sorted_vals = np.sort(regime_threshold)
            n_regime = len(sorted_vals)
            trim_n = int(n_regime * 0.15)
            
            for tau in sorted_vals[trim_n:-trim_n]:
                # Calculate SSR with new threshold
                new_thresholds = sorted(current_thresholds + [tau])
                ssr = calculate_ssr_multiple_thresholds(
                    y, threshold_var, new_thresholds
                )
                
                if ssr < best_ssr:
                    best_ssr = ssr
                    best_split = new_thresholds
        
        if best_split is not None:
            # Estimate model with new thresholds
            model_n = estimate_model_with_thresholds(
                y, threshold_var, best_split
            )
            
            results[n_thresh] = {
                'model': model_n,
                'thresholds': best_split,
                'ssr': best_ssr,
                'aic': calculate_aic_multiple(model_n, n_thresh)
            }
    
    # Select optimal number of thresholds
    aics = {k: v['aic'] for k, v in results.items()}
    optimal_n = min(aics, key=aics.get)
    
    return {
        'optimal_n_thresholds': optimal_n,
        'optimal_model': results[optimal_n],
        'all_results': results
    }
```

### Threshold Confidence Intervals

```python
def threshold_confidence_interval(
    model: ThresholdAutoregressiveModel,
    confidence_level: float = 0.95,
    method: str = 'likelihood_ratio'
) -> tuple:
    """
    Calculate confidence interval for threshold parameter.
    
    Methods:
    - 'likelihood_ratio': Based on LR statistic
    - 'bootstrap': Bootstrap confidence interval
    """
    if method == 'likelihood_ratio':
        # Hansen (2000) likelihood ratio method
        n = sum(len(m.model.endog) for m in model.regime_models.values())
        
        # Critical value for likelihood ratio
        if confidence_level == 0.95:
            c_alpha = 7.35  # From Hansen (2000) Table 1
        elif confidence_level == 0.90:
            c_alpha = 5.94
        else:
            raise ValueError(f"Critical values not available for {confidence_level}")
        
        # Calculate LR statistic for each threshold value
        lr_stats = []
        threshold_values = []
        
        # Get data (simplified - should be stored in model)
        y = np.concatenate([m.model.endog for m in model.regime_models.values()])
        
        # Grid of threshold values
        sorted_threshold = np.sort(model.threshold_var_full)
        n_grid = len(sorted_threshold)
        trim_n = int(n_grid * 0.15)
        
        for tau in sorted_threshold[trim_n:-trim_n]:
            # Calculate SSR for this threshold
            ssr_tau = calculate_ssr_at_threshold(y, model.threshold_var_full, tau)
            
            # LR statistic
            ssr_optimal = sum(m.ssr for m in model.regime_models.values())
            lr = n * (ssr_tau - ssr_optimal) / ssr_optimal
            
            lr_stats.append(lr)
            threshold_values.append(tau)
        
        # Find confidence interval
        lr_stats = np.array(lr_stats)
        threshold_values = np.array(threshold_values)
        
        # Values where LR < critical value
        ci_mask = lr_stats <= c_alpha
        ci_thresholds = threshold_values[ci_mask]
        
        if len(ci_thresholds) > 0:
            ci_lower = ci_thresholds.min()
            ci_upper = ci_thresholds.max()
        else:
            ci_lower = ci_upper = model.threshold
        
        return (ci_lower, ci_upper)
    
    elif method == 'bootstrap':
        # Bootstrap confidence interval
        bootstrap_thresholds = []
        
        for _ in range(1000):
            # Bootstrap sample
            n = len(model.y_full)
            boot_idx = np.random.choice(n, size=n, replace=True)
            
            y_boot = model.y_full[boot_idx]
            threshold_boot = model.threshold_var_full[boot_idx]
            
            # Estimate threshold on bootstrap sample
            model_boot = ThresholdAutoregressiveModel(n_regimes=2)
            model_boot.fit(y_boot, threshold_boot)
            
            bootstrap_thresholds.append(model_boot.threshold)
        
        # Calculate percentiles
        alpha = 1 - confidence_level
        ci_lower = np.percentile(bootstrap_thresholds, alpha/2 * 100)
        ci_upper = np.percentile(bootstrap_thresholds, (1 - alpha/2) * 100)
        
        return (ci_lower, ci_upper)
```

## Model Diagnostics

### Testing Regime-Specific Parameters

```python
def test_parameter_constancy(
    model: ThresholdVECM,
    parameters: list = ['adjustment_speed', 'short_run']
) -> dict:
    """
    Test whether parameters are constant across regimes.
    """
    test_results = {}
    
    for param in parameters:
        if param == 'adjustment_speed':
            # Test α^L = α^H
            results = model.test_asymmetric_adjustment()
            test_results['adjustment_speed'] = results
            
        elif param == 'short_run':
            # Test Γ^L = Γ^H (short-run dynamics)
            # Implementation depends on model specification
            pass
    
    # Overall test (all parameters jointly)
    # Likelihood ratio test
    unrestricted_ll = sum(
        m['models'][var].llf 
        for regime in model.regime_models.values()
        for var, m in regime['models'].items()
    )
    
    # Estimate restricted model (linear VECM)
    # ... (fit linear VECM and get likelihood)
    
    return test_results
```

### Residual Analysis

```python
def analyze_threshold_residuals(model: ThresholdAutoregressiveModel) -> dict:
    """
    Comprehensive residual diagnostics for threshold models.
    """
    diagnostics = {}
    
    # Extract residuals by regime
    residuals_by_regime = {}
    for regime, regime_model in model.regime_models.items():
        residuals_by_regime[regime] = regime_model.resid
    
    # Test for remaining nonlinearity
    from statsmodels.stats.diagnostic import acorr_ljungbox
    
    for regime, residuals in residuals_by_regime.items():
        # Ljung-Box test
        lb_stat, lb_pvalue = acorr_ljungbox(residuals, lags=10)
        
        # ARCH test
        squared_resid = residuals ** 2
        arch_stat, arch_pvalue = acorr_ljungbox(squared_resid, lags=5)
        
        diagnostics[f'regime_{regime}'] = {
            'ljung_box': {'statistic': lb_stat[-1], 'p_value': lb_pvalue[-1]},
            'arch': {'statistic': arch_stat[-1], 'p_value': arch_pvalue[-1]},
            'normality': jarque_bera(residuals)
        }
    
    # Test for remaining threshold effects
    all_residuals = np.concatenate(list(residuals_by_regime.values()))
    remaining_threshold = test_for_remaining_threshold(
        all_residuals,
        model.threshold_var_full
    )
    
    diagnostics['remaining_nonlinearity'] = remaining_threshold
    
    return diagnostics
```

## Applications to Yemen Data

### Market-Specific Threshold Analysis

```python
def analyze_yemen_market_thresholds(
    panel_data: pd.DataFrame,
    commodity: str = 'wheat'
) -> dict:
    """
    Analyze threshold effects for Yemen market integration.
    """
    # Extract commodity data
    commodity_data = panel_data[panel_data['commodity'] == commodity]
    
    # Major markets
    markets = ['SANA', 'ADEN', 'TAIZ', 'HODEIDAH']
    
    results = {}
    
    for market in markets:
        market_data = commodity_data[commodity_data['market_id'] == market]
        
        # Prepare variables
        local_price = market_data['log_price'].values
        global_price = market_data['log_global_price'].values
        conflict = market_data['conflict_intensity'].values
        
        # Test for threshold in price transmission
        model = ThresholdVECM(coint_rank=1, n_regimes=2)
        
        try:
            model.fit(
                market_data,
                endog_vars=['log_price', 'log_global_price'],
                threshold_var='conflict_intensity'
            )
            
            # Extract key results
            results[market] = {
                'threshold': model.threshold,
                'threshold_ci': threshold_confidence_interval(model),
                'adjustment_low': model.regime_models['low']['adjustment_speeds']['log_price'],
                'adjustment_high': model.regime_models['high']['adjustment_speeds']['log_price'],
                'half_life_low': model.regime_models['low']['half_lives']['log_price'],
                'half_life_high': model.regime_models['high']['half_lives']['log_price'],
                'asymmetry_test': model.test_asymmetric_adjustment()['log_price']
            }
            
        except Exception as e:
            results[market] = {'error': str(e)}
    
    return results
```

## See Also

- [Panel Models](panel-models.md) - Linear panel specifications
- [Cointegration](cointegration.md) - Cointegration testing
- [Time Series](time-series.md) - General time series methods
- [API Reference: ThresholdVECM](../../03-api-reference/models/three_tier/tier2_commodity.md)