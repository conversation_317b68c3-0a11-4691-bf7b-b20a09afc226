# Panel Data Diagnostic Tests

**Target Audience**: Econometricians, Researchers  
**Module**: `yemen_market.models.three_tier.diagnostics`

## Overview

This document details the implementation of diagnostic tests for panel data models. These tests ensure model validity by checking key assumptions and identifying potential issues that could bias results.

## Test Framework

### Diagnostic Test Suite

```python
import numpy as np
import pandas as pd
from scipy import stats
from statsmodels.stats.diagnostic import het_breuschpagan
import statsmodels.api as sm

class PanelDiagnostics:
    """
    Comprehensive diagnostic testing for panel data models.
    
    Implements World Bank-standard tests for:
    - Serial correlation
    - Cross-sectional dependence
    - Heteroskedasticity
    - Specification errors
    - Structural stability
    """
    
    def __init__(self, model_results, panel_data: pd.DataFrame):
        """
        Initialize with fitted model and panel data.
        
        Parameters
        ----------
        model_results : fitted model object
            Results from panel model estimation
        panel_data : DataFrame
            Original panel data with entity and time identifiers
        """
        self.model = model_results
        self.data = panel_data
        self.residuals = model_results.resid
        self.fitted = model_results.fittedvalues
        
    def run_all_tests(self, significance_level: float = 0.05) -> dict:
        """Run complete diagnostic test suite."""
        test_results = {
            'serial_correlation': self.test_serial_correlation(),
            'cross_sectional_dependence': self.test_cross_sectional_dependence(),
            'heteroskedasticity': self.test_heteroskedasticity(),
            'specification': self.test_specification(),
            'structural_stability': self.test_structural_stability(),
            'summary': self._create_summary(significance_level)
        }
        
        return test_results
```

## Serial Correlation Tests

### Wooldridge Test for Panel Data

```python
def wooldridge_serial_correlation_test(
    panel_data: pd.DataFrame,
    formula: str,
    entity_col: str = 'entity',
    time_col: str = 'time'
) -> dict:
    """
    Wooldridge (2002) test for serial correlation in panel data.
    
    H0: No first-order autocorrelation
    H1: AR(1) errors
    
    Test is robust to heteroskedasticity and works for unbalanced panels.
    """
    from linearmodels import PanelOLS
    
    # Step 1: Estimate model in first differences
    # Sort data
    panel_data = panel_data.sort_values([entity_col, time_col])
    
    # Create first differences
    numeric_cols = panel_data.select_dtypes(include=[np.number]).columns
    diff_data = panel_data.copy()
    
    for col in numeric_cols:
        diff_data[f'd_{col}'] = panel_data.groupby(entity_col)[col].diff()
    
    # Remove first observation per entity (lost to differencing)
    diff_data = diff_data.groupby(entity_col).apply(
        lambda x: x.iloc[1:]
    ).reset_index(drop=True)
    
    # Adjust formula for differences
    import re
    
    # Parse original formula
    lhs, rhs = formula.split('~')
    lhs = lhs.strip()
    rhs_terms = re.split(r'\s*\+\s*', rhs.strip())
    
    # Create differenced formula
    diff_formula = f'd_{lhs} ~ ' + ' + '.join([f'd_{term}' for term in rhs_terms])
    
    # Estimate first-difference model
    diff_model = PanelOLS.from_formula(
        diff_formula,
        data=diff_data.set_index([entity_col, time_col])
    )
    diff_results = diff_model.fit()
    
    # Step 2: Regress residuals on lagged residuals
    residuals = diff_results.resids
    
    # Create lagged residuals
    resid_data = pd.DataFrame({
        entity_col: diff_data[entity_col],
        time_col: diff_data[time_col],
        'resid': residuals.values
    })
    
    resid_data['resid_lag'] = resid_data.groupby(entity_col)['resid'].shift(1)
    resid_data = resid_data.dropna()
    
    # Regression of residuals on lagged residuals
    auxiliary_model = sm.OLS(
        resid_data['resid'],
        sm.add_constant(resid_data['resid_lag'])
    )
    aux_results = auxiliary_model.fit()
    
    # Step 3: Test H0: coefficient on lagged residual = -0.5
    coef = aux_results.params[1]
    se = aux_results.bse[1]
    
    # t-statistic for H0: β = -0.5
    t_stat = (coef - (-0.5)) / se
    
    # Degrees of freedom
    n_entities = resid_data[entity_col].nunique()
    n_obs = len(resid_data)
    df = n_obs - n_entities - 1
    
    # P-value (two-sided test)
    p_value = 2 * (1 - stats.t.cdf(abs(t_stat), df))
    
    return {
        'test_statistic': t_stat,
        'p_value': p_value,
        'coefficient': coef,
        'standard_error': se,
        'reject_no_autocorrelation': p_value < 0.05,
        'n_entities': n_entities,
        'n_observations': n_obs,
        'interpretation': 'Serial correlation detected' if p_value < 0.05 
                         else 'No evidence of serial correlation'
    }
```

### Breusch-Godfrey Test for Panel Data

```python
def breusch_godfrey_panel_test(
    residuals: np.ndarray,
    X: np.ndarray,
    entity_ids: np.ndarray,
    max_lags: int = 3
) -> dict:
    """
    Breusch-Godfrey test adapted for panel data.
    
    Tests for serial correlation up to specified lag order.
    """
    n = len(residuals)
    k = X.shape[1]
    
    test_results = {}
    
    for lag in range(1, max_lags + 1):
        # Create lagged residuals
        resid_lags = []
        
        for entity in np.unique(entity_ids):
            entity_mask = entity_ids == entity
            entity_resid = residuals[entity_mask]
            
            # Lag within entity
            for i in range(lag):
                if i < len(entity_resid):
                    lagged = np.concatenate([
                        [np.nan] * (i + 1),
                        entity_resid[:-i-1]
                    ])
                    resid_lags.append(lagged)
        
        # Stack lagged residuals
        resid_lag_matrix = np.column_stack(resid_lags)
        
        # Remove NaN observations
        valid_idx = ~np.any(np.isnan(resid_lag_matrix), axis=1)
        
        # Auxiliary regression
        aux_y = residuals[valid_idx]
        aux_X = np.hstack([X[valid_idx], resid_lag_matrix[valid_idx]])
        
        aux_model = sm.OLS(aux_y, aux_X)
        aux_results = aux_model.fit()
        
        # LM statistic: n * R²
        lm_stat = len(aux_y) * aux_results.rsquared
        
        # Chi-square test with lag degrees of freedom
        p_value = 1 - stats.chi2.cdf(lm_stat, df=lag)
        
        test_results[f'lag_{lag}'] = {
            'lm_statistic': lm_stat,
            'p_value': p_value,
            'reject_no_autocorrelation': p_value < 0.05
        }
    
    return test_results
```

## Cross-Sectional Dependence Tests

### Pesaran CD Test

```python
def pesaran_cd_test(
    residuals: pd.DataFrame,
    entity_col: str = 'entity',
    time_col: str = 'time'
) -> dict:
    """
    Pesaran (2004) Cross-sectional Dependence test.
    
    H0: Cross-sectional independence
    H1: Cross-sectional dependence
    
    Works for unbalanced panels and is robust to non-stationarity.
    """
    # Pivot residuals to wide format (entities as columns)
    resid_wide = residuals.pivot(
        index=time_col,
        columns=entity_col,
        values='residual'
    )
    
    # Number of entities and time periods
    N = resid_wide.shape[1]
    T = resid_wide.shape[0]
    
    # Calculate pairwise correlations
    correlations = []
    T_ij_values = []  # Store effective sample sizes
    
    for i in range(N):
        for j in range(i + 1, N):
            # Get residuals for entities i and j
            resid_i = resid_wide.iloc[:, i]
            resid_j = resid_wide.iloc[:, j]
            
            # Find common non-missing observations
            valid_idx = ~(resid_i.isna() | resid_j.isna())
            
            if valid_idx.sum() > 3:  # Minimum observations for correlation
                # Calculate correlation
                corr = np.corrcoef(
                    resid_i[valid_idx],
                    resid_j[valid_idx]
                )[0, 1]
                
                correlations.append(corr)
                T_ij_values.append(valid_idx.sum())
    
    # Calculate CD statistic
    # CD = sqrt(2/(N(N-1))) * sum(sqrt(T_ij) * rho_ij)
    
    cd_sum = 0
    for corr, T_ij in zip(correlations, T_ij_values):
        cd_sum += np.sqrt(T_ij) * corr
    
    cd_statistic = np.sqrt(2 / (N * (N - 1))) * cd_sum
    
    # Under H0, CD ~ N(0,1) asymptotically
    p_value = 2 * (1 - stats.norm.cdf(abs(cd_statistic)))
    
    # Calculate average absolute correlation
    avg_abs_corr = np.mean(np.abs(correlations))
    
    return {
        'cd_statistic': cd_statistic,
        'p_value': p_value,
        'avg_correlation': np.mean(correlations),
        'avg_abs_correlation': avg_abs_corr,
        'n_entities': N,
        'n_periods': T,
        'n_pairs': len(correlations),
        'reject_independence': p_value < 0.05,
        'interpretation': 'Cross-sectional dependence detected' if p_value < 0.05
                         else 'No evidence of cross-sectional dependence'
    }
```

### Breusch-Pagan LM Test

```python
def breusch_pagan_lm_test(
    residuals: pd.DataFrame,
    entity_col: str = 'entity',
    time_col: str = 'time'
) -> dict:
    """
    Breusch-Pagan (1980) LM test for cross-sectional dependence.
    
    More powerful than Pesaran CD for small N, large T panels.
    """
    # Pivot to wide format
    resid_wide = residuals.pivot(
        index=time_col,
        columns=entity_col,
        values='residual'
    )
    
    N = resid_wide.shape[1]
    T = resid_wide.shape[0]
    
    # Calculate squared correlations
    lm_sum = 0
    
    for i in range(N):
        for j in range(i + 1, N):
            resid_i = resid_wide.iloc[:, i].dropna()
            resid_j = resid_wide.iloc[:, j].dropna()
            
            # Find common time periods
            common_idx = resid_i.index.intersection(resid_j.index)
            
            if len(common_idx) > 3:
                corr = np.corrcoef(
                    resid_i[common_idx],
                    resid_j[common_idx]
                )[0, 1]
                
                # LM uses T_ij * rho_ij^2
                lm_sum += len(common_idx) * corr**2
    
    # LM statistic
    lm_statistic = lm_sum
    
    # Under H0, LM ~ χ²(N(N-1)/2)
    df = N * (N - 1) / 2
    p_value = 1 - stats.chi2.cdf(lm_statistic, df)
    
    # Scaled version for finite samples
    scaled_lm = (T - N - 1) / N * lm_statistic
    scaled_p_value = 1 - stats.chi2.cdf(scaled_lm, df)
    
    return {
        'lm_statistic': lm_statistic,
        'p_value': p_value,
        'scaled_lm': scaled_lm,
        'scaled_p_value': scaled_p_value,
        'degrees_of_freedom': df,
        'reject_independence': p_value < 0.05
    }
```

## Heteroskedasticity Tests

### Modified Wald Test for GroupWise Heteroskedasticity

```python
def modified_wald_test(
    residuals: pd.DataFrame,
    entity_col: str = 'entity'
) -> dict:
    """
    Modified Wald test for groupwise heteroskedasticity in fixed effect models.
    
    H0: σ²_i = σ² for all i (homoskedasticity)
    H1: σ²_i ≠ σ²_j for some i,j (groupwise heteroskedasticity)
    """
    # Calculate group-specific variances
    group_variances = residuals.groupby(entity_col)['residual'].var()
    group_sizes = residuals.groupby(entity_col).size()
    
    # Remove groups with insufficient observations
    valid_groups = group_sizes[group_sizes > 2].index
    group_variances = group_variances[valid_groups]
    group_sizes = group_sizes[valid_groups]
    
    N = len(group_variances)
    
    # Pooled variance estimate
    total_variance = residuals['residual'].var()
    
    # Modified Wald statistic
    W = 0
    for entity, var_i in group_variances.items():
        n_i = group_sizes[entity]
        W += (n_i - 1) * (np.log(var_i) - np.log(total_variance))**2
    
    # Under H0, W ~ χ²(N)
    p_value = 1 - stats.chi2.cdf(W, df=N)
    
    # Calculate variance ratio (max/min)
    var_ratio = group_variances.max() / group_variances.min()
    
    return {
        'wald_statistic': W,
        'p_value': p_value,
        'n_groups': N,
        'variance_ratio': var_ratio,
        'group_variances': group_variances.to_dict(),
        'reject_homoskedasticity': p_value < 0.05,
        'interpretation': 'Groupwise heteroskedasticity detected' if p_value < 0.05
                         else 'No evidence of groupwise heteroskedasticity'
    }
```

### White's Test for Panel Data

```python
def white_test_panel(
    residuals: np.ndarray,
    X: np.ndarray,
    cross_terms: bool = True
) -> dict:
    """
    White's test adapted for panel data.
    
    Tests for general form of heteroskedasticity.
    """
    n = len(residuals)
    k = X.shape[1]
    
    # Square residuals
    resid_squared = residuals ** 2
    
    # Build auxiliary regression matrix
    aux_vars = [X]  # Original regressors
    aux_vars.append(X ** 2)  # Squared terms
    
    if cross_terms and k > 1:
        # Add cross products
        for i in range(k):
            for j in range(i + 1, k):
                aux_vars.append((X[:, i] * X[:, j]).reshape(-1, 1))
    
    # Combine all auxiliary variables
    aux_X = np.hstack(aux_vars)
    
    # Remove multicollinear columns
    from numpy.linalg import matrix_rank
    if matrix_rank(aux_X) < aux_X.shape[1]:
        # Use PCA to reduce dimensionality
        from sklearn.decomposition import PCA
        pca = PCA(n_components=min(aux_X.shape))
        aux_X = pca.fit_transform(aux_X)
    
    # Auxiliary regression
    aux_model = sm.OLS(resid_squared, sm.add_constant(aux_X))
    aux_results = aux_model.fit()
    
    # Test statistic: n * R²
    lm_statistic = n * aux_results.rsquared
    
    # Degrees of freedom
    df = aux_X.shape[1]
    
    # P-value
    p_value = 1 - stats.chi2.cdf(lm_statistic, df)
    
    return {
        'lm_statistic': lm_statistic,
        'p_value': p_value,
        'degrees_of_freedom': df,
        'r_squared_aux': aux_results.rsquared,
        'reject_homoskedasticity': p_value < 0.05
    }
```

## Specification Tests

### Ramsey RESET Test

```python
def ramsey_reset_test(
    model_results,
    powers: list = [2, 3]
) -> dict:
    """
    Ramsey Regression Equation Specification Error Test.
    
    H0: Model is correctly specified
    H1: Model suffers from omitted variables
    """
    # Get fitted values
    fitted = model_results.fittedvalues
    
    # Original variables
    X_orig = model_results.model.exog
    y = model_results.model.endog
    
    # Add powers of fitted values
    reset_vars = []
    for power in powers:
        reset_vars.append(fitted ** power)
    
    # Augmented regression
    X_aug = np.column_stack([X_orig] + reset_vars)
    
    aug_model = sm.OLS(y, X_aug)
    aug_results = aug_model.fit()
    
    # F-test for joint significance of added terms
    # Restricted model: original
    # Unrestricted model: augmented
    
    rss_r = model_results.ssr  # Restricted sum of squares
    rss_u = aug_results.ssr     # Unrestricted sum of squares
    
    df_r = model_results.df_resid  # Restricted degrees of freedom
    df_u = aug_results.df_resid     # Unrestricted degrees of freedom
    
    num_restrictions = len(powers)
    
    f_statistic = ((rss_r - rss_u) / num_restrictions) / (rss_u / df_u)
    p_value = 1 - stats.f.cdf(f_statistic, num_restrictions, df_u)
    
    # Alternative: Use likelihood ratio test
    lr_statistic = model_results.nobs * (
        np.log(rss_r) - np.log(rss_u)
    )
    lr_p_value = 1 - stats.chi2.cdf(lr_statistic, num_restrictions)
    
    return {
        'f_statistic': f_statistic,
        'p_value': p_value,
        'lr_statistic': lr_statistic,
        'lr_p_value': lr_p_value,
        'powers_tested': powers,
        'reject_specification': p_value < 0.05,
        'interpretation': 'Specification error detected' if p_value < 0.05
                         else 'No evidence of specification error'
    }
```

### Hausman Test

```python
def hausman_test(
    fe_results,  # Fixed effects results
    re_results   # Random effects results
) -> dict:
    """
    Hausman test for fixed vs random effects.
    
    H0: Random effects is consistent (and efficient)
    H1: Random effects is inconsistent, use fixed effects
    """
    # Extract coefficients (excluding intercept if present)
    # Find common variables
    fe_params = fe_results.params
    re_params = re_results.params
    
    common_vars = fe_params.index.intersection(re_params.index)
    
    # Coefficient difference
    b_diff = fe_params[common_vars] - re_params[common_vars]
    
    # Variance-covariance difference
    # Var(b_FE - b_RE) = Var(b_FE) - Var(b_RE)
    # Under H0, this should be positive semi-definite
    
    fe_cov = fe_results.cov_params().loc[common_vars, common_vars]
    re_cov = re_results.cov_params().loc[common_vars, common_vars]
    
    var_diff = fe_cov - re_cov
    
    # Handle potential numerical issues
    from numpy.linalg import LinAlgError
    
    try:
        # Hausman statistic: (b_FE - b_RE)' * [Var(b_FE) - Var(b_RE)]^(-1) * (b_FE - b_RE)
        var_diff_inv = np.linalg.inv(var_diff)
        hausman_stat = b_diff.T @ var_diff_inv @ b_diff
        
        # Degrees of freedom
        df = len(common_vars)
        
        # P-value
        p_value = 1 - stats.chi2.cdf(hausman_stat, df)
        
        return {
            'hausman_statistic': hausman_stat,
            'p_value': p_value,
            'degrees_of_freedom': df,
            'reject_random_effects': p_value < 0.05,
            'coefficient_differences': b_diff.to_dict(),
            'interpretation': 'Use fixed effects' if p_value < 0.05
                             else 'Random effects is consistent'
        }
        
    except LinAlgError:
        # Matrix not invertible - use alternative approach
        return {
            'hausman_statistic': np.nan,
            'p_value': np.nan,
            'error': 'Variance difference matrix not positive definite',
            'interpretation': 'Test inconclusive - consider using robust Hausman test'
        }
```

## Structural Stability Tests

### Chow Test for Known Break

```python
def chow_test_panel(
    panel_data: pd.DataFrame,
    formula: str,
    break_date: str,
    entity_col: str = 'entity',
    time_col: str = 'time'
) -> dict:
    """
    Chow test for structural break in panel data.
    
    Tests whether coefficients are stable before and after break date.
    """
    from linearmodels import PanelOLS
    
    # Split data
    data_before = panel_data[panel_data[time_col] < break_date]
    data_after = panel_data[panel_data[time_col] >= break_date]
    
    # Check sample sizes
    n_before = len(data_before)
    n_after = len(data_after)
    
    if n_before < 30 or n_after < 30:
        return {
            'error': 'Insufficient observations in one or both subperiods',
            'n_before': n_before,
            'n_after': n_after
        }
    
    # Estimate models
    # Full sample
    model_full = PanelOLS.from_formula(
        formula,
        data=panel_data.set_index([entity_col, time_col])
    )
    results_full = model_full.fit()
    
    # Before break
    model_before = PanelOLS.from_formula(
        formula,
        data=data_before.set_index([entity_col, time_col])
    )
    results_before = model_before.fit()
    
    # After break
    model_after = PanelOLS.from_formula(
        formula,
        data=data_after.set_index([entity_col, time_col])
    )
    results_after = model_after.fit()
    
    # Calculate Chow statistic
    rss_pooled = results_full.resid_ss
    rss_before = results_before.resid_ss
    rss_after = results_after.resid_ss
    rss_unrestricted = rss_before + rss_after
    
    k = len(results_full.params)  # Number of parameters
    n = len(panel_data)
    
    # F-statistic
    f_stat = ((rss_pooled - rss_unrestricted) / k) / (
        rss_unrestricted / (n - 2*k)
    )
    
    # P-value
    p_value = 1 - stats.f.cdf(f_stat, k, n - 2*k)
    
    # Calculate parameter changes
    param_changes = {}
    for param in results_before.params.index:
        if param in results_after.params.index:
            change = results_after.params[param] - results_before.params[param]
            param_changes[param] = {
                'before': results_before.params[param],
                'after': results_after.params[param],
                'change': change,
                'pct_change': (change / results_before.params[param] * 100 
                              if results_before.params[param] != 0 else np.inf)
            }
    
    return {
        'chow_statistic': f_stat,
        'p_value': p_value,
        'break_date': break_date,
        'reject_stability': p_value < 0.05,
        'parameter_changes': param_changes,
        'n_before': n_before,
        'n_after': n_after,
        'interpretation': 'Structural break detected' if p_value < 0.05
                         else 'No evidence of structural break'
    }
```

### CUSUM Test

```python
def cusum_test(
    residuals: np.ndarray,
    significance_level: float = 0.05
) -> dict:
    """
    CUSUM (Cumulative Sum) test for parameter stability.
    
    Detects systematic changes in regression coefficients.
    """
    n = len(residuals)
    
    # Standardize residuals
    sigma = np.std(residuals)
    standardized_resid = residuals / sigma
    
    # Calculate CUSUM
    cusum = np.cumsum(standardized_resid) / np.sqrt(n)
    
    # Critical values (Brown, Durbin, Evans 1975)
    if significance_level == 0.05:
        a = 0.948
    elif significance_level == 0.01:
        a = 1.143
    else:
        a = 0.948  # Default to 5%
    
    # Critical lines
    k = np.arange(1, n + 1)
    upper_bound = a * np.sqrt(n) + 2 * a * (k - 1) / np.sqrt(n)
    lower_bound = -upper_bound
    
    # Test for crossing
    crosses_upper = np.any(cusum > upper_bound[:len(cusum)])
    crosses_lower = np.any(cusum < lower_bound[:len(cusum)])
    reject_stability = crosses_upper or crosses_lower
    
    # Find first crossing point (if any)
    if reject_stability:
        upper_cross = np.where(cusum > upper_bound[:len(cusum)])[0]
        lower_cross = np.where(cusum < lower_bound[:len(cusum)])[0]
        
        first_cross = n
        if len(upper_cross) > 0:
            first_cross = min(first_cross, upper_cross[0])
        if len(lower_cross) > 0:
            first_cross = min(first_cross, lower_cross[0])
    else:
        first_cross = None
    
    return {
        'cusum': cusum,
        'upper_bound': upper_bound,
        'lower_bound': lower_bound,
        'reject_stability': reject_stability,
        'first_crossing_point': first_cross,
        'significance_level': significance_level,
        'interpretation': 'Parameter instability detected' if reject_stability
                         else 'No evidence of parameter instability'
    }
```

### Quandt Likelihood Ratio Test

```python
def quandt_lr_test(
    model_data: pd.DataFrame,
    formula: str,
    trim_pct: float = 0.15
) -> dict:
    """
    Quandt Likelihood Ratio test for unknown structural break.
    
    Tests all possible break points and finds most likely break.
    """
    n = len(model_data)
    trim_n = int(n * trim_pct)
    
    # Test each possible break point
    lr_stats = []
    break_points = []
    
    for t in range(trim_n, n - trim_n):
        # Split data
        data1 = model_data.iloc[:t]
        data2 = model_data.iloc[t:]
        
        try:
            # Estimate models
            model1 = sm.OLS.from_formula(formula, data=data1).fit()
            model2 = sm.OLS.from_formula(formula, data=data2).fit()
            model_full = sm.OLS.from_formula(formula, data=model_data).fit()
            
            # Likelihood ratio statistic
            ll1 = model1.llf
            ll2 = model2.llf
            ll_full = model_full.llf
            
            lr = -2 * (ll_full - (ll1 + ll2))
            
            lr_stats.append(lr)
            break_points.append(t)
            
        except:
            # Skip if estimation fails
            continue
    
    if not lr_stats:
        return {'error': 'Unable to estimate models for any break point'}
    
    # Find maximum LR statistic
    max_idx = np.argmax(lr_stats)
    max_lr = lr_stats[max_idx]
    optimal_break = break_points[max_idx]
    
    # Critical values (Andrews 1993)
    # These depend on number of parameters and trimming
    k = len(model_full.params)
    
    # Approximate critical values
    if k <= 5:
        cv_10pct = 7.04
        cv_5pct = 8.68
        cv_1pct = 12.16
    else:
        cv_10pct = 7.04 + 0.5 * (k - 5)
        cv_5pct = 8.68 + 0.6 * (k - 5)
        cv_1pct = 12.16 + 0.9 * (k - 5)
    
    return {
        'max_lr_statistic': max_lr,
        'optimal_break_point': optimal_break,
        'optimal_break_date': model_data.index[optimal_break] if hasattr(model_data, 'index') else optimal_break,
        'critical_values': {
            '10%': cv_10pct,
            '5%': cv_5pct,
            '1%': cv_1pct
        },
        'reject_stability_5pct': max_lr > cv_5pct,
        'all_lr_statistics': lr_stats,
        'all_break_points': break_points
    }
```

## Diagnostic Report Generation

```python
def generate_diagnostic_report(
    test_results: dict,
    output_format: str = 'text'
) -> str:
    """
    Generate comprehensive diagnostic report.
    """
    if output_format == 'text':
        report = []
        report.append("=" * 60)
        report.append("PANEL DATA DIAGNOSTIC TEST RESULTS")
        report.append("=" * 60)
        
        # Serial Correlation
        report.append("\n1. SERIAL CORRELATION TESTS")
        report.append("-" * 40)
        if 'wooldridge' in test_results.get('serial_correlation', {}):
            wt = test_results['serial_correlation']['wooldridge']
            report.append(f"Wooldridge Test:")
            report.append(f"  Test statistic: {wt['test_statistic']:.4f}")
            report.append(f"  P-value: {wt['p_value']:.4f}")
            report.append(f"  Result: {wt['interpretation']}")
        
        # Cross-sectional Dependence
        report.append("\n2. CROSS-SECTIONAL DEPENDENCE TESTS")
        report.append("-" * 40)
        if 'pesaran_cd' in test_results.get('cross_sectional_dependence', {}):
            cd = test_results['cross_sectional_dependence']['pesaran_cd']
            report.append(f"Pesaran CD Test:")
            report.append(f"  CD statistic: {cd['cd_statistic']:.4f}")
            report.append(f"  P-value: {cd['p_value']:.4f}")
            report.append(f"  Average |correlation|: {cd['avg_abs_correlation']:.4f}")
            report.append(f"  Result: {cd['interpretation']}")
        
        # Heteroskedasticity
        report.append("\n3. HETEROSKEDASTICITY TESTS")
        report.append("-" * 40)
        if 'modified_wald' in test_results.get('heteroskedasticity', {}):
            mw = test_results['heteroskedasticity']['modified_wald']
            report.append(f"Modified Wald Test:")
            report.append(f"  Test statistic: {mw['wald_statistic']:.4f}")
            report.append(f"  P-value: {mw['p_value']:.4f}")
            report.append(f"  Variance ratio (max/min): {mw['variance_ratio']:.2f}")
            report.append(f"  Result: {mw['interpretation']}")
        
        # Summary
        report.append("\n" + "=" * 60)
        report.append("SUMMARY OF ISSUES DETECTED:")
        report.append("-" * 40)
        
        issues = []
        if test_results.get('serial_correlation', {}).get('detected', False):
            issues.append("- Serial correlation present")
        if test_results.get('cross_sectional_dependence', {}).get('detected', False):
            issues.append("- Cross-sectional dependence present")
        if test_results.get('heteroskedasticity', {}).get('detected', False):
            issues.append("- Heteroskedasticity present")
        
        if issues:
            report.extend(issues)
            report.append("\nRECOMMENDED CORRECTIONS:")
            report.append("- Use Driscoll-Kraay standard errors")
            report.append("- Consider dynamic panel methods if serial correlation persists")
            report.append("- Check for omitted time-varying variables")
        else:
            report.append("No major issues detected. Standard inference is appropriate.")
        
        return "\n".join(report)
    
    elif output_format == 'latex':
        # LaTeX table format
        # Implementation for LaTeX output
        pass
    
    elif output_format == 'html':
        # HTML format
        # Implementation for HTML output
        pass
```

## See Also

- [Unit Root Tests](unit-root-tests.md) - Testing for stationarity
- [Robustness Checks](robustness-checks.md) - Additional validation
- [Panel Models](../econometric-models/panel-models.md) - Model specifications
- [API Reference: Diagnostics](../../03-api-reference/models/three_tier/README.md#diagnostics)