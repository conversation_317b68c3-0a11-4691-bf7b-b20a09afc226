# Robustness Check Implementations

**Target Audience**: Econometricians, Researchers  
**Module**: `yemen_market.models.three_tier.diagnostics`

## Overview

This document details comprehensive robustness checks to validate the reliability of econometric results in the Yemen Market Integration analysis. These procedures ensure findings are not artifacts of specific modeling choices or data subsets.

## Sensitivity Analysis Framework

### Core Robustness Testing Strategy

```python
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Callable
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass
from concurrent.futures import ProcessPoolExecutor
import warnings

@dataclass
class RobustnessResult:
    """Container for robustness check results."""
    base_estimate: float
    base_se: float
    alternative_estimates: Dict[str, float]
    alternative_ses: Dict[str, float]
    stability_metrics: Dict[str, float]
    is_robust: bool
    
class RobustnessChecker:
    """
    Comprehensive robustness checking framework.
    
    Tests sensitivity of results to:
    - Model specification
    - Sample selection
    - Variable definitions
    - Estimation methods
    - Time periods
    """
    
    def __init__(
        self,
        base_model: Callable,
        base_data: pd.DataFrame,
        key_parameter: str = 'conflict_intensity'
    ):
        """
        Initialize robustness checker.
        
        Parameters
        ----------
        base_model : callable
            Function that estimates the base model
        base_data : DataFrame
            Original dataset
        key_parameter : str
            Parameter of interest for robustness checks
        """
        self.base_model = base_model
        self.base_data = base_data
        self.key_parameter = key_parameter
        self.results = {}
        
    def run_all_checks(self, parallel: bool = True) -> Dict[str, RobustnessResult]:
        """Run comprehensive robustness checks."""
        checks = [
            ('specification', self.check_specification_robustness),
            ('sample', self.check_sample_robustness),
            ('measurement', self.check_measurement_robustness),
            ('estimation', self.check_estimation_robustness),
            ('temporal', self.check_temporal_stability),
            ('outliers', self.check_outlier_sensitivity),
            ('functional_form', self.check_functional_form),
            ('heterogeneity', self.check_heterogeneous_effects)
        ]
        
        if parallel:
            with ProcessPoolExecutor() as executor:
                futures = {
                    executor.submit(check_func): name 
                    for name, check_func in checks
                }
                
                for future in futures:
                    name = futures[future]
                    self.results[name] = future.result()
        else:
            for name, check_func in checks:
                self.results[name] = check_func()
        
        # Overall robustness assessment
        self.results['overall'] = self._assess_overall_robustness()
        
        return self.results
    
    def check_specification_robustness(self) -> RobustnessResult:
        """Test sensitivity to model specification."""
        # Base specification
        base_result = self.base_model(self.base_data)
        base_coef = base_result.params[self.key_parameter]
        base_se = base_result.std_errors[self.key_parameter]
        
        alternative_specs = {}
        alternative_ses = {}
        
        # 1. Additional controls
        extended_controls = self.base_data.copy()
        extended_controls['conflict_squared'] = extended_controls['conflict_intensity'] ** 2
        extended_controls['conflict_lag1'] = extended_controls.groupby('entity')['conflict_intensity'].shift(1)
        
        result = self.base_model(extended_controls)
        alternative_specs['extended_controls'] = result.params[self.key_parameter]
        alternative_ses['extended_controls'] = result.std_errors[self.key_parameter]
        
        # 2. Different fixed effects
        # Market-time fixed effects
        mt_data = self.base_data.copy()
        mt_data['market_time'] = mt_data['market_id'] + '_' + mt_data['date'].astype(str)
        
        result_mt = self.estimate_with_market_time_fe(mt_data)
        alternative_specs['market_time_fe'] = result_mt['coefficient']
        alternative_ses['market_time_fe'] = result_mt['se']
        
        # 3. Non-linear specifications
        # Log-log specification
        log_data = self.base_data.copy()
        log_data['log_conflict'] = np.log1p(log_data['conflict_intensity'])
        
        result_log = self.estimate_log_specification(log_data)
        alternative_specs['log_log'] = result_log['coefficient']
        alternative_ses['log_log'] = result_log['se']
        
        # 4. Interaction terms
        interaction_data = self.base_data.copy()
        interaction_data['conflict_x_border'] = (
            interaction_data['conflict_intensity'] * 
            interaction_data['border_market']
        )
        
        result_int = self.base_model(interaction_data)
        alternative_specs['with_interactions'] = result_int.params[self.key_parameter]
        alternative_ses['with_interactions'] = result_int.std_errors[self.key_parameter]
        
        # Calculate stability metrics
        stability_metrics = self._calculate_stability_metrics(
            base_coef, alternative_specs
        )
        
        # Assess robustness
        is_robust = self._check_coefficient_stability(
            base_coef, base_se, alternative_specs, alternative_ses
        )
        
        return RobustnessResult(
            base_estimate=base_coef,
            base_se=base_se,
            alternative_estimates=alternative_specs,
            alternative_ses=alternative_ses,
            stability_metrics=stability_metrics,
            is_robust=is_robust
        )
    
    def check_sample_robustness(self) -> RobustnessResult:
        """Test sensitivity to sample selection."""
        base_result = self.base_model(self.base_data)
        base_coef = base_result.params[self.key_parameter]
        base_se = base_result.std_errors[self.key_parameter]
        
        sample_results = {}
        sample_ses = {}
        
        # 1. Exclude capitals
        no_capitals = self.base_data[
            ~self.base_data['market_id'].isin(['SANA', 'ADEN'])
        ]
        result = self.base_model(no_capitals)
        sample_results['exclude_capitals'] = result.params[self.key_parameter]
        sample_ses['exclude_capitals'] = result.std_errors[self.key_parameter]
        
        # 2. Exclude conflict zones
        low_conflict = self.base_data[
            self.base_data['conflict_intensity'] < 
            self.base_data['conflict_intensity'].quantile(0.75)
        ]
        result = self.base_model(low_conflict)
        sample_results['low_conflict_only'] = result.params[self.key_parameter]
        sample_ses['low_conflict_only'] = result.std_errors[self.key_parameter]
        
        # 3. Balanced panel only
        balanced = self._create_balanced_panel(self.base_data)
        result = self.base_model(balanced)
        sample_results['balanced_panel'] = result.params[self.key_parameter]
        sample_ses['balanced_panel'] = result.std_errors[self.key_parameter]
        
        # 4. Bootstrap samples
        bootstrap_coefs = []
        n_bootstrap = 100
        
        for i in range(n_bootstrap):
            boot_sample = self.base_data.sample(
                n=len(self.base_data),
                replace=True
            )
            try:
                boot_result = self.base_model(boot_sample)
                bootstrap_coefs.append(boot_result.params[self.key_parameter])
            except:
                continue
        
        sample_results['bootstrap_mean'] = np.mean(bootstrap_coefs)
        sample_ses['bootstrap_se'] = np.std(bootstrap_coefs)
        
        # 5. Jackknife estimates
        entities = self.base_data['entity'].unique()
        jackknife_coefs = []
        
        for entity in entities[:50]:  # Limit for computation
            jack_data = self.base_data[self.base_data['entity'] != entity]
            try:
                jack_result = self.base_model(jack_data)
                jackknife_coefs.append(jack_result.params[self.key_parameter])
            except:
                continue
        
        sample_results['jackknife_mean'] = np.mean(jackknife_coefs)
        sample_ses['jackknife_se'] = np.std(jackknife_coefs) * np.sqrt(len(entities) - 1)
        
        stability_metrics = {
            'bootstrap_bias': abs(sample_results['bootstrap_mean'] - base_coef),
            'coefficient_of_variation': np.std(bootstrap_coefs) / abs(base_coef),
            'sample_sensitivity': np.std(list(sample_results.values())) / abs(base_coef)
        }
        
        is_robust = (
            stability_metrics['coefficient_of_variation'] < 0.5 and
            all(self._same_sign(base_coef, alt) for alt in sample_results.values())
        )
        
        return RobustnessResult(
            base_estimate=base_coef,
            base_se=base_se,
            alternative_estimates=sample_results,
            alternative_ses=sample_ses,
            stability_metrics=stability_metrics,
            is_robust=is_robust
        )
    
    def check_measurement_robustness(self) -> RobustnessResult:
        """Test sensitivity to variable measurement."""
        base_result = self.base_model(self.base_data)
        base_coef = base_result.params[self.key_parameter]
        base_se = base_result.std_errors[self.key_parameter]
        
        measurement_results = {}
        measurement_ses = {}
        
        # 1. Alternative conflict measures
        # Binary conflict indicator
        binary_data = self.base_data.copy()
        binary_data['conflict_binary'] = (
            binary_data['conflict_intensity'] > 0
        ).astype(int)
        
        result = self.estimate_with_binary_conflict(binary_data)
        measurement_results['binary_conflict'] = result['coefficient']
        measurement_ses['binary_conflict'] = result['se']
        
        # 2. Winsorized conflict measure
        winsor_data = self.base_data.copy()
        winsor_data['conflict_winsor'] = winsorize(
            winsor_data['conflict_intensity'],
            limits=(0.01, 0.01)
        )
        
        result = self.base_model(winsor_data)
        measurement_results['winsorized'] = result.params[self.key_parameter]
        measurement_ses['winsorized'] = result.std_errors[self.key_parameter]
        
        # 3. Standardized measures
        std_data = self.base_data.copy()
        std_data['conflict_std'] = (
            (std_data['conflict_intensity'] - std_data['conflict_intensity'].mean()) /
            std_data['conflict_intensity'].std()
        )
        
        result = self.estimate_with_standardized(std_data)
        measurement_results['standardized'] = result['coefficient']
        measurement_ses['standardized'] = result['se']
        
        # 4. Moving average smoothing
        ma_data = self.base_data.copy()
        ma_data['conflict_ma3'] = ma_data.groupby('entity')['conflict_intensity'].transform(
            lambda x: x.rolling(3, center=True, min_periods=1).mean()
        )
        
        result = self.estimate_with_ma_conflict(ma_data)
        measurement_results['moving_average'] = result['coefficient']
        measurement_ses['moving_average'] = result['se']
        
        # 5. Measurement error simulation
        me_results = self._simulate_measurement_error(
            self.base_data,
            'conflict_intensity',
            noise_levels=[0.1, 0.2, 0.3]
        )
        
        measurement_results.update(me_results['coefficients'])
        measurement_ses.update(me_results['ses'])
        
        stability_metrics = {
            'measurement_sensitivity': np.std(list(measurement_results.values())),
            'attenuation_bias': min(measurement_results.values()) / base_coef,
            'relative_precision': base_se / np.mean(list(measurement_ses.values()))
        }
        
        is_robust = all(
            self._within_confidence_interval(
                base_coef, base_se, alt_coef, alt_se
            )
            for alt_coef, alt_se in zip(
                measurement_results.values(),
                measurement_ses.values()
            )
        )
        
        return RobustnessResult(
            base_estimate=base_coef,
            base_se=base_se,
            alternative_estimates=measurement_results,
            alternative_ses=measurement_ses,
            stability_metrics=stability_metrics,
            is_robust=is_robust
        )
    
    def check_estimation_robustness(self) -> RobustnessResult:
        """Test sensitivity to estimation method."""
        base_result = self.base_model(self.base_data)
        base_coef = base_result.params[self.key_parameter]
        base_se = base_result.std_errors[self.key_parameter]
        
        estimation_results = {}
        estimation_ses = {}
        
        # 1. Different standard error types
        # Clustered at different levels
        se_types = {
            'robust': 'HC1',
            'cluster_market': 'market_id',
            'cluster_commodity': 'commodity',
            'two_way_cluster': ['market_id', 'commodity'],
            'bootstrap': 'bootstrap'
        }
        
        for se_name, se_type in se_types.items():
            result = self.estimate_with_se_type(self.base_data, se_type)
            estimation_results[f'se_{se_name}'] = result['coefficient']
            estimation_ses[f'se_{se_name}'] = result['se']
        
        # 2. Different estimators
        # Random effects
        re_result = self.estimate_random_effects(self.base_data)
        estimation_results['random_effects'] = re_result['coefficient']
        estimation_ses['random_effects'] = re_result['se']
        
        # Between estimator
        be_result = self.estimate_between_effects(self.base_data)
        estimation_results['between_effects'] = be_result['coefficient']
        estimation_ses['between_effects'] = be_result['se']
        
        # 3. GMM estimation
        gmm_result = self.estimate_gmm(self.base_data)
        estimation_results['gmm'] = gmm_result['coefficient']
        estimation_ses['gmm'] = gmm_result['se']
        
        # 4. Weighted least squares
        # Weight by inverse of group size
        wls_result = self.estimate_weighted_ls(self.base_data)
        estimation_results['weighted_ls'] = wls_result['coefficient']
        estimation_ses['weighted_ls'] = wls_result['se']
        
        stability_metrics = {
            'estimator_range': max(estimation_results.values()) - min(estimation_results.values()),
            'relative_efficiency': base_se / min(estimation_ses.values()),
            'standard_error_ratio': max(estimation_ses.values()) / min(estimation_ses.values())
        }
        
        is_robust = (
            stability_metrics['estimator_range'] < 2 * base_se and
            all(self._same_sign(base_coef, alt) for alt in estimation_results.values())
        )
        
        return RobustnessResult(
            base_estimate=base_coef,
            base_se=base_se,
            alternative_estimates=estimation_results,
            alternative_ses=estimation_ses,
            stability_metrics=stability_metrics,
            is_robust=is_robust
        )
    
    def check_temporal_stability(self) -> RobustnessResult:
        """Test parameter stability over time."""
        base_result = self.base_model(self.base_data)
        base_coef = base_result.params[self.key_parameter]
        base_se = base_result.std_errors[self.key_parameter]
        
        temporal_results = {}
        temporal_ses = {}
        
        # 1. Rolling window estimation
        window_size = 24  # months
        dates = sorted(self.base_data['date'].unique())
        
        rolling_coefs = []
        rolling_dates = []
        
        for i in range(len(dates) - window_size):
            window_data = self.base_data[
                self.base_data['date'].isin(dates[i:i+window_size])
            ]
            
            try:
                result = self.base_model(window_data)
                rolling_coefs.append(result.params[self.key_parameter])
                rolling_dates.append(dates[i+window_size//2])
            except:
                continue
        
        temporal_results['rolling_mean'] = np.mean(rolling_coefs)
        temporal_ses['rolling_std'] = np.std(rolling_coefs)
        
        # 2. Recursive estimation
        recursive_coefs = []
        min_obs = 12
        
        for i in range(min_obs, len(dates)):
            recursive_data = self.base_data[
                self.base_data['date'].isin(dates[:i+1])
            ]
            
            try:
                result = self.base_model(recursive_data)
                recursive_coefs.append(result.params[self.key_parameter])
            except:
                continue
        
        temporal_results['recursive_final'] = recursive_coefs[-1] if recursive_coefs else np.nan
        
        # 3. Pre/post event analysis
        # Example: Major conflict escalation
        event_date = pd.Timestamp('2015-03-01')
        
        pre_data = self.base_data[self.base_data['date'] < event_date]
        post_data = self.base_data[self.base_data['date'] >= event_date]
        
        if len(pre_data) > 100 and len(post_data) > 100:
            pre_result = self.base_model(pre_data)
            post_result = self.base_model(post_data)
            
            temporal_results['pre_event'] = pre_result.params[self.key_parameter]
            temporal_results['post_event'] = post_result.params[self.key_parameter]
            temporal_ses['pre_event'] = pre_result.std_errors[self.key_parameter]
            temporal_ses['post_event'] = post_result.std_errors[self.key_parameter]
        
        # 4. Chow test for structural break
        chow_stat = self._chow_test_statistic(
            self.base_data, event_date, self.key_parameter
        )
        
        stability_metrics = {
            'rolling_cv': np.std(rolling_coefs) / abs(np.mean(rolling_coefs)),
            'max_change': max(rolling_coefs) - min(rolling_coefs),
            'chow_statistic': chow_stat['statistic'],
            'chow_pvalue': chow_stat['p_value'],
            'trend_coefficient': np.polyfit(range(len(rolling_coefs)), rolling_coefs, 1)[0]
        }
        
        is_robust = (
            stability_metrics['rolling_cv'] < 0.5 and
            stability_metrics['chow_pvalue'] > 0.05
        )
        
        return RobustnessResult(
            base_estimate=base_coef,
            base_se=base_se,
            alternative_estimates=temporal_results,
            alternative_ses=temporal_ses,
            stability_metrics=stability_metrics,
            is_robust=is_robust
        )
    
    def check_outlier_sensitivity(self) -> RobustnessResult:
        """Test sensitivity to outliers and influential observations."""
        base_result = self.base_model(self.base_data)
        base_coef = base_result.params[self.key_parameter]
        base_se = base_result.std_errors[self.key_parameter]
        
        outlier_results = {}
        outlier_ses = {}
        
        # 1. Cook's distance
        cooks_d = self._calculate_cooks_distance(base_result)
        high_influence = self.base_data[cooks_d < 4/len(self.base_data)]
        
        result = self.base_model(high_influence)
        outlier_results['exclude_influential'] = result.params[self.key_parameter]
        outlier_ses['exclude_influential'] = result.std_errors[self.key_parameter]
        
        # 2. DFBETAS
        dfbetas = self._calculate_dfbetas(base_result, self.key_parameter)
        low_dfbeta = self.base_data[abs(dfbetas) < 2/np.sqrt(len(self.base_data))]
        
        result = self.base_model(low_dfbeta)
        outlier_results['low_dfbeta'] = result.params[self.key_parameter]
        outlier_ses['low_dfbeta'] = result.std_errors[self.key_parameter]
        
        # 3. Robust regression (M-estimator)
        robust_result = self._robust_regression(self.base_data)
        outlier_results['robust_regression'] = robust_result['coefficient']
        outlier_ses['robust_regression'] = robust_result['se']
        
        # 4. Trimmed sample
        # Remove top/bottom 1% of residuals
        residuals = base_result.resid
        trim_idx = (
            (residuals > np.percentile(residuals, 1)) &
            (residuals < np.percentile(residuals, 99))
        )
        trimmed_data = self.base_data[trim_idx]
        
        result = self.base_model(trimmed_data)
        outlier_results['trimmed'] = result.params[self.key_parameter]
        outlier_ses['trimmed'] = result.std_errors[self.key_parameter]
        
        # 5. Leave-one-out analysis
        loo_coefs = []
        entities = self.base_data['entity'].unique()
        
        for entity in np.random.choice(entities, min(50, len(entities)), replace=False):
            loo_data = self.base_data[self.base_data['entity'] != entity]
            try:
                loo_result = self.base_model(loo_data)
                loo_coefs.append(loo_result.params[self.key_parameter])
            except:
                continue
        
        outlier_results['loo_mean'] = np.mean(loo_coefs)
        outlier_ses['loo_std'] = np.std(loo_coefs)
        
        stability_metrics = {
            'influence_proportion': 1 - len(high_influence) / len(self.base_data),
            'outlier_effect': abs(base_coef - outlier_results['exclude_influential']) / base_se,
            'robustness_ratio': outlier_results['robust_regression'] / base_coef
        }
        
        is_robust = (
            stability_metrics['outlier_effect'] < 2 and
            abs(stability_metrics['robustness_ratio'] - 1) < 0.2
        )
        
        return RobustnessResult(
            base_estimate=base_coef,
            base_se=base_se,
            alternative_estimates=outlier_results,
            alternative_ses=outlier_ses,
            stability_metrics=stability_metrics,
            is_robust=is_robust
        )
    
    def check_functional_form(self) -> RobustnessResult:
        """Test alternative functional form specifications."""
        base_result = self.base_model(self.base_data)
        base_coef = base_result.params[self.key_parameter]
        base_se = base_result.std_errors[self.key_parameter]
        
        functional_results = {}
        functional_ses = {}
        
        # 1. Polynomial specifications
        for degree in [2, 3]:
            poly_data = self.base_data.copy()
            for d in range(2, degree + 1):
                poly_data[f'conflict_pow{d}'] = poly_data['conflict_intensity'] ** d
            
            result = self.base_model(poly_data)
            functional_results[f'polynomial_{degree}'] = result.params[self.key_parameter]
            functional_ses[f'polynomial_{degree}'] = result.std_errors[self.key_parameter]
        
        # 2. Spline specifications
        knots = [0, 5, 10, 20]  # Conflict intensity knots
        spline_data = self._create_spline_variables(
            self.base_data, 'conflict_intensity', knots
        )
        
        result = self.base_model(spline_data)
        functional_results['spline'] = self._extract_spline_effect(result, knots)
        functional_ses['spline'] = self._extract_spline_se(result, knots)
        
        # 3. Threshold models
        threshold_result = self._estimate_threshold_model(
            self.base_data, 'conflict_intensity'
        )
        functional_results['threshold_low'] = threshold_result['low_regime']
        functional_results['threshold_high'] = threshold_result['high_regime']
        functional_ses['threshold_low'] = threshold_result['low_se']
        functional_ses['threshold_high'] = threshold_result['high_se']
        
        # 4. Non-parametric estimation
        gam_result = self._estimate_gam(self.base_data)
        functional_results['gam_average'] = gam_result['average_effect']
        functional_ses['gam_average'] = gam_result['average_se']
        
        # 5. Box-Cox transformation
        boxcox_data = self.base_data.copy()
        boxcox_data['price_boxcox'], lambda_param = stats.boxcox(
            boxcox_data['price'] + 1  # Ensure positive
        )
        
        bc_result = self._estimate_boxcox_model(boxcox_data, lambda_param)
        functional_results['boxcox'] = bc_result['coefficient']
        functional_ses['boxcox'] = bc_result['se']
        
        # Test statistics for functional form
        reset_test = self._ramsey_reset_test(base_result)
        
        stability_metrics = {
            'reset_statistic': reset_test['statistic'],
            'reset_pvalue': reset_test['p_value'],
            'nonlinearity_test': self._test_nonlinearity(self.base_data),
            'specification_range': max(functional_results.values()) - min(functional_results.values())
        }
        
        is_robust = (
            reset_test['p_value'] > 0.05 and
            all(self._same_sign(base_coef, alt) for alt in functional_results.values() if not np.isnan(alt))
        )
        
        return RobustnessResult(
            base_estimate=base_coef,
            base_se=base_se,
            alternative_estimates=functional_results,
            alternative_ses=functional_ses,
            stability_metrics=stability_metrics,
            is_robust=is_robust
        )
    
    def check_heterogeneous_effects(self) -> RobustnessResult:
        """Test for heterogeneous treatment effects."""
        base_result = self.base_model(self.base_data)
        base_coef = base_result.params[self.key_parameter]
        base_se = base_result.std_errors[self.key_parameter]
        
        hetero_results = {}
        hetero_ses = {}
        
        # 1. By market characteristics
        # Urban vs rural
        urban_markets = ['SANA', 'ADEN', 'TAIZ', 'HODEIDAH']
        
        urban_data = self.base_data[self.base_data['market_id'].isin(urban_markets)]
        rural_data = self.base_data[~self.base_data['market_id'].isin(urban_markets)]
        
        urban_result = self.base_model(urban_data)
        rural_result = self.base_model(rural_data)
        
        hetero_results['urban'] = urban_result.params[self.key_parameter]
        hetero_results['rural'] = rural_result.params[self.key_parameter]
        hetero_ses['urban'] = urban_result.std_errors[self.key_parameter]
        hetero_ses['rural'] = rural_result.std_errors[self.key_parameter]
        
        # 2. By commodity type
        commodity_groups = {
            'staples': ['wheat', 'rice', 'sugar'],
            'proteins': ['beans', 'eggs', 'chicken'],
            'fuel': ['diesel', 'petrol', 'gas']
        }
        
        for group_name, commodities in commodity_groups.items():
            group_data = self.base_data[
                self.base_data['commodity'].isin(commodities)
            ]
            if len(group_data) > 100:
                result = self.base_model(group_data)
                hetero_results[f'commodity_{group_name}'] = result.params[self.key_parameter]
                hetero_ses[f'commodity_{group_name}'] = result.std_errors[self.key_parameter]
        
        # 3. By time period
        # Pre/post 2015 escalation
        pre_2015 = self.base_data[self.base_data['date'] < '2015-01-01']
        post_2015 = self.base_data[self.base_data['date'] >= '2015-01-01']
        
        if len(pre_2015) > 100 and len(post_2015) > 100:
            pre_result = self.base_model(pre_2015)
            post_result = self.base_model(post_2015)
            
            hetero_results['pre_2015'] = pre_result.params[self.key_parameter]
            hetero_results['post_2015'] = post_result.params[self.key_parameter]
            hetero_ses['pre_2015'] = pre_result.std_errors[self.key_parameter]
            hetero_ses['post_2015'] = post_result.std_errors[self.key_parameter]
        
        # 4. Quantile regression
        quantiles = [0.25, 0.50, 0.75]
        for q in quantiles:
            q_result = self._estimate_quantile_regression(self.base_data, q)
            hetero_results[f'quantile_{q}'] = q_result['coefficient']
            hetero_ses[f'quantile_{q}'] = q_result['se']
        
        # 5. Machine learning heterogeneity
        ml_hetero = self._estimate_ml_heterogeneity(self.base_data)
        hetero_results['ml_ate'] = ml_hetero['average_effect']
        hetero_ses['ml_ate_se'] = ml_hetero['se']
        
        # Test for significant heterogeneity
        heterogeneity_tests = self._test_coefficient_equality(hetero_results, hetero_ses)
        
        stability_metrics = {
            'heterogeneity_chi2': heterogeneity_tests['chi2'],
            'heterogeneity_pvalue': heterogeneity_tests['p_value'],
            'effect_range': max(hetero_results.values()) - min(hetero_results.values()),
            'relative_heterogeneity': np.std(list(hetero_results.values())) / abs(base_coef)
        }
        
        is_robust = (
            stability_metrics['relative_heterogeneity'] < 1.0 and
            all(self._same_sign(base_coef, alt) for alt in hetero_results.values() if not np.isnan(alt))
        )
        
        return RobustnessResult(
            base_estimate=base_coef,
            base_se=base_se,
            alternative_estimates=hetero_results,
            alternative_ses=hetero_ses,
            stability_metrics=stability_metrics,
            is_robust=is_robust
        )
```

## Robustness Visualization

### Coefficient Stability Plots

```python
class RobustnessVisualizer:
    """Visualization tools for robustness checks."""
    
    def plot_robustness_results(
        self,
        robustness_results: Dict[str, RobustnessResult],
        save_path: str = None
    ):
        """Create comprehensive robustness visualization."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()
        
        # 1. Coefficient forest plot
        self._plot_coefficient_forest(
            robustness_results,
            ax=axes[0]
        )
        
        # 2. Specification curve
        self._plot_specification_curve(
            robustness_results,
            ax=axes[1]
        )
        
        # 3. Temporal stability
        self._plot_temporal_stability(
            robustness_results.get('temporal', None),
            ax=axes[2]
        )
        
        # 4. Heterogeneity analysis
        self._plot_heterogeneity(
            robustness_results.get('heterogeneity', None),
            ax=axes[3]
        )
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def _plot_coefficient_forest(
        self,
        results: Dict[str, RobustnessResult],
        ax: plt.Axes
    ):
        """Forest plot of coefficients across specifications."""
        all_estimates = []
        all_ses = []
        all_labels = []
        
        for check_name, result in results.items():
            if check_name == 'overall':
                continue
                
            # Add base estimate
            all_estimates.append(result.base_estimate)
            all_ses.append(result.base_se)
            all_labels.append(f"{check_name}: base")
            
            # Add alternatives
            for alt_name, alt_est in result.alternative_estimates.items():
                if not np.isnan(alt_est):
                    all_estimates.append(alt_est)
                    all_ses.append(result.alternative_ses.get(alt_name, 0))
                    all_labels.append(f"{check_name}: {alt_name}")
        
        # Create forest plot
        y_pos = np.arange(len(all_estimates))
        
        # Plot confidence intervals
        for i, (est, se) in enumerate(zip(all_estimates, all_ses)):
            ax.errorbar(
                est, i,
                xerr=1.96 * se,
                fmt='o',
                color='black' if 'base' in all_labels[i] else 'gray',
                markersize=6 if 'base' in all_labels[i] else 4
            )
        
        # Add reference line at base estimate
        base_est = results[list(results.keys())[0]].base_estimate
        ax.axvline(base_est, color='red', linestyle='--', alpha=0.5)
        ax.axvline(0, color='black', linestyle='-', alpha=0.3)
        
        ax.set_yticks(y_pos)
        ax.set_yticklabels(all_labels, fontsize=8)
        ax.set_xlabel('Coefficient Estimate')
        ax.set_title('Forest Plot: Coefficient Stability')
        ax.grid(True, alpha=0.3)
    
    def _plot_specification_curve(
        self,
        results: Dict[str, RobustnessResult],
        ax: plt.Axes
    ):
        """Specification curve analysis plot."""
        # Collect all estimates
        all_specs = []
        
        for check_name, result in results.items():
            if check_name == 'overall':
                continue
                
            for spec_name, estimate in result.alternative_estimates.items():
                if not np.isnan(estimate):
                    all_specs.append({
                        'check': check_name,
                        'specification': spec_name,
                        'estimate': estimate,
                        'se': result.alternative_ses.get(spec_name, 0)
                    })
        
        # Sort by estimate
        all_specs.sort(key=lambda x: x['estimate'])
        
        # Plot specification curve
        estimates = [s['estimate'] for s in all_specs]
        ses = [s['se'] for s in all_specs]
        
        x = np.arange(len(estimates))
        ax.plot(x, estimates, 'k-', linewidth=2, label='Point estimate')
        
        # Add confidence bands
        upper = [e + 1.96*se for e, se in zip(estimates, ses)]
        lower = [e - 1.96*se for e, se in zip(estimates, ses)]
        
        ax.fill_between(x, lower, upper, alpha=0.2, color='gray')
        
        # Add reference lines
        base_est = results[list(results.keys())[0]].base_estimate
        ax.axhline(base_est, color='red', linestyle='--', label='Base estimate')
        ax.axhline(0, color='black', linestyle='-', alpha=0.3)
        
        ax.set_xlabel('Specification Number')
        ax.set_ylabel('Coefficient Estimate')
        ax.set_title('Specification Curve Analysis')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Add specification indicators below
        # This would show which features are included in each specification
```

## Robustness Reporting

### Summary Report Generation

```python
def generate_robustness_report(
    robustness_results: Dict[str, RobustnessResult],
    output_format: str = 'latex'
) -> str:
    """Generate comprehensive robustness report."""
    
    if output_format == 'latex':
        report = []
        report.append("\\begin{table}[htbp]")
        report.append("\\centering")
        report.append("\\caption{Robustness Check Results}")
        report.append("\\begin{tabular}{lcccc}")
        report.append("\\hline\\hline")
        report.append("Specification & Coefficient & SE & 95\\% CI & Robust \\\\")
        report.append("\\hline")
        
        # Base specification
        base = robustness_results[list(robustness_results.keys())[0]]
        report.append(
            f"Base model & {base.base_estimate:.3f} & "
            f"({base.base_se:.3f}) & "
            f"[{base.base_estimate - 1.96*base.base_se:.3f}, "
            f"{base.base_estimate + 1.96*base.base_se:.3f}] & Yes \\\\"
        )
        
        report.append("\\hline")
        
        # Alternative specifications
        for check_name, result in robustness_results.items():
            if check_name == 'overall':
                continue
                
            report.append(f"\\textit{{{check_name.replace('_', ' ').title()}}} & & & & \\\\")
            
            for alt_name, alt_est in result.alternative_estimates.items():
                if not np.isnan(alt_est):
                    alt_se = result.alternative_ses.get(alt_name, 0)
                    is_robust = "Yes" if abs(alt_est - base.base_estimate) < 2*base.base_se else "No"
                    
                    report.append(
                        f"\\quad {alt_name.replace('_', ' ')} & "
                        f"{alt_est:.3f} & ({alt_se:.3f}) & "
                        f"[{alt_est - 1.96*alt_se:.3f}, "
                        f"{alt_est + 1.96*alt_se:.3f}] & "
                        f"{is_robust} \\\\"
                    )
        
        report.append("\\hline\\hline")
        report.append("\\end{tabular}")
        
        # Add notes
        report.append("\\begin{tablenotes}")
        report.append("\\small")
        report.append("\\item Notes: Robustness checks include specification, sample, ")
        report.append("measurement, estimation, temporal, outlier, functional form, ")
        report.append("and heterogeneity tests. SE = standard error, CI = confidence interval.")
        report.append("\\end{tablenotes}")
        report.append("\\end{table}")
        
        return '\n'.join(report)
    
    else:  # Markdown format
        report = []
        report.append("# Robustness Check Results\n")
        
        # Summary statistics
        overall = robustness_results.get('overall', {})
        report.append("## Summary")
        report.append(f"- Total checks performed: {len(robustness_results) - 1}")
        report.append(f"- Checks passed: {sum(r.is_robust for r in robustness_results.values() if hasattr(r, 'is_robust'))}")
        report.append(f"- Overall assessment: {'ROBUST' if overall.get('is_robust', False) else 'SENSITIVE'}\n")
        
        # Detailed results
        report.append("## Detailed Results\n")
        
        for check_name, result in robustness_results.items():
            if check_name == 'overall':
                continue
                
            report.append(f"### {check_name.replace('_', ' ').title()}")
            report.append(f"- Base estimate: {result.base_estimate:.3f} ({result.base_se:.3f})")
            report.append(f"- Robust: {'Yes' if result.is_robust else 'No'}")
            
            if result.alternative_estimates:
                report.append("\nAlternative specifications:")
                for alt_name, alt_est in result.alternative_estimates.items():
                    if not np.isnan(alt_est):
                        alt_se = result.alternative_ses.get(alt_name, 0)
                        report.append(f"  - {alt_name}: {alt_est:.3f} ({alt_se:.3f})")
            
            if result.stability_metrics:
                report.append("\nStability metrics:")
                for metric_name, metric_val in result.stability_metrics.items():
                    if not np.isnan(metric_val):
                        report.append(f"  - {metric_name}: {metric_val:.3f}")
            
            report.append("")
        
        return '\n'.join(report)
```

## Applied Example: Yemen Market Integration

```python
def yemen_robustness_analysis(panel_data: pd.DataFrame) -> Dict:
    """
    Comprehensive robustness analysis for Yemen market integration.
    """
    from yemen_market.models.three_tier import PooledPanelModel
    
    # Define base model
    def base_model_func(data):
        model = PooledPanelModel()
        return model.fit(
            data,
            outcome_var='log_price',
            treatment_var='conflict_intensity',
            control_vars=['log_global_price', 'ramadan', 'eid'],
            entity_effects=True,
            time_effects=True
        )
    
    # Initialize robustness checker
    checker = RobustnessChecker(
        base_model=base_model_func,
        base_data=panel_data,
        key_parameter='conflict_intensity'
    )
    
    # Run all checks
    results = checker.run_all_checks(parallel=True)
    
    # Generate visualizations
    visualizer = RobustnessVisualizer()
    fig = visualizer.plot_robustness_results(
        results,
        save_path='robustness_analysis.png'
    )
    
    # Generate report
    latex_report = generate_robustness_report(results, 'latex')
    markdown_report = generate_robustness_report(results, 'markdown')
    
    # Save reports
    with open('robustness_table.tex', 'w') as f:
        f.write(latex_report)
    
    with open('robustness_report.md', 'w') as f:
        f.write(markdown_report)
    
    return {
        'results': results,
        'figure': fig,
        'reports': {
            'latex': latex_report,
            'markdown': markdown_report
        }
    }
```

## See Also

- [Diagnostic Tests](diagnostic-tests.md) - Model diagnostic procedures
- [Panel Models](../econometric-models/panel-models.md) - Base model specifications
- [Threshold Models](../econometric-models/threshold-models.md) - Non-linear specifications
- [API Reference: Diagnostics](../../03-api-reference/models/three_tier/diagnostics.md)