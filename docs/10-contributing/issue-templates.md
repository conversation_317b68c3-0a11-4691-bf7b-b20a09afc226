# Issue Templates

This document provides templates for creating issues in the Yemen Market Integration Platform. Using these templates helps maintainers understand and address issues more efficiently.

## 🐛 Bug Report Template

### Title Format
`[BUG] Brief description of the issue`

### Template
```markdown
## Description
A clear and concise description of the bug.

## To Reproduce
Steps to reproduce the behavior:
1. Load data using '...'
2. Run analysis with '...'
3. See error

## Expected Behavior
What you expected to happen.

## Actual Behavior
What actually happened. Include error messages if applicable.

## Code Example
```python
# Minimal code example that reproduces the issue
import yemen_market as ym

# Your code here
```

## Error Output
```
# Paste full error traceback here
```

## Environment
- Yemen Market Integration version: [e.g., 2.0.1]
- Python version: [e.g., 3.9.5]
- Operating System: [e.g., Ubuntu 20.04]
- Dependencies: Run `pip freeze | grep -E "(pandas|numpy|statsmodels|scikit-learn)"`

## Additional Context
- Data characteristics (if relevant): [e.g., panel size, date range]
- Any recent changes to your environment
- Related issues or PRs

## Possible Solution
(Optional) If you have ideas on how to fix this.

## Impact
- [ ] Blocks analysis
- [ ] Degrades performance  
- [ ] Produces incorrect results
- [ ] Documentation issue only
```

### Bug Report Examples

#### Example 1: Data Processing Bug
```markdown
## Description
WFP price data processor fails when encountering Arabic commodity names in the 2024 dataset.

## To Reproduce
1. Download latest WFP data from HDX
2. Run `WFPProcessor.process_data("wfp_prices_2024.csv")`
3. See UnicodeDecodeError

## Expected Behavior
Processor should handle Arabic text in commodity names gracefully.

## Code Example
```python
from yemen_market.data import WFPProcessor

processor = WFPProcessor()
data = processor.process_data("wfp_prices_2024.csv")
```

## Error Output
```
UnicodeDecodeError: 'ascii' codec can't decode byte 0xd8 in position 15: ordinal not in range(128)
```
```

#### Example 2: Econometric Model Bug
```markdown
## Description
Threshold VECM model produces NaN coefficients when number of observations is close to minimum required.

## To Reproduce
1. Create panel with exactly 100 time periods
2. Run ThresholdVECM with default parameters
3. Check model.coefficients

## Expected Behavior
Model should either produce valid coefficients or raise an informative error about insufficient data.

## Actual Behavior
Model runs without error but coefficients contain NaN values.
```

## ✨ Feature Request Template

### Title Format
`[FEATURE] Brief description of the feature`

### Template
```markdown
## Problem Statement
Describe the problem this feature would solve or the need it addresses.

## Proposed Solution
Clear description of the proposed feature.

## Use Case
Concrete example of how this feature would be used:

```python
# Example code showing desired API
from yemen_market import SomeModule

# How you'd like to use the feature
result = SomeModule.new_feature(data, parameter=value)
```

## Benefits
- Who would benefit from this feature?
- How does it improve the platform?
- Does it address specific research needs?

## Alternatives Considered
Other approaches you've considered and why this solution is preferred.

## Implementation Considerations
- Potential challenges
- Performance implications
- Data requirements
- Dependencies needed

## Related Work
- Links to relevant papers or methods
- Similar implementations in other packages
- Related issues or discussions

## Priority
- [ ] Critical for ongoing research
- [ ] Would improve workflow significantly
- [ ] Nice to have
- [ ] Future consideration
```

### Feature Request Examples

#### Example 1: New Econometric Method
```markdown
## Problem Statement
Current models don't account for spatial spillovers in conflict effects on prices. Neighboring markets may influence each other beyond what standard panel models capture.

## Proposed Solution
Implement Spatial Durbin Model (SDM) for panel data with conflict-specific spatial weights.

## Use Case
```python
from yemen_market.models import SpatialDurbinPanel

# Define spatial weights based on conflict intensity
spatial_weights = create_conflict_weighted_matrix(markets, conflicts)

# Estimate model
model = SpatialDurbinPanel(spatial_weights=spatial_weights)
results = model.fit(panel_data, formula="price ~ conflict + controls")

# Get direct and indirect effects
direct_effects = results.get_direct_effects()
spillover_effects = results.get_spillover_effects()
```

## Benefits
- Captures spatial interdependence in conflict economies
- Identifies spillover effects between markets
- Improves policy recommendations for targeted interventions
```

#### Example 2: Data Enhancement Feature
```markdown
## Problem Statement
Exchange rate data is sparse and researchers need to interpolate between known values while accounting for conflict-driven volatility.

## Proposed Solution
Add conflict-aware interpolation methods for exchange rate time series.

## Use Case
```python
from yemen_market.data import ExchangeRateProcessor

processor = ExchangeRateProcessor()
filled_rates = processor.interpolate_rates(
    rates_data,
    method="conflict_weighted",
    conflict_data=conflict_events
)
```
```

## 📊 Data Issue Template

### Title Format
`[DATA] Brief description of data issue`

### Template
```markdown
## Data Source
- Source name: [e.g., WFP, ACLED, ACAPS]
- Time period: [e.g., 2019-2024]
- Geographic coverage: [e.g., all governorates, specific regions]

## Issue Description
Clear description of the data quality issue.

## Impact
- Which analyses are affected?
- How severe is the issue?
- Are there workarounds?

## Examples
Specific examples of problematic data:

| Date | Market | Commodity | Issue |
|------|--------|-----------|-------|
| 2023-01-15 | Sana'a | Wheat | Negative price |
| 2023-01-16 | Aden | Rice | 10x normal value |

## Suggested Resolution
- Potential fixes
- Data cleaning approach
- Alternative data sources

## Validation
How to verify the issue is resolved:
```python
# Code to check data quality
```
```

## 📚 Documentation Issue Template

### Title Format
`[DOCS] Brief description of documentation issue`

### Template
```markdown
## Page/Section
Link to the documentation page or section with issues.

## Issue Type
- [ ] Incorrect information
- [ ] Missing information
- [ ] Unclear explanation
- [ ] Broken example
- [ ] Typo/formatting

## Current Content
What the documentation currently says (quote or screenshot).

## Suggested Improvement
What it should say instead.

## Additional Context
Why this change would help users.
```

## 🔬 Methodology Question Template

### Title Format
`[METHOD] Question about methodology`

### Template
```markdown
## Context
Brief background on your research question or analysis.

## Methodology Question
Specific question about econometric methods, assumptions, or interpretations.

## What I've Tried
- Approaches attempted
- References consulted
- Current understanding

## Relevant Code/Output
```python
# If applicable, show what you're doing
```

## Desired Outcome
What you're trying to achieve or understand.

## References
Papers or resources you've consulted.
```

## 🚀 Performance Issue Template

### Title Format
`[PERF] Brief description of performance issue`

### Template
```markdown
## Description
What operation is slow or resource-intensive?

## Performance Metrics
- Time taken: [e.g., 5 minutes for 1000 markets]
- Memory usage: [e.g., 8GB RAM]
- CPU usage: [e.g., 100% single core]

## Code to Reproduce
```python
# Minimal example showing performance issue
import time
import yemen_market as ym

start = time.time()
# Your code here
print(f"Time: {time.time() - start}")
```

## Dataset Characteristics
- Number of markets:
- Number of time periods:
- Number of commodities:
- Total observations:

## Expected Performance
What would be acceptable performance for this operation?

## System Information
- Hardware: [e.g., 16GB RAM, 8-core CPU]
- Operating system:
- Python version:

## Profiling Results
(Optional) Output from profiling tools.
```

## 🏷️ Issue Labels

### Type Labels
- `bug`: Something isn't working
- `enhancement`: New feature or request
- `documentation`: Documentation improvements
- `question`: Methodology or usage questions
- `data`: Data quality or availability issues
- `performance`: Performance improvements needed

### Priority Labels
- `critical`: Blocks important research
- `high`: Significantly impacts usage
- `medium`: Normal priority
- `low`: Nice to have

### Component Labels
- `data-pipeline`: Data processing components
- `models`: Econometric models
- `visualization`: Plotting and dashboards
- `api`: API and interfaces
- `testing`: Test suite

### Status Labels
- `needs-triage`: Awaiting initial review
- `confirmed`: Issue confirmed by maintainers
- `in-progress`: Being worked on
- `blocked`: Waiting on other issues
- `wont-fix`: Will not be addressed

## 📝 Best Practices

### Do's
- ✅ Search existing issues before creating new ones
- ✅ Provide minimal reproducible examples
- ✅ Include relevant context about your use case
- ✅ Be specific about versions and environment
- ✅ Update issues as you discover new information

### Don'ts
- ❌ Combine multiple unrelated issues
- ❌ Submit issues without checking documentation first
- ❌ Include sensitive or private data
- ❌ Make political statements about the conflict
- ❌ Share exact coordinates of vulnerable locations

## 🤝 After Submitting

1. **Monitor**: Watch for maintainer questions
2. **Respond**: Provide additional information promptly
3. **Test**: Verify fixes when provided
4. **Close**: Confirm when issue is resolved
5. **Share**: Help others with similar issues

Remember: Clear, detailed issues help us improve the platform faster and better serve the research community!