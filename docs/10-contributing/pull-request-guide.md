# Pull Request Guide

This guide outlines the process for submitting pull requests (PRs) to the Yemen Market Integration Platform. Following these guidelines helps maintain code quality and ensures smooth integration of contributions.

## 📋 Before You Submit

### Pre-submission Checklist
- [ ] Tests pass locally (`pytest tests/`)
- [ ] Code follows project style guidelines
- [ ] Documentation is updated
- [ ] Commit messages are clear and descriptive
- [ ] Branch is up to date with main
- [ ] No sensitive data is included

### Required Checks
```bash
# Run all tests
pytest tests/ -v

# Check code style
flake8 src/ tests/
black --check src/ tests/

# Verify documentation builds
cd docs && make html

# Run econometric diagnostics
python scripts/run_diagnostic_tests.py
```

## 🌿 Branching Strategy

### Branch Naming
Use descriptive branch names following this pattern:
- `feature/description` - New features
- `fix/issue-number-description` - Bug fixes
- `docs/description` - Documentation only
- `refactor/description` - Code refactoring
- `test/description` - Test additions/improvements

Examples:
```bash
feature/threshold-vecm-implementation
fix/123-exchange-rate-missing-data
docs/econometric-methods-guide
refactor/panel-data-handler
test/conflict-validation-coverage
```

### Branch Lifecycle
1. Create branch from latest `main`
2. Make atomic commits
3. Push regularly to backup work
4. Open PR when ready for review
5. Address feedback
6. Merge after approval

## 📝 PR Template

When opening a PR, use this template:

```markdown
## Description
Brief description of what this PR does and why it's needed.

## Type of Change
- [ ] Bug fix (non-breaking change fixing an issue)
- [ ] New feature (non-breaking change adding functionality)
- [ ] Breaking change (fix or feature causing existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Refactoring (no functional changes)

## Related Issues
Fixes #(issue number)
Related to #(issue numbers)

## Changes Made
- List key changes
- Highlight important decisions
- Note any assumptions

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing performed
- [ ] Econometric diagnostics pass

### Test Coverage
- Current coverage: X%
- New coverage: Y%

## Documentation
- [ ] Code comments added/updated
- [ ] API documentation updated
- [ ] User guide updated
- [ ] Methodology documentation updated

## Econometric Considerations
(Required for model/analysis changes)
- [ ] Theoretical justification provided
- [ ] Assumptions documented
- [ ] Robustness checks included
- [ ] Literature references added

## Data Considerations
(Required for data-related changes)
- [ ] Data quality checks implemented
- [ ] Missing data handling documented
- [ ] Conflict-related biases addressed
- [ ] Data sources properly attributed

## Screenshots/Examples
(If applicable, include output examples, plots, or screenshots)

## Checklist
- [ ] My code follows the project style guidelines
- [ ] I have performed a self-review
- [ ] I have commented hard-to-understand areas
- [ ] My changes generate no new warnings
- [ ] New and existing tests pass locally
- [ ] Any dependent changes have been merged
```

## 🔍 Review Process

### What Reviewers Look For

#### Code Quality
- **Clarity**: Is the code easy to understand?
- **Efficiency**: Are there performance considerations?
- **Style**: Does it follow project conventions?
- **Error Handling**: Are edge cases handled?

#### Econometric Rigor
- **Methodology**: Is the approach theoretically sound?
- **Assumptions**: Are they clearly stated and tested?
- **Identification**: Is causal inference properly addressed?
- **Robustness**: Are results stable across specifications?

#### Conflict Sensitivity
- **Neutrality**: Does the code maintain political neutrality?
- **Ethics**: Are there any potential harm considerations?
- **Privacy**: Is sensitive information protected?
- **Context**: Is local context properly considered?

### Review Timeline
- Small PRs (< 100 lines): 1-2 days
- Medium PRs (100-500 lines): 2-3 days
- Large PRs (> 500 lines): 3-5 days
- Urgent fixes: Same day (tag as `urgent`)

## 💬 PR Etiquette

### For Authors
- **Keep PRs focused**: One feature/fix per PR
- **Write clear descriptions**: Explain the "why" not just "what"
- **Respond promptly**: Address feedback within 48 hours
- **Be receptive**: Feedback improves code quality
- **Update regularly**: Keep PR up to date with main

### For Reviewers
- **Be constructive**: Suggest improvements, don't just criticize
- **Be specific**: Reference line numbers and provide examples
- **Be timely**: Review within committed timeline
- **Be thorough**: Check code, tests, and documentation
- **Be respectful**: Remember there's a person behind the code

### Comment Examples

Good feedback:
```
"Consider using Panel.get_group() here for better performance with large datasets.
Here's an example: `group_data = panel.get_group(market_id)`"
```

Less helpful:
```
"This is inefficient"
```

## 🚀 Merging Requirements

### Automatic Checks
All PRs must pass:
- [ ] CI/CD pipeline (tests, linting, building)
- [ ] Code coverage threshold (≥80%)
- [ ] Documentation build
- [ ] Security scan

### Manual Approval
- At least 1 approving review
- 2 reviews for breaking changes
- Maintainer approval for architectural changes

### Merge Methods
We use **squash and merge** for:
- Feature branches
- Bug fixes
- Most regular PRs

We use **merge commit** for:
- Release branches
- Large features with meaningful commit history

## 🛠️ Common Issues

### Failing Tests
```bash
# Run specific test for debugging
pytest tests/unit/test_module.py::test_function -v

# Check test coverage
pytest --cov=src tests/
```

### Merge Conflicts
```bash
# Update your branch
git fetch origin
git rebase origin/main

# Resolve conflicts
# Edit conflicted files
git add .
git rebase --continue
```

### Large Data Files
Never commit large data files. Instead:
1. Add to `.gitignore`
2. Document in `data/README.md`
3. Provide download script
4. Use Git LFS if necessary

## 📊 Special Considerations

### Econometric Methods PRs
Additional requirements:
- Literature review in PR description
- Simulation results if applicable
- Comparison with existing methods
- Edge case handling

Example:
```python
def new_estimator(data, **kwargs):
    """
    Implement estimator from Author et al. (2023).
    
    References:
        Author et al. (2023): "Title", Journal Name
    
    Parameters
    ----------
    data : pd.DataFrame
        Panel data with required columns
    **kwargs : dict
        Additional parameters
        
    Returns
    -------
    results : EstimationResults
        Results object with coefficients and diagnostics
    """
```

### Data Source PRs
Must include:
- Data documentation
- Quality assessment
- Integration tests
- Update pipeline documentation

### Performance PRs
Should show:
- Benchmark results
- Memory usage comparison
- Scalability analysis
- No regression in accuracy

## 🎯 Quick Reference

### Commands
```bash
# Create PR-ready branch
git checkout -b feature/my-feature
git push -u origin feature/my-feature

# Keep branch updated
git fetch origin
git rebase origin/main

# Clean up commit history
git rebase -i HEAD~n  # where n is number of commits

# Run pre-PR checks
make pre-pr  # Runs tests, linting, and coverage
```

### Labels
We use labels to categorize PRs:
- `enhancement`: New features
- `bug`: Bug fixes
- `documentation`: Docs only
- `econometrics`: Model changes
- `data`: Data pipeline changes
- `performance`: Speed improvements
- `breaking`: Breaking changes
- `help wanted`: Need assistance
- `good first issue`: Beginner friendly

## 📞 Getting Help

- **PR Questions**: Comment in the PR
- **General Help**: Use Discussions
- **Urgent Issues**: Tag @maintainers
- **Private Concerns**: Email <EMAIL>

Remember: Every PR improves the platform and helps researchers better understand market dynamics in conflict settings. Thank you for contributing!