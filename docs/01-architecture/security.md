# Security Architecture

## 🔐 Overview

This document outlines the security measures implemented in the Yemen Market Integration Platform to protect data integrity, ensure system reliability, and maintain user privacy.

## 🛡️ Security Principles

### Defense in Depth
Multiple layers of security controls:
1. Input validation
2. Access control
3. Secure coding practices
4. Monitoring and logging
5. Regular updates

### Least Privilege
- Minimal access rights
- Role-based permissions
- Temporary credentials
- Audit trails

### Secure by Default
- Safe defaults
- Explicit opt-in for risky operations
- Fail securely
- No hardcoded secrets

## 🔒 Data Security

### Data Classification

| Level | Description | Examples | Protection |
|-------|-------------|----------|------------|
| Public | Publicly available | Published reports | Standard |
| Internal | Project use only | Raw datasets | Encrypted at rest |
| Sensitive | Restricted access | API keys | Encrypted + Access control |
| Critical | Highly restricted | User data | Not stored |

### Data Protection Measures

#### At Rest
```python
# Encrypted storage for sensitive data
from cryptography.fernet import Fernet

class SecureStorage:
    def __init__(self, key: bytes):
        self.cipher = Fernet(key)
    
    def save_sensitive(self, data: str, path: Path):
        encrypted = self.cipher.encrypt(data.encode())
        path.write_bytes(encrypted)
```

#### In Transit
- HTTPS for all API calls
- SSL/TLS for database connections
- Encrypted file transfers

#### In Processing
- Memory clearing after use
- No logging of sensitive data
- Secure temporary files

## 🔑 Authentication & Authorization

### API Key Management

```python
# Environment-based configuration
import os
from pathlib import Path

class APIConfig:
    @staticmethod
    def get_api_key(service: str) -> str:
        # Try environment variable first
        key = os.environ.get(f"{service.upper()}_API_KEY")
        
        # Fall back to secure file
        if not key:
            key_file = Path.home() / f".yemen_market/{service}.key"
            if key_file.exists():
                key = key_file.read_text().strip()
        
        if not key:
            raise ValueError(f"No API key found for {service}")
        
        return key
```

### Access Control

```python
# Role-based access control
class AccessControl:
    ROLES = {
        'viewer': ['read'],
        'analyst': ['read', 'analyze'],
        'admin': ['read', 'analyze', 'write', 'delete']
    }
    
    def check_permission(self, user_role: str, action: str) -> bool:
        allowed_actions = self.ROLES.get(user_role, [])
        return action in allowed_actions
```

## 🛡️ Input Validation

### Data Validation

```python
# Comprehensive input validation
from typing import Any, Dict
import re

class InputValidator:
    @staticmethod
    def validate_market_name(name: str) -> str:
        """Validate and sanitize market names."""
        # Remove potentially dangerous characters
        cleaned = re.sub(r'[^\w\s\-\']', '', name)
        
        # Limit length
        if len(cleaned) > 100:
            raise ValueError("Market name too long")
        
        # Check against whitelist
        if cleaned not in VALID_MARKETS:
            raise ValueError(f"Unknown market: {cleaned}")
        
        return cleaned
    
    @staticmethod
    def validate_date_range(start: str, end: str) -> tuple:
        """Validate date inputs."""
        try:
            start_date = pd.to_datetime(start)
            end_date = pd.to_datetime(end)
        except Exception:
            raise ValueError("Invalid date format")
        
        # Sanity checks
        if start_date > end_date:
            raise ValueError("Start date must be before end date")
        
        if start_date < pd.Timestamp('2015-01-01'):
            raise ValueError("Data not available before 2015")
        
        return start_date, end_date
```

### Path Traversal Prevention

```python
# Safe file path handling
from pathlib import Path

class SafeFileHandler:
    def __init__(self, base_dir: Path):
        self.base_dir = base_dir.resolve()
    
    def safe_path(self, user_path: str) -> Path:
        """Ensure path is within base directory."""
        # Resolve to absolute path
        requested = (self.base_dir / user_path).resolve()
        
        # Check if path is within base directory
        try:
            requested.relative_to(self.base_dir)
        except ValueError:
            raise SecurityError("Path traversal attempt detected")
        
        return requested
```

## 🚨 Error Handling

### Secure Error Messages

```python
# Don't leak sensitive information in errors
class SecureErrorHandler:
    @staticmethod
    def handle_database_error(e: Exception) -> str:
        # Log full error internally
        logger.error(f"Database error: {e}", exc_info=True)
        
        # Return generic message to user
        return "A database error occurred. Please try again later."
    
    @staticmethod
    def handle_api_error(e: Exception) -> str:
        # Don't expose API details
        if "401" in str(e):
            return "Authentication failed"
        elif "404" in str(e):
            return "Resource not found"
        else:
            return "External service unavailable"
```

## 📝 Logging & Monitoring

### Security Logging

```python
# Structured security logging
import json
from datetime import datetime

class SecurityLogger:
    def log_access(self, user: str, resource: str, action: str, success: bool):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'user': user,
            'resource': resource,
            'action': action,
            'success': success,
            'ip': self.get_client_ip()
        }
        
        # Write to secure log
        with open('logs/security.jsonl', 'a') as f:
            f.write(json.dumps(log_entry) + '\n')
```

### Monitoring Alerts

```python
# Anomaly detection
class SecurityMonitor:
    def check_anomalies(self):
        # Unusual access patterns
        if self.failed_attempts > 5:
            self.alert("Multiple failed access attempts")
        
        # Data exfiltration
        if self.data_downloaded > self.threshold:
            self.alert("Unusual data download volume")
        
        # Timing anomalies
        if self.is_unusual_time():
            self.alert("Access at unusual time")
```

## 🔄 Dependency Management

### Supply Chain Security

```python
# requirements.txt with hashes
pandas==2.0.3 \
    --hash=sha256:abc123...
numpy==1.24.3 \
    --hash=sha256:def456...
```

### Vulnerability Scanning

```bash
# Regular security audits
pip-audit

# Check for known vulnerabilities
safety check

# License compliance
pip-licenses
```

## 🏗️ Infrastructure Security

### Container Security

```dockerfile
# Secure Docker configuration
FROM python:3.11-slim

# Non-root user
RUN useradd -m -u 1000 appuser

# Security updates
RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get clean

# Copy only necessary files
COPY --chown=appuser:appuser requirements.txt .
COPY --chown=appuser:appuser src/ ./src/

# Run as non-root
USER appuser
```

### Environment Configuration

```python
# Secure configuration management
class Config:
    # Never commit secrets
    SECRET_KEY = os.environ.get('SECRET_KEY')
    
    # Secure defaults
    DEBUG = os.environ.get('DEBUG', 'False') == 'True'
    
    # Validate configuration
    @classmethod
    def validate(cls):
        if not cls.SECRET_KEY:
            raise ValueError("SECRET_KEY not set")
        
        if cls.DEBUG and cls.is_production():
            raise ValueError("DEBUG enabled in production")
```

## 🔍 Security Testing

### Automated Security Tests

```python
# tests/security/test_input_validation.py
def test_sql_injection_prevention():
    """Test SQL injection prevention."""
    malicious_input = "'; DROP TABLE markets; --"
    
    with pytest.raises(ValueError):
        validate_market_name(malicious_input)

def test_path_traversal_prevention():
    """Test path traversal prevention."""
    malicious_path = "../../../etc/passwd"
    
    with pytest.raises(SecurityError):
        safe_handler.safe_path(malicious_path)
```

### Security Checklist

- [ ] All inputs validated
- [ ] No hardcoded secrets
- [ ] Dependencies up to date
- [ ] Error messages sanitized
- [ ] Logging configured
- [ ] Access controls implemented
- [ ] Data encrypted where needed
- [ ] Security tests passing

## 🚀 Incident Response

### Response Plan

1. **Detection**: Monitoring alerts trigger
2. **Containment**: Isolate affected systems
3. **Investigation**: Analyze logs and impacts
4. **Remediation**: Fix vulnerabilities
5. **Recovery**: Restore normal operations
6. **Lessons Learned**: Update procedures

### Contact Information

- Security Lead: [Designated person]
- Escalation: [Management contact]
- External: [Security vendor if applicable]

## 📚 Related Documentation

- [Architecture Overview](./overview.md) - System design
- [Component Design](./components.md) - Component security
- [Development Guide](../04-development/setup.md) - Secure development
- [API Security](../03-api-reference/security.md) - API security guide

---

*Security is everyone's responsibility. Report concerns immediately.*