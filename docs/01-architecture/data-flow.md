# Data Flow Architecture

## 🌊 Overview

This document details how data flows through the Yemen Market Integration Platform, from raw sources to final analysis results.

## 📥 Data Ingestion Flow

### 1. Source Systems

```mermaid
graph LR
    A[HDX API] --> D[Raw Data Store]
    B[WFP Portal] --> D
    C[ACLED API] --> D
    E[ACAPS Files] --> D
```

**Data Sources**:
- **HDX**: Humanitarian Data Exchange (prices, boundaries)
- **WFP**: World Food Programme (commodity prices)
- **ACLED**: Armed Conflict Location & Event Data
- **ACAPS**: Yemen Analysis Hub (control zones)

### 2. Download Process

```python
# Automated download pipeline
scripts/data_collection/
├── download_data.py         # Main HDX downloader
├── download_acled_data.py   # ACLED-specific
└── manual downloads/        # ACAPS shapefiles
```

**Storage Structure**:
```
data/raw/
├── hdx/
│   ├── wfp-food-prices-for-yemen/
│   └── cod-ab-yem/  # Admin boundaries
├── acled/
│   └── yemen_events_2019-2024.csv
└── acaps/
    └── control_zones_*.zip
```

## 🔄 Data Processing Pipeline

### Stage 1: Initial Processing

```
Raw CSV/ZIP → Processor → Cleaned Data → Parquet
```

**WFP Processing Flow**:
```python
# 1. Load raw data
raw_prices = pd.read_csv("wfp_food_prices.csv")

# 2. Standardize locations
standardized = standardize_market_names(raw_prices)
# Result: 88.4% coverage (up from 62%)

# 3. Currency conversion
usd_prices = convert_to_usd(standardized)

# 4. Quality checks
validated = validate_prices(usd_prices)

# 5. Save processed data
validated.to_parquet("data/processed/wfp_prices.parquet")
```

### Stage 2: Spatial Integration

```
Markets + Control Zones → Spatial Join → Market-Zone Mapping
```

**Spatial Join Process**:
1. Load market coordinates
2. Load control zone polygons
3. Perform point-in-polygon joins
4. Handle temporal changes
5. Validate assignments

### Stage 3: Panel Construction

```
Prices + Conflict + Zones → Panel Builder → Integrated Panel
```

**Panel Types Created**:

1. **Raw Panel**: All observations
   - 450K+ price observations
   - Unbalanced structure
   - Missing data preserved

2. **Balanced Panel**: Complete cases
   - 44K observations
   - 169 markets × 18 commodities
   - No missing values

3. **Smart Panel**: Existing pairs only
   - Reduces false missings
   - Better for analysis
   - Commodity-market validation

## 📊 Feature Engineering Flow

### Feature Pipeline

```
Base Panel → Feature Engineer → Enhanced Panel
     ↓              ↓                ↓
  Spatial      Temporal        Interactions
  Features     Features         & Derived
```

**Feature Categories**:

1. **Spatial Features** (30+ features):
   ```python
   # K-nearest neighbor prices
   knn_prices = get_knn_prices(market, k=5)
   
   # Distance metrics
   border_distance = calculate_border_distance(market)
   
   # Connectivity index
   connectivity = market_connectivity_index(market)
   ```

2. **Temporal Features** (20+ features):
   ```python
   # Lags and leads
   price_lag_1 = prices.shift(1)
   price_lag_12 = prices.shift(12)
   
   # Moving averages
   ma_7 = prices.rolling(7).mean()
   ma_30 = prices.rolling(30).mean()
   
   # Seasonal indicators
   ramadan = is_ramadan(date)
   harvest = is_harvest_season(date, commodity)
   ```

3. **Interaction Features** (50+ features):
   ```python
   # Zone-time interactions
   zone_time = zone_id * time_trend
   
   # Conflict-commodity
   conflict_food = conflict_intensity * is_food_item
   
   # Exchange rate interactions
   fx_premium_zone = fx_premium * zone_indicator
   ```

## 🧮 Analysis Data Flow

### Model Estimation Flow

```
Panel Data → Model Selection → Estimation → Results
     ↓            ↓               ↓           ↓
 Validation   Configuration   Diagnostics  Storage
```

**Three-Tier Processing**:

1. **Tier 1 Flow**:
   ```
   Full Panel → Fixed Effects → Pooled Results
                     ↓
                Diagnostics → Corrected SEs
   ```

2. **Tier 2 Flow**:
   ```
   Panel → Split by Commodity → Individual Models
              ↓                      ↓
         Cointegration          Threshold Tests
              ↓                      ↓
           VECM/VAR            Regime Analysis
   ```

3. **Tier 3 Flow**:
   ```
   Results → Factor Analysis → Validation
                 ↓                ↓
            Common Factors   Policy Tests
   ```

## 💾 Data Storage Architecture

### File Organization

```
data/
├── raw/                 # Original downloads
├── interim/             # Processing stages
│   ├── cleaned/        # Basic cleaning
│   ├── standardized/   # Name standardization
│   └── merged/         # Combined datasets
├── processed/          # Analysis-ready
│   ├── panels/        # Panel datasets
│   ├── features/      # Feature sets
│   └── modeling/      # Model-ready data
└── results/           # Analysis outputs
```

### Format Strategy

| Stage | Format | Reason |
|-------|--------|--------|
| Raw | CSV/Excel | Original format |
| Interim | Parquet | Fast I/O, compression |
| Processed | Parquet | Columnar storage |
| Results | JSON/Parquet | Structured output |

## 🔍 Data Quality Monitoring

### Quality Checkpoints

1. **Ingestion Checks**:
   - File integrity
   - Schema validation
   - Completeness metrics

2. **Processing Checks**:
   - Standardization coverage
   - Join success rates
   - Outlier detection

3. **Feature Checks**:
   - Distribution validation
   - Correlation analysis
   - Missing patterns

### Quality Metrics Tracked

```python
quality_metrics = {
    'coverage': 0.884,          # 88.4% market coverage
    'missing_rate': 0.023,      # 2.3% missing values
    'outlier_rate': 0.001,      # 0.1% outliers
    'join_success': 0.956,      # 95.6% spatial joins
    'validation_pass': True     # All checks passed
}
```

## 🚀 Performance Optimization

### Optimization Strategies

1. **Chunked Processing**:
   ```python
   for chunk in pd.read_csv(file, chunksize=10000):
       process_chunk(chunk)
   ```

2. **Parallel Operations**:
   ```python
   from multiprocessing import Pool
   
   with Pool(4) as p:
       results = p.map(process_market, markets)
   ```

3. **Caching**:
   ```python
   @lru_cache(maxsize=128)
   def expensive_calculation(params):
       # Cached computation
       return result
   ```

## 🔄 Data Refresh Process

### Update Frequency

- **Daily**: Exchange rates
- **Weekly**: WFP prices
- **Bi-weekly**: ACAPS control zones
- **Monthly**: Full panel rebuild

### Incremental Updates

```python
def update_panel(existing_panel, new_data):
    # Merge new observations
    updated = pd.concat([existing_panel, new_data])
    
    # Remove duplicates
    updated = updated.drop_duplicates(
        subset=['market', 'commodity', 'date'],
        keep='last'
    )
    
    # Recompute features
    return add_features(updated)
```

## 📈 Data Lineage

### Tracking Data Transformations

Each dataset includes metadata:
```json
{
    "source_files": ["wfp_prices.csv", "acaps_zones.shp"],
    "processing_date": "2024-05-31",
    "transformations": [
        "standardize_names",
        "convert_currency",
        "spatial_join",
        "add_features"
    ],
    "quality_metrics": {...},
    "schema_version": "2.0"
}
```

## 🔐 Data Security

### Access Control
- Read-only raw data
- Processed data versioning
- Audit logs for changes

### Sensitive Data Handling
- No PII in datasets
- Aggregated conflict data
- Secure API credentials

## 📚 Related Documentation

- [Architecture Overview](./overview.md) - System design
- [Component Design](./components.md) - Component details
- [Data Pipeline Guide](../02-user-guides/data-pipeline.md) - User guide
- [API Reference](../03-api-reference/data/) - Data APIs

---

*For hands-on examples, see the [Data Pipeline Guide](../02-user-guides/data-pipeline.md).*