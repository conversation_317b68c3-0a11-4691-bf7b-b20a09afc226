# IDE Configuration Guide

**Target Audience**: Developers  
**Purpose**: Configure your IDE for optimal Yemen Market Integration Platform development

## Overview

Proper IDE configuration enhances productivity through features like intelligent code completion, integrated testing, debugging, and automatic formatting. This guide covers configuration for popular Python IDEs.

## Visual Studio Code

### Required Extensions

```bash
# Install via command palette (Ctrl+Shift+P)
ext install ms-python.python
ext install ms-python.vscode-pylance
ext install ms-python.black-formatter
ext install charliermarsh.ruff
ext install ms-toolsai.jupyter
ext install eamodio.gitlens
ext install usernamehw.errorlens
```

### Workspace Settings

Create `.vscode/settings.json`:

```json
{
    // Python configuration
    "python.defaultInterpreterPath": "${workspaceFolder}/venv/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "python.linting.pylintEnabled": false,
    
    // Formatting
    "editor.formatOnSave": true,
    "editor.formatOnPaste": false,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length", "88"],
    "[python]": {
        "editor.defaultFormatter": "ms-python.black-formatter",
        "editor.codeActionsOnSave": {
            "source.organizeImports": true
        }
    },
    
    // Testing
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "python.testing.pytestArgs": [
        "tests",
        "-v",
        "--cov=yemen_market",
        "--cov-report=html"
    ],
    
    // Type checking
    "python.analysis.typeCheckingMode": "strict",
    "python.analysis.autoImportCompletions": true,
    "python.analysis.completeFunctionParens": true,
    
    // Editor
    "editor.rulers": [88, 120],
    "editor.wordWrap": "on",
    "editor.minimap.enabled": true,
    "editor.renderWhitespace": "trailing",
    
    // Files
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".pytest_cache": true,
        ".coverage": true,
        "htmlcov": true,
        ".mypy_cache": true,
        ".ruff_cache": true,
        "*.egg-info": true
    },
    "files.associations": {
        "*.yml": "yaml",
        "*.yaml": "yaml",
        ".env*": "dotenv"
    },
    
    // Git
    "git.autofetch": true,
    "git.confirmSync": false,
    
    // Terminal
    "terminal.integrated.env.linux": {
        "PYTHONPATH": "${workspaceFolder}"
    },
    "terminal.integrated.env.osx": {
        "PYTHONPATH": "${workspaceFolder}"
    },
    "terminal.integrated.env.windows": {
        "PYTHONPATH": "${workspaceFolder}"
    }
}
```

### Launch Configurations

Create `.vscode/launch.json`:

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": true,
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        },
        {
            "name": "Python: Run Analysis",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/scripts/run_analysis.py",
            "args": ["--config", "config/analysis.yaml"],
            "console": "integratedTerminal"
        },
        {
            "name": "Python: Debug Tests",
            "type": "python",
            "request": "launch",
            "module": "pytest",
            "args": [
                "tests/",
                "-v",
                "--no-cov"  // Disable coverage during debugging
            ],
            "console": "integratedTerminal"
        },
        {
            "name": "Python: Debug Specific Test",
            "type": "python",
            "request": "launch",
            "module": "pytest",
            "args": [
                "${file}::${selectedText}",
                "-v",
                "-s"
            ],
            "console": "integratedTerminal"
        }
    ]
}
```

### Tasks Configuration

Create `.vscode/tasks.json`:

```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Run Tests",
            "type": "shell",
            "command": "make test",
            "group": {
                "kind": "test",
                "isDefault": true
            },
            "problemMatcher": "$pytest"
        },
        {
            "label": "Run Linting",
            "type": "shell",
            "command": "make lint",
            "problemMatcher": "$ruff"
        },
        {
            "label": "Format Code",
            "type": "shell",
            "command": "make format",
            "group": "build"
        },
        {
            "label": "Generate Coverage Report",
            "type": "shell",
            "command": "make test-coverage && open htmlcov/index.html",
            "group": "test"
        }
    ]
}
```

### Recommended Keybindings

Add to `keybindings.json`:

```json
[
    {
        "key": "cmd+shift+t",
        "command": "python.execInTerminal",
        "when": "editorTextFocus && editorLangId == 'python'"
    },
    {
        "key": "cmd+shift+d",
        "command": "python.debugInTerminal",
        "when": "editorTextFocus && editorLangId == 'python'"
    }
]
```

## PyCharm Professional

### Project Configuration

1. **Open Project**: File → Open → Select project directory

2. **Configure Interpreter**:
   - File → Settings → Project → Python Interpreter
   - Click gear icon → Add → Existing Environment
   - Select: `./venv/bin/python`

3. **Enable Django Support** (if using Django):
   - Settings → Languages & Frameworks → Django
   - Enable Django support
   - Set Django project root

### Code Style Settings

Settings → Editor → Code Style → Python:

```
Tabs and Indents:
- Use tab character: ❌
- Tab size: 4
- Indent: 4
- Continuation indent: 8

Wrapping and Braces:
- Hard wrap at: 88
- Wrap on typing: ✓

Blank Lines:
- After class declaration: 2
- After top-level imports: 2
- Around method: 1

Imports:
- Sort imports alphabetically: ✓
- Sort by type (constants first): ✓
- Optimize imports: ✓
```

### Run Configurations

1. **Test Configuration**:
   - Run → Edit Configurations → + → Python tests → pytest
   - Target: `tests` directory
   - Additional arguments: `-v --cov=yemen_market`

2. **Script Configuration**:
   - Run → Edit Configurations → + → Python
   - Script path: `scripts/run_analysis.py`
   - Parameters: `--config config/analysis.yaml`
   - Environment variables: Load from .env file

### File Templates

Settings → Editor → File and Code Templates:

```python
# Python Script template
#!/usr/bin/env python3
"""
${NAME}.py - ${Description}

Created: ${DATE}
Author: ${USER}
"""

from __future__ import annotations

import logging
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


def main() -> None:
    """Main function."""
    pass


if __name__ == "__main__":
    main()
```

### Inspections

Settings → Editor → Inspections → Python:

Enable these inspections:
- ✓ PEP 8 coding style violation
- ✓ PEP 8 naming convention violation
- ✓ Type checker
- ✓ Missing or empty docstring
- ✓ Unresolved references
- ✓ Unused local symbols

## Jupyter/JupyterLab

### Configuration

Create `~/.jupyter/jupyter_lab_config.py`:

```python
c = get_config()

# Terminal
c.TerminalInteractiveShell.editing_mode = 'vi'

# Notebook
c.NotebookApp.notebook_dir = './notebooks'
c.NotebookApp.open_browser = True

# Code formatting
c.ContentsManager.default_jupytext_formats = 'ipynb,py:percent'

# Extensions
c.LabApp.extensions_enabled = True
```

### Useful Extensions

```bash
# Install JupyterLab extensions
pip install jupyterlab-lsp
pip install jupyterlab-git
pip install jupyterlab-code-formatter

# Install server extensions
jupyter labextension install @jupyter-widgets/jupyterlab-manager
jupyter labextension install @jupyterlab/toc
jupyter labextension install @krassowski/jupyterlab-lsp
```

### Notebook Best Practices

```python
# Standard notebook imports cell
%load_ext autoreload
%autoreload 2

import sys
sys.path.append('..')

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

from yemen_market.data import PanelBuilder
from yemen_market.models.three_tier import ThreeTierRunner

# Configure display
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
plt.style.use('seaborn-v0_8-darkgrid')
```

## Vim/Neovim

### Basic Configuration

Create `~/.config/nvim/init.vim`:

```vim
" Python specific settings
au BufNewFile,BufRead *.py
    \ set tabstop=4 |
    \ set softtabstop=4 |
    \ set shiftwidth=4 |
    \ set textwidth=88 |
    \ set expandtab |
    \ set autoindent |
    \ set fileformat=unix

" Flag unnecessary whitespace
au BufRead,BufNewFile *.py,*.pyw,*.c,*.h match BadWhitespace /\s\+$/

" Python syntax highlighting
let python_highlight_all=1
syntax on

" File navigation
set nu
set encoding=utf-8
```

### LSP Configuration

Using nvim-lspconfig:

```lua
-- ~/.config/nvim/lua/lsp.lua
local lspconfig = require('lspconfig')

lspconfig.pyright.setup{
    settings = {
        python = {
            analysis = {
                typeCheckingMode = "strict",
                autoSearchPaths = true,
                useLibraryCodeForTypes = true,
                diagnosticMode = "workspace"
            }
        }
    }
}

lspconfig.ruff_lsp.setup{}
```

## Common IDE Features

### Code Snippets

#### VS Code Snippets

Create `.vscode/python.code-snippets`:

```json
{
    "Yemen Market Import": {
        "prefix": "ymimport",
        "body": [
            "from yemen_market.data import PanelBuilder",
            "from yemen_market.models.three_tier import ThreeTierRunner",
            "from yemen_market.utils.logging import setup_logging",
            "",
            "logger = setup_logging(__name__)"
        ]
    },
    "Test Function": {
        "prefix": "test",
        "body": [
            "def test_${1:function_name}():",
            "    \"\"\"Test ${2:description}.\"\"\"",
            "    # Arrange",
            "    ${3:setup}",
            "    ",
            "    # Act",
            "    ${4:action}",
            "    ",
            "    # Assert",
            "    ${5:assertion}"
        ]
    }
}
```

### Debugging Configuration

#### Debugging Best Practices

1. **Use Conditional Breakpoints**:
   ```python
   # Right-click on breakpoint → Add condition
   # Example: len(df) > 1000
   ```

2. **Watch Expressions**:
   - `df.shape` - Monitor dataframe dimensions
   - `df.memory_usage().sum()` - Track memory
   - `locals()` - View all local variables

3. **Debug Console Commands**:
   ```python
   # In debug console
   df.info()
   df.describe()
   df.head()
   ```

### Performance Profiling

#### VS Code with Python Profiler

```json
{
    "name": "Python: Profile Current File",
    "type": "python",
    "request": "launch",
    "module": "cProfile",
    "args": [
        "-o", "profile.stats",
        "${file}"
    ]
}
```

#### PyCharm Profiler

1. Run → Profile 'script_name'
2. View results in profiler tab
3. Sort by time or calls
4. Find bottlenecks

## Environment Integration

### Environment Variables

All IDEs should load from `.env`:

```bash
# .env
PYTHONPATH=.
LOG_LEVEL=DEBUG
HDX_API_KEY=your_key_here
```

### Path Configuration

Ensure Python path includes project root:

```json
// VS Code
"terminal.integrated.env.linux": {
    "PYTHONPATH": "${workspaceFolder}"
}
```

### Virtual Environment Detection

Most IDEs auto-detect `venv/` in project root. If not:

1. **VS Code**: Ctrl+Shift+P → "Python: Select Interpreter"
2. **PyCharm**: Settings → Project → Python Interpreter
3. **Vim**: Use vim-virtualenv plugin

## Productivity Tips

### Code Navigation

- **Go to Definition**: F12 (VS Code), Ctrl+B (PyCharm)
- **Find Usages**: Shift+F12 (VS Code), Alt+F7 (PyCharm)
- **Go to Symbol**: Ctrl+Shift+O (VS Code), Ctrl+Alt+Shift+N (PyCharm)

### Refactoring

- **Rename**: F2 (VS Code), Shift+F6 (PyCharm)
- **Extract Method**: Ctrl+Shift+R (VS Code), Ctrl+Alt+M (PyCharm)
- **Optimize Imports**: Ctrl+Alt+O (PyCharm)

### Testing

- **Run Test at Cursor**: Place cursor on test and use test explorer
- **Debug Test**: Right-click test → Debug
- **Run Failed Tests**: Use test explorer's "Run Failed" button

## Troubleshooting

### Import Resolution Issues

```json
// VS Code: Add to settings.json
"python.analysis.extraPaths": [
    "./src",
    "./scripts"
]
```

### Slow Performance

1. Exclude large directories from indexing
2. Disable unused extensions
3. Increase memory allocation for IDE
4. Use `.gitignore` patterns for file exclusion

### Type Checking Errors

```json
// VS Code: Adjust strictness
"python.analysis.typeCheckingMode": "basic"  // or "off" temporarily
```

## Next Steps

- Configure pre-commit hooks: See [Git Workflow](../coding-standards/git-workflow.md)
- Set up debugging: See [Debugging Guide](../debugging/common-issues.md)
- Learn keyboard shortcuts for your IDE
- Customize further based on team preferences