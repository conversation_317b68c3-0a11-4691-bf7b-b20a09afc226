# Dependency Management

**Target Audience**: Developers, DevOps  
**Purpose**: Understanding and managing project dependencies

## Overview

The Yemen Market Integration Platform uses modern Python dependency management with `pyproject.toml` and optional support for `pip-tools` and `poetry`. This guide covers dependency management best practices and troubleshooting.

## Dependency Structure

### Core Dependencies

Located in `pyproject.toml`:

```toml
[project]
dependencies = [
    # Data processing
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "polars>=0.20.0",      # High-performance dataframes
    "duckdb>=0.9.0",       # In-process SQL analytics
    
    # Statistical/Econometric
    "statsmodels>=0.14.0",
    "linearmodels>=4.25",   # Panel data models
    "scipy>=1.10.0",
    "scikit-learn>=1.3.0",
    
    # Geospatial
    "geopandas>=0.13.0",
    "shapely>=2.0.0",
    "pyproj>=3.5.0",
    
    # API clients
    "requests>=2.31.0",
    "hdx-python-api>=6.0.0",
    
    # Configuration
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "python-dotenv>=1.0.0",
    
    # Utilities
    "click>=8.1.0",        # CLI
    "rich>=13.0.0",        # Terminal formatting
    "tqdm>=4.65.0",        # Progress bars
]
```

### Development Dependencies

```toml
[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-xdist>=3.3.0",      # Parallel testing
    "pytest-mock>=3.11.0",
    "hypothesis>=6.82.0",       # Property-based testing
    
    # Code quality
    "black>=23.7.0",
    "ruff>=0.0.285",
    "mypy>=1.4.0",
    "pre-commit>=3.3.0",
    
    # Documentation
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
    
    # Notebooks
    "jupyterlab>=4.0.0",
    "notebook>=7.0.0",
    "ipywidgets>=8.1.0",
    
    # Profiling
    "memory-profiler>=0.61.0",
    "line-profiler>=4.0.0",
]
```

### Optional Dependencies

```toml
# Visualization
viz = [
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.15.0",
    "altair>=5.0.0",
]

# Performance
perf = [
    "numba>=0.57.0",        # JIT compilation
    "cupy>=12.0.0",         # GPU acceleration
    "ray>=2.6.0",           # Distributed computing
    "modin[ray]>=0.23.0",   # Parallel pandas
]

# Production
prod = [
    "gunicorn>=21.0.0",
    "uvicorn>=0.23.0",
    "prometheus-client>=0.17.0",
    "sentry-sdk>=1.29.0",
]
```

## Installation Methods

### Basic Installation

```bash
# Install core dependencies only
pip install .

# Install with development dependencies
pip install -e ".[dev]"

# Install all optional dependencies
pip install -e ".[dev,viz,perf,prod]"
```

### Using pip-tools

```bash
# Install pip-tools
pip install pip-tools

# Generate requirements files
pip-compile pyproject.toml -o requirements.txt
pip-compile --extra dev pyproject.toml -o requirements-dev.txt

# Install from requirements
pip-sync requirements.txt requirements-dev.txt

# Update dependencies
pip-compile --upgrade
```

### Using Poetry (Alternative)

```bash
# Install poetry
curl -sSL https://install.python-poetry.org | python3 -

# Install dependencies
poetry install

# Add new dependency
poetry add pandas@^2.0.0

# Add dev dependency
poetry add --group dev pytest@^7.4.0
```

## Dependency Pinning Strategy

### For Development
- Use flexible version specifiers (`>=`) in `pyproject.toml`
- Allow minor version updates
- Test regularly with latest versions

### For Production
- Generate locked requirements file
- Pin exact versions for reproducibility
- Update deliberately with testing

```bash
# Generate locked requirements
pip freeze > requirements-lock.txt

# Or with pip-tools
pip-compile --generate-hashes pyproject.toml -o requirements-lock.txt
```

## Managing Conflicts

### Common Conflicts and Solutions

#### NumPy/Pandas Version Conflicts
```bash
# Force reinstall with compatible versions
pip install --force-reinstall numpy==1.24.3 pandas==2.0.3
```

#### Geospatial Dependencies
```bash
# Install system dependencies first
# Ubuntu/Debian
sudo apt-get install gdal-bin libgdal-dev

# macOS
brew install gdal

# Then install Python packages
pip install --no-binary shapely shapely
pip install geopandas
```

#### statsmodels/scipy Conflicts
```bash
# Install in specific order
pip install scipy==1.10.1
pip install statsmodels==0.14.0 --no-deps
pip install linearmodels
```

## Security Considerations

### Checking for Vulnerabilities

```bash
# Install safety
pip install safety

# Check for known vulnerabilities
safety check

# Or use pip-audit
pip install pip-audit
pip-audit
```

### Updating Vulnerable Packages

```bash
# Update specific package
pip install --upgrade package-name

# Update all packages (careful!)
pip list --outdated
pip install --upgrade $(pip list --outdated | awk 'NR>2 {print $1}')
```

## Platform-Specific Dependencies

### macOS (Apple Silicon)

```bash
# Some packages need special handling on M1/M2
# Install Rosetta 2 if needed
softwareupdate --install-rosetta

# Use specific wheels
pip install --only-binary :all: numpy pandas scipy

# Or use conda for scientific packages
conda install numpy pandas scipy statsmodels
```

### Windows

```bash
# Install Visual C++ Build Tools first
# Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/

# Use pre-built wheels when available
pip install --only-binary :all: package-name

# Or use conda
conda install -c conda-forge geopandas
```

### Linux (Ubuntu/Debian)

```bash
# System dependencies
sudo apt-get update
sudo apt-get install -y \
    python3-dev \
    build-essential \
    libssl-dev \
    libffi-dev \
    libxml2-dev \
    libxslt1-dev \
    zlib1g-dev \
    libgdal-dev
```

## Dependency Groups

### Minimal Installation
For basic data processing without extras:
```bash
pip install pandas numpy requests pydantic
```

### Data Science Installation
For full analytical capabilities:
```bash
pip install -e ".[viz]"
```

### Development Installation
For contributing to the project:
```bash
pip install -e ".[dev,viz]"
pre-commit install
```

### Production Installation
For deployment:
```bash
pip install ".[prod]"
# No editable install in production
```

## Best Practices

### 1. Regular Updates

```bash
# Check outdated packages weekly
pip list --outdated

# Update development dependencies monthly
pip install --upgrade -r requirements-dev.txt

# Update production dependencies quarterly
# (with thorough testing)
```

### 2. Dependency Documentation

Always document why a dependency is needed:

```toml
dependencies = [
    "hdx-python-api>=6.0.0",  # Required for HDX data access
    "linearmodels>=4.25",      # Panel data econometrics
    "polars>=0.20.0",         # High-performance data processing
]
```

### 3. Version Constraints

- Use `>=` for minimum version requirements
- Use `~=` for compatible releases
- Use `==` only when absolutely necessary
- Never use `*` or unpinned versions

### 4. Testing Dependencies

```bash
# Test with minimum versions
pip install pandas==2.0.0 numpy==1.24.0
pytest

# Test with latest versions
pip install --upgrade pandas numpy
pytest
```

## Troubleshooting

### Installation Failures

```bash
# Clear pip cache
pip cache purge

# Use verbose mode to debug
pip install -v package-name

# Try without cache
pip install --no-cache-dir package-name

# Use different index
pip install -i https://pypi.python.org/simple/ package-name
```

### Import Errors

```python
# Check installed packages
import pkg_resources
installed = [pkg.key for pkg in pkg_resources.working_set]
print(sorted(installed))

# Check package version
import pandas
print(pandas.__version__)

# Check installation location
import statsmodels
print(statsmodels.__file__)
```

### Binary Compatibility

```bash
# Force pure Python installation
pip install --no-binary :all: package-name

# Or force binary installation
pip install --only-binary :all: package-name
```

## CI/CD Dependency Management

### GitHub Actions Example

```yaml
- name: Cache dependencies
  uses: actions/cache@v3
  with:
    path: ~/.cache/pip
    key: ${{ runner.os }}-pip-${{ hashFiles('**/pyproject.toml') }}
    restore-keys: |
      ${{ runner.os }}-pip-

- name: Install dependencies
  run: |
    python -m pip install --upgrade pip
    pip install -e ".[dev]"
```

### Docker Example

```dockerfile
# Multi-stage build for smaller images
FROM python:3.9-slim as builder

WORKDIR /app
COPY pyproject.toml .
RUN pip install --user --no-cache-dir .

FROM python:3.9-slim
COPY --from=builder /root/.local /root/.local
ENV PATH=/root/.local/bin:$PATH
```

## Monitoring Dependencies

### Create Dependency Report

```python
# scripts/dependency_report.py
import pkg_resources
import json

def get_dependency_tree():
    """Generate dependency tree report."""
    packages = {}
    for pkg in pkg_resources.working_set:
        packages[pkg.key] = {
            'version': pkg.version,
            'requires': [str(r) for r in pkg.requires()]
        }
    return packages

if __name__ == "__main__":
    tree = get_dependency_tree()
    with open('dependency_report.json', 'w') as f:
        json.dump(tree, f, indent=2)
```

## Next Steps

- Review [IDE Configuration](ide-configuration.md) for editor setup
- Check [Common Issues](../debugging/common-issues.md) for troubleshooting
- See [Python Style Guide](../coding-standards/python-style-guide.md) for coding standards