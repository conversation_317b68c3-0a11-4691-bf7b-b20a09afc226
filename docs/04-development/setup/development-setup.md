# Development Environment Setup

**Target Audience**: New developers, contributors  
**Prerequisites**: Python 3.9+, Git, basic command line knowledge

## Overview

This guide walks you through setting up a complete development environment for the Yemen Market Integration Platform. By the end, you'll have a fully configured environment ready for development.

## System Requirements

### Minimum Requirements
- **OS**: Ubuntu 20.04+, macOS 10.15+, or Windows 10+ (with WSL2)
- **Python**: 3.9 or higher
- **RAM**: 8GB (16GB recommended for large datasets)
- **Storage**: 20GB free space
- **CPU**: 4 cores (8 recommended for parallel processing)

### Software Prerequisites
```bash
# Check Python version
python --version  # Should be 3.9+

# Check pip
pip --version

# Check git
git --version
```

## Step 1: Clone the Repository

```bash
# Clone via HTTPS
git clone https://github.com/your-org/yemen-market-integration.git

# Or via SSH (if you have SSH keys set up)
<NAME_EMAIL>:your-org/yemen-market-integration.git

cd yemen-market-integration
```

## Step 2: Set Up Python Environment

### Option A: Using the Setup Script (Recommended)

```bash
# Run the automated setup script
./setup_venv.sh

# This script will:
# 1. Create a virtual environment
# 2. Activate it
# 3. Install all dependencies
# 4. Set up pre-commit hooks
```

### Option B: Manual Setup

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Linux/macOS:
source venv/bin/activate
# On Windows:
venv\Scripts\activate

# Upgrade pip
pip install --upgrade pip

# Install package in development mode
pip install -e ".[dev]"
```

## Step 3: Configure Environment Variables

```bash
# Copy example environment file
cp .env.example .env

# Edit .env with your settings
nano .env  # or use your preferred editor
```

### Required Environment Variables
```bash
# .env file
# API Keys
HDX_API_KEY=your_hdx_api_key_here
ACLED_API_KEY=your_acled_api_key_here
ACLED_EMAIL=<EMAIL>

# Database (if using)
DATABASE_URL=postgresql://user:password@localhost/yemen_market

# Logging
LOG_LEVEL=DEBUG
LOG_FILE=logs/development.log

# Performance
POLARS_MAX_THREADS=8
NUMBA_NUM_THREADS=8

# Optional: Monitoring
SENTRY_DSN=your_sentry_dsn_here
```

## Step 4: Install Additional Tools

### Development Dependencies

```bash
# Install development tools
pip install -e ".[dev]"

# This includes:
# - pytest: Testing framework
# - pytest-cov: Coverage reporting
# - black: Code formatter
# - ruff: Fast Python linter
# - mypy: Type checking
# - pre-commit: Git hooks
# - jupyterlab: Notebook environment
```

### Pre-commit Hooks

```bash
# Install pre-commit hooks
pre-commit install

# Run hooks manually
pre-commit run --all-files

# Update hooks
pre-commit autoupdate
```

### Optional: GPU Support

```bash
# For GPU acceleration (NVIDIA)
pip install cupy-cuda11x  # Replace with your CUDA version
pip install jax[cuda]
```

## Step 5: Verify Installation

### Run Test Suite

```bash
# Run all tests
make test

# Or using pytest directly
pytest tests/ -v

# Run with coverage
make test-coverage
```

### Check Imports

```python
# test_imports.py
import yemen_market
from yemen_market.data import PanelBuilder
from yemen_market.models.three_tier import ThreeTierRunner
from yemen_market.utils.logging import setup_logging

print("All imports successful!")
print(f"Yemen Market version: {yemen_market.__version__}")
```

### Run Example Analysis

```bash
# Run a simple analysis to verify everything works
python scripts/run_analysis.py --test-mode
```

## Step 6: IDE Configuration

### VS Code

1. Install Python extension
2. Create `.vscode/settings.json`:

```json
{
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.ruffEnabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "python.testing.pytestArgs": [
        "tests"
    ],
    "editor.formatOnSave": true,
    "editor.rulers": [88],
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".pytest_cache": true,
        "htmlcov": true
    }
}
```

### PyCharm

1. Open project in PyCharm
2. Configure interpreter: File → Settings → Project → Python Interpreter
3. Select the virtual environment: `./venv/bin/python`
4. Enable pytest: Settings → Tools → Python Integrated Tools → Testing → pytest
5. Configure code style: Settings → Editor → Code Style → Python → Set from → Black

## Step 7: Database Setup (Optional)

If using PostgreSQL for data persistence:

```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib  # Ubuntu
brew install postgresql  # macOS

# Create database
createdb yemen_market

# Run migrations
python scripts/setup_database.py
```

## Step 8: Download Initial Data

```bash
# Download required datasets
python scripts/data_collection/download_data.py --all

# This will download:
# - WFP price data
# - ACLED conflict data
# - Administrative boundaries
# - Exchange rate data
```

## Common Setup Issues

### Issue: Missing System Dependencies

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install python3-dev build-essential libssl-dev libffi-dev

# macOS (with Homebrew)
brew install python3 openssl libffi

# Windows
# Install Visual Studio Build Tools
```

### Issue: Pandas/NumPy Installation Errors

```bash
# Install binary wheels
pip install --only-binary :all: pandas numpy

# Or use conda
conda install pandas numpy scipy
```

### Issue: Permission Errors

```bash
# Fix permissions
chmod +x setup_venv.sh
chmod +x scripts/*.py

# Or run with python
python setup_venv.sh
```

### Issue: Memory Errors During Tests

```bash
# Limit test parallelism
pytest -n 2  # Use only 2 cores

# Or disable parallel testing
pytest tests/unit/  # Run unit tests only
```

## Development Workflow

### Daily Development

```bash
# Start your day
cd yemen-market-integration
source venv/bin/activate  # Activate environment
git pull origin main      # Get latest changes

# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and test
make test  # Run tests
make lint  # Check code style

# Commit changes
git add .
git commit -m "feat: Add your feature"
git push origin feature/your-feature-name
```

### Running Notebooks

```bash
# Start JupyterLab
jupyter lab

# Or Jupyter Notebook
jupyter notebook

# Navigate to notebooks/ directory
```

## Performance Optimization

### For Large Datasets

```bash
# Set memory limits
export POLARS_MAX_THREADS=4
export NUMEXPR_MAX_THREADS=4

# Use memory profiling
mprof run python your_script.py
mprof plot
```

### Parallel Processing

```python
# In your code
from yemen_market.utils.performance import parallel_config

# Configure parallelism
parallel_config(n_jobs=4, backend='threading')
```

## Troubleshooting

### Reset Environment

```bash
# Deactivate current environment
deactivate

# Remove old environment
rm -rf venv/

# Recreate
./setup_venv.sh
```

### Clear Caches

```bash
# Clear Python caches
find . -type d -name __pycache__ -exec rm -rf {} +
find . -type f -name "*.pyc" -delete

# Clear pytest cache
rm -rf .pytest_cache/

# Clear coverage data
rm -rf htmlcov/ .coverage
```

### Debug Installation

```bash
# Check installed packages
pip list

# Verify specific package
pip show yemen-market

# Check for conflicts
pip check
```

## Next Steps

Now that your environment is set up:

1. Read the [Python Style Guide](../coding-standards/python-style-guide.md)
2. Review the [Testing Guide](../testing/unit-testing.md)
3. Explore the [API Documentation](../../03-api-reference/)
4. Try the [Quick Start Tutorial](../../00-getting-started/quick-start.md)

## Getting Help

- Check [Common Issues](../debugging/common-issues.md)
- Search existing GitHub issues
- Ask on the development Slack channel
- Email: <EMAIL>