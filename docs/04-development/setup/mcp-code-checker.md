# MCP Code Checker Installation Guide

## Prerequisites
- Python 3.8+
- Git
<PERSON> Claude <PERSON> app

## Installation Steps

### 1. Run the Installation Script

From **outside** the yemen-market-integration directory:

```bash
cd ~/Documents/GitHub
./yemen-market-integration/setup_mcp_code_checker.sh
```

### 2. Manual Installation (Alternative)

If the script doesn't work, follow these manual steps:

```bash
# Navigate to GitHub directory
cd ~/Documents/GitHub

# Clone the repository
git clone https://github.com/mcpflow/mcp_server_code_checker_python.git
cd mcp_server_code_checker_python

# Create and activate virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -e .
```

### 3. Configure Claude Desktop

1. Open Claude Desktop configuration file:
   - **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

2. Add this configuration (adjust paths as needed):

```json
{
    "mcpServers": {
        "code_checker": {
            "command": "/Users/<USER>/Documents/GitHub/mcp_server_code_checker_python/.venv/bin/python",
            "args": [
                "/Users/<USER>/Documents/GitHub/mcp_server_code_checker_python/src/main.py",
                "--project-dir", "/Users/<USER>/Documents/GitHub/yemen-market-integration",
                "--python-executable", "/Users/<USER>/Documents/GitHub/yemen-market-integration/venv/bin/python",
                "--venv-path", "/Users/<USER>/Documents/GitHub/yemen-market-integration/venv"
            ],
            "env": {
                "PYTHONPATH": "/Users/<USER>/Documents/GitHub/mcp_server_code_checker_python"
            }
        }
    }
}
```

3. Restart Claude Desktop

## Usage

Once configured, you can use these commands in Claude:

- `run_pytest_check` - Run pytest and get AI-friendly analysis
- `run_pylint_check` - Run pylint for code quality
- `run_all_checks` - Run both pytest and pylint

## Troubleshooting

### Common Issues

1. **"Command not found"** - Ensure Python path is correct
2. **"Module not found"** - Check PYTHONPATH in configuration
3. **"Permission denied"** - Make sure script is executable: `chmod +x setup_mcp_code_checker.sh`

### Verify Installation

After setup, in Claude Desktop you should see:
- The MCP server icon in the connection status
- Ability to run the code checking commands

## Benefits for Yemen Market Integration

With MCP Code Checker installed, you can:
1. Automatically run pytest and get AI-powered test failure analysis
2. Quickly identify which tests need fixing for 100% coverage
3. Get smart suggestions for fixing failing tests
4. Run code quality checks alongside test coverage

## Next Steps

After installation:
1. Test the connection in Claude Desktop
2. Run `run_pytest_check` to analyze current test failures
3. Use the AI suggestions to fix failing tests
4. Monitor progress toward 100% coverage