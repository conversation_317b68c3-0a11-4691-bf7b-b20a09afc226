# Local Deployment Guide

**Target Audience**: <PERSON><PERSON><PERSON>, DevOps  
**Purpose**: Deploy Yemen Market Integration Platform locally for development and testing

## Overview

This guide covers deploying the platform locally for development, testing, and demonstration purposes. Local deployment helps developers test the full system before pushing to production.

## Prerequisites

### System Requirements

- **OS**: Ubuntu 20.04+, macOS 10.15+, or Windows 10 with WSL2
- **Python**: 3.9+
- **Docker**: 20.10+ (optional, for containerized deployment)
- **PostgreSQL**: 14+ (optional, for persistent storage)
- **Redis**: 6+ (optional, for caching)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 10GB free space

### Software Dependencies

```bash
# Check Python
python --version  # Should be 3.9+

# Check pip
pip --version

# Check Docker (optional)
docker --version
docker-compose --version

# Check PostgreSQL (optional)
psql --version

# Check Redis (optional)
redis-cli --version
```

## Deployment Options

### Option 1: Direct Python Deployment

Best for development and debugging.

#### Step 1: Clone and Setup

```bash
# Clone repository
git clone https://github.com/your-org/yemen-market-integration.git
cd yemen-market-integration

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -e ".[prod]"
```

#### Step 2: Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

Required environment variables:
```bash
# Application
APP_ENV=development
APP_HOST=localhost
APP_PORT=8000
LOG_LEVEL=INFO

# Database (SQLite for local)
DATABASE_URL=sqlite:///./local_data.db

# API Keys
HDX_API_KEY=your_hdx_key
ACLED_API_KEY=your_acled_key
ACLED_EMAIL=<EMAIL>

# Performance
WORKERS=4
THREADS=2
```

#### Step 3: Initialize Database

```bash
# Run database migrations
python scripts/setup_database.py

# Load sample data (optional)
python scripts/load_sample_data.py
```

#### Step 4: Start Services

```bash
# Start the application
python main.py

# Or using gunicorn
gunicorn -w 4 -b 0.0.0.0:8000 yemen_market.app:application

# Or using Make
make run-local
```

### Option 2: Docker Deployment

Best for testing production-like environment.

#### Step 1: Build Images

```bash
# Build application image
docker build -t yemen-market:latest .

# Or using docker-compose
docker-compose build
```

#### Step 2: Configure Docker Environment

Create `docker-compose.override.yml` for local settings:

```yaml
version: '3.8'

services:
  app:
    environment:
      - APP_ENV=development
      - LOG_LEVEL=DEBUG
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    ports:
      - "8000:8000"
      
  postgres:
    ports:
      - "5432:5432"
      
  redis:
    ports:
      - "6379:6379"
```

#### Step 3: Start Services

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop services
docker-compose down
```

### Option 3: Development with Hot Reload

Best for active development.

#### Using Flask Development Server

```python
# run_dev.py
from yemen_market.app import create_app

app = create_app(config='development')

if __name__ == '__main__':
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True
    )
```

```bash
# Run with hot reload
python run_dev.py
```

#### Using Jupyter Kernel

```bash
# Start Jupyter with app context
jupyter lab --NotebookApp.kernel_name=yemen_market
```

## Service Configuration

### PostgreSQL Setup (Optional)

```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib  # Ubuntu
brew install postgresql  # macOS

# Create database and user
sudo -u postgres psql
CREATE DATABASE yemen_market;
CREATE USER yemen_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE yemen_market TO yemen_user;
\q

# Update .env
DATABASE_URL=postgresql://yemen_user:secure_password@localhost/yemen_market
```

### Redis Setup (Optional)

```bash
# Install Redis
sudo apt-get install redis-server  # Ubuntu
brew install redis  # macOS

# Start Redis
redis-server

# Test connection
redis-cli ping  # Should return PONG

# Update .env
REDIS_URL=redis://localhost:6379/0
```

### Nginx Reverse Proxy (Optional)

```nginx
# /etc/nginx/sites-available/yemen-market
server {
    listen 80;
    server_name localhost;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static {
        alias /path/to/yemen-market/static;
        expires 30d;
    }
    
    location /data {
        alias /path/to/yemen-market/data;
        autoindex on;
    }
}
```

## Data Setup

### Download Initial Data

```bash
# Download all required datasets
python scripts/data_collection/download_all_data.py

# Download specific datasets
python scripts/data_collection/download_wfp_prices.py
python scripts/data_collection/download_acled_events.py
python scripts/data_collection/download_boundaries.py
```

### Create Sample Dataset

```python
# scripts/create_sample_data.py
import pandas as pd
from yemen_market.data import TestDataFactory

# Create sample panel data
factory = TestDataFactory()
sample_data = factory.create_panel_data(
    n_markets=10,
    n_commodities=5,
    n_periods=52,
    start_date='2023-01-01'
)

# Save to data directory
sample_data.to_parquet('data/sample/panel_data.parquet')
print(f"Created sample data with {len(sample_data)} observations")
```

## Running the Application

### Command Line Interface

```bash
# Run analysis
python -m yemen_market analyze \
    --input data/raw/prices.csv \
    --output results/analysis.json \
    --config config/analysis.yaml

# Process data
python -m yemen_market process \
    --source hdx \
    --dataset wfp-food-prices \
    --output data/processed/

# Train models
python -m yemen_market train \
    --data data/processed/panel.parquet \
    --model three-tier \
    --output models/
```

### Web Interface

```bash
# Start web server
python -m yemen_market.web

# Access at http://localhost:8000
```

### API Endpoints

```bash
# Health check
curl http://localhost:8000/health

# Run analysis
curl -X POST http://localhost:8000/api/v1/analyze \
    -H "Content-Type: application/json" \
    -d '{
        "data_source": "local",
        "file_path": "data/sample/panel_data.parquet",
        "analysis_type": "market_integration"
    }'

# Get results
curl http://localhost:8000/api/v1/results/job_123
```

## Monitoring and Logs

### Log Files

```bash
# Application logs
tail -f logs/app.log

# Access logs
tail -f logs/access.log

# Error logs
tail -f logs/error.log
```

### Log Configuration

```yaml
# config/logging.yaml
version: 1
formatters:
  default:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

handlers:
  console:
    class: logging.StreamHandler
    formatter: default
    stream: ext://sys.stdout
    
  file:
    class: logging.handlers.RotatingFileHandler
    formatter: default
    filename: logs/app.log
    maxBytes: 10485760  # 10MB
    backupCount: 5

root:
  level: INFO
  handlers: [console, file]

loggers:
  yemen_market:
    level: DEBUG
  sqlalchemy:
    level: WARNING
```

### Performance Monitoring

```python
# Enable performance monitoring
from yemen_market.utils.monitoring import setup_monitoring

setup_monitoring(
    enable_profiling=True,
    enable_memory_tracking=True,
    profile_output='logs/performance.prof'
)
```

## Testing the Deployment

### Smoke Tests

```bash
# Run smoke tests
python scripts/smoke_tests.py

# Or using pytest
pytest tests/smoke/ -v
```

Example smoke test:
```python
# tests/smoke/test_deployment.py
import requests
import pandas as pd

def test_api_health():
    """Test API is responding."""
    response = requests.get("http://localhost:8000/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

def test_data_pipeline():
    """Test data pipeline is working."""
    # Load sample data
    data = pd.read_csv("data/sample/test_prices.csv")
    
    # Process through pipeline
    from yemen_market.data import DataPipeline
    pipeline = DataPipeline()
    result = pipeline.process(data)
    
    assert len(result) > 0
    assert "price_cleaned" in result.columns
```

### Load Testing

```bash
# Install locust
pip install locust

# Run load tests
locust -f tests/load/locustfile.py --host=http://localhost:8000
```

## Troubleshooting

### Common Issues

#### Port Already in Use

```bash
# Find process using port
lsof -i :8000  # macOS/Linux
netstat -ano | findstr :8000  # Windows

# Kill process
kill -9 <PID>
```

#### Database Connection Errors

```bash
# Test database connection
python -c "
from sqlalchemy import create_engine
engine = create_engine('your_database_url')
engine.connect()
print('Connection successful!')
"

# Reset database
python scripts/reset_database.py
```

#### Memory Issues

```bash
# Limit memory usage
export MEMORY_LIMIT=4G

# Or in Docker
docker run -m 4g yemen-market:latest
```

### Debug Mode

```python
# Enable debug mode
# .env
DEBUG=True
LOG_LEVEL=DEBUG
EXPLAIN_QUERIES=True

# In code
from yemen_market.utils.debug import enable_debug_mode
enable_debug_mode()
```

## Development Workflow

### Hot Reload Setup

```python
# watchdog_runner.py
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import subprocess
import os

class ReloadHandler(FileSystemEventHandler):
    def __init__(self):
        self.process = None
        self.start_app()
    
    def start_app(self):
        if self.process:
            self.process.terminate()
        self.process = subprocess.Popen(['python', 'main.py'])
    
    def on_modified(self, event):
        if event.src_path.endswith('.py'):
            print(f"Detected change in {event.src_path}")
            self.start_app()

if __name__ == "__main__":
    handler = ReloadHandler()
    observer = Observer()
    observer.schedule(handler, path='src/', recursive=True)
    observer.start()
    
    try:
        observer.join()
    except KeyboardInterrupt:
        observer.stop()
        handler.process.terminate()
```

### Database Migrations

```bash
# Create migration
alembic revision -m "Add price index table"

# Run migrations
alembic upgrade head

# Rollback
alembic downgrade -1
```

## Security Considerations

### Local Development Security

1. **Never commit .env files**
2. **Use different credentials for local/production**
3. **Restrict database access to localhost**
4. **Use HTTPS even locally for testing**

### SSL Certificate for Local HTTPS

```bash
# Generate self-signed certificate
openssl req -x509 -newkey rsa:4096 -nodes -out cert.pem -keyout key.pem -days 365

# Run with HTTPS
gunicorn -w 4 -b 0.0.0.0:8443 --certfile=cert.pem --keyfile=key.pem app:application
```

## Next Steps

- Deploy to production: See [Production Guide](production-guide.md)
- Setup Docker: See [Docker Deployment](docker-deployment.md)
- Configure monitoring: See [Monitoring Guide](monitoring.md)
- Run tests: See [Testing Guide](../testing/integration-testing.md)