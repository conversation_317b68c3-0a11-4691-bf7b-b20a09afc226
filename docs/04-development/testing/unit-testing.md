# Unit Testing Guide

**Target Audience**: Developers  
**Purpose**: Best practices for writing effective unit tests

## Overview

Unit tests are the foundation of our testing strategy. They test individual components in isolation, ensuring each function, method, and class works correctly. This guide covers how to write, organize, and maintain unit tests for the Yemen Market Integration Platform.

## Testing Philosophy

### Principles

1. **Test Behavior, Not Implementation**: Focus on what the code does, not how
2. **One Assertion Per Test**: Each test should verify one specific behavior
3. **Fast and Independent**: Tests should run quickly and not depend on each other
4. **Readable and Maintainable**: Tests are documentation
5. **Deterministic**: Same input always produces same output

### Test Coverage Goals

- **Minimum**: 90% line coverage
- **Target**: 95% line coverage
- **Critical paths**: 100% coverage
- **Focus on**: Branch coverage, not just line coverage

## Test Organization

### Directory Structure

```
tests/
├── unit/
│   ├── __init__.py
│   ├── conftest.py          # Shared fixtures
│   ├── test_<module>.py     # Mirror src structure
│   ├── data/
│   │   ├── test_panel_builder.py
│   │   ├── test_processors.py
│   │   └── test_validators.py
│   ├── models/
│   │   ├── test_base_model.py
│   │   ├── three_tier/
│   │   │   ├── test_tier1_pooled.py
│   │   │   ├── test_tier2_commodity.py
│   │   │   └── test_tier3_validation.py
│   │   └── test_model_comparison.py
│   └── utils/
│       ├── test_logging.py
│       └── test_performance.py
├── fixtures/
│   ├── sample_data.py
│   └── mock_responses.py
└── helpers/
    ├── assertions.py
    └── data_generators.py
```

### Naming Conventions

```python
# Test file names match source files
# src/yemen_market/data/panel_builder.py
# tests/unit/data/test_panel_builder.py

# Test class names
class TestPanelBuilder:
    """Test PanelBuilder class."""
    
class TestPanelBuilderEdgeCases:
    """Test PanelBuilder edge cases."""

# Test method names describe behavior
def test_creates_balanced_panel_with_minimum_coverage():
    """Test that balanced panel meets minimum coverage requirement."""
    
def test_raises_error_when_data_is_empty():
    """Test that empty data raises appropriate error."""
    
def test_handles_missing_values_correctly():
    """Test missing value handling follows specified method."""
```

## Writing Effective Tests

### Basic Test Structure

```python
import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

from yemen_market.data import PanelBuilder
from yemen_market.exceptions import DataValidationError


class TestPanelBuilder:
    """Test PanelBuilder functionality."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample panel data for testing."""
        return pd.DataFrame({
            'market_id': ['M1', 'M1', 'M2', 'M2'],
            'commodity': ['wheat', 'wheat', 'wheat', 'wheat'],
            'date': pd.date_range('2023-01-01', periods=4, freq='D'),
            'price': [100, 102, 98, 101]
        })
    
    @pytest.fixture
    def builder(self):
        """Create PanelBuilder instance."""
        return PanelBuilder(min_markets=2, min_periods=2)
    
    def test_initialization_with_defaults(self):
        """Test PanelBuilder initializes with default parameters."""
        # Arrange & Act
        builder = PanelBuilder()
        
        # Assert
        assert builder.min_markets == 10
        assert builder.min_periods == 30
        assert builder.interpolation_method == 'linear'
    
    def test_create_panel_returns_dataframe(self, builder, sample_data):
        """Test create_panel returns DataFrame with expected structure."""
        # Act
        result = builder.create_panel(sample_data)
        
        # Assert
        assert isinstance(result, pd.DataFrame)
        assert 'market_id' in result.columns
        assert 'commodity' in result.columns
        assert len(result) > 0
    
    def test_validates_required_columns(self, builder):
        """Test validation raises error for missing columns."""
        # Arrange
        invalid_data = pd.DataFrame({'price': [1, 2, 3]})
        
        # Act & Assert
        with pytest.raises(DataValidationError) as exc_info:
            builder.create_panel(invalid_data)
        
        assert "Missing required columns" in str(exc_info.value)
```

### Testing Different Scenarios

```python
class TestPriceCalculations:
    """Test price calculation methods."""
    
    def test_calculate_returns_with_normal_prices(self):
        """Test return calculation with normal price series."""
        # Arrange
        prices = pd.Series([100, 110, 121])
        expected = pd.Series([np.nan, 0.1, 0.1])
        
        # Act
        returns = calculate_returns(prices)
        
        # Assert
        pd.testing.assert_series_equal(returns, expected)
    
    def test_calculate_returns_with_zero_prices(self):
        """Test return calculation handles zero prices."""
        # Arrange
        prices = pd.Series([100, 0, 110])
        
        # Act
        returns = calculate_returns(prices, handle_zeros='drop')
        
        # Assert
        assert len(returns) == 2  # Zero price dropped
        assert not np.any(np.isinf(returns))
    
    @pytest.mark.parametrize("method,expected", [
        ('simple', [np.nan, 0.1, 0.1]),
        ('log', [np.nan, 0.0953, 0.0953]),
        ('geometric', [np.nan, 0.1, 0.1])
    ])
    def test_different_return_methods(self, method, expected):
        """Test different return calculation methods."""
        # Arrange
        prices = pd.Series([100, 110, 121])
        
        # Act
        returns = calculate_returns(prices, method=method)
        
        # Assert
        np.testing.assert_array_almost_equal(
            returns.dropna(),
            expected[1:],
            decimal=4
        )
```

### Fixtures and Setup

```python
# conftest.py - Shared fixtures
import pytest
import pandas as pd
import tempfile
from pathlib import Path

@pytest.fixture(scope='session')
def sample_panel_data():
    """Create comprehensive sample panel data."""
    np.random.seed(42)
    
    markets = ['SANA', 'ADEN', 'TAIZ', 'HODEIDAH']
    commodities = ['wheat', 'rice', 'sugar']
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='W')
    
    data = []
    for market in markets:
        for commodity in commodities:
            for date in dates:
                price = 100 + np.random.normal(0, 10)
                data.append({
                    'market_id': market,
                    'commodity': commodity,
                    'date': date,
                    'price': max(price, 10)  # Ensure positive
                })
    
    return pd.DataFrame(data)

@pytest.fixture
def temp_data_dir():
    """Create temporary directory for test data."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)

@pytest.fixture
def mock_api_client():
    """Create mock API client for testing."""
    client = Mock()
    client.fetch_data.return_value = {'status': 'success', 'data': []}
    return client
```

### Mocking External Dependencies

```python
class TestHDXClient:
    """Test HDX API client."""
    
    @patch('requests.get')
    def test_fetch_dataset_success(self, mock_get):
        """Test successful dataset fetch from HDX."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'success': True,
            'result': {'id': '123', 'name': 'yemen-prices'}
        }
        mock_get.return_value = mock_response
        
        client = HDXClient(api_key='test-key')
        
        # Act
        result = client.fetch_dataset('yemen-prices')
        
        # Assert
        assert result['id'] == '123'
        mock_get.assert_called_once()
        assert 'test-key' in mock_get.call_args[1]['headers']['X-CKAN-API-Key']
    
    @patch('requests.get')
    def test_fetch_dataset_handles_errors(self, mock_get):
        """Test error handling for failed requests."""
        # Arrange
        mock_get.side_effect = requests.RequestException("Network error")
        client = HDXClient(api_key='test-key')
        
        # Act & Assert
        with pytest.raises(DataFetchError) as exc_info:
            client.fetch_dataset('yemen-prices')
        
        assert "Failed to fetch dataset" in str(exc_info.value)
```

### Testing Edge Cases

```python
class TestEdgeCases:
    """Test edge cases and boundary conditions."""
    
    def test_empty_dataframe_handling(self):
        """Test behavior with empty DataFrame."""
        # Arrange
        empty_df = pd.DataFrame()
        processor = DataProcessor()
        
        # Act
        result = processor.process(empty_df)
        
        # Assert
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 0
        assert not processor.is_processed
    
    def test_single_observation_panel(self):
        """Test panel with only one observation."""
        # Arrange
        single_obs = pd.DataFrame({
            'market_id': ['M1'],
            'date': ['2023-01-01'],
            'price': [100]
        })
        
        # Act & Assert
        with pytest.warns(UserWarning, match="Insufficient data"):
            result = create_panel(single_obs)
        
        assert len(result) == 1
    
    def test_extreme_values(self):
        """Test handling of extreme values."""
        # Arrange
        data = pd.DataFrame({
            'price': [1e-10, 1e10, np.inf, -np.inf, np.nan]
        })
        
        # Act
        cleaned = clean_extreme_values(data)
        
        # Assert
        assert not np.any(np.isinf(cleaned['price']))
        assert cleaned['price'].min() > 0
        assert cleaned['price'].max() < 1e6
```

### Property-Based Testing

```python
from hypothesis import given, strategies as st
from hypothesis.extra.pandas import data_frames, column

class TestPropertyBased:
    """Property-based tests using Hypothesis."""
    
    @given(
        df=data_frames(
            columns=[
                column('price', dtype=float, 
                      elements=st.floats(min_value=1, max_value=1000)),
                column('quantity', dtype=float,
                      elements=st.floats(min_value=0, max_value=100))
            ],
            min_size=10
        )
    )
    def test_calculate_value_properties(self, df):
        """Test value calculation maintains properties."""
        # Act
        df['value'] = calculate_value(df['price'], df['quantity'])
        
        # Assert - Properties that should always hold
        assert (df['value'] >= 0).all()
        assert (df['value'] <= df['price'] * df['quantity'] * 1.001).all()
        assert df['value'].isna().sum() == 0
    
    @given(st.lists(st.floats(min_value=1, max_value=1000), min_size=2))
    def test_price_index_bounds(self, prices):
        """Test price index stays within reasonable bounds."""
        # Act
        index = calculate_price_index(prices)
        
        # Assert
        assert 0 < index.min() < 10
        assert index.max() < 10
        assert len(index) == len(prices)
```

## Testing Patterns

### Arrange-Act-Assert (AAA)

```python
def test_model_prediction():
    """Test model makes accurate predictions."""
    # Arrange
    training_data = create_training_data()
    model = ThresholdModel(n_regimes=2)
    model.fit(training_data)
    test_input = create_test_input()
    
    # Act
    prediction = model.predict(test_input)
    
    # Assert
    assert prediction.shape == (len(test_input), 1)
    assert np.all(prediction > 0)
    assert np.mean(prediction) < 1000
```

### Test Data Builders

```python
# tests/helpers/data_generators.py
class PanelDataBuilder:
    """Builder for creating test panel data."""
    
    def __init__(self):
        self.markets = ['M1']
        self.commodities = ['wheat']
        self.periods = 10
        self.start_date = '2023-01-01'
        self.base_price = 100
        
    def with_markets(self, markets):
        self.markets = markets
        return self
        
    def with_commodities(self, commodities):
        self.commodities = commodities
        return self
        
    def with_missing_data(self, fraction=0.1):
        self.missing_fraction = fraction
        return self
        
    def build(self):
        """Build panel data with specified characteristics."""
        # Implementation...
        return pd.DataFrame(data)

# Usage in tests
def test_with_multiple_markets():
    """Test handling of multiple markets."""
    # Arrange
    data = (PanelDataBuilder()
            .with_markets(['M1', 'M2', 'M3'])
            .with_commodities(['wheat', 'rice'])
            .with_periods(50)
            .build())
    
    # Act & Assert...
```

### Testing Exceptions

```python
def test_invalid_input_raises_exception():
    """Test appropriate exception for invalid input."""
    # Arrange
    invalid_data = "not a dataframe"
    
    # Act & Assert
    with pytest.raises(TypeError) as exc_info:
        process_data(invalid_data)
    
    assert "Expected DataFrame" in str(exc_info.value)
    assert exc_info.type == TypeError

def test_specific_error_conditions():
    """Test specific error conditions are handled."""
    # Test multiple error conditions
    test_cases = [
        (None, TypeError, "cannot be None"),
        (pd.DataFrame(), ValueError, "empty DataFrame"),
        (pd.DataFrame({'a': [1]}), KeyError, "Missing required columns")
    ]
    
    for input_data, expected_error, expected_message in test_cases:
        with pytest.raises(expected_error) as exc_info:
            process_data(input_data)
        assert expected_message in str(exc_info.value)
```

## Test Performance

### Fast Tests

```python
# Mark slow tests
@pytest.mark.slow
def test_large_dataset_processing():
    """Test processing of large datasets."""
    # This test takes > 1 second
    data = generate_large_dataset(1000000)
    result = process_data(data)
    assert len(result) > 0

# Run fast tests only
# pytest -m "not slow"
```

### Test Optimization

```python
# Use class-level fixtures for expensive setup
class TestExpensiveModel:
    """Test expensive model operations."""
    
    @classmethod
    def setup_class(cls):
        """Set up expensive resources once."""
        cls.model = train_expensive_model()
        cls.test_data = load_test_data()
    
    def test_prediction_accuracy(self):
        """Test model prediction accuracy."""
        predictions = self.model.predict(self.test_data)
        assert accuracy(predictions) > 0.9
    
    def test_prediction_speed(self):
        """Test prediction speed meets requirements."""
        import time
        start = time.time()
        self.model.predict(self.test_data[:100])
        elapsed = time.time() - start
        assert elapsed < 0.1  # Should be fast
```

## Continuous Integration

### pytest Configuration

```ini
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -ra
    --strict-markers
    --cov=yemen_market
    --cov-report=term-missing
    --cov-report=html
    --cov-report=xml
    --cov-fail-under=90
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
```

### Running Tests

```bash
# Run all unit tests
pytest tests/unit/

# Run with coverage
pytest --cov=yemen_market tests/

# Run specific test file
pytest tests/unit/test_panel_builder.py

# Run specific test
pytest tests/unit/test_panel_builder.py::TestPanelBuilder::test_creates_balanced_panel

# Run tests matching pattern
pytest -k "price_calculation"

# Run with verbose output
pytest -vv

# Run in parallel
pytest -n auto

# Generate HTML coverage report
pytest --cov=yemen_market --cov-report=html
# Open htmlcov/index.html
```

## Best Practices

### Do's

1. **Write tests first** (TDD when possible)
2. **Keep tests simple** and focused
3. **Use descriptive test names**
4. **Test edge cases** and error conditions
5. **Mock external dependencies**
6. **Use fixtures** for common setup
7. **Maintain test data** separately
8. **Run tests frequently** during development

### Don'ts

1. **Don't test implementation details**
2. **Don't write brittle tests** that break with refactoring
3. **Don't ignore test failures**
4. **Don't use production data** in tests
5. **Don't test third-party libraries**
6. **Don't write tests that depend on test order**
7. **Don't use hard-coded paths** or system-specific values

## Debugging Tests

### Using pytest debugger

```python
def test_complex_calculation():
    """Test complex calculation with debugging."""
    data = prepare_complex_data()
    
    # Drop into debugger
    import pdb; pdb.set_trace()
    
    result = complex_calculation(data)
    assert result == expected

# Or use pytest's --pdb flag
# pytest --pdb tests/unit/test_calculations.py
```

### Verbose Test Output

```bash
# Show print statements
pytest -s

# Show local variables on failure
pytest -l

# Show full diff
pytest -vv

# Combination
pytest -svvl
```

## Test Maintenance

### Refactoring Tests

```python
# Before: Repetitive tests
def test_calculate_mean_positive():
    assert calculate_mean([1, 2, 3]) == 2

def test_calculate_mean_negative():
    assert calculate_mean([-1, -2, -3]) == -2

# After: Parameterized test
@pytest.mark.parametrize("values,expected", [
    ([1, 2, 3], 2),
    ([-1, -2, -3], -2),
    ([0, 0, 0], 0),
    ([1.5, 2.5], 2.0)
])
def test_calculate_mean(values, expected):
    assert calculate_mean(values) == expected
```

### Keeping Tests Updated

1. **Update tests when requirements change**
2. **Remove obsolete tests**
3. **Refactor tests when code is refactored**
4. **Review test coverage regularly**
5. **Update test data periodically**

## Resources

- [pytest Documentation](https://docs.pytest.org/)
- [Python Testing 101](https://realpython.com/python-testing/)
- [Effective Python Testing](https://testdriven.io/)
- [Property-based Testing](https://hypothesis.works/)