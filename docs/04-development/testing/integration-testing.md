# Integration Testing Guide

**Target Audience**: <PERSON><PERSON><PERSON>, QA Engineers  
**Purpose**: Best practices for integration testing of the Yemen Market Integration Platform

## Overview

Integration tests verify that different components of the system work together correctly. Unlike unit tests that test components in isolation, integration tests ensure that data flows properly through the system and that interfaces between modules function as expected.

## Integration Test Scope

### What to Test

1. **Data Pipeline Integration**: Data flows from sources through processing to storage
2. **Model Integration**: Models work with real data structures
3. **API Integration**: External APIs are called correctly
4. **Database Integration**: Data persistence and retrieval
5. **Feature Integration**: End-to-end feature functionality

### Test Boundaries

```
┌─────────────────────────────────────────────┐
│          Integration Test Scope             │
│                                             │
│  ┌─────────┐    ┌──────────┐    ┌────────┐│
│  │  Data   │───▶│Processing│───▶│ Models ││
│  │ Sources │    │ Pipeline │    │        ││
│  └─────────┘    └──────────┘    └────────┘│
│       ▲              │               │      │
│       │              ▼               ▼      │
│  ┌─────────┐    ┌──────────┐    ┌────────┐│
│  │External │    │   Data   │    │Results ││
│  │  APIs   │    │  Store   │    │Output  ││
│  └─────────┘    └──────────┘    └────────┘│
└─────────────────────────────────────────────┘
```

## Test Organization

### Directory Structure

```
tests/
├── integration/
│   ├── __init__.py
│   ├── conftest.py              # Integration test fixtures
│   ├── test_data_pipeline.py    # Data flow tests
│   ├── test_model_pipeline.py   # Model integration tests
│   ├── test_api_integration.py  # External API tests
│   ├── test_full_workflow.py    # End-to-end workflows
│   └── fixtures/
│       ├── test_database.py     # Test database setup
│       └── mock_services.py     # Service mocks
```

## Writing Integration Tests

### Basic Integration Test

```python
import pytest
import pandas as pd
from pathlib import Path

from yemen_market.data import HDXClient, WFPProcessor, PanelBuilder
from yemen_market.models.three_tier import ThreeTierRunner


class TestDataPipelineIntegration:
    """Test data pipeline from source to panel creation."""
    
    @pytest.fixture
    def hdx_client(self, mock_hdx_api):
        """Create HDX client with mocked API."""
        return HDXClient(api_key="test-key")
    
    @pytest.fixture
    def temp_data_dir(self, tmp_path):
        """Create temporary data directory."""
        data_dir = tmp_path / "data"
        data_dir.mkdir()
        return data_dir
    
    def test_hdx_to_panel_pipeline(self, hdx_client, temp_data_dir):
        """Test complete pipeline from HDX download to panel creation."""
        # Arrange
        dataset_id = "wfp-food-prices-yemen"
        
        # Act - Download data
        hdx_client.download_dataset(dataset_id, temp_data_dir)
        
        # Act - Process data
        processor = WFPProcessor()
        processed_data = processor.process_directory(temp_data_dir)
        
        # Act - Create panel
        builder = PanelBuilder()
        panel = builder.create_panel(processed_data)
        
        # Assert
        assert isinstance(panel, pd.DataFrame)
        assert len(panel) > 0
        assert all(col in panel.columns for col in [
            'market_id', 'commodity', 'date', 'price'
        ])
        assert panel['price'].notna().sum() > 0.9 * len(panel)
```

### Database Integration Tests

```python
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from yemen_market.data.models import Base, Market, Price
from yemen_market.data.repository import MarketRepository


class TestDatabaseIntegration:
    """Test database operations."""
    
    @pytest.fixture
    def test_db(self):
        """Create test database."""
        engine = create_engine("sqlite:///:memory:")
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        yield session
        session.close()
    
    @pytest.fixture
    def market_repo(self, test_db):
        """Create market repository."""
        return MarketRepository(test_db)
    
    def test_save_and_retrieve_market_data(self, market_repo):
        """Test saving and retrieving market data."""
        # Arrange
        market = Market(
            id="YE_SANA",
            name="Sana'a",
            region="North",
            lat=15.3694,
            lon=44.1910
        )
        
        # Act - Save
        market_repo.save(market)
        
        # Act - Retrieve
        retrieved = market_repo.get_by_id("YE_SANA")
        
        # Assert
        assert retrieved is not None
        assert retrieved.name == "Sana'a"
        assert retrieved.lat == 15.3694
    
    def test_bulk_price_insert_performance(self, market_repo, test_db):
        """Test bulk insertion performance."""
        # Arrange
        prices = []
        for i in range(10000):
            prices.append(Price(
                market_id="YE_SANA",
                commodity="wheat",
                date=f"2023-01-{(i % 30) + 1:02d}",
                price=100 + i % 10
            ))
        
        # Act
        import time
        start = time.time()
        market_repo.bulk_insert_prices(prices)
        elapsed = time.time() - start
        
        # Assert
        assert elapsed < 1.0  # Should be fast
        count = test_db.query(Price).count()
        assert count == 10000
```

### External API Integration

```python
import pytest
import responses
import requests

from yemen_market.data import ACLEDClient


class TestACLEDIntegration:
    """Test ACLED API integration."""
    
    @pytest.fixture
    def acled_client(self):
        """Create ACLED client."""
        return ACLEDClient(
            api_key="test-key",
            email="<EMAIL>"
        )
    
    @responses.activate
    def test_fetch_conflict_data(self, acled_client):
        """Test fetching conflict data from ACLED."""
        # Arrange - Mock API response
        responses.add(
            responses.GET,
            "https://api.acleddata.com/acled/read",
            json={
                "status": 200,
                "success": True,
                "data": [
                    {
                        "event_id_cnty": "YEM12345",
                        "event_date": "2023-01-15",
                        "event_type": "Battles",
                        "admin1": "Sana'a",
                        "latitude": "15.3694",
                        "longitude": "44.1910",
                        "fatalities": "2"
                    }
                ]
            },
            status=200
        )
        
        # Act
        events = acled_client.fetch_events(
            country="Yemen",
            start_date="2023-01-01",
            end_date="2023-01-31"
        )
        
        # Assert
        assert len(events) == 1
        assert events[0]["event_type"] == "Battles"
        assert events[0]["admin1"] == "Sana'a"
    
    @pytest.mark.integration
    def test_real_api_connection(self, acled_client):
        """Test real API connection (requires valid credentials)."""
        # Skip if no real credentials
        if not os.getenv("ACLED_API_KEY"):
            pytest.skip("No ACLED API credentials available")
        
        # Act
        events = acled_client.fetch_events(
            country="Yemen",
            start_date="2023-01-01",
            end_date="2023-01-01",
            limit=1
        )
        
        # Assert
        assert isinstance(events, list)
        if events:  # API might return empty for specific date
            assert "event_id_cnty" in events[0]
```

### Model Integration Tests

```python
class TestModelIntegration:
    """Test model integration with real data structures."""
    
    @pytest.fixture
    def prepared_data(self, sample_panel_data):
        """Prepare data for model testing."""
        from yemen_market.features import FeatureEngineer
        
        engineer = FeatureEngineer()
        return engineer.fit_transform(sample_panel_data)
    
    def test_three_tier_model_workflow(self, prepared_data):
        """Test complete three-tier model workflow."""
        # Arrange
        config = {
            'tier1_config': {
                'outcome_var': 'log_price',
                'treatment_var': 'conflict_intensity',
                'fixed_effects': ['market_id', 'commodity']
            },
            'tier2_config': {
                'commodities': ['wheat', 'rice'],
                'max_lags': 4
            },
            'tier3_config': {
                'n_factors': 3
            }
        }
        
        # Act
        runner = ThreeTierRunner(config)
        results = runner.run_all_tiers(prepared_data)
        
        # Assert - Tier 1
        assert hasattr(results, 'tier1_results')
        assert results.tier1_results.coefficients is not None
        assert 'conflict_intensity' in results.tier1_results.coefficients.index
        
        # Assert - Tier 2
        assert hasattr(results, 'tier2_results')
        assert 'wheat' in results.tier2_results
        assert results.tier2_results['wheat'].threshold_value > 0
        
        # Assert - Tier 3
        assert hasattr(results, 'tier3_results')
        assert results.tier3_results.n_factors == 3
        assert results.tier3_results.variance_explained.sum() > 0.5
```

### End-to-End Feature Tests

```python
class TestEndToEndFeatures:
    """Test complete features from input to output."""
    
    def test_market_integration_analysis_workflow(self, temp_data_dir):
        """Test complete market integration analysis."""
        # Arrange - Setup test data
        input_file = temp_data_dir / "test_prices.csv"
        create_test_price_data(input_file)
        
        # Act - Run complete analysis
        from yemen_market.pipelines import run_analysis
        
        results = run_analysis(
            input_file=input_file,
            output_dir=temp_data_dir,
            config={
                'analysis_type': 'market_integration',
                'time_period': '2023',
                'commodities': ['wheat', 'rice']
            }
        )
        
        # Assert - Check outputs
        assert results['status'] == 'success'
        assert (temp_data_dir / 'integration_report.html').exists()
        assert (temp_data_dir / 'results.json').exists()
        
        # Assert - Validate results content
        import json
        with open(temp_data_dir / 'results.json') as f:
            result_data = json.load(f)
        
        assert 'integration_scores' in result_data
        assert 'model_diagnostics' in result_data
        assert result_data['integration_scores']['wheat'] > 0
```

## Testing Strategies

### Test Data Management

```python
# tests/integration/fixtures/test_data_factory.py
class TestDataFactory:
    """Factory for creating test data."""
    
    @staticmethod
    def create_price_series(
        market: str,
        commodity: str,
        start_date: str,
        periods: int,
        base_price: float = 100,
        volatility: float = 0.1,
        trend: float = 0.001
    ) -> pd.DataFrame:
        """Create realistic price series for testing."""
        dates = pd.date_range(start_date, periods=periods, freq='W')
        
        # Generate prices with trend and noise
        prices = []
        current_price = base_price
        
        for i, date in enumerate(dates):
            # Add trend
            current_price *= (1 + trend)
            
            # Add random walk
            change = np.random.normal(0, volatility * current_price)
            current_price += change
            
            # Ensure positive
            current_price = max(current_price, base_price * 0.5)
            
            prices.append({
                'market_id': market,
                'commodity': commodity,
                'date': date,
                'price': round(current_price, 2)
            })
        
        return pd.DataFrame(prices)
    
    @staticmethod
    def create_conflict_events(
        markets: list,
        start_date: str,
        end_date: str,
        avg_events_per_month: int = 5
    ) -> pd.DataFrame:
        """Create realistic conflict event data."""
        # Implementation...
```

### Performance Testing

```python
class TestPerformance:
    """Test system performance with realistic data volumes."""
    
    @pytest.mark.slow
    def test_large_dataset_processing(self, benchmark):
        """Test processing performance with large dataset."""
        # Arrange
        large_data = create_large_dataset(n_markets=50, n_periods=1000)
        processor = DataProcessor()
        
        # Act & Measure
        result = benchmark(processor.process, large_data)
        
        # Assert
        assert len(result) > 0
        assert benchmark.stats['mean'] < 5.0  # Should complete in <5 seconds
    
    def test_concurrent_model_fitting(self):
        """Test concurrent model fitting performance."""
        # Arrange
        datasets = [create_dataset(commodity=c) for c in ['wheat', 'rice', 'sugar']]
        
        # Act
        import concurrent.futures
        import time
        
        start = time.time()
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = []
            for data in datasets:
                model = CommodityModel()
                future = executor.submit(model.fit, data)
                futures.append(future)
            
            results = [f.result() for f in futures]
        elapsed = time.time() - start
        
        # Assert
        assert len(results) == 3
        assert all(r.is_fitted for r in results)
        assert elapsed < 10.0  # Should parallelize effectively
```

### Error Recovery Testing

```python
class TestErrorRecovery:
    """Test system recovery from errors."""
    
    def test_pipeline_handles_partial_failures(self, temp_data_dir):
        """Test pipeline continues after partial failures."""
        # Arrange - Create files with some corrupt data
        create_test_files_with_errors(temp_data_dir)
        
        # Act
        pipeline = DataPipeline()
        results = pipeline.process_directory(
            temp_data_dir,
            skip_errors=True
        )
        
        # Assert
        assert results['processed_files'] > 0
        assert results['failed_files'] > 0
        assert len(results['errors']) == results['failed_files']
        assert results['status'] == 'partial_success'
    
    def test_transaction_rollback_on_error(self, test_db):
        """Test database transaction rollback."""
        # Arrange
        repo = DataRepository(test_db)
        
        # Act & Assert
        with pytest.raises(IntegrityError):
            with repo.transaction():
                # First operation succeeds
                repo.save_market(Market(id="M1", name="Market 1"))
                
                # Second operation fails (duplicate ID)
                repo.save_market(Market(id="M1", name="Duplicate"))
        
        # Verify rollback
        assert repo.get_market("M1") is None
```

## Testing Best Practices

### Use Test Containers

```python
import testcontainers.postgres

class TestWithPostgres:
    """Test with real PostgreSQL using testcontainers."""
    
    @pytest.fixture(scope="class")
    def postgres_db(self):
        """Spin up PostgreSQL container for testing."""
        with testcontainers.postgres.PostgresContainer("postgres:14") as postgres:
            yield postgres.get_connection_url()
    
    def test_complex_queries(self, postgres_db):
        """Test complex SQL queries against real database."""
        engine = create_engine(postgres_db)
        # Run tests...
```

### Mock External Services

```python
# tests/integration/fixtures/mock_services.py
class MockHDXService:
    """Mock HDX service for testing."""
    
    def __init__(self):
        self.datasets = {
            "yemen-prices": self._create_price_dataset(),
            "yemen-admin": self._create_admin_dataset()
        }
        self.call_count = 0
    
    def get_dataset(self, dataset_id):
        """Mock dataset retrieval."""
        self.call_count += 1
        
        if self.call_count > 10:
            raise Exception("Rate limit exceeded")
            
        return self.datasets.get(dataset_id)
```

### Environment Isolation

```python
@pytest.fixture(autouse=True)
def isolate_environment(monkeypatch):
    """Isolate test environment from system environment."""
    # Set test environment variables
    monkeypatch.setenv("APP_ENV", "test")
    monkeypatch.setenv("DATABASE_URL", "sqlite:///:memory:")
    monkeypatch.setenv("API_TIMEOUT", "5")
    
    # Clear any production configs
    monkeypatch.delenv("PRODUCTION_API_KEY", raising=False)
    monkeypatch.delenv("SENTRY_DSN", raising=False)
```

## Running Integration Tests

### Test Commands

```bash
# Run all integration tests
pytest tests/integration/

# Run specific integration test category
pytest tests/integration/test_data_pipeline.py

# Run with real external services (requires credentials)
pytest tests/integration/ -m "not mock"

# Run with performance benchmarks
pytest tests/integration/ --benchmark-only

# Run with database logging
pytest tests/integration/ --log-cli-level=DEBUG
```

### CI/CD Configuration

```yaml
# .github/workflows/integration-tests.yml
name: Integration Tests
on: [push, pull_request]

jobs:
  integration-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: testpass
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
          
      - name: Install dependencies
        run: |
          pip install -e ".[dev]"
          
      - name: Run integration tests
        env:
          DATABASE_URL: postgresql://postgres:testpass@localhost/test
        run: |
          pytest tests/integration/ -v --cov=yemen_market
```

## Debugging Integration Tests

### Logging Configuration

```python
# tests/integration/conftest.py
import logging

@pytest.fixture(autouse=True)
def configure_logging():
    """Configure logging for tests."""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Set specific loggers
    logging.getLogger('yemen_market').setLevel(logging.DEBUG)
    logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
```

### Test Artifacts

```python
@pytest.fixture
def save_test_artifacts(request, tmp_path):
    """Save test artifacts on failure."""
    yield tmp_path
    
    if request.node.rep_call.failed:
        # Save any generated files
        test_name = request.node.name
        artifact_dir = Path("test_artifacts") / test_name
        artifact_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy temporary files
        import shutil
        shutil.copytree(tmp_path, artifact_dir, dirs_exist_ok=True)
        
        print(f"Test artifacts saved to: {artifact_dir}")
```

## Common Integration Test Patterns

### Testing Data Flows

```python
def test_data_transformation_flow():
    """Test data flows through transformation pipeline."""
    # Input -> Transform -> Validate -> Output
    
    # Arrange
    raw_data = load_test_data("raw_prices.csv")
    
    # Act
    stage1 = clean_data(raw_data)
    stage2 = normalize_prices(stage1)
    stage3 = add_features(stage2)
    final = validate_output(stage3)
    
    # Assert each stage
    assert stage1.isna().sum().sum() == 0  # No missing after cleaning
    assert stage2['price'].std() < 1.0      # Normalized
    assert 'price_ma7' in stage3.columns    # Features added
    assert final['is_valid'].all()          # All valid
```

### Testing Service Integration

```python
def test_service_orchestration():
    """Test multiple services working together."""
    # Service A -> Service B -> Service C
    
    # Arrange
    data_service = DataService()
    process_service = ProcessingService(data_service)
    model_service = ModelService()
    
    # Act
    raw_data = data_service.fetch_latest()
    processed = process_service.process(raw_data)
    results = model_service.analyze(processed)
    
    # Assert
    assert results['status'] == 'complete'
    assert results['data_source'] == data_service.source_id
    assert results['processing_version'] == process_service.version
```

## Next Steps

- See [Unit Testing](unit-testing.md) for component testing
- See [Test Data Management](test-data.md) for test data strategies
- See [Coverage Requirements](coverage-requirements.md) for coverage standards
- See [Debugging Guide](../debugging/common-issues.md) for troubleshooting