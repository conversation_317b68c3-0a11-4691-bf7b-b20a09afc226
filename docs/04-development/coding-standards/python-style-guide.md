# Python Style Guide

**Target Audience**: All Python developers  
**Purpose**: Ensure consistent, readable, and maintainable code across the Yemen Market Integration Platform

## Overview

This guide defines Python coding standards for the project, based on PEP 8 with additional project-specific conventions. All code must pass automated style checks before merging.

## Code Formatting

### Basic Rules

```python
# Line length: 88 characters (Black default)
# Indentation: 4 spaces (no tabs)
# String quotes: Double quotes preferred, single quotes acceptable

# Good
def calculate_price_index(
    prices: pd.DataFrame,
    base_period: str = "2020-01",
    method: str = "laspeyres"
) -> pd.Series:
    """Calculate price index using specified method."""
    pass

# Bad
def calculate_price_index(prices: pd.DataFrame, base_period: str = "2020-01", method: str = "laspeyres") -> pd.Series:
    """Calculate price index using specified method."""
    pass
```

### Imports

```python
# Standard library imports first
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Third-party imports
import numpy as np
import pandas as pd
from pydantic import BaseModel, Field
from statsmodels.regression.linear_model import OLS

# Local imports
from yemen_market.data import PanelBuilder
from yemen_market.models.three_tier import ThreeTierRunner
from yemen_market.utils.logging import setup_logging
```

### String Formatting

```python
# Use f-strings (Python 3.6+)
name = "Yemen"
year = 2024
message = f"Analysis for {name} in {year}"

# For logging, use lazy formatting
logger.info("Processing %d records for %s", count, market_name)

# Multi-line strings
query = """
    SELECT market_id, commodity, price
    FROM prices
    WHERE date >= ? AND date <= ?
    ORDER BY date
"""
```

## Naming Conventions

### Variables and Functions

```python
# Use snake_case for variables and functions
market_data = load_market_data()
price_index = calculate_price_index(market_data)

# Use descriptive names
# Good
total_conflict_events = sum(events)
average_price_by_market = df.groupby("market").mean()

# Bad
t = sum(events)
avg = df.groupby("market").mean()
```

### Classes

```python
# Use PascalCase for classes
class MarketAnalyzer:
    """Analyze market integration patterns."""
    
class PriceIndexCalculator:
    """Calculate various price indices."""
    
# Exception classes end with "Error"
class DataValidationError(Exception):
    """Raised when data validation fails."""
```

### Constants

```python
# Use UPPER_SNAKE_CASE for constants
DEFAULT_CONFIDENCE_LEVEL = 0.95
MAX_ITERATIONS = 1000
SUPPORTED_COMMODITIES = ["wheat", "rice", "sugar", "oil"]

# Group related constants
class ModelConfig:
    DEFAULT_LAG_ORDER = 4
    MIN_OBSERVATIONS = 30
    CONVERGENCE_TOLERANCE = 1e-6
```

### Private Methods and Variables

```python
class DataProcessor:
    def __init__(self):
        self._cache = {}  # Private attribute
        
    def process(self, data: pd.DataFrame) -> pd.DataFrame:
        """Public method."""
        validated = self._validate_data(data)  # Private method
        return self._transform_data(validated)
    
    def _validate_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Private method - not part of public API."""
        pass
```

## Type Annotations

### Basic Types

```python
from typing import Dict, List, Optional, Tuple, Union

def calculate_statistics(
    values: List[float],
    confidence: float = 0.95,
    method: Optional[str] = None
) -> Dict[str, float]:
    """Calculate summary statistics."""
    return {
        "mean": np.mean(values),
        "std": np.std(values),
        "ci_lower": ...,
        "ci_upper": ...
    }
```

### Complex Types

```python
from typing import TypeAlias, TypedDict

# Type aliases for clarity
MarketID: TypeAlias = str
CommodityName: TypeAlias = str
PriceData: TypeAlias = Dict[MarketID, Dict[CommodityName, float]]

# TypedDict for structured data
class MarketInfo(TypedDict):
    market_id: str
    name: str
    region: str
    coordinates: Tuple[float, float]
    population: Optional[int]
```

### Generic Types

```python
from typing import Generic, TypeVar

T = TypeVar("T", bound=pd.DataFrame)

class DataValidator(Generic[T]):
    def validate(self, data: T) -> T:
        """Validate data maintaining type."""
        return data
```

## Documentation

### Module Documentation

```python
"""
market_analysis.py - Market integration analysis module.

This module provides functions for analyzing market integration
patterns in Yemen using panel data econometric methods.

Examples:
    >>> from yemen_market.analysis import MarketAnalyzer
    >>> analyzer = MarketAnalyzer()
    >>> results = analyzer.analyze(panel_data)
"""
```

### Class Documentation

```python
class ThresholdVECM:
    """
    Threshold Vector Error Correction Model.
    
    Estimates regime-switching price transmission dynamics
    between markets based on conflict intensity thresholds.
    
    Attributes:
        n_regimes: Number of regimes (default: 2)
        threshold_var: Variable used for regime switching
        max_lags: Maximum lag order to consider
        
    Examples:
        >>> model = ThresholdVECM(n_regimes=2)
        >>> model.fit(price_data)
        >>> results = model.get_results()
    """
```

### Function Documentation

```python
def estimate_spatial_correlation(
    prices: pd.DataFrame,
    distance_matrix: np.ndarray,
    method: str = "moran",
    weights: Optional[np.ndarray] = None
) -> Dict[str, Any]:
    """
    Estimate spatial correlation in price data.
    
    Args:
        prices: Panel data with columns [market_id, date, price]
        distance_matrix: Square matrix of distances between markets
        method: Correlation method ('moran' or 'geary')
        weights: Optional weight matrix for spatial relationships
        
    Returns:
        Dictionary containing:
            - statistic: Test statistic value
            - p_value: Significance level
            - z_score: Standardized test statistic
            
    Raises:
        ValueError: If distance matrix dimensions don't match markets
        NotImplementedError: If method is not supported
        
    Notes:
        The Moran's I statistic ranges from -1 (perfect dispersion)
        to +1 (perfect correlation), with 0 indicating randomness.
        
    Examples:
        >>> distances = calculate_distances(market_coords)
        >>> results = estimate_spatial_correlation(
        ...     prices_df,
        ...     distances,
        ...     method="moran"
        ... )
        >>> print(f"Moran's I: {results['statistic']:.3f}")
    """
```

## Error Handling

### Exception Handling

```python
# Be specific with exceptions
try:
    data = pd.read_csv(filepath)
except FileNotFoundError:
    logger.error("Data file not found: %s", filepath)
    raise
except pd.errors.EmptyDataError:
    logger.warning("Empty data file: %s", filepath)
    return pd.DataFrame()
except Exception as e:
    logger.exception("Unexpected error reading file: %s", filepath)
    raise DataLoadError(f"Failed to load {filepath}") from e
```

### Custom Exceptions

```python
class YemenMarketError(Exception):
    """Base exception for Yemen Market package."""

class DataValidationError(YemenMarketError):
    """Raised when data validation fails."""
    
class ModelConvergenceError(YemenMarketError):
    """Raised when model fails to converge."""
    
class ConfigurationError(YemenMarketError):
    """Raised when configuration is invalid."""
```

### Input Validation

```python
def process_market_data(
    data: pd.DataFrame,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
) -> pd.DataFrame:
    """Process market data with validation."""
    # Validate inputs
    if data.empty:
        raise ValueError("Input data is empty")
        
    required_columns = ["market_id", "date", "price"]
    missing = set(required_columns) - set(data.columns)
    if missing:
        raise ValueError(f"Missing required columns: {missing}")
        
    # Validate date range
    if start_date and end_date:
        if pd.to_datetime(start_date) > pd.to_datetime(end_date):
            raise ValueError("start_date must be before end_date")
            
    # Process data...
```

## Code Organization

### Module Structure

```python
# Standard module layout
"""Module docstring."""

# Imports
from __future__ import annotations

import logging
from typing import Any, Dict, List, Optional

# Constants
DEFAULT_TIMEOUT = 30
VALID_METHODS = ["ols", "gls", "gmm"]

# Module-level variables
logger = logging.getLogger(__name__)

# Classes
class MainClass:
    """Main class implementation."""

# Functions
def public_function():
    """Public API function."""

def _private_function():
    """Internal function."""

# Script execution
def main():
    """Main entry point."""

if __name__ == "__main__":
    main()
```

### Class Organization

```python
class MarketAnalyzer:
    """Analyze market integration patterns."""
    
    # Class variables
    default_config = {...}
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize analyzer."""
        # Instance variables
        self.config = config or self.default_config
        self._cache = {}
        
    # Properties
    @property
    def is_fitted(self) -> bool:
        """Check if model is fitted."""
        return hasattr(self, "_results")
    
    # Public methods
    def fit(self, data: pd.DataFrame) -> None:
        """Fit the model."""
        self._validate_data(data)
        self._prepare_data(data)
        self._estimate_model()
        
    # Private methods
    def _validate_data(self, data: pd.DataFrame) -> None:
        """Validate input data."""
        
    # Class methods
    @classmethod
    def from_config(cls, config_path: str) -> MarketAnalyzer:
        """Create instance from config file."""
        
    # Static methods
    @staticmethod
    def calculate_metrics(results: Dict) -> Dict:
        """Calculate performance metrics."""
```

## Best Practices

### Performance

```python
# Use vectorized operations
# Good
prices_log = np.log(prices_df["price"])

# Bad
prices_log = [np.log(price) for price in prices_df["price"]]

# Pre-allocate arrays for known sizes
results = np.zeros((n_markets, n_periods))

# Use generators for large sequences
def process_large_dataset(files: List[Path]):
    for file in files:
        df = pd.read_csv(file)
        yield process_chunk(df)
```

### Memory Management

```python
# Use context managers
with pd.HDFStore("data.h5") as store:
    df = store["prices"]
    # Process data
    
# Explicitly delete large objects
del large_dataframe
import gc
gc.collect()

# Use chunking for large files
for chunk in pd.read_csv("large_file.csv", chunksize=10000):
    process_chunk(chunk)
```

### Pandas Best Practices

```python
# Chain operations for readability
result = (
    df
    .query("price > 0")
    .groupby(["market_id", "commodity"])
    .agg({
        "price": ["mean", "std"],
        "quantity": "sum"
    })
    .reset_index()
)

# Avoid chained indexing
# Good
df.loc[df["price"] > 0, "flag"] = True

# Bad
df[df["price"] > 0]["flag"] = True  # SettingWithCopyWarning
```

## Testing

### Test Naming

```python
# Test files match module names
# yemen_market/data/processor.py -> tests/unit/test_processor.py

# Test class names
class TestMarketAnalyzer:
    """Test MarketAnalyzer class."""
    
# Test method names describe behavior
def test_calculates_price_index_correctly():
    """Test that price index calculation is correct."""
    
def test_raises_error_on_empty_data():
    """Test that empty data raises ValueError."""
```

### Test Structure

```python
def test_threshold_model_identifies_regimes():
    """Test that threshold model correctly identifies regimes."""
    # Arrange
    price_data = create_test_prices()
    conflict_data = create_test_conflicts()
    model = ThresholdVECM(n_regimes=2)
    
    # Act
    model.fit(price_data, conflict_data)
    regimes = model.identify_regimes()
    
    # Assert
    assert len(regimes) == len(price_data)
    assert regimes.nunique() == 2
    assert all(regime in [0, 1] for regime in regimes)
```

## Code Review Checklist

Before submitting code for review:

- [ ] Code passes all tests: `make test`
- [ ] Code is formatted: `make format`
- [ ] Code passes linting: `make lint`
- [ ] Type hints are included
- [ ] Docstrings are complete
- [ ] No commented-out code
- [ ] No print statements (use logging)
- [ ] Error handling is appropriate
- [ ] Performance implications considered
- [ ] Security implications considered

## Automated Formatting

### Using Black

```bash
# Format single file
black src/yemen_market/models/model.py

# Format entire project
black src/ tests/

# Check without modifying
black --check src/
```

### Using Ruff

```bash
# Lint and show issues
ruff src/

# Fix auto-fixable issues
ruff --fix src/

# More aggressive fixing
ruff --fix --unsafe-fixes src/
```

### Pre-commit Configuration

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        args: [--line-length=88]
        
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.0.285
    hooks:
      - id: ruff
        args: [--fix]
```

## Gradual Adoption

For existing code:

1. Format new code according to this guide
2. Update modified functions to meet standards
3. Gradually refactor existing code
4. Use `# fmt: skip` sparingly for special cases

## Resources

- [PEP 8](https://pep8.org/) - Python style guide
- [Black](https://black.readthedocs.io/) - Code formatter
- [Ruff](https://beta.ruff.rs/) - Fast Python linter
- [Type Hints](https://docs.python.org/3/library/typing.html) - Type annotations