# Documentation Standards

**Target Audience**: All contributors  
**Purpose**: Ensure consistent, comprehensive documentation across the Yemen Market Integration Platform

## Overview

Good documentation is crucial for project maintainability and usability. This guide covers standards for code documentation, API documentation, user guides, and technical documentation.

## Documentation Types

### 1. Code Documentation (Docstrings)
- **Purpose**: Explain what code does and how to use it
- **Audience**: Developers using the code
- **Location**: In source code files

### 2. API Reference Documentation
- **Purpose**: Comprehensive API reference
- **Audience**: Developers integrating with the platform
- **Location**: `docs/03-api-reference/`

### 3. User Guides
- **Purpose**: How-to guides for end users
- **Audience**: Researchers, analysts
- **Location**: `docs/02-user-guides/`

### 4. Technical Documentation
- **Purpose**: Architecture, design decisions, methodology
- **Audience**: Developers, researchers
- **Location**: `docs/01-architecture/`, `docs/05-methodology/`

## Docstring Standards

### Module Docstrings

```python
"""
Market integration analysis module.

This module provides tools for analyzing market integration patterns
in conflict-affected regions using panel data econometric methods.
It implements the three-tier framework described in the methodology.

Key features:
    - Pooled panel models with fixed effects
    - Threshold vector error correction models
    - External validation using conflict data
    
Usage:
    from yemen_market.analysis import MarketAnalyzer
    
    analyzer = MarketAnalyzer(config)
    results = analyzer.analyze(panel_data)
    
Notes:
    This module requires panel data in the standard format with
    columns: [market_id, commodity, date, price].
    
See Also:
    - :mod:`yemen_market.models`: Model implementations
    - :mod:`yemen_market.data`: Data processing utilities
"""
```

### Class Docstrings

```python
class PanelDataProcessor:
    """
    Process and validate panel data for econometric analysis.
    
    This class handles data cleaning, validation, and transformation
    for panel data analysis. It ensures data meets the requirements
    for the three-tier econometric framework.
    
    Parameters
    ----------
    config : dict, optional
        Configuration dictionary with the following keys:
        - min_observations : int
            Minimum observations per panel unit (default: 30)
        - interpolate_missing : bool
            Whether to interpolate missing values (default: True)
        - outlier_method : str
            Method for outlier detection ('iqr', 'zscore', 'isolation')
            
    Attributes
    ----------
    config : dict
        Configuration parameters
    validation_report : dict
        Results from last validation run
    is_validated : bool
        Whether data has been validated
        
    Examples
    --------
    Basic usage:
    
    >>> processor = PanelDataProcessor()
    >>> clean_data = processor.process(raw_data)
    
    With custom configuration:
    
    >>> config = {'min_observations': 50, 'outlier_method': 'isolation'}
    >>> processor = PanelDataProcessor(config)
    >>> clean_data = processor.process(raw_data)
    
    Raises
    ------
    DataValidationError
        If data fails validation checks
    ConfigurationError
        If configuration is invalid
        
    See Also
    --------
    PanelBuilder : Constructs balanced panel datasets
    DataValidator : Detailed validation functionality
    
    Notes
    -----
    The processor follows these steps:
    1. Validate input format
    2. Remove duplicates
    3. Handle missing values
    4. Detect and handle outliers
    5. Create balanced panel (optional)
    
    References
    ----------
    .. [1] Wooldridge, J.M. (2010). Econometric Analysis of Cross 
           Section and Panel Data. MIT Press.
    """
```

### Function/Method Docstrings

```python
def calculate_market_integration_index(
    prices: pd.DataFrame,
    method: str = "price_correlation",
    window: int = 12,
    min_markets: int = 5,
    weights: Optional[Dict[str, float]] = None,
    **kwargs
) -> pd.Series:
    """
    Calculate market integration index over time.
    
    Computes a time-varying index of market integration using
    price correlations or cointegration relationships. Higher
    values indicate stronger market integration.
    
    Parameters
    ----------
    prices : pd.DataFrame
        Panel data with columns [market_id, date, commodity, price].
        Must be sorted by date.
    method : {'price_correlation', 'cointegration', 'variance_ratio'}, default 'price_correlation'
        Method for calculating integration:
        
        - 'price_correlation': Average pairwise price correlations
        - 'cointegration': Fraction of cointegrated market pairs  
        - 'variance_ratio': Ratio of common to idiosyncratic variance
        
    window : int, default 12
        Rolling window size in periods for calculations.
    min_markets : int, default 5
        Minimum number of markets required for valid calculation.
    weights : dict of {str: float}, optional
        Market weights for weighted averaging. Keys are market_ids,
        values are weights. If None, equal weights are used.
    **kwargs
        Additional arguments passed to specific methods:
        
        - For 'cointegration': max_lag (int), significance (float)
        - For 'variance_ratio': n_factors (int)
        
    Returns
    -------
    pd.Series
        Time series of integration index values, indexed by date.
        Values typically range from 0 (no integration) to 1 
        (perfect integration).
        
    Raises
    ------
    ValueError
        If method is not recognized or data has insufficient markets.
    KeyError
        If required columns are missing from input data.
        
    Examples
    --------
    Calculate integration using price correlations:
    
    >>> index = calculate_market_integration_index(
    ...     prices_df,
    ...     method='price_correlation',
    ...     window=24
    ... )
    
    Using cointegration with custom parameters:
    
    >>> index = calculate_market_integration_index(
    ...     prices_df,
    ...     method='cointegration',
    ...     window=36,
    ...     max_lag=4,
    ...     significance=0.05
    ... )
    
    With market population weights:
    
    >>> weights = {'SANA': 0.3, 'ADEN': 0.2, 'TAIZ': 0.15, ...}
    >>> index = calculate_market_integration_index(
    ...     prices_df,
    ...     weights=weights
    ... )
    
    See Also
    --------
    estimate_pairwise_cointegration : Test cointegration between market pairs
    calculate_price_correlations : Compute correlation matrices
    
    Notes
    -----
    The integration index is calculated using rolling windows to capture
    time-varying integration patterns. Missing values are handled through
    forward filling within reasonable limits (max 3 periods).
    
    For the cointegration method, the Johansen test is used for market
    pairs, and the index represents the fraction of market pairs that
    show evidence of cointegration at the specified significance level.
    
    References
    ----------
    .. [1] Baulch, B. (1997). "Transfer costs, spatial arbitrage, and 
           testing for food market integration." American Journal of 
           Agricultural Economics, 79(2), 477-487.
    .. [2] Fackler, P. L., & Goodwin, B. K. (2001). "Spatial price 
           analysis." Handbook of agricultural economics, 1, 971-1024.
    """
    # Implementation...
```

### Property Docstrings

```python
@property
def integration_summary(self) -> Dict[str, Any]:
    """
    Summary statistics of market integration analysis.
    
    Returns
    -------
    dict
        Dictionary containing:
        - 'mean_integration': float
            Average integration index across time
        - 'trend': float
            Linear trend coefficient (positive = increasing integration)
        - 'volatility': float
            Standard deviation of integration index
        - 'structural_breaks': list of str
            Dates of detected structural breaks
    """
    return self._calculate_summary()
```

## Markdown Documentation

### Document Structure

```markdown
# Document Title

**Target Audience**: Researchers, Developers  
**Last Updated**: 2024-01-15  
**Version**: 1.0

## Overview

Brief introduction explaining the purpose and scope of this document.
Include what readers will learn and any prerequisites.

## Table of Contents

- [Section 1](#section-1)
- [Section 2](#section-2)
- [Section 3](#section-3)

## Section 1

### Subsection 1.1

Content with proper formatting...

### Subsection 1.2

More content...

## Examples

### Example 1: Basic Usage

```python
# Code example with syntax highlighting
from yemen_market import SomeClass

instance = SomeClass()
result = instance.method()
```

## See Also

- [Related Document 1](../path/to/doc1.md)
- [Related Document 2](../path/to/doc2.md)

## References

1. Author, A. (2024). "Title of Paper." Journal Name.
2. Organization. (2024). Technical Report. URL.
```

### API Reference Format

```markdown
# ModuleName API Reference

**Module**: `yemen_market.module.submodule`

## Classes

### ClassName

Brief description of the class.

#### Parameters

- **param1** (`type`): Description
- **param2** (`type`, optional): Description (default: value)

#### Attributes

- **attr1** (`type`): Description
- **attr2** (`type`): Description

#### Methods

##### method_name

```python
def method_name(self, arg1: type, arg2: type = default) -> return_type:
```

Brief description of what the method does.

**Parameters:**
- **arg1** (`type`): Description
- **arg2** (`type`, optional): Description (default: value)

**Returns:**
- **return_type**: Description of return value

**Raises:**
- **ExceptionType**: When this exception is raised

**Example:**
```python
result = instance.method_name(value1, value2)
```
```

### User Guide Format

```markdown
# How to Analyze Market Integration

## Prerequisites

- Yemen Market Integration Platform installed
- Access to price data
- Basic Python knowledge

## Step 1: Prepare Your Data

First, ensure your data is in the correct format:

```python
import pandas as pd

# Load your data
data = pd.read_csv('prices.csv')

# Required columns
required_columns = ['market_id', 'commodity', 'date', 'price']
```

## Step 2: Run Analysis

[Clear instructions with code examples...]

## Interpreting Results

[Explanation of outputs with visualizations...]

## Common Issues

### Issue: Missing Data

**Solution**: Use interpolation...

### Issue: Convergence Failure

**Solution**: Adjust parameters...

## Next Steps

- Try advanced features...
- Explore other analyses...
```

## Writing Style Guidelines

### Clarity and Conciseness

- **Be Clear**: Use simple, direct language
- **Be Concise**: Avoid unnecessary words
- **Be Specific**: Use concrete examples
- **Be Consistent**: Use the same terms throughout

### Active Voice

```markdown
# Good
The function calculates the integration index.

# Avoid
The integration index is calculated by the function.
```

### Present Tense

```markdown
# Good
This method returns a DataFrame.

# Avoid
This method will return a DataFrame.
```

### Technical Terms

- Define technical terms on first use
- Link to glossary for complex concepts
- Provide context for domain-specific knowledge

## Code Examples

### Example Requirements

1. **Runnable**: Examples should work as written
2. **Relevant**: Show typical use cases
3. **Complete**: Include imports and setup
4. **Annotated**: Add comments explaining key points

### Good Example

```python
# Complete, runnable example
import pandas as pd
from yemen_market.models.three_tier import ThreeTierRunner

# Load sample data (included with package)
data = pd.read_parquet('data/sample/panel_data.parquet')

# Configure analysis
config = {
    'tier1_config': {
        'outcome_var': 'log_price',
        'treatment_var': 'conflict_intensity',
        'fixed_effects': ['market_id', 'commodity', 'month']
    }
}

# Run analysis
runner = ThreeTierRunner(config)
results = runner.run_all_tiers(data)

# Display key results
print(f"Conflict effect: {results.tier1.coefficients['conflict_intensity']:.3f}")
print(f"P-value: {results.tier1.pvalues['conflict_intensity']:.4f}")
```

## Version Control for Documentation

### Commit Messages

```bash
# Good commit messages for docs
docs: Add API reference for PanelBuilder class
docs: Update installation guide for Windows users
docs: Fix broken links in methodology section
docs: Improve examples in user guide

# Include doc updates with code
feat: Add threshold detection with documentation
fix: Correct price calculation and update docs
```

### Documentation Reviews

Documentation PRs should include:
- [ ] Spell check passed
- [ ] Links verified
- [ ] Code examples tested
- [ ] Formatting consistent
- [ ] Technical accuracy verified

## Tools and Automation

### Sphinx Documentation

```python
# conf.py configuration
extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.napoleon',
    'sphinx.ext.viewcode',
    'sphinx.ext.intersphinx',
    'sphinx_rtd_theme',
    'myst_parser'
]

# Napoleon settings for Google/NumPy style
napoleon_google_docstring = False
napoleon_numpy_docstring = True
```

### Documentation Building

```bash
# Build HTML documentation
make docs

# Check for broken links
make linkcheck

# Generate API docs from docstrings
sphinx-apidoc -o docs/api src/yemen_market
```

### Docstring Validation

```python
# Use pydocstyle for validation
# .pydocstyle configuration
[pydocstyle]
inherit = false
match = .*\.py
match-dir = ^(?!venv|build|dist).*
convention = numpy
add-ignore = D100,D104  # Module and package docstrings
```

## Documentation Maintenance

### Regular Reviews

- **Monthly**: Review and update examples
- **Quarterly**: Verify all links and references
- **Per Release**: Update API documentation
- **Annually**: Comprehensive documentation audit

### Deprecation Notices

```python
def old_function():
    """
    Calculate something.
    
    .. deprecated:: 1.2.0
        Use :func:`new_function` instead. This function will be
        removed in version 2.0.0.
    """
    warnings.warn(
        "old_function is deprecated, use new_function instead",
        DeprecationWarning,
        stacklevel=2
    )
```

## Common Documentation Patterns

### Configuration Documentation

```python
"""
Configuration Options
--------------------

The following configuration options are available:

.. code-block:: yaml

    analysis:
      confidence_level: 0.95  # Statistical confidence level
      min_observations: 30    # Minimum observations per group
      parallel: true          # Enable parallel processing
      
    data:
      interpolate_missing: true  # Interpolate missing values
      outlier_threshold: 3       # Standard deviations for outliers
"""
```

### Mathematical Notation

```python
r"""
The price transmission equation is:

.. math::
    
    p_{it} = \alpha_i + \beta_1 p_{jt} + \beta_2 X_{it} + \epsilon_{it}

where:
    - :math:`p_{it}` is the log price in market i at time t
    - :math:`\alpha_i` is the market fixed effect
    - :math:`\beta_1` is the transmission elasticity
    - :math:`X_{it}` are control variables
"""
```

## Documentation Checklist

Before submitting documentation:

- [ ] Spell check completed
- [ ] Grammar check completed
- [ ] Technical terms defined
- [ ] Code examples tested
- [ ] Links verified
- [ ] Formatting consistent
- [ ] Target audience appropriate
- [ ] Cross-references added
- [ ] Version/date updated

## Resources

- [NumPy Docstring Guide](https://numpydoc.readthedocs.io/en/latest/format.html)
- [Sphinx Documentation](https://www.sphinx-doc.org/)
- [MyST Parser](https://myst-parser.readthedocs.io/)
- [Write the Docs](https://www.writethedocs.org/guide/)