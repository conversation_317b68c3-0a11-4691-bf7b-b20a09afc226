# Git Workflow and Standards

**Target Audience**: All contributors  
**Purpose**: Define Git workflow, branching strategy, and commit standards

## Overview

This guide establishes Git conventions for the Yemen Market Integration Platform to ensure clean history, effective collaboration, and smooth releases.

## Branching Strategy

### Branch Types

```
main
├── develop
├── feature/feature-name
├── bugfix/bug-description
├── hotfix/critical-fix
├── release/v1.2.0
└── experimental/idea-name
```

### Branch Descriptions

#### main
- **Purpose**: Production-ready code
- **Protection**: Requires PR approval, passing tests
- **Merges from**: release/, hotfix/

#### develop
- **Purpose**: Integration branch for features
- **Protection**: Requires PR approval
- **Merges from**: feature/, bugfix/

#### feature/*
- **Purpose**: New features and enhancements
- **Naming**: `feature/add-threshold-detection`
- **Created from**: develop
- **Merges to**: develop

#### bugfix/*
- **Purpose**: Non-critical bug fixes
- **Naming**: `bugfix/fix-panel-calculation`
- **Created from**: develop
- **Merges to**: develop

#### hotfix/*
- **Purpose**: Critical production fixes
- **Naming**: `hotfix/fix-data-corruption`
- **Created from**: main
- **Merges to**: main AND develop

#### release/*
- **Purpose**: Release preparation
- **Naming**: `release/v1.2.0`
- **Created from**: develop
- **Merges to**: main AND develop

## Commit Standards

### Commit Message Format

```
<type>(<scope>): <subject>

<body>

<footer>
```

### Types

- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes (formatting, etc.)
- **refactor**: Code refactoring
- **perf**: Performance improvements
- **test**: Test additions or modifications
- **build**: Build system changes
- **ci**: CI/CD changes
- **chore**: Maintenance tasks

### Examples

```bash
# Feature commit
feat(models): Add threshold detection to VECM models

Implement Hansen (1999) threshold detection algorithm for
identifying regime switches in price transmission models.
Includes bootstrap confidence intervals.

Closes #123

# Bug fix
fix(data): Correct date parsing for WFP price data

The date parser was incorrectly handling month-end dates,
causing some prices to be assigned to wrong periods.

Fixes #456

# Documentation
docs(api): Update PanelBuilder API documentation

- Add missing parameters to docstrings
- Include usage examples
- Fix formatting issues

# Refactoring
refactor(tests): Reorganize integration tests by module

Move integration tests to match source code structure for
better organization and easier maintenance.
```

### Commit Message Guidelines

1. **Subject Line**
   - Use imperative mood ("Add" not "Added")
   - Limit to 50 characters
   - Capitalize first letter
   - No period at end

2. **Body**
   - Wrap at 72 characters
   - Explain what and why, not how
   - Include motivation for change
   - Reference issues/tickets

3. **Footer**
   - Reference issues: `Closes #123`, `Fixes #456`
   - Breaking changes: `BREAKING CHANGE: description`
   - Co-authors: `Co-authored-by: Name <email>`

## Workflow Examples

### Feature Development

```bash
# 1. Create feature branch
git checkout develop
git pull origin develop
git checkout -b feature/add-market-clustering

# 2. Make changes
# ... edit files ...

# 3. Stage and commit
git add src/yemen_market/analysis/clustering.py
git add tests/unit/test_clustering.py
git commit -m "feat(analysis): Add hierarchical clustering for markets

Implement agglomerative clustering to identify market groups
based on price correlation patterns. Uses Ward linkage with
correlation distance metric.

- Add MarketClusterer class
- Include visualization methods
- Add comprehensive tests"

# 4. Push and create PR
git push origin feature/add-market-clustering
# Create PR on GitHub/GitLab
```

### Bug Fix

```bash
# 1. Create bugfix branch
git checkout develop
git pull origin develop
git checkout -b bugfix/fix-missing-data-handling

# 2. Fix bug and add test
# ... make fixes ...

# 3. Commit with clear message
git add -A
git commit -m "fix(data): Handle missing values in panel construction

Panel builder was failing when encountering markets with
sparse data. Now properly interpolates up to 3 missing
periods and excludes markets with >20% missing data.

Added tests for edge cases.

Fixes #789"

# 4. Push and create PR
git push origin bugfix/fix-missing-data-handling
```

### Hotfix

```bash
# 1. Create hotfix from main
git checkout main
git pull origin main
git checkout -b hotfix/fix-api-authentication

# 2. Apply fix
# ... urgent fix ...

# 3. Commit
git commit -m "fix(api): Restore HDX API authentication

API key validation was failing due to header format change.
Updated to match new API requirements.

Fixes #999"

# 4. Merge to main and develop
git checkout main
git merge --no-ff hotfix/fix-api-authentication
git tag -a v1.2.1 -m "Hotfix: API authentication"
git push origin main --tags

git checkout develop
git merge --no-ff hotfix/fix-api-authentication
git push origin develop
```

## Pull Request Process

### PR Template

```markdown
## Description
Brief description of changes and motivation.

## Type of Change
- [ ] Bug fix (non-breaking change fixing an issue)
- [ ] New feature (non-breaking change adding functionality)
- [ ] Breaking change (fix or feature causing existing functionality to change)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Comments added for complex code
- [ ] Documentation updated
- [ ] No new warnings generated
- [ ] Tests added for new functionality
- [ ] All tests passing

## Related Issues
Closes #XXX
Relates to #YYY

## Screenshots (if applicable)
Add screenshots for UI changes
```

### PR Review Guidelines

#### For Authors

1. **Keep PRs Small**: Aim for <400 lines changed
2. **Single Purpose**: One feature/fix per PR
3. **Test Coverage**: Include tests for changes
4. **Documentation**: Update relevant docs
5. **Clean History**: Squash WIP commits

#### For Reviewers

1. **Functionality**: Does it solve the problem?
2. **Code Quality**: Is it maintainable?
3. **Tests**: Adequate coverage?
4. **Performance**: Any performance impacts?
5. **Security**: Any security concerns?

### Review Comments

```python
# Constructive feedback
# Instead of: "This is wrong"
# Use: "Consider using a dictionary comprehension here for better readability:
#      `result = {k: v for k, v in items.items() if v > 0}`"

# Explain why
# Instead of: "Don't use global variables"
# Use: "Global variables can cause issues with testing and parallel execution.
#      Consider passing this as a parameter or using a configuration object."
```

## Git Configuration

### Recommended Git Config

```bash
# Set up user information
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Helpful aliases
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.st status
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'

# Better diffs
git config --global diff.algorithm histogram
git config --global diff.colorMoved zebra

# Rebase settings
git config --global pull.rebase true
git config --global rebase.autoStash true

# Push settings
git config --global push.default current
git config --global push.followTags true
```

### Git Hooks

#### Pre-commit Hook

```bash
#!/bin/sh
# .git/hooks/pre-commit

# Run tests
echo "Running tests..."
pytest tests/unit/ -x
if [ $? -ne 0 ]; then
    echo "Tests failed. Commit aborted."
    exit 1
fi

# Run linting
echo "Running linter..."
ruff src/
if [ $? -ne 0 ]; then
    echo "Linting failed. Commit aborted."
    exit 1
fi

# Check for large files
MAX_SIZE=5000000  # 5MB
for file in $(git diff --cached --name-only); do
    if [ -f "$file" ]; then
        size=$(wc -c < "$file")
        if [ $size -gt $MAX_SIZE ]; then
            echo "File $file is too large ($size bytes)"
            exit 1
        fi
    fi
done
```

## Advanced Git Usage

### Interactive Rebase

```bash
# Squash last 3 commits
git rebase -i HEAD~3

# In editor, change 'pick' to 'squash' for commits to combine
pick abc1234 feat: Add base feature
squash def5678 WIP: Continue feature
squash ghi9012 fix: Typo

# Save and write new commit message
```

### Cherry-picking

```bash
# Apply specific commit to current branch
git cherry-pick abc1234

# Cherry-pick without committing
git cherry-pick -n abc1234
```

### Stashing

```bash
# Save work in progress
git stash save "WIP: Working on threshold detection"

# List stashes
git stash list

# Apply specific stash
git stash apply stash@{1}

# Apply and remove stash
git stash pop
```

### Bisect for Bug Finding

```bash
# Start bisect
git bisect start
git bisect bad HEAD
git bisect good v1.0.0

# Git will checkout commits to test
# After testing each:
git bisect good  # or
git bisect bad

# When done
git bisect reset
```

## Release Process

### Version Numbering

Follow Semantic Versioning (SemVer):
- **MAJOR.MINOR.PATCH** (e.g., 1.2.3)
- **MAJOR**: Breaking changes
- **MINOR**: New features (backwards compatible)
- **PATCH**: Bug fixes

### Release Workflow

```bash
# 1. Create release branch
git checkout -b release/v1.2.0 develop

# 2. Update version
# Update pyproject.toml, __init__.py, etc.
git commit -m "chore: Bump version to 1.2.0"

# 3. Update CHANGELOG
# Add release notes
git commit -m "docs: Update CHANGELOG for v1.2.0"

# 4. Merge to main
git checkout main
git merge --no-ff release/v1.2.0
git tag -a v1.2.0 -m "Release version 1.2.0"

# 5. Merge back to develop
git checkout develop
git merge --no-ff release/v1.2.0

# 6. Push everything
git push origin main develop v1.2.0
```

### Tag Annotations

```bash
# Create annotated tag with detailed message
git tag -a v1.2.0 -m "Release version 1.2.0

Features:
- Add threshold detection for VECM models
- Implement market clustering analysis
- Add export to LaTeX tables

Fixes:
- Fix date parsing in WFP data processor
- Correct memory leak in panel builder
- Handle missing data in spatial joins

See CHANGELOG.md for full details."
```

## Troubleshooting

### Common Issues

#### Merge Conflicts

```bash
# During merge/rebase
git status  # See conflicted files

# Edit files to resolve conflicts
# Look for <<<<<<< HEAD markers

# After resolving
git add resolved_file.py
git rebase --continue  # or git merge --continue
```

#### Accidentally Committed to Wrong Branch

```bash
# If not pushed yet
git reset HEAD~1  # Undo last commit, keep changes
git stash
git checkout correct-branch
git stash pop
git commit
```

#### Need to Undo Published Commits

```bash
# Create a revert commit (safe for shared branches)
git revert abc1234

# For multiple commits
git revert --no-commit abc1234..def5678
git commit -m "Revert: Remove feature X due to issues"
```

### Clean Up

```bash
# Remove merged branches
git branch --merged | grep -v "\*\|main\|develop" | xargs -n 1 git branch -d

# Clean up remote tracking
git remote prune origin

# Garbage collection
git gc --aggressive
```

## Git Best Practices

1. **Commit Often**: Small, logical commits
2. **Pull Before Push**: Always sync before pushing
3. **No Force Push**: Never force push to shared branches
4. **Clean History**: Use interactive rebase before merging
5. **Descriptive Messages**: Future you will thank you
6. **Branch Protection**: Set up rules for important branches
7. **Sign Commits**: Use GPG signing for releases

## Resources

- [Pro Git Book](https://git-scm.com/book)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [Git Flow](https://nvie.com/posts/a-successful-git-branching-model/)
- [GitHub Flow](https://guides.github.com/introduction/flow/)