# Development Guide

**Target Audience**: Developers, Contributors  
**Purpose**: Comprehensive guide for developing, testing, and deploying the Yemen Market Integration Platform

## Overview

This guide covers all aspects of development for the Yemen Market Integration Platform, from initial setup through production deployment. Whether you're contributing code, running tests, or deploying the system, you'll find the necessary documentation here.

## Directory Structure

### [setup/](setup/) - Development Environment Setup
- **[development-setup.md](setup/development-setup.md)** - Complete development environment setup
- **[dependencies.md](setup/dependencies.md)** - Dependency management and requirements
- **[ide-configuration.md](setup/ide-configuration.md)** - IDE setup for VS Code, PyCharm, etc.

### [coding-standards/](coding-standards/) - Code Style and Standards
- **[python-style-guide.md](coding-standards/python-style-guide.md)** - Python coding standards
- **[documentation-standards.md](coding-standards/documentation-standards.md)** - Documentation requirements
- **[git-workflow.md](coding-standards/git-workflow.md)** - Git workflow and commit standards

### [testing/](testing/) - Testing Guidelines
- **[unit-testing.md](testing/unit-testing.md)** - Unit testing best practices
- **[integration-testing.md](testing/integration-testing.md)** - Integration test guidelines
- **[test-data.md](testing/test-data.md)** - Test data management
- **[coverage-requirements.md](testing/coverage-requirements.md)** - Coverage standards (>90%)

### [deployment/](deployment/) - Deployment Guides
- **[local-deployment.md](deployment/local-deployment.md)** - Local deployment steps
- **[docker-deployment.md](deployment/docker-deployment.md)** - Docker configuration
- **[production-guide.md](deployment/production-guide.md)** - Production deployment
- **[monitoring.md](deployment/monitoring.md)** - Monitoring and logging setup

### [debugging/](debugging/) - Debugging and Troubleshooting
- **[common-issues.md](debugging/common-issues.md)** - Common problems and solutions
- **[performance-profiling.md](debugging/performance-profiling.md)** - Performance debugging
- **[memory-profiling.md](debugging/memory-profiling.md)** - Memory usage analysis

## Quick Start for Developers

### 1. Clone and Setup
```bash
# Clone repository
git clone https://github.com/your-org/yemen-market-integration.git
cd yemen-market-integration

# Create virtual environment
./setup_venv.sh

# Or manually
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -e ".[dev]"
```

### 2. Run Tests
```bash
# Run all tests
make test

# Run with coverage
make test-coverage

# Run specific test module
pytest tests/unit/test_panel_builder.py -v
```

### 3. Start Development
```bash
# Run linting
make lint

# Format code
make format

# Start development server (if applicable)
make run-dev
```

## Development Workflow

### 1. Feature Development
1. Create feature branch: `git checkout -b feature/your-feature`
2. Write tests first (TDD approach)
3. Implement feature
4. Ensure tests pass: `make test`
5. Check coverage: `make coverage`
6. Lint and format: `make lint format`

### 2. Code Review Process
1. Push feature branch
2. Create pull request with description
3. Ensure CI passes
4. Request review from team members
5. Address feedback
6. Merge after approval

### 3. Release Process
1. Update version in `pyproject.toml`
2. Update CHANGELOG.md
3. Create release branch
4. Run full test suite
5. Build and test Docker images
6. Tag release
7. Deploy to production

## Key Development Tools

### Makefile Commands
```bash
make help           # Show all available commands
make setup          # Setup development environment
make test           # Run tests
make test-coverage  # Run tests with coverage
make lint           # Run linting (ruff, black)
make format         # Auto-format code
make docs           # Build documentation
make clean          # Clean build artifacts
make docker-build   # Build Docker images
make docker-run     # Run in Docker
```

### Project Configuration
- **pyproject.toml** - Project metadata and dependencies
- **Makefile** - Development automation
- **setup_venv.sh** - Virtual environment setup script
- **.env.example** - Environment variable template
- **config/logging_config.yaml** - Logging configuration

## Architecture Overview

The platform follows a modular architecture:

```
src/yemen_market/
├── data/           # Data ingestion and processing
├── features/       # Feature engineering
├── models/         # Econometric models (three-tier)
├── analysis/       # Analysis pipelines
├── utils/          # Utilities (logging, performance)
└── visualization/  # Plotting and visualization
```

## Testing Strategy

### Test Organization
```
tests/
├── unit/           # Unit tests (isolated components)
├── integration/    # Integration tests (component interaction)
├── fixtures/       # Test data and fixtures
└── conftest.py     # Pytest configuration
```

### Testing Requirements
- Minimum 90% code coverage
- All new features must include tests
- Integration tests for critical paths
- Performance tests for data-intensive operations

## Continuous Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/ci.yml
name: CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
      - name: Install dependencies
        run: pip install -e ".[dev]"
      - name: Run tests
        run: make test-coverage
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## Performance Considerations

### Data Processing
- Use Polars for large datasets (>1M rows)
- Implement chunking for memory-intensive operations
- Cache intermediate results
- Use parallel processing where applicable

### Model Optimization
- Profile model fitting with cProfile
- Use numba for numerical optimizations
- Consider GPU acceleration for large-scale computations
- Implement early stopping for iterative algorithms

## Security Guidelines

### Code Security
- Never commit credentials or API keys
- Use environment variables for sensitive data
- Validate all user inputs
- Sanitize SQL queries
- Implement rate limiting for APIs

### Data Security
- Encrypt sensitive data at rest
- Use secure connections (HTTPS/TLS)
- Implement access controls
- Audit data access
- Follow GDPR/privacy regulations

## Contributing

### How to Contribute
1. Fork the repository
2. Create your feature branch
3. Write tests and documentation
4. Ensure all tests pass
5. Submit pull request

### Code Review Criteria
- [ ] Tests pass
- [ ] Coverage maintained/improved
- [ ] Documentation updated
- [ ] Code follows style guide
- [ ] No security vulnerabilities
- [ ] Performance impact considered

## Resources

### Internal Documentation
- [API Reference](../03-api-reference/) - Detailed API documentation
- [User Guides](../02-user-guides/) - User-facing documentation
- [Methodology](../05-methodology/) - Technical methodology

### External Resources
- [Python Style Guide (PEP 8)](https://pep8.org/)
- [NumPy Style Docstrings](https://numpydoc.readthedocs.io/)
- [Pytest Documentation](https://docs.pytest.org/)
- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)

## Getting Help

### Support Channels
- GitHub Issues for bug reports
- Discussions for questions
- Slack channel for real-time help
- Email: <EMAIL>

### Debugging Tips
1. Check logs in `logs/` directory
2. Enable debug logging: `export LOG_LEVEL=DEBUG`
3. Use debugger: `import pdb; pdb.set_trace()`
4. Profile performance: `python -m cProfile script.py`

## Next Steps

- [Development Setup](setup/development-setup.md) - Get your environment ready
- [Python Style Guide](coding-standards/python-style-guide.md) - Learn coding standards
- [Testing Guide](testing/unit-testing.md) - Write effective tests
- [Deployment Guide](deployment/local-deployment.md) - Deploy locally