# Common Issues and Solutions

**Target Audience**: Developers, Support Team  
**Purpose**: Troubleshooting guide for common development and runtime issues

## Overview

This guide documents common issues encountered during development and deployment of the Yemen Market Integration Platform, along with their solutions and debugging approaches.

## Installation Issues

### Issue: Package Installation Failures

#### Symptom
```bash
ERROR: Could not build wheels for numpy, pandas, scipy
```

#### Solutions

**1. Missing System Dependencies**
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install python3-dev build-essential
sudo apt-get install libatlas-base-dev gfortran

# macOS
xcode-select --install
brew install openblas gfortran

# Windows
# Install Visual Studio Build Tools
# https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
```

**2. Use Binary Wheels**
```bash
# Force binary installation
pip install --only-binary :all: numpy pandas scipy

# Upgrade pip first
python -m pip install --upgrade pip
pip install --upgrade setuptools wheel
```

**3. Memory Issues During Compilation**
```bash
# Limit parallel compilation
export NPY_NUM_BUILD_JOBS=1
pip install numpy

# Or use pre-compiled conda packages
conda install numpy pandas scipy
```

### Issue: Geospatial Dependencies

#### Symptom
```bash
ERROR: GDAL headers not found
ImportError: libgdal.so.26: cannot open shared object file
```

#### Solutions

**1. Install GDAL System Libraries**
```bash
# Ubuntu/Debian
sudo apt-get install gdal-bin libgdal-dev
export CPLUS_INCLUDE_PATH=/usr/include/gdal
export C_INCLUDE_PATH=/usr/include/gdal

# macOS
brew install gdal
export LDFLAGS="-L/usr/local/opt/gdal/lib"
export CPPFLAGS="-I/usr/local/opt/gdal/include"

# Then install Python packages
pip install GDAL==$(gdal-config --version) --no-cache-dir
pip install geopandas
```

**2. Use Conda for Geospatial Stack**
```bash
conda create -n yemen-geo python=3.9
conda activate yemen-geo
conda install -c conda-forge geopandas gdal rasterio
```

## Data Loading Issues

### Issue: Memory Errors with Large Datasets

#### Symptom
```python
MemoryError: Unable to allocate array with shape (10000000, 50)
```

#### Solutions

**1. Use Chunking**
```python
# Instead of loading entire file
df = pd.read_csv('large_file.csv')

# Use chunks
chunk_size = 10000
chunks = []
for chunk in pd.read_csv('large_file.csv', chunksize=chunk_size):
    processed = process_chunk(chunk)
    chunks.append(processed)

result = pd.concat(chunks, ignore_index=True)
```

**2. Use Dask or Modin**
```python
# Using Dask
import dask.dataframe as dd

df = dd.read_csv('large_file.csv')
result = df.groupby('market_id').price.mean().compute()

# Using Modin
import modin.pandas as pd
df = pd.read_csv('large_file.csv')  # Automatically parallelized
```

**3. Optimize Data Types**
```python
# Reduce memory usage
def optimize_dtypes(df):
    for col in df.columns:
        col_type = df[col].dtype
        
        if col_type != 'object':
            c_min = df[col].min()
            c_max = df[col].max()
            
            if str(col_type)[:3] == 'int':
                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                    df[col] = df[col].astype(np.int8)
                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                    df[col] = df[col].astype(np.int16)
                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                    df[col] = df[col].astype(np.int32)
            else:
                if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:
                    df[col] = df[col].astype(np.float16)
                elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                    df[col] = df[col].astype(np.float32)
    
    return df
```

### Issue: Date Parsing Errors

#### Symptom
```python
ValueError: time data '2023-13-01' does not match format '%Y-%m-%d'
ParserError: Unknown string format: '15/Jan/2023'
```

#### Solutions

**1. Flexible Date Parsing**
```python
# Let pandas infer format
df['date'] = pd.to_datetime(df['date'], errors='coerce')

# Handle multiple formats
date_formats = ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%b-%Y']
for fmt in date_formats:
    try:
        df['date'] = pd.to_datetime(df['date'], format=fmt)
        break
    except ValueError:
        continue

# Custom parser
def parse_yemen_date(date_str):
    """Parse various date formats used in Yemen data."""
    if pd.isna(date_str):
        return pd.NaT
    
    # Handle Arabic month names
    arabic_months = {
        'يناير': '01', 'فبراير': '02', 'مارس': '03',
        'أبريل': '04', 'مايو': '05', 'يونيو': '06',
        'يوليو': '07', 'أغسطس': '08', 'سبتمبر': '09',
        'أكتوبر': '10', 'نوفمبر': '11', 'ديسمبر': '12'
    }
    
    for ar_month, num_month in arabic_months.items():
        date_str = date_str.replace(ar_month, num_month)
    
    return pd.to_datetime(date_str, errors='coerce')

df['date'] = df['date'].apply(parse_yemen_date)
```

## Model Training Issues

### Issue: Model Convergence Failures

#### Symptom
```python
ConvergenceWarning: Maximum number of iterations reached
RuntimeError: Model failed to converge after 1000 iterations
```

#### Solutions

**1. Scale Your Data**
```python
from sklearn.preprocessing import StandardScaler

# Scale features before modeling
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# For panel data
def scale_panel_data(df, feature_cols, group_col='market_id'):
    """Scale features within groups."""
    scaled_data = []
    
    for group, data in df.groupby(group_col):
        scaler = StandardScaler()
        data_copy = data.copy()
        data_copy[feature_cols] = scaler.fit_transform(data[feature_cols])
        scaled_data.append(data_copy)
    
    return pd.concat(scaled_data)
```

**2. Adjust Model Parameters**
```python
# Increase iterations
from statsmodels.regression.linear_model import OLS

model = OLS(y, X)
results = model.fit(maxiter=5000, method='newton')

# Use different optimizer
from scipy.optimize import minimize

result = minimize(
    objective_function,
    x0=initial_params,
    method='L-BFGS-B',  # Try different methods
    options={'maxiter': 10000, 'ftol': 1e-8}
)

# For threshold models
from yemen_market.models.three_tier import ThresholdVECM

model = ThresholdVECM(
    convergence_tol=1e-5,  # Relax tolerance
    max_iter=2000,
    step_size=0.1  # Smaller steps
)
```

**3. Check for Multicollinearity**
```python
from statsmodels.stats.outliers_influence import variance_inflation_factor

def check_vif(df, feature_cols):
    """Check Variance Inflation Factor."""
    vif_data = pd.DataFrame()
    vif_data["Feature"] = feature_cols
    vif_data["VIF"] = [
        variance_inflation_factor(df[feature_cols].values, i)
        for i in range(len(feature_cols))
    ]
    
    # Remove high VIF features
    high_vif = vif_data[vif_data["VIF"] > 10]
    if not high_vif.empty:
        print(f"High VIF features: {high_vif['Feature'].tolist()}")
        
    return vif_data
```

### Issue: Singular Matrix Errors

#### Symptom
```python
LinAlgError: Singular matrix
numpy.linalg.LinAlgError: Matrix is singular and cannot be inverted
```

#### Solutions

**1. Remove Perfect Collinearity**
```python
# Check correlation matrix
corr_matrix = df[numeric_cols].corr()
high_corr_pairs = []

for i in range(len(corr_matrix.columns)):
    for j in range(i+1, len(corr_matrix.columns)):
        if abs(corr_matrix.iloc[i, j]) > 0.95:
            high_corr_pairs.append((
                corr_matrix.columns[i],
                corr_matrix.columns[j],
                corr_matrix.iloc[i, j]
            ))

# Remove one from each highly correlated pair
for var1, var2, corr in high_corr_pairs:
    print(f"Dropping {var2} (correlation with {var1}: {corr:.3f})")
    df = df.drop(columns=[var2])
```

**2. Add Regularization**
```python
# Ridge regression for singular matrices
from sklearn.linear_model import Ridge

model = Ridge(alpha=0.1)  # Adjust alpha as needed
model.fit(X, y)

# Or use elastic net
from sklearn.linear_model import ElasticNet

model = ElasticNet(alpha=0.1, l1_ratio=0.5)
model.fit(X, y)
```

## API and External Service Issues

### Issue: API Rate Limiting

#### Symptom
```python
HTTPError: 429 Too Many Requests
RateLimitError: Rate limit exceeded. Try again in 3600 seconds.
```

#### Solutions

**1. Implement Exponential Backoff**
```python
import time
import random
from functools import wraps

def retry_with_backoff(max_retries=5, base_delay=1):
    """Decorator for exponential backoff retry."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except (HTTPError, RateLimitError) as e:
                    if attempt == max_retries - 1:
                        raise
                    
                    # Exponential backoff with jitter
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    print(f"Rate limited. Retrying in {delay:.1f} seconds...")
                    time.sleep(delay)
            
            return None
        return wrapper
    return decorator

@retry_with_backoff(max_retries=3)
def fetch_data_from_api(endpoint):
    response = requests.get(endpoint)
    response.raise_for_status()
    return response.json()
```

**2. Implement Request Caching**
```python
from functools import lru_cache
import hashlib
import json

class CachedAPIClient:
    def __init__(self, cache_dir='cache/api'):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
    
    def _get_cache_key(self, url, params):
        """Generate cache key from URL and parameters."""
        cache_str = f"{url}_{json.dumps(params, sort_keys=True)}"
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def fetch(self, url, params=None, cache_hours=24):
        """Fetch with caching."""
        cache_key = self._get_cache_key(url, params)
        cache_file = self.cache_dir / f"{cache_key}.json"
        
        # Check cache
        if cache_file.exists():
            age_hours = (time.time() - cache_file.stat().st_mtime) / 3600
            if age_hours < cache_hours:
                with open(cache_file) as f:
                    return json.load(f)
        
        # Fetch fresh data
        response = requests.get(url, params=params)
        response.raise_for_status()
        data = response.json()
        
        # Save to cache
        with open(cache_file, 'w') as f:
            json.dump(data, f)
        
        return data
```

### Issue: Connection Timeouts

#### Symptom
```python
TimeoutError: Connection timed out after 30 seconds
requests.exceptions.ConnectTimeout
```

#### Solutions

**1. Increase Timeout and Add Retries**
```python
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

def create_robust_session():
    """Create requests session with retry logic."""
    session = requests.Session()
    
    retry_strategy = Retry(
        total=3,
        status_forcelist=[429, 500, 502, 503, 504],
        method_whitelist=["HEAD", "GET", "OPTIONS"],
        backoff_factor=1
    )
    
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    # Set timeout for all requests
    session.request = partial(session.request, timeout=60)
    
    return session

# Usage
session = create_robust_session()
response = session.get(url)
```

## Performance Issues

### Issue: Slow Data Processing

#### Symptom
- Processing takes hours for moderate datasets
- High CPU usage but low throughput
- Memory usage keeps growing

#### Solutions

**1. Profile Your Code**
```python
import cProfile
import pstats
from io import StringIO

def profile_function(func):
    """Profile function execution."""
    profiler = cProfile.Profile()
    profiler.enable()
    
    result = func()
    
    profiler.disable()
    
    # Print results
    s = StringIO()
    ps = pstats.Stats(profiler, stream=s).sort_stats('cumulative')
    ps.print_stats(20)  # Top 20 functions
    print(s.getvalue())
    
    return result

# Or use line_profiler
# pip install line_profiler
# @profile decorator on functions
# kernprof -l -v script.py
```

**2. Optimize Pandas Operations**
```python
# Bad: Iterating over rows
for idx, row in df.iterrows():
    df.loc[idx, 'new_col'] = complex_function(row['col1'], row['col2'])

# Good: Vectorized operations
df['new_col'] = df.apply(lambda x: complex_function(x['col1'], x['col2']), axis=1)

# Better: Pure vectorization
df['new_col'] = complex_function_vectorized(df['col1'].values, df['col2'].values)

# For groupby operations
# Bad
results = []
for group, data in df.groupby('market'):
    results.append(process_group(data))
result_df = pd.concat(results)

# Good
result_df = df.groupby('market').apply(process_group)

# Better with parallel processing
from joblib import Parallel, delayed

def process_group_wrapper(name_data):
    name, data = name_data
    return process_group(data)

groups = list(df.groupby('market'))
results = Parallel(n_jobs=-1)(
    delayed(process_group_wrapper)(group) for group in groups
)
result_df = pd.concat(results)
```

**3. Use Numba for Numerical Operations**
```python
from numba import jit, prange

@jit(nopython=True, parallel=True)
def calculate_price_index_fast(prices, weights):
    """Fast price index calculation."""
    n_periods = prices.shape[0]
    index = np.zeros(n_periods)
    
    for t in prange(n_periods):
        weighted_sum = 0.0
        weight_sum = 0.0
        
        for i in range(prices.shape[1]):
            if not np.isnan(prices[t, i]):
                weighted_sum += prices[t, i] * weights[i]
                weight_sum += weights[i]
        
        if weight_sum > 0:
            index[t] = weighted_sum / weight_sum
        else:
            index[t] = np.nan
    
    return index
```

## Testing Issues

### Issue: Flaky Tests

#### Symptom
- Tests pass locally but fail in CI
- Intermittent test failures
- Different results on different runs

#### Solutions

**1. Fix Random Seeds**
```python
import random
import numpy as np
import pytest

@pytest.fixture(autouse=True)
def set_random_seeds():
    """Set random seeds for reproducibility."""
    random.seed(42)
    np.random.seed(42)
    
    # For TensorFlow/Keras
    # import tensorflow as tf
    # tf.random.set_seed(42)
    
    yield
    
    # Reset after test
    random.seed()
    np.random.seed()
```

**2. Mock Time-Dependent Code**
```python
from unittest.mock import patch
from datetime import datetime

@patch('yemen_market.utils.datetime')
def test_time_dependent_function(mock_datetime):
    """Test with fixed time."""
    mock_datetime.now.return_value = datetime(2023, 1, 1, 12, 0, 0)
    mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
    
    result = function_that_uses_current_time()
    assert result['timestamp'] == '2023-01-01 12:00:00'
```

**3. Isolate File System Operations**
```python
import tempfile
from pathlib import Path

@pytest.fixture
def isolated_filesystem():
    """Create isolated filesystem for testing."""
    with tempfile.TemporaryDirectory() as tmpdir:
        old_cwd = os.getcwd()
        os.chdir(tmpdir)
        yield Path(tmpdir)
        os.chdir(old_cwd)
```

## Debugging Techniques

### Using Python Debugger

```python
# Insert breakpoint
import pdb; pdb.set_trace()

# Or in Python 3.7+
breakpoint()

# In pytest
pytest --pdb  # Drop to debugger on failure

# In IPython/Jupyter
%debug  # Post-mortem debugging
```

### Logging for Debugging

```python
import logging
import functools

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(funcName)s:%(lineno)d - %(message)s'
)

def debug_log(func):
    """Decorator to log function calls."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        logger.debug(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"{func.__name__} returned {result}")
            return result
        except Exception as e:
            logger.exception(f"{func.__name__} raised {type(e).__name__}: {e}")
            raise
    
    return wrapper

@debug_log
def problematic_function(x, y):
    return x / y  # Will log division by zero
```

### Memory Debugging

```python
# Track memory usage
from memory_profiler import profile

@profile
def memory_intensive_function():
    # Your code here
    pass

# Run with: python -m memory_profiler script.py

# Or use tracemalloc
import tracemalloc

tracemalloc.start()

# Your code here

current, peak = tracemalloc.get_traced_memory()
print(f"Current memory usage: {current / 10**6:.1f} MB")
print(f"Peak memory usage: {peak / 10**6:.1f} MB")

# Get top memory allocations
snapshot = tracemalloc.take_snapshot()
top_stats = snapshot.statistics('lineno')

for stat in top_stats[:10]:
    print(stat)
```

## Getting Help

### Resources

1. **Error Messages**: Always read the full stack trace
2. **Documentation**: Check official docs for the library
3. **GitHub Issues**: Search for similar issues
4. **Stack Overflow**: Search with specific error messages
5. **Community**: Yemen Market Slack channel

### Creating Good Bug Reports

```markdown
## Issue Description
Brief description of the issue

## Environment
- OS: Ubuntu 20.04
- Python: 3.9.7
- yemen-market version: 1.2.0
- Related packages: pandas==1.3.0, numpy==1.21.0

## Steps to Reproduce
1. Load data with `pd.read_csv('file.csv')`
2. Run `process_data(df)`
3. Error occurs

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## Error Message
```
Full error traceback here
```

## Minimal Reproducible Example
```python
import pandas as pd
from yemen_market import process_data

# Minimal code that reproduces the issue
df = pd.DataFrame({'a': [1, 2, None]})
result = process_data(df)  # Error here
```
```

## Next Steps

- See [Performance Profiling](performance-profiling.md) for optimization
- See [Memory Profiling](memory-profiling.md) for memory issues
- See [Unit Testing](../testing/unit-testing.md) for test debugging
- See [Logging Guide](../../02-user-guides/logging-monitoring.md) for logging setup