# Docker Production Deployment

## 🎯 Target Audience

- **DevOps Engineers**: Deploying to production environments
- **System Administrators**: Managing production containers
- **Security Teams**: Ensuring secure deployments

## 📋 Overview

This guide covers production-grade Docker deployment for the Yemen Market Integration Platform, focusing on security, performance, and reliability for econometric analysis workloads.

## 🔒 Production Dockerfile

```dockerfile
# docker/Dockerfile.prod
# Multi-stage build for optimized production image
FROM python:3.11-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    gfortran \
    libopenblas-dev \
    liblapack-dev \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy and install requirements
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:3.11-slim

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    libopenblas0 \
    liblapack3 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -g 1000 ymip && \
    useradd -r -u 1000 -g ymip ymip

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv

# Set up application directory
WORKDIR /app
RUN chown -R ymip:ymip /app

# Copy application code
COPY --chown=ymip:ymip . .

# Install application
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-deps .

# Security: Remove unnecessary files
RUN find . -name "*.pyc" -delete && \
    find . -name "__pycache__" -type d -delete && \
    rm -rf tests/ notebooks/ scripts/

# Switch to non-root user
USER ymip

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Expose application port
EXPOSE 8000

# Production environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    YMIP_ENV=production

# Run with gunicorn
CMD ["gunicorn", "yemen_market.api:app", \
     "--bind", "0.0.0.0:8000", \
     "--workers", "4", \
     "--worker-class", "uvicorn.workers.UvicornWorker", \
     "--timeout", "300", \
     "--access-logfile", "-", \
     "--error-logfile", "-"]
```

## 🏗️ Production Compose Stack

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    image: ymip:production
    build:
      context: .
      dockerfile: docker/Dockerfile.prod
      cache_from:
        - ymip:production
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '2'
          memory: 8G
        reservations:
          cpus: '1'
          memory: 4G
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    environment:
      - YMIP_ENV=production
      - YMIP_DB_HOST=postgres
      - YMIP_REDIS_HOST=redis
      - YMIP_LOG_LEVEL=INFO
    env_file:
      - .env.production
    networks:
      - ymip-prod
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    labels:
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=8000"
      - "prometheus.io/path=/metrics"

  postgres:
    image: postgres:15-alpine
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
    environment:
      POSTGRES_DB: yemen_market
      POSTGRES_USER_FILE: /run/secrets/db_user
      POSTGRES_PASSWORD_FILE: /run/secrets/db_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    secrets:
      - db_user
      - db_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/postgres_backup.sh:/usr/local/bin/backup.sh:ro
    networks:
      - ymip-prod
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $$(cat /run/secrets/db_user)"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 2G
    command: >
      redis-server
      --requirepass $${REDIS_PASSWORD}
      --maxmemory 1gb
      --maxmemory-policy volatile-lru
      --save 60 1000
      --appendonly yes
    environment:
      REDIS_PASSWORD_FILE: /run/secrets/redis_password
    secrets:
      - redis_password
    volumes:
      - redis_data:/data
    networks:
      - ymip-prod
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  nginx:
    image: nginx:alpine
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_cache:/var/cache/nginx
    networks:
      - ymip-prod
    depends_on:
      - app

  worker:
    image: ymip:production
    command: ["celery", "-A", "yemen_market.tasks", "worker", "--loglevel=info"]
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '4'
          memory: 16G
    environment:
      - YMIP_ENV=production
      - YMIP_WORKER_TYPE=compute
    env_file:
      - .env.production
    networks:
      - ymip-prod
    depends_on:
      - redis
      - postgres

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nginx_cache:
    driver: local

networks:
  ymip-prod:
    driver: overlay
    encrypted: true

secrets:
  db_user:
    external: true
  db_password:
    external: true
  redis_password:
    external: true
```

## 🔐 Security Configuration

### Environment Variables
```bash
# .env.production (DO NOT COMMIT)
YMIP_SECRET_KEY=<generated-secret>
YMIP_DB_HOST=postgres
YMIP_DB_PORT=5432
YMIP_DB_NAME=yemen_market
YMIP_REDIS_URL=redis://redis:6379/0
YMIP_ALLOWED_HOSTS=api.yemen-market.org
YMIP_CORS_ORIGINS=https://yemen-market.org
YMIP_SENTRY_DSN=https://<EMAIL>/yyy
```

### Nginx Configuration
```nginx
# nginx/nginx.conf
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'" always;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_status 429;

    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    upstream app {
        least_conn;
        server app:8000 max_fails=3 fail_timeout=30s;
    }

    server {
        listen 80;
        server_name api.yemen-market.org;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name api.yemen-market.org;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts for long-running analyses
            proxy_connect_timeout 60s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }

        # Health check endpoint
        location /health {
            access_log off;
            proxy_pass http://app/health;
        }

        # Static files with caching
        location /static/ {
            alias /app/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

## 🚀 Deployment Process

### 1. Build and Tag Image
```bash
# Build production image
docker build -f docker/Dockerfile.prod -t ymip:production .

# Tag for registry
docker tag ymip:production registry.yemen-market.org/ymip:v1.0.0
docker tag ymip:production registry.yemen-market.org/ymip:latest

# Push to registry
docker push registry.yemen-market.org/ymip:v1.0.0
docker push registry.yemen-market.org/ymip:latest
```

### 2. Deploy with Docker Swarm
```bash
# Initialize swarm (if not already)
docker swarm init

# Create secrets
echo "ymip_prod_user" | docker secret create db_user -
echo "secure_password" | docker secret create db_password -
echo "redis_password" | docker secret create redis_password -

# Deploy stack
docker stack deploy -c docker-compose.prod.yml ymip

# Check deployment status
docker stack services ymip
docker service logs ymip_app
```

### 3. Zero-Downtime Updates
```bash
# Update service with new image
docker service update \
    --image registry.yemen-market.org/ymip:v1.1.0 \
    --update-parallelism 1 \
    --update-delay 10s \
    ymip_app

# Monitor rollout
docker service ps ymip_app
```

## 📊 Performance Optimization

### Resource Limits
```yaml
# Optimized for econometric workloads
deploy:
  resources:
    limits:
      cpus: '4'
      memory: 16G
    reservations:
      cpus: '2'
      memory: 8G
```

### Caching Strategy
```python
# Application-level caching
from functools import lru_cache
import redis

redis_client = redis.from_url(os.environ['YMIP_REDIS_URL'])

@lru_cache(maxsize=128)
def get_cached_results(model_id: str, params_hash: str):
    """Cache expensive model results"""
    cache_key = f"model_results:{model_id}:{params_hash}"
    cached = redis_client.get(cache_key)
    if cached:
        return json.loads(cached)
    return None
```

## 🔍 Monitoring Integration

### Prometheus Metrics
```python
# src/yemen_market/metrics.py
from prometheus_client import Counter, Histogram, Gauge

# Model metrics
model_runs = Counter('ymip_model_runs_total', 
                    'Total model runs', 
                    ['model_type', 'status'])
model_duration = Histogram('ymip_model_duration_seconds',
                          'Model execution time',
                          ['model_type'])
active_analyses = Gauge('ymip_active_analyses',
                       'Currently running analyses')
```

### Health Checks
```python
# src/yemen_market/api/health.py
from fastapi import APIRouter
from sqlalchemy import text

router = APIRouter()

@router.get("/health")
async def health_check(db: Session, redis: Redis):
    """Comprehensive health check"""
    checks = {
        "status": "healthy",
        "checks": {
            "database": False,
            "redis": False,
            "disk_space": False
        }
    }
    
    # Database check
    try:
        db.execute(text("SELECT 1"))
        checks["checks"]["database"] = True
    except Exception as e:
        checks["status"] = "unhealthy"
    
    # Redis check
    try:
        redis.ping()
        checks["checks"]["redis"] = True
    except Exception:
        checks["status"] = "unhealthy"
    
    # Disk space check
    stat = os.statvfs('/app/data')
    free_gb = (stat.f_frsize * stat.f_bavail) / (1024**3)
    checks["checks"]["disk_space"] = free_gb > 10
    
    return checks
```

## 🛡️ Security Hardening

### Image Scanning
```bash
# Scan for vulnerabilities
docker scan ymip:production

# Use Trivy for comprehensive scanning
trivy image ymip:production
```

### Runtime Security
```yaml
# Security options in compose
security_opt:
  - no-new-privileges:true
  - apparmor:docker-default
read_only: true
tmpfs:
  - /tmp
  - /var/run
```

## 📈 Scaling Strategies

### Horizontal Scaling
```bash
# Scale app service
docker service scale ymip_app=5

# Scale workers for heavy computation
docker service scale ymip_worker=10
```

### Auto-scaling with Metrics
```yaml
# docker-compose.prod.yml addition
deploy:
  replicas: 3
  update_config:
    parallelism: 1
    delay: 10s
  restart_policy:
    condition: any
    delay: 5s
    max_attempts: 3
  labels:
    - "autoscaling.min=2"
    - "autoscaling.max=10"
    - "autoscaling.cpu=70"
```

## 🔧 Troubleshooting

### Common Issues

1. **Memory Pressure**
```bash
# Check memory usage
docker stats --no-stream

# Increase memory limits
docker service update --limit-memory 32G ymip_worker
```

2. **Slow Queries**
```bash
# Access PostgreSQL
docker exec -it $(docker ps -qf "name=postgres") psql -U ymip_user

# Check slow queries
SELECT query, calls, mean_exec_time 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;
```

3. **Container Crashes**
```bash
# Check logs
docker service logs ymip_app --tail 100

# Debug container
docker run -it --rm ymip:production /bin/bash
```

## 📚 Additional Resources

- [Docker Security Best Practices](https://docs.docker.com/develop/security-best-practices/)
- [Production Checklist](https://docs.docker.com/develop/dev-best-practices/)
- [Container Monitoring](https://prometheus.io/docs/guides/cadvisor/)