# Docker Development Environment

## 🎯 Target Audience

- **Researchers**: Running econometric models locally
- **Developers**: Building and testing new features
- **Data Scientists**: Experimenting with analysis pipelines

## 📋 Overview

This guide covers setting up a Docker-based development environment for the Yemen Market Integration Platform, optimized for econometric research and data analysis workflows.

## 🔧 Prerequisites

- Docker Desktop 4.0+ (includes Docker Compose)
- 16GB RAM minimum (32GB recommended)
- 50GB free disk space
- Git for version control

## 🏗️ Development Dockerfile

```dockerfile
# docker/Dockerfile.dev
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    gfortran \
    libopenblas-dev \
    liblapack-dev \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir \
    jupyter \
    ipdb \
    pytest-watch \
    black \
    flake8

# Copy application code
COPY . .

# Install package in development mode
RUN pip install -e .

# Expose ports
EXPOSE 8888 8000 5000

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV JUPYTER_ENABLE_LAB=yes
ENV YMIP_ENV=development

# Default command
CMD ["jupyter", "lab", "--ip=0.0.0.0", "--no-browser", "--allow-root"]
```

## 🐳 Docker Compose Configuration

```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  ymip-dev:
    build:
      context: .
      dockerfile: docker/Dockerfile.dev
    container_name: ymip-dev
    volumes:
      - .:/app
      - ./data:/app/data
      - ./notebooks:/app/notebooks
      - ./results:/app/results
    ports:
      - "8888:8888"  # Jupyter Lab
      - "8000:8000"  # API server
      - "5000:5000"  # Dashboard
    environment:
      - PYTHONPATH=/app
      - YMIP_ENV=development
      - YMIP_DATA_PATH=/app/data
      - YMIP_LOG_LEVEL=DEBUG
    stdin_open: true
    tty: true
    networks:
      - ymip-network

  postgres:
    image: postgres:15-alpine
    container_name: ymip-postgres
    environment:
      POSTGRES_DB: yemen_market
      POSTGRES_USER: ymip_user
      POSTGRES_PASSWORD: dev_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - ymip-network

  redis:
    image: redis:7-alpine
    container_name: ymip-redis
    ports:
      - "6379:6379"
    networks:
      - ymip-network

volumes:
  postgres_data:

networks:
  ymip-network:
    driver: bridge
```

## 🚀 Quick Start

### 1. Build Development Image
```bash
# Clone repository
git clone https://github.com/yemen-market-integration/platform.git
cd platform

# Build development image
docker-compose -f docker-compose.dev.yml build

# Start services
docker-compose -f docker-compose.dev.yml up -d
```

### 2. Access Development Tools
```bash
# Access Jupyter Lab
open http://localhost:8888

# Access container shell
docker exec -it ymip-dev bash

# Run tests in watch mode
docker exec -it ymip-dev pytest-watch

# Format code
docker exec -it ymip-dev black src/ tests/
```

### 3. Run Econometric Models
```bash
# Inside container
python scripts/run_three_tier_models.py \
    --data-path=/app/data/processed \
    --output-path=/app/results/dev

# Or using Docker exec
docker exec -it ymip-dev python scripts/run_three_tier_models.py
```

## 💻 VSCode Integration

```json
// .vscode/settings.json
{
    "python.defaultInterpreterPath": "/usr/local/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true,
    "docker.showExplorer": true,
    "remote.containers.defaultExtensions": [
        "ms-python.python",
        "ms-python.vscode-pylance",
        "ms-toolsai.jupyter"
    ]
}
```

### DevContainer Configuration
```json
// .devcontainer/devcontainer.json
{
    "name": "YMIP Development",
    "dockerComposeFile": "../docker-compose.dev.yml",
    "service": "ymip-dev",
    "workspaceFolder": "/app",
    "settings": {
        "terminal.integrated.shell.linux": "/bin/bash"
    },
    "extensions": [
        "ms-python.python",
        "ms-toolsai.jupyter",
        "ms-azuretools.vscode-docker"
    ],
    "postCreateCommand": "pip install -e .",
    "remoteUser": "root"
}
```

## 🔍 Debugging Configuration

```python
# .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug: Run Analysis",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/scripts/run_analysis.py",
            "args": ["--debug", "--data-path=/app/data"],
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "Debug: Current File",
            "type": "python",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        }
    ]
}
```

## 📊 Data Volume Management

```bash
# Create named volumes for persistent data
docker volume create ymip-data
docker volume create ymip-results

# Mount in docker-compose
volumes:
  - ymip-data:/app/data:rw
  - ymip-results:/app/results:rw

# Backup volumes
docker run --rm -v ymip-data:/data -v $(pwd):/backup \
    alpine tar czf /backup/data-backup.tar.gz -C /data .
```

## 🛠️ Development Workflows

### Running Tests
```bash
# Run all tests
docker exec -it ymip-dev pytest

# Run specific test file
docker exec -it ymip-dev pytest tests/test_models.py

# Run with coverage
docker exec -it ymip-dev pytest --cov=src --cov-report=html
```

### Data Pipeline Development
```bash
# Process new data
docker exec -it ymip-dev python scripts/process_wfp_data.py \
    --input=/app/data/raw/wfp \
    --output=/app/data/processed

# Run spatial joins
docker exec -it ymip-dev python scripts/run_spatial_joins.py
```

### Model Development
```python
# Example: Developing new model in Jupyter
# Access at http://localhost:8888

import sys
sys.path.append('/app')

from yemen_market.models.three_tier import ThreeTierModel
from yemen_market.data import PanelBuilder

# Load data
panel_builder = PanelBuilder()
panel_data = panel_builder.build_balanced_panel()

# Test model
model = ThreeTierModel()
results = model.fit(panel_data)
```

## 🔧 Troubleshooting

### Common Issues

1. **Memory Issues**
```bash
# Increase Docker memory allocation
# Docker Desktop > Preferences > Resources > Memory: 16GB
```

2. **Permission Issues**
```bash
# Fix volume permissions
docker exec -it ymip-dev chown -R $(id -u):$(id -g) /app/data
```

3. **Port Conflicts**
```bash
# Check for port usage
lsof -i :8888
# Change ports in docker-compose.dev.yml if needed
```

### Performance Optimization
```yaml
# docker-compose.dev.yml optimizations
services:
  ymip-dev:
    build:
      context: .
      dockerfile: docker/Dockerfile.dev
      args:
        BUILDKIT_INLINE_CACHE: 1
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 16G
        reservations:
          cpus: '2'
          memory: 8G
```

## 📚 Additional Resources

- [Docker Best Practices](https://docs.docker.com/develop/dev-best-practices/)
- [Python Development in Docker](https://pythonspeed.com/docker/)
- [Jupyter in Docker](https://jupyter-docker-stacks.readthedocs.io/)

## 🆘 Getting Help

- Check logs: `docker-compose -f docker-compose.dev.yml logs`
- Shell access: `docker exec -it ymip-dev bash`
- Reset environment: `docker-compose -f docker-compose.dev.yml down -v`