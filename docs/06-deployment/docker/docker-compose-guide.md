# Docker Compose Orchestration Guide

## 🎯 Target Audience

- **DevOps Engineers**: Managing multi-container deployments
- **Data Engineers**: Orchestrating data pipelines
- **Researchers**: Running complete analysis environments

## 📋 Overview

This guide covers Docker Compose orchestration for the Yemen Market Integration Platform, including development, testing, and production configurations for the complete econometric analysis stack.

## 🏗️ Complete Stack Architecture

```yaml
# docker-compose.full.yml
version: '3.8'

x-common-variables: &common-variables
  YMIP_ENV: ${YMIP_ENV:-development}
  YMIP_LOG_LEVEL: ${YMIP_LOG_LEVEL:-INFO}
  TZ: UTC

x-app-defaults: &app-defaults
  image: ymip:${YMIP_VERSION:-latest}
  restart: unless-stopped
  networks:
    - ymip-network
  environment:
    <<: *common-variables

services:
  # Core Application Services
  api:
    <<: *app-defaults
    container_name: ymip-api
    command: ["gunicorn", "yemen_market.api:app", "--bind", "0.0.0.0:8000"]
    ports:
      - "${API_PORT:-8000}:8000"
    environment:
      <<: *common-variables
      YMIP_SERVICE_NAME: api
      YMIP_DB_URL: postgresql://ymip:${DB_PASSWORD}@postgres:5432/yemen_market
      YMIP_REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Data Processing Workers
  worker-compute:
    <<: *app-defaults
    container_name: ymip-worker-compute
    command: ["celery", "-A", "yemen_market.tasks", "worker", "-Q", "compute", "-c", "2"]
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 16G
    environment:
      <<: *common-variables
      YMIP_SERVICE_NAME: worker-compute
      YMIP_WORKER_CONCURRENCY: 2
      C_FORCE_ROOT: 'true'

  worker-io:
    <<: *app-defaults
    container_name: ymip-worker-io
    command: ["celery", "-A", "yemen_market.tasks", "worker", "-Q", "io", "-c", "4"]
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 8G
    environment:
      <<: *common-variables
      YMIP_SERVICE_NAME: worker-io
      YMIP_WORKER_CONCURRENCY: 4

  # Scheduler
  scheduler:
    <<: *app-defaults
    container_name: ymip-scheduler
    command: ["celery", "-A", "yemen_market.tasks", "beat", "-l", "info"]
    environment:
      <<: *common-variables
      YMIP_SERVICE_NAME: scheduler

  # Dashboard
  dashboard:
    <<: *app-defaults
    container_name: ymip-dashboard
    command: ["streamlit", "run", "scripts/streamlit_dashboard.py", "--server.port=8501"]
    ports:
      - "${DASHBOARD_PORT:-8501}:8501"
    environment:
      <<: *common-variables
      YMIP_SERVICE_NAME: dashboard
    volumes:
      - ./data:/app/data:ro
      - ./results:/app/results:ro

  # Database
  postgres:
    image: postgres:15-alpine
    container_name: ymip-postgres
    environment:
      POSTGRES_DB: yemen_market
      POSTGRES_USER: ymip
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/01_init.sql:ro
      - ./scripts/create_extensions.sql:/docker-entrypoint-initdb.d/02_extensions.sql:ro
    ports:
      - "${DB_PORT:-5432}:5432"
    networks:
      - ymip-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ymip"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Cache
  redis:
    image: redis:7-alpine
    container_name: ymip-redis
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 2gb
      --maxmemory-policy allkeys-lru
      --save ""
    ports:
      - "${REDIS_PORT:-6379}:6379"
    networks:
      - ymip-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Jupyter Lab for Research
  jupyter:
    <<: *app-defaults
    container_name: ymip-jupyter
    command: >
      jupyter lab
      --ip=0.0.0.0
      --no-browser
      --NotebookApp.token='${JUPYTER_TOKEN}'
    ports:
      - "${JUPYTER_PORT:-8888}:8888"
    volumes:
      - ./notebooks:/app/notebooks
      - ./data:/app/data
      - ./results:/app/results
    environment:
      <<: *common-variables
      YMIP_SERVICE_NAME: jupyter

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:latest
    container_name: ymip-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - ymip-network

  grafana:
    image: grafana/grafana:latest
    container_name: ymip-grafana
    ports:
      - "${GRAFANA_PORT:-3000}:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - ymip-network
    depends_on:
      - prometheus

  # Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: ymip-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - static_files:/usr/share/nginx/html/static:ro
    networks:
      - ymip-network
    depends_on:
      - api
      - dashboard

  # Backup Service
  backup:
    image: postgres:15-alpine
    container_name: ymip-backup
    environment:
      PGPASSWORD: ${DB_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    entrypoint: ["/bin/sh"]
    command: ["-c", "while true; do /backup.sh; sleep 86400; done"]
    networks:
      - ymip-network
    depends_on:
      - postgres

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  static_files:

networks:
  ymip-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

## 🔧 Environment Configuration

### Development Environment
```bash
# .env.development
YMIP_ENV=development
YMIP_VERSION=dev
YMIP_LOG_LEVEL=DEBUG

# Database
DB_PASSWORD=dev_password
DB_PORT=5432

# Redis
REDIS_PASSWORD=dev_redis_password
REDIS_PORT=6379

# Services
API_PORT=8000
DASHBOARD_PORT=8501
JUPYTER_PORT=8888
JUPYTER_TOKEN=development_token

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
GRAFANA_PASSWORD=admin
```

### Production Environment
```bash
# .env.production
YMIP_ENV=production
YMIP_VERSION=1.0.0
YMIP_LOG_LEVEL=INFO

# Database (use secrets in production)
DB_PASSWORD=${DB_PASSWORD_SECRET}
DB_PORT=5432

# Redis
REDIS_PASSWORD=${REDIS_PASSWORD_SECRET}
REDIS_PORT=6379

# Services
API_PORT=8000
DASHBOARD_PORT=8501

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
GRAFANA_PASSWORD=${GRAFANA_PASSWORD_SECRET}
```

## 🚀 Deployment Commands

### Starting the Stack
```bash
# Development
docker-compose -f docker-compose.full.yml --env-file .env.development up -d

# Production
docker-compose -f docker-compose.full.yml --env-file .env.production up -d

# Specific services only
docker-compose up -d api postgres redis

# Scale workers
docker-compose up -d --scale worker-compute=3 --scale worker-io=2
```

### Managing Services
```bash
# View logs
docker-compose logs -f api worker-compute

# Restart service
docker-compose restart api

# Execute commands
docker-compose exec api python scripts/run_analysis.py

# Stop services
docker-compose stop

# Remove everything
docker-compose down -v
```

## 📊 Service Profiles

### Minimal Research Profile
```yaml
# docker-compose.research.yml
version: '3.8'

services:
  jupyter:
    extends:
      file: docker-compose.full.yml
      service: jupyter
    ports:
      - "8888:8888"
  
  postgres:
    extends:
      file: docker-compose.full.yml
      service: postgres
    ports:
      - "5432:5432"
```

### Data Processing Profile
```yaml
# docker-compose.processing.yml
version: '3.8'

services:
  worker-compute:
    extends:
      file: docker-compose.full.yml
      service: worker-compute
    scale: 4
  
  worker-io:
    extends:
      file: docker-compose.full.yml
      service: worker-io
    scale: 2
  
  redis:
    extends:
      file: docker-compose.full.yml
      service: redis
  
  scheduler:
    extends:
      file: docker-compose.full.yml
      service: scheduler
```

## 🔍 Health Monitoring

### Compose Health Checks
```yaml
healthcheck:
  test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health').raise_for_status()"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s
```

### Monitoring Dashboard
```yaml
# monitoring/grafana/dashboards/ymip-overview.json
{
  "dashboard": {
    "title": "YMIP Overview",
    "panels": [
      {
        "title": "API Request Rate",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])"
          }
        ]
      },
      {
        "title": "Model Execution Time",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, ymip_model_duration_seconds)"
          }
        ]
      }
    ]
  }
}
```

## 🛠️ Advanced Configurations

### Resource Constraints
```yaml
deploy:
  resources:
    limits:
      cpus: '4.0'
      memory: 16G
    reservations:
      cpus: '2.0'
      memory: 8G
```

### Network Isolation
```yaml
networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true
  monitoring:
    driver: bridge
    internal: true

services:
  api:
    networks:
      - frontend
      - backend
  
  postgres:
    networks:
      - backend
```

### Volume Optimization
```yaml
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/fast-ssd/postgres
  
  model_cache:
    driver: local
    driver_opts:
      type: tmpfs
      o: size=2g,uid=1000,gid=1000
```

## 🔐 Security Best Practices

### Secrets Management
```bash
# Create secrets
echo "secure_password" | docker secret create db_password -
echo "redis_secret" | docker secret create redis_password -

# Use in compose
services:
  postgres:
    secrets:
      - db_password
    environment:
      POSTGRES_PASSWORD_FILE: /run/secrets/db_password
```

### Network Security
```yaml
services:
  api:
    networks:
      - frontend
      - backend
    deploy:
      placement:
        constraints:
          - node.labels.zone == dmz
```

## 📈 Performance Tuning

### Dependency Optimization
```yaml
depends_on:
  postgres:
    condition: service_healthy
  redis:
    condition: service_started
```

### Build Optimization
```yaml
build:
  context: .
  dockerfile: Dockerfile
  cache_from:
    - ymip:latest
  args:
    BUILDKIT_INLINE_CACHE: 1
```

## 🔧 Troubleshooting

### Common Issues

1. **Service Dependencies**
```bash
# Check service health
docker-compose ps
docker-compose exec postgres pg_isready

# Force recreate
docker-compose up -d --force-recreate api
```

2. **Network Issues**
```bash
# Inspect network
docker network inspect yemen-market-integration_ymip-network

# Test connectivity
docker-compose exec api ping postgres
```

3. **Volume Permissions**
```bash
# Fix permissions
docker-compose exec api chown -R 1000:1000 /app/data

# Check mounts
docker-compose exec api df -h
```

### Debugging Commands
```bash
# Interactive shell
docker-compose exec api /bin/bash

# View environment
docker-compose exec api env | grep YMIP

# Check processes
docker-compose exec api ps aux
```

## 📚 Additional Resources

- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Compose Best Practices](https://docs.docker.com/compose/production/)
- [Container Orchestration Patterns](https://www.docker.com/blog/best-practices-for-compose-managed-python-applications/)