# Google Cloud Platform Deployment Guide

## 🎯 Target Audience

- **Cloud Architects**: Designing GCP infrastructure
- **DevOps Engineers**: Implementing GCP deployments
- **Data Scientists**: Leveraging GCP's ML capabilities

## 📋 Overview

This guide covers deploying the Yemen Market Integration Platform on Google Cloud Platform (GCP), utilizing GCP's data analytics and machine learning services for advanced econometric analysis.

## 🏗️ GCP Architecture

```mermaid
graph TB
    subgraph "Internet"
        Users[Research Users]
        API[API Clients]
    end
    
    subgraph "Google Cloud Platform"
        subgraph "Global Services"
            CDN[Cloud CDN]
            LB[Global Load Balancer]
            Armor[Cloud Armor]
        end
        
        subgraph "Regional Services - us-central1"
            subgraph "VPC - ymip-network"
                subgraph "Public Subnet - ********/24"
                    NAT[Cloud NAT]
                end
                
                subgraph "Private Subnet - ********/24"
                    GKE[GKE Cluster]
                    CR[Cloud Run]
                end
                
                subgraph "Data Subnet - ********/24"
                    SQL[Cloud SQL]
                    Memorystore[Memorystore Redis]
                end
            end
            
            subgraph "Analytics"
                BQ[BigQuery]
                Dataflow[Dataflow]
                Composer[Cloud Composer]
            end
            
            subgraph "Storage"
                GCS[Cloud Storage]
                Firestore[Firestore]
            end
            
            subgraph "ML Platform"
                VertexAI[Vertex AI]
                Notebooks[Vertex Notebooks]
            end
        end
        
        subgraph "Operations"
            Monitoring[Cloud Monitoring]
            Logging[Cloud Logging]
            Trace[Cloud Trace]
        end
    end
    
    Users --> CDN
    API --> CDN
    CDN --> LB
    LB --> GKE
    GKE --> SQL
    GKE --> Memorystore
    GKE --> GCS
    Dataflow --> BQ
    Composer --> Dataflow
```

## 🚀 Infrastructure as Code

### Terraform Configuration
```hcl
# terraform/main.tf
terraform {
  required_version = ">= 1.0"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 5.0"
    }
  }
  
  backend "gcs" {
    bucket = "ymip-terraform-state"
    prefix = "terraform/state"
  }
}

locals {
  project_id = var.project_id
  region     = var.region
  zone       = "${var.region}-a"
  
  labels = {
    project     = "yemen-market-integration"
    environment = var.environment
    managed_by  = "terraform"
  }
}

# Enable required APIs
resource "google_project_service" "apis" {
  for_each = toset([
    "compute.googleapis.com",
    "container.googleapis.com",
    "sqladmin.googleapis.com",
    "redis.googleapis.com",
    "bigquery.googleapis.com",
    "dataflow.googleapis.com",
    "composer.googleapis.com",
    "aiplatform.googleapis.com",
    "monitoring.googleapis.com",
    "logging.googleapis.com"
  ])
  
  project = local.project_id
  service = each.value
  
  disable_on_destroy = false
}

# VPC Network
resource "google_compute_network" "vpc" {
  name                    = "ymip-network-${var.environment}"
  project                 = local.project_id
  auto_create_subnetworks = false
  routing_mode            = "REGIONAL"
  
  depends_on = [google_project_service.apis]
}

# Subnets
resource "google_compute_subnetwork" "public" {
  name          = "ymip-public-${var.environment}"
  project       = local.project_id
  region        = local.region
  network       = google_compute_network.vpc.id
  ip_cidr_range = "********/24"
  
  log_config {
    aggregation_interval = "INTERVAL_5_MIN"
    flow_sampling        = 0.5
  }
}

resource "google_compute_subnetwork" "private" {
  name                     = "ymip-private-${var.environment}"
  project                  = local.project_id
  region                   = local.region
  network                  = google_compute_network.vpc.id
  ip_cidr_range            = "********/24"
  private_ip_google_access = true
  
  secondary_ip_range {
    range_name    = "gke-pods"
    ip_cidr_range = "********/16"
  }
  
  secondary_ip_range {
    range_name    = "gke-services"
    ip_cidr_range = "********/16"
  }
}

# Cloud NAT
resource "google_compute_router" "router" {
  name    = "ymip-router-${var.environment}"
  project = local.project_id
  region  = local.region
  network = google_compute_network.vpc.id
}

resource "google_compute_router_nat" "nat" {
  name                               = "ymip-nat-${var.environment}"
  project                            = local.project_id
  router                             = google_compute_router.router.name
  region                             = local.region
  nat_ip_allocate_option             = "AUTO_ONLY"
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
  
  log_config {
    enable = true
    filter = "ERRORS_ONLY"
  }
}

# GKE Cluster
resource "google_container_cluster" "primary" {
  name     = "ymip-gke-${var.environment}"
  project  = local.project_id
  location = local.region
  
  # Use release channel for automatic upgrades
  release_channel {
    channel = var.environment == "production" ? "STABLE" : "REGULAR"
  }
  
  # Workload Identity
  workload_identity_config {
    workload_pool = "${local.project_id}.svc.id.goog"
  }
  
  # Network configuration
  network    = google_compute_network.vpc.name
  subnetwork = google_compute_subnetwork.private.name
  
  ip_allocation_policy {
    cluster_secondary_range_name  = "gke-pods"
    services_secondary_range_name = "gke-services"
  }
  
  # Security
  private_cluster_config {
    enable_private_nodes    = true
    enable_private_endpoint = false
    master_ipv4_cidr_block  = "**********/28"
  }
  
  # Monitoring
  monitoring_config {
    enable_components = ["SYSTEM_COMPONENTS", "WORKLOADS"]
    managed_prometheus {
      enabled = true
    }
  }
  
  # Initial node pool (will be removed)
  initial_node_count       = 1
  remove_default_node_pool = true
  
  resource_labels = local.labels
}

# Node pools
resource "google_container_node_pool" "system" {
  name     = "system-pool"
  project  = local.project_id
  location = local.region
  cluster  = google_container_cluster.primary.name
  
  initial_node_count = 1
  
  autoscaling {
    min_node_count = 1
    max_node_count = 3
  }
  
  node_config {
    machine_type = "e2-standard-4"
    disk_size_gb = 100
    disk_type    = "pd-ssd"
    
    labels = merge(local.labels, {
      node_pool = "system"
    })
    
    workload_metadata_config {
      mode = "GKE_METADATA"
    }
    
    shielded_instance_config {
      enable_secure_boot          = true
      enable_integrity_monitoring = true
    }
  }
}

resource "google_container_node_pool" "compute" {
  name     = "compute-pool"
  project  = local.project_id
  location = local.region
  cluster  = google_container_cluster.primary.name
  
  initial_node_count = 0
  
  autoscaling {
    min_node_count = 0
    max_node_count = 10
  }
  
  node_config {
    machine_type = "n2-highmem-8"
    disk_size_gb = 200
    disk_type    = "pd-ssd"
    
    labels = merge(local.labels, {
      node_pool = "compute"
      workload  = "econometric"
    })
    
    taint {
      key    = "compute"
      value  = "true"
      effect = "NO_SCHEDULE"
    }
    
    guest_accelerator {
      type  = "nvidia-tesla-t4"
      count = 1
    }
  }
}

# Cloud SQL PostgreSQL
resource "google_sql_database_instance" "postgres" {
  name             = "ymip-postgres-${var.environment}"
  project          = local.project_id
  region           = local.region
  database_version = "POSTGRES_15"
  
  settings {
    tier              = var.environment == "production" ? "db-custom-4-16384" : "db-custom-2-8192"
    availability_type = var.environment == "production" ? "REGIONAL" : "ZONAL"
    disk_size         = 100
    disk_type         = "PD_SSD"
    disk_autoresize   = true
    
    backup_configuration {
      enabled                        = true
      start_time                     = "03:00"
      point_in_time_recovery_enabled = true
      retained_backups               = 30
      retention_unit                 = "COUNT"
    }
    
    ip_configuration {
      ipv4_enabled    = false
      private_network = google_compute_network.vpc.id
      require_ssl     = true
    }
    
    database_flags {
      name  = "max_connections"
      value = "200"
    }
    
    database_flags {
      name  = "shared_buffers"
      value = "4096"
    }
    
    insights_config {
      query_insights_enabled  = true
      query_string_length     = 1024
      record_application_tags = true
      record_client_address   = true
    }
  }
  
  deletion_protection = var.environment == "production"
}

# Memorystore Redis
resource "google_redis_instance" "cache" {
  name               = "ymip-redis-${var.environment}"
  project            = local.project_id
  region             = local.region
  memory_size_gb     = var.environment == "production" ? 5 : 2
  redis_version      = "REDIS_6_X"
  display_name       = "YMIP Cache ${title(var.environment)}"
  authorized_network = google_compute_network.vpc.id
  connect_mode       = "PRIVATE_SERVICE_ACCESS"
  
  redis_configs = {
    maxmemory-policy = "allkeys-lru"
  }
  
  labels = local.labels
}
```

### Kubernetes Deployments
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ymip
  labels:
    environment: production
    istio-injection: enabled
    
---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ymip-api
  namespace: ymip
  labels:
    app: ymip-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ymip-api
  template:
    metadata:
      labels:
        app: ymip-api
        version: v1
    spec:
      serviceAccountName: ymip-api
      containers:
      - name: api
        image: gcr.io/ymip-project/ymip-api:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: YMIP_ENV
          value: "production"
        - name: GOOGLE_CLOUD_PROJECT
          value: "ymip-project"
        - name: DB_HOST
          value: "/cloudsql/ymip-project:us-central1:ymip-postgres"
        envFrom:
        - secretRef:
            name: ymip-secrets
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 10
      - name: cloud-sql-proxy
        image: gcr.io/cloud-sql-connectors/cloud-sql-proxy:2.0
        args:
          - "--private-ip"
          - "--structured-logs"
          - "ymip-project:us-central1:ymip-postgres"
        securityContext:
          runAsNonRoot: true
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
            
---
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ymip-api-hpa
  namespace: ymip
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ymip-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
```

## 🔐 Security Configuration

### Workload Identity
```yaml
# k8s/workload-identity.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: ymip-api
  namespace: ymip
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>
    
---
apiVersion: iam.cnrm.cloud.google.com/v1beta1
kind: IAMServiceAccount
metadata:
  name: ymip-api
  namespace: ymip
spec:
  displayName: YMIP API Service Account
  
---
apiVersion: iam.cnrm.cloud.google.com/v1beta1
kind: IAMPolicy
metadata:
  name: ymip-api-policy
  namespace: ymip
spec:
  resourceRef:
    apiVersion: iam.cnrm.cloud.google.com/v1beta1
    kind: IAMServiceAccount
    name: ymip-api
  bindings:
  - role: roles/cloudsql.client
    members:
    - serviceAccount:<EMAIL>
  - role: roles/storage.objectViewer
    members:
    - serviceAccount:<EMAIL>
  - role: roles/bigquery.user
    members:
    - serviceAccount:<EMAIL>
```

### Secret Management
```yaml
# k8s/external-secrets.yaml
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: gcpsm-secret-store
  namespace: ymip
spec:
  provider:
    gcpsm:
      projectID: ymip-project
      auth:
        workloadIdentity:
          clusterLocation: us-central1
          clusterName: ymip-gke-production
          serviceAccountRef:
            name: ymip-api
            
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: ymip-secrets
  namespace: ymip
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: gcpsm-secret-store
    kind: SecretStore
  target:
    name: ymip-secrets
    creationPolicy: Owner
  data:
  - secretKey: db-password
    remoteRef:
      key: ymip-db-password
  - secretKey: redis-password
    remoteRef:
      key: ymip-redis-password
  - secretKey: app-secret-key
    remoteRef:
      key: ymip-app-secret
```

## 📊 Data Analytics Platform

### BigQuery Setup
```sql
-- bigquery/dataset.sql
CREATE SCHEMA IF NOT EXISTS `ymip-project.yemen_market_data`
OPTIONS(
  description="Yemen Market Integration Platform Analytics",
  location="us-central1",
  default_table_expiration_ms=**********, -- 90 days
  labels=[("project", "ymip"), ("environment", "production")]
);

-- Price analysis table
CREATE OR REPLACE TABLE `ymip-project.yemen_market_data.price_analysis`
PARTITION BY DATE(analysis_date)
CLUSTER BY commodity, market_id AS
SELECT
  market_id,
  commodity,
  analysis_date,
  price_yer,
  price_usd,
  exchange_rate,
  conflict_intensity,
  model_predictions,
  created_at
FROM `ymip-project.yemen_market_data.raw_prices`
WHERE analysis_date >= '2020-01-01';

-- Create materialized view for dashboard
CREATE MATERIALIZED VIEW `ymip-project.yemen_market_data.market_summary`
PARTITION BY DATE(date)
CLUSTER BY governorate AS
SELECT
  DATE(timestamp) as date,
  governorate,
  commodity,
  AVG(price_usd) as avg_price_usd,
  STDDEV(price_usd) as price_volatility,
  COUNT(*) as observation_count,
  AVG(conflict_events) as avg_conflict
FROM `ymip-project.yemen_market_data.price_analysis`
GROUP BY date, governorate, commodity;
```

### Dataflow Pipeline
```python
# dataflow/price_processing_pipeline.py
import apache_beam as beam
from apache_beam.options.pipeline_options import PipelineOptions
from apache_beam.io import ReadFromPubSub, WriteToBigQuery
import json

class ProcessPriceData(beam.DoFn):
    def process(self, element):
        """Process incoming price data"""
        data = json.loads(element)
        
        # Add processing timestamp
        data['processing_timestamp'] = beam.utils.timestamp.Timestamp.now()
        
        # Calculate real prices
        if data.get('exchange_rate'):
            data['price_usd'] = data['price_yer'] / data['exchange_rate']
        
        # Add data quality flags
        data['quality_score'] = self.calculate_quality_score(data)
        
        yield data
    
    def calculate_quality_score(self, data):
        score = 1.0
        if not data.get('exchange_rate'):
            score -= 0.3
        if data.get('price_yer', 0) <= 0:
            score -= 0.5
        return max(0, score)

def run_pipeline():
    options = PipelineOptions([
        '--project=ymip-project',
        '--region=us-central1',
        '--runner=DataflowRunner',
        '--temp_location=gs://ymip-dataflow-temp/temp',
        '--staging_location=gs://ymip-dataflow-temp/staging',
        '--job_name=ymip-price-processing',
        '--autoscaling_algorithm=THROUGHPUT_BASED',
        '--max_num_workers=10'
    ])
    
    with beam.Pipeline(options=options) as p:
        (p
         | 'Read from PubSub' >> ReadFromPubSub(
             subscription='projects/ymip-project/subscriptions/price-updates')
         | 'Process Data' >> beam.ParDo(ProcessPriceData())
         | 'Write to BigQuery' >> WriteToBigQuery(
             table='ymip-project:yemen_market_data.price_analysis',
             schema='AUTO_DETECT',
             write_disposition=beam.io.BigQueryDisposition.WRITE_APPEND))

if __name__ == '__main__':
    run_pipeline()
```

## 🤖 ML Platform Integration

### Vertex AI Pipeline
```python
# vertex_ai/training_pipeline.py
from google.cloud import aiplatform
from google.cloud.aiplatform import pipeline_jobs
import kfp
from kfp.v2 import compiler, dsl

@dsl.component(
    base_image='python:3.11',
    packages_to_install=['pandas', 'numpy', 'statsmodels', 'linearmodels']
)
def prepare_panel_data(
    project_id: str,
    dataset_id: str,
    output_path: dsl.Output[dsl.Dataset]
):
    """Prepare panel data for econometric analysis"""
    from google.cloud import bigquery
    import pandas as pd
    
    client = bigquery.Client(project=project_id)
    
    query = f"""
    SELECT *
    FROM `{project_id}.{dataset_id}.price_analysis`
    WHERE DATE(analysis_date) >= DATE_SUB(CURRENT_DATE(), INTERVAL 2 YEAR)
    """
    
    df = client.query(query).to_dataframe()
    df.to_parquet(output_path.path)

@dsl.component(
    base_image='python:3.11',
    packages_to_install=['linearmodels', 'statsmodels']
)
def train_panel_model(
    data_path: dsl.Input[dsl.Dataset],
    model_path: dsl.Output[dsl.Model]
):
    """Train panel data model"""
    import pandas as pd
    from linearmodels import PanelOLS
    import joblib
    
    # Load data
    df = pd.read_parquet(data_path.path)
    df = df.set_index(['market_id', 'date'])
    
    # Prepare model
    model = PanelOLS(
        dependent=df['price_usd'],
        exog=df[['conflict_intensity', 'exchange_rate', 'global_price']],
        entity_effects=True,
        time_effects=True
    )
    
    # Fit model
    results = model.fit(cov_type='clustered', cluster_entity=True)
    
    # Save model
    joblib.dump(results, model_path.path)

@dsl.pipeline(
    name='ymip-econometric-pipeline',
    description='Yemen Market Integration econometric analysis pipeline'
)
def econometric_pipeline(
    project_id: str = 'ymip-project',
    dataset_id: str = 'yemen_market_data'
):
    # Data preparation
    data_task = prepare_panel_data(
        project_id=project_id,
        dataset_id=dataset_id
    )
    
    # Model training
    model_task = train_panel_model(
        data_path=data_task.outputs['output_path']
    )
    
    # Model evaluation
    evaluate_task = evaluate_model(
        model_path=model_task.outputs['model_path'],
        data_path=data_task.outputs['output_path']
    )

# Compile and submit pipeline
compiler.Compiler().compile(
    pipeline_func=econometric_pipeline,
    package_path='econometric_pipeline.json'
)

aiplatform.init(project='ymip-project', location='us-central1')

job = aiplatform.PipelineJob(
    display_name='ymip-econometric-analysis',
    template_path='econometric_pipeline.json',
    pipeline_root='gs://ymip-pipeline-root',
    enable_caching=True
)

job.submit()
```

## 📈 Monitoring and Observability

### Cloud Monitoring Dashboard
```yaml
# monitoring/dashboard.yaml
displayName: YMIP Production Dashboard
mosaicLayout:
  columns: 12
  tiles:
  - width: 6
    height: 4
    widget:
      title: API Request Rate
      xyChart:
        dataSets:
        - timeSeriesQuery:
            timeSeriesFilter:
              filter: |
                resource.type="k8s_container"
                resource.labels.namespace_name="ymip"
                metric.type="kubernetes.io/container/request_count"
        timeshiftDuration: 0s
        yAxis:
          scale: LINEAR
  - xPos: 6
    width: 6
    height: 4
    widget:
      title: Model Execution Time
      xyChart:
        dataSets:
        - timeSeriesQuery:
            timeSeriesFilter:
              filter: |
                resource.type="k8s_container"
                metric.type="custom.googleapis.com/ymip/model_execution_time"
              aggregation:
                alignmentPeriod: 60s
                perSeriesAligner: ALIGN_PERCENTILE_95
  - yPos: 4
    width: 6
    height: 4
    widget:
      title: Database Connections
      xyChart:
        dataSets:
        - timeSeriesQuery:
            timeSeriesFilter:
              filter: |
                resource.type="cloudsql_database"
                metric.type="cloudsql.googleapis.com/database/postgresql/num_backends"
```

### Structured Logging
```python
# logging_config.py
import google.cloud.logging
from google.cloud.logging_v2.handlers import CloudLoggingHandler
import logging

def setup_cloud_logging():
    """Configure structured logging for GCP"""
    client = google.cloud.logging.Client()
    handler = CloudLoggingHandler(client)
    
    # Configure root logger
    logging.getLogger().setLevel(logging.INFO)
    logging.getLogger().addHandler(handler)
    
    # Add custom fields
    class StructuredLogger(logging.LoggerAdapter):
        def process(self, msg, kwargs):
            # Add trace information
            trace_header = get_trace_header()
            if trace_header:
                kwargs["extra"] = {
                    "trace": f"projects/{PROJECT_ID}/traces/{trace_header}",
                    "spanId": get_span_id()
                }
            return msg, kwargs
    
    return StructuredLogger(logging.getLogger(), {})

logger = setup_cloud_logging()
```

## 💰 Cost Optimization

### Committed Use Discounts
```hcl
# terraform/committed_use.tf
resource "google_compute_commitment" "cpu_commitment" {
  name        = "ymip-cpu-commitment"
  project     = local.project_id
  region      = local.region
  plan        = "TWELVE_MONTH"
  
  resources {
    type   = "VCPU"
    amount = 100
  }
  
  resources {
    type   = "MEMORY"
    amount = 400  # GB
  }
}
```

### Preemptible Instances
```yaml
# k8s/batch-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: ymip-batch-analysis
spec:
  template:
    spec:
      nodeSelector:
        cloud.google.com/gke-preemptible: "true"
      tolerations:
      - key: cloud.google.com/gke-preemptible
        operator: Equal
        value: "true"
        effect: NoSchedule
      containers:
      - name: analysis
        image: gcr.io/ymip-project/batch-processor:latest
        resources:
          requests:
            cpu: 4
            memory: 16Gi
```

## 🔄 CI/CD Pipeline

### Cloud Build Configuration
```yaml
# cloudbuild.yaml
steps:
  # Build application image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/ymip-api:$COMMIT_SHA', '.']
    
  # Run tests
  - name: 'gcr.io/$PROJECT_ID/ymip-api:$COMMIT_SHA'
    entrypoint: 'pytest'
    args: ['--cov=src', '--cov-report=xml']
    
  # Push to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/ymip-api:$COMMIT_SHA']
    
  # Deploy to GKE
  - name: 'gcr.io/cloud-builders/gke-deploy'
    args:
    - run
    - --filename=k8s/
    - --image=gcr.io/$PROJECT_ID/ymip-api:$COMMIT_SHA
    - --cluster=ymip-gke-production
    - --location=us-central1
    - --namespace=ymip

options:
  machineType: 'E2_HIGHCPU_8'
  substitutionOption: 'ALLOW_LOOSE'

substitutions:
  _ENVIRONMENT: production

timeout: 1200s
```

## 🛠️ Maintenance

### Backup Strategy
```bash
#!/bin/bash
# backup-databases.sh

# Export Cloud SQL backup
gcloud sql backups create \
  --instance=ymip-postgres-production \
  --description="Scheduled backup $(date +%Y%m%d-%H%M%S)"

# Export BigQuery datasets
bq extract \
  --destination_format=AVRO \
  --compression=SNAPPY \
  yemen_market_data.price_analysis \
  gs://ymip-backups/bigquery/$(date +%Y%m%d)/price_analysis-*.avro

# Backup Firestore
gcloud firestore export gs://ymip-backups/firestore/$(date +%Y%m%d)
```

### Disaster Recovery
```hcl
# terraform/disaster_recovery.tf
# Multi-region bucket for backups
resource "google_storage_bucket" "backup" {
  name          = "ymip-backups-${var.environment}"
  location      = "US"  # Multi-region
  force_destroy = false
  
  versioning {
    enabled = true
  }
  
  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type          = "SetStorageClass"
      storage_class = "NEARLINE"
    }
  }
  
  lifecycle_rule {
    condition {
      age = 90
    }
    action {
      type          = "SetStorageClass"
      storage_class = "COLDLINE"
    }
  }
}
```

## 📚 Additional Resources

- [GCP Architecture Framework](https://cloud.google.com/architecture/framework)
- [GKE Best Practices](https://cloud.google.com/kubernetes-engine/docs/best-practices)
- [BigQuery Best Practices](https://cloud.google.com/bigquery/docs/best-practices-performance-overview)
- [Vertex AI Documentation](https://cloud.google.com/vertex-ai/docs)