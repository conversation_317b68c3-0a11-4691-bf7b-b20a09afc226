# Azure Deployment Guide

## 🎯 Target Audience

- **Cloud Architects**: Designing Azure infrastructure
- **DevOps Engineers**: Implementing Azure deployments
- **Data Engineers**: Managing Azure data services

## 📋 Overview

This guide covers deploying the Yemen Market Integration Platform on Microsoft Azure, leveraging Azure's analytics services and global infrastructure for econometric research workloads.

## 🏗️ Azure Architecture

```mermaid
graph TB
    subgraph "Internet"
        Users[Research Users]
        API[API Clients]
    end
    
    subgraph "Azure Cloud"
        subgraph "Front Door"
            FD[Azure Front Door]
            WAF[Web Application Firewall]
        end
        
        subgraph "Resource Group: ymip-prod-rg"
            subgraph "Virtual Network - 10.0.0.0/16"
                subgraph "Public Subnet - ********/24"
                    AGW[Application Gateway]
                    NATGW[NAT Gateway]
                end
                
                subgraph "App Subnet - ********/24"
                    AKS[AKS Cluster]
                    ACI[Container Instances]
                end
                
                subgraph "Data Subnet - ********/24"
                    PSQL[PostgreSQL Flexible Server]
                    Redis[Azure Cache for Redis]
                end
                
                subgraph "Compute Subnet - ********/24"
                    Batch[Azure Batch]
                    ML[Azure ML Compute]
                end
            end
            
            subgraph "Storage"
                SA[Storage Account]
                DL[Data Lake Gen2]
                Synapse[Synapse Analytics]
            end
        end
        
        subgraph "Monitoring"
            Monitor[Azure Monitor]
            AppIns[Application Insights]
            LA[Log Analytics]
        end
    end
    
    Users --> FD
    API --> FD
    FD --> AGW
    AGW --> AKS
    AKS --> PSQL
    AKS --> Redis
    AKS --> SA
    Batch --> DL
```

## 🚀 Infrastructure as Code

### Bicep Templates
```bicep
// main.bicep
@description('The environment name')
@allowed(['dev', 'staging', 'production'])
param environment string

@description('The Azure region')
param location string = resourceGroup().location

@description('The project name')
param projectName string = 'ymip'

var resourcePrefix = '${projectName}-${environment}'
var vnetName = '${resourcePrefix}-vnet'
var aksName = '${resourcePrefix}-aks'

// Virtual Network
resource vnet 'Microsoft.Network/virtualNetworks@2023-05-01' = {
  name: vnetName
  location: location
  properties: {
    addressSpace: {
      addressPrefixes: [
        '10.0.0.0/16'
      ]
    }
    subnets: [
      {
        name: 'public'
        properties: {
          addressPrefix: '********/24'
          networkSecurityGroup: {
            id: publicNsg.id
          }
        }
      }
      {
        name: 'app'
        properties: {
          addressPrefix: '********/24'
          networkSecurityGroup: {
            id: appNsg.id
          }
          serviceEndpoints: [
            {
              service: 'Microsoft.Storage'
            }
            {
              service: 'Microsoft.Sql'
            }
          ]
        }
      }
      {
        name: 'data'
        properties: {
          addressPrefix: '********/24'
          networkSecurityGroup: {
            id: dataNsg.id
          }
          privateEndpointNetworkPolicies: 'Disabled'
        }
      }
      {
        name: 'compute'
        properties: {
          addressPrefix: '********/24'
          networkSecurityGroup: {
            id: computeNsg.id
          }
        }
      }
    ]
  }
}

// Network Security Groups
resource publicNsg 'Microsoft.Network/networkSecurityGroups@2023-05-01' = {
  name: '${resourcePrefix}-public-nsg'
  location: location
  properties: {
    securityRules: [
      {
        name: 'AllowHTTPS'
        properties: {
          priority: 100
          direction: 'Inbound'
          access: 'Allow'
          protocol: 'Tcp'
          sourcePortRange: '*'
          destinationPortRange: '443'
          sourceAddressPrefix: 'Internet'
          destinationAddressPrefix: '*'
        }
      }
    ]
  }
}

// Azure Kubernetes Service
resource aks 'Microsoft.ContainerService/managedClusters@2023-08-01' = {
  name: aksName
  location: location
  identity: {
    type: 'SystemAssigned'
  }
  properties: {
    dnsPrefix: aksName
    enableRBAC: true
    nodeResourceGroup: '${resourcePrefix}-aks-nodes-rg'
    networkProfile: {
      networkPlugin: 'azure'
      networkPolicy: 'calico'
      serviceCidr: '**********/16'
      dnsServiceIP: '***********'
    }
    agentPoolProfiles: [
      {
        name: 'system'
        count: 2
        vmSize: 'Standard_D2s_v3'
        mode: 'System'
        enableAutoScaling: true
        minCount: 2
        maxCount: 4
        vnetSubnetID: vnet.properties.subnets[1].id
      }
      {
        name: 'compute'
        count: 1
        vmSize: 'Standard_D8s_v3'
        mode: 'User'
        enableAutoScaling: true
        minCount: 1
        maxCount: 10
        vnetSubnetID: vnet.properties.subnets[1].id
        nodeLabels: {
          workload: 'compute'
        }
        nodeTaints: [
          'compute=true:NoSchedule'
        ]
      }
    ]
    addonProfiles: {
      azureKeyvaultSecretsProvider: {
        enabled: true
      }
      omsagent: {
        enabled: true
        config: {
          logAnalyticsWorkspaceResourceID: logAnalytics.id
        }
      }
    }
  }
}

// PostgreSQL Flexible Server
resource postgresql 'Microsoft.DBforPostgreSQL/flexibleServers@2023-03-01-preview' = {
  name: '${resourcePrefix}-psql'
  location: location
  sku: {
    name: 'Standard_D4s_v3'
    tier: 'GeneralPurpose'
  }
  properties: {
    version: '15'
    administratorLogin: 'ymip_admin'
    administratorLoginPassword: keyVault.getSecret('db-admin-password')
    storage: {
      storageSizeGB: 128
      autoGrow: 'Enabled'
    }
    backup: {
      backupRetentionDays: 30
      geoRedundantBackup: 'Enabled'
    }
    network: {
      delegatedSubnetResourceId: vnet.properties.subnets[2].id
      privateDnsZoneArmResourceId: privateDnsZone.id
    }
    highAvailability: {
      mode: environment == 'production' ? 'ZoneRedundant' : 'Disabled'
    }
  }
}

// Storage Account for Data Lake
resource storageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' = {
  name: toLower('${projectName}${environment}data')
  location: location
  sku: {
    name: 'Standard_RAGRS'
  }
  kind: 'StorageV2'
  properties: {
    isHnsEnabled: true // Enable Data Lake Gen2
    minimumTlsVersion: 'TLS1_2'
    allowBlobPublicAccess: false
    networkAcls: {
      defaultAction: 'Deny'
      virtualNetworkRules: [
        {
          id: '${vnet.id}/subnets/app'
          action: 'Allow'
        }
        {
          id: '${vnet.id}/subnets/compute'
          action: 'Allow'
        }
      ]
    }
    encryption: {
      services: {
        blob: {
          enabled: true
        }
        file: {
          enabled: true
        }
      }
      keySource: 'Microsoft.Storage'
    }
  }
}

// Azure Cache for Redis
resource redis 'Microsoft.Cache/redis@2023-08-01' = {
  name: '${resourcePrefix}-redis'
  location: location
  properties: {
    sku: {
      name: environment == 'production' ? 'Premium' : 'Standard'
      family: environment == 'production' ? 'P' : 'C'
      capacity: environment == 'production' ? 1 : 2
    }
    enableNonSslPort: false
    minimumTlsVersion: '1.2'
    redisConfiguration: {
      'maxmemory-policy': 'allkeys-lru'
    }
    subnetId: vnet.properties.subnets[2].id
  }
}
```

### Kubernetes Manifests
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ymip
  labels:
    environment: production
    
---
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ymip-api
  namespace: ymip
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ymip-api
  template:
    metadata:
      labels:
        app: ymip-api
        aadpodidbinding: ymip-pod-identity
    spec:
      containers:
      - name: api
        image: ymipacr.azurecr.io/ymip-api:latest
        ports:
        - containerPort: 8000
        env:
        - name: YMIP_ENV
          value: "production"
        - name: APPLICATIONINSIGHTS_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: ymip-secrets
              key: appinsights-connection-string
        envFrom:
        - secretRef:
            name: ymip-secrets
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 10
      nodeSelector:
        agentpool: system
        
---
# k8s/worker-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ymip-worker
  namespace: ymip
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ymip-worker
  template:
    metadata:
      labels:
        app: ymip-worker
    spec:
      containers:
      - name: worker
        image: ymipacr.azurecr.io/ymip-api:latest
        command: ["celery", "-A", "yemen_market.tasks", "worker", "-Q", "compute"]
        resources:
          requests:
            cpu: 2000m
            memory: 8Gi
          limits:
            cpu: 4000m
            memory: 16Gi
      tolerations:
      - key: "compute"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      nodeSelector:
        agentpool: compute
        workload: compute
```

## 🔐 Security Configuration

### Key Vault Integration
```bicep
// keyvault.bicep
resource keyVault 'Microsoft.KeyVault/vaults@2023-07-01' = {
  name: '${resourcePrefix}-kv'
  location: location
  properties: {
    sku: {
      family: 'A'
      name: 'standard'
    }
    tenantId: subscription().tenantId
    enabledForDeployment: true
    enabledForTemplateDeployment: true
    enableRbacAuthorization: true
    enableSoftDelete: true
    softDeleteRetentionInDays: 90
    enablePurgeProtection: true
    networkAcls: {
      defaultAction: 'Deny'
      virtualNetworkRules: [
        {
          id: '${vnet.id}/subnets/app'
        }
      ]
    }
  }
}

// Secrets
resource dbPasswordSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  parent: keyVault
  name: 'db-admin-password'
  properties: {
    value: generatePassword()
  }
}

resource appSecretKey 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = {
  parent: keyVault
  name: 'app-secret-key'
  properties: {
    value: generateSecretKey()
  }
}
```

### Managed Identity
```yaml
# k8s/aad-pod-identity.yaml
apiVersion: aadpodidentity.k8s.io/v1
kind: AzureIdentity
metadata:
  name: ymip-pod-identity
  namespace: ymip
spec:
  type: 0
  resourceID: /subscriptions/<sub-id>/resourcegroups/<rg>/providers/Microsoft.ManagedIdentity/userAssignedIdentities/ymip-identity
  clientID: <client-id>
  
---
apiVersion: aadpodidentity.k8s.io/v1
kind: AzureIdentityBinding
metadata:
  name: ymip-pod-identity-binding
  namespace: ymip
spec:
  azureIdentity: ymip-pod-identity
  selector: ymip-pod-identity
```

## 📊 Data Analytics Platform

### Synapse Analytics
```bicep
// synapse.bicep
resource synapseWorkspace 'Microsoft.Synapse/workspaces@2021-06-01' = {
  name: '${resourcePrefix}-synapse'
  location: location
  identity: {
    type: 'SystemAssigned'
  }
  properties: {
    defaultDataLakeStorage: {
      accountUrl: 'https://${storageAccount.name}.dfs.core.windows.net'
      filesystem: 'ymip-data'
    }
    encryption: {
      cmk: {
        kekIdentity: {
          userAssignedIdentity: managedIdentity.id
        }
      }
    }
    managedVirtualNetwork: 'default'
    managedResourceGroupName: '${resourcePrefix}-synapse-managed-rg'
  }
}

// Spark Pool for Heavy Computation
resource sparkPool 'Microsoft.Synapse/workspaces/bigDataPools@2021-06-01' = {
  parent: synapseWorkspace
  name: 'ymipspark'
  location: location
  properties: {
    nodeCount: 3
    nodeSizeFamily: 'MemoryOptimized'
    nodeSize: 'Medium'
    autoScale: {
      enabled: true
      minNodeCount: 3
      maxNodeCount: 10
    }
    autoPause: {
      enabled: true
      delayInMinutes: 15
    }
    sparkVersion: '3.3'
    defaultSparkLogFolder: 'logs/'
    libraryRequirements: {
      content: '''
        pandas==1.5.3
        numpy==1.24.3
        statsmodels==0.14.0
        scikit-learn==1.3.0
      '''
      filename: 'requirements.txt'
    }
  }
}
```

### Azure ML Integration
```python
# azure_ml_config.py
from azureml.core import Workspace, Experiment, Environment
from azureml.core.compute import ComputeTarget, AmlCompute
from azureml.core.runconfig import RunConfiguration

# Connect to workspace
ws = Workspace(
    subscription_id=os.environ['AZURE_SUBSCRIPTION_ID'],
    resource_group='ymip-prod-rg',
    workspace_name='ymip-ml-workspace'
)

# Create compute cluster for model training
compute_config = AmlCompute.provisioning_configuration(
    vm_size='Standard_NC6',  # GPU-enabled for deep learning
    min_nodes=0,
    max_nodes=4,
    idle_seconds_before_scaledown=1800
)

compute_target = ComputeTarget.create(
    ws, 
    'ymip-gpu-cluster', 
    compute_config
)

# Environment for econometric models
env = Environment(name='ymip-econometrics')
env.python.conda_dependencies.add_pip_package('linearmodels')
env.python.conda_dependencies.add_pip_package('arch')
env.python.conda_dependencies.add_pip_package('statsmodels')
env.register(workspace=ws)
```

## 📈 Monitoring and Logging

### Application Insights
```csharp
// Startup configuration for .NET apps
public void ConfigureServices(IServiceCollection services)
{
    services.AddApplicationInsightsTelemetry(Configuration["ApplicationInsights:ConnectionString"]);
    services.AddApplicationInsightsKubernetesEnricher();
    
    services.ConfigureTelemetryModule<DependencyTrackingTelemetryModule>((module, o) =>
    {
        module.EnableSqlCommandTextInstrumentation = true;
    });
}
```

### Log Analytics Queries
```kusto
// Key performance metrics
let timeRange = 1h;
let binSize = 5m;
requests
| where timestamp > ago(timeRange)
| summarize 
    RequestCount = count(), 
    AvgDuration = avg(duration),
    P95Duration = percentile(duration, 95),
    FailureRate = countif(success == false) * 100.0 / count()
    by bin(timestamp, binSize), name
| render timechart

// Model execution times
customEvents
| where name == "ModelExecutionCompleted"
| extend 
    ModelType = tostring(customDimensions.ModelType),
    Duration = todouble(customDimensions.Duration)
| summarize 
    AvgDuration = avg(Duration),
    MaxDuration = max(Duration),
    Count = count()
    by ModelType
| order by AvgDuration desc
```

## 💰 Cost Optimization

### Azure Advisor Recommendations
```bicep
// cost-optimization.bicep
// Auto-shutdown for non-production environments
resource autoShutdown 'Microsoft.DevTestLab/schedules@2018-09-15' = if (environment != 'production') {
  name: 'shutdown-computevm-${vmName}'
  location: location
  properties: {
    status: 'Enabled'
    taskType: 'ComputeVmShutdownTask'
    dailyRecurrence: {
      time: '1900'
    }
    timeZoneId: 'UTC'
    targetResourceId: vm.id
    notificationSettings: {
      status: 'Disabled'
    }
  }
}

// Reserved instances for predictable workloads
resource reservation 'Microsoft.Capacity/reservationOrders@2022-03-01' = {
  name: 'ymip-reserved-instances'
  properties: {
    displayName: 'YMIP Production Reserved Instances'
    reservedResourceType: 'VirtualMachines'
    billingScopeId: subscription().id
    term: 'P3Y' // 3-year term for maximum savings
    quantity: 5
    appliedScopes: [
      subscription().id
    ]
    reservedResourceProperties: {
      instanceFlexibility: 'On'
    }
  }
}
```

### Cost Management Alerts
```bicep
resource budget 'Microsoft.Consumption/budgets@2021-10-01' = {
  name: '${resourcePrefix}-monthly-budget'
  properties: {
    category: 'Cost'
    amount: 5000
    timeGrain: 'Monthly'
    timePeriod: {
      startDate: '2024-01-01T00:00:00Z'
    }
    notifications: {
      Actual_GreaterThan_80_Percent: {
        enabled: true
        operator: 'GreaterThan'
        threshold: 80
        contactEmails: [
          '<EMAIL>'
        ]
      }
    }
  }
}
```

## 🔄 CI/CD Pipeline

### Azure DevOps Pipeline
```yaml
# azure-pipelines.yml
trigger:
  branches:
    include:
      - main
      - release/*

variables:
  dockerRegistryServiceConnection: 'ymip-acr'
  imageRepository: 'ymip-api'
  containerRegistry: 'ymipacr.azurecr.io'
  dockerfilePath: '$(Build.SourcesDirectory)/Dockerfile'
  tag: '$(Build.BuildId)'
  
pool:
  vmImage: 'ubuntu-latest'

stages:
- stage: Build
  displayName: Build and Test
  jobs:
  - job: Build
    displayName: Build
    steps:
    - task: Docker@2
      displayName: Build and push image
      inputs:
        command: buildAndPush
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: |
          $(tag)
          latest
          
    - task: PublishTestResults@2
      displayName: Publish test results
      inputs:
        testResultsFormat: 'JUnit'
        testResultsFiles: '**/test-*.xml'
        
- stage: Deploy
  displayName: Deploy to AKS
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: Deploy
    displayName: Deploy to Production
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: KubernetesManifest@0
            displayName: Deploy to Kubernetes
            inputs:
              action: deploy
              kubernetesServiceConnection: 'ymip-aks'
              namespace: 'ymip'
              manifests: |
                $(Pipeline.Workspace)/manifests/*
              containers: |
                $(containerRegistry)/$(imageRepository):$(tag)
```

## 🛠️ Maintenance

### Backup Strategy
```powershell
# backup-databases.ps1
# PostgreSQL backup to blob storage
$date = Get-Date -Format "yyyyMMdd-HHmmss"
$backupName = "ymip-psql-backup-$date.sql"

az postgres flexible-server backup create `
  --resource-group ymip-prod-rg `
  --name ymip-prod-psql `
  --backup-name $backupName

# Copy to long-term storage
az storage blob copy start `
  --source-uri "https://backups.blob.core.windows.net/postgres/$backupName" `
  --destination-container archive `
  --destination-blob "postgres/$backupName" `
  --account-name ymiparchive
```

### Update Management
```bicep
// update-management.bicep
resource updateManagement 'Microsoft.Automation/automationAccounts/softwareUpdateConfigurations@2019-06-01' = {
  name: '${automationAccount.name}/ymip-weekly-updates'
  properties: {
    updateConfiguration: {
      operatingSystem: 'Linux'
      duration: 'PT2H'
      linux: {
        includedPackageClassifications: 'Critical,Security'
        rebootSetting: 'IfRequired'
      }
      azureVirtualMachines: [
        for i in range(0, vmCount): resourceId('Microsoft.Compute/virtualMachines', 'ymip-vm-${i}')
      ]
    }
    scheduleInfo: {
      frequency: 'Week'
      startTime: '2024-01-07T02:00:00+00:00'
      timeZone: 'UTC'
      interval: 1
      advancedSchedule: {
        weekDays: ['Sunday']
      }
    }
  }
}
```

## 📚 Additional Resources

- [Azure Well-Architected Framework](https://docs.microsoft.com/en-us/azure/architecture/framework/)
- [AKS Best Practices](https://docs.microsoft.com/en-us/azure/aks/best-practices)
- [Azure Cost Management](https://azure.microsoft.com/en-us/services/cost-management/)