# AWS Deployment Guide

## 🎯 Target Audience

- **Cloud Architects**: Designing AWS infrastructure
- **DevOps Engineers**: Implementing AWS deployments
- **Cost Optimizers**: Managing AWS resource costs

## 📋 Overview

This guide covers deploying the Yemen Market Integration Platform on Amazon Web Services (AWS), optimized for econometric workloads with considerations for data residency, compliance, and cost optimization.

## 🏗️ AWS Architecture

```mermaid
graph TB
    subgraph "Internet"
        Users[Research Users]
        API[API Clients]
    end
    
    subgraph "AWS Cloud"
        subgraph "Edge Services"
            CF[CloudFront CDN]
            WAF[AWS WAF]
            R53[Route 53]
        end
        
        subgraph "VPC - 10.0.0.0/16"
            subgraph "Public Subnet - ********/24"
                ALB[Application Load Balancer]
                NAT[NAT Gateway]
            end
            
            subgraph "Private Subnet - ********/24"
                subgraph "ECS Cluster"
                    API_Service[API Service]
                    Worker_Service[Worker Service]
                end
                
                subgraph "Data Layer"
                    RDS[(RDS PostgreSQL)]
                    ElastiCache[(ElastiCache Redis)]
                end
            end
            
            subgraph "Compute Subnet - ********/24"
                Batch[AWS Batch]
                SageMaker[SageMaker]
            end
        end
        
        subgraph "Storage"
            S3[S3 Buckets]
            EFS[EFS]
        end
        
        subgraph "Monitoring"
            CW[CloudWatch]
            XRay[X-Ray]
        end
    end
    
    Users --> CF
    API --> CF
    CF --> ALB
    ALB --> API_Service
    API_Service --> RDS
    API_Service --> ElastiCache
    API_Service --> S3
    Worker_Service --> Batch
    Batch --> S3
```

## 🚀 Infrastructure as Code

### Terraform Configuration
```hcl
# terraform/main.tf
terraform {
  required_version = ">= 1.0"
  
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  
  backend "s3" {
    bucket = "ymip-terraform-state"
    key    = "production/terraform.tfstate"
    region = "us-east-1"
    encrypt = true
    dynamodb_table = "ymip-terraform-locks"
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = "Yemen Market Integration"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# VPC Configuration
module "vpc" {
  source = "terraform-aws-modules/vpc/aws"
  version = "5.0.0"
  
  name = "ymip-${var.environment}"
  cidr = "10.0.0.0/16"
  
  azs             = ["${var.aws_region}a", "${var.aws_region}b", "${var.aws_region}c"]
  public_subnets  = ["********/24", "********/24", "********/24"]
  private_subnets = ["*********/24", "*********/24", "*********/24"]
  database_subnets = ["*********/24", "*********/24", "*********/24"]
  
  enable_nat_gateway = true
  single_nat_gateway = var.environment != "production"
  enable_dns_hostnames = true
  enable_dns_support = true
  
  enable_flow_log = true
  flow_log_destination_type = "cloud-watch-logs"
  
  tags = {
    "kubernetes.io/cluster/${local.cluster_name}" = "shared"
  }
}

# ECS Cluster
resource "aws_ecs_cluster" "main" {
  name = "ymip-${var.environment}"
  
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
  
  configuration {
    execute_command_configuration {
      logging = "OVERRIDE"
      
      log_configuration {
        cloud_watch_log_group_name = aws_cloudwatch_log_group.ecs.name
      }
    }
  }
}

# RDS PostgreSQL
module "rds" {
  source = "terraform-aws-modules/rds/aws"
  version = "6.0.0"
  
  identifier = "ymip-${var.environment}"
  
  engine               = "postgres"
  engine_version       = "15.4"
  family              = "postgres15"
  major_engine_version = "15"
  instance_class       = var.db_instance_class
  
  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_encrypted     = true
  
  db_name  = "yemen_market"
  username = "ymip_admin"
  password = random_password.db_password.result
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = module.vpc.database_subnet_group_name
  
  backup_retention_period = 30
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  enabled_cloudwatch_logs_exports = ["postgresql"]
  
  performance_insights_enabled = true
  performance_insights_retention_period = 7
  
  parameters = [
    {
      name  = "shared_preload_libraries"
      value = "pg_stat_statements"
    },
    {
      name  = "log_statement"
      value = "all"
    }
  ]
}
```

### ECS Task Definitions
```hcl
# terraform/ecs_tasks.tf
resource "aws_ecs_task_definition" "api" {
  family                   = "ymip-api-${var.environment}"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = var.api_cpu
  memory                   = var.api_memory
  execution_role_arn       = aws_iam_role.ecs_execution.arn
  task_role_arn           = aws_iam_role.ecs_task.arn
  
  container_definitions = jsonencode([
    {
      name  = "api"
      image = "${aws_ecr_repository.ymip.repository_url}:${var.app_version}"
      
      portMappings = [
        {
          containerPort = 8000
          protocol      = "tcp"
        }
      ]
      
      environment = [
        {
          name  = "YMIP_ENV"
          value = var.environment
        },
        {
          name  = "YMIP_DB_HOST"
          value = module.rds.db_instance_address
        }
      ]
      
      secrets = [
        {
          name      = "YMIP_DB_PASSWORD"
          valueFrom = aws_secretsmanager_secret.db_password.arn
        },
        {
          name      = "YMIP_SECRET_KEY"
          valueFrom = aws_secretsmanager_secret.app_secret.arn
        }
      ]
      
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.api.name
          "awslogs-region"        = var.aws_region
          "awslogs-stream-prefix" = "api"
        }
      }
      
      healthCheck = {
        command     = ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
        interval    = 30
        timeout     = 5
        retries     = 3
        startPeriod = 60
      }
    }
  ])
}

# Worker Task for Compute-Intensive Jobs
resource "aws_ecs_task_definition" "worker" {
  family                   = "ymip-worker-${var.environment}"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  cpu                      = "4096"  # 4 vCPU
  memory                   = "16384" # 16 GB
  execution_role_arn       = aws_iam_role.ecs_execution.arn
  task_role_arn           = aws_iam_role.ecs_task.arn
  
  container_definitions = jsonencode([
    {
      name  = "worker"
      image = "${aws_ecr_repository.ymip.repository_url}:${var.app_version}"
      
      command = ["celery", "-A", "yemen_market.tasks", "worker", "-Q", "compute"]
      
      environment = concat(
        local.common_environment,
        [
          {
            name  = "YMIP_WORKER_TYPE"
            value = "compute"
          }
        ]
      )
      
      secrets = local.common_secrets
      
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.worker.name
          "awslogs-region"        = var.aws_region
          "awslogs-stream-prefix" = "worker"
        }
      }
    }
  ])
}
```

## 🔐 Security Configuration

### IAM Roles and Policies
```hcl
# terraform/iam.tf
resource "aws_iam_role" "ecs_task" {
  name = "ymip-ecs-task-${var.environment}"
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_policy" "ecs_task" {
  name = "ymip-ecs-task-${var.environment}"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        Resource = [
          "${aws_s3_bucket.data.arn}/*",
          "${aws_s3_bucket.results.arn}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue"
        ]
        Resource = [
          aws_secretsmanager_secret.db_password.arn,
          aws_secretsmanager_secret.app_secret.arn
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt"
        ]
        Resource = [
          aws_kms_key.ymip.arn
        ]
      }
    ]
  })
}

# Security Groups
resource "aws_security_group" "alb" {
  name_prefix = "ymip-alb-${var.environment}-"
  vpc_id      = module.vpc.vpc_id
  
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}
```

## 📊 Data Storage

### S3 Buckets
```hcl
# terraform/s3.tf
resource "aws_s3_bucket" "data" {
  bucket = "ymip-data-${var.environment}-${data.aws_caller_identity.current.account_id}"
  
  tags = {
    Purpose = "Raw and processed data storage"
  }
}

resource "aws_s3_bucket_versioning" "data" {
  bucket = aws_s3_bucket.data.id
  
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "data" {
  bucket = aws_s3_bucket.data.id
  
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm     = "aws:kms"
      kms_master_key_id = aws_kms_key.ymip.arn
    }
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "data" {
  bucket = aws_s3_bucket.data.id
  
  rule {
    id     = "archive-old-data"
    status = "Enabled"
    
    transition {
      days          = 90
      storage_class = "STANDARD_IA"
    }
    
    transition {
      days          = 180
      storage_class = "GLACIER"
    }
  }
}

# S3 VPC Endpoint for private access
resource "aws_vpc_endpoint" "s3" {
  vpc_id            = module.vpc.vpc_id
  service_name      = "com.amazonaws.${var.aws_region}.s3"
  vpc_endpoint_type = "Gateway"
  route_table_ids   = module.vpc.private_route_table_ids
}
```

## 🚀 Auto-Scaling Configuration

### ECS Service Auto-Scaling
```hcl
# terraform/autoscaling.tf
resource "aws_appautoscaling_target" "api" {
  max_capacity       = var.api_max_count
  min_capacity       = var.api_min_count
  resource_id        = "service/${aws_ecs_cluster.main.name}/${aws_ecs_service.api.name}"
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace  = "ecs"
}

resource "aws_appautoscaling_policy" "api_cpu" {
  name               = "ymip-api-cpu-${var.environment}"
  policy_type        = "TargetTrackingScaling"
  resource_id        = aws_appautoscaling_target.api.resource_id
  scalable_dimension = aws_appautoscaling_target.api.scalable_dimension
  service_namespace  = aws_appautoscaling_target.api.service_namespace
  
  target_tracking_scaling_policy_configuration {
    predefined_metric_specification {
      predefined_metric_type = "ECSServiceAverageCPUUtilization"
    }
    target_value = 70.0
  }
}

# Spot instances for batch processing
resource "aws_batch_compute_environment" "spot" {
  compute_environment_name = "ymip-spot-${var.environment}"
  type                     = "MANAGED"
  state                    = "ENABLED"
  
  compute_resources {
    type      = "EC2_SPOT"
    min_vcpus = 0
    max_vcpus = 256
    desired_vcpus = 4
    
    instance_type = [
      "m5.large",
      "m5.xlarge",
      "m5.2xlarge",
      "c5.large",
      "c5.xlarge"
    ]
    
    spot_iam_fleet_role = aws_iam_role.batch_spot_fleet.arn
    instance_role       = aws_iam_instance_profile.batch.arn
    
    security_group_ids = [aws_security_group.batch.id]
    subnets            = module.vpc.private_subnets
    
    bid_percentage = 80
  }
}
```

## 📊 Monitoring and Logging

### CloudWatch Configuration
```hcl
# terraform/monitoring.tf
resource "aws_cloudwatch_dashboard" "main" {
  dashboard_name = "ymip-${var.environment}"
  
  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/ECS", "CPUUtilization", "ServiceName", aws_ecs_service.api.name],
            [".", "MemoryUtilization", ".", "."]
          ]
          period = 300
          stat   = "Average"
          region = var.aws_region
          title  = "API Service Utilization"
        }
      },
      {
        type   = "metric"
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/RDS", "DatabaseConnections", "DBInstanceIdentifier", module.rds.db_instance_id],
            [".", "CPUUtilization", ".", "."],
            [".", "FreeableMemory", ".", ".", { stat = "Average" }]
          ]
          period = 300
          stat   = "Average"
          region = var.aws_region
          title  = "Database Metrics"
        }
      }
    ]
  })
}

# Alarms
resource "aws_cloudwatch_metric_alarm" "api_cpu_high" {
  alarm_name          = "ymip-api-cpu-high-${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors API CPU utilization"
  alarm_actions       = [aws_sns_topic.alerts.arn]
  
  dimensions = {
    ServiceName = aws_ecs_service.api.name
    ClusterName = aws_ecs_cluster.main.name
  }
}
```

## 💰 Cost Optimization

### Cost Management
```hcl
# terraform/cost_optimization.tf
# Use Spot instances for workers
resource "aws_ecs_service" "worker" {
  name            = "ymip-worker-${var.environment}"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.worker.arn
  desired_count   = var.worker_count
  
  capacity_provider_strategy {
    capacity_provider = "FARGATE_SPOT"
    weight            = 100
    base              = 0
  }
  
  network_configuration {
    subnets          = module.vpc.private_subnets
    security_groups  = [aws_security_group.ecs_tasks.id]
    assign_public_ip = false
  }
}

# S3 Intelligent Tiering
resource "aws_s3_bucket_intelligent_tiering_configuration" "data" {
  bucket = aws_s3_bucket.data.id
  name   = "entire-bucket"
  
  tiering {
    access_tier = "ARCHIVE_ACCESS"
    days        = 90
  }
  
  tiering {
    access_tier = "DEEP_ARCHIVE_ACCESS"
    days        = 180
  }
}

# Reserved Instances recommendations
resource "aws_budgets_budget" "monthly" {
  name              = "ymip-monthly-${var.environment}"
  budget_type       = "COST"
  limit_amount      = var.monthly_budget
  limit_unit        = "USD"
  time_unit         = "MONTHLY"
  
  notification {
    comparison_operator        = "GREATER_THAN"
    threshold                  = 80
    threshold_type            = "PERCENTAGE"
    notification_type          = "FORECASTED"
    subscriber_email_addresses = var.budget_alert_emails
  }
}
```

## 🔄 CI/CD Pipeline

### GitHub Actions Deployment
```yaml
# .github/workflows/deploy-aws.yml
name: Deploy to AWS

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY: ymip

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'staging' }}
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      
      - name: Build and push image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
      
      - name: Deploy to ECS
        run: |
          aws ecs update-service \
            --cluster ymip-${{ github.event.inputs.environment || 'staging' }} \
            --service ymip-api-${{ github.event.inputs.environment || 'staging' }} \
            --force-new-deployment
```

## 🛠️ Maintenance

### Backup Strategy
```bash
# RDS automated backups configured in Terraform
# Additional backup to S3
aws rds create-db-snapshot \
  --db-instance-identifier ymip-production \
  --db-snapshot-identifier ymip-manual-$(date +%Y%m%d)

# S3 cross-region replication for disaster recovery
aws s3api put-bucket-replication \
  --bucket ymip-data-production \
  --replication-configuration file://replication.json
```

### Monitoring Scripts
```bash
#!/bin/bash
# scripts/check_health.sh
API_ENDPOINT="https://api.yemen-market.org/health"
SLACK_WEBHOOK=$SLACK_ALERT_WEBHOOK

response=$(curl -s -o /dev/null -w "%{http_code}" $API_ENDPOINT)
if [ $response -ne 200 ]; then
    curl -X POST $SLACK_WEBHOOK \
      -H 'Content-type: application/json' \
      -d '{"text":"⚠️ YMIP API health check failed! Response: '$response'"}'
fi
```

## 📚 Additional Resources

- [AWS Well-Architected Framework](https://aws.amazon.com/architecture/well-architected/)
- [ECS Best Practices Guide](https://docs.aws.amazon.com/AmazonECS/latest/bestpracticesguide/)
- [AWS Cost Optimization](https://aws.amazon.com/aws-cost-management/aws-cost-optimization/)