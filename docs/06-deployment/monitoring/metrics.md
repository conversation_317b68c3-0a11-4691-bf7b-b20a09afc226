# Performance Metrics Configuration

## 🎯 Target Audience

- **DevOps Engineers**: Setting up metrics collection
- **Data Engineers**: Monitoring pipeline performance
- **System Administrators**: Tracking resource utilization

## 📋 Overview

This guide covers implementing comprehensive performance metrics for the Yemen Market Integration Platform, focusing on econometric model performance, data pipeline efficiency, and system resource utilization.

## 🏗️ Metrics Architecture

```mermaid
graph TB
    subgraph "Application Metrics"
        API[API Metrics]
        Models[Model Metrics]
        Pipeline[Pipeline Metrics]
        Business[Business Metrics]
    end
    
    subgraph "System Metrics"
        Node[Node Metrics]
        Container[Container Metrics]
        Database[Database Metrics]
        Network[Network Metrics]
    end
    
    subgraph "Collection"
        Prometheus[Prometheus]
        Pushgateway[Pushgateway]
        ServiceMonitor[Service Monitors]
    end
    
    subgraph "Storage"
        TSDB[Prometheus TSDB]
        LongTerm[Thanos/Cortex]
    end
    
    subgraph "Visualization"
        Grafana[Grafana]
        AlertManager[Alert Manager]
        Reports[Automated Reports]
    end
    
    API --> Prometheus
    Models --> Pushgateway
    Pipeline --> Prometheus
    Business --> Prometheus
    
    Node --> Prometheus
    Container --> ServiceMonitor
    Database --> Prometheus
    Network --> Prometheus
    
    Pushgateway --> Prometheus
    ServiceMonitor --> Prometheus
    
    Prometheus --> TSDB
    TSDB --> LongTerm
    
    TSDB --> Grafana
    TSDB --> AlertManager
    LongTerm --> Reports
```

## 📊 Application Metrics

### Python Metrics Implementation
```python
# src/yemen_market/utils/metrics.py
from prometheus_client import (
    Counter, Histogram, Gauge, Summary, Info,
    CollectorRegistry, generate_latest, push_to_gateway
)
from functools import wraps
import time
import os

# Create registry
registry = CollectorRegistry()

# API Metrics
http_requests_total = Counter(
    'ymip_http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status'],
    registry=registry
)

http_request_duration = Histogram(
    'ymip_http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint'],
    buckets=(0.01, 0.05, 0.1, 0.5, 1.0, 2.5, 5.0, 10.0),
    registry=registry
)

# Model Metrics
model_runs_total = Counter(
    'ymip_model_runs_total',
    'Total model runs',
    ['model_type', 'status'],
    registry=registry
)

model_duration = Histogram(
    'ymip_model_duration_seconds',
    'Model execution duration',
    ['model_type', 'tier'],
    buckets=(1, 5, 10, 30, 60, 120, 300, 600, 1800, 3600),
    registry=registry
)

model_performance = Gauge(
    'ymip_model_performance',
    'Model performance metrics',
    ['model_type', 'metric_name'],
    registry=registry
)

# Data Pipeline Metrics
data_processed_total = Counter(
    'ymip_data_processed_total',
    'Total data records processed',
    ['pipeline_stage', 'data_type'],
    registry=registry
)

pipeline_lag = Gauge(
    'ymip_pipeline_lag_seconds',
    'Data pipeline processing lag',
    ['pipeline_name'],
    registry=registry
)

data_quality_score = Gauge(
    'ymip_data_quality_score',
    'Data quality score (0-1)',
    ['dataset', 'quality_dimension'],
    registry=registry
)

# Business Metrics
market_coverage = Gauge(
    'ymip_market_coverage_ratio',
    'Market coverage ratio',
    ['governorate', 'commodity'],
    registry=registry
)

price_volatility = Gauge(
    'ymip_price_volatility',
    'Price volatility index',
    ['market', 'commodity'],
    registry=registry
)

# System Info
system_info = Info(
    'ymip_system_info',
    'System information',
    registry=registry
)

# Decorators for metric collection
def track_request_metrics(func):
    """Decorator to track HTTP request metrics"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            status = getattr(result, 'status_code', 200)
            http_requests_total.labels(
                method=kwargs.get('method', 'GET'),
                endpoint=kwargs.get('endpoint', '/'),
                status=status
            ).inc()
            
            return result
        finally:
            duration = time.time() - start_time
            http_request_duration.labels(
                method=kwargs.get('method', 'GET'),
                endpoint=kwargs.get('endpoint', '/')
            ).observe(duration)
    
    return wrapper

def track_model_metrics(model_type, tier=None):
    """Decorator to track model execution metrics"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                model_runs_total.labels(
                    model_type=model_type,
                    status='success'
                ).inc()
                
                # Extract and record performance metrics
                if hasattr(result, 'metrics'):
                    for metric_name, value in result.metrics.items():
                        model_performance.labels(
                            model_type=model_type,
                            metric_name=metric_name
                        ).set(value)
                
                return result
                
            except Exception as e:
                model_runs_total.labels(
                    model_type=model_type,
                    status='failed'
                ).inc()
                raise
                
            finally:
                duration = time.time() - start_time
                model_duration.labels(
                    model_type=model_type,
                    tier=tier or 'unknown'
                ).observe(duration)
        
        return wrapper
    return decorator

class MetricsCollector:
    """Centralized metrics collection"""
    
    def __init__(self, pushgateway_url=None):
        self.pushgateway_url = pushgateway_url or os.getenv(
            'PROMETHEUS_PUSHGATEWAY_URL',
            'localhost:9091'
        )
        self.job_name = os.getenv('YMIP_JOB_NAME', 'ymip_batch')
        
    def push_metrics(self, grouping_key=None):
        """Push metrics to Pushgateway"""
        try:
            push_to_gateway(
                self.pushgateway_url,
                job=self.job_name,
                registry=registry,
                grouping_key=grouping_key or {}
            )
        except Exception as e:
            logger.error(f"Failed to push metrics: {e}")
    
    def record_pipeline_metrics(self, pipeline_name, records_processed, 
                              processing_time, errors=0):
        """Record data pipeline metrics"""
        data_processed_total.labels(
            pipeline_stage=pipeline_name,
            data_type='batch'
        ).inc(records_processed)
        
        if errors > 0:
            data_processed_total.labels(
                pipeline_stage=pipeline_name,
                data_type='errors'
            ).inc(errors)
        
        # Calculate and record lag
        current_time = time.time()
        lag = current_time - (current_time - processing_time)
        pipeline_lag.labels(pipeline_name=pipeline_name).set(lag)
    
    def record_data_quality(self, dataset, quality_metrics):
        """Record data quality metrics"""
        for dimension, score in quality_metrics.items():
            data_quality_score.labels(
                dataset=dataset,
                quality_dimension=dimension
            ).set(score)
    
    def record_business_metrics(self, market_data):
        """Record business-specific metrics"""
        for market in market_data:
            market_coverage.labels(
                governorate=market['governorate'],
                commodity=market['commodity']
            ).set(market['coverage_ratio'])
            
            price_volatility.labels(
                market=market['market_id'],
                commodity=market['commodity']
            ).set(market['volatility_index'])

# Initialize system info
system_info.info({
    'version': os.getenv('APP_VERSION', 'unknown'),
    'environment': os.getenv('YMIP_ENV', 'development'),
    'python_version': sys.version.split()[0]
})
```

### FastAPI Metrics Integration
```python
# src/yemen_market/api/middleware/metrics.py
from fastapi import FastAPI, Request, Response
from prometheus_client import make_asgi_app
import time

def setup_metrics(app: FastAPI):
    """Setup Prometheus metrics for FastAPI"""
    
    # Add metrics endpoint
    metrics_app = make_asgi_app(registry=registry)
    app.mount("/metrics", metrics_app)
    
    @app.middleware("http")
    async def track_requests(request: Request, call_next):
        """Middleware to track HTTP metrics"""
        start_time = time.time()
        
        # Extract path template for consistent labeling
        path = request.url.path
        method = request.method
        
        try:
            response = await call_next(request)
            
            # Record metrics
            http_requests_total.labels(
                method=method,
                endpoint=path,
                status=response.status_code
            ).inc()
            
            return response
            
        except Exception as e:
            http_requests_total.labels(
                method=method,
                endpoint=path,
                status=500
            ).inc()
            raise
            
        finally:
            duration = time.time() - start_time
            http_request_duration.labels(
                method=method,
                endpoint=path
            ).observe(duration)
```

## 🔧 Infrastructure Metrics

### Prometheus Configuration
```yaml
# prometheus/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'ymip-production'
    region: 'us-east-1'

# Alerting configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093

# Rule files
rule_files:
  - "rules/*.yml"

# Scrape configurations
scrape_configs:
  # Application metrics
  - job_name: 'ymip-api'
    kubernetes_sd_configs:
      - role: pod
        namespaces:
          names:
            - ymip
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - source_labels: [__meta_kubernetes_pod_label_app]
        action: replace
        target_label: app
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
        
  # Node metrics
  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
      - role: node
    relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
        
  # Database metrics
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        regex: '([^:]+)(?::\d+)?'
        replacement: '$1'
        
  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
        
  # Pushgateway for batch jobs
  - job_name: 'pushgateway'
    honor_labels: true
    static_configs:
      - targets: ['pushgateway:9091']

# Remote storage for long-term retention
remote_write:
  - url: http://thanos-receive:19291/api/v1/receive
    write_relabel_configs:
      - source_labels: [__name__]
        regex: 'ymip_.*'
        action: keep
```

### Kubernetes Service Monitors
```yaml
# k8s/service-monitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: ymip-api-monitor
  namespace: ymip
  labels:
    app: ymip-api
spec:
  selector:
    matchLabels:
      app: ymip-api
  endpoints:
  - port: metrics
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
    relabelings:
    - sourceLabels: [__meta_kubernetes_pod_name]
      targetLabel: pod
    - sourceLabels: [__meta_kubernetes_pod_node_name]
      targetLabel: node
      
---
apiVersion: monitoring.coreos.com/v1
kind: PodMonitor
metadata:
  name: ymip-workers-monitor
  namespace: ymip
spec:
  selector:
    matchLabels:
      app: ymip-worker
  podMetricsEndpoints:
  - port: metrics
    interval: 60s
    path: /metrics
```

## 📈 Grafana Dashboards

### Main Dashboard Configuration
```json
{
  "dashboard": {
    "id": null,
    "uid": "ymip-overview",
    "title": "YMIP System Overview",
    "tags": ["ymip", "production"],
    "timezone": "UTC",
    "schemaVersion": 30,
    "version": 0,
    "refresh": "30s",
    "panels": [
      {
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
        "id": 1,
        "title": "API Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "sum(rate(ymip_http_requests_total[5m])) by (status)",
            "legendFormat": "Status {{status}}",
            "refId": "A"
          }
        ],
        "yaxes": [
          {
            "format": "reqps",
            "label": "Requests/sec"
          }
        ]
      },
      {
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
        "id": 2,
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, sum(rate(ymip_http_request_duration_seconds_bucket[5m])) by (le, endpoint))",
            "legendFormat": "P95 {{endpoint}}",
            "refId": "A"
          },
          {
            "expr": "histogram_quantile(0.99, sum(rate(ymip_http_request_duration_seconds_bucket[5m])) by (le))",
            "legendFormat": "P99 Overall",
            "refId": "B"
          }
        ]
      },
      {
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8},
        "id": 3,
        "title": "Model Execution Performance",
        "type": "table",
        "targets": [
          {
            "expr": "avg by (model_type, tier) (rate(ymip_model_duration_seconds_sum[5m]) / rate(ymip_model_duration_seconds_count[5m]))",
            "format": "table",
            "instant": true
          }
        ],
        "styles": [
          {
            "pattern": "Value",
            "type": "number",
            "unit": "s"
          }
        ]
      },
      {
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16},
        "id": 4,
        "title": "Data Pipeline Throughput",
        "type": "graph",
        "targets": [
          {
            "expr": "sum(rate(ymip_data_processed_total[5m])) by (pipeline_stage)",
            "legendFormat": "{{pipeline_stage}}"
          }
        ]
      },
      {
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16},
        "id": 5,
        "title": "Data Quality Scores",
        "type": "heatmap",
        "targets": [
          {
            "expr": "ymip_data_quality_score",
            "format": "heatmap"
          }
        ]
      }
    ]
  }
}
```

### Model Performance Dashboard
```json
{
  "dashboard": {
    "title": "YMIP Model Performance",
    "panels": [
      {
        "title": "Model Execution Times Distribution",
        "type": "histogram",
        "targets": [
          {
            "expr": "ymip_model_duration_seconds_bucket",
            "legendFormat": "{{model_type}} - {{tier}}"
          }
        ]
      },
      {
        "title": "Model Success Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(rate(ymip_model_runs_total{status='success'}[5m])) / sum(rate(ymip_model_runs_total[5m])) * 100"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent",
            "thresholds": {
              "mode": "absolute",
              "steps": [
                {"color": "red", "value": null},
                {"color": "yellow", "value": 95},
                {"color": "green", "value": 99}
              ]
            }
          }
        }
      },
      {
        "title": "Model Performance Metrics",
        "type": "table",
        "targets": [
          {
            "expr": "ymip_model_performance",
            "format": "table",
            "instant": true
          }
        ]
      }
    ]
  }
}
```

## 🚨 Alert Rules

### Prometheus Alert Configuration
```yaml
# alerts/performance_alerts.yml
groups:
  - name: ymip_performance
    interval: 30s
    rules:
      # API Performance
      - alert: APIHighLatency
        expr: |
          histogram_quantile(0.95,
            sum(rate(ymip_http_request_duration_seconds_bucket[5m])) by (le)
          ) > 1
        for: 5m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "API latency is high"
          description: "95th percentile latency is {{ $value }}s"
          
      - alert: APIHighErrorRate
        expr: |
          sum(rate(ymip_http_requests_total{status=~"5.."}[5m]))
          /
          sum(rate(ymip_http_requests_total[5m])) > 0.05
        for: 5m
        labels:
          severity: critical
          component: api
        annotations:
          summary: "API error rate exceeds 5%"
          description: "Current error rate: {{ $value | humanizePercentage }}"
          
      # Model Performance
      - alert: ModelExecutionSlow
        expr: |
          avg by (model_type) (
            rate(ymip_model_duration_seconds_sum[5m])
            /
            rate(ymip_model_duration_seconds_count[5m])
          ) > 1800
        for: 10m
        labels:
          severity: warning
          component: models
        annotations:
          summary: "Model {{ $labels.model_type }} is running slowly"
          description: "Average execution time: {{ $value | humanizeDuration }}"
          
      - alert: ModelFailureRate
        expr: |
          sum by (model_type) (rate(ymip_model_runs_total{status="failed"}[5m]))
          /
          sum by (model_type) (rate(ymip_model_runs_total[5m])) > 0.1
        for: 5m
        labels:
          severity: critical
          component: models
        annotations:
          summary: "High failure rate for model {{ $labels.model_type }}"
          description: "Failure rate: {{ $value | humanizePercentage }}"
          
      # Data Pipeline
      - alert: DataPipelineLag
        expr: ymip_pipeline_lag_seconds > 3600
        for: 15m
        labels:
          severity: warning
          component: pipeline
        annotations:
          summary: "Data pipeline {{ $labels.pipeline_name }} is lagging"
          description: "Current lag: {{ $value | humanizeDuration }}"
          
      - alert: DataQualityDegraded
        expr: |
          avg by (dataset) (ymip_data_quality_score) < 0.8
        for: 30m
        labels:
          severity: warning
          component: data
        annotations:
          summary: "Data quality degraded for {{ $labels.dataset }}"
          description: "Quality score: {{ $value }}"
          
      # Resource Usage
      - alert: HighMemoryUsage
        expr: |
          (container_memory_working_set_bytes{namespace="ymip"}
          / container_spec_memory_limit_bytes{namespace="ymip"}) > 0.9
        for: 5m
        labels:
          severity: warning
          component: infrastructure
        annotations:
          summary: "High memory usage in {{ $labels.pod }}"
          description: "Memory usage: {{ $value | humanizePercentage }}"
```

## 📊 Custom Metrics

### Business Metrics Collection
```python
# src/yemen_market/metrics/business_metrics.py
from prometheus_client import Gauge
import numpy as np

class BusinessMetricsCollector:
    """Collect and expose business metrics"""
    
    def __init__(self):
        self.market_integration = Gauge(
            'ymip_market_integration_score',
            'Market integration score (0-1)',
            ['market_pair', 'commodity']
        )
        
        self.price_convergence = Gauge(
            'ymip_price_convergence_rate',
            'Price convergence rate',
            ['region', 'commodity']
        )
        
        self.conflict_impact = Gauge(
            'ymip_conflict_impact_score',
            'Conflict impact on prices',
            ['governorate', 'severity']
        )
        
    def update_integration_scores(self, integration_results):
        """Update market integration metrics"""
        for result in integration_results:
            self.market_integration.labels(
                market_pair=f"{result['market1']}-{result['market2']}",
                commodity=result['commodity']
            ).set(result['integration_score'])
    
    def update_convergence_rates(self, convergence_data):
        """Update price convergence metrics"""
        for region, commodities in convergence_data.items():
            for commodity, rate in commodities.items():
                self.price_convergence.labels(
                    region=region,
                    commodity=commodity
                ).set(rate)
    
    def update_conflict_impact(self, impact_analysis):
        """Update conflict impact metrics"""
        for gov, impacts in impact_analysis.items():
            for severity, score in impacts.items():
                self.conflict_impact.labels(
                    governorate=gov,
                    severity=severity
                ).set(score)
```

### Performance Profiling Metrics
```python
# src/yemen_market/metrics/profiling.py
import cProfile
import pstats
from io import StringIO
from contextlib import contextmanager

class PerformanceProfiler:
    """Profile and expose performance metrics"""
    
    def __init__(self):
        self.function_calls = Counter(
            'ymip_function_calls_total',
            'Total function calls',
            ['function_name']
        )
        
        self.function_time = Histogram(
            'ymip_function_time_seconds',
            'Function execution time',
            ['function_name'],
            buckets=(0.001, 0.01, 0.1, 0.5, 1.0, 5.0, 10.0)
        )
    
    @contextmanager
    def profile(self, name):
        """Context manager for profiling code blocks"""
        profiler = cProfile.Profile()
        profiler.enable()
        
        start_time = time.time()
        try:
            yield profiler
        finally:
            profiler.disable()
            duration = time.time() - start_time
            
            # Record metrics
            self.function_time.labels(function_name=name).observe(duration)
            self.function_calls.labels(function_name=name).inc()
            
            # Extract detailed stats
            s = StringIO()
            ps = pstats.Stats(profiler, stream=s).sort_stats('cumulative')
            ps.print_stats(10)  # Top 10 functions
            
            logger.debug(f"Profile for {name}:\n{s.getvalue()}")
```

## 🔧 Monitoring Best Practices

### Metric Naming Conventions
```python
# Good metric names
ymip_http_requests_total          # Counter
ymip_model_duration_seconds       # Histogram
ymip_active_connections           # Gauge
ymip_data_quality_score          # Gauge (0-1)

# Include units in metric names
_seconds, _bytes, _total, _ratio, _percent
```

### Label Best Practices
```python
# Good labels
model_type="panel_regression"
status="success"
environment="production"

# Avoid high cardinality
# Bad: user_id="12345" (millions of values)
# Good: user_type="researcher" (few values)
```

### Resource Optimization
```yaml
# Optimize Prometheus storage
storage.tsdb.retention.time: 15d
storage.tsdb.retention.size: 100GB
storage.tsdb.wal-compression: true

# Optimize scrape configs
scrape_interval: 30s  # Don't over-scrape
scrape_timeout: 10s
sample_limit: 10000   # Prevent cardinality explosions
```

## 📚 Additional Resources

- [Prometheus Best Practices](https://prometheus.io/docs/practices/)
- [Grafana Dashboard Best Practices](https://grafana.com/docs/grafana/latest/best-practices/dashboard-management/)
- [SRE Monitoring Guide](https://sre.google/sre-book/monitoring-distributed-systems/)
- [OpenTelemetry Metrics](https://opentelemetry.io/docs/concepts/metrics/)