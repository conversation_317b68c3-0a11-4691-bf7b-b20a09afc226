# Alert Configuration Guide

## 🎯 Target Audience

- **DevOps Engineers**: Setting up alerting systems
- **On-call Engineers**: Responding to alerts
- **Team Leads**: Managing alert policies

## 📋 Overview

This guide covers implementing comprehensive alerting for the Yemen Market Integration Platform, including alert definitions, routing, escalation policies, and incident response procedures.

## 🏗️ Alerting Architecture

```mermaid
graph TB
    subgraph "Metrics Sources"
        Prometheus[Prometheus]
        CloudWatch[CloudWatch]
        AppInsights[Application Insights]
        CustomMetrics[Custom Metrics]
    end
    
    subgraph "Alert Processing"
        AlertManager[Alert Manager]
        Rules[Alert Rules]
        Routing[Routing Logic]
        Grouping[Alert Grouping]
        Silencing[Silencing Rules]
    end
    
    subgraph "Notification Channels"
        Slack[Slack]
        PagerDuty[PagerDuty]
        Email[Email]
        Teams[MS Teams]
        Webhook[Webhooks]
    end
    
    subgraph "Incident Management"
        Oncall[On-call Rotation]
        Escalation[Escalation Policy]
        Runbooks[Runbooks]
        PostMortem[Post-mortems]
    end
    
    Prometheus --> AlertManager
    CloudWatch --> AlertManager
    AppInsights --> AlertManager
    CustomMetrics --> AlertManager
    
    AlertManager --> Rules
    Rules --> Routing
    Routing --> Grouping
    Grouping --> Silencing
    
    Silencing --> Slack
    Silencing --> PagerDuty
    Silencing --> Email
    Silencing --> Teams
    Silencing --> Webhook
    
    PagerDuty --> Oncall
    Oncall --> Escalation
    Escalation --> Runbooks
    Runbooks --> PostMortem
```

## 🚨 Alert Configuration

### Alert Manager Configuration
```yaml
# alertmanager/config.yml
global:
  resolve_timeout: 5m
  slack_api_url: '${SLACK_WEBHOOK_URL}'
  pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'

# Alert routing tree
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 5m
  repeat_interval: 12h
  receiver: 'default'
  
  routes:
    # Critical alerts - immediate page
    - match:
        severity: critical
      receiver: 'pagerduty-critical'
      group_wait: 0s
      repeat_interval: 5m
      continue: true
      
    # Model alerts - to data science team
    - match_re:
        component: (models|pipeline)
      receiver: 'data-team'
      group_wait: 30s
      group_interval: 10m
      
    # Infrastructure alerts
    - match:
        component: infrastructure
      receiver: 'ops-team'
      
    # Business hours only
    - match:
        severity: warning
      receiver: 'slack-warnings'
      active_time_intervals:
        - business_hours

# Receivers configuration
receivers:
  - name: 'default'
    slack_configs:
      - channel: '#ymip-alerts'
        title: 'YMIP Alert'
        text: '{{ template "slack.default.text" . }}'
        send_resolved: true
        
  - name: 'pagerduty-critical'
    pagerduty_configs:
      - service_key: '${PAGERDUTY_SERVICE_KEY}'
        description: '{{ template "pagerduty.default.description" . }}'
        details:
          firing: '{{ template "pagerduty.default.instances" .Alerts.Firing }}'
          resolved: '{{ template "pagerduty.default.instances" .Alerts.Resolved }}'
          num_firing: '{{ .Alerts.Firing | len }}'
          num_resolved: '{{ .Alerts.Resolved | len }}'
          
  - name: 'data-team'
    slack_configs:
      - channel: '#data-science-alerts'
        title: 'Model/Pipeline Alert'
        text: '{{ range .Alerts }}{{ .Annotations.description }}{{ end }}'
        actions:
          - type: button
            text: 'View Dashboard'
            url: 'https://grafana.ymip.org/d/model-performance'
          - type: button
            text: 'Runbook'
            url: '{{ .Annotations.runbook_url }}'
            
  - name: 'ops-team'
    email_configs:
      - to: '<EMAIL>'
        headers:
          Subject: 'YMIP Infrastructure Alert: {{ .GroupLabels.alertname }}'
        html: '{{ template "email.default.html" . }}'
        
  - name: 'slack-warnings'
    slack_configs:
      - channel: '#ymip-warnings'
        send_resolved: false
        title: 'Warning: {{ .GroupLabels.alertname }}'
        text: '{{ .CommonAnnotations.summary }}'

# Inhibition rules
inhibit_rules:
  # Inhibit warnings when critical alerts are firing
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'cluster', 'service']
    
  # Inhibit node alerts when cluster is down
  - source_match:
      alertname: 'ClusterDown'
    target_match_re:
      alertname: 'Node.*'

# Time intervals
time_intervals:
  - name: business_hours
    time_intervals:
      - times:
          - start_time: '09:00'
            end_time: '18:00'
        weekdays: ['monday:friday']
        location: 'UTC'
```

## 📊 Alert Rules

### Application Alerts
```yaml
# alerts/application_alerts.yml
groups:
  - name: ymip_application
    interval: 30s
    rules:
      # API Availability
      - alert: APIDown
        expr: up{job="ymip-api"} == 0
        for: 2m
        labels:
          severity: critical
          component: api
          service: ymip
        annotations:
          summary: "YMIP API is down"
          description: "API endpoint {{ $labels.instance }} has been down for more than 2 minutes"
          runbook_url: "https://runbooks.ymip.org/api-down"
          dashboard_url: "https://grafana.ymip.org/d/api-health"
          
      # API Error Rate
      - alert: APIHighErrorRate
        expr: |
          (
            sum(rate(ymip_http_requests_total{status=~"5.."}[5m]))
            /
            sum(rate(ymip_http_requests_total[5m]))
          ) > 0.05
        for: 5m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "High API error rate"
          description: "Error rate is {{ $value | humanizePercentage }} (threshold: 5%)"
          
      # API Response Time
      - alert: APISlowResponse
        expr: |
          histogram_quantile(0.95,
            sum(rate(ymip_http_request_duration_seconds_bucket[5m])) by (le, endpoint)
          ) > 2
        for: 10m
        labels:
          severity: warning
          component: api
        annotations:
          summary: "Slow API responses for {{ $labels.endpoint }}"
          description: "95th percentile response time is {{ $value }}s (threshold: 2s)"
          
      # Database Connection Pool
      - alert: DatabaseConnectionPoolExhausted
        expr: |
          ymip_database_connections_active / ymip_database_connections_max > 0.9
        for: 5m
        labels:
          severity: critical
          component: database
        annotations:
          summary: "Database connection pool nearly exhausted"
          description: "{{ $value | humanizePercentage }} of connections in use"
          runbook_url: "https://runbooks.ymip.org/db-connections"
```

### Model Performance Alerts
```yaml
# alerts/model_alerts.yml
groups:
  - name: ymip_models
    interval: 60s
    rules:
      # Model Execution Failures
      - alert: ModelHighFailureRate
        expr: |
          sum by (model_type) (
            rate(ymip_model_runs_total{status="failed"}[15m])
          ) /
          sum by (model_type) (
            rate(ymip_model_runs_total[15m])
          ) > 0.1
        for: 10m
        labels:
          severity: critical
          component: models
          team: data-science
        annotations:
          summary: "High failure rate for {{ $labels.model_type }} model"
          description: "Failure rate: {{ $value | humanizePercentage }}"
          runbook_url: "https://runbooks.ymip.org/model-failures"
          
      # Model Performance Degradation
      - alert: ModelPerformanceDegraded
        expr: |
          ymip_model_performance{metric_name="r_squared"} < 0.7
        for: 30m
        labels:
          severity: warning
          component: models
          team: data-science
        annotations:
          summary: "Model performance degraded for {{ $labels.model_type }}"
          description: "R-squared: {{ $value }} (threshold: 0.7)"
          action: "Review model inputs and retrain if necessary"
          
      # Long Running Models
      - alert: ModelExecutionStuck
        expr: |
          time() - ymip_model_execution_start_time > 7200
        for: 5m
        labels:
          severity: warning
          component: models
        annotations:
          summary: "Model execution running for over 2 hours"
          description: "Model {{ $labels.model_type }} on {{ $labels.instance }} has been running for {{ $value | humanizeDuration }}"
          
      # Model Queue Backlog
      - alert: ModelQueueBacklog
        expr: |
          ymip_model_queue_size > 100
        for: 15m
        labels:
          severity: warning
          component: models
        annotations:
          summary: "Large model queue backlog"
          description: "{{ $value }} models waiting in queue"
```

### Data Pipeline Alerts
```yaml
# alerts/pipeline_alerts.yml
groups:
  - name: ymip_pipeline
    interval: 60s
    rules:
      # Data Freshness
      - alert: StaleData
        expr: |
          (time() - ymip_last_data_update_timestamp) > 86400
        for: 30m
        labels:
          severity: warning
          component: pipeline
          team: data-engineering
        annotations:
          summary: "Data for {{ $labels.dataset }} is stale"
          description: "Last update was {{ $value | humanizeDuration }} ago"
          runbook_url: "https://runbooks.ymip.org/stale-data"
          
      # Data Quality
      - alert: DataQualityDegraded
        expr: |
          ymip_data_quality_score < 0.8
        for: 1h
        labels:
          severity: warning
          component: pipeline
        annotations:
          summary: "Data quality degraded for {{ $labels.dataset }}"
          description: "Quality score: {{ $value }} on dimension {{ $labels.quality_dimension }}"
          
      # Pipeline Failures
      - alert: PipelineJobFailed
        expr: |
          increase(ymip_pipeline_job_failures_total[1h]) > 3
        labels:
          severity: critical
          component: pipeline
        annotations:
          summary: "Pipeline {{ $labels.pipeline_name }} failing repeatedly"
          description: "{{ $value }} failures in the last hour"
          
      # Data Volume Anomaly
      - alert: DataVolumeAnomaly
        expr: |
          abs(
            ymip_data_processed_total - 
            avg_over_time(ymip_data_processed_total[7d])
          ) / 
          avg_over_time(ymip_data_processed_total[7d]) > 0.5
        for: 1h
        labels:
          severity: warning
          component: pipeline
        annotations:
          summary: "Unusual data volume for {{ $labels.pipeline_stage }}"
          description: "Current volume differs by {{ $value | humanizePercentage }} from 7-day average"
```

### Infrastructure Alerts
```yaml
# alerts/infrastructure_alerts.yml
groups:
  - name: ymip_infrastructure
    interval: 30s
    rules:
      # Resource Usage
      - alert: HighCPUUsage
        expr: |
          (
            100 - (avg by (instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
          ) > 80
        for: 10m
        labels:
          severity: warning
          component: infrastructure
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is {{ $value }}%"
          
      - alert: HighMemoryUsage
        expr: |
          (
            node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes
          ) / node_memory_MemTotal_bytes > 0.9
        for: 10m
        labels:
          severity: warning
          component: infrastructure
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is {{ $value | humanizePercentage }}"
          
      - alert: DiskSpaceLow
        expr: |
          (
            node_filesystem_avail_bytes{fstype!="tmpfs"} / 
            node_filesystem_size_bytes{fstype!="tmpfs"}
          ) < 0.1
        for: 15m
        labels:
          severity: critical
          component: infrastructure
        annotations:
          summary: "Low disk space on {{ $labels.instance }}"
          description: "Only {{ $value | humanizePercentage }} disk space available on {{ $labels.mountpoint }}"
          runbook_url: "https://runbooks.ymip.org/disk-space"
          
      # Database Specific
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 2m
        labels:
          severity: critical
          component: database
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL instance {{ $labels.instance }} is not responding"
          
      - alert: PostgreSQLSlowQueries
        expr: |
          rate(pg_stat_activity_max_tx_duration[5m]) > 60
        for: 10m
        labels:
          severity: warning
          component: database
        annotations:
          summary: "Slow PostgreSQL queries detected"
          description: "Queries taking longer than {{ $value }}s"
```

## 📱 Notification Templates

### Slack Template
```go
{{ define "slack.default.text" }}
{{ range .Alerts }}
*Alert:* {{ .Annotations.summary }}
*Severity:* {{ .Labels.severity }}
*Description:* {{ .Annotations.description }}
*Details:*
{{ range .Labels.SortedPairs }} • *{{ .Name }}:* `{{ .Value }}`
{{ end }}
{{ if .Annotations.runbook_url }}*Runbook:* <{{ .Annotations.runbook_url }}|View Runbook>{{ end }}
{{ if .Annotations.dashboard_url }}*Dashboard:* <{{ .Annotations.dashboard_url }}|View Dashboard>{{ end }}
{{ end }}
{{ end }}
```

### Email Template
```html
{{ define "email.default.html" }}
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; }
        .alert { margin: 20px 0; padding: 15px; border-left: 4px solid; }
        .critical { border-color: #d32f2f; background-color: #ffebee; }
        .warning { border-color: #f57c00; background-color: #fff3e0; }
        .info { border-color: #0288d1; background-color: #e3f2fd; }
        .details { margin: 10px 0; }
        .label { font-weight: bold; }
    </style>
</head>
<body>
    <h2>YMIP Alert Notification</h2>
    {{ range .Alerts }}
    <div class="alert {{ .Labels.severity }}">
        <h3>{{ .Annotations.summary }}</h3>
        <p>{{ .Annotations.description }}</p>
        <div class="details">
            <p><span class="label">Severity:</span> {{ .Labels.severity }}</p>
            <p><span class="label">Component:</span> {{ .Labels.component }}</p>
            <p><span class="label">Time:</span> {{ .StartsAt.Format "2006-01-02 15:04:05 MST" }}</p>
            {{ if .Annotations.runbook_url }}
            <p><span class="label">Runbook:</span> <a href="{{ .Annotations.runbook_url }}">View Runbook</a></p>
            {{ end }}
        </div>
    </div>
    {{ end }}
</body>
</html>
{{ end }}
```

## 📋 Runbooks

### API Down Runbook
```markdown
# Runbook: API Down

## Alert
**Name:** APIDown
**Severity:** Critical
**Component:** API

## Description
The YMIP API is not responding to health checks.

## Impact
- Users cannot access the platform
- Data processing pipelines may fail
- Model executions will be queued

## Diagnosis
1. Check API logs:
   ```bash
   kubectl logs -n ymip -l app=ymip-api --tail=100
   ```

2. Check pod status:
   ```bash
   kubectl get pods -n ymip -l app=ymip-api
   kubectl describe pod <pod-name> -n ymip
   ```

3. Check service endpoints:
   ```bash
   kubectl get endpoints -n ymip ymip-api
   ```

4. Test API directly:
   ```bash
   kubectl exec -it <pod-name> -n ymip -- curl localhost:8000/health
   ```

## Resolution Steps
1. **If pods are crashing:**
   - Check resource limits
   - Review recent deployments
   - Check for OOM kills

2. **If pods are running but not ready:**
   - Check readiness probe configuration
   - Verify database connectivity
   - Check for dependency issues

3. **Emergency rollback:**
   ```bash
   kubectl rollout undo deployment/ymip-api -n ymip
   ```

## Escalation
- After 15 minutes: Page on-call engineer
- After 30 minutes: Escalate to team lead
- After 1 hour: Engage platform team

## Prevention
- Implement canary deployments
- Improve health check coverage
- Add circuit breakers
```

## 🔄 On-Call Procedures

### On-Call Rotation
```yaml
# pagerduty/schedule.yml
schedule:
  name: YMIP On-Call
  time_zone: UTC
  rotation:
    - name: Primary
      users:
        - <EMAIL>
        - <EMAIL>
        - <EMAIL>
      type: weekly
      start: Monday 09:00
      handoff: Monday 09:00
      
    - name: Secondary
      users:
        - <EMAIL>
        - <EMAIL>
      type: weekly
      start: Monday 09:00
      
  escalation_policy:
    - level: 1
      targets:
        - type: schedule
          name: Primary
      escalation_delay: 5
      
    - level: 2
      targets:
        - type: schedule
          name: Secondary
      escalation_delay: 15
      
    - level: 3
      targets:
        - type: user
          name: <EMAIL>
      escalation_delay: 30
```

### Incident Response Process
```mermaid
graph TB
    Alert[Alert Triggered] --> Triage{Severity?}
    
    Triage -->|Critical| Page[Page On-Call]
    Triage -->|Warning| Slack[Slack Notification]
    Triage -->|Info| Log[Log Only]
    
    Page --> Acknowledge[Acknowledge Alert]
    Acknowledge --> Investigate[Investigate Issue]
    
    Investigate --> Fix{Can Fix?}
    Fix -->|Yes| Implement[Implement Fix]
    Fix -->|No| Escalate[Escalate]
    
    Implement --> Verify[Verify Resolution]
    Verify --> Document[Document Incident]
    
    Escalate --> Implement
    
    Document --> PostMortem{Severity?}
    PostMortem -->|Critical| Meeting[Post-Mortem Meeting]
    PostMortem -->|Other| Report[Incident Report]
```

## 🛡️ Alert Testing

### Alert Testing Framework
```python
# tests/test_alerts.py
import pytest
from prometheus_client import CollectorRegistry, Counter, Gauge
import time

class AlertTester:
    """Test alert rules against metrics"""
    
    def __init__(self, rules_file):
        self.rules = self.load_rules(rules_file)
        self.registry = CollectorRegistry()
        
    def test_api_down_alert(self):
        """Test APIDown alert triggers correctly"""
        # Create metric
        up = Gauge('up', 'Target up', ['job'], registry=self.registry)
        
        # Set metric to down
        up.labels(job='ymip-api').set(0)
        
        # Evaluate rules
        alerts = self.evaluate_rules()
        
        # Verify alert fired
        assert any(a['alertname'] == 'APIDown' for a in alerts)
        
    def test_high_error_rate_alert(self):
        """Test high error rate alert"""
        # Create metrics
        requests = Counter(
            'ymip_http_requests_total',
            'Total requests',
            ['status'],
            registry=self.registry
        )
        
        # Simulate traffic
        for _ in range(100):
            requests.labels(status='200').inc()
        for _ in range(10):
            requests.labels(status='500').inc()
            
        # Should trigger alert (10% error rate > 5% threshold)
        alerts = self.evaluate_rules()
        assert any(a['alertname'] == 'APIHighErrorRate' for a in alerts)
```

## 📚 Additional Resources

- [Prometheus Alerting](https://prometheus.io/docs/alerting/latest/overview/)
- [Alert Manager Documentation](https://prometheus.io/docs/alerting/latest/alertmanager/)
- [SRE Workbook - Alerting](https://sre.google/workbook/alerting-on-slos/)
- [Effective Alerting](https://www.oreilly.com/library/view/effective-monitoring-and/9781449333515/)