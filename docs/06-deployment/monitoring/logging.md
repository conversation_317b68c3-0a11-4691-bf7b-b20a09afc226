# Centralized Logging Configuration

## 🎯 Target Audience

- **DevOps Engineers**: Setting up logging infrastructure
- **Developers**: Implementing application logging
- **Security Teams**: Monitoring for security events

## 📋 Overview

This guide covers implementing centralized logging for the Yemen Market Integration Platform, focusing on capturing, storing, and analyzing logs from econometric models, data pipelines, and API services.

## 🏗️ Logging Architecture

```mermaid
graph LR
    subgraph "Application Layer"
        API[API Services]
        Workers[Worker Processes]
        Models[Econometric Models]
        Pipeline[Data Pipeline]
    end
    
    subgraph "Collection Layer"
        Fluentd[Fluentd/Fluent Bit]
        Filebeat[Filebeat]
        Vector[Vector]
    end
    
    subgraph "Processing Layer"
        Logstash[Logstash]
        Kafka[Kafka]
    end
    
    subgraph "Storage Layer"
        ES[Elasticsearch]
        S3[S3/Object Storage]
        CloudWatch[CloudWatch Logs]
    end
    
    subgraph "Analysis Layer"
        Kibana[Kibana]
        Grafana[Grafana Loki]
        Alerts[Alert Manager]
    end
    
    API --> Fluentd
    Workers --> Fluentd
    Models --> Vector
    Pipeline --> Filebeat
    
    Fluentd --> Kafka
    Filebeat --> Kafka
    Vector --> Kafka
    
    Kafka --> Logstash
    Logstash --> ES
    Logstash --> S3
    
    ES --> Kibana
    ES --> Alerts
```

## 📝 Application Logging

### Python Logging Configuration
```python
# src/yemen_market/utils/logging_config.py
import logging
import logging.config
import json
from datetime import datetime
from pythonjsonlogger import jsonlogger
import os

class CustomJsonFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter for structured logging"""
    
    def add_fields(self, log_record, record, message_dict):
        super(CustomJsonFormatter, self).add_fields(log_record, record, message_dict)
        
        # Add custom fields
        log_record['timestamp'] = datetime.utcnow().isoformat()
        log_record['service'] = 'ymip'
        log_record['environment'] = os.getenv('YMIP_ENV', 'development')
        log_record['host'] = os.getenv('HOSTNAME', 'localhost')
        log_record['version'] = os.getenv('APP_VERSION', 'unknown')
        
        # Add trace context if available
        if hasattr(record, 'trace_id'):
            log_record['trace_id'] = record.trace_id
        if hasattr(record, 'span_id'):
            log_record['span_id'] = record.span_id
        
        # Add user context
        if hasattr(record, 'user_id'):
            log_record['user_id'] = record.user_id

LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'json': {
            '()': CustomJsonFormatter,
            'format': '%(timestamp)s %(level)s %(name)s %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        }
    },
    'filters': {
        'sensitive_data': {
            '()': 'yemen_market.utils.logging.SensitiveDataFilter'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'json',
            'stream': 'ext://sys.stdout',
            'filters': ['sensitive_data']
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'DEBUG',
            'formatter': 'json',
            'filename': '/var/log/ymip/app.log',
            'maxBytes': 104857600,  # 100MB
            'backupCount': 10,
            'filters': ['sensitive_data']
        },
        'error_file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'ERROR',
            'formatter': 'json',
            'filename': '/var/log/ymip/error.log',
            'maxBytes': 104857600,
            'backupCount': 10
        },
        'performance': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'INFO',
            'formatter': 'json',
            'filename': '/var/log/ymip/performance.log',
            'maxBytes': 104857600,
            'backupCount': 5
        }
    },
    'loggers': {
        'yemen_market': {
            'level': 'DEBUG',
            'handlers': ['console', 'file', 'error_file'],
            'propagate': False
        },
        'yemen_market.models': {
            'level': 'INFO',
            'handlers': ['console', 'file', 'performance'],
            'propagate': False
        },
        'yemen_market.data': {
            'level': 'INFO',
            'handlers': ['console', 'file'],
            'propagate': False
        }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['console', 'file']
    }
}

def setup_logging():
    """Initialize logging configuration"""
    logging.config.dictConfig(LOGGING_CONFIG)
    
    # Set third-party library log levels
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)

class SensitiveDataFilter(logging.Filter):
    """Filter to redact sensitive data from logs"""
    
    SENSITIVE_PATTERNS = [
        ('password', '***REDACTED***'),
        ('api_key', '***REDACTED***'),
        ('secret', '***REDACTED***'),
        ('token', '***REDACTED***'),
        ('authorization', '***REDACTED***')
    ]
    
    def filter(self, record):
        # Redact sensitive data from message
        message = record.getMessage()
        for pattern, replacement in self.SENSITIVE_PATTERNS:
            if pattern.lower() in message.lower():
                record.msg = self._redact_pattern(message, pattern, replacement)
        
        # Redact from extra fields
        for attr in dir(record):
            if not attr.startswith('_'):
                value = getattr(record, attr, None)
                if isinstance(value, str):
                    for pattern, replacement in self.SENSITIVE_PATTERNS:
                        if pattern.lower() in attr.lower():
                            setattr(record, attr, replacement)
        
        return True
    
    def _redact_pattern(self, text, pattern, replacement):
        """Redact pattern from text"""
        import re
        return re.sub(
            rf'{pattern}\s*[=:]\s*\S+',
            f'{pattern}={replacement}',
            text,
            flags=re.IGNORECASE
        )
```

### Model Execution Logging
```python
# src/yemen_market/models/logging_decorator.py
import logging
import time
import functools
import traceback
from contextlib import contextmanager

logger = logging.getLogger(__name__)

def log_model_execution(model_type):
    """Decorator to log model execution details"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            # Create execution context
            execution_id = f"{model_type}_{int(start_time)}"
            
            # Log start
            logger.info(
                "Model execution started",
                extra={
                    'model_type': model_type,
                    'execution_id': execution_id,
                    'function': func.__name__,
                    'args_count': len(args),
                    'kwargs_keys': list(kwargs.keys())
                }
            )
            
            try:
                # Execute model
                result = func(*args, **kwargs)
                
                # Log success
                execution_time = time.time() - start_time
                logger.info(
                    "Model execution completed",
                    extra={
                        'model_type': model_type,
                        'execution_id': execution_id,
                        'execution_time': execution_time,
                        'status': 'success'
                    }
                )
                
                # Log performance metrics
                if hasattr(result, 'metrics'):
                    logger.info(
                        "Model performance metrics",
                        extra={
                            'model_type': model_type,
                            'execution_id': execution_id,
                            'metrics': result.metrics
                        }
                    )
                
                return result
                
            except Exception as e:
                # Log failure
                execution_time = time.time() - start_time
                logger.error(
                    f"Model execution failed: {str(e)}",
                    extra={
                        'model_type': model_type,
                        'execution_id': execution_id,
                        'execution_time': execution_time,
                        'status': 'failed',
                        'error_type': type(e).__name__,
                        'error_message': str(e),
                        'traceback': traceback.format_exc()
                    }
                )
                raise
        
        return wrapper
    return decorator

@contextmanager
def log_data_processing(operation_name, **context):
    """Context manager for logging data processing operations"""
    start_time = time.time()
    operation_id = f"{operation_name}_{int(start_time)}"
    
    logger.info(
        f"Data operation started: {operation_name}",
        extra={
            'operation': operation_name,
            'operation_id': operation_id,
            'context': context
        }
    )
    
    try:
        yield operation_id
        
        duration = time.time() - start_time
        logger.info(
            f"Data operation completed: {operation_name}",
            extra={
                'operation': operation_name,
                'operation_id': operation_id,
                'duration': duration,
                'status': 'success'
            }
        )
        
    except Exception as e:
        duration = time.time() - start_time
        logger.error(
            f"Data operation failed: {operation_name}",
            extra={
                'operation': operation_name,
                'operation_id': operation_id,
                'duration': duration,
                'status': 'failed',
                'error': str(e),
                'traceback': traceback.format_exc()
            }
        )
        raise
```

## 🚀 Log Collection

### Fluent Bit Configuration
```yaml
# fluent-bit/fluent-bit.conf
[SERVICE]
    Flush         5
    Daemon        off
    Log_Level     info
    Parsers_File  parsers.conf
    
[INPUT]
    Name              tail
    Path              /var/log/ymip/*.log
    Parser            json
    Tag               ymip.*
    Refresh_Interval  5
    Mem_Buf_Limit     50MB
    Skip_Long_Lines   On
    
[INPUT]
    Name            systemd
    Tag             host.*
    Read_From_Tail  On
    
[FILTER]
    Name         record_modifier
    Match        ymip.*
    Record       cluster ${CLUSTER_NAME}
    Record       node ${NODE_NAME}
    Record       namespace ${NAMESPACE}
    Record       pod ${POD_NAME}
    
[FILTER]
    Name         nest
    Match        ymip.*
    Operation    lift
    Nested_under kubernetes
    
[OUTPUT]
    Name         kafka
    Match        ymip.*
    Brokers      kafka-broker-1:9092,kafka-broker-2:9092
    Topics       ymip-logs
    Timestamp_Key timestamp
    
[OUTPUT]
    Name         s3
    Match        ymip.error
    bucket       ymip-logs-archive
    region       us-east-1
    total_file_size 50M
    upload_timeout 10m
    use_put_object On

# parsers.conf
[PARSER]
    Name         json
    Format       json
    Time_Key     timestamp
    Time_Format  %Y-%m-%dT%H:%M:%S.%LZ
    Time_Keep    On
```

### Vector Configuration
```toml
# vector/vector.toml
[sources.ymip_logs]
type = "file"
include = ["/var/log/ymip/*.log"]
encoding.codec = "json"

[sources.docker_logs]
type = "docker_logs"
docker_host = "unix:///var/run/docker.sock"

[transforms.parse_logs]
type = "remap"
inputs = ["ymip_logs"]
source = '''
.service = "ymip"
.environment = get_env_var!("YMIP_ENV")
.host = get_hostname!()
.level = upcase!(string!(.level))

# Parse model metrics
if exists(.model_type) {
  .metrics.model_type = del(.model_type)
  .metrics.execution_time = del(.execution_time)
}

# Add GeoIP data for market locations
if exists(.market_id) {
  .location = get_enrichment_table_record!(
    "market_locations",
    { "market_id": .market_id }
  )
}
'''

[transforms.sample_logs]
type = "sample"
inputs = ["parse_logs"]
rate = 0.1
condition.type = "vrl"
condition.source = '.level == "DEBUG"'

[sinks.elasticsearch]
type = "elasticsearch"
inputs = ["parse_logs"]
endpoint = "https://elasticsearch:9200"
index = "ymip-logs-%Y.%m.%d"
auth.strategy = "basic"
auth.user = "${ES_USER}"
auth.password = "${ES_PASSWORD}"
batch.max_bytes = 10485760
batch.timeout_secs = 5

[sinks.s3_archive]
type = "aws_s3"
inputs = ["parse_logs"]
bucket = "ymip-logs-archive"
key_prefix = "logs/year=%Y/month=%m/day=%d/"
compression = "gzip"
encoding.codec = "json"
batch.max_bytes = 52428800  # 50MB
```

## 📊 Log Storage

### Elasticsearch Index Template
```json
{
  "index_patterns": ["ymip-logs-*"],
  "template": {
    "settings": {
      "number_of_shards": 3,
      "number_of_replicas": 1,
      "index.lifecycle.name": "ymip-logs-policy",
      "index.lifecycle.rollover_alias": "ymip-logs"
    },
    "mappings": {
      "properties": {
        "timestamp": {
          "type": "date",
          "format": "strict_date_time"
        },
        "level": {
          "type": "keyword"
        },
        "service": {
          "type": "keyword"
        },
        "environment": {
          "type": "keyword"
        },
        "message": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "trace_id": {
          "type": "keyword"
        },
        "span_id": {
          "type": "keyword"
        },
        "model_type": {
          "type": "keyword"
        },
        "execution_time": {
          "type": "float"
        },
        "error_type": {
          "type": "keyword"
        },
        "metrics": {
          "type": "object"
        },
        "location": {
          "type": "geo_point"
        }
      }
    }
  }
}
```

### Index Lifecycle Management
```json
{
  "policy": {
    "phases": {
      "hot": {
        "min_age": "0ms",
        "actions": {
          "rollover": {
            "max_age": "1d",
            "max_size": "50GB"
          },
          "set_priority": {
            "priority": 100
          }
        }
      },
      "warm": {
        "min_age": "7d",
        "actions": {
          "shrink": {
            "number_of_shards": 1
          },
          "forcemerge": {
            "max_num_segments": 1
          },
          "set_priority": {
            "priority": 50
          }
        }
      },
      "cold": {
        "min_age": "30d",
        "actions": {
          "set_priority": {
            "priority": 0
          },
          "migrate": {
            "enabled": false
          }
        }
      },
      "delete": {
        "min_age": "90d",
        "actions": {
          "delete": {}
        }
      }
    }
  }
}
```

## 📈 Log Analysis

### Kibana Dashboards
```json
{
  "version": "8.0.0",
  "objects": [
    {
      "id": "ymip-overview-dashboard",
      "type": "dashboard",
      "attributes": {
        "title": "YMIP System Overview",
        "panels": [
          {
            "gridData": {
              "x": 0,
              "y": 0,
              "w": 24,
              "h": 15
            },
            "type": "visualization",
            "visualization": {
              "title": "Log Volume Over Time",
              "visState": {
                "type": "line",
                "aggs": [
                  {
                    "id": "1",
                    "type": "count",
                    "schema": "metric"
                  },
                  {
                    "id": "2",
                    "type": "date_histogram",
                    "schema": "segment",
                    "params": {
                      "field": "timestamp",
                      "interval": "auto"
                    }
                  },
                  {
                    "id": "3",
                    "type": "terms",
                    "schema": "group",
                    "params": {
                      "field": "level.keyword",
                      "size": 5
                    }
                  }
                ]
              }
            }
          },
          {
            "gridData": {
              "x": 0,
              "y": 15,
              "w": 12,
              "h": 15
            },
            "type": "visualization",
            "visualization": {
              "title": "Model Execution Times",
              "visState": {
                "type": "histogram",
                "aggs": [
                  {
                    "id": "1",
                    "type": "avg",
                    "schema": "metric",
                    "params": {
                      "field": "execution_time"
                    }
                  },
                  {
                    "id": "2",
                    "type": "terms",
                    "schema": "segment",
                    "params": {
                      "field": "model_type.keyword",
                      "size": 10
                    }
                  }
                ]
              }
            }
          }
        ]
      }
    }
  ]
}
```

### Log Queries
```kusto
// Find slow model executions
GET ymip-logs-*/_search
{
  "query": {
    "bool": {
      "must": [
        {"exists": {"field": "execution_time"}},
        {"range": {"execution_time": {"gte": 300}}}
      ]
    }
  },
  "sort": [{"execution_time": "desc"}],
  "size": 100
}

// Error analysis by service
GET ymip-logs-*/_search
{
  "size": 0,
  "query": {
    "term": {"level": "ERROR"}
  },
  "aggs": {
    "by_service": {
      "terms": {
        "field": "service.keyword",
        "size": 20
      },
      "aggs": {
        "by_error": {
          "terms": {
            "field": "error_type.keyword",
            "size": 10
          }
        }
      }
    }
  }
}

// Trace analysis
GET ymip-logs-*/_search
{
  "query": {
    "term": {"trace_id": "abc123"}
  },
  "sort": [{"timestamp": "asc"}]
}
```

## 🚨 Alerting

### Alert Rules
```yaml
# alerts/logging_alerts.yml
groups:
  - name: ymip_logging
    rules:
      - alert: HighErrorRate
        expr: |
          rate(log_messages_total{level="ERROR"}[5m]) > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors/sec"
          
      - alert: ModelExecutionSlow
        expr: |
          histogram_quantile(0.95, 
            rate(model_execution_duration_bucket[5m])
          ) > 600
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Model execution is slow"
          description: "95th percentile execution time is {{ $value }}s"
          
      - alert: LogIngestionFailure
        expr: |
          rate(log_ingestion_failures_total[5m]) > 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Log ingestion failing"
          description: "Logs are not being ingested properly"
```

## 🔐 Security Considerations

### Log Sanitization
```python
# src/yemen_market/utils/log_sanitizer.py
import re
import hashlib

class LogSanitizer:
    """Sanitize sensitive data in logs"""
    
    PATTERNS = {
        'email': (r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', 'email_hash'),
        'ip': (r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b', 'ip_hash'),
        'phone': (r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', 'phone_hash'),
        'credit_card': (r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b', 'REDACTED_CC'),
        'api_key': (r'api[_-]?key["\']?\s*[:=]\s*["\']?([^"\'\s]+)', 'REDACTED_API_KEY')
    }
    
    @classmethod
    def sanitize(cls, log_entry):
        """Sanitize a log entry"""
        if isinstance(log_entry, dict):
            return cls._sanitize_dict(log_entry)
        elif isinstance(log_entry, str):
            return cls._sanitize_string(log_entry)
        return log_entry
    
    @classmethod
    def _sanitize_dict(cls, data):
        """Sanitize dictionary recursively"""
        sanitized = {}
        for key, value in data.items():
            if isinstance(value, dict):
                sanitized[key] = cls._sanitize_dict(value)
            elif isinstance(value, list):
                sanitized[key] = [cls.sanitize(item) for item in value]
            elif isinstance(value, str):
                sanitized[key] = cls._sanitize_string(value)
            else:
                sanitized[key] = value
        return sanitized
    
    @classmethod
    def _sanitize_string(cls, text):
        """Sanitize string content"""
        for pattern_name, (pattern, replacement) in cls.PATTERNS.items():
            if pattern_name in ['email', 'ip', 'phone']:
                # Hash sensitive data instead of removing
                text = re.sub(
                    pattern,
                    lambda m: f"{replacement}:{hashlib.sha256(m.group().encode()).hexdigest()[:8]}",
                    text
                )
            else:
                # Redact completely
                text = re.sub(pattern, replacement, text)
        return text
```

## 🔧 Troubleshooting

### Common Issues

1. **High Log Volume**
```yaml
# Implement sampling for debug logs
transforms:
  sample_debug:
    type: sample
    rate: 0.1  # Keep only 10% of debug logs
    condition:
      type: check_fields
      message.level.eq: debug
```

2. **Log Parsing Failures**
```python
# Add fallback parser
try:
    parsed = json.loads(log_line)
except json.JSONDecodeError:
    parsed = {
        'message': log_line,
        'level': 'INFO',
        'timestamp': datetime.utcnow().isoformat(),
        'parse_error': True
    }
```

3. **Storage Issues**
```bash
# Monitor disk usage
df -h /var/log
du -sh /var/log/ymip/*

# Clean old logs
find /var/log/ymip -name "*.log" -mtime +7 -delete
```

## 📚 Additional Resources

- [Elasticsearch Logging Best Practices](https://www.elastic.co/guide/en/elasticsearch/reference/current/logging.html)
- [Fluent Bit Documentation](https://docs.fluentbit.io/)
- [Vector Documentation](https://vector.dev/docs/)
- [Structured Logging Guide](https://www.structlog.org/)