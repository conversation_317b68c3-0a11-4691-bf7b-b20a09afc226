# Authentication and Access Control

## 🎯 Target Audience

- **Security Engineers**: Implementing authentication systems
- **DevOps Engineers**: Managing access controls
- **Developers**: Integrating authentication in applications

## 📋 Overview

This guide covers implementing secure authentication and access control for the Yemen Market Integration Platform, including identity management, API authentication, and role-based access control for sensitive economic data.

## 🏗️ Authentication Architecture

```mermaid
graph TB
    subgraph "Identity Providers"
        OIDC[OIDC Provider]
        SAML[SAML IdP]
        Local[Local Auth]
        MFA[MFA Service]
    end
    
    subgraph "Authentication Layer"
        Gateway[API Gateway]
        AuthService[Auth Service]
        TokenStore[Token Store]
        SessionStore[Session Store]
    end
    
    subgraph "Authorization"
        RBAC[RBAC Engine]
        Policies[Policy Engine]
        Permissions[Permissions DB]
    end
    
    subgraph "Applications"
        API[REST API]
        Dashboard[Web Dashboard]
        CLI[CLI Tools]
        Notebooks[Jupyter Notebooks]
    end
    
    subgraph "Audit"
        AuditLog[Audit Logger]
        Compliance[Compliance Reports]
    end
    
    OIDC --> Gateway
    SAML --> Gateway
    Local --> Gateway
    
    Gateway --> AuthService
    AuthService --> TokenStore
    AuthService --> SessionStore
    AuthService --> MFA
    
    AuthService --> RBAC
    RBAC --> Policies
    RBAC --> Permissions
    
    API --> RBAC
    Dashboard --> RBAC
    CLI --> RBAC
    Notebooks --> RBAC
    
    RBAC --> AuditLog
    AuditLog --> Compliance
```

## 🔐 Authentication Implementation

### JWT-Based Authentication
```python
# src/yemen_market/auth/jwt_auth.py
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
import jwt
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from passlib.context import CryptContext
import secrets

class JWTAuthManager:
    """JWT-based authentication manager"""
    
    def __init__(self, 
                 private_key_path: str,
                 public_key_path: str,
                 algorithm: str = "RS256",
                 token_expiry: int = 3600):
        self.algorithm = algorithm
        self.token_expiry = token_expiry
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Load keys
        with open(private_key_path, 'rb') as f:
            self.private_key = serialization.load_pem_private_key(
                f.read(),
                password=None
            )
        
        with open(public_key_path, 'rb') as f:
            self.public_key = serialization.load_pem_public_key(f.read())
    
    def create_access_token(self, 
                          user_id: str,
                          roles: List[str],
                          permissions: List[str],
                          additional_claims: Dict[str, Any] = None) -> str:
        """Create JWT access token"""
        now = datetime.now(timezone.utc)
        expire = now + timedelta(seconds=self.token_expiry)
        
        claims = {
            "sub": user_id,
            "iat": now,
            "exp": expire,
            "nbf": now,
            "jti": secrets.token_urlsafe(16),
            "type": "access",
            "roles": roles,
            "permissions": permissions,
            "iss": "ymip-auth",
            "aud": ["ymip-api", "ymip-dashboard"]
        }
        
        if additional_claims:
            claims.update(additional_claims)
        
        return jwt.encode(claims, self.private_key, algorithm=self.algorithm)
    
    def create_refresh_token(self, user_id: str) -> str:
        """Create refresh token"""
        now = datetime.now(timezone.utc)
        expire = now + timedelta(days=30)
        
        claims = {
            "sub": user_id,
            "iat": now,
            "exp": expire,
            "jti": secrets.token_urlsafe(16),
            "type": "refresh",
            "iss": "ymip-auth"
        }
        
        return jwt.encode(claims, self.private_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str, token_type: str = "access") -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            claims = jwt.decode(
                token,
                self.public_key,
                algorithms=[self.algorithm],
                issuer="ymip-auth",
                options={"require": ["exp", "iat", "sub", "jti", "type"]}
            )
            
            if claims.get("type") != token_type:
                raise jwt.InvalidTokenError(f"Invalid token type: expected {token_type}")
            
            # Check if token is blacklisted
            if self.is_token_blacklisted(claims["jti"]):
                raise jwt.InvalidTokenError("Token has been revoked")
            
            return claims
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.InvalidTokenError as e:
            raise AuthenticationError(f"Invalid token: {str(e)}")
    
    def revoke_token(self, jti: str, expiry: datetime):
        """Revoke a token by adding to blacklist"""
        # Store in Redis with expiry
        redis_client.setex(
            f"blacklist:{jti}",
            int((expiry - datetime.now(timezone.utc)).total_seconds()),
            "revoked"
        )
    
    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt"""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        return self.pwd_context.verify(plain_password, hashed_password)

# API Key Authentication for programmatic access
class APIKeyAuth:
    """API Key authentication for services"""
    
    def __init__(self, key_prefix: str = "ymip_"):
        self.key_prefix = key_prefix
        
    def generate_api_key(self, 
                        service_name: str,
                        permissions: List[str],
                        expires_at: Optional[datetime] = None) -> str:
        """Generate new API key"""
        key_id = secrets.token_urlsafe(8)
        key_secret = secrets.token_urlsafe(32)
        api_key = f"{self.key_prefix}{key_id}.{key_secret}"
        
        # Store key metadata
        key_data = {
            "service_name": service_name,
            "permissions": permissions,
            "created_at": datetime.utcnow(),
            "expires_at": expires_at,
            "key_hash": self.hash_key(key_secret),
            "last_used": None,
            "usage_count": 0
        }
        
        db.api_keys.insert_one({
            "_id": key_id,
            **key_data
        })
        
        return api_key
    
    def verify_api_key(self, api_key: str) -> Dict[str, Any]:
        """Verify API key and return metadata"""
        if not api_key.startswith(self.key_prefix):
            raise AuthenticationError("Invalid API key format")
        
        try:
            key_id, key_secret = api_key[len(self.key_prefix):].split(".", 1)
        except ValueError:
            raise AuthenticationError("Invalid API key format")
        
        # Fetch key metadata
        key_data = db.api_keys.find_one({"_id": key_id})
        if not key_data:
            raise AuthenticationError("API key not found")
        
        # Check expiration
        if key_data.get("expires_at") and key_data["expires_at"] < datetime.utcnow():
            raise AuthenticationError("API key has expired")
        
        # Verify key secret
        if not self.verify_key(key_secret, key_data["key_hash"]):
            raise AuthenticationError("Invalid API key")
        
        # Update usage statistics
        db.api_keys.update_one(
            {"_id": key_id},
            {
                "$set": {"last_used": datetime.utcnow()},
                "$inc": {"usage_count": 1}
            }
        )
        
        return {
            "service_name": key_data["service_name"],
            "permissions": key_data["permissions"]
        }
    
    def hash_key(self, key_secret: str) -> str:
        """Hash API key secret"""
        return hashlib.sha256(key_secret.encode()).hexdigest()
    
    def verify_key(self, key_secret: str, key_hash: str) -> bool:
        """Verify API key secret against hash"""
        return self.hash_key(key_secret) == key_hash
```

### OAuth2/OIDC Integration
```python
# src/yemen_market/auth/oauth2.py
from authlib.integrations.fastapi_client import OAuth
from fastapi import FastAPI, Depends, HTTPException, Request
from fastapi.security import OAuth2AuthorizationCodeBearer
import httpx

class OAuth2Manager:
    """OAuth2/OIDC authentication manager"""
    
    def __init__(self, app: FastAPI):
        self.oauth = OAuth()
        
        # Configure OIDC provider
        self.oauth.register(
            name='ymip',
            client_id=os.getenv('OIDC_CLIENT_ID'),
            client_secret=os.getenv('OIDC_CLIENT_SECRET'),
            server_metadata_url=os.getenv('OIDC_DISCOVERY_URL'),
            client_kwargs={
                'scope': 'openid email profile',
                'token_endpoint_auth_method': 'client_secret_basic'
            }
        )
        
        self.oauth2_scheme = OAuth2AuthorizationCodeBearer(
            authorizationUrl=os.getenv('OIDC_AUTH_URL'),
            tokenUrl=os.getenv('OIDC_TOKEN_URL')
        )
    
    async def login_redirect(self, request: Request):
        """Redirect to OIDC provider for login"""
        redirect_uri = request.url_for('auth_callback')
        return await self.oauth.ymip.authorize_redirect(request, redirect_uri)
    
    async def auth_callback(self, request: Request):
        """Handle OIDC callback"""
        token = await self.oauth.ymip.authorize_access_token(request)
        
        # Get user info from OIDC provider
        user = await self.oauth.ymip.userinfo(token=token)
        
        # Create or update user in local database
        local_user = await self.sync_user(user)
        
        # Create JWT for local use
        access_token = jwt_manager.create_access_token(
            user_id=local_user['id'],
            roles=local_user['roles'],
            permissions=local_user['permissions']
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": local_user
        }
    
    async def sync_user(self, oidc_user: Dict[str, Any]) -> Dict[str, Any]:
        """Sync OIDC user with local database"""
        user_data = {
            "email": oidc_user['email'],
            "name": oidc_user.get('name', ''),
            "oidc_sub": oidc_user['sub'],
            "updated_at": datetime.utcnow()
        }
        
        # Upsert user
        result = await db.users.update_one(
            {"oidc_sub": oidc_user['sub']},
            {"$set": user_data, "$setOnInsert": {"created_at": datetime.utcnow()}},
            upsert=True
        )
        
        # Fetch user with roles
        user = await db.users.find_one({"oidc_sub": oidc_user['sub']})
        
        # Apply default roles if new user
        if result.upserted_id:
            user['roles'] = ['viewer']
            user['permissions'] = ['read:public_data']
            await db.users.update_one(
                {"_id": user['_id']},
                {"$set": {"roles": user['roles'], "permissions": user['permissions']}}
            )
        
        return user
```

## 🛡️ Role-Based Access Control

### RBAC Implementation
```python
# src/yemen_market/auth/rbac.py
from typing import List, Set, Dict, Any
from enum import Enum
import fnmatch

class Permission(str, Enum):
    """System permissions"""
    # Data permissions
    READ_PUBLIC_DATA = "read:public_data"
    READ_SENSITIVE_DATA = "read:sensitive_data"
    WRITE_DATA = "write:data"
    DELETE_DATA = "delete:data"
    
    # Model permissions
    RUN_MODELS = "run:models"
    CREATE_MODELS = "create:models"
    DELETE_MODELS = "delete:models"
    
    # Admin permissions
    MANAGE_USERS = "manage:users"
    MANAGE_SYSTEM = "manage:system"
    VIEW_AUDIT_LOGS = "view:audit_logs"

class Role(str, Enum):
    """System roles"""
    VIEWER = "viewer"
    ANALYST = "analyst"
    RESEARCHER = "researcher"
    DATA_ENGINEER = "data_engineer"
    ADMIN = "admin"

class RBACManager:
    """Role-based access control manager"""
    
    # Role to permissions mapping
    ROLE_PERMISSIONS = {
        Role.VIEWER: {
            Permission.READ_PUBLIC_DATA
        },
        Role.ANALYST: {
            Permission.READ_PUBLIC_DATA,
            Permission.READ_SENSITIVE_DATA,
            Permission.RUN_MODELS
        },
        Role.RESEARCHER: {
            Permission.READ_PUBLIC_DATA,
            Permission.READ_SENSITIVE_DATA,
            Permission.WRITE_DATA,
            Permission.RUN_MODELS,
            Permission.CREATE_MODELS
        },
        Role.DATA_ENGINEER: {
            Permission.READ_PUBLIC_DATA,
            Permission.READ_SENSITIVE_DATA,
            Permission.WRITE_DATA,
            Permission.DELETE_DATA,
            Permission.RUN_MODELS
        },
        Role.ADMIN: set(Permission)  # All permissions
    }
    
    def __init__(self):
        self.resource_policies = {}
        self.initialize_policies()
    
    def initialize_policies(self):
        """Initialize resource-based policies"""
        # Market data policies
        self.add_resource_policy(
            "markets:*:public",
            [Permission.READ_PUBLIC_DATA]
        )
        self.add_resource_policy(
            "markets:*:sensitive",
            [Permission.READ_SENSITIVE_DATA]
        )
        
        # Model policies
        self.add_resource_policy(
            "models:tier1:*",
            [Permission.RUN_MODELS]
        )
        self.add_resource_policy(
            "models:tier3:*",
            [Permission.RUN_MODELS, Permission.READ_SENSITIVE_DATA]
        )
    
    def get_role_permissions(self, roles: List[Role]) -> Set[Permission]:
        """Get all permissions for given roles"""
        permissions = set()
        for role in roles:
            permissions.update(self.ROLE_PERMISSIONS.get(role, set()))
        return permissions
    
    def check_permission(self, 
                        user_permissions: Set[str],
                        required_permission: Permission) -> bool:
        """Check if user has required permission"""
        return required_permission in user_permissions
    
    def check_resource_access(self,
                            user_permissions: Set[str],
                            resource: str,
                            action: str) -> bool:
        """Check resource-level access"""
        # Check against resource policies
        for pattern, allowed_permissions in self.resource_policies.items():
            if fnmatch.fnmatch(resource, pattern):
                required = f"{action}:{resource.split(':')[0]}"
                return any(
                    perm in user_permissions 
                    for perm in allowed_permissions
                    if perm.startswith(action)
                )
        
        # Default deny
        return False
    
    def add_resource_policy(self, 
                          resource_pattern: str,
                          permissions: List[Permission]):
        """Add resource-based access policy"""
        self.resource_policies[resource_pattern] = permissions
    
    def get_accessible_resources(self,
                               user_permissions: Set[str],
                               resource_type: str) -> List[str]:
        """Get list of accessible resources for user"""
        accessible = []
        
        for pattern, required_perms in self.resource_policies.items():
            if pattern.startswith(f"{resource_type}:"):
                if any(perm in user_permissions for perm in required_perms):
                    accessible.append(pattern)
        
        return accessible

# FastAPI dependency for authorization
async def require_permission(permission: Permission):
    """Dependency to require specific permission"""
    async def permission_checker(
        current_user: Dict = Depends(get_current_user)
    ):
        if permission not in current_user.get('permissions', []):
            raise HTTPException(
                status_code=403,
                detail=f"Permission denied. Required: {permission}"
            )
        return current_user
    return permission_checker

# Attribute-based access control for fine-grained permissions
class ABACPolicy:
    """Attribute-based access control policy"""
    
    def __init__(self, name: str, effect: str, 
                 subjects: Dict[str, Any],
                 resources: Dict[str, Any],
                 actions: List[str],
                 conditions: Dict[str, Any] = None):
        self.name = name
        self.effect = effect  # "allow" or "deny"
        self.subjects = subjects
        self.resources = resources
        self.actions = actions
        self.conditions = conditions or {}
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """Evaluate policy against context"""
        # Check subject attributes
        if not self._match_attributes(context.get('subject', {}), self.subjects):
            return False
        
        # Check resource attributes
        if not self._match_attributes(context.get('resource', {}), self.resources):
            return False
        
        # Check action
        if context.get('action') not in self.actions:
            return False
        
        # Check conditions
        if not self._evaluate_conditions(context):
            return False
        
        return self.effect == "allow"
    
    def _match_attributes(self, actual: Dict, required: Dict) -> bool:
        """Match attributes using patterns"""
        for key, pattern in required.items():
            if key not in actual:
                return False
            if not fnmatch.fnmatch(str(actual[key]), str(pattern)):
                return False
        return True
    
    def _evaluate_conditions(self, context: Dict[str, Any]) -> bool:
        """Evaluate policy conditions"""
        for condition_type, condition_value in self.conditions.items():
            if condition_type == "time_range":
                if not self._check_time_range(condition_value):
                    return False
            elif condition_type == "ip_range":
                if not self._check_ip_range(
                    context.get('request_ip'), 
                    condition_value
                ):
                    return False
        return True
```

## 🔑 Multi-Factor Authentication

### MFA Implementation
```python
# src/yemen_market/auth/mfa.py
import pyotp
import qrcode
from io import BytesIO
import base64

class MFAManager:
    """Multi-factor authentication manager"""
    
    def __init__(self, issuer: str = "Yemen Market Integration"):
        self.issuer = issuer
    
    def generate_secret(self) -> str:
        """Generate new TOTP secret"""
        return pyotp.random_base32()
    
    def generate_qr_code(self, user_email: str, secret: str) -> str:
        """Generate QR code for TOTP setup"""
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=user_email,
            issuer_name=self.issuer
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buf = BytesIO()
        img.save(buf, format='PNG')
        
        return base64.b64encode(buf.getvalue()).decode()
    
    def verify_totp(self, secret: str, token: str, window: int = 1) -> bool:
        """Verify TOTP token"""
        totp = pyotp.TOTP(secret)
        return totp.verify(token, valid_window=window)
    
    def generate_backup_codes(self, count: int = 10) -> List[str]:
        """Generate backup codes"""
        return [secrets.token_hex(4) for _ in range(count)]
    
    async def setup_mfa(self, user_id: str) -> Dict[str, Any]:
        """Setup MFA for user"""
        secret = self.generate_secret()
        backup_codes = self.generate_backup_codes()
        
        # Store in database
        await db.users.update_one(
            {"_id": user_id},
            {
                "$set": {
                    "mfa.secret": secret,
                    "mfa.backup_codes": [
                        {"code": self.hash_code(code), "used": False}
                        for code in backup_codes
                    ],
                    "mfa.enabled": False,
                    "mfa.setup_at": datetime.utcnow()
                }
            }
        )
        
        user = await db.users.find_one({"_id": user_id})
        qr_code = self.generate_qr_code(user['email'], secret)
        
        return {
            "secret": secret,
            "qr_code": qr_code,
            "backup_codes": backup_codes
        }
    
    async def enable_mfa(self, user_id: str, token: str) -> bool:
        """Enable MFA after verification"""
        user = await db.users.find_one({"_id": user_id})
        
        if not user.get('mfa', {}).get('secret'):
            raise ValueError("MFA not setup for user")
        
        if self.verify_totp(user['mfa']['secret'], token):
            await db.users.update_one(
                {"_id": user_id},
                {"$set": {"mfa.enabled": True}}
            )
            return True
        
        return False
    
    def hash_code(self, code: str) -> str:
        """Hash backup code"""
        return hashlib.sha256(code.encode()).hexdigest()
```

## 🌐 SSO Integration

### SAML Configuration
```xml
<!-- saml/metadata.xml -->
<EntityDescriptor 
    xmlns="urn:oasis:names:tc:SAML:2.0:metadata"
    entityID="https://api.yemen-market.org/saml/metadata">
    
    <SPSSODescriptor 
        protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol"
        AuthnRequestsSigned="true"
        WantAssertionsSigned="true">
        
        <KeyDescriptor use="signing">
            <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                <X509Data>
                    <X509Certificate>...</X509Certificate>
                </X509Data>
            </KeyInfo>
        </KeyDescriptor>
        
        <AssertionConsumerService
            Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
            Location="https://api.yemen-market.org/saml/acs"
            index="0"
            isDefault="true"/>
            
        <AttributeConsumingService index="0">
            <ServiceName xml:lang="en">Yemen Market Integration Platform</ServiceName>
            <RequestedAttribute Name="email" isRequired="true"/>
            <RequestedAttribute Name="name" isRequired="true"/>
            <RequestedAttribute Name="groups" isRequired="false"/>
        </AttributeConsumingService>
    </SPSSODescriptor>
</EntityDescriptor>
```

### SAML Integration Code
```python
# src/yemen_market/auth/saml.py
from onelogin.saml2.auth import OneLogin_Saml2_Auth
from onelogin.saml2.settings import OneLogin_Saml2_Settings

class SAMLAuth:
    """SAML authentication handler"""
    
    def __init__(self, settings_path: str):
        with open(settings_path) as f:
            self.settings = json.load(f)
    
    def init_saml_auth(self, request: Request) -> OneLogin_Saml2_Auth:
        """Initialize SAML auth object"""
        req = {
            'https': 'on' if request.url.scheme == 'https' else 'off',
            'http_host': request.headers['host'],
            'server_port': request.url.port,
            'script_name': request.url.path,
            'get_data': request.query_params,
            'post_data': await request.form() if request.method == 'POST' else {}
        }
        
        return OneLogin_Saml2_Auth(req, self.settings)
    
    async def login(self, request: Request):
        """Initiate SAML login"""
        auth = self.init_saml_auth(request)
        return auth.login()
    
    async def acs(self, request: Request):
        """Handle SAML assertion consumer service"""
        auth = self.init_saml_auth(request)
        auth.process_response()
        
        errors = auth.get_errors()
        if errors:
            raise AuthenticationError(f"SAML errors: {', '.join(errors)}")
        
        if not auth.is_authenticated():
            raise AuthenticationError("SAML authentication failed")
        
        # Get user attributes
        attributes = auth.get_attributes()
        user_data = {
            'email': attributes.get('email', [None])[0],
            'name': attributes.get('name', [None])[0],
            'groups': attributes.get('groups', []),
            'saml_session': auth.get_session_index()
        }
        
        # Sync with local user database
        local_user = await self.sync_user(user_data)
        
        # Create JWT
        access_token = jwt_manager.create_access_token(
            user_id=local_user['id'],
            roles=local_user['roles'],
            permissions=local_user['permissions'],
            additional_claims={'saml_session': user_data['saml_session']}
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer"
        }
```

## 📊 Session Management

### Secure Session Handling
```python
# src/yemen_market/auth/session.py
from typing import Optional
import redis
from cryptography.fernet import Fernet

class SessionManager:
    """Secure session management"""
    
    def __init__(self, 
                 redis_client: redis.Redis,
                 encryption_key: bytes,
                 session_timeout: int = 3600):
        self.redis = redis_client
        self.cipher = Fernet(encryption_key)
        self.timeout = session_timeout
    
    def create_session(self, 
                      user_id: str,
                      metadata: Dict[str, Any]) -> str:
        """Create new session"""
        session_id = secrets.token_urlsafe(32)
        
        session_data = {
            "user_id": user_id,
            "created_at": datetime.utcnow().isoformat(),
            "last_activity": datetime.utcnow().isoformat(),
            "metadata": metadata
        }
        
        # Encrypt session data
        encrypted_data = self.cipher.encrypt(
            json.dumps(session_data).encode()
        )
        
        # Store in Redis with expiry
        self.redis.setex(
            f"session:{session_id}",
            self.timeout,
            encrypted_data
        )
        
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve session data"""
        encrypted_data = self.redis.get(f"session:{session_id}")
        
        if not encrypted_data:
            return None
        
        try:
            decrypted_data = self.cipher.decrypt(encrypted_data)
            session_data = json.loads(decrypted_data)
            
            # Update last activity
            session_data["last_activity"] = datetime.utcnow().isoformat()
            
            # Re-encrypt and update
            encrypted_data = self.cipher.encrypt(
                json.dumps(session_data).encode()
            )
            
            self.redis.setex(
                f"session:{session_id}",
                self.timeout,
                encrypted_data
            )
            
            return session_data
            
        except Exception:
            # Invalid session data
            self.destroy_session(session_id)
            return None
    
    def destroy_session(self, session_id: str):
        """Destroy session"""
        self.redis.delete(f"session:{session_id}")
    
    def extend_session(self, session_id: str, extension: int = None):
        """Extend session timeout"""
        if self.redis.exists(f"session:{session_id}"):
            self.redis.expire(
                f"session:{session_id}",
                extension or self.timeout
            )
```

## 🔍 Audit Logging

### Authentication Audit Trail
```python
# src/yemen_market/auth/audit.py
class AuthAuditLogger:
    """Authentication audit logger"""
    
    def __init__(self, db_client):
        self.db = db_client
        self.collection = self.db.auth_audit_logs
    
    async def log_auth_event(self,
                           event_type: str,
                           user_id: Optional[str],
                           success: bool,
                           metadata: Dict[str, Any]):
        """Log authentication event"""
        event = {
            "timestamp": datetime.utcnow(),
            "event_type": event_type,
            "user_id": user_id,
            "success": success,
            "ip_address": metadata.get("ip_address"),
            "user_agent": metadata.get("user_agent"),
            "metadata": metadata
        }
        
        await self.collection.insert_one(event)
        
        # Alert on suspicious activity
        if not success:
            await self.check_failed_attempts(user_id, metadata.get("ip_address"))
    
    async def check_failed_attempts(self, 
                                  user_id: Optional[str],
                                  ip_address: Optional[str]):
        """Check for suspicious failed login attempts"""
        # Count recent failures
        query = {
            "event_type": "login",
            "success": False,
            "timestamp": {"$gte": datetime.utcnow() - timedelta(minutes=15)}
        }
        
        if user_id:
            query["user_id"] = user_id
        if ip_address:
            query["ip_address"] = ip_address
        
        count = await self.collection.count_documents(query)
        
        if count >= 5:
            # Trigger security alert
            await self.trigger_security_alert(
                f"Multiple failed login attempts: {count} in 15 minutes",
                {"user_id": user_id, "ip_address": ip_address}
            )
```

## 🛡️ Security Headers

### Security Middleware
```python
# src/yemen_market/auth/security_headers.py
from fastapi import Request
from fastapi.middleware.base import BaseHTTPMiddleware

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Add security headers to responses"""
    
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data: https:; "
            "font-src 'self'; "
            "connect-src 'self' https://api.yemen-market.org"
        )
        
        # HSTS for HTTPS
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = (
                "max-age=31536000; includeSubDomains"
            )
        
        return response
```

## 🔧 Testing Authentication

### Authentication Test Suite
```python
# tests/test_auth.py
import pytest
from unittest.mock import Mock, patch

class TestAuthentication:
    """Test authentication functionality"""
    
    @pytest.fixture
    def auth_manager(self):
        return JWTAuthManager(
            private_key_path="tests/keys/private.pem",
            public_key_path="tests/keys/public.pem"
        )
    
    def test_create_access_token(self, auth_manager):
        """Test JWT creation"""
        token = auth_manager.create_access_token(
            user_id="test_user",
            roles=["analyst"],
            permissions=["read:public_data"]
        )
        
        # Verify token
        claims = auth_manager.verify_token(token)
        assert claims["sub"] == "test_user"
        assert "analyst" in claims["roles"]
        assert "read:public_data" in claims["permissions"]
    
    def test_token_expiry(self, auth_manager):
        """Test token expiration"""
        # Create token with short expiry
        auth_manager.token_expiry = 1
        token = auth_manager.create_access_token(
            user_id="test_user",
            roles=[],
            permissions=[]
        )
        
        # Wait for expiry
        time.sleep(2)
        
        # Should raise exception
        with pytest.raises(AuthenticationError):
            auth_manager.verify_token(token)
    
    def test_rbac_permissions(self):
        """Test RBAC permission checking"""
        rbac = RBACManager()
        
        # Test role permissions
        analyst_perms = rbac.get_role_permissions([Role.ANALYST])
        assert Permission.READ_SENSITIVE_DATA in analyst_perms
        assert Permission.DELETE_DATA not in analyst_perms
        
        # Test resource access
        assert rbac.check_resource_access(
            analyst_perms,
            "markets:sana:public",
            "read"
        )
        assert not rbac.check_resource_access(
            analyst_perms,
            "models:tier3:vecm",
            "delete"
        )
```

## 📚 Additional Resources

- [OWASP Authentication Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Authentication_Cheat_Sheet.html)
- [JWT Best Practices](https://datatracker.ietf.org/doc/html/rfc8725)
- [OAuth 2.0 Security Best Practices](https://datatracker.ietf.org/doc/html/draft-ietf-oauth-security-topics)
- [NIST Digital Identity Guidelines](https://pages.nist.gov/800-63-3/)