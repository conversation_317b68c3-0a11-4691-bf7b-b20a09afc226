# Secrets Management Guide

## 🎯 Target Audience

- **Security Engineers**: Implementing secrets management
- **DevOps Engineers**: Managing deployment secrets
- **Developers**: Accessing secrets securely in applications

## 📋 Overview

This guide covers secure management of secrets and credentials for the Yemen Market Integration Platform, including API keys, database passwords, encryption keys, and sensitive configuration data.

## 🏗️ Secrets Architecture

```mermaid
graph TB
    subgraph "Secret Sources"
        EnvVars[Environment Variables]
        ConfigFiles[Config Files]
        Code[Hardcoded Secrets]
    end
    
    subgraph "Secret Stores"
        Vault[HashiCorp Vault]
        AWS[AWS Secrets Manager]
        Azure[Azure Key Vault]
        K8s[Kubernetes Secrets]
    end
    
    subgraph "Secret Delivery"
        InitContainer[Init Containers]
        Sidecar[Sidecar Proxy]
        SDK[Secret SDK]
        CSI[CSI Driver]
    end
    
    subgraph "Applications"
        API[API Service]
        Workers[Worker Nodes]
        Notebooks[Jupyter]
        CLI[CLI Tools]
    end
    
    subgraph "Audit & Compliance"
        AuditLog[Audit Logs]
        Rotation[Secret Rotation]
        Scanning[Secret Scanning]
    end
    
    EnvVars --> Vault
    ConfigFiles --> Vault
    Code --> Scanning
    
    Vault --> InitContainer
    AWS --> SDK
    Azure --> SDK
    K8s --> CSI
    
    InitContainer --> API
    Sidecar --> Workers
    SDK --> Notebooks
    CSI --> CLI
    
    Vault --> AuditLog
    Vault --> Rotation
```

## 🔐 HashiCorp Vault Configuration

### Vault Setup
```hcl
# vault/config.hcl
storage "consul" {
  address = "consul:8500"
  path    = "vault/"
}

listener "tcp" {
  address       = "0.0.0.0:8200"
  tls_cert_file = "/vault/certs/server.crt"
  tls_key_file  = "/vault/certs/server.key"
}

api_addr = "https://vault.yemen-market.org:8200"
cluster_addr = "https://vault.yemen-market.org:8201"

ui = true

audit {
  file {
    path = "/vault/logs/audit.log"
    format = "json"
  }
}

telemetry {
  prometheus_retention_time = "30s"
  disable_hostname = true
}
```

### Vault Policies
```hcl
# vault/policies/ymip-api.hcl
# Policy for API service
path "secret/data/ymip/api/*" {
  capabilities = ["read", "list"]
}

path "secret/data/ymip/database/*" {
  capabilities = ["read"]
}

path "auth/token/renew-self" {
  capabilities = ["update"]
}

path "auth/token/lookup-self" {
  capabilities = ["read"]
}

# vault/policies/ymip-admin.hcl
# Admin policy
path "secret/*" {
  capabilities = ["create", "read", "update", "delete", "list"]
}

path "auth/*" {
  capabilities = ["create", "read", "update", "delete", "list", "sudo"]
}

path "sys/*" {
  capabilities = ["create", "read", "update", "delete", "list", "sudo"]
}

# vault/policies/ymip-rotate.hcl
# Secret rotation policy
path "secret/data/ymip/*/rotate" {
  capabilities = ["create", "update"]
}

path "secret/metadata/ymip/*" {
  capabilities = ["read", "list"]
}
```

### Python Vault Integration
```python
# src/yemen_market/security/vault_client.py
import hvac
from typing import Dict, Any, Optional
import json
import os
from functools import lru_cache
from datetime import datetime, timedelta

class VaultClient:
    """HashiCorp Vault client for secrets management"""
    
    def __init__(self, 
                 vault_url: str,
                 auth_method: str = "kubernetes",
                 role: str = "ymip-api",
                 mount_point: str = "secret"):
        self.vault_url = vault_url
        self.auth_method = auth_method
        self.role = role
        self.mount_point = mount_point
        self.client = None
        self._token_expiry = None
        
    def authenticate(self):
        """Authenticate with Vault"""
        self.client = hvac.Client(url=self.vault_url)
        
        if self.auth_method == "kubernetes":
            # Read service account token
            with open('/var/run/secrets/kubernetes.io/serviceaccount/token') as f:
                jwt = f.read()
            
            # Authenticate with Kubernetes auth
            response = self.client.auth.kubernetes.login(
                role=self.role,
                jwt=jwt
            )
            self.client.token = response['auth']['client_token']
            self._token_expiry = datetime.utcnow() + timedelta(
                seconds=response['auth']['lease_duration']
            )
            
        elif self.auth_method == "approle":
            role_id = os.environ.get('VAULT_ROLE_ID')
            secret_id = os.environ.get('VAULT_SECRET_ID')
            
            response = self.client.auth.approle.login(
                role_id=role_id,
                secret_id=secret_id
            )
            self.client.token = response['auth']['client_token']
            
        elif self.auth_method == "token":
            self.client.token = os.environ.get('VAULT_TOKEN')
            
        if not self.client.is_authenticated():
            raise Exception("Failed to authenticate with Vault")
    
    def ensure_authenticated(self):
        """Ensure client is authenticated"""
        if not self.client or not self.client.is_authenticated():
            self.authenticate()
        elif self._token_expiry and datetime.utcnow() > self._token_expiry - timedelta(minutes=5):
            # Renew token if expiring soon
            self.client.auth.token.renew_self()
    
    @lru_cache(maxsize=128)
    def get_secret(self, path: str, version: Optional[int] = None) -> Dict[str, Any]:
        """Get secret from Vault with caching"""
        self.ensure_authenticated()
        
        try:
            if version:
                response = self.client.secrets.kv.v2.read_secret_version(
                    path=path,
                    version=version,
                    mount_point=self.mount_point
                )
            else:
                response = self.client.secrets.kv.v2.read_secret(
                    path=path,
                    mount_point=self.mount_point
                )
            
            return response['data']['data']
            
        except hvac.exceptions.InvalidPath:
            raise KeyError(f"Secret not found: {path}")
        except Exception as e:
            logger.error(f"Failed to read secret {path}: {e}")
            raise
    
    def create_secret(self, path: str, data: Dict[str, Any], cas: Optional[int] = None):
        """Create or update secret in Vault"""
        self.ensure_authenticated()
        
        self.client.secrets.kv.v2.create_or_update_secret(
            path=path,
            secret=data,
            cas=cas,
            mount_point=self.mount_point
        )
        
        # Clear cache for this path
        self.get_secret.cache_clear()
    
    def rotate_secret(self, path: str, rotation_func):
        """Rotate a secret"""
        self.ensure_authenticated()
        
        # Get current secret
        current = self.get_secret(path)
        
        # Generate new secret
        new_data = rotation_func(current)
        
        # Update in Vault with version tracking
        metadata = self.client.secrets.kv.v2.read_secret_metadata(
            path=path,
            mount_point=self.mount_point
        )
        
        self.create_secret(
            path=path,
            data=new_data,
            cas=metadata['data']['current_version']
        )
        
        # Log rotation
        logger.info(f"Rotated secret: {path}")
        
        return new_data
    
    def get_dynamic_credentials(self, backend: str, role: str) -> Dict[str, Any]:
        """Get dynamic credentials from Vault"""
        self.ensure_authenticated()
        
        if backend == "database":
            creds = self.client.secrets.database.generate_credentials(role)
            return {
                'username': creds['data']['username'],
                'password': creds['data']['password'],
                'ttl': creds['lease_duration']
            }
        elif backend == "aws":
            creds = self.client.secrets.aws.generate_credentials(role)
            return {
                'access_key': creds['data']['access_key'],
                'secret_key': creds['data']['secret_key'],
                'ttl': creds['lease_duration']
            }
        else:
            raise ValueError(f"Unsupported backend: {backend}")

# Global Vault client instance
vault_client = None

def get_vault_client() -> VaultClient:
    """Get or create Vault client"""
    global vault_client
    if not vault_client:
        vault_client = VaultClient(
            vault_url=os.getenv('VAULT_ADDR', 'https://vault.yemen-market.org:8200'),
            auth_method=os.getenv('VAULT_AUTH_METHOD', 'kubernetes'),
            role=os.getenv('VAULT_ROLE', 'ymip-api')
        )
    return vault_client
```

## 🔑 Kubernetes Secrets Integration

### Kubernetes Secret Objects
```yaml
# k8s/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: ymip-database
  namespace: ymip
type: Opaque
data:
  # Base64 encoded values
  username: eW1pcF91c2Vy
  password: <base64-encoded-password>
  
---
apiVersion: v1
kind: Secret
metadata:
  name: ymip-api-keys
  namespace: ymip
type: Opaque
stringData:
  # Plain text values (will be base64 encoded)
  jwt_private_key: |
    -----BEGIN RSA PRIVATE KEY-----
    ...
    -----END RSA PRIVATE KEY-----
  encryption_key: "your-32-byte-encryption-key-here"
  
---
apiVersion: v1
kind: Secret
metadata:
  name: ymip-oauth
  namespace: ymip
type: Opaque
stringData:
  client_id: "ymip-oauth-client"
  client_secret: "oauth-client-secret"
  
---
# External Secrets Operator for Vault integration
apiVersion: external-secrets.io/v1beta1
kind: SecretStore
metadata:
  name: vault-backend
  namespace: ymip
spec:
  provider:
    vault:
      server: "https://vault.yemen-market.org:8200"
      path: "secret"
      version: "v2"
      auth:
        kubernetes:
          mountPath: "kubernetes"
          role: "ymip-api"
          serviceAccountRef:
            name: "ymip-api"
            
---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: ymip-secrets
  namespace: ymip
spec:
  refreshInterval: 15m
  secretStoreRef:
    name: vault-backend
    kind: SecretStore
  target:
    name: ymip-secrets
    creationPolicy: Owner
  dataFrom:
  - extract:
      key: ymip/api/config
```

### Secrets CSI Driver
```yaml
# k8s/secrets-csi.yaml
apiVersion: v1
kind: SecretProviderClass
metadata:
  name: ymip-secrets-provider
  namespace: ymip
spec:
  provider: vault
  parameters:
    vaultAddress: "https://vault.yemen-market.org:8200"
    roleName: "ymip-api"
    objects: |
      - objectName: "database-password"
        secretPath: "secret/data/ymip/database"
        secretKey: "password"
      - objectName: "api-key"
        secretPath: "secret/data/ymip/api"
        secretKey: "key"
      - objectName: "jwt-private-key"
        secretPath: "secret/data/ymip/jwt"
        secretKey: "private_key"
        
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ymip-api
  namespace: ymip
spec:
  template:
    spec:
      serviceAccountName: ymip-api
      volumes:
      - name: secrets-store
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: ymip-secrets-provider
      containers:
      - name: api
        volumeMounts:
        - name: secrets-store
          mountPath: "/mnt/secrets"
          readOnly: true
        env:
        - name: DATABASE_PASSWORD_FILE
          value: "/mnt/secrets/database-password"
```

## 🔄 Secret Rotation

### Automated Secret Rotation
```python
# src/yemen_market/security/secret_rotation.py
from abc import ABC, abstractmethod
from typing import Dict, Any
import secrets
import string
from datetime import datetime, timedelta

class SecretRotator(ABC):
    """Base class for secret rotation"""
    
    @abstractmethod
    def generate_new_secret(self) -> Any:
        """Generate new secret value"""
        pass
    
    @abstractmethod
    def update_secret(self, old_value: Any, new_value: Any) -> bool:
        """Update secret in target system"""
        pass
    
    @abstractmethod
    def validate_secret(self, value: Any) -> bool:
        """Validate secret works correctly"""
        pass
    
    def rotate(self) -> bool:
        """Perform secret rotation"""
        # Generate new secret
        new_secret = self.generate_new_secret()
        
        # Get current secret
        old_secret = self.get_current_secret()
        
        # Update in target system
        if not self.update_secret(old_secret, new_secret):
            logger.error("Failed to update secret in target system")
            return False
        
        # Validate new secret works
        if not self.validate_secret(new_secret):
            logger.error("New secret validation failed, rolling back")
            self.update_secret(new_secret, old_secret)
            return False
        
        # Update in secret store
        self.store_secret(new_secret)
        
        # Log rotation
        self.log_rotation()
        
        return True

class DatabasePasswordRotator(SecretRotator):
    """Rotate database passwords"""
    
    def __init__(self, db_config: Dict[str, Any], vault_client: VaultClient):
        self.db_config = db_config
        self.vault_client = vault_client
        self.secret_path = "ymip/database/password"
    
    def generate_new_secret(self) -> str:
        """Generate strong password"""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(32))
    
    def update_secret(self, old_password: str, new_password: str) -> bool:
        """Update database password"""
        try:
            # Connect with old password
            conn = psycopg2.connect(
                host=self.db_config['host'],
                user=self.db_config['user'],
                password=old_password,
                database=self.db_config['database']
            )
            
            # Update password
            with conn.cursor() as cur:
                cur.execute(
                    "ALTER USER %s WITH PASSWORD %s",
                    (self.db_config['user'], new_password)
                )
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Failed to update database password: {e}")
            return False
    
    def validate_secret(self, password: str) -> bool:
        """Validate new password works"""
        try:
            conn = psycopg2.connect(
                host=self.db_config['host'],
                user=self.db_config['user'],
                password=password,
                database=self.db_config['database']
            )
            conn.close()
            return True
        except:
            return False

class APIKeyRotator(SecretRotator):
    """Rotate API keys"""
    
    def __init__(self, vault_client: VaultClient):
        self.vault_client = vault_client
        self.secret_path = "ymip/api/keys"
    
    def generate_new_secret(self) -> str:
        """Generate new API key"""
        prefix = "ymip_"
        key = secrets.token_urlsafe(32)
        return f"{prefix}{key}"
    
    def update_secret(self, old_key: str, new_key: str) -> bool:
        """Update API key in system"""
        try:
            # Add new key to allowed keys
            current_keys = self.vault_client.get_secret(self.secret_path)
            
            # Keep last 2 keys for graceful rotation
            active_keys = current_keys.get('active_keys', [])
            active_keys.append(new_key)
            if len(active_keys) > 2:
                active_keys = active_keys[-2:]
            
            # Update in Vault
            self.vault_client.create_secret(
                self.secret_path,
                {
                    'primary_key': new_key,
                    'active_keys': active_keys,
                    'rotated_at': datetime.utcnow().isoformat()
                }
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update API key: {e}")
            return False

# Rotation scheduler
class SecretRotationScheduler:
    """Schedule and manage secret rotations"""
    
    def __init__(self, vault_client: VaultClient):
        self.vault_client = vault_client
        self.rotators = {}
        
    def register_rotator(self, name: str, rotator: SecretRotator, 
                        interval_days: int):
        """Register a secret rotator"""
        self.rotators[name] = {
            'rotator': rotator,
            'interval': timedelta(days=interval_days),
            'last_rotation': None
        }
    
    def check_and_rotate(self):
        """Check and rotate secrets as needed"""
        for name, config in self.rotators.items():
            try:
                # Check if rotation needed
                metadata = self.vault_client.get_secret(
                    f"ymip/rotation/{name}/metadata"
                )
                
                last_rotation = datetime.fromisoformat(
                    metadata.get('last_rotation', '2000-01-01')
                )
                
                if datetime.utcnow() - last_rotation > config['interval']:
                    logger.info(f"Rotating secret: {name}")
                    
                    if config['rotator'].rotate():
                        # Update metadata
                        self.vault_client.create_secret(
                            f"ymip/rotation/{name}/metadata",
                            {'last_rotation': datetime.utcnow().isoformat()}
                        )
                        logger.info(f"Successfully rotated: {name}")
                    else:
                        logger.error(f"Failed to rotate: {name}")
                        
            except Exception as e:
                logger.error(f"Error checking rotation for {name}: {e}")
```

## 🛡️ Secrets Scanning

### Pre-commit Hook
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: .*\.lock$
        
  - repo: https://github.com/trufflesecurity/trufflehog
    rev: v3.63.0
    hooks:
      - id: trufflehog
        entry: trufflehog filesystem .
        pass_filenames: false
        
  - repo: https://github.com/gitleaks/gitleaks
    rev: v8.18.0
    hooks:
      - id: gitleaks
```

### GitLeaks Configuration
```toml
# .gitleaks.toml
[extend]
useDefault = true

[[rules]]
id = "ymip-api-key"
description = "Yemen Market Integration API Key"
regex = '''ymip_[a-zA-Z0-9_-]{32,}'''
tags = ["api", "key"]

[[rules]]
id = "jwt-private-key"
description = "JWT Private Key"
regex = '''-----BEGIN RSA PRIVATE KEY-----'''
tags = ["key", "jwt"]

[[allowlist]]
description = "Allow test keys"
paths = [
    '''tests/fixtures/.*''',
    '''.*_test\.py'''
]

[[allowlist]]
description = "Allow example keys"
regexes = [
    '''ymip_example_.*''',
    '''ymip_test_.*'''
]
```

## 🔒 Environment-Specific Secrets

### Development Secrets
```python
# src/yemen_market/config/secrets_dev.py
"""Development secrets configuration"""
import os
from pathlib import Path

class DevSecretsManager:
    """Manage secrets for development environment"""
    
    def __init__(self):
        self.env_file = Path(".env.local")
        self.load_env_file()
    
    def load_env_file(self):
        """Load secrets from .env file"""
        if self.env_file.exists():
            with open(self.env_file) as f:
                for line in f:
                    if line.strip() and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value
    
    def get_secret(self, key: str, default: str = None) -> str:
        """Get secret from environment"""
        value = os.getenv(key, default)
        if value is None:
            raise ValueError(f"Secret not found: {key}")
        return value
    
    def get_database_url(self) -> str:
        """Get database connection URL"""
        return self.get_secret(
            'DATABASE_URL',
            'postgresql://ymip:devpassword@localhost:5432/yemen_market'
        )
    
    def get_redis_url(self) -> str:
        """Get Redis connection URL"""
        return self.get_secret(
            'REDIS_URL',
            'redis://localhost:6379/0'
        )
```

### Production Secrets
```python
# src/yemen_market/config/secrets_prod.py
"""Production secrets configuration"""
from typing import Dict, Any
import json

class ProdSecretsManager:
    """Manage secrets for production environment"""
    
    def __init__(self, vault_client: VaultClient):
        self.vault = vault_client
        self._cache = {}
        self._load_secrets()
    
    def _load_secrets(self):
        """Load all secrets from Vault"""
        paths = [
            'ymip/api/config',
            'ymip/database/credentials',
            'ymip/redis/credentials',
            'ymip/jwt/keys',
            'ymip/oauth/clients'
        ]
        
        for path in paths:
            try:
                self._cache[path] = self.vault.get_secret(path)
            except Exception as e:
                logger.error(f"Failed to load secret {path}: {e}")
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        creds = self._cache.get('ymip/database/credentials', {})
        return {
            'host': creds.get('host', 'postgres'),
            'port': creds.get('port', 5432),
            'database': creds.get('database', 'yemen_market'),
            'user': creds.get('username'),
            'password': creds.get('password'),
            'sslmode': 'require'
        }
    
    def get_jwt_keys(self) -> Dict[str, str]:
        """Get JWT signing keys"""
        return self._cache.get('ymip/jwt/keys', {})
    
    def get_api_config(self) -> Dict[str, Any]:
        """Get API configuration"""
        config = self._cache.get('ymip/api/config', {})
        return {
            'secret_key': config.get('secret_key'),
            'allowed_hosts': config.get('allowed_hosts', []),
            'cors_origins': config.get('cors_origins', []),
            'rate_limit': config.get('rate_limit', '100/hour')
        }
```

## 📊 Secrets Audit and Compliance

### Audit Logger
```python
# src/yemen_market/security/secrets_audit.py
class SecretsAuditLogger:
    """Audit logger for secrets access"""
    
    def __init__(self, audit_backend: str = "elasticsearch"):
        self.backend = audit_backend
        self.setup_backend()
    
    def log_secret_access(self, 
                         user: str,
                         secret_path: str,
                         action: str,
                         success: bool,
                         metadata: Dict[str, Any] = None):
        """Log secret access event"""
        event = {
            'timestamp': datetime.utcnow().isoformat(),
            'user': user,
            'secret_path': secret_path,
            'action': action,  # read, write, delete, rotate
            'success': success,
            'source_ip': metadata.get('ip_address'),
            'user_agent': metadata.get('user_agent'),
            'metadata': metadata
        }
        
        if self.backend == "elasticsearch":
            self.es_client.index(
                index=f"secrets-audit-{datetime.utcnow():%Y.%m}",
                body=event
            )
        elif self.backend == "file":
            with open(f"/var/log/ymip/secrets-audit.log", "a") as f:
                f.write(json.dumps(event) + "\n")
    
    def generate_compliance_report(self, 
                                 start_date: datetime,
                                 end_date: datetime) -> Dict[str, Any]:
        """Generate compliance report for secrets access"""
        # Query audit logs
        query = {
            "query": {
                "range": {
                    "timestamp": {
                        "gte": start_date.isoformat(),
                        "lte": end_date.isoformat()
                    }
                }
            },
            "aggs": {
                "by_user": {
                    "terms": {"field": "user.keyword"}
                },
                "by_secret": {
                    "terms": {"field": "secret_path.keyword"}
                },
                "by_action": {
                    "terms": {"field": "action.keyword"}
                },
                "failed_attempts": {
                    "filter": {"term": {"success": False}}
                }
            }
        }
        
        results = self.es_client.search(
            index="secrets-audit-*",
            body=query
        )
        
        return {
            'period': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat()
            },
            'total_accesses': results['hits']['total']['value'],
            'by_user': results['aggregations']['by_user']['buckets'],
            'by_secret': results['aggregations']['by_secret']['buckets'],
            'by_action': results['aggregations']['by_action']['buckets'],
            'failed_attempts': results['aggregations']['failed_attempts']['doc_count']
        }
```

## 🔧 Testing Secrets Management

### Secrets Testing
```python
# tests/test_secrets.py
import pytest
from unittest.mock import Mock, patch

class TestSecretsManagement:
    """Test secrets management functionality"""
    
    @pytest.fixture
    def mock_vault(self):
        """Mock Vault client"""
        mock = Mock()
        mock.get_secret.return_value = {
            'username': 'test_user',
            'password': 'test_password'
        }
        return mock
    
    def test_secret_rotation(self, mock_vault):
        """Test secret rotation"""
        rotator = DatabasePasswordRotator(
            db_config={'host': 'localhost', 'user': 'test'},
            vault_client=mock_vault
        )
        
        # Mock database update
        with patch('psycopg2.connect') as mock_connect:
            mock_conn = Mock()
            mock_connect.return_value = mock_conn
            
            # Test rotation
            new_password = rotator.generate_new_secret()
            assert len(new_password) == 32
            assert rotator.validate_secret(new_password)
    
    def test_secrets_scanning(self):
        """Test secrets scanning in code"""
        # This should be caught by pre-commit hooks
        test_content = '''
        api_key = "ymip_1234567890abcdef1234567890abcdef"
        password = "super_secret_password"
        '''
        
        # Run detection
        from detect_secrets import SecretsCollection
        from detect_secrets.settings import default_settings
        
        secrets = SecretsCollection()
        secrets.scan_string(test_content)
        
        assert len(secrets.data) > 0
```

## 🔐 Best Practices

### Secrets Management Checklist
```markdown
## Development
- [ ] Use `.env` files for local development
- [ ] Add `.env` to `.gitignore`
- [ ] Use different secrets for each environment
- [ ] Document required environment variables

## Storage
- [ ] Never commit secrets to version control
- [ ] Use proper secret stores (Vault, K8s Secrets)
- [ ] Encrypt secrets at rest
- [ ] Implement proper access controls

## Rotation
- [ ] Implement automated rotation
- [ ] Support graceful rotation (dual keys)
- [ ] Monitor rotation failures
- [ ] Document rotation procedures

## Access
- [ ] Use least privilege principle
- [ ] Implement audit logging
- [ ] Use short-lived credentials
- [ ] Require MFA for sensitive operations

## Monitoring
- [ ] Alert on unauthorized access
- [ ] Monitor for exposed secrets
- [ ] Regular compliance audits
- [ ] Track secret usage patterns
```

## 📚 Additional Resources

- [OWASP Secrets Management Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Secrets_Management_Cheat_Sheet.html)
- [HashiCorp Vault Best Practices](https://www.vaultproject.io/docs/internals/security)
- [Kubernetes Secrets Best Practices](https://kubernetes.io/docs/concepts/security/secrets-good-practices/)
- [NIST Key Management Guidelines](https://csrc.nist.gov/projects/key-management/key-management-guidelines)