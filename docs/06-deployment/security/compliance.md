# Data Compliance and Security Guide

## 🎯 Target Audience

- **Compliance Officers**: Ensuring regulatory compliance
- **Security Teams**: Implementing security controls
- **Data Engineers**: Handling sensitive data properly
- **Legal Teams**: Understanding data obligations

## 📋 Overview

This guide covers data compliance and security requirements for the Yemen Market Integration Platform, including handling of sensitive economic data, conflict information, and personal data in accordance with international regulations.

## 🏗️ Compliance Framework

```mermaid
graph TB
    subgraph "Regulatory Requirements"
        GDPR[GDPR - EU]
        CCPA[CCPA - California]
        UN[UN Data Protection]
        Local[Yemen Data Laws]
    end
    
    subgraph "Data Classification"
        Public[Public Data]
        Sensitive[Sensitive Economic]
        Personal[Personal Data]
        Conflict[Conflict Data]
    end
    
    subgraph "Security Controls"
        Encryption[Encryption]
        Access[Access Control]
        Audit[Audit Logging]
        DLP[Data Loss Prevention]
    end
    
    subgraph "Compliance Processes"
        Privacy[Privacy Assessment]
        Impact[Impact Assessment]
        Breach[Breach Response]
        Rights[Data Rights]
    end
    
    subgraph "Documentation"
        Policies[Security Policies]
        Records[Processing Records]
        Agreements[Data Agreements]
        Reports[Compliance Reports]
    end
    
    GDPR --> Privacy
    CCPA --> Rights
    UN --> Impact
    Local --> Policies
    
    Public --> Access
    Sensitive --> Encryption
    Personal --> DLP
    Conflict --> Audit
    
    Privacy --> Records
    Impact --> Reports
    Breach --> Agreements
    Rights --> Policies
```

## 📊 Data Classification

### Data Classification Policy
```python
# src/yemen_market/compliance/data_classification.py
from enum import Enum
from typing import Dict, List, Any
import json

class DataClassification(Enum):
    """Data classification levels"""
    PUBLIC = "public"                    # Publicly available data
    INTERNAL = "internal"                # Internal use only
    SENSITIVE = "sensitive"              # Sensitive economic data
    CONFIDENTIAL = "confidential"        # Confidential research data
    RESTRICTED = "restricted"            # Highly restricted (conflict zones)
    PERSONAL = "personal"                # Personal identifiable information

class DataClassifier:
    """Classify and tag data based on content"""
    
    def __init__(self):
        self.classification_rules = self._load_classification_rules()
        
    def _load_classification_rules(self) -> Dict[str, Any]:
        """Load data classification rules"""
        return {
            "market_prices": {
                "default": DataClassification.PUBLIC,
                "rules": [
                    {
                        "condition": {"source": "informal_markets"},
                        "classification": DataClassification.SENSITIVE
                    }
                ]
            },
            "conflict_data": {
                "default": DataClassification.RESTRICTED,
                "rules": [
                    {
                        "condition": {"aggregated": True, "location_precision": "governorate"},
                        "classification": DataClassification.SENSITIVE
                    }
                ]
            },
            "trader_surveys": {
                "default": DataClassification.CONFIDENTIAL,
                "rules": [
                    {
                        "condition": {"anonymized": True},
                        "classification": DataClassification.SENSITIVE
                    }
                ]
            },
            "user_data": {
                "default": DataClassification.PERSONAL,
                "rules": []
            }
        }
    
    def classify_data(self, data_type: str, metadata: Dict[str, Any]) -> DataClassification:
        """Classify data based on type and metadata"""
        if data_type not in self.classification_rules:
            return DataClassification.INTERNAL
        
        rules = self.classification_rules[data_type]
        classification = rules["default"]
        
        # Apply rules
        for rule in rules["rules"]:
            if self._matches_condition(metadata, rule["condition"]):
                classification = rule["classification"]
                break
        
        return classification
    
    def _matches_condition(self, metadata: Dict[str, Any], condition: Dict[str, Any]) -> bool:
        """Check if metadata matches condition"""
        for key, value in condition.items():
            if metadata.get(key) != value:
                return False
        return True
    
    def get_handling_requirements(self, classification: DataClassification) -> Dict[str, Any]:
        """Get data handling requirements for classification"""
        requirements = {
            DataClassification.PUBLIC: {
                "encryption_at_rest": False,
                "encryption_in_transit": True,
                "access_control": "basic",
                "audit_logging": False,
                "retention_days": 3650,  # 10 years
                "geographic_restrictions": []
            },
            DataClassification.INTERNAL: {
                "encryption_at_rest": True,
                "encryption_in_transit": True,
                "access_control": "role_based",
                "audit_logging": True,
                "retention_days": 2555,  # 7 years
                "geographic_restrictions": []
            },
            DataClassification.SENSITIVE: {
                "encryption_at_rest": True,
                "encryption_in_transit": True,
                "access_control": "attribute_based",
                "audit_logging": True,
                "retention_days": 2555,
                "geographic_restrictions": []
            },
            DataClassification.CONFIDENTIAL: {
                "encryption_at_rest": True,
                "encryption_in_transit": True,
                "access_control": "strict",
                "audit_logging": True,
                "retention_days": 1825,  # 5 years
                "geographic_restrictions": ["embargoed_countries"]
            },
            DataClassification.RESTRICTED: {
                "encryption_at_rest": True,
                "encryption_in_transit": True,
                "access_control": "need_to_know",
                "audit_logging": True,
                "retention_days": 1095,  # 3 years
                "geographic_restrictions": ["conflict_zones"]
            },
            DataClassification.PERSONAL: {
                "encryption_at_rest": True,
                "encryption_in_transit": True,
                "access_control": "privacy_enhanced",
                "audit_logging": True,
                "retention_days": 365,  # 1 year or as required
                "geographic_restrictions": [],
                "special_requirements": ["gdpr", "right_to_erasure"]
            }
        }
        
        return requirements.get(classification, requirements[DataClassification.INTERNAL])
```

## 🔐 Data Encryption

### Encryption Implementation
```python
# src/yemen_market/security/encryption.py
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
import base64
import os

class DataEncryption:
    """Handle data encryption for different classification levels"""
    
    def __init__(self, master_key: bytes = None):
        self.master_key = master_key or self._get_master_key()
        self.key_cache = {}
        
    def _get_master_key(self) -> bytes:
        """Get master encryption key from secure storage"""
        # In production, retrieve from HSM or key management service
        key_path = os.environ.get('YMIP_MASTER_KEY_PATH', '/secrets/master.key')
        if os.path.exists(key_path):
            with open(key_path, 'rb') as f:
                return f.read()
        else:
            # Generate for development only
            return Fernet.generate_key()
    
    def derive_key(self, purpose: str, salt: bytes = None) -> bytes:
        """Derive purpose-specific key from master key"""
        if purpose in self.key_cache:
            return self.key_cache[purpose]
        
        salt = salt or os.urandom(16)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.master_key + purpose.encode()))
        self.key_cache[purpose] = key
        return key
    
    def encrypt_field(self, 
                     data: str, 
                     classification: DataClassification,
                     field_name: str) -> Dict[str, str]:
        """Encrypt a single field based on classification"""
        if classification == DataClassification.PUBLIC:
            return {"value": data, "encrypted": False}
        
        # Use field-specific key for searchable encryption
        key = self.derive_key(f"{classification.value}:{field_name}")
        f = Fernet(key)
        
        encrypted = f.encrypt(data.encode())
        return {
            "value": base64.b64encode(encrypted).decode(),
            "encrypted": True,
            "algorithm": "fernet",
            "key_id": f"{classification.value}:{field_name}"
        }
    
    def decrypt_field(self, encrypted_data: Dict[str, str]) -> str:
        """Decrypt an encrypted field"""
        if not encrypted_data.get("encrypted"):
            return encrypted_data["value"]
        
        key = self.derive_key(encrypted_data["key_id"])
        f = Fernet(key)
        
        encrypted = base64.b64decode(encrypted_data["value"])
        decrypted = f.decrypt(encrypted)
        return decrypted.decode()
    
    def encrypt_file(self, 
                    file_path: str, 
                    classification: DataClassification) -> str:
        """Encrypt entire file"""
        output_path = f"{file_path}.encrypted"
        
        # Use AESGCM for file encryption
        key = self.derive_key(classification.value)[:32]  # 256-bit key
        aesgcm = AESGCM(key)
        
        with open(file_path, 'rb') as infile:
            plaintext = infile.read()
        
        # Generate nonce
        nonce = os.urandom(12)
        
        # Encrypt with associated data
        associated_data = json.dumps({
            "classification": classification.value,
            "original_name": os.path.basename(file_path),
            "encrypted_at": datetime.utcnow().isoformat()
        }).encode()
        
        ciphertext = aesgcm.encrypt(nonce, plaintext, associated_data)
        
        # Write encrypted file
        with open(output_path, 'wb') as outfile:
            outfile.write(nonce)
            outfile.write(len(associated_data).to_bytes(4, 'big'))
            outfile.write(associated_data)
            outfile.write(ciphertext)
        
        return output_path

class FieldLevelEncryption:
    """Field-level encryption for databases"""
    
    def __init__(self, encryption: DataEncryption):
        self.encryption = encryption
        
    def encrypt_document(self, 
                        document: Dict[str, Any],
                        schema: Dict[str, DataClassification]) -> Dict[str, Any]:
        """Encrypt document fields based on schema"""
        encrypted_doc = {}
        
        for field, value in document.items():
            if field in schema:
                classification = schema[field]
                if isinstance(value, str):
                    encrypted_doc[field] = self.encryption.encrypt_field(
                        value, classification, field
                    )
                elif isinstance(value, (int, float)):
                    # Convert to string for encryption
                    encrypted_doc[field] = self.encryption.encrypt_field(
                        str(value), classification, field
                    )
                else:
                    encrypted_doc[field] = value
            else:
                encrypted_doc[field] = value
        
        return encrypted_doc
```

## 🛡️ Access Control

### Attribute-Based Access Control
```python
# src/yemen_market/security/abac.py
from typing import Dict, Any, List, Set
import json

class ABACEngine:
    """Attribute-based access control for data compliance"""
    
    def __init__(self):
        self.policies = self._load_policies()
        
    def _load_policies(self) -> List[Dict[str, Any]]:
        """Load ABAC policies"""
        return [
            {
                "id": "gdpr_personal_data",
                "description": "GDPR compliant personal data access",
                "effect": "permit",
                "subject": {
                    "attributes": {
                        "role": ["researcher", "admin"],
                        "gdpr_trained": True,
                        "location": {"not_in": ["restricted_countries"]}
                    }
                },
                "resource": {
                    "attributes": {
                        "classification": "personal",
                        "purpose": ["research", "analysis"]
                    }
                },
                "action": ["read", "process"],
                "condition": {
                    "data_minimized": True,
                    "consent_obtained": True
                }
            },
            {
                "id": "conflict_data_access",
                "description": "Access to sensitive conflict data",
                "effect": "permit",
                "subject": {
                    "attributes": {
                        "clearance_level": ["secret", "top_secret"],
                        "organization": ["un", "approved_ngo"]
                    }
                },
                "resource": {
                    "attributes": {
                        "classification": "restricted",
                        "region": "yemen"
                    }
                },
                "action": ["read"],
                "condition": {
                    "aggregation_level": "governorate",
                    "time_delay_days": 30
                }
            }
        ]
    
    def evaluate_access(self,
                       subject: Dict[str, Any],
                       resource: Dict[str, Any],
                       action: str,
                       context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Evaluate access request against policies"""
        context = context or {}
        
        for policy in self.policies:
            if self._matches_policy(policy, subject, resource, action, context):
                if policy["effect"] == "permit":
                    return {
                        "decision": "permit",
                        "policy_id": policy["id"],
                        "obligations": self._get_obligations(policy, resource)
                    }
        
        return {
            "decision": "deny",
            "reason": "No matching permit policy"
        }
    
    def _matches_policy(self,
                       policy: Dict[str, Any],
                       subject: Dict[str, Any],
                       resource: Dict[str, Any],
                       action: str,
                       context: Dict[str, Any]) -> bool:
        """Check if request matches policy"""
        # Check action
        if action not in policy.get("action", []):
            return False
        
        # Check subject attributes
        if not self._matches_attributes(
            subject.get("attributes", {}),
            policy.get("subject", {}).get("attributes", {})
        ):
            return False
        
        # Check resource attributes
        if not self._matches_attributes(
            resource.get("attributes", {}),
            policy.get("resource", {}).get("attributes", {})
        ):
            return False
        
        # Check conditions
        if not self._matches_conditions(
            context,
            policy.get("condition", {})
        ):
            return False
        
        return True
    
    def _matches_attributes(self, 
                          actual: Dict[str, Any],
                          required: Dict[str, Any]) -> bool:
        """Check if actual attributes match required"""
        for key, value in required.items():
            if key not in actual:
                return False
            
            if isinstance(value, list):
                if actual[key] not in value:
                    return False
            elif isinstance(value, dict):
                if "not_in" in value:
                    if actual[key] in value["not_in"]:
                        return False
            else:
                if actual[key] != value:
                    return False
        
        return True
    
    def _get_obligations(self,
                        policy: Dict[str, Any],
                        resource: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get obligations for permitted access"""
        obligations = []
        
        # Add audit logging obligation
        obligations.append({
            "type": "audit_log",
            "details": {
                "policy_id": policy["id"],
                "resource_id": resource.get("id"),
                "classification": resource.get("attributes", {}).get("classification")
            }
        })
        
        # Add data handling obligations
        classification = resource.get("attributes", {}).get("classification")
        if classification == "personal":
            obligations.extend([
                {"type": "minimize_data"},
                {"type": "pseudonymize"},
                {"type": "limit_retention", "days": 365}
            ])
        elif classification == "restricted":
            obligations.extend([
                {"type": "encrypt_at_rest"},
                {"type": "secure_deletion"},
                {"type": "access_notification"}
            ])
        
        return obligations
```

## 📋 GDPR Compliance

### Data Subject Rights
```python
# src/yemen_market/compliance/gdpr.py
from typing import Dict, Any, List, Optional
import asyncio
from datetime import datetime, timedelta

class GDPRComplianceManager:
    """Manage GDPR compliance requirements"""
    
    def __init__(self, db_client, storage_client):
        self.db = db_client
        self.storage = storage_client
        
    async def handle_access_request(self, user_id: str) -> Dict[str, Any]:
        """Handle data subject access request (DSAR)"""
        logger.info(f"Processing GDPR access request for user: {user_id}")
        
        # Collect all user data
        user_data = {}
        
        # User profile
        user_data["profile"] = await self.db.users.find_one({"_id": user_id})
        
        # Activity logs
        user_data["activity_logs"] = await self.db.activity_logs.find(
            {"user_id": user_id}
        ).to_list(None)
        
        # Analysis results
        user_data["analysis_results"] = await self.db.analysis_results.find(
            {"created_by": user_id}
        ).to_list(None)
        
        # API usage
        user_data["api_usage"] = await self.db.api_logs.find(
            {"user_id": user_id}
        ).to_list(None)
        
        # Generate report
        report_path = await self._generate_data_report(user_id, user_data)
        
        # Log request
        await self._log_gdpr_request(user_id, "access", "completed")
        
        return {
            "request_id": f"DSAR-{user_id}-{datetime.utcnow().timestamp()}",
            "status": "completed",
            "report_url": report_path,
            "expires_at": datetime.utcnow() + timedelta(days=30)
        }
    
    async def handle_deletion_request(self, user_id: str) -> Dict[str, Any]:
        """Handle right to erasure request"""
        logger.info(f"Processing GDPR deletion request for user: {user_id}")
        
        # Check for legal obligations to retain data
        retention_check = await self._check_retention_obligations(user_id)
        if retention_check["must_retain"]:
            return {
                "status": "partially_completed",
                "reason": retention_check["reason"],
                "retained_data": retention_check["categories"]
            }
        
        # Delete personal data
        deletion_results = []
        
        # User profile (pseudonymize instead of delete)
        result = await self.db.users.update_one(
            {"_id": user_id},
            {
                "$set": {
                    "email": f"deleted-{user_id}@example.com",
                    "name": "Deleted User",
                    "deleted_at": datetime.utcnow(),
                    "deletion_reason": "gdpr_request"
                },
                "$unset": {
                    "phone": "",
                    "address": "",
                    "personal_details": ""
                }
            }
        )
        deletion_results.append(("users", result.modified_count))
        
        # Activity logs (anonymize)
        result = await self.db.activity_logs.update_many(
            {"user_id": user_id},
            {"$set": {"user_id": f"anonymous-{user_id}", "ip_address": "0.0.0.0"}}
        )
        deletion_results.append(("activity_logs", result.modified_count))
        
        # Log deletion
        await self._log_gdpr_request(user_id, "deletion", "completed")
        
        return {
            "request_id": f"DEL-{user_id}-{datetime.utcnow().timestamp()}",
            "status": "completed",
            "deleted_records": deletion_results,
            "completed_at": datetime.utcnow()
        }
    
    async def handle_portability_request(self, user_id: str) -> Dict[str, Any]:
        """Handle data portability request"""
        logger.info(f"Processing GDPR portability request for user: {user_id}")
        
        # Collect portable data
        portable_data = {
            "profile": await self.db.users.find_one(
                {"_id": user_id},
                {"password": 0, "security_questions": 0}  # Exclude security data
            ),
            "saved_analyses": await self.db.saved_analyses.find(
                {"user_id": user_id}
            ).to_list(None),
            "preferences": await self.db.user_preferences.find_one(
                {"user_id": user_id}
            )
        }
        
        # Convert to standard format
        export_path = await self._export_portable_data(user_id, portable_data)
        
        # Log request
        await self._log_gdpr_request(user_id, "portability", "completed")
        
        return {
            "request_id": f"PORT-{user_id}-{datetime.utcnow().timestamp()}",
            "status": "completed",
            "format": "json",
            "download_url": export_path,
            "expires_at": datetime.utcnow() + timedelta(days=7)
        }
    
    async def _check_retention_obligations(self, user_id: str) -> Dict[str, Any]:
        """Check if data must be retained for legal reasons"""
        obligations = {
            "must_retain": False,
            "reason": None,
            "categories": []
        }
        
        # Check for ongoing investigations
        investigations = await self.db.investigations.count_documents({
            "subject_id": user_id,
            "status": "active"
        })
        
        if investigations > 0:
            obligations["must_retain"] = True
            obligations["reason"] = "ongoing_investigation"
            obligations["categories"].append("activity_logs")
        
        # Check for financial records
        financial_records = await self.db.transactions.count_documents({
            "user_id": user_id,
            "created_at": {"$gte": datetime.utcnow() - timedelta(days=2555)}  # 7 years
        })
        
        if financial_records > 0:
            obligations["must_retain"] = True
            obligations["reason"] = "financial_retention_requirement"
            obligations["categories"].append("transaction_records")
        
        return obligations
    
    async def _log_gdpr_request(self, user_id: str, request_type: str, status: str):
        """Log GDPR request for compliance"""
        await self.db.gdpr_requests.insert_one({
            "user_id": user_id,
            "request_type": request_type,
            "status": status,
            "requested_at": datetime.utcnow(),
            "completed_at": datetime.utcnow() if status == "completed" else None
        })

class ConsentManager:
    """Manage user consent for data processing"""
    
    def __init__(self, db_client):
        self.db = db_client
        
    async def record_consent(self,
                           user_id: str,
                           purpose: str,
                           granted: bool,
                           details: Dict[str, Any] = None) -> str:
        """Record user consent"""
        consent_record = {
            "user_id": user_id,
            "purpose": purpose,
            "granted": granted,
            "timestamp": datetime.utcnow(),
            "ip_address": details.get("ip_address"),
            "user_agent": details.get("user_agent"),
            "version": details.get("consent_version", "1.0"),
            "expires_at": datetime.utcnow() + timedelta(days=365) if granted else None
        }
        
        result = await self.db.consent_records.insert_one(consent_record)
        return str(result.inserted_id)
    
    async def check_consent(self, user_id: str, purpose: str) -> bool:
        """Check if user has given consent for purpose"""
        consent = await self.db.consent_records.find_one(
            {
                "user_id": user_id,
                "purpose": purpose,
                "granted": True,
                "expires_at": {"$gt": datetime.utcnow()}
            },
            sort=[("timestamp", -1)]
        )
        
        return consent is not None
    
    async def get_consent_history(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's consent history"""
        return await self.db.consent_records.find(
            {"user_id": user_id}
        ).sort("timestamp", -1).to_list(None)
```

## 🌍 Geographic Restrictions

### Data Residency Controls
```python
# src/yemen_market/compliance/data_residency.py
class DataResidencyManager:
    """Manage data residency and geographic restrictions"""
    
    def __init__(self):
        self.regions = self._load_region_config()
        self.restrictions = self._load_restrictions()
        
    def _load_region_config(self) -> Dict[str, Any]:
        """Load region configuration"""
        return {
            "eu": {
                "countries": ["DE", "FR", "IT", "ES", "NL"],  # etc.
                "regulations": ["gdpr"],
                "storage_locations": ["eu-west-1", "eu-central-1"],
                "allowed_transfers": ["us"] # with appropriate safeguards
            },
            "us": {
                "countries": ["US"],
                "regulations": ["ccpa", "hipaa"],
                "storage_locations": ["us-east-1", "us-west-2"],
                "allowed_transfers": ["eu", "ca"]
            },
            "conflict_zones": {
                "countries": ["YE"],
                "regulations": ["un_sanctions", "local_laws"],
                "storage_locations": ["isolated-region"],
                "allowed_transfers": []  # No transfers allowed
            }
        }
    
    def _load_restrictions(self) -> Dict[str, List[str]]:
        """Load data transfer restrictions"""
        return {
            "embargoed_countries": ["IR", "KP", "SY"],  # Example
            "restricted_entities": ["entity1", "entity2"],
            "sanctioned_individuals": []  # Would be loaded from sanctions list
        }
    
    def check_transfer_allowed(self,
                             data_classification: str,
                             source_region: str,
                             destination_region: str,
                             user_location: str) -> Dict[str, Any]:
        """Check if data transfer is allowed"""
        # Check embargo
        if destination_region in self.restrictions["embargoed_countries"]:
            return {
                "allowed": False,
                "reason": "destination_embargoed",
                "regulations": ["export_control"]
            }
        
        # Check data classification restrictions
        if data_classification in ["restricted", "confidential"]:
            source_config = self.regions.get(source_region, {})
            if destination_region not in source_config.get("allowed_transfers", []):
                return {
                    "allowed": False,
                    "reason": "classification_restriction",
                    "regulations": source_config.get("regulations", [])
                }
        
        # Check user location
        if user_location in self.restrictions["embargoed_countries"]:
            return {
                "allowed": False,
                "reason": "user_location_restricted",
                "regulations": ["sanctions"]
            }
        
        # Check for adequate protection
        if not self._has_adequate_protection(source_region, destination_region):
            return {
                "allowed": False,
                "reason": "inadequate_protection",
                "required_safeguards": ["scc", "bcr", "adequacy_decision"]
            }
        
        return {
            "allowed": True,
            "conditions": self._get_transfer_conditions(
                data_classification,
                source_region,
                destination_region
            )
        }
    
    def _has_adequate_protection(self, source: str, destination: str) -> bool:
        """Check if destination has adequate data protection"""
        adequate_countries = {
            "eu": ["US", "CA", "JP", "NZ", "IL"],  # Example adequacy decisions
            "us": ["EU", "CA", "UK", "AU"]
        }
        
        return destination in adequate_countries.get(source, [])
    
    def get_storage_location(self,
                           data_classification: str,
                           user_region: str) -> str:
        """Determine appropriate storage location"""
        region_config = self.regions.get(user_region, self.regions["us"])
        
        # For restricted data, use isolated storage
        if data_classification == "restricted":
            return "isolated-region"
        
        # Use primary region storage
        return region_config["storage_locations"][0]
```

## 📊 Compliance Reporting

### Compliance Dashboard
```python
# src/yemen_market/compliance/reporting.py
class ComplianceReporter:
    """Generate compliance reports and metrics"""
    
    def __init__(self, db_client, audit_logger):
        self.db = db_client
        self.audit = audit_logger
        
    async def generate_compliance_report(self,
                                       start_date: datetime,
                                       end_date: datetime,
                                       report_type: str = "full") -> Dict[str, Any]:
        """Generate comprehensive compliance report"""
        report = {
            "period": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat()
            },
            "generated_at": datetime.utcnow().isoformat(),
            "report_type": report_type
        }
        
        # Data protection metrics
        report["data_protection"] = await self._get_data_protection_metrics(
            start_date, end_date
        )
        
        # Access control metrics
        report["access_control"] = await self._get_access_control_metrics(
            start_date, end_date
        )
        
        # GDPR compliance
        report["gdpr_compliance"] = await self._get_gdpr_metrics(
            start_date, end_date
        )
        
        # Security incidents
        report["security_incidents"] = await self._get_security_incidents(
            start_date, end_date
        )
        
        # Data classification
        report["data_classification"] = await self._get_classification_metrics()
        
        # Audit summary
        report["audit_summary"] = await self._get_audit_summary(
            start_date, end_date
        )
        
        return report
    
    async def _get_data_protection_metrics(self,
                                         start_date: datetime,
                                         end_date: datetime) -> Dict[str, Any]:
        """Get data protection metrics"""
        return {
            "encrypted_at_rest": {
                "percentage": 98.5,
                "total_records": 1_500_000,
                "encrypted_records": 1_477_500
            },
            "encrypted_in_transit": {
                "percentage": 100.0,
                "total_connections": 45_000,
                "tls_connections": 45_000
            },
            "data_minimization": {
                "fields_removed": 234,
                "records_anonymized": 12_000,
                "retention_policy_applied": 89_000
            }
        }
    
    async def _get_access_control_metrics(self,
                                        start_date: datetime,
                                        end_date: datetime) -> Dict[str, Any]:
        """Get access control metrics"""
        # Query access logs
        total_requests = await self.db.access_logs.count_documents({
            "timestamp": {"$gte": start_date, "$lte": end_date}
        })
        
        denied_requests = await self.db.access_logs.count_documents({
            "timestamp": {"$gte": start_date, "$lte": end_date},
            "decision": "deny"
        })
        
        # Aggregate by classification
        classification_access = await self.db.access_logs.aggregate([
            {
                "$match": {
                    "timestamp": {"$gte": start_date, "$lte": end_date}
                }
            },
            {
                "$group": {
                    "_id": "$resource_classification",
                    "count": {"$sum": 1},
                    "denied": {
                        "$sum": {"$cond": [{"$eq": ["$decision", "deny"]}, 1, 0]}
                    }
                }
            }
        ]).to_list(None)
        
        return {
            "total_access_requests": total_requests,
            "denied_requests": denied_requests,
            "denial_rate": (denied_requests / total_requests * 100) if total_requests > 0 else 0,
            "by_classification": classification_access,
            "mfa_usage_rate": 87.3,  # Example
            "privileged_access_reviews": 42
        }
    
    async def _get_gdpr_metrics(self,
                               start_date: datetime,
                               end_date: datetime) -> Dict[str, Any]:
        """Get GDPR compliance metrics"""
        # Data subject requests
        dsar_requests = await self.db.gdpr_requests.aggregate([
            {
                "$match": {
                    "requested_at": {"$gte": start_date, "$lte": end_date}
                }
            },
            {
                "$group": {
                    "_id": "$request_type",
                    "count": {"$sum": 1},
                    "completed": {
                        "$sum": {"$cond": [{"$eq": ["$status", "completed"]}, 1, 0]}
                    },
                    "avg_completion_time": {
                        "$avg": {
                            "$subtract": ["$completed_at", "$requested_at"]
                        }
                    }
                }
            }
        ]).to_list(None)
        
        # Consent metrics
        active_consents = await self.db.consent_records.count_documents({
            "granted": True,
            "expires_at": {"$gt": datetime.utcnow()}
        })
        
        return {
            "data_subject_requests": dsar_requests,
            "active_consents": active_consents,
            "consent_withdrawal_rate": 2.3,  # Example percentage
            "data_breach_notifications": 0,
            "privacy_impact_assessments": 5,
            "cross_border_transfers": 128
        }
```

## 🔍 Audit and Monitoring

### Compliance Monitoring
```python
# src/yemen_market/compliance/monitoring.py
class ComplianceMonitor:
    """Monitor compliance in real-time"""
    
    def __init__(self, alert_manager):
        self.alerts = alert_manager
        self.monitors = self._setup_monitors()
        
    def _setup_monitors(self) -> Dict[str, Any]:
        """Setup compliance monitors"""
        return {
            "unauthorized_access": {
                "threshold": 5,
                "window": "5m",
                "severity": "high"
            },
            "excessive_data_export": {
                "threshold": 1000,
                "window": "1h",
                "severity": "medium"
            },
            "retention_violation": {
                "threshold": 0,
                "window": "1d",
                "severity": "high"
            },
            "encryption_failure": {
                "threshold": 0,
                "window": "1m",
                "severity": "critical"
            }
        }
    
    async def check_compliance_violations(self):
        """Check for compliance violations"""
        violations = []
        
        # Check unauthorized access attempts
        unauthorized = await self._check_unauthorized_access()
        if unauthorized:
            violations.extend(unauthorized)
        
        # Check data retention violations
        retention = await self._check_retention_violations()
        if retention:
            violations.extend(retention)
        
        # Check encryption compliance
        encryption = await self._check_encryption_compliance()
        if encryption:
            violations.extend(encryption)
        
        # Send alerts for violations
        for violation in violations:
            await self.alerts.send_compliance_alert(violation)
        
        return violations
    
    async def _check_unauthorized_access(self) -> List[Dict[str, Any]]:
        """Check for unauthorized access attempts"""
        # Implementation would query logs and detect patterns
        return []
    
    async def _check_retention_violations(self) -> List[Dict[str, Any]]:
        """Check for data retention policy violations"""
        # Implementation would check data ages against policies
        return []
    
    async def _check_encryption_compliance(self) -> List[Dict[str, Any]]:
        """Check encryption compliance"""
        # Implementation would verify encryption status
        return []
```

## 🛡️ Security Controls

### Data Loss Prevention
```python
# src/yemen_market/security/dlp.py
class DataLossPreventionEngine:
    """Prevent unauthorized data exfiltration"""
    
    def __init__(self):
        self.patterns = self._load_dlp_patterns()
        self.policies = self._load_dlp_policies()
        
    def _load_dlp_patterns(self) -> Dict[str, Any]:
        """Load DLP detection patterns"""
        return {
            "personal_data": {
                "email": r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
                "phone": r'\+?[1-9]\d{1,14}',
                "national_id": r'[0-9]{10}',  # Yemen national ID format
            },
            "sensitive_locations": {
                "coordinates": r'-?\d+\.\d+,\s*-?\d+\.\d+',
                "grid_reference": r'[A-Z]{2}\d{4,}'
            },
            "financial": {
                "bank_account": r'[0-9]{10,20}',
                "credit_card": r'\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}'
            }
        }
    
    def scan_for_sensitive_data(self, content: str) -> List[Dict[str, Any]]:
        """Scan content for sensitive data"""
        findings = []
        
        for category, patterns in self.patterns.items():
            for pattern_name, regex in patterns.items():
                matches = re.finditer(regex, content)
                for match in matches:
                    findings.append({
                        "category": category,
                        "type": pattern_name,
                        "location": match.span(),
                        "severity": self._get_severity(category)
                    })
        
        return findings
    
    def apply_dlp_policy(self,
                        data: Any,
                        classification: str,
                        destination: str) -> Dict[str, Any]:
        """Apply DLP policy to data transfer"""
        policy = self.policies.get(classification, {})
        
        # Check if transfer is allowed
        if destination in policy.get("blocked_destinations", []):
            return {
                "allowed": False,
                "reason": "destination_blocked",
                "policy": classification
            }
        
        # Check data volume
        if hasattr(data, '__len__'):
            max_records = policy.get("max_records", float('inf'))
            if len(data) > max_records:
                return {
                    "allowed": False,
                    "reason": "volume_exceeded",
                    "limit": max_records
                }
        
        # Apply transformations
        if policy.get("require_anonymization"):
            data = self._anonymize_data(data)
        
        if policy.get("require_aggregation"):
            data = self._aggregate_data(data)
        
        return {
            "allowed": True,
            "transformed_data": data,
            "applied_policies": policy
        }
```

## 📚 Compliance Documentation

### Policy Templates
```markdown
# Data Protection Policy Template

## Purpose
Define how Yemen Market Integration Platform handles and protects data.

## Scope
All data collected, processed, and stored by the platform.

## Data Classification
- Public: Market prices from public sources
- Internal: Aggregated analysis results
- Sensitive: Detailed conflict data
- Confidential: Research methodologies
- Restricted: Real-time conflict zone data
- Personal: User information and activity

## Handling Requirements
### Encryption
- All sensitive data encrypted at rest (AES-256)
- All data encrypted in transit (TLS 1.3)

### Access Control
- Role-based access control (RBAC)
- Attribute-based access for sensitive data
- Multi-factor authentication required

### Retention
- Public data: 10 years
- Analysis results: 7 years
- Personal data: 1 year or as required
- Audit logs: 3 years

## Compliance Requirements
- GDPR (European users)
- CCPA (California users)
- UN data protection standards
- Local Yemen regulations
```

## 🔧 Testing Compliance

### Compliance Test Suite
```python
# tests/test_compliance.py
import pytest
from datetime import datetime, timedelta

class TestCompliance:
    """Test compliance functionality"""
    
    @pytest.fixture
    def compliance_manager(self):
        return GDPRComplianceManager(mock_db, mock_storage)
    
    async def test_data_subject_access_request(self, compliance_manager):
        """Test DSAR handling"""
        user_id = "test_user_123"
        
        # Create test data
        await create_test_user_data(user_id)
        
        # Process DSAR
        result = await compliance_manager.handle_access_request(user_id)
        
        assert result["status"] == "completed"
        assert "report_url" in result
        assert result["expires_at"] > datetime.utcnow()
    
    async def test_data_encryption(self):
        """Test data encryption compliance"""
        encryption = DataEncryption()
        
        # Test field encryption
        sensitive_data = "<EMAIL>"
        encrypted = encryption.encrypt_field(
            sensitive_data,
            DataClassification.PERSONAL,
            "email"
        )
        
        assert encrypted["encrypted"] is True
        assert encrypted["value"] != sensitive_data
        
        # Test decryption
        decrypted = encryption.decrypt_field(encrypted)
        assert decrypted == sensitive_data
    
    async def test_geographic_restrictions(self):
        """Test data residency controls"""
        residency = DataResidencyManager()
        
        # Test blocked transfer
        result = residency.check_transfer_allowed(
            data_classification="restricted",
            source_region="eu",
            destination_region="IR",  # Embargoed
            user_location="US"
        )
        
        assert result["allowed"] is False
        assert result["reason"] == "destination_embargoed"
```

## 📚 Additional Resources

- [GDPR Compliance Checklist](https://gdpr.eu/checklist/)
- [ISO 27001 Information Security](https://www.iso.org/isoiec-27001-information-security.html)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [UN Data Strategy](https://www.un.org/en/content/datastrategy/)