# 🗺️ Documentation Map

This guide helps you navigate the Yemen Market Integration documentation efficiently.

## 📢 Migration Notice (January 2025)

The documentation has been reorganized into a numbered structure for better organization and discoverability. Key changes:
- All documentation now follows a numbered structure (00-10)
- Deprecated directories have been removed or consolidated
- New sections added for deployment, case studies, results, and troubleshooting
- Cross-references updated throughout

## 🎯 Quick Navigation by Role

### For Researchers/Academics

1. Start: [`00-getting-started/README.md`](00-getting-started/README.md) - Quick introduction
2. Methodology: [`05-methodology/`](05-methodology/) - Research approaches
3. Results: [`08-results/`](08-results/) - Analysis outputs and findings
4. Case Studies: [`07-case-studies/`](07-case-studies/) - Real-world applications

### For Developers

1. Start: [`10-contributing/CONTRIBUTING.md`](10-contributing/CONTRIBUTING.md) - How to contribute
2. Architecture: [`01-architecture/`](01-architecture/) - System design
3. API: [`03-api-reference/`](03-api-reference/) - Complete API documentation
4. Development: [`04-development/`](04-development/) - Development guides

### For Policy Makers

1. Overview: [`00-getting-started/README.md`](00-getting-started/README.md) - Project overview
2. Methods: [`05-methodology/`](05-methodology/) - Non-technical summaries
3. Results: [`08-results/`](08-results/) - Key findings and implications
4. Case Studies: [`07-case-studies/`](07-case-studies/) - Practical applications

### For Operations Teams

1. Deployment: [`06-deployment/`](06-deployment/) - Deployment guides
2. Troubleshooting: [`09-troubleshooting/`](09-troubleshooting/) - Common issues
3. User Guides: [`02-user-guides/`](02-user-guides/) - Operating procedures

## 📚 Documentation Structure (Updated Jan 30, 2025)

```
yemen-market-integration/
│
├── 📋 Root Documentation
│   ├── README.md ..................... Project overview, installation
│   ├── METHODOLOGY.md ................ Three-tier methodology overview
│   ├── CLAUDE.md .................... Development rules and standards
│   └── CONTRIBUTING.md .............. Contribution guidelines
│
├── 📖 docs/
│   ├── DOCUMENTATION_MAP.md ......... THIS FILE - Navigation guide
│   │
│   ├── 00-getting-started/ .......... 🆕 Quick start and introduction
│   │   ├── README.md ................ Getting started overview
│   │   ├── installation.md .......... Installation guide
│   │   ├── quick-start.md ........... Quick start tutorial
│   │   └── first-analysis.md ........ Running your first analysis
│   │
│   ├── 01-architecture/ ............. 🔄 System design and structure
│   │   ├── README.md ................ Architecture overview
│   │   ├── overview.md .............. High-level system design
│   │   ├── components.md ............ Component descriptions
│   │   ├── data-flow.md ............. Data flow diagrams
│   │   └── security.md .............. Security considerations
│   │
│   ├── 02-user-guides/ .............. 📘 How-to guides for users
│   │   ├── README.md ................ User guide overview
│   │   ├── data-pipeline.md ......... Using the data pipeline
│   │   ├── running-analyses.md ...... Running analyses
│   │   ├── interpreting-results.md .. Understanding outputs
│   │   └── troubleshooting.md ....... Common issues
│   │
│   ├── 03-api-reference/ ............ 🔧 Complete API documentation
│   │   ├── README.md ................ API overview
│   │   ├── data/ .................... Data processing APIs
│   │   ├── features/ ................ Feature engineering APIs
│   │   ├── models/ .................. Model APIs
│   │   └── utils/ ................... Utility APIs
│   │
│   ├── 04-development/ .............. 👩‍💻 Developer resources
│   │   ├── README.md ................ Developer guide
│   │   ├── setup.md ................. Development environment
│   │   ├── testing.md ............... Testing guidelines
│   │   └── best-practices.md ........ Coding standards
│   │
│   ├── 05-methodology/ .............. 📊 Research methodology
│   │   ├── README.md ................ Methodology overview
│   │   ├── three-tier-approach.md ... Three-tier model details
│   │   ├── world-bank-standards.md .. World Bank methodology
│   │   └── econometric-models.md .... Model specifications
│   │
│   ├── 06-deployment/ ............... 🚀 Deployment and operations
│   │   ├── README.md ................ Deployment overview
│   │   ├── production-guide.md ...... Production deployment
│   │   ├── docker.md ................ Docker deployment
│   │   └── monitoring.md ............ Monitoring setup
│   │
│   ├── 07-case-studies/ ............. 📚 Real-world applications
│   │   ├── README.md ................ Case study overview
│   │   └── yemen-conflict.md ........ Yemen conflict analysis
│   │
│   ├── 08-results/ .................. 📈 Analysis results
│   │   ├── README.md ................ Results overview
│   │   ├── executive-summary.md ..... Key findings
│   │   └── detailed-analysis.md ..... Full analysis results
│   │
│   ├── 09-troubleshooting/ .......... 🔍 Problem solving
│   │   ├── README.md ................ Troubleshooting guide
│   │   ├── common-errors.md ......... Error solutions
│   │   └── faq.md ................... Frequently asked questions
│   │
│   └── 10-contributing/ ............. 🤝 Contribution guidelines
│       ├── CONTRIBUTING.md .......... How to contribute
│       ├── code-of-conduct.md ....... Community standards
│       └── style-guide.md ........... Documentation style
│
├── 🤖 .claude/
│   ├── README.md .................... Claude directory guide
│   ├── ACTIVE_CONTEXT.md ............ Current development status
│   ├── project_memory.md ............ Technical decisions
│   ├── methodology_notes.md ......... Key methodology decisions
│   ├── commands/ .................... Custom slash commands
│   └── prompts/ ..................... Reusable prompts
│
└── 📊 reports/
    ├── EXECUTIVE_SUMMARY_CONSOLIDATED.md Executive summary
    ├── progress/
    │   └── README.md ................ Progress dashboard
    └── archive/ ..................... Historical reports
```

### 🔄 Migration from Old Structure

**Deprecated Directories** (content moved to numbered structure):
- `api/` → `03-api-reference/`
- `guides/` → `02-user-guides/`
- `methodology/` → `05-methodology/`
- `models/` → `05-methodology/` and `03-api-reference/models/`
- `implementation/` → `04-development/` and `08-results/`
- `testing/` → `04-development/testing.md`
- `data/` → `02-user-guides/data-pipeline.md`

## 🔍 Finding Information

### By Topic

| Topic | Primary Doc | Detailed Doc | Code Example |
|-------|------------|--------------|--------------|
| **Getting Started** | [`00-getting-started/`](00-getting-started/) | [`quick-start.md`](00-getting-started/quick-start.md) | [`first-analysis.md`](00-getting-started/first-analysis.md) |
| **Three-Tier Method** | [`05-methodology/three-tier-approach.md`](05-methodology/three-tier-approach.md) | [`03-api-reference/models/`](03-api-reference/models/) | [`enhanced_analysis_pipeline.py`](../scripts/analysis/enhanced_analysis_pipeline.py) |
| **World Bank Approach** | [`05-methodology/world-bank-standards.md`](05-methodology/world-bank-standards.md) | [`08-results/`](08-results/) | [`data_preparation.py`](../src/yemen_market/features/data_preparation.py) |
| **Data Pipeline** | [`02-user-guides/data-pipeline.md`](02-user-guides/data-pipeline.md) | [`03-api-reference/data/`](03-api-reference/data/) | [`prepare_data_for_modeling.py`](../scripts/analysis/prepare_data_for_modeling.py) |
| **Deployment** | [`06-deployment/`](06-deployment/) | [`production-guide.md`](06-deployment/production-guide.md) | Docker/K8s configs |
| **Troubleshooting** | [`09-troubleshooting/`](09-troubleshooting/) | [`common-errors.md`](09-troubleshooting/common-errors.md) | [`faq.md`](09-troubleshooting/faq.md) |
| **API Reference** | [`03-api-reference/`](03-api-reference/) | Module-specific docs | Source code |
| **Development** | [`04-development/`](04-development/) | [`10-contributing/CONTRIBUTING.md`](10-contributing/CONTRIBUTING.md) | [`best-practices.md`](04-development/best-practices.md) |

### By Question

**"How do I get started?"**
→ [`00-getting-started/README.md`](00-getting-started/README.md)

**"How do I install the system?"**
→ [`00-getting-started/installation.md`](00-getting-started/installation.md)

**"How do I run my first analysis?"**
→ [`00-getting-started/first-analysis.md`](00-getting-started/first-analysis.md)

**"What's the system architecture?"**
→ [`01-architecture/overview.md`](01-architecture/overview.md)

**"How do I use the data pipeline?"**
→ [`02-user-guides/data-pipeline.md`](02-user-guides/data-pipeline.md)

**"Where's the API documentation?"**
→ [`03-api-reference/README.md`](03-api-reference/README.md)

**"How do I set up my development environment?"**
→ [`04-development/setup.md`](04-development/setup.md)

**"What's the research methodology?"**
→ [`05-methodology/README.md`](05-methodology/README.md)

**"How do I deploy to production?"**
→ [`06-deployment/production-guide.md`](06-deployment/production-guide.md)

**"Where are the case studies?"**
→ [`07-case-studies/README.md`](07-case-studies/README.md)

**"What are the analysis results?"**
→ [`08-results/executive-summary.md`](08-results/executive-summary.md)

**"I'm having issues, where do I look?"**
→ [`09-troubleshooting/README.md`](09-troubleshooting/README.md)

**"How can I contribute?"**
→ [`10-contributing/CONTRIBUTING.md`](10-contributing/CONTRIBUTING.md)

## 🌟 Key Documents

### Must Read (Top 5)

1. [`00-getting-started/README.md`](00-getting-started/README.md) - Start here
2. [`01-architecture/overview.md`](01-architecture/overview.md) - System design
3. [`05-methodology/three-tier-approach.md`](05-methodology/three-tier-approach.md) - Core methodology
4. [`08-results/executive-summary.md`](08-results/executive-summary.md) - Key findings
5. [`02-user-guides/running-analyses.md`](02-user-guides/running-analyses.md) - How to run analyses

### For Implementation

1. [`scripts/analysis/enhanced_analysis_pipeline.py`](../scripts/analysis/enhanced_analysis_pipeline.py) - Main analysis pipeline
2. [`scripts/analysis/run_three_tier_models.py`](../scripts/analysis/run_three_tier_models.py) - Three-tier runner
3. [`03-api-reference/models/`](03-api-reference/models/) - Model API documentation

### For Data Work

1. [`02-user-guides/data-pipeline.md`](02-user-guides/data-pipeline.md) - Data processing guide
2. [`02-user-guides/balanced-panel-creation.md`](02-user-guides/balanced-panel-creation.md) - Creating balanced panels
3. [`scripts/analysis/create_integrated_balanced_panel.py`](../scripts/analysis/create_integrated_balanced_panel.py) - Balanced panel script
4. [`03-api-reference/data/`](03-api-reference/data/) - Data API reference

### For Development

1. [`04-development/setup.md`](04-development/setup.md) - Development environment
2. [`04-development/testing.md`](04-development/testing.md) - Testing guidelines
3. [`04-development/best-practices.md`](04-development/best-practices.md) - Coding standards
4. [`10-contributing/CONTRIBUTING.md`](10-contributing/CONTRIBUTING.md) - How to contribute

## 📝 Documentation Standards

- **Single Source of Truth**: Each topic has ONE authoritative location
- **Cross-References**: Documents link to related content
- **No Duplication**: Information appears once, referenced elsewhere
- **Clear Hierarchy**: General → Specific → Implementation
- **Numbered Structure**: Logical progression from 00 (getting started) to 10 (contributing)

## 🔄 Keeping Updated

| Directory | Update Frequency | Who Updates |
|-----------|-----------------|-------------|
| `00-getting-started/` | With major releases | Maintainers |
| `01-architecture/` | With design changes | Architects |
| `02-user-guides/` | With feature changes | Feature developers |
| `03-api-reference/` | With API changes | API developers |
| `04-development/` | As needed | Development team |
| `05-methodology/` | With research updates | Research team |
| `06-deployment/` | With deployment changes | DevOps team |
| `07-case-studies/` | With new studies | Research team |
| `08-results/` | With analysis runs | Analysis team |
| `09-troubleshooting/` | With new issues | Support team |
| `10-contributing/` | With process changes | Maintainers |

## 🚀 Quick Links

### Essential Resources
- **Start Here**: [`00-getting-started/`](00-getting-started/)
- **Architecture**: [`01-architecture/`](01-architecture/)
- **User Guides**: [`02-user-guides/`](02-user-guides/)
- **API Docs**: [`03-api-reference/`](03-api-reference/)

### For Specific Needs
- **Running Analysis**: [`02-user-guides/running-analyses.md`](02-user-guides/running-analyses.md)
- **Troubleshooting**: [`09-troubleshooting/`](09-troubleshooting/)
- **Contributing**: [`10-contributing/CONTRIBUTING.md`](10-contributing/CONTRIBUTING.md)
- **Deployment**: [`06-deployment/`](06-deployment/)

---

**New to the project?** Start with [`00-getting-started/README.md`](00-getting-started/README.md)!

**Looking for something specific?** Use the search function or check the topic/question guides above.
