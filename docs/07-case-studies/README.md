# Case Studies

This section presents real-world applications of the Yemen Market Integration Platform, demonstrating how the three-tier econometric model can be used to analyze critical economic issues in conflict-affected Yemen.

## Overview

Each case study showcases:
- Real economic crises and market dynamics in Yemen
- Application of the platform's analytical capabilities
- Policy-relevant insights derived from the analysis
- Practical code examples and visualizations

## Case Studies

### 1. [Wheat Market Integration Analysis](wheat-analysis.md)
**Period**: 2019-2024  
**Focus**: How wheat markets respond to global price shocks differently across conflict zones  
**Key Finding**: Exchange rate divergence creates artificial market segmentation

### 2. [Fuel Crisis of 2021](fuel-crisis-2021.md)
**Period**: January-December 2021  
**Focus**: Impact of fuel blockades on market integration and price transmission  
**Key Finding**: Fuel markets show immediate disintegration during blockades

### 3. [Exchange Rate Divergence Impact](exchange-rate-impact.md)
**Period**: 2020-2024  
**Focus**: How currency zone separation affects market integration  
**Key Finding**: Dual exchange rate system creates persistent price differentials

### 4. [Conflict Spillover Effects](conflict-spillovers.md)
**Period**: 2019-2023  
**Focus**: Spatial transmission of conflict impacts on neighboring markets  
**Key Finding**: Conflict effects propagate through trade networks, not just geography

## Using These Case Studies

### For Researchers
- Understand the platform's analytical capabilities
- Learn best practices for econometric specification
- See examples of handling data challenges

### For Policy Makers
- Evidence-based insights for intervention design
- Understanding of market dynamics during crises
- Quantified impacts of different policy options

### For Practitioners
- Step-by-step implementation guides
- Code templates for similar analyses
- Troubleshooting common issues

## Quick Start

To run any case study analysis:

```bash
# 1. Ensure data is up to date
python scripts/download_data.py

# 2. Create balanced panel
python scripts/analysis/create_balanced_panel.py

# 3. Run specific case study
python scripts/case_studies/run_wheat_analysis.py  # Example
```

## Key Methodological Notes

All case studies use:
- **Three-tier econometric framework**: Pooled, commodity-specific, and validation models
- **Robust standard errors**: Clustered at market level
- **Multiple specification tests**: To ensure result reliability
- **Real exchange rate data**: Accounting for parallel market rates

## Data Requirements

Each case study requires:
- WFP price data (monthly)
- ACLED conflict events
- Exchange rate data (official and parallel)
- Market characteristics (control zones, connectivity)

## Contributing New Case Studies

To add a new case study:
1. Identify a significant economic event or pattern
2. Formulate testable hypotheses
3. Apply the three-tier framework
4. Document findings with policy implications
5. Submit PR with code and documentation

## Citation

When using these case studies, please cite:
```
Yemen Market Integration Platform Case Studies (2024)
https://github.com/your-repo/yemen-market-integration
```