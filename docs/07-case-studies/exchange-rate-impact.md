# Exchange Rate Divergence Impact on Market Integration

## Executive Summary

Since 2020, Yemen has operated under a de facto dual exchange rate system, with the Yemeni Rial (YER) trading at ~535 YER/USD in Houthi-controlled areas versus ~2,150 YER/USD in government-controlled areas. This 4x differential creates one of the most extreme currency fragmentations in modern history. This case study demonstrates how exchange rate divergence fundamentally alters market integration patterns and creates persistent price differentials that cannot be arbitraged away.

## Background and Context

### The Currency Split Timeline
- **Pre-2020**: Single currency with minor regional variations
- **January 2020**: Houthis ban new banknotes printed in Aden
- **2020-2021**: Gradual divergence begins
- **2022-2023**: Stabilization at ~4x differential
- **2024**: Persistent dual system with occasional volatility

### Economic Mechanisms
1. **Transaction costs**: Currency conversion adds 5-10% costs
2. **Capital controls**: Movement of cash between zones restricted
3. **Price stickiness**: Local prices adjust slowly to exchange rate changes
4. **Import channels**: Different zones use different FX rates for imports

## Research Questions

1. **How does exchange rate divergence affect price integration between zones?**
2. **Are USD-denominated prices more integrated than YER prices?**
3. **Which commodities show the strongest exchange rate pass-through?**
4. **Can we identify profitable arbitrage opportunities that persist due to currency controls?**

## Data and Methodology

### Multi-Source Exchange Rate Data
```python
from yemen_market.data import ExchangeRateCollector
import pandas as pd
import numpy as np

# Collect exchange rate data from multiple sources
fx_collector = ExchangeRateCollector()

# Official rates
cbya_rates = fx_collector.get_central_bank_rates('Aden')
cbys_rates = fx_collector.get_central_bank_rates('Sanaa')

# Parallel market rates
parallel_rates = fx_collector.get_parallel_market_rates([
    'money_exchangers',
    'hawala_networks',
    'telegram_channels'
])

# Create comprehensive FX dataset
fx_data = pd.DataFrame({
    'date': pd.date_range('2019-01-01', '2024-12-31', freq='D'),
    'official_aden': cbya_rates,
    'official_sanaa': cbys_rates,
    'parallel_aden': parallel_rates['aden'],
    'parallel_sanaa': parallel_rates['sanaa'],
    'parallel_taiz': parallel_rates['taiz']
})

# Calculate divergence metrics
fx_data['divergence_ratio'] = fx_data['parallel_aden'] / fx_data['parallel_sanaa']
fx_data['divergence_percent'] = (fx_data['divergence_ratio'] - 1) * 100
```

### Comprehensive Price Dataset
```python
from yemen_market.data import PanelBuilder

# Build panel with all commodities
panel_builder = PanelBuilder()
price_data = panel_builder.build_balanced_panel(
    commodities='all',
    start_date='2019-01-01',
    end_date='2024-12-31'
)

# Merge with exchange rates
analysis_data = price_data.merge(
    fx_data[['date', 'parallel_aden', 'parallel_sanaa']],
    on='date'
)

# Assign appropriate exchange rate by market
analysis_data['exchange_rate'] = np.where(
    analysis_data['control_zone'] == 'Houthi',
    analysis_data['parallel_sanaa'],
    analysis_data['parallel_aden']
)

# Calculate USD prices
analysis_data['price_usd'] = (
    analysis_data['price_yer'] / analysis_data['exchange_rate']
)

# Add zone pair identifier
analysis_data['zone_pair'] = (
    analysis_data['control_zone'] + '_' + 
    analysis_data.groupby('market')['control_zone'].shift(1)
)
```

## Implementation

### Step 1: Documenting the Divergence
```python
import matplotlib.pyplot as plt
import seaborn as sns

# Create comprehensive divergence visualization
fig, axes = plt.subplots(3, 1, figsize=(14, 12))

# Panel A: Exchange rates over time
ax1 = axes[0]
ax1.plot(fx_data['date'], fx_data['parallel_sanaa'], 
         'b-', linewidth=2, label='Sana\'a (Houthi)')
ax1.plot(fx_data['date'], fx_data['parallel_aden'], 
         'r-', linewidth=2, label='Aden (Government)')
ax1.fill_between(fx_data['date'], 
                 fx_data['parallel_sanaa'], 
                 fx_data['parallel_aden'],
                 alpha=0.2, color='gray')
ax1.set_ylabel('YER/USD')
ax1.set_title('Exchange Rate Evolution by Control Zone')
ax1.legend()
ax1.axvline(x=pd.Timestamp('2020-01-01'), color='black', 
            linestyle='--', alpha=0.5, label='Currency ban')

# Panel B: Divergence ratio
ax2 = axes[1]
ax2.plot(fx_data['date'], fx_data['divergence_ratio'], 
         'g-', linewidth=2)
ax2.axhline(y=1, color='black', linestyle='-', alpha=0.3)
ax2.fill_between(fx_data['date'], 1, fx_data['divergence_ratio'],
                 where=(fx_data['divergence_ratio'] > 1),
                 alpha=0.3, color='red', label='Divergence')
ax2.set_ylabel('Ratio (Aden/Sana\'a)')
ax2.set_title('Exchange Rate Divergence Ratio')
ax2.set_ylim(0.8, 4.5)

# Panel C: Volatility comparison
rolling_vol = fx_data.set_index('date')[['parallel_sanaa', 'parallel_aden']].pct_change().rolling(30).std() * np.sqrt(252)
ax3 = axes[2]
ax3.plot(rolling_vol.index, rolling_vol['parallel_sanaa'], 
         'b-', label='Sana\'a volatility')
ax3.plot(rolling_vol.index, rolling_vol['parallel_aden'], 
         'r-', label='Aden volatility')
ax3.set_ylabel('Annualized volatility')
ax3.set_xlabel('Date')
ax3.set_title('Exchange Rate Volatility (30-day rolling)')
ax3.legend()

plt.tight_layout()
plt.savefig('results/exchange_rate_divergence_comprehensive.png', dpi=300)
```

### Step 2: Price Integration Analysis

#### Tier 1: Currency Zone Effects
```python
from yemen_market.models.three_tier import ThreeTierRunner

# Test integration separately for YER and USD prices
runner = ThreeTierRunner()

# Model 1: YER prices
yer_config = {
    'dependent_var': 'log_price_yer',
    'independent_vars': [
        'conflict_events',
        'same_zone',  # 1 if both markets in same currency zone
        'log_distance',
        'same_zone:log_distance',  # Interaction
        'divergence_ratio',
        'global_price_index'
    ],
    'fixed_effects': ['market_pair', 'month'],
    'cluster_var': 'market_pair'
}

yer_results = runner.run_tier1(
    create_market_pairs(analysis_data),
    config=yer_config
)

# Model 2: USD prices
usd_config = yer_config.copy()
usd_config['dependent_var'] = 'log_price_usd'

usd_results = runner.run_tier1(
    create_market_pairs(analysis_data),
    config=usd_config
)

# Compare integration metrics
integration_comparison = pd.DataFrame({
    'Price Currency': ['YER', 'USD'],
    'Within-zone R²': [yer_results['within_r2'], usd_results['within_r2']],
    'Cross-zone R²': [yer_results['cross_r2'], usd_results['cross_r2']],
    'Distance decay': [yer_results['distance_coefficient'], 
                      usd_results['distance_coefficient']]
})
```

#### Tier 2: Commodity-Specific Pass-through
```python
# Estimate exchange rate pass-through by commodity
passthrough_results = {}

for commodity in analysis_data['commodity'].unique():
    comm_data = analysis_data[analysis_data['commodity'] == commodity]
    
    # Estimate pass-through regression
    model = runner.run_tier2(
        comm_data,
        config={
            'dependent_var': 'log_price_yer',
            'independent_vars': [
                'log_exchange_rate',
                'log_global_price',
                'log_exchange_rate:log_global_price',
                'control_zone'
            ],
            'method': 'panel_fe'
        }
    )
    
    passthrough_results[commodity] = {
        'short_run_passthrough': model.params['log_exchange_rate'],
        'import_share': get_import_share(commodity),
        'tradability': get_tradability_index(commodity),
        'std_error': model.bse['log_exchange_rate']
    }

# Create tradability chart
passthrough_df = pd.DataFrame(passthrough_results).T
passthrough_df['significant'] = passthrough_df['std_error'] < 0.05

fig, ax = plt.subplots(figsize=(12, 8))
scatter = ax.scatter(passthrough_df['import_share'], 
                    passthrough_df['short_run_passthrough'],
                    s=passthrough_df['tradability'] * 100,
                    c=passthrough_df['significant'],
                    cmap='RdYlBu', alpha=0.6)

# Add commodity labels
for idx, row in passthrough_df.iterrows():
    ax.annotate(idx, (row['import_share'], row['short_run_passthrough']),
                fontsize=8, ha='center')

ax.set_xlabel('Import Share (%)')
ax.set_ylabel('Exchange Rate Pass-through Coefficient')
ax.set_title('Exchange Rate Pass-through by Commodity Characteristics')
ax.axhline(y=1, color='black', linestyle='--', alpha=0.3, 
           label='Complete pass-through')
ax.grid(True, alpha=0.3)

plt.colorbar(scatter, label='Statistical Significance')
plt.savefig('results/exchange_rate_passthrough_commodities.png', dpi=300)
```

#### Tier 3: Arbitrage Analysis
```python
# Identify persistent arbitrage opportunities
from yemen_market.models.three_tier.tier3_validation import ArbitrageAnalysis

arbitrage_analyzer = ArbitrageAnalysis()

# Calculate potential arbitrage profits
arbitrage_opportunities = []

for commodity in ['Wheat Flour', 'Sugar', 'Rice (Imported)']:
    comm_data = analysis_data[analysis_data['commodity'] == commodity]
    
    # Compare prices across zone boundaries
    for date in comm_data['date'].unique():
        date_data = comm_data[comm_data['date'] == date]
        
        houthi_markets = date_data[date_data['control_zone'] == 'Houthi']
        gov_markets = date_data[date_data['control_zone'] == 'Government']
        
        if len(houthi_markets) > 0 and len(gov_markets) > 0:
            # Calculate price differentials in USD
            avg_houthi_usd = houthi_markets['price_usd'].mean()
            avg_gov_usd = gov_markets['price_usd'].mean()
            
            # Account for transaction costs
            transaction_cost = 0.10  # 10% for currency conversion + transport
            
            arbitrage_profit = abs(avg_houthi_usd - avg_gov_usd) - \
                              (min(avg_houthi_usd, avg_gov_usd) * transaction_cost)
            
            if arbitrage_profit > 0:
                arbitrage_opportunities.append({
                    'date': date,
                    'commodity': commodity,
                    'price_diff_usd': abs(avg_houthi_usd - avg_gov_usd),
                    'price_diff_percent': abs(avg_houthi_usd - avg_gov_usd) / 
                                         min(avg_houthi_usd, avg_gov_usd) * 100,
                    'arbitrage_profit': arbitrage_profit,
                    'direction': 'Houthi→Gov' if avg_houthi_usd < avg_gov_usd 
                                else 'Gov→Houthi'
                })

arbitrage_df = pd.DataFrame(arbitrage_opportunities)

# Visualize arbitrage persistence
fig, ax = plt.subplots(figsize=(14, 8))

for commodity in arbitrage_df['commodity'].unique():
    comm_arb = arbitrage_df[arbitrage_df['commodity'] == commodity]
    monthly_avg = comm_arb.groupby(pd.Grouper(key='date', freq='M'))['arbitrage_profit'].mean()
    
    ax.plot(monthly_avg.index, monthly_avg.values, 
            linewidth=2, label=commodity, marker='o')

ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
ax.fill_between(ax.get_xlim(), 0, ax.get_ylim()[1], 
                alpha=0.1, color='green', label='Profitable arbitrage zone')
ax.set_ylabel('Arbitrage profit (USD/kg)')
ax.set_xlabel('Date')
ax.set_title('Persistent Arbitrage Opportunities Due to Currency Controls')
ax.legend()
ax.grid(True, alpha=0.3)

plt.savefig('results/exchange_rate_arbitrage_opportunities.png', dpi=300)
```

### Step 3: Mechanism Testing
```python
# Test different mechanisms for price divergence

# Mechanism 1: Direct exchange rate effect
mechanism1 = runner.run_tier1(
    analysis_data,
    config={
        'dependent_var': 'price_ratio_to_global',
        'independent_vars': ['log_exchange_rate', 'control_zone'],
        'fixed_effects': ['commodity', 'month']
    }
)

# Mechanism 2: Transaction cost channel
analysis_data['cross_zone_trade'] = (
    analysis_data.groupby(['market', 'date'])['control_zone']
    .transform(lambda x: x != x.mode()[0])
)

mechanism2 = runner.run_tier1(
    analysis_data,
    config={
        'dependent_var': 'log_price_usd',
        'independent_vars': ['cross_zone_trade', 'divergence_ratio', 
                            'cross_zone_trade:divergence_ratio'],
        'fixed_effects': ['market', 'commodity', 'month']
    }
)

# Mechanism 3: Market power in controlled zones
analysis_data['hhi'] = calculate_market_concentration(analysis_data)

mechanism3 = runner.run_tier1(
    analysis_data,
    config={
        'dependent_var': 'log_price_yer',
        'independent_vars': ['hhi', 'control_zone', 'hhi:control_zone'],
        'fixed_effects': ['market', 'commodity', 'month']
    }
)
```

## Results and Visualizations

### Finding 1: Law of One Price Holds in USD, Fails in YER
```python
# Create price comparison visualization
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

# YER prices
sample_commodity = 'Wheat Flour'
wheat_data = analysis_data[analysis_data['commodity'] == sample_commodity]

# Plot YER prices
for zone in ['Houthi', 'Government']:
    zone_data = wheat_data[wheat_data['control_zone'] == zone]
    monthly_avg = zone_data.groupby('date')['price_yer'].mean()
    ax1.plot(monthly_avg.index, monthly_avg.values, 
             linewidth=2, label=f'{zone} control')

ax1.set_ylabel('Price (YER/kg)')
ax1.set_title(f'{sample_commodity} Prices in YER')
ax1.legend()
ax1.set_ylim(bottom=0)

# Plot USD prices
for zone in ['Houthi', 'Government']:
    zone_data = wheat_data[wheat_data['control_zone'] == zone]
    monthly_avg = zone_data.groupby('date')['price_usd'].mean()
    ax2.plot(monthly_avg.index, monthly_avg.values, 
             linewidth=2, label=f'{zone} control')

ax2.set_ylabel('Price (USD/kg)')
ax2.set_title(f'{sample_commodity} Prices in USD')
ax2.legend()
ax2.set_ylim(bottom=0)

plt.tight_layout()
plt.savefig('results/exchange_rate_lop_comparison.png', dpi=300)

# Statistical test for LOP
from yemen_market.models.validation import test_law_of_one_price

lop_results = {
    'YER': test_law_of_one_price(wheat_data, price_var='price_yer'),
    'USD': test_law_of_one_price(wheat_data, price_var='price_usd')
}

print(f"Law of One Price Test Results:")
print(f"YER prices: {'Rejected' if lop_results['YER']['p_value'] < 0.05 else 'Not rejected'}")
print(f"USD prices: {'Rejected' if lop_results['USD']['p_value'] < 0.05 else 'Not rejected'}")
```

### Finding 2: Zone Premium Calculation
```python
# Calculate average zone premiums
zone_premiums = []

for commodity in analysis_data['commodity'].unique():
    comm_data = analysis_data[analysis_data['commodity'] == commodity]
    
    # Calculate average prices by zone
    zone_avg = comm_data.groupby(['date', 'control_zone'])[
        ['price_yer', 'price_usd']
    ].mean().reset_index()
    
    # Calculate premiums
    pivoted = zone_avg.pivot(index='date', columns='control_zone')
    
    yer_premium = (pivoted[('price_yer', 'Government')] - 
                   pivoted[('price_yer', 'Houthi')]) / \
                  pivoted[('price_yer', 'Houthi')] * 100
    
    usd_premium = (pivoted[('price_usd', 'Government')] - 
                   pivoted[('price_usd', 'Houthi')]) / \
                  pivoted[('price_usd', 'Houthi')] * 100
    
    zone_premiums.append({
        'commodity': commodity,
        'avg_yer_premium': yer_premium.mean(),
        'avg_usd_premium': usd_premium.mean(),
        'yer_premium_vol': yer_premium.std(),
        'usd_premium_vol': usd_premium.std()
    })

premium_df = pd.DataFrame(zone_premiums)

# Visualize premiums
fig, ax = plt.subplots(figsize=(12, 8))

x = np.arange(len(premium_df))
width = 0.35

bars1 = ax.bar(x - width/2, premium_df['avg_yer_premium'], 
                width, label='YER premium', yerr=premium_df['yer_premium_vol'])
bars2 = ax.bar(x + width/2, premium_df['avg_usd_premium'], 
                width, label='USD premium', yerr=premium_df['usd_premium_vol'])

ax.set_xlabel('Commodity')
ax.set_ylabel('Government zone premium (%)')
ax.set_title('Price Premiums in Government vs Houthi Areas')
ax.set_xticks(x)
ax.set_xticklabels(premium_df['commodity'], rotation=45, ha='right')
ax.legend()
ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
ax.grid(True, alpha=0.3, axis='y')

plt.tight_layout()
plt.savefig('results/exchange_rate_zone_premiums.png', dpi=300)
```

### Finding 3: Market Segmentation Map
```python
# Create geographic visualization of market integration
import geopandas as gpd
from yemen_market.visualization import create_integration_map

# Load Yemen shapefile
yemen_shape = gpd.read_file('data/boundaries/yemen_admin2.shp')

# Calculate bilateral integration scores
integration_scores = calculate_bilateral_integration(
    analysis_data,
    method='price_correlation',
    window=6  # 6-month rolling
)

# Create integration heatmap
fig = create_integration_map(
    yemen_shape,
    integration_scores,
    title='Market Integration Intensity by Currency Zone',
    save_path='results/exchange_rate_integration_map.png'
)

# Add currency zone boundaries
zone_boundaries = gpd.read_file('data/boundaries/control_zones_2024.shp')
fig.add_layer(zone_boundaries, color='red', linewidth=2, 
              linestyle='--', label='Currency zone boundary')
```

## Policy Implications

### 1. Currency Reunification Strategy
- **Finding**: USD prices remain integrated while YER prices diverge
- **Implication**: Market forces support currency reunification
- **Recommendation**: 
  - Phase 1: Establish USD as common unit of account
  - Phase 2: Gradual exchange rate convergence
  - Phase 3: Unified monetary policy

### 2. Trade Facilitation
- **Finding**: Transaction costs prevent efficient arbitrage
- **Implication**: Reducing trade barriers more important than price controls
- **Recommendation**:
  - Simplify inter-zone payment systems
  - Reduce checkpoint delays
  - Standardize customs procedures

### 3. Price Stabilization Mechanisms
- **Finding**: Exchange rate volatility drives price instability
- **Implication**: FX stability more important than direct price intervention
- **Recommendation**:
  - Foreign exchange reserves buffer
  - Coordinated intervention triggers
  - Communication strategy for expectations

### 4. Social Protection Design
- **Finding**: Real purchasing power differs dramatically by zone
- **Implication**: Uniform cash transfers have unequal impacts
- **Recommendation**:
  - Zone-adjusted transfer amounts
  - Index to local USD prices, not YER
  - In-kind options where FX access limited

## Lessons Learned

### Economic Insights
1. **Currency matters more than conflict**: Exchange rate explains more price variation than violence
2. **Traders adapt quickly**: USD pricing emerges spontaneously as solution
3. **Geography becomes destiny**: Physical location determines currency access
4. **Integration is multi-dimensional**: Markets can be integrated in one currency but not another

### Methodological Contributions
1. **Dual price analysis**: Always analyze in both local and reference currency
2. **Zone interactions**: Cross-zone market pairs need special treatment
3. **Time-varying effects**: Exchange rate impact changes with divergence level
4. **Mechanism testing**: Multiple channels operate simultaneously

### Practical Challenges
1. **Data synchronization**: Match price and FX data carefully
2. **Quality variation**: Currency zones may trade different qualities
3. **Informal markets**: Capture parallel market rates, not just official
4. **Political sensitivity**: Present findings neutrally

## Implementation Code

```python
# Key functions for exchange rate analysis
from yemen_market.exchange_rate_tools import (
    collect_parallel_rates,
    calculate_real_exchange_rates,
    test_purchasing_power_parity,
    estimate_passthrough_coefficients,
    identify_arbitrage_opportunities,
    simulate_reunification_scenarios
)

# Example usage
results = analyze_exchange_rate_impact(
    price_data=analysis_data,
    fx_data=fx_data,
    commodities=['Wheat Flour', 'Sugar', 'Rice (Imported)'],
    zones=['Houthi', 'Government'],
    methods=['correlation', 'cointegration', 'arbitrage']
)
```

## Future Extensions

1. **High-frequency analysis**: Daily price/FX data during volatile periods
2. **Behavioral responses**: Survey traders on pricing decisions
3. **Policy experiments**: Natural experiments from temporary controls
4. **Regional comparisons**: Similar dual-currency situations globally

## References

1. Goldberg, P. K., & Knetter, M. M. (1997). "Goods Prices and Exchange Rates: What Have We Learned?"
2. Burstein, A., & Gopinath, G. (2014). "International Prices and Exchange Rates"
3. IMF (2023). "Yemen: Exchange Rate Dynamics and Economic Fragmentation"
4. Frankel, J. A., Parsley, D., & Wei, S. J. (2012). "Slow Pass-through Around the World"