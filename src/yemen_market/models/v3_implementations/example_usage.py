"""Example usage of V3 performance-optimized components.

This script demonstrates how to use the Polars data loader and DuckDB
transformer for high-performance Yemen market analysis.
"""

from pathlib import Path
from typing import Optional

import pandas as pd
import polars as pl

from .polars_loader import PolarsDataLoader
from .duckdb_transformer import DuckDBTransformer
from ...utils.logging import info, bind


def example_v3_pipeline(
    output_dir: Optional[Path] = None,
    save_results: bool = True
) -> pd.DataFrame:
    """Run example V3 pipeline with Polars and DuckDB.
    
    Args:
        output_dir: Directory for saving results
        save_results: Whether to save results to disk
        
    Returns:
        Integrated panel DataFrame
    """
    bind(module="v3_example")
    info("Running V3 pipeline example")
    
    # Step 1: Load data with Polars
    info("Step 1: Loading data with Polar<PERSON>")
    loader = PolarsDataLoader(lazy=False, enable_benchmarks=True)
    
    # Load all components
    components = loader.load_all_components()
    
    # Convert to pandas for DuckDB (zero-copy where possible)
    data_frames = {}
    for name, df in components.items():
        if df is not None:
            data_frames[name] = loader.to_pandas(df)
            info(f"Loaded {name}: {len(data_frames[name]):,} rows")
    
    # Step 2: Transform with DuckDB
    info("Step 2: Transforming data with DuckDB")
    
    with DuckDBTransformer(memory_limit='4GB', enable_benchmarks=True) as transformer:
        
        # Register all data with DuckDB
        for name, df in data_frames.items():
            transformer.register_dataframe(df, name)
        
        # Create balanced panel if we have price data
        if 'prices' in data_frames:
            # Get dimensions for balanced panel
            commodities = transformer.conn.execute("""
                SELECT DISTINCT commodity 
                FROM prices 
                WHERE commodity IN (
                    'Wheat', 'Wheat Flour', 'Rice (Imported)', 
                    'Sugar', 'Oil (Vegetable)'
                )
                ORDER BY commodity
            """).df()['commodity'].tolist()
            
            markets = transformer.conn.execute("""
                SELECT DISTINCT market_id
                FROM prices
                WHERE market_id IS NOT NULL
                AND commodity IN (
                    'Wheat', 'Wheat Flour', 'Rice (Imported)',
                    'Sugar', 'Oil (Vegetable)'
                )
                ORDER BY market_id
                LIMIT 21  -- Core markets for balanced panel
            """).df()['market_id'].tolist()
            
            info(f"Creating balanced panel: {len(commodities)} commodities × {len(markets)} markets")
            
            # Create balanced panel
            balanced_panel = transformer.create_balanced_panel(
                'prices',
                commodities,
                markets,
                '2019-09-01',
                '2024-11-30'
            )
            
            info(f"Balanced panel created: {len(balanced_panel):,} observations")
            
            # Register balanced panel
            transformer.register_dataframe(balanced_panel, 'balanced_panel')
        
        # Aggregate conflict data if available
        if 'conflict' in data_frames:
            conflict_intensity = transformer.aggregate_conflict_intensity(
                'conflict',
                level='governorate'
            )
            transformer.register_dataframe(conflict_intensity, 'conflict_intensity')
            info(f"Conflict intensity calculated: {len(conflict_intensity):,} rows")
        
        # Calculate price indices
        if 'balanced_panel' in transformer.conn.list_tables():
            price_indices = transformer.calculate_price_indices(
                'balanced_panel',
                base_period='2019-09'
            )
            info(f"Price indices calculated: {len(price_indices):,} rows")
            
            # Create market pairs for spatial analysis
            market_pairs = transformer.create_market_pairs(
                'balanced_panel',
                max_distance_km=300
            )
            info(f"Market pairs created: {len(market_pairs):,} pairs")
        
        # Export results if requested
        if save_results and output_dir:
            output_dir = Path(output_dir)
            output_dir.mkdir(exist_ok=True)
            
            # Export to Parquet for efficient storage
            if 'balanced_panel' in transformer.conn.list_tables():
                transformer.export_to_parquet(
                    'balanced_panel',
                    output_dir / 'balanced_panel_v3.parquet'
                )
            
            if 'price_indices' in locals():
                price_indices.to_parquet(
                    output_dir / 'price_indices_v3.parquet'
                )
            
            if 'market_pairs' in locals():
                market_pairs.to_parquet(
                    output_dir / 'market_pairs_v3.parquet'
                )
            
            info(f"Results saved to {output_dir}")
    
    # Return the integrated panel
    return balanced_panel if 'balanced_panel' in locals() else pd.DataFrame()


def example_custom_analysis():
    """Example of custom analysis using V3 components."""
    bind(module="v3_custom_analysis")
    
    # Load specific data
    loader = PolarsDataLoader()
    
    # Load just WFP prices (lazy evaluation)
    prices = loader.load_wfp_prices()
    
    # Apply Polars transformations
    filtered_prices = prices.filter(
        pl.col('commodity').is_in(['Wheat', 'Rice (Imported)', 'Sugar'])
    ).filter(
        pl.col('year') >= 2020
    ).select([
        'date', 'commodity', 'market_id', 'governorate',
        'price_usd', 'price_local'
    ])
    
    # Collect results
    result_df = filtered_prices.collect()
    
    # Convert to pandas for DuckDB
    pandas_df = loader.to_pandas(result_df)
    
    # Use DuckDB for complex analytics
    with DuckDBTransformer() as transformer:
        transformer.register_dataframe(pandas_df, 'filtered_prices')
        
        # Complex analytical query
        analysis = transformer.conn.execute("""
            WITH monthly_stats AS (
                SELECT 
                    commodity,
                    governorate,
                    DATE_TRUNC('month', date) as month,
                    COUNT(*) as observations,
                    AVG(price_usd) as avg_price,
                    STDDEV(price_usd) as price_volatility,
                    MIN(price_usd) as min_price,
                    MAX(price_usd) as max_price,
                    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY price_usd) as median_price
                FROM filtered_prices
                WHERE price_usd IS NOT NULL
                GROUP BY commodity, governorate, month
            ),
            ranked_volatility AS (
                SELECT 
                    *,
                    RANK() OVER (
                        PARTITION BY commodity, month 
                        ORDER BY price_volatility DESC
                    ) as volatility_rank
                FROM monthly_stats
            )
            SELECT 
                commodity,
                governorate,
                month,
                observations,
                ROUND(avg_price, 2) as avg_price,
                ROUND(median_price, 2) as median_price,
                ROUND(price_volatility, 2) as volatility,
                volatility_rank,
                CASE 
                    WHEN volatility_rank <= 3 THEN 'High Risk'
                    WHEN volatility_rank <= 10 THEN 'Medium Risk'
                    ELSE 'Low Risk'
                END as risk_category
            FROM ranked_volatility
            WHERE observations >= 5  -- Minimum data quality threshold
            ORDER BY commodity, month, volatility_rank
        """).df()
        
        info(f"Custom analysis complete: {len(analysis):,} rows")
        
        # Summary statistics
        summary = transformer.conn.execute("""
            SELECT 
                commodity,
                COUNT(DISTINCT governorate) as n_governorates,
                COUNT(DISTINCT month) as n_months,
                AVG(observations) as avg_observations,
                AVG(avg_price) as overall_avg_price,
                AVG(volatility) as avg_volatility
            FROM (
                SELECT * FROM analysis
            ) t
            GROUP BY commodity
            ORDER BY commodity
        """).df()
        
        print("\nCommodity Summary:")
        print(summary.to_string(index=False))
        
    return analysis


if __name__ == "__main__":
    # Run example pipeline
    from ...config.settings import PROCESSED_DATA_DIR
    
    output_dir = PROCESSED_DATA_DIR / "v3_output"
    panel = example_v3_pipeline(output_dir=output_dir)
    
    if not panel.empty:
        print(f"\nSuccessfully created panel with {len(panel):,} observations")
        print(f"Columns: {', '.join(panel.columns)}")
        print(f"\nSample data:")
        print(panel.head())
    
    # Run custom analysis example
    print("\n" + "="*60)
    print("Running custom analysis example...")
    custom_results = example_custom_analysis()