"""Polars-based data loader for V3 performance optimization.

This module provides high-performance data loading using Polars DataFrames,
which offer significant speed improvements over pandas for large datasets.

Key Features:
- Lazy evaluation for efficient query planning
- Zero-copy operations where possible
- Columnar storage for better cache efficiency
- Native support for parallel operations
- Seamless integration with existing pandas-based code
"""

import time
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple, Any

import numpy as np
import polars as pl
import pandas as pd

from ...utils.logging import (
    bind, timer, progress, log_data_shape,
    info, warning, error, debug
)
from ...config.settings import PROCESSED_DATA_DIR, RAW_DATA_DIR


class PolarsDataLoader:
    """High-performance data loader using Polars.
    
    This loader provides optimized data loading for all Yemen market data sources,
    including WFP prices, ACLED conflict data, ACAPS control zones, and spatial data.
    
    Performance improvements:
    - 5-10x faster CSV reading than pandas
    - Lazy evaluation for complex queries
    - Efficient memory usage through columnar storage
    - Native parallel processing support
    
    Attributes:
        data_dir: Directory containing data files
        lazy: Whether to use lazy evaluation (default True)
        enable_benchmarks: Whether to benchmark operations
    """
    
    def __init__(
        self, 
        data_dir: Optional[Path] = None,
        lazy: bool = True,
        enable_benchmarks: bool = True
    ):
        """Initialize Polars data loader.
        
        Args:
            data_dir: Directory containing data files
            lazy: Use lazy evaluation for query optimization
            enable_benchmarks: Enable performance benchmarking
        """
        self.data_dir = data_dir or PROCESSED_DATA_DIR
        self.lazy = lazy
        self.enable_benchmarks = enable_benchmarks
        self._benchmarks: Dict[str, float] = {}
        bind(module="PolarsDataLoader")
        
    def benchmark(self, operation: str):
        """Context manager for benchmarking operations."""
        class BenchmarkContext:
            def __init__(self, loader, name):
                self.loader = loader
                self.name = name
                self.start_time = None
                
            def __enter__(self):
                if self.loader.enable_benchmarks:
                    self.start_time = time.perf_counter()
                return self
                
            def __exit__(self, exc_type, exc_val, exc_tb):
                if self.loader.enable_benchmarks and self.start_time:
                    elapsed = time.perf_counter() - self.start_time
                    self.loader._benchmarks[self.name] = elapsed
                    debug(f"Benchmark {self.name}: {elapsed:.4f}s")
                    
        return BenchmarkContext(self, operation)
        
    def load_wfp_prices(self, file_path: Optional[Path] = None) -> Union[pl.DataFrame, pl.LazyFrame]:
        """Load WFP price data using Polars.
        
        Args:
            file_path: Path to WFP CSV file
            
        Returns:
            Polars DataFrame or LazyFrame with price data
        """
        with timer("load_wfp_prices_polars"):
            info("Loading WFP price data with Polars")
            
            if file_path is None:
                # Find most recent WFP file
                wfp_files = list((RAW_DATA_DIR / "wfp").glob("wfp_food_prices_*.csv"))
                if not wfp_files:
                    raise FileNotFoundError("No WFP price files found")
                file_path = max(wfp_files, key=lambda p: p.stat().st_mtime)
            
            with self.benchmark("read_csv"):
                # Define schema for faster reading
                schema = {
                    'date': pl.Date,
                    'admin1': pl.Utf8,
                    'admin2': pl.Utf8,
                    'market': pl.Utf8,
                    'latitude': pl.Float64,
                    'longitude': pl.Float64,
                    'category': pl.Utf8,
                    'commodity': pl.Utf8,
                    'unit': pl.Utf8,
                    'priceflag': pl.Utf8,
                    'pricetype': pl.Utf8,
                    'currency': pl.Utf8,
                    'price': pl.Float64,
                    'usdprice': pl.Float64
                }
                
                if self.lazy:
                    df = pl.scan_csv(
                        file_path,
                        schema=schema,
                        try_parse_dates=True,
                        ignore_errors=True
                    )
                else:
                    df = pl.read_csv(
                        file_path,
                        schema=schema,
                        try_parse_dates=True,
                        ignore_errors=True
                    )
            
            # Apply transformations
            with self.benchmark("transform_wfp"):
                df = df.with_columns([
                    # Standardize column names
                    pl.col('admin1').alias('governorate'),
                    pl.col('admin2').alias('district'),
                    pl.col('market').alias('market_name'),
                    pl.col('latitude').alias('lat'),
                    pl.col('longitude').alias('lon'),
                    pl.col('price').alias('price_local'),
                    pl.col('usdprice').alias('price_usd'),
                    
                    # Extract temporal features
                    pl.col('date').dt.year().alias('year'),
                    pl.col('date').dt.month().alias('month'),
                    pl.col('date').dt.strftime('%Y-%m').alias('year_month'),
                    
                    # Create market ID
                    (pl.col('market') + '_' + pl.col('admin1')).alias('market_id')
                ])
            
            if not self.lazy:
                log_data_shape("wfp_prices", df)
            
            return df
    
    def load_conflict_data(self, file_path: Optional[Path] = None) -> Union[pl.DataFrame, pl.LazyFrame]:
        """Load ACLED conflict data using Polars.
        
        Args:
            file_path: Path to ACLED CSV file
            
        Returns:
            Polars DataFrame or LazyFrame with conflict data
        """
        with timer("load_conflict_data_polars"):
            info("Loading ACLED conflict data with Polars")
            
            if file_path is None:
                file_path = RAW_DATA_DIR / "acled" / "acled_yemen_events_2019-01-01_to_2024-12-31.csv"
            
            with self.benchmark("read_conflict_csv"):
                # ACLED specific columns
                columns = [
                    'event_date', 'event_type', 'sub_event_type',
                    'admin1', 'admin2', 'location',
                    'latitude', 'longitude',
                    'fatalities', 'notes'
                ]
                
                if self.lazy:
                    df = pl.scan_csv(
                        file_path,
                        columns=columns,
                        try_parse_dates=True
                    )
                else:
                    df = pl.read_csv(
                        file_path,
                        columns=columns,
                        try_parse_dates=True  
                    )
            
            # Process conflict data
            with self.benchmark("transform_conflict"):
                df = df.with_columns([
                    pl.col('event_date').cast(pl.Date).alias('date'),
                    pl.col('admin1').alias('governorate'),
                    pl.col('admin2').alias('district'),
                    pl.col('fatalities').cast(pl.Int32),
                    
                    # Extract temporal features
                    pl.col('event_date').cast(pl.Date).dt.year().alias('year'),
                    pl.col('event_date').cast(pl.Date).dt.month().alias('month'),
                    pl.col('event_date').cast(pl.Date).dt.strftime('%Y-%m').alias('year_month')
                ])
                
                # Aggregate by month and location
                df = df.group_by(['governorate', 'district', 'year_month']).agg([
                    pl.count().alias('n_events'),
                    pl.sum('fatalities').alias('total_fatalities'),
                    (pl.col('event_type') == 'Battles').sum().alias('n_battles'),
                    (pl.col('event_type') == 'Explosions/Remote violence').sum().alias('n_explosions'),
                    (pl.col('event_type') == 'Violence against civilians').sum().alias('n_violence_civilians')
                ])
            
            if not self.lazy:
                log_data_shape("conflict_data", df)
                
            return df
    
    def load_control_zones(self, file_path: Optional[Path] = None) -> Union[pl.DataFrame, pl.LazyFrame]:
        """Load ACAPS control zone data using Polars.
        
        Args:
            file_path: Path to control zones file
            
        Returns:
            Polars DataFrame or LazyFrame with control zone data
        """
        with timer("load_control_zones_polars"):
            info("Loading ACAPS control zone data with Polars")
            
            if file_path is None:
                file_path = self.data_dir / "acaps_control_zones.parquet"
                
            with self.benchmark("read_control_zones"):
                if file_path.suffix == '.parquet':
                    if self.lazy:
                        df = pl.scan_parquet(file_path)
                    else:
                        df = pl.read_parquet(file_path)
                else:
                    # CSV fallback
                    if self.lazy:
                        df = pl.scan_csv(file_path)
                    else:
                        df = pl.read_csv(file_path)
            
            if not self.lazy:
                log_data_shape("control_zones", df)
                
            return df
    
    def to_pandas(self, df: Union[pl.DataFrame, pl.LazyFrame]) -> pd.DataFrame:
        """Convert Polars DataFrame to pandas for compatibility.
        
        Args:
            df: Polars DataFrame or LazyFrame
            
        Returns:
            pandas DataFrame
        """
        with self.benchmark("to_pandas"):
            if isinstance(df, pl.LazyFrame):
                df = df.collect()
            return df.to_pandas()
    
    def load_all_components(self) -> Dict[str, Union[pl.DataFrame, pl.LazyFrame]]:
        """Load all data components for panel construction.
        
        Returns:
            Dictionary of component DataFrames
        """
        with timer("load_all_components_polars"):
            info("Loading all data components with Polars")
            
            components = {}
            
            # Load each component
            with progress("Loading components", total=4) as update:
                # WFP prices
                try:
                    components['prices'] = self.load_wfp_prices()
                    update(1)
                except Exception as e:
                    error(f"Failed to load WFP prices: {e}")
                    components['prices'] = None
                
                # Conflict data
                try:
                    components['conflict'] = self.load_conflict_data()
                    update(1)
                except Exception as e:
                    error(f"Failed to load conflict data: {e}")
                    components['conflict'] = None
                
                # Control zones
                try:
                    components['control_zones'] = self.load_control_zones()
                    update(1)
                except Exception as e:
                    error(f"Failed to load control zones: {e}")
                    components['control_zones'] = None
                
                # Market metadata (if exists)
                try:
                    market_file = self.data_dir / "market_metadata.parquet"
                    if market_file.exists():
                        if self.lazy:
                            components['markets'] = pl.scan_parquet(market_file)
                        else:
                            components['markets'] = pl.read_parquet(market_file)
                    update(1)
                except Exception as e:
                    warning(f"No market metadata found: {e}")
                    
            # Report benchmarks
            if self.enable_benchmarks:
                self._report_benchmarks()
                
            return components
    
    def _report_benchmarks(self):
        """Report performance benchmarks."""
        if not self._benchmarks:
            return
            
        info("Performance benchmarks:")
        total_time = sum(self._benchmarks.values())
        
        for operation, duration in sorted(self._benchmarks.items()):
            percentage = (duration / total_time) * 100 if total_time > 0 else 0
            info(f"  {operation}: {duration:.4f}s ({percentage:.1f}%)")
            
        info(f"  Total: {total_time:.4f}s")
    
    def create_balanced_panel_polars(
        self,
        prices_df: Union[pl.DataFrame, pl.LazyFrame],
        commodities: List[str],
        markets: List[str],
        start_date: str,
        end_date: str
    ) -> pl.DataFrame:
        """Create a balanced panel using Polars operations.
        
        Args:
            prices_df: Price data
            commodities: List of commodities
            markets: List of markets
            start_date: Start date
            end_date: End date
            
        Returns:
            Balanced panel DataFrame
        """
        with timer("create_balanced_panel_polars"):
            info("Creating balanced panel with Polars")
            
            # Create date range
            date_range = pl.date_range(
                start=pl.datetime(start_date),
                end=pl.datetime(end_date),
                interval="1mo",
                eager=True
            )
            
            # Create cross product of dimensions
            with self.benchmark("create_panel_structure"):
                # Create all combinations
                panel_structure = (
                    pl.DataFrame({"commodity": commodities})
                    .join(pl.DataFrame({"market_id": markets}), how="cross")
                    .join(pl.DataFrame({"date": date_range}), how="cross")
                )
            
            # Merge with actual data
            with self.benchmark("merge_panel_data"):
                if isinstance(prices_df, pl.LazyFrame):
                    prices_df = prices_df.collect()
                
                balanced_panel = panel_structure.join(
                    prices_df.select(['commodity', 'market_id', 'date', 'price_usd']),
                    on=['commodity', 'market_id', 'date'],
                    how='left'
                )
            
            log_data_shape("balanced_panel", balanced_panel)
            
            return balanced_panel
            
    def compare_with_pandas(self, file_path: Path) -> Dict[str, Any]:
        """Compare Polars performance with pandas baseline.
        
        Args:
            file_path: Path to test file
            
        Returns:
            Performance comparison results
        """
        results = {}
        
        # Polars benchmark
        start = time.perf_counter()
        df_polars = pl.read_csv(file_path)
        polars_time = time.perf_counter() - start
        results['polars_read_time'] = polars_time
        results['polars_memory'] = df_polars.estimated_size() / 1024 / 1024  # MB
        
        # Pandas benchmark
        start = time.perf_counter()
        df_pandas = pd.read_csv(file_path)
        pandas_time = time.perf_counter() - start
        results['pandas_read_time'] = pandas_time
        results['pandas_memory'] = df_pandas.memory_usage(deep=True).sum() / 1024 / 1024  # MB
        
        # Calculate speedup
        results['speedup'] = pandas_time / polars_time
        results['memory_ratio'] = results['pandas_memory'] / results['polars_memory']
        
        info("Performance comparison:",
             polars_time=f"{polars_time:.4f}s",
             pandas_time=f"{pandas_time:.4f}s", 
             speedup=f"{results['speedup']:.2f}x",
             memory_savings=f"{results['memory_ratio']:.2f}x")
        
        return results