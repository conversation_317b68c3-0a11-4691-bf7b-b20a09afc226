"""DuckDB-based data transformer for V3 performance optimization.

This module provides high-performance data transformations using DuckDB,
an in-memory analytical database that excels at complex SQL operations.

Key Features:
- In-memory columnar storage for fast analytics
- Vectorized query execution
- Parallel query processing
- Zero-copy integration with Polars/pandas
- SQL-based transformations for complex operations
"""

import time
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple, Any, Callable

import duckdb
import numpy as np
import polars as pl
import pandas as pd

from ...utils.logging import (
    bind, timer, progress, log_data_shape,
    info, warning, error, debug
)
from ...config.settings import PROCESSED_DATA_DIR, ANALYSIS_CONFIG


class DuckDBTransformer:
    """High-performance data transformer using DuckDB.
    
    This transformer leverages DuckDB's in-memory analytical capabilities
    for complex data transformations required in Yemen market analysis.
    
    Performance improvements:
    - 10-100x faster for complex aggregations
    - Efficient joins across large datasets
    - Parallel processing of queries
    - Memory-efficient columnar storage
    
    Attributes:
        conn: DuckDB connection
        enable_benchmarks: Whether to benchmark operations
    """
    
    def __init__(
        self,
        memory_limit: Optional[str] = None,
        threads: Optional[int] = None,
        enable_benchmarks: bool = True
    ):
        """Initialize DuckDB transformer.
        
        Args:
            memory_limit: Maximum memory to use (e.g., '4GB')
            threads: Number of threads for parallel processing
            enable_benchmarks: Enable performance benchmarking
        """
        self.enable_benchmarks = enable_benchmarks
        self._benchmarks: Dict[str, float] = {}
        bind(module="DuckDBTransformer")
        
        # Initialize DuckDB connection
        self.conn = duckdb.connect(':memory:')
        
        # Configure DuckDB settings
        if memory_limit:
            self.conn.execute(f"SET memory_limit='{memory_limit}'")
        if threads:
            self.conn.execute(f"SET threads={threads}")
            
        # Enable progress bar for long queries
        self.conn.execute("SET enable_progress_bar=true")
        
        info("Initialized DuckDB transformer",
             memory_limit=memory_limit,
             threads=threads or "auto")
    
    def benchmark(self, operation: str):
        """Context manager for benchmarking operations."""
        class BenchmarkContext:
            def __init__(self, transformer, name):
                self.transformer = transformer
                self.name = name
                self.start_time = None
                
            def __enter__(self):
                if self.transformer.enable_benchmarks:
                    self.start_time = time.perf_counter()
                return self
                
            def __exit__(self, exc_type, exc_val, exc_tb):
                if self.transformer.enable_benchmarks and self.start_time:
                    elapsed = time.perf_counter() - self.start_time
                    self.transformer._benchmarks[self.name] = elapsed
                    debug(f"Benchmark {self.name}: {elapsed:.4f}s")
                    
        return BenchmarkContext(self, operation)
    
    def register_dataframe(
        self, 
        df: Union[pd.DataFrame, pl.DataFrame],
        name: str
    ) -> None:
        """Register a DataFrame with DuckDB for SQL operations.
        
        Args:
            df: pandas or Polars DataFrame
            name: Table name for SQL queries
        """
        with self.benchmark(f"register_{name}"):
            if isinstance(df, pl.DataFrame):
                # Convert Polars to Arrow for zero-copy transfer
                arrow_table = df.to_arrow()
                self.conn.register(name, arrow_table)
            else:
                # Register pandas DataFrame
                self.conn.register(name, df)
                
            info(f"Registered table '{name}'", 
                 rows=len(df),
                 columns=len(df.columns))
    
    def create_balanced_panel(
        self,
        prices_table: str,
        commodities: List[str],
        markets: List[str],
        start_date: str,
        end_date: str
    ) -> pd.DataFrame:
        """Create balanced panel using DuckDB SQL operations.
        
        Args:
            prices_table: Name of registered prices table
            commodities: List of commodities
            markets: List of markets  
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            Balanced panel DataFrame
        """
        with timer("create_balanced_panel_duckdb"):
            info("Creating balanced panel with DuckDB")
            
            # Create temporary tables for dimensions
            with self.benchmark("create_dimensions"):
                self.conn.execute(f"""
                    CREATE TEMPORARY TABLE commodities AS
                    SELECT unnest({commodities!r}) as commodity
                """)
                
                self.conn.execute(f"""
                    CREATE TEMPORARY TABLE markets AS  
                    SELECT unnest({markets!r}) as market_id
                """)
                
                self.conn.execute(f"""
                    CREATE TEMPORARY TABLE date_range AS
                    SELECT generate_series(
                        DATE '{start_date}',
                        DATE '{end_date}',
                        INTERVAL '1 month'
                    )::DATE as date
                """)
            
            # Create balanced panel structure
            with self.benchmark("create_panel_structure"):
                query = f"""
                    CREATE TEMPORARY TABLE panel_structure AS
                    SELECT 
                        c.commodity,
                        m.market_id,
                        d.date,
                        YEAR(d.date) as year,
                        MONTH(d.date) as month,
                        STRFTIME(d.date, '%Y-%m') as year_month
                    FROM commodities c
                    CROSS JOIN markets m
                    CROSS JOIN date_range d
                """
                self.conn.execute(query)
            
            # Join with actual price data
            with self.benchmark("merge_prices"):
                result = self.conn.execute(f"""
                    SELECT 
                        ps.*,
                        p.price_usd,
                        p.price_local,
                        p.unit,
                        p.governorate,
                        p.district,
                        p.lat,
                        p.lon
                    FROM panel_structure ps
                    LEFT JOIN {prices_table} p
                        ON ps.commodity = p.commodity
                        AND ps.market_id = p.market_id
                        AND ps.date = p.date
                    ORDER BY ps.commodity, ps.market_id, ps.date
                """).df()
            
            log_data_shape("balanced_panel", result)
            
            return result
    
    def aggregate_conflict_intensity(
        self,
        conflict_table: str,
        level: str = 'governorate'
    ) -> pd.DataFrame:
        """Aggregate conflict data to calculate intensity metrics.
        
        Args:
            conflict_table: Name of registered conflict table
            level: Aggregation level ('governorate' or 'district')
            
        Returns:
            Aggregated conflict intensity DataFrame
        """
        with timer("aggregate_conflict_intensity_duckdb"):
            info(f"Aggregating conflict intensity at {level} level")
            
            with self.benchmark("conflict_aggregation"):
                query = f"""
                    WITH monthly_conflict AS (
                        SELECT 
                            {level},
                            year_month,
                            SUM(n_events) as total_events,
                            SUM(total_fatalities) as total_fatalities,
                            SUM(n_battles) as n_battles,
                            SUM(n_explosions) as n_explosions,
                            SUM(n_violence_civilians) as n_violence_civilians
                        FROM {conflict_table}
                        GROUP BY {level}, year_month
                    ),
                    intensity_calc AS (
                        SELECT 
                            *,
                            -- Calculate conflict intensity score
                            (total_events * 0.3 + 
                             total_fatalities * 0.4 + 
                             n_battles * 0.2 + 
                             n_violence_civilians * 0.1) as conflict_intensity,
                            -- Calculate moving averages
                            AVG(total_events) OVER (
                                PARTITION BY {level} 
                                ORDER BY year_month 
                                ROWS BETWEEN 2 PRECEDING AND CURRENT ROW
                            ) as events_ma3,
                            AVG(total_fatalities) OVER (
                                PARTITION BY {level}
                                ORDER BY year_month
                                ROWS BETWEEN 2 PRECEDING AND CURRENT ROW  
                            ) as fatalities_ma3
                        FROM monthly_conflict
                    )
                    SELECT 
                        *,
                        -- Add lags for threshold models
                        LAG(conflict_intensity, 1) OVER (
                            PARTITION BY {level} ORDER BY year_month
                        ) as conflict_intensity_lag1,
                        LAG(conflict_intensity, 2) OVER (
                            PARTITION BY {level} ORDER BY year_month
                        ) as conflict_intensity_lag2,
                        LAG(conflict_intensity, 3) OVER (
                            PARTITION BY {level} ORDER BY year_month
                        ) as conflict_intensity_lag3,
                        AVG(conflict_intensity) OVER (
                            PARTITION BY {level}
                            ORDER BY year_month
                            ROWS BETWEEN 2 PRECEDING AND CURRENT ROW
                        ) as conflict_ma3
                    FROM intensity_calc
                    ORDER BY {level}, year_month
                """
                
                result = self.conn.execute(query).df()
            
            log_data_shape("conflict_intensity", result)
            
            return result
    
    def merge_panel_components(
        self,
        panel_table: str,
        conflict_table: str,
        zones_table: str,
        exchange_table: Optional[str] = None
    ) -> pd.DataFrame:
        """Merge all panel components using efficient SQL joins.
        
        Args:
            panel_table: Name of base panel table
            conflict_table: Name of conflict intensity table
            zones_table: Name of control zones table
            exchange_table: Optional exchange rates table
            
        Returns:
            Integrated panel DataFrame
        """
        with timer("merge_panel_components_duckdb"):
            info("Merging panel components with DuckDB")
            
            # Build join query
            with self.benchmark("build_merge_query"):
                query = f"""
                    SELECT 
                        p.*,
                        -- Control zone information
                        z.control_zone,
                        z.zone_change_date,
                        -- Conflict metrics
                        c.conflict_intensity,
                        c.total_fatalities,
                        c.n_battles,
                        c.n_explosions,
                        c.n_violence_civilians,
                        c.conflict_intensity_lag1,
                        c.conflict_intensity_lag2,
                        c.conflict_intensity_lag3,
                        c.conflict_ma3
                """
                
                if exchange_table:
                    query += """
                        ,
                        -- Exchange rate data
                        e.official_rate,
                        e.parallel_rate,
                        e.rate_differential,
                        e.rate_premium
                    """
                
                query += f"""
                    FROM {panel_table} p
                    LEFT JOIN {zones_table} z
                        ON p.governorate = z.governorate
                        AND p.district = z.district
                        AND p.year_month = z.year_month
                    LEFT JOIN {conflict_table} c
                        ON p.governorate = c.governorate
                        AND p.year_month = c.year_month
                """
                
                if exchange_table:
                    query += f"""
                    LEFT JOIN {exchange_table} e
                        ON p.market_id = e.market_id
                        AND p.year_month = e.year_month
                    """
                
                query += """
                    ORDER BY p.commodity, p.market_id, p.date
                """
            
            # Execute merge
            with self.benchmark("execute_merge"):
                result = self.conn.execute(query).df()
            
            log_data_shape("integrated_panel", result)
            
            return result
    
    def calculate_price_indices(
        self,
        panel_table: str,
        base_period: str = '2019-01'
    ) -> pd.DataFrame:
        """Calculate price indices and inflation metrics.
        
        Args:
            panel_table: Name of panel table with prices
            base_period: Base period for index calculation
            
        Returns:
            DataFrame with price indices
        """
        with timer("calculate_price_indices_duckdb"):
            info(f"Calculating price indices with base period {base_period}")
            
            with self.benchmark("price_indices"):
                query = f"""
                    WITH base_prices AS (
                        SELECT 
                            commodity,
                            market_id,
                            AVG(price_usd) as base_price
                        FROM {panel_table}
                        WHERE year_month = '{base_period}'
                        AND price_usd IS NOT NULL
                        GROUP BY commodity, market_id
                    ),
                    price_changes AS (
                        SELECT 
                            p.*,
                            bp.base_price,
                            -- Price index (base = 100)
                            CASE 
                                WHEN bp.base_price > 0 
                                THEN (p.price_usd / bp.base_price) * 100
                                ELSE NULL
                            END as price_index,
                            -- Month-on-month change
                            LAG(p.price_usd) OVER (
                                PARTITION BY p.commodity, p.market_id 
                                ORDER BY p.date
                            ) as price_lag1,
                            -- Year-on-year change  
                            LAG(p.price_usd, 12) OVER (
                                PARTITION BY p.commodity, p.market_id
                                ORDER BY p.date
                            ) as price_lag12
                        FROM {panel_table} p
                        LEFT JOIN base_prices bp
                            ON p.commodity = bp.commodity
                            AND p.market_id = bp.market_id
                    )
                    SELECT 
                        *,
                        -- Calculate inflation rates
                        CASE 
                            WHEN price_lag1 > 0
                            THEN ((price_usd - price_lag1) / price_lag1) * 100
                            ELSE NULL
                        END as inflation_mom,
                        CASE
                            WHEN price_lag12 > 0
                            THEN ((price_usd - price_lag12) / price_lag12) * 100
                            ELSE NULL
                        END as inflation_yoy,
                        -- Price volatility (rolling std dev)
                        STDDEV(price_usd) OVER (
                            PARTITION BY commodity, market_id
                            ORDER BY date
                            ROWS BETWEEN 11 PRECEDING AND CURRENT ROW
                        ) as price_volatility
                    FROM price_changes
                    ORDER BY commodity, market_id, date
                """
                
                result = self.conn.execute(query).df()
            
            log_data_shape("price_indices", result)
            
            return result
    
    def create_market_pairs(
        self,
        panel_table: str,
        max_distance_km: float = 500.0
    ) -> pd.DataFrame:
        """Create market pairs for spatial price transmission analysis.
        
        Args:
            panel_table: Name of panel table
            max_distance_km: Maximum distance between markets
            
        Returns:
            DataFrame with market pairs and price differentials
        """
        with timer("create_market_pairs_duckdb"):
            info(f"Creating market pairs within {max_distance_km}km")
            
            with self.benchmark("market_pairs"):
                query = f"""
                    WITH market_locations AS (
                        SELECT DISTINCT
                            market_id,
                            governorate,
                            district,
                            lat,
                            lon,
                            control_zone
                        FROM {panel_table}
                        WHERE lat IS NOT NULL AND lon IS NOT NULL
                    ),
                    market_distances AS (
                        SELECT 
                            m1.market_id as market1_id,
                            m2.market_id as market2_id,
                            m1.governorate as market1_gov,
                            m2.governorate as market2_gov,
                            m1.control_zone as market1_zone,
                            m2.control_zone as market2_zone,
                            -- Haversine distance formula
                            6371 * 2 * ASIN(SQRT(
                                POWER(SIN((m2.lat - m1.lat) * PI() / 180 / 2), 2) +
                                COS(m1.lat * PI() / 180) * COS(m2.lat * PI() / 180) *
                                POWER(SIN((m2.lon - m1.lon) * PI() / 180 / 2), 2)
                            )) as distance_km
                        FROM market_locations m1
                        CROSS JOIN market_locations m2
                        WHERE m1.market_id < m2.market_id  -- Avoid duplicates
                    )
                    SELECT 
                        md.*,
                        p1.commodity,
                        p1.date,
                        p1.year_month,
                        p1.price_usd as price1,
                        p2.price_usd as price2,
                        ABS(p1.price_usd - p2.price_usd) as price_diff,
                        CASE 
                            WHEN p2.price_usd > 0
                            THEN ABS(p1.price_usd - p2.price_usd) / p2.price_usd * 100
                            ELSE NULL
                        END as price_diff_pct,
                        -- Same zone indicator
                        CASE 
                            WHEN md.market1_zone = md.market2_zone 
                            THEN 1 ELSE 0 
                        END as same_zone
                    FROM market_distances md
                    JOIN {panel_table} p1
                        ON md.market1_id = p1.market_id
                    JOIN {panel_table} p2
                        ON md.market2_id = p2.market_id
                        AND p1.commodity = p2.commodity
                        AND p1.date = p2.date
                    WHERE md.distance_km <= {max_distance_km}
                    AND p1.price_usd IS NOT NULL
                    AND p2.price_usd IS NOT NULL
                    ORDER BY p1.commodity, p1.date, md.market1_id, md.market2_id
                """
                
                result = self.conn.execute(query).df()
            
            log_data_shape("market_pairs", result)
            
            return result
    
    def export_to_parquet(self, table_name: str, output_path: Path) -> None:
        """Export table to Parquet format for efficient storage.
        
        Args:
            table_name: Name of table to export
            output_path: Output path for Parquet file
        """
        with self.benchmark(f"export_{table_name}"):
            query = f"COPY {table_name} TO '{output_path}' (FORMAT PARQUET)"
            self.conn.execute(query)
            info(f"Exported {table_name} to {output_path}")
    
    def compare_with_pandas(
        self,
        operation: Callable,
        pandas_func: Callable,
        *args,
        **kwargs
    ) -> Dict[str, Any]:
        """Compare DuckDB performance with pandas baseline.
        
        Args:
            operation: DuckDB operation to benchmark
            pandas_func: Equivalent pandas operation
            *args, **kwargs: Arguments for operations
            
        Returns:
            Performance comparison results
        """
        results = {}
        
        # DuckDB benchmark
        start = time.perf_counter()
        duckdb_result = operation(*args, **kwargs)
        duckdb_time = time.perf_counter() - start
        results['duckdb_time'] = duckdb_time
        
        # Pandas benchmark
        start = time.perf_counter()
        pandas_result = pandas_func(*args, **kwargs)
        pandas_time = time.perf_counter() - start
        results['pandas_time'] = pandas_time
        
        # Calculate speedup
        results['speedup'] = pandas_time / duckdb_time
        
        info("Performance comparison:",
             duckdb_time=f"{duckdb_time:.4f}s",
             pandas_time=f"{pandas_time:.4f}s",
             speedup=f"{results['speedup']:.2f}x")
        
        return results
    
    def _report_benchmarks(self):
        """Report performance benchmarks."""
        if not self._benchmarks:
            return
            
        info("DuckDB Performance benchmarks:")
        total_time = sum(self._benchmarks.values())
        
        for operation, duration in sorted(self._benchmarks.items()):
            percentage = (duration / total_time) * 100 if total_time > 0 else 0
            info(f"  {operation}: {duration:.4f}s ({percentage:.1f}%)")
            
        info(f"  Total: {total_time:.4f}s")
    
    def close(self):
        """Close DuckDB connection and report benchmarks."""
        if self.enable_benchmarks:
            self._report_benchmarks()
        self.conn.close()
        
    def __enter__(self):
        """Context manager entry."""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()