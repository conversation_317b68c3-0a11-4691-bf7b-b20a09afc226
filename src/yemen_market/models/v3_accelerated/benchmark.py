"""Performance benchmarking for V3 accelerated models.

This module provides tools to measure and compare performance between
standard implementations and accelerated versions using MLX and Ray.
"""

import numpy as np
import pandas as pd
import time
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
import psutil
import gc
from contextlib import contextmanager

from yemen_market.utils.logging import info, timer, log_data_shape
from .mlx_operations import (
    MLXAccelerator, MLXPanelEstimator, MLXFactorAnalyzer, MLXVECMEstimator
)
from .ray_distributed import RayDistributedRunner, RayModelEstimator


@dataclass
class BenchmarkResult:
    """Result of a single benchmark run."""
    operation: str
    implementation: str
    execution_time: float
    memory_usage_mb: float
    accuracy_check: bool
    speedup: Optional[float] = None
    additional_metrics: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AccelerationMetrics:
    """Overall acceleration metrics for a set of benchmarks."""
    total_speedup: float
    average_speedup: float
    memory_reduction: float
    operations: Dict[str, Dict[str, float]]
    recommendations: List[str]


class PerformanceBenchmark:
    """Benchmark suite for comparing accelerated vs standard implementations."""
    
    def __init__(self, enable_mlx: bool = True, enable_ray: bool = True):
        """Initialize benchmark suite.
        
        Parameters
        ----------
        enable_mlx : bool
            Whether to benchmark MLX implementations
        enable_ray : bool
            Whether to benchmark Ray implementations
        """
        self.enable_mlx = enable_mlx
        self.enable_ray = enable_ray
        self.results: List[BenchmarkResult] = []
        
        # Initialize accelerators
        self.mlx_accelerator = MLXAccelerator() if enable_mlx else None
        self.ray_runner = RayDistributedRunner() if enable_ray else None
    
    @contextmanager
    def measure_performance(self, operation: str):
        """Context manager to measure execution time and memory usage.
        
        Parameters
        ----------
        operation : str
            Name of the operation being measured
        
        Yields
        ------
        dict
            Dictionary to store results
        """
        # Force garbage collection before measurement
        gc.collect()
        
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Start timer
        start_time = time.time()
        
        # Dictionary to store results
        result = {}
        
        try:
            yield result
        finally:
            # End timer
            execution_time = time.time() - start_time
            
            # Get final memory usage
            gc.collect()
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_usage = final_memory - initial_memory
            
            # Store metrics
            result['operation'] = operation
            result['execution_time'] = execution_time
            result['memory_usage_mb'] = memory_usage
    
    def benchmark_matrix_operations(self, sizes: List[int] = [100, 500, 1000, 2000]) -> List[BenchmarkResult]:
        """Benchmark basic matrix operations.
        
        Parameters
        ----------
        sizes : List[int]
            Matrix sizes to benchmark
        
        Returns
        -------
        results : List[BenchmarkResult]
            Benchmark results
        """
        info("Benchmarking matrix operations")
        results = []
        
        for size in sizes:
            info(f"Testing matrix size: {size}x{size}")
            
            # Generate test data
            np.random.seed(42)
            A = np.random.randn(size, size)
            b = np.random.randn(size)
            
            # Benchmark NumPy
            with self.measure_performance(f"lstsq_{size}") as metrics:
                x_numpy, _ = np.linalg.lstsq(A, b, rcond=None)[:2]
                metrics['implementation'] = 'numpy'
            
            numpy_time = metrics['execution_time']
            results.append(BenchmarkResult(
                operation=f"lstsq_{size}",
                implementation="numpy",
                execution_time=numpy_time,
                memory_usage_mb=metrics['memory_usage_mb'],
                accuracy_check=True
            ))
            
            # Benchmark MLX
            if self.enable_mlx and self.mlx_accelerator:
                with self.measure_performance(f"lstsq_{size}") as metrics:
                    x_mlx, _ = self.mlx_accelerator.lstsq(A, b)
                    metrics['implementation'] = 'mlx'
                
                # Check accuracy
                accuracy_check = np.allclose(x_numpy, x_mlx, rtol=1e-5)
                speedup = numpy_time / metrics['execution_time']
                
                results.append(BenchmarkResult(
                    operation=f"lstsq_{size}",
                    implementation="mlx",
                    execution_time=metrics['execution_time'],
                    memory_usage_mb=metrics['memory_usage_mb'],
                    accuracy_check=accuracy_check,
                    speedup=speedup
                ))
                
                info(f"  MLX speedup: {speedup:.2f}x, Accuracy: {'✓' if accuracy_check else '✗'}")
        
        self.results.extend(results)
        return results
    
    def benchmark_panel_estimation(self, panel_sizes: List[Tuple[int, int, int]] = None) -> List[BenchmarkResult]:
        """Benchmark panel model estimation.
        
        Parameters
        ----------
        panel_sizes : List[Tuple[int, int, int]]
            List of (n_entities, n_periods, n_features) to test
        
        Returns
        -------
        results : List[BenchmarkResult]
            Benchmark results
        """
        if panel_sizes is None:
            panel_sizes = [(50, 100, 5), (100, 200, 10), (200, 400, 15)]
        
        info("Benchmarking panel estimation")
        results = []
        
        for n_entities, n_periods, n_features in panel_sizes:
            info(f"Testing panel: {n_entities} entities × {n_periods} periods × {n_features} features")
            
            # Generate synthetic panel data
            np.random.seed(42)
            n_obs = n_entities * n_periods
            
            # Create balanced panel structure
            entity_ids = np.repeat(np.arange(n_entities), n_periods)
            time_ids = np.tile(np.arange(n_periods), n_entities)
            
            # Generate features and target
            X = np.random.randn(n_obs, n_features)
            true_beta = np.random.randn(n_features)
            entity_effects = np.random.randn(n_entities)
            y = X @ true_beta + entity_effects[entity_ids] + np.random.randn(n_obs) * 0.5
            
            # Benchmark standard fixed effects (within transformation)
            with self.measure_performance(f"panel_fe_{n_entities}x{n_periods}") as metrics:
                # Within transformation
                X_demeaned = np.zeros_like(X)
                y_demeaned = np.zeros_like(y)
                
                for entity in range(n_entities):
                    mask = entity_ids == entity
                    X_demeaned[mask] = X[mask] - X[mask].mean(axis=0)
                    y_demeaned[mask] = y[mask] - y[mask].mean()
                
                beta_numpy = np.linalg.lstsq(X_demeaned, y_demeaned, rcond=None)[0]
                metrics['implementation'] = 'numpy'
            
            numpy_time = metrics['execution_time']
            results.append(BenchmarkResult(
                operation=f"panel_fe_{n_entities}x{n_periods}",
                implementation="numpy",
                execution_time=numpy_time,
                memory_usage_mb=metrics['memory_usage_mb'],
                accuracy_check=True
            ))
            
            # Benchmark MLX panel estimation
            if self.enable_mlx and self.mlx_accelerator:
                mlx_estimator = MLXPanelEstimator()
                
                with self.measure_performance(f"panel_fe_{n_entities}x{n_periods}") as metrics:
                    mlx_results = mlx_estimator.estimate_fixed_effects(X, y, entity_ids, time_ids)
                    beta_mlx = mlx_results['coefficients']
                    metrics['implementation'] = 'mlx'
                
                accuracy_check = np.allclose(beta_numpy, beta_mlx, rtol=1e-5)
                speedup = numpy_time / metrics['execution_time']
                
                results.append(BenchmarkResult(
                    operation=f"panel_fe_{n_entities}x{n_periods}",
                    implementation="mlx",
                    execution_time=metrics['execution_time'],
                    memory_usage_mb=metrics['memory_usage_mb'],
                    accuracy_check=accuracy_check,
                    speedup=speedup
                ))
                
                info(f"  MLX speedup: {speedup:.2f}x, Accuracy: {'✓' if accuracy_check else '✗'}")
        
        self.results.extend(results)
        return results
    
    def benchmark_parallel_estimation(self, n_commodities: List[int] = [5, 10, 20]) -> List[BenchmarkResult]:
        """Benchmark parallel commodity estimation.
        
        Parameters
        ----------
        n_commodities : List[int]
            Number of commodities to estimate in parallel
        
        Returns
        -------
        results : List[BenchmarkResult]
            Benchmark results
        """
        info("Benchmarking parallel estimation")
        results = []
        
        for n_comm in n_commodities:
            info(f"Testing {n_comm} commodities in parallel")
            
            # Generate synthetic data for each commodity
            np.random.seed(42)
            commodity_data = {}
            for i in range(n_comm):
                n_obs = np.random.randint(100, 500)
                commodity_data[f"commodity_{i}"] = {
                    'X': np.random.randn(n_obs, 5),
                    'y': np.random.randn(n_obs)
                }
            
            # Benchmark sequential estimation
            with self.measure_performance(f"sequential_{n_comm}_commodities") as metrics:
                sequential_results = {}
                for comm_name, data in commodity_data.items():
                    beta = np.linalg.lstsq(data['X'], data['y'], rcond=None)[0]
                    sequential_results[comm_name] = beta
                metrics['implementation'] = 'sequential'
            
            sequential_time = metrics['execution_time']
            results.append(BenchmarkResult(
                operation=f"commodity_estimation_{n_comm}",
                implementation="sequential",
                execution_time=sequential_time,
                memory_usage_mb=metrics['memory_usage_mb'],
                accuracy_check=True
            ))
            
            # Benchmark Ray parallel estimation
            if self.enable_ray and self.ray_runner:
                def estimate_commodity(comm_data_tuple):
                    comm_name, data = comm_data_tuple
                    beta = np.linalg.lstsq(data['X'], data['y'], rcond=None)[0]
                    return comm_name, beta
                
                with self.measure_performance(f"parallel_{n_comm}_commodities") as metrics:
                    parallel_results = self.ray_runner.map(
                        estimate_commodity,
                        list(commodity_data.items()),
                        desc=f"Estimating {n_comm} commodities"
                    )
                    parallel_results = dict(parallel_results)
                    metrics['implementation'] = 'ray'
                
                # Check accuracy
                accuracy_check = all(
                    np.allclose(sequential_results[k], parallel_results[k], rtol=1e-5)
                    for k in sequential_results
                )
                speedup = sequential_time / metrics['execution_time']
                
                results.append(BenchmarkResult(
                    operation=f"commodity_estimation_{n_comm}",
                    implementation="ray",
                    execution_time=metrics['execution_time'],
                    memory_usage_mb=metrics['memory_usage_mb'],
                    accuracy_check=accuracy_check,
                    speedup=speedup
                ))
                
                info(f"  Ray speedup: {speedup:.2f}x, Accuracy: {'✓' if accuracy_check else '✗'}")
        
        self.results.extend(results)
        return results
    
    def benchmark_factor_analysis(self, dimensions: List[Tuple[int, int]] = None) -> List[BenchmarkResult]:
        """Benchmark factor analysis operations.
        
        Parameters
        ----------
        dimensions : List[Tuple[int, int]]
            List of (n_samples, n_features) to test
        
        Returns
        -------
        results : List[BenchmarkResult]
            Benchmark results
        """
        if dimensions is None:
            dimensions = [(1000, 50), (5000, 100), (10000, 200)]
        
        info("Benchmarking factor analysis")
        results = []
        
        for n_samples, n_features in dimensions:
            info(f"Testing PCA: {n_samples} samples × {n_features} features")
            
            # Generate test data
            np.random.seed(42)
            X = np.random.randn(n_samples, n_features)
            n_components = min(10, n_features // 2)
            
            # Benchmark NumPy PCA
            with self.measure_performance(f"pca_{n_samples}x{n_features}") as metrics:
                X_centered = X - X.mean(axis=0)
                cov_matrix = np.cov(X_centered, rowvar=False)
                eigenvalues, eigenvectors = np.linalg.eigh(cov_matrix)
                idx = np.argsort(eigenvalues)[::-1][:n_components]
                components_numpy = eigenvectors[:, idx].T
                metrics['implementation'] = 'numpy'
            
            numpy_time = metrics['execution_time']
            results.append(BenchmarkResult(
                operation=f"pca_{n_samples}x{n_features}",
                implementation="numpy",
                execution_time=numpy_time,
                memory_usage_mb=metrics['memory_usage_mb'],
                accuracy_check=True
            ))
            
            # Benchmark MLX PCA
            if self.enable_mlx and self.mlx_accelerator:
                mlx_analyzer = MLXFactorAnalyzer()
                
                with self.measure_performance(f"pca_{n_samples}x{n_features}") as metrics:
                    pca_results = mlx_analyzer.pca(X, n_components)
                    components_mlx = pca_results['components']
                    metrics['implementation'] = 'mlx'
                
                # Check accuracy (components may have different signs)
                accuracy_check = np.allclose(
                    np.abs(components_numpy),
                    np.abs(components_mlx),
                    rtol=1e-4
                )
                speedup = numpy_time / metrics['execution_time']
                
                results.append(BenchmarkResult(
                    operation=f"pca_{n_samples}x{n_features}",
                    implementation="mlx",
                    execution_time=metrics['execution_time'],
                    memory_usage_mb=metrics['memory_usage_mb'],
                    accuracy_check=accuracy_check,
                    speedup=speedup
                ))
                
                info(f"  MLX speedup: {speedup:.2f}x, Accuracy: {'✓' if accuracy_check else '✗'}")
        
        self.results.extend(results)
        return results
    
    def generate_report(self) -> AccelerationMetrics:
        """Generate comprehensive performance report.
        
        Returns
        -------
        metrics : AccelerationMetrics
            Summary of acceleration performance
        """
        if not self.results:
            raise ValueError("No benchmark results available. Run benchmarks first.")
        
        # Group results by operation and implementation
        operations = {}
        for result in self.results:
            if result.operation not in operations:
                operations[result.operation] = {}
            operations[result.operation][result.implementation] = {
                'time': result.execution_time,
                'memory': result.memory_usage_mb,
                'speedup': result.speedup or 1.0,
                'accurate': result.accuracy_check
            }
        
        # Calculate overall metrics
        total_speedups = []
        memory_reductions = []
        
        for op, impls in operations.items():
            if 'numpy' in impls or 'sequential' in impls:
                baseline = impls.get('numpy', impls.get('sequential'))
                for impl_name, impl_data in impls.items():
                    if impl_name not in ['numpy', 'sequential']:
                        speedup = baseline['time'] / impl_data['time']
                        memory_reduction = 1 - (impl_data['memory'] / baseline['memory'])
                        
                        total_speedups.append(speedup)
                        if memory_reduction > 0:
                            memory_reductions.append(memory_reduction)
        
        # Generate recommendations
        recommendations = []
        
        # Check MLX performance
        mlx_speedups = [r.speedup for r in self.results 
                       if r.implementation == 'mlx' and r.speedup]
        if mlx_speedups:
            avg_mlx_speedup = np.mean(mlx_speedups)
            if avg_mlx_speedup > 2.0:
                recommendations.append(
                    f"MLX acceleration is highly effective ({avg_mlx_speedup:.1f}x average speedup). "
                    "Prioritize MLX for matrix-heavy operations."
                )
            elif avg_mlx_speedup > 1.2:
                recommendations.append(
                    f"MLX provides moderate speedup ({avg_mlx_speedup:.1f}x). "
                    "Use for large matrices and iterative algorithms."
                )
            else:
                recommendations.append(
                    "MLX speedup is limited. Consider for very large matrices only."
                )
        
        # Check Ray performance
        ray_speedups = [r.speedup for r in self.results 
                       if r.implementation == 'ray' and r.speedup]
        if ray_speedups:
            avg_ray_speedup = np.mean(ray_speedups)
            if avg_ray_speedup > 3.0:
                recommendations.append(
                    f"Ray parallelization is very effective ({avg_ray_speedup:.1f}x speedup). "
                    "Use for all independent commodity/market analyses."
                )
            elif avg_ray_speedup > 1.5:
                recommendations.append(
                    f"Ray provides good parallelization ({avg_ray_speedup:.1f}x). "
                    "Use when processing >5 independent units."
                )
            else:
                recommendations.append(
                    "Ray overhead may exceed benefits for small workloads. "
                    "Use only for large-scale parallel tasks."
                )
        
        # Memory usage recommendations
        if memory_reductions:
            avg_memory_reduction = np.mean(memory_reductions)
            if avg_memory_reduction > 0.3:
                recommendations.append(
                    f"Significant memory reduction achieved ({avg_memory_reduction:.0%}). "
                    "Accelerated implementations enable larger dataset processing."
                )
        
        return AccelerationMetrics(
            total_speedup=np.prod(total_speedups) ** (1/len(total_speedups)) if total_speedups else 1.0,
            average_speedup=np.mean(total_speedups) if total_speedups else 1.0,
            memory_reduction=np.mean(memory_reductions) if memory_reductions else 0.0,
            operations=operations,
            recommendations=recommendations
        )
    
    def print_report(self, metrics: AccelerationMetrics):
        """Print formatted performance report.
        
        Parameters
        ----------
        metrics : AccelerationMetrics
            Metrics to print
        """
        print("\n" + "="*80)
        print("V3 ACCELERATION PERFORMANCE REPORT")
        print("="*80)
        
        print(f"\nOverall Performance Gains:")
        print(f"  - Average Speedup: {metrics.average_speedup:.1f}x")
        print(f"  - Memory Reduction: {metrics.memory_reduction:.0%}")
        
        print(f"\nDetailed Results by Operation:")
        for op, impls in metrics.operations.items():
            print(f"\n  {op}:")
            for impl, data in impls.items():
                print(f"    {impl:12s}: {data['time']:6.3f}s, "
                      f"{data['memory']:6.1f}MB, "
                      f"Speedup: {data['speedup']:4.1f}x, "
                      f"Accurate: {'✓' if data['accurate'] else '✗'}")
        
        print(f"\nRecommendations:")
        for i, rec in enumerate(metrics.recommendations, 1):
            print(f"  {i}. {rec}")
        
        print("\n" + "="*80)
    
    def cleanup(self):
        """Clean up resources."""
        if self.ray_runner:
            self.ray_runner.shutdown()


def run_full_benchmark():
    """Run complete benchmark suite and generate report."""
    benchmark = PerformanceBenchmark(enable_mlx=True, enable_ray=True)
    
    try:
        # Run all benchmarks
        info("Starting V3 acceleration benchmarks")
        
        benchmark.benchmark_matrix_operations()
        benchmark.benchmark_panel_estimation()
        benchmark.benchmark_parallel_estimation()
        benchmark.benchmark_factor_analysis()
        
        # Generate and print report
        metrics = benchmark.generate_report()
        benchmark.print_report(metrics)
        
        return metrics
        
    finally:
        benchmark.cleanup()


if __name__ == "__main__":
    # Run benchmarks when module is executed directly
    run_full_benchmark()