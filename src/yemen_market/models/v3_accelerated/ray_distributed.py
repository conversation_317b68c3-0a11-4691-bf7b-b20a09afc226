"""Ray-distributed operations for parallel model estimation.

This module provides distributed computing capabilities for econometric models
using <PERSON> to parallelize across CPU cores and potentially multiple machines.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass
import warnings
from concurrent.futures import as_completed

try:
    import ray
    RAY_AVAILABLE = True
except ImportError:
    RAY_AVAILABLE = False
    warnings.warn("Ray not available. Falling back to sequential processing.")

from yemen_market.utils.logging import info, timer, progress, log_data_shape
from yemen_market.models.three_tier.tier1_pooled.pooled_panel_model import PooledPanelModel
from yemen_market.models.three_tier.tier2_commodity.commodity_specific_model import CommoditySpecificModel
from yemen_market.models.three_tier.tier3_validation.pca_analysis import PCAAnalysis


@dataclass
class DistributedConfig:
    """Configuration for Ray distributed processing."""
    n_workers: Optional[int] = None  # None = use all available cores
    memory_per_worker_gb: float = 2.0
    object_store_memory_gb: float = 4.0
    include_dashboard: bool = False
    local_mode: bool = False  # For debugging


class RayDistributedRunner:
    """Distributed runner for parallel model estimation using Ray."""
    
    def __init__(self, config: Optional[DistributedConfig] = None):
        """Initialize Ray distributed runner.
        
        Parameters
        ----------
        config : DistributedConfig, optional
            Configuration for distributed processing
        """
        self.config = config or DistributedConfig()
        self.ray_initialized = False
        
        if RAY_AVAILABLE:
            self._initialize_ray()
        else:
            info("Ray not available, using sequential processing")
    
    def _initialize_ray(self):
        """Initialize Ray with appropriate configuration."""
        if not ray.is_initialized():
            ray_config = {
                "num_cpus": self.config.n_workers,
                "object_store_memory": int(self.config.object_store_memory_gb * 1e9),
                "include_dashboard": self.config.include_dashboard,
                "local_mode": self.config.local_mode
            }
            ray.init(**{k: v for k, v in ray_config.items() if v is not None})
            self.ray_initialized = True
            info(f"Ray initialized with {ray.available_resources()}")
    
    def shutdown(self):
        """Shutdown Ray if it was initialized."""
        if self.ray_initialized and ray.is_initialized():
            ray.shutdown()
            self.ray_initialized = False
    
    def __del__(self):
        """Cleanup Ray on object destruction."""
        self.shutdown()
    
    def map(self, func: Callable, items: List[Any], desc: str = "Processing") -> List[Any]:
        """Map a function over items in parallel.
        
        Parameters
        ----------
        func : Callable
            Function to apply to each item
        items : List[Any]
            Items to process
        desc : str
            Description for progress bar
        
        Returns
        -------
        results : List[Any]
            Results in the same order as input items
        """
        if not RAY_AVAILABLE or len(items) <= 1:
            # Sequential fallback
            results = []
            with progress(desc, total=len(items)) as update:
                for item in items:
                    results.append(func(item))
                    update(1)
            return results
        
        # Ray parallel execution
        @ray.remote
        def ray_func(item):
            return func(item)
        
        # Submit all tasks
        futures = [ray_func.remote(item) for item in items]
        
        # Collect results with progress bar
        results = [None] * len(items)
        with progress(desc, total=len(items)) as update:
            for i, future in enumerate(futures):
                results[i] = ray.get(future)
                update(1)
        
        return results
    
    def map_with_shared_data(
        self, 
        func: Callable, 
        items: List[Any], 
        shared_data: Any,
        desc: str = "Processing"
    ) -> List[Any]:
        """Map a function over items with shared read-only data.
        
        Parameters
        ----------
        func : Callable
            Function that takes (item, shared_data) as arguments
        items : List[Any]
            Items to process
        shared_data : Any
            Data to be shared across all workers (read-only)
        desc : str
            Description for progress bar
        
        Returns
        -------
        results : List[Any]
            Results in the same order as input items
        """
        if not RAY_AVAILABLE:
            # Sequential fallback
            results = []
            with progress(desc, total=len(items)) as update:
                for item in items:
                    results.append(func(item, shared_data))
                    update(1)
            return results
        
        # Put shared data in Ray object store
        shared_data_ref = ray.put(shared_data)
        
        @ray.remote
        def ray_func(item, shared_ref):
            return func(item, ray.get(shared_ref))
        
        # Submit all tasks
        futures = [ray_func.remote(item, shared_data_ref) for item in items]
        
        # Collect results
        results = [None] * len(items)
        with progress(desc, total=len(items)) as update:
            for i, future in enumerate(futures):
                results[i] = ray.get(future)
                update(1)
        
        return results


class RayModelEstimator:
    """Distributed model estimation for econometric models."""
    
    def __init__(self, runner: Optional[RayDistributedRunner] = None):
        """Initialize model estimator.
        
        Parameters
        ----------
        runner : RayDistributedRunner, optional
            Distributed runner instance
        """
        self.runner = runner or RayDistributedRunner()
    
    def estimate_commodity_models(
        self,
        panel_data: pd.DataFrame,
        commodities: List[str],
        model_class: type = CommoditySpecificModel,
        **model_kwargs
    ) -> Dict[str, Any]:
        """Estimate commodity-specific models in parallel.
        
        Parameters
        ----------
        panel_data : pd.DataFrame
            Panel data with all commodities
        commodities : List[str]
            List of commodity names to estimate
        model_class : type
            Model class to use for estimation
        **model_kwargs : dict
            Additional arguments for model initialization
        
        Returns
        -------
        results : Dict[str, Any]
            Dictionary mapping commodity names to estimation results
        """
        with timer("distributed_commodity_estimation"):
            info(f"Estimating {len(commodities)} commodity models in parallel")
            
            def estimate_single_commodity(commodity: str) -> Tuple[str, Any]:
                """Estimate model for a single commodity."""
                # Filter data for this commodity
                commodity_data = panel_data[panel_data['commodity'] == commodity].copy()
                
                if len(commodity_data) < 10:
                    return commodity, {
                        'status': 'skipped',
                        'reason': 'insufficient_data',
                        'n_obs': len(commodity_data)
                    }
                
                try:
                    # Initialize and fit model
                    model = model_class(**model_kwargs)
                    results = model.fit(commodity_data)
                    return commodity, {
                        'status': 'success',
                        'results': results,
                        'n_obs': len(commodity_data)
                    }
                except Exception as e:
                    return commodity, {
                        'status': 'failed',
                        'error': str(e),
                        'n_obs': len(commodity_data)
                    }
            
            # Estimate all models in parallel
            commodity_results = self.runner.map(
                estimate_single_commodity,
                commodities,
                desc="Estimating commodity models"
            )
            
            # Convert to dictionary
            results_dict = dict(commodity_results)
            
            # Summary statistics
            n_success = sum(1 for r in results_dict.values() if r['status'] == 'success')
            n_failed = sum(1 for r in results_dict.values() if r['status'] == 'failed')
            n_skipped = sum(1 for r in results_dict.values() if r['status'] == 'skipped')
            
            info(f"Commodity estimation complete: {n_success} success, {n_failed} failed, {n_skipped} skipped")
            
            return results_dict
    
    def cross_validate_parallel(
        self,
        data: pd.DataFrame,
        model_class: type,
        cv_folds: int = 5,
        **model_kwargs
    ) -> Dict[str, Any]:
        """Perform parallel cross-validation.
        
        Parameters
        ----------
        data : pd.DataFrame
            Input data
        model_class : type
            Model class to validate
        cv_folds : int
            Number of cross-validation folds
        **model_kwargs : dict
            Model initialization arguments
        
        Returns
        -------
        results : Dict[str, Any]
            Cross-validation results
        """
        with timer("distributed_cross_validation"):
            info(f"Running {cv_folds}-fold cross-validation in parallel")
            
            # Create fold indices
            n_samples = len(data)
            fold_size = n_samples // cv_folds
            fold_indices = []
            
            for fold in range(cv_folds):
                start_idx = fold * fold_size
                end_idx = start_idx + fold_size if fold < cv_folds - 1 else n_samples
                test_idx = list(range(start_idx, end_idx))
                train_idx = list(range(0, start_idx)) + list(range(end_idx, n_samples))
                fold_indices.append((fold, train_idx, test_idx))
            
            def evaluate_fold(fold_info: Tuple[int, List[int], List[int]]) -> Dict[str, Any]:
                """Evaluate a single CV fold."""
                fold_num, train_idx, test_idx = fold_info
                
                # Split data
                train_data = data.iloc[train_idx].copy()
                test_data = data.iloc[test_idx].copy()
                
                try:
                    # Train model
                    model = model_class(**model_kwargs)
                    model.fit(train_data)
                    
                    # Evaluate on test set
                    predictions = model.predict(test_data)
                    
                    # Calculate metrics
                    y_true = test_data['price'].values
                    mse = np.mean((predictions - y_true) ** 2)
                    mae = np.mean(np.abs(predictions - y_true))
                    r2 = 1 - mse / np.var(y_true)
                    
                    return {
                        'fold': fold_num,
                        'status': 'success',
                        'mse': mse,
                        'mae': mae,
                        'r2': r2,
                        'n_train': len(train_idx),
                        'n_test': len(test_idx)
                    }
                except Exception as e:
                    return {
                        'fold': fold_num,
                        'status': 'failed',
                        'error': str(e)
                    }
            
            # Run folds in parallel
            fold_results = self.runner.map(
                evaluate_fold,
                fold_indices,
                desc="Cross-validation folds"
            )
            
            # Aggregate results
            successful_folds = [r for r in fold_results if r['status'] == 'success']
            
            if successful_folds:
                avg_mse = np.mean([r['mse'] for r in successful_folds])
                avg_mae = np.mean([r['mae'] for r in successful_folds])
                avg_r2 = np.mean([r['r2'] for r in successful_folds])
                std_mse = np.std([r['mse'] for r in successful_folds])
                std_mae = np.std([r['mae'] for r in successful_folds])
                std_r2 = np.std([r['r2'] for r in successful_folds])
            else:
                avg_mse = avg_mae = avg_r2 = np.nan
                std_mse = std_mae = std_r2 = np.nan
            
            return {
                'fold_results': fold_results,
                'n_successful_folds': len(successful_folds),
                'metrics': {
                    'mse': {'mean': avg_mse, 'std': std_mse},
                    'mae': {'mean': avg_mae, 'std': std_mae},
                    'r2': {'mean': avg_r2, 'std': std_r2}
                }
            }
    
    def bootstrap_parallel(
        self,
        data: pd.DataFrame,
        model_class: type,
        n_bootstrap: int = 1000,
        sample_size: Optional[int] = None,
        **model_kwargs
    ) -> Dict[str, Any]:
        """Perform parallel bootstrap for uncertainty quantification.
        
        Parameters
        ----------
        data : pd.DataFrame
            Input data
        model_class : type
            Model class to bootstrap
        n_bootstrap : int
            Number of bootstrap samples
        sample_size : int, optional
            Size of each bootstrap sample (default: same as data)
        **model_kwargs : dict
            Model initialization arguments
        
        Returns
        -------
        results : Dict[str, Any]
            Bootstrap results with confidence intervals
        """
        with timer("distributed_bootstrap"):
            info(f"Running {n_bootstrap} bootstrap iterations in parallel")
            
            n_samples = len(data)
            sample_size = sample_size or n_samples
            
            # Create bootstrap sample indices
            np.random.seed(42)  # For reproducibility
            bootstrap_indices = []
            for i in range(n_bootstrap):
                indices = np.random.choice(n_samples, size=sample_size, replace=True)
                bootstrap_indices.append((i, indices))
            
            def fit_bootstrap_sample(sample_info: Tuple[int, np.ndarray]) -> Dict[str, Any]:
                """Fit model on a bootstrap sample."""
                iteration, indices = sample_info
                
                # Create bootstrap sample
                bootstrap_data = data.iloc[indices].copy()
                
                try:
                    # Fit model
                    model = model_class(**model_kwargs)
                    results = model.fit(bootstrap_data)
                    
                    # Extract key parameters
                    if hasattr(results, 'params'):
                        params = results.params
                    elif isinstance(results, dict) and 'coefficients' in results:
                        params = results['coefficients']
                    else:
                        params = None
                    
                    return {
                        'iteration': iteration,
                        'status': 'success',
                        'parameters': params
                    }
                except Exception as e:
                    return {
                        'iteration': iteration,
                        'status': 'failed',
                        'error': str(e)
                    }
            
            # Run bootstrap in parallel
            bootstrap_results = self.runner.map(
                fit_bootstrap_sample,
                bootstrap_indices,
                desc="Bootstrap iterations"
            )
            
            # Extract successful results
            successful_results = [r for r in bootstrap_results if r['status'] == 'success']
            
            if successful_results and successful_results[0]['parameters'] is not None:
                # Stack parameters
                param_array = np.array([r['parameters'] for r in successful_results])
                
                # Calculate statistics
                param_mean = np.mean(param_array, axis=0)
                param_std = np.std(param_array, axis=0)
                param_percentiles = np.percentile(param_array, [2.5, 97.5], axis=0)
                
                results = {
                    'n_successful': len(successful_results),
                    'n_failed': n_bootstrap - len(successful_results),
                    'parameter_estimates': {
                        'mean': param_mean,
                        'std': param_std,
                        'ci_lower': param_percentiles[0],
                        'ci_upper': param_percentiles[1]
                    },
                    'bootstrap_samples': param_array
                }
            else:
                results = {
                    'n_successful': len(successful_results),
                    'n_failed': n_bootstrap - len(successful_results),
                    'parameter_estimates': None,
                    'bootstrap_samples': None
                }
            
            info(f"Bootstrap complete: {results['n_successful']} successful, {results['n_failed']} failed")
            
            return results
    
    def monte_carlo_simulation(
        self,
        model: Any,
        n_simulations: int = 10000,
        simulation_func: Optional[Callable] = None,
        **sim_kwargs
    ) -> Dict[str, Any]:
        """Run Monte Carlo simulations in parallel.
        
        Parameters
        ----------
        model : Any
            Fitted model to simulate from
        n_simulations : int
            Number of simulation runs
        simulation_func : Callable, optional
            Custom simulation function
        **sim_kwargs : dict
            Additional arguments for simulation
        
        Returns
        -------
        results : Dict[str, Any]
            Simulation results
        """
        with timer("distributed_monte_carlo"):
            info(f"Running {n_simulations} Monte Carlo simulations in parallel")
            
            if simulation_func is None:
                # Default simulation function
                def simulation_func(sim_id: int, model: Any, **kwargs) -> np.ndarray:
                    # Simple example: simulate future values
                    n_periods = kwargs.get('n_periods', 12)
                    noise_scale = kwargs.get('noise_scale', 0.1)
                    
                    # Generate random shocks
                    np.random.seed(sim_id)
                    shocks = np.random.normal(0, noise_scale, n_periods)
                    
                    # Simulate path (simplified)
                    if hasattr(model, 'predict'):
                        base_pred = model.predict(n_periods)
                        return base_pred + shocks
                    else:
                        return shocks
            
            # Create simulation tasks
            sim_tasks = [(i, model, sim_kwargs) for i in range(n_simulations)]
            
            def run_single_simulation(task_info: Tuple[int, Any, Dict]) -> np.ndarray:
                sim_id, model, kwargs = task_info
                return simulation_func(sim_id, model, **kwargs)
            
            # Run simulations in parallel
            simulation_results = self.runner.map(
                run_single_simulation,
                sim_tasks,
                desc="Monte Carlo simulations"
            )
            
            # Stack results
            sim_array = np.array(simulation_results)
            
            # Calculate statistics
            sim_mean = np.mean(sim_array, axis=0)
            sim_std = np.std(sim_array, axis=0)
            sim_percentiles = np.percentile(sim_array, [5, 25, 50, 75, 95], axis=0)
            
            return {
                'simulations': sim_array,
                'mean': sim_mean,
                'std': sim_std,
                'percentiles': {
                    5: sim_percentiles[0],
                    25: sim_percentiles[1],
                    50: sim_percentiles[2],
                    75: sim_percentiles[3],
                    95: sim_percentiles[4]
                },
                'n_simulations': n_simulations
            }