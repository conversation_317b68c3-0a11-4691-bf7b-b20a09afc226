"""V3 Accelerated Models Implementation.

This module provides high-performance implementations of econometric models
using modern acceleration technologies:
- MLX for Apple Silicon GPU acceleration
- Ray for distributed CPU processing

The implementations maintain numerical accuracy while achieving significant
performance improvements for the Yemen Market Integration Platform.
"""

from .mlx_operations import MLXAccelerator, MLXPanelEstimator, MLXFactorAnalyzer
from .ray_distributed import RayDistributedRunner, RayModelEstimator
from .benchmark import PerformanceBenchmark, AccelerationMetrics

__all__ = [
    "MLXAccelerator",
    "MLXPanelEstimator", 
    "MLXFactorAnalyzer",
    "RayDistributedRunner",
    "RayModelEstimator",
    "PerformanceBenchmark",
    "AccelerationMetrics",
]