"""MLX-accelerated operations for econometric models.

This module provides Apple Silicon GPU-accelerated implementations of key
matrix operations used in panel models, VECM estimation, and factor analysis.
"""

import numpy as np
from typing import Tuple, Optional, Union, Dict, Any
from dataclasses import dataclass
import warnings

try:
    import mlx.core as mx
    import mlx.nn as nn
    MLX_AVAILABLE = True
except ImportError:
    MLX_AVAILABLE = False
    warnings.warn("MLX not available. Falling back to NumPy operations.")

from yemen_market.utils.logging import info, timer, log_data_shape


@dataclass
class AccelerationConfig:
    """Configuration for MLX acceleration."""
    use_gpu: bool = True
    precision: str = "float32"  # float32 or float16
    batch_size: Optional[int] = None
    memory_limit_gb: float = 8.0


class MLXAccelerator:
    """Base class for MLX-accelerated operations with NumPy fallback."""
    
    def __init__(self, config: Optional[AccelerationConfig] = None):
        """Initialize MLX accelerator.
        
        Parameters
        ----------
        config : AccelerationConfig, optional
            Configuration for acceleration settings
        """
        self.config = config or AccelerationConfig()
        self.use_mlx = MLX_AVAILABLE and self.config.use_gpu
        
        if self.use_mlx:
            info("MLX acceleration enabled on Apple Silicon")
            # Set MLX device preferences
            mx.set_default_device(mx.gpu if self.config.use_gpu else mx.cpu)
        else:
            info("MLX not available, using NumPy backend")
    
    def to_mlx(self, array: np.ndarray) -> Union[mx.array, np.ndarray]:
        """Convert NumPy array to MLX array if available."""
        if self.use_mlx:
            return mx.array(array, dtype=getattr(mx, self.config.precision))
        return array
    
    def to_numpy(self, array: Union[mx.array, np.ndarray]) -> np.ndarray:
        """Convert MLX array to NumPy array."""
        if self.use_mlx and isinstance(array, mx.array):
            return np.array(array)
        return array
    
    def lstsq(self, A: np.ndarray, b: np.ndarray) -> Tuple[np.ndarray, float]:
        """Solve least squares problem Ax = b.
        
        Parameters
        ----------
        A : np.ndarray
            Design matrix (n_samples, n_features)
        b : np.ndarray
            Target values (n_samples,) or (n_samples, n_targets)
        
        Returns
        -------
        x : np.ndarray
            Solution vector
        residual : float
            Sum of squared residuals
        """
        with timer("mlx_lstsq"):
            if self.use_mlx:
                A_mlx = self.to_mlx(A)
                b_mlx = self.to_mlx(b)
                
                # MLX implementation using normal equations for stability
                # (A^T A) x = A^T b
                AtA = A_mlx.T @ A_mlx
                Atb = A_mlx.T @ b_mlx
                
                # Solve using Cholesky decomposition for positive definite matrices
                try:
                    L = mx.linalg.cholesky(AtA)
                    y = mx.linalg.triangular_solve(L, Atb, lower=True)
                    x = mx.linalg.triangular_solve(L.T, y, lower=False)
                except:
                    # Fall back to general solver if Cholesky fails
                    x = mx.linalg.solve(AtA, Atb)
                
                # Compute residuals
                residuals = b_mlx - A_mlx @ x
                residual_sum = mx.sum(residuals * residuals)
                
                return self.to_numpy(x), float(self.to_numpy(residual_sum))
            else:
                # NumPy fallback
                x, residuals, _, _ = np.linalg.lstsq(A, b, rcond=None)
                residual_sum = np.sum(residuals) if residuals.size > 0 else 0.0
                return x, residual_sum
    
    def eig(self, A: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Compute eigenvalues and eigenvectors.
        
        Parameters
        ----------
        A : np.ndarray
            Square matrix
        
        Returns
        -------
        eigenvalues : np.ndarray
            Eigenvalues in descending order
        eigenvectors : np.ndarray
            Corresponding eigenvectors
        """
        with timer("mlx_eig"):
            if self.use_mlx:
                A_mlx = self.to_mlx(A)
                # MLX eigendecomposition
                eigenvalues, eigenvectors = mx.linalg.eigh(A_mlx)
                # Sort in descending order
                idx = mx.argsort(eigenvalues)[::-1]
                eigenvalues = eigenvalues[idx]
                eigenvectors = eigenvectors[:, idx]
                return self.to_numpy(eigenvalues), self.to_numpy(eigenvectors)
            else:
                # NumPy fallback
                eigenvalues, eigenvectors = np.linalg.eigh(A)
                idx = np.argsort(eigenvalues)[::-1]
                return eigenvalues[idx], eigenvectors[:, idx]
    
    def svd(self, A: np.ndarray, k: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Compute Singular Value Decomposition.
        
        Parameters
        ----------
        A : np.ndarray
            Input matrix
        k : int, optional
            Number of components to keep
        
        Returns
        -------
        U : np.ndarray
            Left singular vectors
        S : np.ndarray
            Singular values
        V : np.ndarray
            Right singular vectors
        """
        with timer("mlx_svd"):
            if self.use_mlx:
                A_mlx = self.to_mlx(A)
                U, S, Vt = mx.linalg.svd(A_mlx, full_matrices=False)
                
                if k is not None:
                    U = U[:, :k]
                    S = S[:k]
                    Vt = Vt[:k, :]
                
                return self.to_numpy(U), self.to_numpy(S), self.to_numpy(Vt.T)
            else:
                # NumPy fallback
                U, S, Vt = np.linalg.svd(A, full_matrices=False)
                if k is not None:
                    U = U[:, :k]
                    S = S[:k]
                    Vt = Vt[:k, :]
                return U, S, Vt.T
    
    def inv(self, A: np.ndarray) -> np.ndarray:
        """Compute matrix inverse with stability checks."""
        with timer("mlx_inv"):
            if self.use_mlx:
                A_mlx = self.to_mlx(A)
                return self.to_numpy(mx.linalg.inv(A_mlx))
            else:
                return np.linalg.inv(A)


class MLXPanelEstimator(MLXAccelerator):
    """MLX-accelerated panel model estimator."""
    
    def estimate_fixed_effects(
        self, 
        X: np.ndarray, 
        y: np.ndarray,
        entity_ids: np.ndarray,
        time_ids: np.ndarray
    ) -> Dict[str, Any]:
        """Estimate panel fixed effects model with MLX acceleration.
        
        Parameters
        ----------
        X : np.ndarray
            Feature matrix (n_samples, n_features)
        y : np.ndarray
            Target values (n_samples,)
        entity_ids : np.ndarray
            Entity identifiers (n_samples,)
        time_ids : np.ndarray
            Time period identifiers (n_samples,)
        
        Returns
        -------
        results : dict
            Model estimation results including coefficients and statistics
        """
        with timer("mlx_fixed_effects_estimation"):
            n_samples, n_features = X.shape
            n_entities = len(np.unique(entity_ids))
            n_periods = len(np.unique(time_ids))
            
            info(f"Estimating fixed effects: {n_samples} obs, {n_entities} entities, {n_periods} periods")
            
            # Demean within entities (entity fixed effects)
            X_demeaned = np.zeros_like(X)
            y_demeaned = np.zeros_like(y)
            
            for entity in np.unique(entity_ids):
                mask = entity_ids == entity
                X_entity = X[mask]
                y_entity = y[mask]
                
                # Within transformation
                X_demeaned[mask] = X_entity - X_entity.mean(axis=0)
                y_demeaned[mask] = y_entity - y_entity.mean()
            
            # Estimate coefficients using MLX
            beta, rss = self.lstsq(X_demeaned, y_demeaned)
            
            # Compute statistics
            n_params = n_features + n_entities - 1  # -1 for dropped dummy
            dof = n_samples - n_params
            sigma2 = rss / dof
            
            # Variance-covariance matrix
            if self.use_mlx:
                X_mlx = self.to_mlx(X_demeaned)
                XtX_inv = self.inv(X_mlx.T @ X_mlx)
                var_cov = self.to_numpy(XtX_inv) * sigma2
            else:
                XtX_inv = np.linalg.inv(X_demeaned.T @ X_demeaned)
                var_cov = XtX_inv * sigma2
            
            # Standard errors
            std_errors = np.sqrt(np.diag(var_cov))
            
            # T-statistics
            t_stats = beta / std_errors
            
            # R-squared (within R-squared for FE models)
            y_pred = X_demeaned @ beta
            ss_res = np.sum((y_demeaned - y_pred) ** 2)
            ss_tot = np.sum((y_demeaned - y_demeaned.mean()) ** 2)
            r_squared = 1 - ss_res / ss_tot
            
            return {
                'coefficients': beta,
                'std_errors': std_errors,
                't_statistics': t_stats,
                'r_squared': r_squared,
                'residual_sum_squares': rss,
                'sigma_squared': sigma2,
                'var_cov_matrix': var_cov,
                'degrees_of_freedom': dof
            }
    
    def estimate_random_effects(
        self,
        X: np.ndarray,
        y: np.ndarray,
        entity_ids: np.ndarray,
        time_ids: np.ndarray
    ) -> Dict[str, Any]:
        """Estimate panel random effects model with MLX acceleration.
        
        Uses feasible GLS estimation for random effects.
        """
        with timer("mlx_random_effects_estimation"):
            n_samples, n_features = X.shape
            n_entities = len(np.unique(entity_ids))
            
            # First estimate pooled OLS to get residuals
            beta_ols, rss_ols = self.lstsq(X, y)
            residuals = y - X @ beta_ols
            
            # Estimate variance components
            # Within-entity variance
            sigma2_e = 0.0
            for entity in np.unique(entity_ids):
                mask = entity_ids == entity
                entity_resid = residuals[mask]
                sigma2_e += np.sum((entity_resid - entity_resid.mean()) ** 2)
            sigma2_e /= (n_samples - n_entities)
            
            # Between-entity variance
            entity_means = []
            for entity in np.unique(entity_ids):
                mask = entity_ids == entity
                entity_means.append(residuals[mask].mean())
            sigma2_u = np.var(entity_means)
            
            # Compute theta for quasi-demeaning
            T_i = n_samples / n_entities  # Average group size
            theta = 1 - np.sqrt(sigma2_e / (sigma2_e + T_i * sigma2_u))
            
            # Quasi-demean the data
            X_transformed = np.zeros_like(X)
            y_transformed = np.zeros_like(y)
            
            for entity in np.unique(entity_ids):
                mask = entity_ids == entity
                X_entity = X[mask]
                y_entity = y[mask]
                
                X_mean = X_entity.mean(axis=0)
                y_mean = y_entity.mean()
                
                X_transformed[mask] = X_entity - theta * X_mean
                y_transformed[mask] = y_entity - theta * y_mean
            
            # Estimate using transformed data
            beta_re, rss_re = self.lstsq(X_transformed, y_transformed)
            
            # Compute standard errors accounting for the two-component error structure
            var_cov = self.inv(X_transformed.T @ X_transformed) * sigma2_e
            std_errors = np.sqrt(np.diag(var_cov))
            
            return {
                'coefficients': beta_re,
                'std_errors': std_errors,
                't_statistics': beta_re / std_errors,
                'sigma2_entity': sigma2_u,
                'sigma2_idiosyncratic': sigma2_e,
                'theta': theta,
                'var_cov_matrix': var_cov
            }


class MLXFactorAnalyzer(MLXAccelerator):
    """MLX-accelerated factor analysis for PCA and dynamic factor models."""
    
    def pca(self, X: np.ndarray, n_components: int) -> Dict[str, Any]:
        """Perform Principal Component Analysis with MLX acceleration.
        
        Parameters
        ----------
        X : np.ndarray
            Data matrix (n_samples, n_features)
        n_components : int
            Number of principal components
        
        Returns
        -------
        results : dict
            PCA results including components, loadings, and explained variance
        """
        with timer("mlx_pca"):
            # Center the data
            X_centered = X - X.mean(axis=0)
            
            # Compute covariance matrix
            if self.use_mlx:
                X_mlx = self.to_mlx(X_centered)
                cov_matrix = (X_mlx.T @ X_mlx) / (X.shape[0] - 1)
                
                # Eigendecomposition
                eigenvalues, eigenvectors = mx.linalg.eigh(cov_matrix)
                
                # Sort in descending order
                idx = mx.argsort(eigenvalues)[::-1]
                eigenvalues = eigenvalues[idx][:n_components]
                eigenvectors = eigenvectors[:, idx][:, :n_components]
                
                # Transform data
                scores = X_mlx @ eigenvectors
                
                # Convert back to numpy
                components = self.to_numpy(eigenvectors.T)
                scores = self.to_numpy(scores)
                explained_variance = self.to_numpy(eigenvalues)
            else:
                # NumPy fallback
                cov_matrix = np.cov(X_centered, rowvar=False)
                eigenvalues, eigenvectors = np.linalg.eigh(cov_matrix)
                
                idx = np.argsort(eigenvalues)[::-1]
                eigenvalues = eigenvalues[idx][:n_components]
                eigenvectors = eigenvectors[:, idx][:, :n_components]
                
                components = eigenvectors.T
                scores = X_centered @ eigenvectors
                explained_variance = eigenvalues
            
            # Compute explained variance ratio
            total_variance = np.sum(np.var(X_centered, axis=0))
            explained_variance_ratio = explained_variance / total_variance
            
            log_data_shape("pca_scores", scores)
            info(f"PCA completed: {n_components} components explain {explained_variance_ratio.sum():.2%} of variance")
            
            return {
                'components': components,
                'scores': scores,
                'explained_variance': explained_variance,
                'explained_variance_ratio': explained_variance_ratio,
                'loadings': components.T * np.sqrt(explained_variance)
            }
    
    def factor_analysis(self, X: np.ndarray, n_factors: int, max_iter: int = 100) -> Dict[str, Any]:
        """Perform Factor Analysis using EM algorithm with MLX acceleration.
        
        Parameters
        ----------
        X : np.ndarray
            Data matrix (n_samples, n_features)
        n_factors : int
            Number of latent factors
        max_iter : int
            Maximum iterations for EM algorithm
        
        Returns
        -------
        results : dict
            Factor analysis results
        """
        with timer("mlx_factor_analysis"):
            n_samples, n_features = X.shape
            X_centered = X - X.mean(axis=0)
            
            # Initialize with PCA
            pca_results = self.pca(X_centered, n_factors)
            loadings = pca_results['loadings']
            
            # EM algorithm for maximum likelihood factor analysis
            psi = np.ones(n_features)  # Specific variances
            
            for iteration in range(max_iter):
                # E-step: Estimate factor scores
                if self.use_mlx:
                    X_mlx = self.to_mlx(X_centered)
                    loadings_mlx = self.to_mlx(loadings)
                    psi_mlx = self.to_mlx(psi)
                    
                    # Woodbury matrix identity for efficient inversion
                    psi_inv = mx.diag(1.0 / psi_mlx)
                    M = mx.eye(n_factors) + loadings_mlx.T @ psi_inv @ loadings_mlx
                    M_inv = self.inv(M)
                    
                    # Factor scores
                    scores = X_mlx @ psi_inv @ loadings_mlx @ M_inv
                    
                    # M-step: Update loadings and psi
                    loadings_new = (X_mlx.T @ scores) @ self.inv(scores.T @ scores)
                    
                    # Update specific variances
                    fitted = scores @ loadings_new.T
                    residuals = X_mlx - fitted
                    psi_new = mx.mean(residuals ** 2, axis=0)
                    
                    # Convert back
                    loadings = self.to_numpy(loadings_new)
                    psi = self.to_numpy(psi_new)
                    scores_final = self.to_numpy(scores)
                else:
                    # NumPy implementation
                    psi_inv = np.diag(1.0 / psi)
                    M = np.eye(n_factors) + loadings.T @ psi_inv @ loadings
                    M_inv = np.linalg.inv(M)
                    
                    scores = X_centered @ psi_inv @ loadings @ M_inv
                    loadings = (X_centered.T @ scores) @ np.linalg.inv(scores.T @ scores)
                    
                    fitted = scores @ loadings.T
                    residuals = X_centered - fitted
                    psi = np.mean(residuals ** 2, axis=0)
                    scores_final = scores
                
                # Check convergence
                if iteration > 0 and np.max(np.abs(psi - psi_old)) < 1e-4:
                    info(f"Factor analysis converged after {iteration + 1} iterations")
                    break
                psi_old = psi.copy()
            
            # Compute communalities
            communalities = np.sum(loadings ** 2, axis=1)
            
            return {
                'loadings': loadings,
                'scores': scores_final,
                'specific_variances': psi,
                'communalities': communalities,
                'n_iterations': iteration + 1
            }


class MLXVECMEstimator(MLXAccelerator):
    """MLX-accelerated Vector Error Correction Model estimation."""
    
    def johansen_test(self, Y: np.ndarray, p: int) -> Dict[str, Any]:
        """Perform Johansen cointegration test with MLX acceleration.
        
        Parameters
        ----------
        Y : np.ndarray
            Multivariate time series (n_obs, n_vars)
        p : int
            Number of lags
        
        Returns
        -------
        results : dict
            Test statistics and critical values
        """
        with timer("mlx_johansen_test"):
            n_obs, n_vars = Y.shape
            
            # Construct lagged differences and levels
            dY = np.diff(Y, axis=0)
            Y_lag = Y[:-1]
            
            # Create lagged differences matrix
            Z = []
            for lag in range(1, p):
                if lag < dY.shape[0]:
                    Z.append(dY[lag-1:-lag if lag < dY.shape[0] else None])
            
            if Z:
                Z = np.column_stack(Z)
                # Adjust dimensions
                min_len = min(dY[p-1:].shape[0], Y_lag[p-1:].shape[0], Z.shape[0])
                dY_adj = dY[p-1:p-1+min_len]
                Y_lag_adj = Y_lag[p-1:p-1+min_len]
                Z_adj = Z[:min_len]
            else:
                dY_adj = dY[p-1:]
                Y_lag_adj = Y_lag[p-1:]
                Z_adj = None
            
            # Regression residuals
            if Z_adj is not None:
                # Multivariate regression dY on Z
                R0 = dY_adj - Z_adj @ self.lstsq(Z_adj, dY_adj)[0]
                R1 = Y_lag_adj - Z_adj @ self.lstsq(Z_adj, Y_lag_adj)[0]
            else:
                R0 = dY_adj
                R1 = Y_lag_adj
            
            # Product moment matrices
            S00 = R0.T @ R0 / R0.shape[0]
            S01 = R0.T @ R1 / R0.shape[0]
            S10 = S01.T
            S11 = R1.T @ R1 / R1.shape[0]
            
            # Solve eigenvalue problem
            if self.use_mlx:
                S11_inv = self.inv(self.to_mlx(S11))
                M = self.to_mlx(S10) @ self.to_mlx(S00).inv() @ self.to_mlx(S01) @ S11_inv
                eigenvalues, eigenvectors = mx.linalg.eig(M)
                
                # Sort and extract
                idx = mx.argsort(mx.real(eigenvalues))[::-1]
                eigenvalues = mx.real(eigenvalues[idx])
                eigenvectors = eigenvectors[:, idx]
                
                eigenvalues = self.to_numpy(eigenvalues)
                eigenvectors = self.to_numpy(eigenvectors)
            else:
                S11_inv = np.linalg.inv(S11)
                M = S10 @ np.linalg.inv(S00) @ S01 @ S11_inv
                eigenvalues, eigenvectors = np.linalg.eig(M)
                
                idx = np.argsort(eigenvalues)[::-1]
                eigenvalues = eigenvalues[idx].real
                eigenvectors = eigenvectors[:, idx]
            
            # Trace and max eigenvalue statistics
            trace_stats = -n_obs * np.cumsum(np.log(1 - eigenvalues))
            max_eig_stats = -n_obs * np.log(1 - eigenvalues)
            
            return {
                'eigenvalues': eigenvalues,
                'eigenvectors': eigenvectors,
                'trace_statistics': trace_stats,
                'max_eigenvalue_statistics': max_eig_stats,
                'rank': np.sum(eigenvalues > 0.1)  # Simple threshold
            }
    
    def estimate_vecm(self, Y: np.ndarray, r: int, p: int) -> Dict[str, Any]:
        """Estimate VECM parameters with MLX acceleration.
        
        Parameters
        ----------
        Y : np.ndarray
            Multivariate time series
        r : int
            Cointegration rank
        p : int
            Number of lags
        
        Returns
        -------
        results : dict
            VECM parameter estimates
        """
        with timer("mlx_vecm_estimation"):
            # Get cointegration vectors from Johansen test
            johansen_results = self.johansen_test(Y, p)
            beta = johansen_results['eigenvectors'][:, :r]
            
            # Normalize beta
            beta = beta / beta[0, :]
            
            # Error correction terms
            ect = Y[:-1] @ beta
            
            # Construct the VECM regression
            dY = np.diff(Y, axis=0)
            
            # Create design matrix
            X_parts = [ect[p-1:]]
            for lag in range(1, p):
                if lag < dY.shape[0]:
                    X_parts.append(dY[lag-1:-lag if lag < dY.shape[0] else None])
            
            X = np.column_stack(X_parts)
            y = dY[p-1:]
            
            # Ensure dimensions match
            min_len = min(X.shape[0], y.shape[0])
            X = X[:min_len]
            y = y[:min_len]
            
            # Estimate parameters
            params, rss = self.lstsq(X, y)
            
            # Extract alpha (adjustment) and Gamma (short-run) parameters
            alpha = params[:r].T
            gamma = params[r:].T if params.shape[0] > r else None
            
            # Compute standard errors
            n_obs = y.shape[0]
            n_params = X.shape[1]
            sigma2 = rss / (n_obs - n_params)
            
            if self.use_mlx:
                X_mlx = self.to_mlx(X)
                var_cov = self.inv(X_mlx.T @ X_mlx) * sigma2
                var_cov = self.to_numpy(var_cov)
            else:
                var_cov = np.linalg.inv(X.T @ X) * sigma2
            
            std_errors = np.sqrt(np.diag(var_cov))
            
            return {
                'alpha': alpha,
                'beta': beta,
                'gamma': gamma,
                'parameters': params,
                'std_errors': std_errors,
                'var_cov_matrix': var_cov,
                'residual_sum_squares': rss
            }