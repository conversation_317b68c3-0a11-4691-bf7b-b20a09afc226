"""Integration tests for PostgreSQL repositories."""

import asyncio
import pytest
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from uuid import uuid4

import asyncpg
from testcontainers.postgres import PostgresContainer

from v2.src.core.domain.market.entities import Market, PriceObservation
from v2.src.core.domain.market.value_objects import (
    MarketId, Coordinates, MarketType, Commodity, Price
)
from v2.src.core.domain.conflict.entities import ConflictEvent
from v2.src.core.domain.conflict.value_objects import (
    ConflictEventId, EventType, ConflictLocation, FatalityCount
)
from v2.src.infrastructure.persistence.unit_of_work import PostgresUnitOfWork


@pytest.fixture(scope="session")
def postgres_container():
    """PostgreSQL test container."""
    with PostgresContainer("postgres:15") as postgres:
        yield postgres


@pytest.fixture(scope="session") 
async def db_connection_string(postgres_container):
    """Database connection string."""
    return postgres_container.get_connection_url()


@pytest.fixture(scope="session")
async def setup_database(db_connection_string):
    """Set up test database schema."""
    # Read and execute schema
    schema_path = "v2/src/infrastructure/persistence/migrations/001_initial_schema.sql"
    with open(schema_path, 'r') as f:
        schema_sql = f.read()
    
    conn = await asyncpg.connect(db_connection_string)
    try:
        await conn.execute(schema_sql)
    finally:
        await conn.close()
    
    yield db_connection_string


@pytest.fixture
async def uow(setup_database):
    """Unit of work fixture."""
    async with PostgresUnitOfWork(setup_database) as unit_of_work:
        yield unit_of_work


@pytest.fixture
async def sample_commodities(uow):
    """Create sample commodities."""
    commodities = [
        Commodity(
            code='WHEAT',
            name='Wheat',
            category='Cereals',
            standard_unit='kg'
        ),
        Commodity(
            code='RICE',
            name='Rice (Imported)',
            category='Cereals', 
            standard_unit='kg'
        )
    ]
    
    await uow.commodities.save_batch(commodities)
    return commodities


@pytest.fixture
async def sample_markets(uow):
    """Create sample markets."""
    markets = [
        Market(
            market_id=MarketId("market_001"),
            name="Sana'a Central Market",
            coordinates=Coordinates(latitude=15.3694, longitude=44.1910),
            market_type=MarketType.WHOLESALE,
            governorate="Sana'a",
            district="Old City",
            active_since=datetime(2019, 1, 1),
            active_until=None
        ),
        Market(
            market_id=MarketId("market_002"),
            name="Aden Port Market",
            coordinates=Coordinates(latitude=12.7797, longitude=45.0367),
            market_type=MarketType.PORT,
            governorate="Aden",
            district="Crater",
            active_since=datetime(2019, 1, 1),
            active_until=None
        )
    ]
    
    for market in markets:
        await uow.markets.save(market)
    
    return markets


class TestMarketRepository:
    """Test market repository functionality."""
    
    async def test_save_and_find_market(self, uow):
        """Test saving and finding a market."""
        market = Market(
            market_id=MarketId("test_market"),
            name="Test Market",
            coordinates=Coordinates(latitude=15.0, longitude=44.0),
            market_type=MarketType.RETAIL,
            governorate="Test Gov",
            district="Test District",
            active_since=datetime.now(),
            active_until=None
        )
        
        await uow.markets.save(market)
        
        found_market = await uow.markets.find_by_id(MarketId("test_market"))
        assert found_market is not None
        assert found_market.name == "Test Market"
        assert found_market.governorate == "Test Gov"
    
    async def test_find_by_governorate(self, uow, sample_markets):
        """Test finding markets by governorate."""
        markets = await uow.markets.find_by_governorate("Sana'a")
        assert len(markets) == 1
        assert markets[0].name == "Sana'a Central Market"
    
    async def test_find_active_at_date(self, uow, sample_markets):
        """Test finding markets active at a specific date."""
        test_date = datetime(2020, 6, 1)
        markets = await uow.markets.find_active_at(test_date)
        assert len(markets) == 2  # Both markets should be active
    
    async def test_find_within_radius(self, uow, sample_markets):
        """Test finding markets within radius."""
        # Search near Sana'a
        markets = await uow.markets.find_within_radius(15.3694, 44.1910, 50.0)
        assert len(markets) >= 1
        assert any(m.name == "Sana'a Central Market" for m in markets)
    
    async def test_find_by_control_zone(self, uow, sample_markets):
        """Test finding markets by control zone."""
        # Sana'a should be in Houthi zone
        houthi_markets = await uow.markets.find_by_control_zone("houthi")
        assert len(houthi_markets) == 1
        assert houthi_markets[0].governorate == "Sana'a"
        
        # Aden should be in Government zone
        gov_markets = await uow.markets.find_by_control_zone("government")
        assert len(gov_markets) == 1
        assert gov_markets[0].governorate == "Aden"


class TestPriceRepository:
    """Test price repository functionality."""
    
    async def test_save_and_find_price_observation(self, uow, sample_markets, sample_commodities):
        """Test saving and finding price observations."""
        market = sample_markets[0]
        commodity = sample_commodities[0]
        
        observation = PriceObservation(
            market_id=market.market_id,
            commodity=commodity,
            price=Price(amount=Decimal("100.50"), currency="YER", unit="kg"),
            observed_date=datetime(2020, 1, 1),
            source="WFP",
            quality="high",
            observations_count=5
        )
        
        await uow.prices.save(observation)
        
        observations = await uow.prices.find_by_market_and_commodity(
            market.market_id,
            commodity,
            datetime(2019, 12, 1),
            datetime(2020, 2, 1)
        )
        
        assert len(observations) == 1
        assert observations[0].price.amount == Decimal("100.50")
        assert observations[0].source == "WFP"
    
    async def test_batch_save_observations(self, uow, sample_markets, sample_commodities):
        """Test batch saving price observations."""
        market = sample_markets[0]
        commodity = sample_commodities[0]
        
        observations = []
        for i in range(10):
            obs = PriceObservation(
                market_id=market.market_id,
                commodity=commodity,
                price=Price(amount=Decimal(f"{100 + i}.00"), currency="YER", unit="kg"),
                observed_date=datetime(2020, 1, i + 1),
                source="WFP",
                quality="standard",
                observations_count=1
            )
            observations.append(obs)
        
        await uow.prices.save_batch(observations)
        
        found_observations = await uow.prices.find_by_market_and_commodity(
            market.market_id,
            commodity,
            datetime(2020, 1, 1),
            datetime(2020, 1, 31)
        )
        
        assert len(found_observations) == 10
    
    async def test_find_by_date_range(self, uow, sample_markets, sample_commodities):
        """Test finding observations by date range."""
        market = sample_markets[0]
        commodity = sample_commodities[0]
        
        # Create observations across different dates
        observations = []
        for i in range(5):
            obs = PriceObservation(
                market_id=market.market_id,
                commodity=commodity,
                price=Price(amount=Decimal("100.00"), currency="YER", unit="kg"),
                observed_date=datetime(2020, 1, 1) + timedelta(days=i*10),
                source="WFP",
                quality="standard",
                observations_count=1
            )
            observations.append(obs)
        
        await uow.prices.save_batch(observations)
        
        # Find observations in a specific date range
        found_observations = await uow.prices.find_by_date_range(
            datetime(2020, 1, 15),
            datetime(2020, 1, 35),
            market_ids=[market.market_id],
            commodity_codes=[commodity.code]
        )
        
        assert len(found_observations) == 2  # Days 20 and 30
    
    async def test_get_price_statistics(self, uow, sample_markets, sample_commodities):
        """Test getting price statistics."""
        market = sample_markets[0]
        commodity = sample_commodities[0]
        
        # Create observations with known values
        prices = [Decimal("100.00"), Decimal("110.00"), Decimal("120.00"), Decimal("130.00"), Decimal("140.00")]
        observations = []
        
        for i, price in enumerate(prices):
            obs = PriceObservation(
                market_id=market.market_id,
                commodity=commodity,
                price=Price(amount=price, currency="YER", unit="kg"),
                observed_date=datetime(2020, 1, i + 1),
                source="WFP",
                quality="standard",
                observations_count=1
            )
            observations.append(obs)
        
        await uow.prices.save_batch(observations)
        
        stats = await uow.prices.get_price_statistics(
            market.market_id,
            commodity,
            datetime(2020, 1, 1),
            datetime(2020, 1, 31)
        )
        
        assert stats['count'] == 5
        assert stats['mean'] == 120.0
        assert stats['min'] == 100.0
        assert stats['max'] == 140.0


class TestConflictEventRepository:
    """Test conflict event repository functionality."""
    
    async def test_save_and_find_conflict_event(self, uow):
        """Test saving and finding conflict events."""
        event = ConflictEvent(
            event_id=ConflictEventId("test_event_001"),
            event_type=EventType.VIOLENCE_AGAINST_CIVILIANS,
            sub_event_type="Attack",
            event_date=datetime(2020, 1, 1),
            location=ConflictLocation(
                latitude=15.0,
                longitude=44.0,
                governorate="Sana'a",
                district="Test District"
            ),
            fatality_count=FatalityCount(5),
            notes="Test conflict event",
            source="ACLED",
            source_scale="National"
        )
        
        await uow.conflict_events.save(event)
        
        found_event = await uow.conflict_events.find_by_id("test_event_001")
        assert found_event is not None
        assert found_event.fatality_count.value == 5
        assert found_event.location.governorate == "Sana'a"
    
    async def test_find_by_governorate_and_date_range(self, uow):
        """Test finding events by governorate and date range."""
        events = []
        for i in range(3):
            event = ConflictEvent(
                event_id=ConflictEventId(f"event_{i}"),
                event_type=EventType.BATTLES,
                sub_event_type="Armed clash",
                event_date=datetime(2020, 1, i + 1),
                location=ConflictLocation(
                    latitude=15.0 + i * 0.1,
                    longitude=44.0 + i * 0.1,
                    governorate="Sana'a",
                    district=f"District {i}"
                ),
                fatality_count=FatalityCount(i + 1),
                notes=f"Event {i}",
                source="ACLED",
                source_scale="National"
            )
            events.append(event)
        
        await uow.conflict_events.save_batch(events)
        
        found_events = await uow.conflict_events.find_by_governorate_and_date_range(
            "Sana'a",
            datetime(2020, 1, 1),
            datetime(2020, 1, 31)
        )
        
        assert len(found_events) == 3
    
    async def test_find_by_location_radius(self, uow):
        """Test finding events within radius of a location."""
        # Create events at different distances from Sana'a center
        events = []
        locations = [
            (15.3694, 44.1910),  # Sana'a center
            (15.4000, 44.2000),  # Close to Sana'a
            (12.7797, 45.0367),  # Aden (far from Sana'a)
        ]
        
        for i, (lat, lon) in enumerate(locations):
            event = ConflictEvent(
                event_id=ConflictEventId(f"location_event_{i}"),
                event_type=EventType.RIOTS,
                sub_event_type="Violent demonstration",
                event_date=datetime(2020, 1, 1),
                location=ConflictLocation(
                    latitude=lat,
                    longitude=lon,
                    governorate="Test Gov",
                    district="Test District"
                ),
                fatality_count=FatalityCount(0),
                notes=f"Location event {i}",
                source="ACLED",
                source_scale="Local"
            )
            events.append(event)
        
        await uow.conflict_events.save_batch(events)
        
        # Find events within 50km of Sana'a center
        nearby_events = await uow.conflict_events.find_by_location_radius(
            15.3694, 44.1910, 50.0
        )
        
        # Should find the two events in/near Sana'a, not the one in Aden
        assert len(nearby_events) >= 2
    
    async def test_get_aggregated_statistics(self, uow):
        """Test getting aggregated conflict statistics."""
        # Create events across different months
        events = []
        for month in range(1, 4):  # Jan, Feb, Mar
            for i in range(2):  # 2 events per month
                event = ConflictEvent(
                    event_id=ConflictEventId(f"stats_event_{month}_{i}"),
                    event_type=EventType.BATTLES,
                    sub_event_type="Armed clash",
                    event_date=datetime(2020, month, 15),
                    location=ConflictLocation(
                        latitude=15.0,
                        longitude=44.0,
                        governorate="Sana'a",
                        district="Test District"
                    ),
                    fatality_count=FatalityCount(month),  # Different fatalities per month
                    notes=f"Stats event {month}-{i}",
                    source="ACLED",
                    source_scale="National"
                )
                events.append(event)
        
        await uow.conflict_events.save_batch(events)
        
        stats = await uow.conflict_events.get_aggregated_statistics(
            governorate="Sana'a",
            start_date=datetime(2020, 1, 1),
            end_date=datetime(2020, 3, 31),
            groupby_period='month'
        )
        
        assert len(stats) == 3  # 3 months
        assert all(stat['event_count'] == 2 for stat in stats)  # 2 events per month


class TestCommodityRepository:
    """Test commodity repository functionality."""
    
    async def test_save_and_find_commodity(self, uow):
        """Test saving and finding commodities."""
        commodity = Commodity(
            code='TEST_COMMODITY',
            name='Test Commodity',
            category='Test Category',
            standard_unit='kg'
        )
        
        await uow.commodities.save(commodity)
        
        found_commodity = await uow.commodities.find_by_code('TEST_COMMODITY')
        assert found_commodity is not None
        assert found_commodity.name == 'Test Commodity'
        assert found_commodity.category == 'Test Category'
    
    async def test_find_by_category(self, uow, sample_commodities):
        """Test finding commodities by category."""
        cereals = await uow.commodities.find_by_category('Cereals')
        assert len(cereals) == 2
        assert all(c.category == 'Cereals' for c in cereals)


class TestUnitOfWork:
    """Test Unit of Work pattern."""
    
    async def test_transaction_commit(self, setup_database):
        """Test successful transaction commit."""
        async with PostgresUnitOfWork(setup_database) as uow:
            commodity = Commodity(
                code='TRANSACTION_TEST',
                name='Transaction Test',
                category='Test',
                standard_unit='kg'
            )
            
            await uow.commodities.save(commodity)
            # Transaction should commit automatically
        
        # Verify the commodity was saved
        async with PostgresUnitOfWork(setup_database) as uow:
            found = await uow.commodities.find_by_code('TRANSACTION_TEST')
            assert found is not None
    
    async def test_transaction_rollback(self, setup_database):
        """Test transaction rollback on exception."""
        try:
            async with PostgresUnitOfWork(setup_database) as uow:
                commodity = Commodity(
                    code='ROLLBACK_TEST',
                    name='Rollback Test',
                    category='Test',
                    standard_unit='kg'
                )
                
                await uow.commodities.save(commodity)
                
                # Force an exception
                raise ValueError("Test exception")
        
        except ValueError:
            pass  # Expected
        
        # Verify the commodity was not saved
        async with PostgresUnitOfWork(setup_database) as uow:
            found = await uow.commodities.find_by_code('ROLLBACK_TEST')
            assert found is None


if __name__ == "__main__":
    pytest.main([__file__])