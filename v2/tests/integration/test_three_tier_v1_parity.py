"""Integration tests for V1/V2 parity in three-tier analysis.

These tests ensure that V2 analysis using V1 adapters produces results
matching the validated 35% conflict effect findings.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add V1 source to path for comparison
v1_src = Path(__file__).parent.parent.parent.parent / "src"
if v1_src.exists() and str(v1_src) not in sys.path:
    sys.path.insert(0, str(v1_src))

from v2.src.application.services.three_tier_analysis_service import ThreeTierAnalysisService
from v2.src.infrastructure.adapters.tier1_adapter import Tier1Adapter
from v2.src.infrastructure.adapters.tier2_adapter import Tier2Adapter
from v2.src.infrastructure.adapters.tier3_adapter import Tier3Adapter
from v2.src.core.domain.market.entities import PanelData, Market, PriceObservation
from v2.src.core.domain.market.value_objects import (
    MarketId, Commodity, Price, Currency, Coordinates, MarketType
)


@pytest.fixture
def sample_panel_data():
    """Create sample panel data matching V1 test data structure."""
    # Generate synthetic data with known conflict effect
    np.random.seed(42)
    
    markets = ['Sana\'a_Market1', 'Aden_Market1', 'Taiz_Market1', 'Hodeidah_Market1']
    commodities = ['Wheat', 'Rice', 'Sugar', 'Fuel (Diesel)']
    
    # Generate 24 months of data
    start_date = datetime(2022, 1, 1)
    dates = [start_date + timedelta(days=30*i) for i in range(24)]
    
    observations = []
    
    for market in markets:
        # Create market entity
        market_obj = Market(
            market_id=MarketId(market),
            name=market.split('_')[1],
            coordinates=Coordinates(
                latitude=15.0 + np.random.random(),
                longitude=44.0 + np.random.random()
            ),
            market_type=MarketType.RETAIL,
            governorate=market.split('_')[0],
            district=f"{market.split('_')[0]}_District",
            active_since=start_date
        )
        
        for commodity_name in commodities:
            # Create commodity
            commodity = Commodity(
                code=commodity_name.upper().replace(' ', '_').replace('(', '').replace(')', ''),
                name=commodity_name,
                category='food' if commodity_name != 'Fuel (Diesel)' else 'fuel',
                standard_unit='kg' if commodity_name != 'Fuel (Diesel)' else 'liter'
            )
            
            # Generate price series with conflict effect
            base_price = 100 + np.random.normal(0, 10)
            
            for i, date in enumerate(dates):
                # Add trend
                trend = i * 0.5
                
                # Add seasonality
                seasonality = 10 * np.sin(2 * np.pi * i / 12)
                
                # Add conflict effect (35% increase in conflict areas)
                conflict_intensity = np.random.poisson(2) if 'Sana' in market or 'Taiz' in market else 0
                conflict_effect = 1.35 ** (conflict_intensity / 10)  # ~35% for intensity=10
                
                # Generate price
                price_value = (base_price + trend + seasonality) * conflict_effect + np.random.normal(0, 5)
                
                # Create price observation
                obs = PriceObservation(
                    market_id=market_obj.market_id,
                    commodity=commodity,
                    price=Price(
                        amount=price_value,
                        currency=Currency.YER,
                        unit=commodity.standard_unit
                    ),
                    observed_date=date,
                    source='WFP',
                    quality='standard',
                    observations_count=30
                )
                
                # Add additional attributes for analysis
                obs.conflict_intensity = conflict_intensity
                obs.lag_price = base_price if i == 0 else observations[-len(commodities)].price.amount
                obs.global_price_index = 100 + i * 0.2
                obs.exchange_rate = 500 + i * 10  # YER/USD
                
                observations.append(obs)
    
    # Create panel data
    panel_data = PanelData(observations=observations)
    
    return panel_data


@pytest.fixture
def three_tier_service():
    """Create three-tier analysis service with V1 adapters."""
    return ThreeTierAnalysisService(
        tier1_adapter=Tier1Adapter({
            'entity_effects': True,
            'time_effects': True,
            'cluster_entity': True,
            'validate_conflict_effect': True
        }),
        tier2_adapter=Tier2Adapter({
            'min_markets': 3,
            'min_periods': 20,
            'threshold_test': True
        }),
        tier3_adapter=Tier3Adapter({
            'n_factors': 2,
            'n_components': 3,
            'conflict_validation': False  # No conflict data in this test
        })
    )


@pytest.mark.asyncio
async def test_tier1_conflict_effect_validation(three_tier_service, sample_panel_data):
    """Test that Tier 1 produces the expected 35% conflict effect."""
    # Run only Tier 1
    tier1_results = await three_tier_service._run_tier1(
        sample_panel_data,
        {'apply_corrections': True}
    )
    
    # Verify conflict effect
    conflict_effect = tier1_results['conflict_effect']
    
    assert conflict_effect['is_valid'], f"Conflict validation failed: {conflict_effect['validation_message']}"
    assert 0.30 <= conflict_effect['coefficient'] <= 0.40, \
        f"Conflict coefficient {conflict_effect['coefficient']:.3f} outside expected range"
    assert conflict_effect['p_value'] < 0.001, \
        f"Conflict effect not significant (p={conflict_effect['p_value']:.4f})"
    
    # Verify model fit
    assert tier1_results['model_fit']['r_squared'] > 0.5, \
        f"R-squared {tier1_results['model_fit']['r_squared']:.3f} too low"


@pytest.mark.asyncio
async def test_tier2_commodity_analysis(three_tier_service, sample_panel_data):
    """Test Tier 2 commodity-specific analysis."""
    # Run Tier 2
    tier2_results = await three_tier_service._run_tier2(
        sample_panel_data,
        {'threshold_test': True}
    )
    
    # Verify structure
    assert 'commodity_results' in tier2_results or 'summary' in tier2_results
    
    # Check commodity results
    if 'commodity_results' in tier2_results:
        commodity_results = tier2_results['commodity_results']
        
        # Should have results for all commodities
        assert len(commodity_results) >= 4
        
        # Check each commodity
        for comm_name, comm_results in commodity_results.items():
            if 'error' not in comm_results:
                assert 'integration_level' in comm_results
                assert 'n_markets' in comm_results
                assert comm_results['n_markets'] >= 3  # Minimum markets
    
    # Check summary
    if 'summary' in tier2_results:
        summary = tier2_results['summary']
        assert 'total_commodities' in summary
        assert summary['total_commodities'] >= 4


@pytest.mark.asyncio
async def test_tier3_validation(three_tier_service, sample_panel_data):
    """Test Tier 3 validation models."""
    # Need Tier 1 results first
    tier1_results = await three_tier_service._run_tier1(
        sample_panel_data,
        {}
    )
    
    # Run Tier 3
    tier3_results = await three_tier_service._run_tier3(
        sample_panel_data,
        tier1_results,
        {},  # Empty Tier 2 results for this test
        None,  # No conflict data
        {'cross_tier_validation': False}
    )
    
    # Verify validation results
    assert 'validation_summary' in tier3_results
    summary = tier3_results['validation_summary']
    
    assert len(summary['models_completed']) > 0
    assert 'key_findings' in summary


@pytest.mark.asyncio
async def test_full_three_tier_analysis(three_tier_service, sample_panel_data):
    """Test complete three-tier analysis with V1/V2 parity."""
    # Run full analysis
    results = await three_tier_service.run_analysis(
        sample_panel_data,
        config={
            'validate_conflict_effect': True,
            'apply_corrections': True,
            'run_diagnostics': True
        }
    )
    
    # Verify overall structure
    assert results['analysis_type'] == 'three_tier'
    assert 'tiers' in results
    assert all(tier in results['tiers'] for tier in ['tier1', 'tier2', 'tier3'])
    
    # Verify Tier 1 conflict effect
    tier1 = results['tiers']['tier1']
    conflict_effect = tier1['conflict_effect']
    assert conflict_effect['is_valid']
    assert 0.30 <= conflict_effect['coefficient'] <= 0.40
    
    # Verify summary
    assert 'summary' in results
    summary = results['summary']
    assert 'conflict_effect' in summary
    assert len(summary['key_findings']) > 0
    
    # Verify confidence scores
    assert 'confidence_scores' in results
    scores = results['confidence_scores']
    assert scores['overall'] > 0.7  # High confidence expected


@pytest.mark.asyncio
async def test_diagnostic_corrections(three_tier_service, sample_panel_data):
    """Test that diagnostic failures trigger corrections."""
    # Modify data to introduce serial correlation
    obs_list = list(sample_panel_data.observations)
    for i in range(1, len(obs_list)):
        if i % 4 == 0:  # Every 4th observation
            obs_list[i].price.amount = obs_list[i-1].price.amount * 1.1
    
    modified_panel = PanelData(observations=obs_list)
    
    # Run Tier 1 with diagnostics
    tier1_results = await three_tier_service._run_tier1(
        modified_panel,
        {'apply_corrections': True, 'run_diagnostics': True}
    )
    
    # Check if corrections were applied
    if 'corrections_applied' in tier1_results:
        corrections = tier1_results['corrections_applied']
        # Should detect and correct for serial correlation
        assert 'standard_errors' in corrections
        assert corrections['standard_errors'] in ['newey_west', 'driscoll_kraay']


@pytest.mark.asyncio
async def test_numerical_accuracy(three_tier_service, sample_panel_data):
    """Test numerical accuracy matches V1 within tolerance."""
    # Run analysis twice to check consistency
    results1 = await three_tier_service.run_analysis(sample_panel_data)
    results2 = await three_tier_service.run_analysis(sample_panel_data)
    
    # Compare key metrics
    coef1 = results1['tiers']['tier1']['conflict_effect']['coefficient']
    coef2 = results2['tiers']['tier1']['conflict_effect']['coefficient']
    
    # Should be identical (deterministic)
    assert abs(coef1 - coef2) < 1e-10
    
    # R-squared should also match
    r2_1 = results1['tiers']['tier1']['model_fit']['r_squared']
    r2_2 = results2['tiers']['tier1']['model_fit']['r_squared']
    
    assert abs(r2_1 - r2_2) < 1e-10


@pytest.mark.skip(reason="Requires actual V1 installation")
@pytest.mark.asyncio
async def test_v1_v2_exact_parity():
    """Test exact parity between V1 and V2 results on same data."""
    # This test would:
    # 1. Load the same data in both V1 and V2 formats
    # 2. Run both analyses
    # 3. Compare results within tolerance
    # 
    # Skipped as it requires full V1 setup
    pass