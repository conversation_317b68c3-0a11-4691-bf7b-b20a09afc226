"""Integration tests for market API endpoints."""

import pytest
from datetime import date
from httpx import AsyncClient
from fastapi import status

from v2.src.interfaces.api.rest.app import app


@pytest.mark.asyncio
class TestMarketEndpoints:
    """Test suite for market endpoints."""
    
    async def test_list_markets(self, client: AsyncClient):
        """Test listing markets with pagination."""
        response = await client.get("/api/v1/markets?skip=0&limit=10")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Verify response structure
        assert "markets" in data
        assert "total" in data
        assert "skip" in data
        assert "limit" in data
        
        # Verify pagination
        assert data["skip"] == 0
        assert data["limit"] == 10
        assert isinstance(data["markets"], list)
    
    async def test_list_markets_with_filters(self, client: AsyncClient):
        """Test listing markets with filters."""
        response = await client.get(
            "/api/v1/markets",
            params={
                "governorate": "Sana'a",
                "market_type": "wholesale",
                "active_at": "2023-06-01T00:00:00",
                "skip": 0,
                "limit": 20
            }
        )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # All returned markets should match filters
        for market in data["markets"]:
            assert market["governorate"] == "Sana'a"
            assert market["market_type"] == "wholesale"
    
    async def test_get_market_by_id(self, client: AsyncClient):
        """Test getting a specific market."""
        # First, get a market ID from the list
        list_response = await client.get("/api/v1/markets?limit=1")
        markets = list_response.json()["markets"]
        
        if markets:
            market_id = markets[0]["market_id"]
            
            response = await client.get(f"/api/v1/markets/{market_id}")
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            
            # Verify market data
            assert data["market_id"] == market_id
            assert "name" in data
            assert "governorate" in data
            assert "latitude" in data
            assert "longitude" in data
    
    async def test_get_market_not_found(self, client: AsyncClient):
        """Test getting non-existent market."""
        response = await client.get("/api/v1/markets/NON_EXISTENT_MARKET")
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "error" in data
    
    async def test_get_market_prices(self, client: AsyncClient):
        """Test getting price series for a market."""
        # First, get a market ID
        list_response = await client.get("/api/v1/markets?limit=1")
        markets = list_response.json()["markets"]
        
        if markets:
            market_id = markets[0]["market_id"]
            
            response = await client.get(
                f"/api/v1/markets/{market_id}/prices",
                params={
                    "commodity": "Wheat",
                    "start_date": "2023-01-01",
                    "end_date": "2023-12-31",
                    "currency": "USD"
                }
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            
            # Verify response structure
            assert data["market_id"] == market_id
            assert data["currency"] == "USD"
            assert "data" in data
            assert "statistics" in data
            
            # Verify statistics
            stats = data["statistics"]
            assert "count" in stats
            assert "mean" in stats
            assert "trend" in stats
    
    async def test_get_market_prices_invalid_currency(self, client: AsyncClient):
        """Test getting prices with invalid currency."""
        response = await client.get(
            "/api/v1/markets/SANAA_CENTRAL/prices",
            params={"currency": "EUR"}  # Invalid currency
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    async def test_get_market_accessibility(self, client: AsyncClient):
        """Test getting market accessibility metrics."""
        # First, get a market ID
        list_response = await client.get("/api/v1/markets?limit=1")
        markets = list_response.json()["markets"]
        
        if markets:
            market_id = markets[0]["market_id"]
            
            response = await client.get(
                f"/api/v1/markets/{market_id}/accessibility",
                params={"k_nearest": 5}
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            
            # Verify response structure
            assert data["market_id"] == market_id
            assert "accessibility_metrics" in data
            
            metrics = data["accessibility_metrics"]
            assert "isolation_index" in metrics
            assert "connectivity_score" in metrics
            assert "nearest_markets" in metrics
            assert isinstance(metrics["nearest_markets"], list)


@pytest.fixture
async def client():
    """Create test client."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac