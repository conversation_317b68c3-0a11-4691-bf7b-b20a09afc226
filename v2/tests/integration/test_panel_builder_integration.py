"""Integration tests for Panel Builder Service."""

import pytest
from datetime import datetime
from decimal import Decimal
import pandas as pd
import numpy as np
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from v2.src.application.services.panel_builder_service import (
    PanelBuilderService, PanelConfiguration
)
from v2.src.core.domain.market.entities import Market, PriceObservation
from v2.src.core.domain.market.value_objects import (
    MarketId, Commodity, Price, Currency, Coordinates, MarketType, ControlStatus
)
from v2.src.core.domain.conflict.entities import ConflictEvent
from v2.src.core.domain.geography.services import SpatialService
from v2.src.infrastructure.persistence.unit_of_work import SQLAlchemyUnitOfWork
from v2.src.infrastructure.persistence.repositories import (
    SQLAlchemyMarketRepository, SQLAlchemyPriceRepository, SQLAlchemyConflictRepository
)


@pytest.fixture
async def db_engine():
    """Create test database engine."""
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        echo=False
    )
    
    # Create tables
    async with engine.begin() as conn:
        # In real implementation, would create tables from models
        await conn.execute("""
            CREATE TABLE markets (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                governorate TEXT NOT NULL,
                district TEXT NOT NULL,
                latitude REAL,
                longitude REAL,
                market_type TEXT,
                active_since TIMESTAMP,
                active_until TIMESTAMP
            )
        """)
        
        await conn.execute("""
            CREATE TABLE price_observations (
                id TEXT PRIMARY KEY,
                market_id TEXT NOT NULL,
                commodity_name TEXT NOT NULL,
                price_amount DECIMAL NOT NULL,
                currency TEXT NOT NULL,
                unit TEXT NOT NULL,
                observed_date TIMESTAMP NOT NULL,
                source TEXT,
                quality TEXT,
                FOREIGN KEY (market_id) REFERENCES markets(id)
            )
        """)
        
        await conn.execute("""
            CREATE TABLE conflict_events (
                id TEXT PRIMARY KEY,
                event_date TIMESTAMP NOT NULL,
                event_type TEXT NOT NULL,
                latitude REAL,
                longitude REAL,
                fatalities INTEGER
            )
        """)
    
    yield engine
    
    # Cleanup
    await engine.dispose()


@pytest.fixture
async def session_factory(db_engine):
    """Create session factory."""
    return sessionmaker(
        bind=db_engine,
        class_=AsyncSession,
        expire_on_commit=False
    )


@pytest.fixture
async def uow(session_factory):
    """Create unit of work."""
    return SQLAlchemyUnitOfWork(session_factory)


@pytest.fixture
def spatial_service():
    """Create spatial service."""
    return SpatialService()


@pytest.fixture
async def seed_data(uow):
    """Seed test data."""
    async with uow:
        # Create markets
        markets = []
        for i in range(21):  # 21 markets (Aden, Taiz, Sana'a, etc.)
            market = Market(
                market_id=MarketId(f"GOV{i//7}_MARKET_{i}"),
                name=f"Market {i}",
                coordinates=Coordinates(
                    latitude=12.0 + (i % 7) * 0.5,
                    longitude=44.0 + (i // 7) * 0.5
                ),
                market_type=MarketType.RETAIL if i % 3 != 0 else MarketType.WHOLESALE,
                governorate=f"Governorate_{i//7}",
                district=f"District_{i}",
                active_since=datetime(2018, 1, 1),
                control_status=ControlStatus.GOVERNMENT if i % 2 == 0 else ControlStatus.HOUTHI
            )
            markets.append(market)
        
        market_repo = uow.get_repository(SQLAlchemyMarketRepository)
        for market in markets:
            await market_repo.add(market)
        
        # Create price observations
        commodities = [
            "Wheat", "Wheat Flour", "Rice (Imported)", "Sugar", "Oil (Vegetable)",
            "Beans (Kidney Red)", "Beans (White)", "Lentils", "Salt", "Eggs",
            "Onions", "Tomatoes", "Potatoes", "Fuel (Diesel)", "Fuel (Petrol-Gasoline)", "Fuel (Gas)"
        ]
        
        price_repo = uow.get_repository(SQLAlchemyPriceRepository)
        
        # Generate 75 months of data (2019-01 to 2024-03)
        for month_offset in range(75):
            date = datetime(2019, 1, 15) + pd.DateOffset(months=month_offset)
            
            for market in markets:
                for commodity in commodities:
                    # Simulate missing data (88.4% coverage target)
                    if np.random.random() > 0.884:
                        continue
                    
                    # Base price varies by commodity
                    base_price = {
                        "Wheat": 400, "Wheat Flour": 450, "Rice (Imported)": 600,
                        "Sugar": 800, "Oil (Vegetable)": 1200, "Salt": 200,
                        "Eggs": 1500, "Fuel (Diesel)": 700, "Fuel (Petrol-Gasoline)": 750
                    }.get(commodity, 500)
                    
                    # Add market-specific variation
                    market_factor = 1.0 + (hash(market.market_id.value) % 20 - 10) / 100
                    
                    # Add temporal variation
                    seasonal_factor = 1.0 + 0.1 * np.sin(2 * np.pi * month_offset / 12)
                    
                    # Add random noise
                    noise = np.random.normal(1.0, 0.05)
                    
                    price_amount = base_price * market_factor * seasonal_factor * noise
                    
                    price = PriceObservation(
                        market_id=market.market_id,
                        commodity=Commodity(name=commodity, category="food"),
                        price=Price(
                            amount=Decimal(str(round(price_amount, 2))),
                            currency=Currency.YER,
                            unit="kg"
                        ),
                        observed_date=date.to_pydatetime(),
                        source="WFP",
                        quality="standard"
                    )
                    
                    await price_repo.add(price)
        
        # Create conflict events
        conflict_repo = uow.get_repository(SQLAlchemyConflictRepository)
        
        for month_offset in range(75):
            date = datetime(2019, 1, 1) + pd.DateOffset(months=month_offset)
            
            # Variable conflict intensity
            n_events = np.random.poisson(5)  # Average 5 events per month
            
            for _ in range(n_events):
                event_date = date + pd.Timedelta(days=np.random.randint(0, 28))
                
                conflict = ConflictEvent(
                    event_id=f"CONFLICT_{month_offset}_{_}",
                    event_date=event_date.to_pydatetime(),
                    event_type=np.random.choice(["battles", "explosions", "violence_against_civilians"]),
                    coordinates=Coordinates(
                        latitude=12.0 + np.random.uniform(0, 3.5),
                        longitude=44.0 + np.random.uniform(0, 1.5)
                    ),
                    fatalities=np.random.poisson(2)
                )
                
                await conflict_repo.add(conflict)
        
        await uow.commit()


class TestPanelBuilderIntegration:
    """Integration tests for Panel Builder Service."""
    
    @pytest.mark.asyncio
    async def test_create_balanced_panel_full_integration(
        self,
        uow,
        spatial_service,
        seed_data
    ):
        """Test full panel creation with real data."""
        # Create service
        panel_builder = PanelBuilderService(
            unit_of_work=uow,
            spatial_service=spatial_service
        )
        
        # Create configuration
        config = PanelConfiguration(
            start_date=datetime(2019, 1, 1),
            end_date=datetime(2019, 12, 31),
            frequency="M",
            min_coverage_pct=80.0,  # Slightly lower for test data
            min_markets_per_commodity=15,
            min_commodities_per_market=10
        )
        
        # Create panel
        panel = await panel_builder.create_balanced_panel(config)
        
        # Verify panel structure
        assert isinstance(panel, pd.DataFrame)
        assert len(panel) > 0
        
        # Check required columns
        required_columns = [
            "market_id", "commodity", "date", "price",
            "governorate", "district", "latitude", "longitude"
        ]
        for col in required_columns:
            assert col in panel.columns
        
        # Check panel dimensions
        assert panel["market_id"].nunique() >= 15
        assert panel["commodity"].nunique() >= 10
        assert panel["date"].nunique() == 12
        
        # Check temporal features
        temporal_features = [
            "price_lag1", "price_lag2", "price_lag3",
            "price_diff", "price_pct_change", "price_ma3"
        ]
        for feature in temporal_features:
            assert feature in panel.columns
        
        # Check spatial features
        if "latitude" in panel.columns and panel["latitude"].notna().any():
            assert "distance_to_capital_km" in panel.columns
            assert "has_port_access" in panel.columns
        
        # Check conflict integration
        conflict_features = [
            "events_total", "events_battles", "fatalities_total",
            "conflict_intensity", "conflict_ma3"
        ]
        for feature in conflict_features:
            assert feature in panel.columns
    
    @pytest.mark.asyncio
    async def test_coverage_optimization(
        self,
        uow,
        spatial_service,
        seed_data
    ):
        """Test that panel builder optimizes for coverage."""
        # Create service
        panel_builder = PanelBuilderService(
            unit_of_work=uow,
            spatial_service=spatial_service
        )
        
        # Create configuration with strict coverage requirements
        config = PanelConfiguration(
            start_date=datetime(2019, 1, 1),
            end_date=datetime(2019, 12, 31),
            frequency="M",
            min_coverage_pct=85.0,
            min_markets_per_commodity=20,
            min_commodities_per_market=15
        )
        
        # Create panel
        panel = await panel_builder.create_balanced_panel(config)
        
        # Calculate actual coverage
        price_coverage = panel["price"].notna().sum() / len(panel) * 100
        
        # Should achieve good coverage after interpolation
        assert price_coverage >= 80.0  # Allow some tolerance
    
    @pytest.mark.asyncio
    async def test_missing_data_handling(
        self,
        uow,
        spatial_service,
        seed_data
    ):
        """Test missing data interpolation and filling."""
        # Create service
        panel_builder = PanelBuilderService(
            unit_of_work=uow,
            spatial_service=spatial_service
        )
        
        # Create configuration
        config = PanelConfiguration(
            start_date=datetime(2019, 1, 1),
            end_date=datetime(2019, 6, 30),
            frequency="M",
            interpolation_limit=2,
            seasonal_adjustment=True
        )
        
        # Create panel
        panel = await panel_builder.create_balanced_panel(config)
        
        # Check that interpolation was applied
        # Group by market-commodity and check for interpolated values
        for (market, commodity), group in panel.groupby(["market_id", "commodity"]):
            prices = group.sort_values("date")["price"]
            
            # If there were gaps, they should be filled
            if prices.isna().any():
                # Check that no more than 2 consecutive NaN values remain
                is_nan = prices.isna()
                consecutive_nans = is_nan.groupby((is_nan != is_nan.shift()).cumsum()).sum()
                assert consecutive_nans.max() <= config.interpolation_limit
    
    @pytest.mark.asyncio
    async def test_model_specific_panels(
        self,
        uow,
        spatial_service,
        seed_data
    ):
        """Test creation of model-specific panels."""
        # Create service
        panel_builder = PanelBuilderService(
            unit_of_work=uow,
            spatial_service=spatial_service
        )
        
        # Create base panel
        config = PanelConfiguration(
            start_date=datetime(2019, 1, 1),
            end_date=datetime(2019, 12, 31),
            frequency="M"
        )
        
        base_panel = await panel_builder.create_balanced_panel(config)
        
        # Create model-specific panels
        panels = await panel_builder.create_model_specific_panels(base_panel)
        
        # Check price transmission panel
        if "price_transmission" in panels and not panels["price_transmission"].empty:
            pt_panel = panels["price_transmission"]
            assert "market1_id" in pt_panel.columns
            assert "market2_id" in pt_panel.columns
            assert "price_ratio" in pt_panel.columns
            assert "log_price_diff" in pt_panel.columns
        
        # Check threshold panel
        if "threshold_coint" in panels:
            tc_panel = panels["threshold_coint"]
            assert "log_price" in tc_panel.columns
            assert "d_log_price" in tc_panel.columns
            assert "time_trend" in tc_panel.columns
    
    @pytest.mark.asyncio
    async def test_v1_compatibility(
        self,
        uow,
        spatial_service,
        seed_data,
        tmp_path
    ):
        """Test compatibility with V1 panel structure."""
        # Create service
        panel_builder = PanelBuilderService(
            unit_of_work=uow,
            spatial_service=spatial_service
        )
        
        # Create panel
        config = PanelConfiguration(
            start_date=datetime(2019, 1, 1),
            end_date=datetime(2019, 3, 31),
            frequency="M"
        )
        
        v2_panel = await panel_builder.create_balanced_panel(config)
        
        # Create mock V1 panel
        v1_panel = pd.DataFrame({
            "market": v2_panel["market_id"],
            "commodity": v2_panel["commodity"],
            "date": v2_panel["date"],
            "price": v2_panel["price"],
            "admin1": v2_panel["governorate"],
            "admin2": v2_panel["district"]
        })
        
        v1_path = tmp_path / "v1_panel.parquet"
        v1_panel.to_parquet(v1_path)
        
        # Validate compatibility
        validation = await panel_builder.validate_against_v1(v2_panel, v1_path)
        
        # Should have similar coverage
        assert abs(
            validation["coverage_comparison"]["v1_coverage"] -
            validation["coverage_comparison"]["v2_coverage"]
        ) < 5.0  # Within 5% difference
    
    @pytest.mark.asyncio
    async def test_performance_large_panel(
        self,
        uow,
        spatial_service,
        seed_data
    ):
        """Test performance with larger panel."""
        import time
        
        # Create service
        panel_builder = PanelBuilderService(
            unit_of_work=uow,
            spatial_service=spatial_service,
            cache_enabled=True
        )
        
        # Create configuration for 2 years
        config = PanelConfiguration(
            start_date=datetime(2019, 1, 1),
            end_date=datetime(2020, 12, 31),
            frequency="M",
            include_spatial_features=False,  # Disable for speed
            include_conflict_data=False
        )
        
        # Measure time
        start_time = time.time()
        panel = await panel_builder.create_balanced_panel(config)
        elapsed = time.time() - start_time
        
        # Should complete reasonably fast
        assert elapsed < 30.0  # 30 seconds max
        
        # Check panel size
        expected_size = 21 * 16 * 24  # markets × commodities × months
        assert len(panel) == pytest.approx(expected_size, rel=0.1)