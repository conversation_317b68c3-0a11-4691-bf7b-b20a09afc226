"""Specific tests for validating the 35% conflict effect finding.

These tests ensure the V2 implementation correctly reproduces the critical
35% conflict effect finding from the V1 analysis.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta
from typing import List, Tuple

from v2.src.application.services.three_tier_analysis_service import ThreeTierAnalysisService
from v2.src.infrastructure.adapters.tier1_adapter import Tier1Adapter
from v2.src.core.domain.market.entities import PanelData, Market, PriceObservation
from v2.src.core.domain.market.value_objects import (
    MarketId, Commodity, Price, Currency, Coordinates, MarketType
)


def generate_conflict_panel_data(
    conflict_effect_size: float = 0.35,
    n_markets: int = 20,
    n_commodities: int = 10,
    n_periods: int = 36,
    conflict_markets_ratio: float = 0.5
) -> <PERSON><PERSON>[PanelData, pd.DataFrame]:
    """Generate panel data with known conflict effect.
    
    Parameters
    ----------
    conflict_effect_size : float
        True conflict effect (default: 0.35 for 35%)
    n_markets : int
        Number of markets
    n_commodities : int
        Number of commodities
    n_periods : int
        Number of time periods
    conflict_markets_ratio : float
        Proportion of markets with conflict
        
    Returns
    -------
    tuple
        (PanelData, true_parameters_df)
    """
    np.random.seed(12345)  # For reproducibility
    
    # Generate market names
    governorates = ['Sana\'a', 'Aden', 'Taiz', 'Hodeidah', 'Ibb', 'Dhamar']
    markets = []
    for i in range(n_markets):
        gov = governorates[i % len(governorates)]
        market = Market(
            market_id=MarketId(f"{gov}_Market{i}"),
            name=f"Market{i}",
            coordinates=Coordinates(15.0 + np.random.random(), 44.0 + np.random.random()),
            market_type=MarketType.RETAIL,
            governorate=gov,
            district=f"{gov}_District",
            active_since=datetime(2020, 1, 1)
        )
        markets.append(market)
    
    # Determine conflict markets
    n_conflict_markets = int(n_markets * conflict_markets_ratio)
    conflict_markets = np.random.choice(range(n_markets), n_conflict_markets, replace=False)
    
    # Generate commodities
    commodity_names = [
        'Wheat', 'Rice', 'Sugar', 'Salt', 'Oil (Vegetable)',
        'Beans (Kidney Red)', 'Lentils', 'Fuel (Diesel)', 
        'Fuel (Petrol)', 'Eggs'
    ][:n_commodities]
    
    commodities = []
    for name in commodity_names:
        commodity = Commodity(
            code=name.upper().replace(' ', '_').replace('(', '').replace(')', ''),
            name=name,
            category='fuel' if 'Fuel' in name else 'food',
            standard_unit='liter' if 'Fuel' in name else 'kg'
        )
        commodities.append(commodity)
    
    # Generate time periods
    start_date = datetime(2021, 1, 1)
    dates = [start_date + timedelta(days=30*i) for i in range(n_periods)]
    
    # Generate panel data
    observations = []
    true_params = []
    
    for m_idx, market in enumerate(markets):
        is_conflict_market = m_idx in conflict_markets
        
        for commodity in commodities:
            # Commodity-specific parameters
            base_price = 100 * (1 + 0.5 * np.random.random())
            trend = 0.02 * np.random.random()  # 0-2% monthly trend
            
            # Generate price series
            for t_idx, date in enumerate(dates):
                # Time trend
                time_effect = base_price * (1 + trend * t_idx)
                
                # Seasonal effect
                seasonal = 0.1 * base_price * np.sin(2 * np.pi * t_idx / 12)
                
                # Conflict intensity (varies over time for conflict markets)
                if is_conflict_market:
                    # Poisson-distributed conflict events
                    conflict_intensity = np.random.poisson(3)
                else:
                    conflict_intensity = 0
                
                # Apply conflict effect
                # Log specification: log(price) = ... + β * conflict_intensity
                # This implies: price = base_price * exp(β * conflict_intensity)
                # For 35% effect when conflict_intensity = 1: exp(β) = 1.35, so β ≈ 0.30
                conflict_multiplier = np.exp(np.log(1 + conflict_effect_size) * conflict_intensity / 3)
                
                # Random error
                error = np.random.normal(0, 0.05 * base_price)
                
                # Final price
                price_value = (time_effect + seasonal) * conflict_multiplier + error
                price_value = max(price_value, 10)  # Ensure positive
                
                # Create observation
                obs = PriceObservation(
                    market_id=market.market_id,
                    commodity=commodity,
                    price=Price(
                        amount=price_value * 500,  # Convert to YER
                        currency=Currency.YER,
                        unit=commodity.standard_unit
                    ),
                    observed_date=date,
                    source='WFP',
                    quality='standard',
                    observations_count=30
                )
                
                # Add analysis attributes
                obs.conflict_intensity = conflict_intensity
                obs.lag_price = base_price * 500 if t_idx == 0 else observations[-1].price.amount
                obs.global_price_index = 100 * (1 + 0.001 * t_idx)
                obs.exchange_rate = 500 + t_idx * 5
                
                observations.append(obs)
            
            # Store true parameters
            true_params.append({
                'market': str(market.market_id),
                'commodity': commodity.name,
                'is_conflict': is_conflict_market,
                'base_price': base_price,
                'trend': trend
            })
    
    panel_data = PanelData(observations=observations)
    true_params_df = pd.DataFrame(true_params)
    
    return panel_data, true_params_df


@pytest.mark.parametrize("true_effect", [0.25, 0.30, 0.35, 0.40, 0.45])
@pytest.mark.asyncio
async def test_conflict_effect_recovery(true_effect):
    """Test that we can recover different true conflict effects."""
    # Generate data with known effect
    panel_data, _ = generate_conflict_panel_data(
        conflict_effect_size=true_effect,
        n_markets=20,
        n_commodities=5,
        n_periods=24
    )
    
    # Create adapter with appropriate tolerance
    tier1_adapter = Tier1Adapter({
        'entity_effects': True,
        'time_effects': True,
        'cluster_entity': True,
        'validate_conflict_effect': False  # Don't validate against 35% for this test
    })
    
    # Prepare and run analysis
    data = await tier1_adapter.prepare_data(panel_data)
    results = await tier1_adapter.run_model(data)
    extracted = await tier1_adapter.extract_results(results)
    
    # Check recovered effect
    estimated_effect = extracted['conflict_effect']['coefficient']
    
    # Should recover true effect within reasonable tolerance
    tolerance = 0.10  # 10% relative error
    assert abs(estimated_effect - true_effect) / true_effect < tolerance, \
        f"Failed to recover true effect {true_effect:.3f}, got {estimated_effect:.3f}"


@pytest.mark.asyncio
async def test_conflict_effect_robustness():
    """Test conflict effect estimation is robust to various data conditions."""
    # Test 1: Standard case (should find ~35% effect)
    panel_data, _ = generate_conflict_panel_data(conflict_effect_size=0.35)
    
    service = ThreeTierAnalysisService()
    results = await service._run_tier1(panel_data, {'apply_corrections': True})
    
    conflict_effect = results['conflict_effect']
    assert conflict_effect['is_valid'], "Standard case should validate"
    assert 0.30 <= conflict_effect['coefficient'] <= 0.40
    
    # Test 2: High noise case
    noisy_data, _ = generate_conflict_panel_data(conflict_effect_size=0.35)
    # Add noise to prices
    for obs in noisy_data.observations:
        obs.price.amount *= np.random.normal(1, 0.1)
    
    results_noisy = await service._run_tier1(noisy_data, {'apply_corrections': True})
    
    # Should still detect effect but with higher standard error
    assert results_noisy['conflict_effect']['std_error'] > conflict_effect['std_error']
    
    # Test 3: Unbalanced panel (missing observations)
    unbalanced_data, _ = generate_conflict_panel_data(conflict_effect_size=0.35)
    # Remove 20% of observations randomly
    n_remove = int(len(unbalanced_data.observations) * 0.2)
    indices_to_remove = np.random.choice(
        len(unbalanced_data.observations), n_remove, replace=False
    )
    
    kept_obs = [
        obs for i, obs in enumerate(unbalanced_data.observations) 
        if i not in indices_to_remove
    ]
    unbalanced_panel = PanelData(observations=kept_obs)
    
    results_unbalanced = await service._run_tier1(
        unbalanced_panel, 
        {'apply_corrections': True}
    )
    
    # Should still find significant effect
    assert results_unbalanced['conflict_effect']['p_value'] < 0.05


@pytest.mark.asyncio  
async def test_diagnostic_corrections_for_conflict():
    """Test that diagnostic corrections don't bias conflict effect estimation."""
    # Generate data with serial correlation
    panel_data, _ = generate_conflict_panel_data(conflict_effect_size=0.35)
    
    # Introduce serial correlation
    for i in range(1, len(panel_data.observations)):
        if i % 10 != 0:  # Same commodity series
            prev_price = panel_data.observations[i-1].price.amount
            curr_price = panel_data.observations[i].price.amount
            # AR(1) component
            panel_data.observations[i].price.amount = 0.7 * prev_price + 0.3 * curr_price
    
    # Run with and without corrections
    adapter_no_correction = Tier1Adapter({
        'standard_errors': 'cluster_robust',
        'validate_conflict_effect': True
    })
    
    adapter_with_correction = Tier1Adapter({
        'standard_errors': 'cluster_robust',
        'validate_conflict_effect': True
    })
    
    # Without correction
    data = await adapter_no_correction.prepare_data(panel_data)
    results_no_corr = await adapter_no_correction.run_model(data)
    extracted_no_corr = await adapter_no_correction.extract_results(results_no_corr)
    
    # With correction (will be applied if diagnostics fail)
    results_with_corr = await adapter_with_correction.run_model(data)
    results_with_corr['data'] = data
    extracted_with_corr = await adapter_with_correction.extract_results(results_with_corr)
    
    # Both should find similar point estimates
    coef_no_corr = extracted_no_corr['conflict_effect']['coefficient']
    coef_with_corr = extracted_with_corr['conflict_effect']['coefficient']
    
    assert abs(coef_no_corr - coef_with_corr) < 0.05, \
        "Corrections should not substantially change point estimate"
    
    # But standard errors might differ
    se_no_corr = extracted_no_corr['conflict_effect']['std_error']
    se_with_corr = extracted_with_corr['conflict_effect']['std_error']
    
    # Corrected SEs usually larger (more conservative)
    assert se_with_corr >= se_no_corr * 0.9  # Allow some variation


@pytest.mark.asyncio
async def test_conflict_effect_by_commodity():
    """Test that conflict effect varies reasonably by commodity type."""
    # Generate data
    panel_data, true_params = generate_conflict_panel_data(
        conflict_effect_size=0.35,
        n_commodities=10
    )
    
    # Run Tier 2 analysis
    service = ThreeTierAnalysisService()
    tier2_results = await service._run_tier2(panel_data, {})
    
    # Extract commodity-specific effects
    commodity_effects = {}
    
    if 'commodity_results' in tier2_results:
        for commodity, results in tier2_results['commodity_results'].items():
            if 'coefficients' in results and 'conflict_intensity' in results['coefficients']:
                commodity_effects[commodity] = results['coefficients']['conflict_intensity']
    
    # Check patterns
    if commodity_effects:
        # Fuel commodities might have different effects
        fuel_effects = [
            effect for comm, effect in commodity_effects.items() 
            if 'FUEL' in comm
        ]
        
        food_effects = [
            effect for comm, effect in commodity_effects.items() 
            if 'FUEL' not in comm
        ]
        
        if fuel_effects and food_effects:
            # Effects should be positive for both
            assert all(e > 0 for e in fuel_effects + food_effects), \
                "All commodities should show positive conflict effects"


@pytest.mark.asyncio
async def test_full_pipeline_conflict_validation():
    """Test complete three-tier analysis validates conflict effect."""
    # Generate comprehensive test data
    panel_data, _ = generate_conflict_panel_data(
        conflict_effect_size=0.35,
        n_markets=30,
        n_commodities=15,
        n_periods=36,
        conflict_markets_ratio=0.4
    )
    
    # Run full three-tier analysis
    service = ThreeTierAnalysisService(
        tier1_adapter=Tier1Adapter({'validate_conflict_effect': True}),
        tier2_adapter=Tier2Adapter({'min_markets': 5}),
        tier3_adapter=Tier3Adapter({'n_factors': 2})
    )
    
    results = await service.run_analysis(
        panel_data,
        config={
            'validate_conflict_effect': True,
            'apply_corrections': True,
            'run_diagnostics': True
        }
    )
    
    # Verify all tiers completed
    assert all(tier in results['tiers'] for tier in ['tier1', 'tier2', 'tier3'])
    
    # Verify conflict effect
    conflict_effect = results['tiers']['tier1']['conflict_effect']
    assert conflict_effect['is_valid'], \
        f"Conflict validation failed: {conflict_effect['validation_message']}"
    
    # Verify it's in the summary
    assert 'Conflict increases prices' in str(results['summary']['key_findings'])
    
    # Verify high confidence
    assert results['confidence_scores']['overall'] > 0.8, \
        "Should have high confidence in results"
    
    # Verify no critical warnings
    assert 'validation_warning' not in results, \
        "Should not have validation warnings"