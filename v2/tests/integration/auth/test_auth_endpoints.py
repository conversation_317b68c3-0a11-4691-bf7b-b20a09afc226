"""Integration tests for authentication endpoints."""

import pytest
from httpx import AsyncClient
from unittest.mock import Mock, patch, AsyncMock

from src.core.domain.auth import User
from src.core.domain.auth.value_objects import User<PERSON><PERSON>, UserStatus, Email, HashedPassword
from src.infrastructure.security import hash_password


@pytest.mark.asyncio
class TestAuthEndpoints:
    """Test authentication API endpoints."""
    
    @pytest.fixture
    def mock_user(self):
        """Create a mock user for testing."""
        return User(
            id="123e4567-e89b-12d3-a456-426614174000",
            username="testuser",
            email=Email(value="<EMAIL>"),
            password_hash=HashedPassword(value=hash_password("TestPassword123!")),
            full_name="Test User",
            roles=[UserRole.ANALYST],
            status=UserStatus.ACTIVE,
            email_verified=True
        )
    
    async def test_login_success(self, client: AsyncClient, mock_user):
        """Test successful login."""
        # Mock authentication service
        with patch('src.shared.container.Container.auth_service') as mock_auth_service:
            mock_service = AsyncMock()
            mock_service.authenticate_user.return_value = (mock_user, None)
            mock_auth_service.return_value = mock_service
            
            response = await client.post(
                "/api/v1/auth/login",
                json={
                    "username": "testuser",
                    "password": "TestPassword123!",
                    "remember_me": False
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert "access_token" in data
            assert data["token_type"] == "bearer"
            assert "expires_in" in data
            assert "user" in data
            assert data["user"]["username"] == "testuser"
            assert data["user"]["email"] == "<EMAIL>"
            assert "analyst" in data["user"]["roles"]
    
    async def test_login_invalid_credentials(self, client: AsyncClient):
        """Test login with invalid credentials."""
        with patch('src.shared.container.Container.auth_service') as mock_auth_service:
            mock_service = AsyncMock()
            mock_service.authenticate_user.return_value = (None, "Invalid username or password")
            mock_auth_service.return_value = mock_service
            
            response = await client.post(
                "/api/v1/auth/login",
                json={
                    "username": "wronguser",
                    "password": "WrongPassword"
                }
            )
            
            assert response.status_code == 401
            assert "Invalid username or password" in response.json()["detail"]
    
    async def test_login_with_remember_me(self, client: AsyncClient, mock_user):
        """Test login with remember_me option."""
        with patch('src.shared.container.Container.auth_service') as mock_auth_service:
            mock_service = AsyncMock()
            mock_service.authenticate_user.return_value = (mock_user, None)
            mock_service.create_refresh_token.return_value = None
            mock_auth_service.return_value = mock_service
            
            response = await client.post(
                "/api/v1/auth/login",
                json={
                    "username": "testuser",
                    "password": "TestPassword123!",
                    "remember_me": True
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert "refresh_token" in data
            assert data["refresh_token"] is not None
    
    async def test_oauth_login(self, client: AsyncClient, mock_user):
        """Test OAuth2 compatible login endpoint."""
        with patch('src.shared.container.Container.auth_service') as mock_auth_service:
            mock_service = AsyncMock()
            mock_service.authenticate_user.return_value = (mock_user, None)
            mock_service.create_refresh_token.return_value = None
            mock_auth_service.return_value = mock_service
            
            response = await client.post(
                "/api/v1/auth/login/oauth",
                data={
                    "username": "testuser",
                    "password": "TestPassword123!",
                    "grant_type": "password"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert "access_token" in data
            assert "refresh_token" in data
    
    async def test_refresh_token(self, client: AsyncClient, mock_user):
        """Test refreshing access token."""
        with patch('src.shared.container.Container.auth_service') as mock_auth_service:
            mock_service = AsyncMock()
            mock_service.validate_refresh_token.return_value = (mock_user, None)
            mock_service.revoke_refresh_token.return_value = None
            mock_service.create_refresh_token.return_value = None
            mock_auth_service.return_value = mock_service
            
            response = await client.post(
                "/api/v1/auth/refresh",
                json={
                    "refresh_token": "valid.refresh.token"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert "access_token" in data
            assert "refresh_token" in data
            assert data["token_type"] == "bearer"
            assert "expires_in" in data
    
    async def test_refresh_token_invalid(self, client: AsyncClient):
        """Test refreshing with invalid token."""
        with patch('src.shared.container.Container.auth_service') as mock_auth_service:
            mock_service = AsyncMock()
            mock_service.validate_refresh_token.return_value = (None, "Invalid refresh token")
            mock_auth_service.return_value = mock_service
            
            response = await client.post(
                "/api/v1/auth/refresh",
                json={
                    "refresh_token": "invalid.refresh.token"
                }
            )
            
            assert response.status_code == 401
            assert "Invalid refresh token" in response.json()["detail"]
    
    async def test_logout(self, client: AsyncClient):
        """Test logout endpoint."""
        with patch('src.shared.container.Container.get_current_user') as mock_get_user:
            mock_get_user.return_value = {"sub": "123", "username": "testuser"}
            
            with patch('src.shared.container.Container.auth_service') as mock_auth_service:
                mock_service = AsyncMock()
                mock_service.revoke_refresh_token.return_value = None
                mock_auth_service.return_value = mock_service
                
                # Need to provide auth header
                headers = {"Authorization": "Bearer valid.token"}
                
                response = await client.post(
                    "/api/v1/auth/logout",
                    json={
                        "refresh_token": "some.refresh.token"
                    },
                    headers=headers
                )
                
                assert response.status_code == 204
    
    async def test_verify_token(self, client: AsyncClient):
        """Test token verification endpoint."""
        with patch('src.shared.container.Container.get_current_user') as mock_get_user:
            mock_get_user.return_value = {
                "sub": "123",
                "username": "testuser",
                "email": "<EMAIL>",
                "roles": ["analyst"]
            }
            
            headers = {"Authorization": "Bearer valid.token"}
            
            response = await client.get(
                "/api/v1/auth/verify",
                headers=headers
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["valid"] is True
            assert data["user"]["username"] == "testuser"
            assert data["user"]["email"] == "<EMAIL>"
            assert "analyst" in data["user"]["roles"]
    
    async def test_password_reset_request(self, client: AsyncClient):
        """Test password reset request."""
        response = await client.post(
            "/api/v1/auth/password-reset/request",
            json={
                "email": "<EMAIL>"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        # Should always return success to prevent user enumeration
        assert "password reset link has been sent" in data["message"]