# Yemen Market Integration V2 - Comprehensive Test Suite

This directory contains the comprehensive test suite for the Yemen Market Integration V2 system, designed to achieve 90% code coverage and ensure production-grade reliability.

## 📁 Test Structure

```
tests/
├── README.md                           # This file
├── conftest.py                         # Basic pytest configuration
├── conftest_enhanced.py                # Enhanced fixtures and test utilities
├── pytest.ini                         # Pytest configuration
│
├── unit/                              # Unit tests (90% of test suite)
│   ├── application/                   # Application service tests
│   │   ├── test_analysis_orchestrator.py
│   │   ├── test_data_preparation_service.py
│   │   └── test_three_tier_analysis_service.py
│   ├── core/                         # Domain logic tests
│   │   ├── domain/
│   │   │   ├── test_repositories.py
│   │   │   ├── market/
│   │   │   ├── conflict/
│   │   │   └── auth/
│   │   └── models/
│   ├── infrastructure/               # Infrastructure tests
│   │   ├── test_security_comprehensive.py
│   │   ├── test_persistence.py
│   │   ├── test_external_services.py
│   │   └── test_diagnostics.py
│   └── interfaces/                   # Interface tests
│       ├── api/
│       └── cli/
│
├── integration/                      # Integration tests
│   ├── test_full_analysis_workflow.py
│   ├── test_api_endpoints.py
│   ├── test_database_integration.py
│   └── test_external_service_mocks.py
│
├── e2e/                             # End-to-end tests
│   ├── test_complete_user_workflows.py
│   ├── test_authentication_flows.py
│   └── test_data_pipeline_e2e.py
│
├── performance/                     # Performance tests
│   ├── test_load_and_stress.py
│   ├── test_memory_usage.py
│   └── test_concurrent_operations.py
│
└── fixtures/                       # Test data and fixtures
    ├── sample_data/
    ├── mock_responses/
    └── test_configs/
```

## 🎯 Test Categories

### Unit Tests (`unit/`)
- **Coverage Target**: 95%
- **Scope**: Individual components in isolation
- **Mocking**: Heavy use of mocks for dependencies
- **Speed**: Fast execution (< 2 seconds per test)

#### Key Areas:
- **Domain Models**: Entity validation, value object behavior
- **Services**: Business logic, orchestration patterns
- **Repositories**: Data access patterns (mocked)
- **Security**: Authentication, authorization, encryption
- **Utilities**: Helper functions, data transformers

### Integration Tests (`integration/`)
- **Coverage Target**: 85%
- **Scope**: Multiple components working together
- **Dependencies**: Real database, mocked external services
- **Speed**: Medium execution (5-30 seconds per test)

#### Key Areas:
- **API Endpoints**: Request/response validation
- **Database Operations**: CRUD operations, transactions
- **Service Integration**: Cross-service communication
- **Event Handling**: Async event processing

### End-to-End Tests (`e2e/`)
- **Coverage Target**: 70%
- **Scope**: Complete user workflows
- **Dependencies**: Full system, real services
- **Speed**: Slow execution (30-300 seconds per test)

#### Key Areas:
- **User Authentication**: Registration, login, logout
- **Data Ingestion**: Complete data pipeline
- **Analysis Workflows**: Full three-tier analysis
- **Policy Simulation**: End-to-end policy modeling

### Performance Tests (`performance/`)
- **Scope**: System performance under load
- **Metrics**: Response time, throughput, resource usage
- **Types**: Load, stress, endurance, spike testing

#### Key Areas:
- **Concurrent Users**: 100+ simultaneous users
- **Large Datasets**: 100k+ price observations
- **Memory Usage**: Leak detection, optimization
- **API Performance**: Response time SLAs

## 🚀 Running Tests

### Quick Start

```bash
# Run all tests with coverage
poetry run pytest --cov=src --cov-report=html

# Run specific test categories
poetry run pytest tests/unit/           # Unit tests only
poetry run pytest tests/integration/   # Integration tests only
poetry run pytest tests/e2e/           # E2E tests only

# Run with markers
poetry run pytest -m "unit"           # All unit tests
poetry run pytest -m "performance"    # Performance tests only
poetry run pytest -m "security"       # Security tests only
```

### Using the Test Runner Script

```bash
# Run comprehensive test suite
python scripts/run_tests.py all

# Run specific test types
python scripts/run_tests.py unit integration
python scripts/run_tests.py performance --verbose

# Run with options
python scripts/run_tests.py unit --no-coverage --fail-fast
```

### CI/CD Integration

The test suite is automatically executed in GitHub Actions:

```bash
# Trigger manual workflow
gh workflow run comprehensive-testing.yml

# Check workflow status
gh run list --workflow=comprehensive-testing.yml
```

## 📊 Coverage Requirements

### Overall Coverage Targets
- **Total Coverage**: 90%+
- **Unit Test Coverage**: 95%+
- **Integration Coverage**: 85%+
- **Critical Path Coverage**: 100%

### Coverage by Component
- **Domain Models**: 98%+
- **Application Services**: 95%+
- **API Endpoints**: 90%+
- **Infrastructure**: 85%+
- **External Integrations**: 80%+

### Generating Coverage Reports

```bash
# Generate HTML coverage report
poetry run pytest --cov=src --cov-report=html
open htmlcov/index.html

# Generate terminal coverage report
poetry run pytest --cov=src --cov-report=term-missing

# Generate XML coverage report (for CI)
poetry run pytest --cov=src --cov-report=xml
```

## 🔧 Test Configuration

### Environment Setup

```bash
# Development environment
export DATABASE_URL="postgresql://dev:dev@localhost:5432/yemen_dev"
export REDIS_URL="redis://localhost:6379/0"
export LOG_LEVEL="DEBUG"

# Test environment
export DATABASE_URL="postgresql://test:test@localhost:5432/yemen_test"
export REDIS_URL="redis://localhost:6379/1"
export LOG_LEVEL="WARNING"
```

### Required Services

For integration and E2E tests:

```bash
# Start PostgreSQL
docker run -d --name postgres-test \
  -e POSTGRES_DB=yemen_test \
  -e POSTGRES_USER=test \
  -e POSTGRES_PASSWORD=test \
  -p 5432:5432 postgres:15

# Start Redis
docker run -d --name redis-test \
  -p 6379:6379 redis:7
```

### Test Data

Test data is managed through fixtures in `conftest_enhanced.py`:

- **Sample Markets**: 5 representative Yemen markets
- **Price Observations**: 4 years of realistic price data
- **Conflict Events**: 100 synthetic conflict events
- **User Accounts**: Multiple user roles and permissions

## 🏷️ Test Markers

Tests are categorized using pytest markers:

```python
# Test type markers
@pytest.mark.unit
@pytest.mark.integration
@pytest.mark.e2e
@pytest.mark.performance

# Feature markers
@pytest.mark.security
@pytest.mark.api
@pytest.mark.database
@pytest.mark.external

# Speed markers
@pytest.mark.slow        # > 5 seconds
@pytest.mark.fast        # < 1 second
```

### Running Tests by Marker

```bash
# Run only fast tests
pytest -m "fast"

# Run security tests
pytest -m "security"

# Run unit tests excluding slow ones
pytest -m "unit and not slow"

# Run API and database tests
pytest -m "api or database"
```

## 🔒 Security Testing

Comprehensive security testing covers:

### Authentication & Authorization
- JWT token security
- Password hashing and validation
- Role-based access control
- API key management

### Input Validation
- SQL injection prevention
- XSS protection
- Path traversal prevention
- Input sanitization

### Security Headers
- HTTPS enforcement
- Content Security Policy
- XSS protection headers
- CSRF protection

### Rate Limiting
- API rate limiting
- Progressive penalties
- Client isolation

### Example Security Test

```python
def test_jwt_tampering_detection():
    """Test detection of tampered JWT tokens."""
    jwt_handler = JWTHandler(secret_key="test-key")
    
    payload = {"user_id": "user123", "role": "analyst"}
    token = jwt_handler.create_token(payload)
    
    # Tamper with token
    tampered_token = token[:-10] + "tampered123"
    
    # Should detect tampering
    with pytest.raises(AuthenticationError):
        jwt_handler.validate_token(tampered_token)
```

## ⚡ Performance Testing

### Load Testing Scenarios
- **Concurrent Analysis**: 20 simultaneous analyses
- **API Load**: 200 requests across 50 concurrent users
- **Data Ingestion**: 10,000 price observations per batch

### Performance Metrics
- **Response Time**: P95 < 2 seconds for API calls
- **Throughput**: 10+ requests per second
- **Memory Usage**: < 80% maximum usage
- **Analysis Time**: < 60 seconds for standard analysis

### Performance Test Example

```python
@pytest.mark.performance
async def test_concurrent_analysis_performance():
    """Test system performance under concurrent load."""
    metrics = PerformanceMetrics()
    
    # Run 20 concurrent analyses
    tasks = [run_analysis(f"test_{i}") for i in range(20)]
    results = await asyncio.gather(*tasks)
    
    # Assert performance requirements
    assert metrics.average_response_time < 30  # seconds
    assert metrics.success_rate >= 90  # percent
    assert metrics.max_memory_usage < 80  # percent
```

## 🐛 Debugging Tests

### Common Issues

1. **Import Errors**
   ```bash
   # Ensure Python path is set
   export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
   ```

2. **Database Connection Issues**
   ```bash
   # Check PostgreSQL is running
   pg_isready -h localhost -p 5432
   ```

3. **Async Test Issues**
   ```python
   # Use pytest-asyncio
   @pytest.mark.asyncio
   async def test_async_function():
       result = await async_function()
       assert result is not None
   ```

### Test Debugging Tools

```bash
# Run with debugging output
pytest --pdb --pdbcls=IPython.terminal.debugger:Pdb

# Run single test with verbose output
pytest tests/unit/test_specific.py::test_function -v -s

# Show local variables on failure
pytest --tb=long --showlocals
```

## 📈 Metrics and Reporting

### Test Metrics Tracked
- **Test Count**: Total number of tests
- **Coverage Percentage**: Code coverage metrics
- **Execution Time**: Test suite duration
- **Success Rate**: Pass/fail ratios
- **Performance Metrics**: Response times, resource usage

### Reporting Formats
- **JUnit XML**: For CI/CD integration
- **HTML Coverage**: Interactive coverage reports
- **JSON Results**: Machine-readable test results
- **Terminal Output**: Human-readable summaries

### Example Report Generation

```bash
# Generate comprehensive test report
python scripts/run_tests.py all --output=test_results.json

# View HTML coverage report
open htmlcov/index.html

# View JUnit results
cat junit-*.xml
```

## 🤝 Contributing to Tests

### Writing New Tests

1. **Follow naming conventions**:
   ```python
   def test_function_should_behavior_when_condition():
       """Test that function behaves correctly when condition is met."""
       pass
   ```

2. **Use appropriate fixtures**:
   ```python
   def test_market_creation(sample_market, mock_uow):
       """Test market creation with mocked dependencies."""
       pass
   ```

3. **Include docstrings**:
   ```python
   def test_complex_scenario():
       """
       Test complex scenario with multiple conditions.
       
       This test verifies that the system correctly handles
       edge cases when multiple markets have conflicting data.
       """
       pass
   ```

### Test Review Checklist

- [ ] Test has clear, descriptive name
- [ ] Test has docstring explaining purpose
- [ ] Test uses appropriate fixtures
- [ ] Test assertions are specific and meaningful
- [ ] Test is categorized with correct markers
- [ ] Test runs independently (no side effects)
- [ ] Test execution time is reasonable
- [ ] Test covers both positive and negative cases

### Adding Performance Tests

```python
@pytest.mark.performance
def test_new_performance_scenario():
    """Test performance of new feature."""
    metrics = PerformanceMetrics()
    metrics.start_monitoring()
    
    # Execute performance scenario
    result = execute_scenario()
    
    metrics.stop_monitoring()
    summary = metrics.get_summary()
    
    # Assert performance requirements
    assert summary["response_times"]["mean"] < 5.0
    assert summary["success_rate"] >= 95
```

## 📚 Additional Resources

- [Pytest Documentation](https://docs.pytest.org/)
- [Coverage.py Documentation](https://coverage.readthedocs.io/)
- [Async Testing Guide](https://pytest-asyncio.readthedocs.io/)
- [Performance Testing Best Practices](https://martinfowler.com/articles/practical-test-pyramid.html)
- [Security Testing Guidelines](https://owasp.org/www-project-web-security-testing-guide/)

## 🆘 Support

For test-related issues:

1. Check this README for common solutions
2. Review test logs in `tests.log`
3. Run tests with `--verbose` flag for detailed output
4. Check CI/CD pipeline logs for environment-specific issues
5. Create an issue with test failure details and environment info