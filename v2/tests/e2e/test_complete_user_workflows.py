"""End-to-end tests for complete user workflows."""

import asyncio
import json
import pytest
from datetime import datetime, timedelta
from uuid import uuid4
from typing import Dict, Any
from unittest.mock import patch, AsyncMock

import httpx
from fastapi.testclient import TestClient

from src.interfaces.api.rest.app import create_app
from src.shared.container import Container


@pytest.fixture
async def test_app():
    """Create test application."""
    container = Container()
    
    # Configure for E2E testing
    container.config.database.url.from_value("postgresql://test:test@localhost:5432/test_db")
    container.config.cache.type.from_value("memory")
    container.config.events.type.from_value("inmemory")
    container.config.auth.enabled.from_value(True)
    container.config.auth.jwt_secret.from_value("test-secret-key")
    
    app = create_app(container)
    return app


@pytest.fixture
async def test_client(test_app):
    """Create test client."""
    return TestClient(test_app)


@pytest.fixture
async def authenticated_headers(test_client):
    """Get authenticated headers for API requests."""
    # Create test user and get JWT token
    user_data = {
        "username": "test_analyst",
        "email": "<EMAIL>",
        "password": "secure_password123",
        "role": "analyst"
    }
    
    # Register user
    response = test_client.post("/api/v1/auth/register", json=user_data)
    assert response.status_code == 201
    
    # Login to get token
    login_data = {
        "username": user_data["username"],
        "password": user_data["password"]
    }
    response = test_client.post("/api/v1/auth/login", json=login_data)
    assert response.status_code == 200
    
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def mock_data_services():
    """Mock external data services for E2E tests."""
    with patch('src.infrastructure.external_services.wfp_client.WFPClient') as mock_wfp, \
         patch('src.infrastructure.external_services.hdx_client.HDXClient') as mock_hdx, \
         patch('src.infrastructure.external_services.acled_client.ACLEDClient') as mock_acled:
        
        # Setup WFP mock data
        mock_wfp.return_value.get_price_data.return_value = [
            {
                "market": "Sanaa Central Market",
                "commodity": "Wheat",
                "price": 400.0,
                "currency": "YER",
                "date": "2023-01-01",
                "unit": "kg"
            }
        ]
        
        # Setup HDX mock data
        mock_hdx.return_value.get_market_locations.return_value = [
            {
                "market_id": "SANAA_CENTRAL",
                "name": "Sanaa Central Market",
                "latitude": 15.3694,
                "longitude": 44.1910,
                "governorate": "Sana'a"
            }
        ]
        
        # Setup ACLED mock data
        mock_acled.return_value.get_conflict_events.return_value = [
            {
                "event_id": "YEM12345",
                "event_date": "2023-01-01",
                "latitude": 15.3694,
                "longitude": 44.1910,
                "event_type": "Violence against civilians",
                "fatalities": 2
            }
        ]
        
        yield {
            'wfp': mock_wfp.return_value,
            'hdx': mock_hdx.return_value,
            'acled': mock_acled.return_value
        }


class TestCompleteUserWorkflows:
    """End-to-end tests for complete user workflows."""
    
    def test_user_registration_and_authentication_workflow(self, test_client):
        """Test complete user registration and authentication workflow."""
        # Step 1: Register new user
        user_data = {
            "username": "new_researcher",
            "email": "<EMAIL>",
            "password": "secure_password123",
            "role": "researcher"
        }
        
        response = test_client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        user_id = response.json()["user_id"]
        
        # Step 2: Login with credentials
        login_data = {
            "username": user_data["username"],
            "password": user_data["password"]
        }
        response = test_client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 200
        
        token_data = response.json()
        assert "access_token" in token_data
        assert "token_type" in token_data
        assert token_data["token_type"] == "bearer"
        
        # Step 3: Access protected endpoint with token
        headers = {"Authorization": f"Bearer {token_data['access_token']}"}
        response = test_client.get("/api/v1/auth/profile", headers=headers)
        assert response.status_code == 200
        
        profile = response.json()
        assert profile["username"] == user_data["username"]
        assert profile["email"] == user_data["email"]
        assert profile["role"] == user_data["role"]
        
        # Step 4: Logout
        response = test_client.post("/api/v1/auth/logout", headers=headers)
        assert response.status_code == 200
        
        # Step 5: Verify token is invalidated
        response = test_client.get("/api/v1/auth/profile", headers=headers)
        assert response.status_code == 401
    
    def test_data_ingestion_workflow(self, test_client, authenticated_headers, mock_data_services):
        """Test complete data ingestion workflow."""
        # Step 1: Start data ingestion
        ingestion_request = {
            "sources": ["wfp", "hdx", "acled"],
            "date_range": {
                "start_date": "2023-01-01",
                "end_date": "2023-12-31"
            },
            "regions": ["Sana'a", "Aden", "Hodeidah"]
        }
        
        response = test_client.post(
            "/api/v1/data/ingest",
            json=ingestion_request,
            headers=authenticated_headers
        )
        assert response.status_code == 202
        
        ingestion_id = response.json()["ingestion_id"]
        
        # Step 2: Monitor ingestion progress
        max_attempts = 10
        for attempt in range(max_attempts):
            response = test_client.get(
                f"/api/v1/data/ingest/{ingestion_id}/status",
                headers=authenticated_headers
            )
            assert response.status_code == 200
            
            status = response.json()
            if status["status"] == "completed":
                break
            elif status["status"] == "failed":
                pytest.fail(f"Ingestion failed: {status.get('error')}")
            
            # Wait before next check
            import time
            time.sleep(1)
        
        assert status["status"] == "completed"
        assert "summary" in status
        assert status["summary"]["total_records"] > 0
        
        # Step 3: Verify data is available
        response = test_client.get("/api/v1/markets", headers=authenticated_headers)
        assert response.status_code == 200
        markets = response.json()["items"]
        assert len(markets) > 0
        
        response = test_client.get("/api/v1/prices", headers=authenticated_headers)
        assert response.status_code == 200
        prices = response.json()["items"]
        assert len(prices) > 0
    
    def test_market_analysis_workflow(self, test_client, authenticated_headers, mock_data_services):
        """Test complete market analysis workflow."""
        # Step 1: Get available markets and commodities
        response = test_client.get("/api/v1/markets", headers=authenticated_headers)
        assert response.status_code == 200
        markets = response.json()["items"]
        
        response = test_client.get("/api/v1/commodities", headers=authenticated_headers)
        assert response.status_code == 200
        commodities = response.json()["items"]
        
        # Step 2: Submit analysis request
        analysis_request = {
            "name": "Market Integration Analysis",
            "description": "Testing market integration between major cities",
            "markets": [m["market_id"] for m in markets[:3]],
            "commodities": [c["code"] for c in commodities[:2]],
            "date_range": {
                "start_date": "2020-01-01",
                "end_date": "2023-12-31"
            },
            "analysis_type": "three_tier",
            "parameters": {
                "confidence_level": 0.95,
                "lag_periods": 12,
                "include_seasonality": True
            }
        }
        
        response = test_client.post(
            "/api/v1/analysis",
            json=analysis_request,
            headers=authenticated_headers
        )
        assert response.status_code == 202
        
        analysis_id = response.json()["analysis_id"]
        
        # Step 3: Monitor analysis progress via SSE
        # Note: In real E2E test, this would use actual SSE client
        # For now, we'll poll the status endpoint
        max_attempts = 30
        for attempt in range(max_attempts):
            response = test_client.get(
                f"/api/v1/analysis/{analysis_id}/status",
                headers=authenticated_headers
            )
            assert response.status_code == 200
            
            status = response.json()
            if status["status"] == "completed":
                break
            elif status["status"] == "failed":
                pytest.fail(f"Analysis failed: {status.get('error')}")
            
            import time
            time.sleep(2)
        
        assert status["status"] == "completed"
        
        # Step 4: Retrieve analysis results
        response = test_client.get(
            f"/api/v1/analysis/{analysis_id}/results",
            headers=authenticated_headers
        )
        assert response.status_code == 200
        
        results = response.json()
        assert "tier1_results" in results
        assert "tier2_results" in results
        assert "tier3_results" in results
        assert "metadata" in results
        
        # Step 5: Download results in different formats
        for format_type in ["json", "csv", "excel"]:
            response = test_client.get(
                f"/api/v1/analysis/{analysis_id}/export",
                params={"format": format_type},
                headers=authenticated_headers
            )
            assert response.status_code == 200
            
            if format_type == "json":
                assert response.headers["content-type"] == "application/json"
            elif format_type == "csv":
                assert "text/csv" in response.headers["content-type"]
            elif format_type == "excel":
                assert "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" in response.headers["content-type"]
    
    def test_policy_simulation_workflow(self, test_client, authenticated_headers, mock_data_services):
        """Test complete policy simulation workflow."""
        # Step 1: Create policy scenario
        scenario_request = {
            "name": "Fuel Subsidy Removal Impact",
            "description": "Simulating the impact of removing fuel subsidies",
            "policy_type": "subsidy_removal",
            "target_commodities": ["Fuel (Petrol-Gasoline)", "Fuel (Diesel)"],
            "affected_regions": ["Sana'a", "Aden"],
            "implementation_date": "2024-06-01",
            "parameters": {
                "subsidy_reduction_percent": 50,
                "phase_in_months": 6,
                "compensation_programs": ["cash_transfer"]
            }
        }
        
        response = test_client.post(
            "/api/v1/policy/scenarios",
            json=scenario_request,
            headers=authenticated_headers
        )
        assert response.status_code == 201
        
        scenario_id = response.json()["scenario_id"]
        
        # Step 2: Run policy simulation
        simulation_request = {
            "scenario_id": scenario_id,
            "simulation_period_months": 12,
            "monte_carlo_runs": 100,
            "confidence_intervals": [0.90, 0.95, 0.99]
        }
        
        response = test_client.post(
            "/api/v1/policy/simulate",
            json=simulation_request,
            headers=authenticated_headers
        )
        assert response.status_code == 202
        
        simulation_id = response.json()["simulation_id"]
        
        # Step 3: Monitor simulation progress
        max_attempts = 20
        for attempt in range(max_attempts):
            response = test_client.get(
                f"/api/v1/policy/simulate/{simulation_id}/status",
                headers=authenticated_headers
            )
            assert response.status_code == 200
            
            status = response.json()
            if status["status"] == "completed":
                break
            elif status["status"] == "failed":
                pytest.fail(f"Simulation failed: {status.get('error')}")
            
            import time
            time.sleep(1)
        
        assert status["status"] == "completed"
        
        # Step 4: Retrieve simulation results
        response = test_client.get(
            f"/api/v1/policy/simulate/{simulation_id}/results",
            headers=authenticated_headers
        )
        assert response.status_code == 200
        
        results = response.json()
        assert "baseline_scenario" in results
        assert "policy_scenario" in results
        assert "impact_analysis" in results
        assert "confidence_intervals" in results
        
        # Step 5: Generate policy brief
        brief_request = {
            "simulation_id": simulation_id,
            "target_audience": "policy_makers",
            "include_visualizations": True,
            "executive_summary": True
        }
        
        response = test_client.post(
            "/api/v1/policy/brief",
            json=brief_request,
            headers=authenticated_headers
        )
        assert response.status_code == 201
        
        brief = response.json()
        assert "brief_id" in brief
        assert "download_url" in brief
    
    def test_collaborative_analysis_workflow(self, test_client):
        """Test collaborative analysis workflow with multiple users."""
        # Step 1: Create multiple users with different roles
        users = [
            {
                "username": "lead_analyst",
                "email": "<EMAIL>",
                "password": "password123",
                "role": "analyst"
            },
            {
                "username": "junior_researcher",
                "email": "<EMAIL>",
                "password": "password123",
                "role": "researcher"
            },
            {
                "username": "policy_advisor",
                "email": "<EMAIL>",
                "password": "password123",
                "role": "policy_maker"
            }
        ]
        
        user_tokens = {}
        for user in users:
            # Register user
            response = test_client.post("/api/v1/auth/register", json=user)
            assert response.status_code == 201
            
            # Login and get token
            login_data = {"username": user["username"], "password": user["password"]}
            response = test_client.post("/api/v1/auth/login", json=login_data)
            assert response.status_code == 200
            
            token = response.json()["access_token"]
            user_tokens[user["username"]] = {"Authorization": f"Bearer {token}"}
        
        # Step 2: Lead analyst creates project and adds collaborators
        project_request = {
            "name": "Yemen Market Dynamics Study",
            "description": "Comprehensive analysis of market dynamics in conflict zones",
            "collaborators": [
                {"username": "junior_researcher", "role": "contributor"},
                {"username": "policy_advisor", "role": "reviewer"}
            ]
        }
        
        response = test_client.post(
            "/api/v1/projects",
            json=project_request,
            headers=user_tokens["lead_analyst"]
        )
        assert response.status_code == 201
        
        project_id = response.json()["project_id"]
        
        # Step 3: Junior researcher adds analysis to project
        analysis_request = {
            "project_id": project_id,
            "name": "Wheat Price Integration",
            "markets": ["SANAA_CENTRAL", "ADEN_MAIN"],
            "commodities": ["WHEAT"],
            "date_range": {
                "start_date": "2020-01-01",
                "end_date": "2023-12-31"
            }
        }
        
        response = test_client.post(
            "/api/v1/analysis",
            json=analysis_request,
            headers=user_tokens["junior_researcher"]
        )
        assert response.status_code == 202
        
        analysis_id = response.json()["analysis_id"]
        
        # Step 4: Policy advisor reviews and comments
        comment_request = {
            "analysis_id": analysis_id,
            "comment": "Consider adding seasonal adjustments for better policy relevance",
            "comment_type": "suggestion"
        }
        
        response = test_client.post(
            "/api/v1/analysis/comments",
            json=comment_request,
            headers=user_tokens["policy_advisor"]
        )
        assert response.status_code == 201
        
        # Step 5: Lead analyst approves and publishes results
        approval_request = {
            "analysis_id": analysis_id,
            "status": "approved",
            "publish": True,
            "approval_notes": "Ready for publication after addressing reviewer comments"
        }
        
        response = test_client.post(
            "/api/v1/analysis/approve",
            json=approval_request,
            headers=user_tokens["lead_analyst"]
        )
        assert response.status_code == 200
        
        # Step 6: Verify all collaborators can access final results
        for username, headers in user_tokens.items():
            response = test_client.get(
                f"/api/v1/projects/{project_id}",
                headers=headers
            )
            assert response.status_code == 200
            
            project = response.json()
            assert len(project["analyses"]) > 0
    
    def test_error_handling_and_recovery_workflow(self, test_client, authenticated_headers):
        """Test error handling and recovery in various workflows."""
        # Test 1: Invalid analysis request
        invalid_request = {
            "name": "Invalid Analysis",
            "markets": [],  # Empty markets should fail
            "commodities": ["WHEAT"],
            "date_range": {
                "start_date": "2023-01-01",
                "end_date": "2020-01-01"  # Invalid date range
            }
        }
        
        response = test_client.post(
            "/api/v1/analysis",
            json=invalid_request,
            headers=authenticated_headers
        )
        assert response.status_code == 422
        
        error = response.json()
        assert "validation_errors" in error
        
        # Test 2: Accessing non-existent resource
        fake_analysis_id = str(uuid4())
        response = test_client.get(
            f"/api/v1/analysis/{fake_analysis_id}",
            headers=authenticated_headers
        )
        assert response.status_code == 404
        
        # Test 3: Insufficient permissions
        # Create a user with limited permissions
        limited_user = {
            "username": "limited_user",
            "email": "<EMAIL>",
            "password": "password123",
            "role": "viewer"
        }
        
        response = test_client.post("/api/v1/auth/register", json=limited_user)
        assert response.status_code == 201
        
        login_data = {"username": "limited_user", "password": "password123"}
        response = test_client.post("/api/v1/auth/login", json=login_data)
        limited_headers = {"Authorization": f"Bearer {response.json()['access_token']}"}
        
        # Try to create analysis with limited permissions
        analysis_request = {
            "name": "Unauthorized Analysis",
            "markets": ["SANAA_CENTRAL"],
            "commodities": ["WHEAT"],
            "date_range": {
                "start_date": "2020-01-01",
                "end_date": "2023-12-31"
            }
        }
        
        response = test_client.post(
            "/api/v1/analysis",
            json=analysis_request,
            headers=limited_headers
        )
        assert response.status_code == 403
        
        # Test 4: Rate limiting
        # Make many rapid requests to trigger rate limiting
        for _ in range(50):
            response = test_client.get("/api/v1/markets", headers=authenticated_headers)
            if response.status_code == 429:
                assert "rate_limit_exceeded" in response.json()
                break
    
    def test_performance_under_load(self, test_client, authenticated_headers):
        """Test system performance under concurrent load."""
        import concurrent.futures
        import time
        
        def make_request():
            response = test_client.get("/api/v1/markets", headers=authenticated_headers)
            return response.status_code, response.elapsed.total_seconds() if hasattr(response, 'elapsed') else 0
        
        # Test concurrent requests
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(50)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Verify all requests succeeded
        status_codes = [result[0] for result in results]
        assert all(code == 200 for code in status_codes)
        
        # Verify reasonable performance
        assert total_time < 30  # All 50 requests should complete within 30 seconds
        
        # Verify individual request performance
        response_times = [result[1] for result in results if result[1] > 0]
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            assert avg_response_time < 2.0  # Average response time should be under 2 seconds