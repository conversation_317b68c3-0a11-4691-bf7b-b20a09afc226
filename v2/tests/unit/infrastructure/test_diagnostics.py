"""
Unit tests for V2 diagnostic test implementations.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from v2.src.infrastructure.diagnostics.tests.ramsey_reset import ramsey_reset_test
from v2.src.infrastructure.diagnostics.tests.chow_structural_break import chow_structural_break_test
from v2.src.infrastructure.diagnostics.tests.quandt_likelihood_ratio import quandt_likelihood_ratio_test
from v2.src.infrastructure.diagnostics.panel_diagnostics import PanelDiagnosticTests
from v2.src.core.models.interfaces import DiagnosticResult


class TestRamseyRESET:
    """Test suite for Ramsey RESET test implementation."""
    
    def setup_method(self):
        """Set up test data."""
        np.random.seed(42)
        n = 100
        
        # Generate data with correct functional form
        self.X_correct = pd.DataFrame({
            'x1': np.random.randn(n),
            'x2': np.random.randn(n),
            'const': 1
        })
        self.y_correct = 2 + 3 * self.X_correct['x1'] + 4 * self.X_correct['x2'] + np.random.randn(n)
        self.fitted_correct = 2 + 3 * self.X_correct['x1'] + 4 * self.X_correct['x2']
        
        # Generate data with misspecified functional form
        self.X_misspec = pd.DataFrame({
            'x1': np.random.randn(n),
            'x2': np.random.randn(n),
            'const': 1
        })
        # True model has quadratic term, but we fit linear
        self.y_misspec = (2 + 3 * self.X_misspec['x1'] + 4 * self.X_misspec['x2'] + 
                          5 * self.X_misspec['x1']**2 + np.random.randn(n))
        self.fitted_misspec = 2 + 3 * self.X_misspec['x1'] + 4 * self.X_misspec['x2']
    
    def test_reset_correct_specification(self):
        """Test RESET on correctly specified model."""
        f_stat, p_value, recommendation = ramsey_reset_test(
            self.y_correct, 
            self.X_correct, 
            pd.Series(self.fitted_correct)
        )
        
        assert isinstance(f_stat, float)
        assert isinstance(p_value, float)
        assert 0 <= p_value <= 1
        assert isinstance(recommendation, str)
        # Should not reject null for correct specification
        assert p_value > 0.10, "Should not reject null for correct specification"
        assert "No significant evidence" in recommendation
    
    def test_reset_misspecified_model(self):
        """Test RESET on misspecified model."""
        f_stat, p_value, recommendation = ramsey_reset_test(
            self.y_misspec,
            self.X_misspec,
            pd.Series(self.fitted_misspec),
            powers=[2, 3]
        )
        
        assert isinstance(f_stat, float)
        assert isinstance(p_value, float)
        # Should detect misspecification
        assert p_value < 0.10, "Should detect functional form misspecification"
        assert "misspecification" in recommendation.lower()
    
    def test_reset_dimension_mismatch(self):
        """Test RESET with mismatched dimensions."""
        f_stat, p_value, recommendation = ramsey_reset_test(
            self.y_correct[:50],  # Different length
            self.X_correct,
            pd.Series(self.fitted_correct)
        )
        
        assert np.isnan(f_stat)
        assert np.isnan(p_value)
        assert "Dimension mismatch" in recommendation
    
    def test_reset_custom_powers(self):
        """Test RESET with custom powers."""
        f_stat, p_value, recommendation = ramsey_reset_test(
            self.y_correct,
            self.X_correct,
            pd.Series(self.fitted_correct),
            powers=[2, 3, 4]
        )
        
        assert isinstance(f_stat, float)
        assert isinstance(p_value, float)
        assert 0 <= p_value <= 1


class TestChowStructuralBreak:
    """Test suite for Chow structural break test."""
    
    def setup_method(self):
        """Set up panel data with and without structural break."""
        np.random.seed(42)
        
        # Generate panel data
        n_entities = 10
        n_periods = 100
        dates = pd.date_range('2020-01-01', periods=n_periods, freq='D')
        
        data_list = []
        break_date = dates[50]
        
        for entity in range(n_entities):
            for t, date in enumerate(dates):
                # Create structural break in middle
                if date < break_date:
                    price = 10 + 2 * np.random.randn() + 0.5 * t
                    conflict = 0.1 + 0.05 * np.random.randn()
                else:
                    price = 20 + 3 * np.random.randn() + 0.8 * t  # Different intercept and trend
                    conflict = 0.3 + 0.1 * np.random.randn()  # Higher conflict
                
                exchange_rate = 100 + 0.1 * t + np.random.randn()
                
                data_list.append({
                    'entity': f'market_{entity}',
                    'date': date,
                    'price': price,
                    'conflict': conflict,
                    'exchange_rate': exchange_rate
                })
        
        self.panel_data = pd.DataFrame(data_list)
        self.break_date = break_date
        self.formula = 'price ~ conflict + exchange_rate'
        
        # Create data without break
        data_no_break = []
        for entity in range(n_entities):
            for t, date in enumerate(dates):
                price = 10 + 2 * np.random.randn() + 0.5 * t
                conflict = 0.1 + 0.05 * np.random.randn()
                exchange_rate = 100 + 0.1 * t + np.random.randn()
                
                data_no_break.append({
                    'entity': f'market_{entity}',
                    'date': date,
                    'price': price,
                    'conflict': conflict,
                    'exchange_rate': exchange_rate
                })
        
        self.panel_data_no_break = pd.DataFrame(data_no_break)
    
    def test_chow_with_structural_break(self):
        """Test Chow test detects structural break."""
        f_stat, p_value, recommendation = chow_structural_break_test(
            self.panel_data,
            self.formula,
            str(self.break_date),
            entity_col='entity',
            time_col='date'
        )
        
        assert isinstance(f_stat, float)
        assert isinstance(p_value, float)
        assert 0 <= p_value <= 1
        # Should detect the structural break
        assert p_value < 0.05, "Should detect structural break"
        assert "structural break" in recommendation.lower()
    
    def test_chow_without_structural_break(self):
        """Test Chow test on stable parameters."""
        f_stat, p_value, recommendation = chow_structural_break_test(
            self.panel_data_no_break,
            self.formula,
            str(self.break_date),
            entity_col='entity',
            time_col='date'
        )
        
        assert isinstance(f_stat, float)
        assert isinstance(p_value, float)
        # Should not detect break when there isn't one
        assert p_value > 0.10, "Should not detect break in stable data"
        assert "No significant evidence" in recommendation
    
    def test_chow_insufficient_data(self):
        """Test Chow test with insufficient data."""
        # Use only first 10 observations
        small_data = self.panel_data.iloc[:10]
        
        f_stat, p_value, recommendation = chow_structural_break_test(
            small_data,
            self.formula,
            str(self.break_date),
            entity_col='entity',
            time_col='date'
        )
        
        assert np.isnan(f_stat)
        assert np.isnan(p_value)
        assert "Insufficient data" in recommendation


class TestQuandtLikelihoodRatio:
    """Test suite for Quandt Likelihood Ratio test."""
    
    def setup_method(self):
        """Set up panel data."""
        np.random.seed(42)
        
        # Generate panel data with unknown break
        n_entities = 10
        n_periods = 100
        dates = pd.date_range('2020-01-01', periods=n_periods, freq='D')
        
        data_list = []
        true_break_idx = 60  # Unknown break location
        
        for entity in range(n_entities):
            for t, date in enumerate(dates):
                if t < true_break_idx:
                    price = 10 + 2 * np.random.randn() + 0.3 * t
                    conflict = 0.1 + 0.05 * np.random.randn()
                else:
                    price = 25 + 4 * np.random.randn() + 0.6 * t
                    conflict = 0.4 + 0.1 * np.random.randn()
                
                exchange_rate = 100 + 0.1 * t + np.random.randn()
                
                data_list.append({
                    'entity': f'market_{entity}',
                    'date': date,
                    'price': price,
                    'conflict': conflict,
                    'exchange_rate': exchange_rate
                })
        
        self.panel_data = pd.DataFrame(data_list)
        self.formula = 'price ~ conflict + exchange_rate'
        self.true_break_date = dates[true_break_idx]
    
    def test_qlr_detects_unknown_break(self):
        """Test QLR detects break at unknown date."""
        max_f_stat, break_date, p_value, recommendation = quandt_likelihood_ratio_test(
            self.panel_data,
            self.formula,
            trim_pct=0.15,
            entity_col='entity',
            time_col='date'
        )
        
        assert isinstance(max_f_stat, float)
        assert isinstance(break_date, str)
        assert isinstance(p_value, float)
        assert isinstance(recommendation, str)
        
        # Should detect a break
        assert p_value <= 0.10, "Should detect structural break"
        assert "structural break" in recommendation.lower()
        
        # Check if detected break is close to true break
        detected_date = pd.to_datetime(break_date)
        date_diff = abs((detected_date - self.true_break_date).days)
        assert date_diff < 20, f"Detected break should be close to true break (diff: {date_diff} days)"
    
    def test_qlr_custom_trim(self):
        """Test QLR with custom trim percentage."""
        max_f_stat, break_date, p_value, recommendation = quandt_likelihood_ratio_test(
            self.panel_data,
            self.formula,
            trim_pct=0.20,  # More conservative trim
            entity_col='entity',
            time_col='date'
        )
        
        assert isinstance(max_f_stat, float)
        assert isinstance(break_date, str)
        assert isinstance(p_value, float)
    
    def test_qlr_insufficient_dates(self):
        """Test QLR with too few time periods."""
        small_data = self.panel_data[self.panel_data['date'] < '2020-01-10']
        
        max_f_stat, break_date, p_value, recommendation = quandt_likelihood_ratio_test(
            small_data,
            self.formula,
            trim_pct=0.15,
            entity_col='entity',
            time_col='date'
        )
        
        assert np.isnan(max_f_stat)
        assert p_value is np.nan
        assert "Insufficient dates" in recommendation


class TestPanelDiagnosticIntegration:
    """Test integration of diagnostic tests in PanelDiagnosticTests class."""
    
    def setup_method(self):
        """Set up test data."""
        np.random.seed(42)
        
        # Create panel structure
        n_entities = 20
        n_periods = 50
        
        # Generate residuals with some structure
        residuals_data = []
        fitted_data = []
        X_data = []
        
        for i in range(n_entities):
            for t in range(n_periods):
                # Add serial correlation
                if t == 0:
                    resid = np.random.randn()
                else:
                    resid = 0.5 * residuals_data[-1] + np.random.randn()
                
                residuals_data.append(resid)
                fitted_data.append(10 + 0.1 * t + np.random.randn())
                X_data.append([1, np.random.randn(), np.random.randn()])
        
        index = pd.MultiIndex.from_product(
            [range(n_entities), range(n_periods)], 
            names=['entity', 'time']
        )
        
        self.residuals = pd.Series(residuals_data, index=index)
        self.fitted_values = pd.Series(fitted_data, index=index)
        self.X = pd.DataFrame(X_data, columns=['const', 'x1', 'x2'], index=index)
        
        self.panel_info = {
            'entity_id': 'entity',
            'time_id': 'time',
            'N': n_entities,
            'T': n_periods,
            'nobs': n_entities * n_periods
        }
        
        self.diagnostics = PanelDiagnosticTests()
    
    def test_run_all_diagnostics(self):
        """Test running all diagnostic tests."""
        results = self.diagnostics.run_all_diagnostics(
            self.residuals,
            self.X,
            self.panel_info,
            self.fitted_values
        )
        
        # Check all expected tests are present
        expected_tests = [
            'heteroskedasticity',
            'serial_correlation',
            'cross_sectional_dependence',
            'breusch_pagan_lm',
            'functional_form',
            'panel_unit_root'
        ]
        
        for test_name in expected_tests:
            assert test_name in results
            result = results[test_name]
            assert isinstance(result, DiagnosticResult)
            assert hasattr(result, 'test_name')
            assert hasattr(result, 'test_statistic')
            assert hasattr(result, 'p_value')
            assert hasattr(result, 'reject_null')
            assert hasattr(result, 'interpretation')
    
    def test_structural_break_tests(self):
        """Test structural break diagnostic methods."""
        # Create panel data
        dates = pd.date_range('2020-01-01', periods=50, freq='D')
        data_list = []
        
        for entity in range(20):
            for t, date in enumerate(dates):
                data_list.append({
                    'market_id': f'market_{entity}',
                    'date': date,
                    'price': 10 + 0.1 * t + np.random.randn(),
                    'conflict': 0.1 + 0.05 * np.random.randn(),
                    'exchange_rate': 100 + 0.05 * t + np.random.randn()
                })
        
        panel_data = pd.DataFrame(data_list)
        
        # Test with known break date
        results = self.diagnostics.test_structural_breaks(
            panel_data,
            'price ~ conflict + exchange_rate',
            entity_col='market_id',
            time_col='date',
            known_break_date='2020-01-25',
            run_qlr=True
        )
        
        assert 'chow_test' in results
        assert 'quandt_likelihood_ratio_test' in results
        
        for test_name, result in results.items():
            assert isinstance(result, DiagnosticResult)
            assert hasattr(result, 'test_statistic')
            assert hasattr(result, 'p_value')