"""Tests for Role-Based Access Control."""

import pytest
from unittest.mock import Mock, patch

from fastapi import HTTPEx<PERSON>

from src.infrastructure.security.rbac import (
    Permission, Role, RBACManager, 
    require_permission, require_any_permission, require_all_permissions, require_role
)


class TestRBACManager:
    """Test RBAC manager functionality."""
    
    @pytest.fixture
    def rbac_manager(self):
        """Create RBAC manager instance."""
        return RBACManager()
    
    def test_get_role_permissions_admin(self, rbac_manager):
        """Test getting permissions for admin role."""
        permissions = rbac_manager.get_role_permissions(Role.ADMIN)
        
        # Admin should have all permissions
        assert Permission.ADMIN_ACCESS in permissions
        assert Permission.ANALYSIS_CREATE in permissions
        assert Permission.USER_DELETE in permissions
        assert len(permissions) > 20  # Should have many permissions
    
    def test_get_role_permissions_analyst(self, rbac_manager):
        """Test getting permissions for analyst role."""
        permissions = rbac_manager.get_role_permissions(Role.ANALYST)
        
        # Analyst should have read and analysis permissions
        assert Permission.ANALYSIS_READ in permissions
        assert Permission.ANALYSIS_CREATE in permissions
        assert Permission.MARKET_READ in permissions
        
        # But not admin permissions
        assert Permission.ADMIN_ACCESS not in permissions
        assert Permission.USER_DELETE not in permissions
    
    def test_get_role_permissions_viewer(self, rbac_manager):
        """Test getting permissions for viewer role."""
        permissions = rbac_manager.get_role_permissions(Role.VIEWER)
        
        # Viewer should only have read permissions
        assert Permission.ANALYSIS_READ in permissions
        assert Permission.MARKET_READ in permissions
        
        # But not write permissions
        assert Permission.ANALYSIS_CREATE not in permissions
        assert Permission.MARKET_UPDATE not in permissions
    
    def test_get_user_permissions_single_role(self, rbac_manager):
        """Test getting permissions for user with single role."""
        permissions = rbac_manager.get_user_permissions([Role.ANALYST])
        
        assert Permission.ANALYSIS_READ in permissions
        assert Permission.ANALYSIS_CREATE in permissions
    
    def test_get_user_permissions_multiple_roles(self, rbac_manager):
        """Test getting permissions for user with multiple roles."""
        permissions = rbac_manager.get_user_permissions([Role.VIEWER, Role.API_USER])
        
        # Should have union of both roles' permissions
        assert Permission.ANALYSIS_READ in permissions  # From VIEWER
        assert Permission.API_KEY_READ in permissions   # From API_USER
    
    def test_has_permission_true(self, rbac_manager):
        """Test checking permission when user has it."""
        has_perm = rbac_manager.has_permission(
            [Role.ANALYST], 
            Permission.ANALYSIS_CREATE
        )
        assert has_perm is True
    
    def test_has_permission_false(self, rbac_manager):
        """Test checking permission when user doesn't have it."""
        has_perm = rbac_manager.has_permission(
            [Role.VIEWER], 
            Permission.ANALYSIS_DELETE
        )
        assert has_perm is False
    
    def test_has_any_permission_true(self, rbac_manager):
        """Test checking any permission when user has at least one."""
        has_perm = rbac_manager.has_any_permission(
            [Role.VIEWER],
            [Permission.ANALYSIS_READ, Permission.ANALYSIS_CREATE]
        )
        assert has_perm is True  # Has ANALYSIS_READ
    
    def test_has_any_permission_false(self, rbac_manager):
        """Test checking any permission when user has none."""
        has_perm = rbac_manager.has_any_permission(
            [Role.VIEWER],
            [Permission.USER_CREATE, Permission.USER_DELETE]
        )
        assert has_perm is False
    
    def test_has_all_permissions_true(self, rbac_manager):
        """Test checking all permissions when user has all."""
        has_perm = rbac_manager.has_all_permissions(
            [Role.ANALYST],
            [Permission.ANALYSIS_READ, Permission.ANALYSIS_CREATE]
        )
        assert has_perm is True
    
    def test_has_all_permissions_false(self, rbac_manager):
        """Test checking all permissions when user lacks some."""
        has_perm = rbac_manager.has_all_permissions(
            [Role.ANALYST],
            [Permission.ANALYSIS_READ, Permission.USER_DELETE]
        )
        assert has_perm is False  # Doesn't have USER_DELETE
    
    def test_add_permission_to_role(self, rbac_manager):
        """Test adding permission to a role."""
        # Create custom role
        custom_role = Role.API_USER
        
        # Add permission
        rbac_manager.add_permission_to_role(custom_role, Permission.CONFLICT_CREATE)
        
        # Verify it was added
        permissions = rbac_manager.get_role_permissions(custom_role)
        assert Permission.CONFLICT_CREATE in permissions
    
    def test_remove_permission_from_role(self, rbac_manager):
        """Test removing permission from a role."""
        # Remove a permission from API_USER
        rbac_manager.remove_permission_from_role(Role.API_USER, Permission.API_KEY_READ)
        
        # Verify it was removed
        permissions = rbac_manager.get_role_permissions(Role.API_USER)
        assert Permission.API_KEY_READ not in permissions


class TestPermissionDecorators:
    """Test permission decorator functions."""
    
    @pytest.mark.asyncio
    async def test_require_permission_granted(self):
        """Test require_permission when user has permission."""
        @require_permission(Permission.ANALYSIS_READ)
        async def protected_func(current_user):
            return "success"
        
        # Mock user with analyst role
        current_user = {
            "roles": ["analyst"],
            "sub": "123"
        }
        
        result = await protected_func(current_user=current_user)
        assert result == "success"
    
    @pytest.mark.asyncio
    async def test_require_permission_denied(self):
        """Test require_permission when user lacks permission."""
        @require_permission(Permission.USER_DELETE)
        async def protected_func(current_user):
            return "success"
        
        # Mock user with viewer role (no delete permission)
        current_user = {
            "roles": ["viewer"],
            "sub": "123"
        }
        
        with pytest.raises(HTTPException) as exc_info:
            await protected_func(current_user=current_user)
        
        assert exc_info.value.status_code == 403
        assert "Insufficient permissions" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_require_permission_no_user(self):
        """Test require_permission when no user provided."""
        @require_permission(Permission.ANALYSIS_READ)
        async def protected_func(current_user=None):
            return "success"
        
        with pytest.raises(HTTPException) as exc_info:
            await protected_func()
        
        assert exc_info.value.status_code == 401
        assert "Authentication required" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_require_any_permission_granted(self):
        """Test require_any_permission when user has one permission."""
        @require_any_permission([Permission.ANALYSIS_CREATE, Permission.USER_CREATE])
        async def protected_func(current_user):
            return "success"
        
        # User with analyst role has ANALYSIS_CREATE
        current_user = {
            "roles": ["analyst"],
            "sub": "123"
        }
        
        result = await protected_func(current_user=current_user)
        assert result == "success"
    
    @pytest.mark.asyncio
    async def test_require_all_permissions_granted(self):
        """Test require_all_permissions when user has all permissions."""
        @require_all_permissions([Permission.ANALYSIS_READ, Permission.MARKET_READ])
        async def protected_func(current_user):
            return "success"
        
        # User with analyst role has both read permissions
        current_user = {
            "roles": ["analyst"],
            "sub": "123"
        }
        
        result = await protected_func(current_user=current_user)
        assert result == "success"
    
    @pytest.mark.asyncio
    async def test_require_role_granted(self):
        """Test require_role when user has the role."""
        @require_role(Role.ADMIN)
        async def protected_func(current_user):
            return "success"
        
        current_user = {
            "roles": ["admin"],
            "sub": "123"
        }
        
        result = await protected_func(current_user=current_user)
        assert result == "success"
    
    @pytest.mark.asyncio
    async def test_require_role_denied(self):
        """Test require_role when user lacks the role."""
        @require_role(Role.ADMIN)
        async def protected_func(current_user):
            return "success"
        
        current_user = {
            "roles": ["viewer"],
            "sub": "123"
        }
        
        with pytest.raises(HTTPException) as exc_info:
            await protected_func(current_user=current_user)
        
        assert exc_info.value.status_code == 403
        assert "Insufficient role" in exc_info.value.detail