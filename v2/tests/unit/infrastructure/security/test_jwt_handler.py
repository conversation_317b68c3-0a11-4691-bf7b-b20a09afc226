"""Tests for JWT token handling."""

import pytest
from datetime import datetime, timedelta, timezone
from unittest.mock import Mock, patch

from src.infrastructure.security.jwt_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>, JWTConfig, TokenData, create_access_token, verify_token
)


class TestJWTHandler:
    """Test JWT token handling."""
    
    @pytest.fixture
    def jwt_config(self):
        """Create test JWT configuration."""
        return JWTConfig(
            secret_key="test-secret-key-for-testing-only",
            algorithm="HS256",
            access_token_expire_minutes=30,
            refresh_token_expire_days=7,
            issuer="test-issuer",
            audience="test-audience"
        )
    
    @pytest.fixture
    def jwt_handler(self, jwt_config):
        """Create JWT handler instance."""
        return JWTHandler(config=jwt_config)
    
    @pytest.fixture
    def test_user_data(self):
        """Create test user data."""
        return {
            "sub": "123e4567-e89b-12d3-a456-426614174000",
            "username": "testuser",
            "email": "<EMAIL>",
            "roles": ["analyst", "viewer"],
            "permissions": ["analysis:read", "market:read"]
        }
    
    def test_create_access_token(self, jwt_handler, test_user_data):
        """Test creating an access token."""
        token = jwt_handler.create_access_token(test_user_data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Verify token can be decoded
        decoded = jwt_handler.verify_token(token, token_type="access")
        assert decoded is not None
        assert decoded.sub == test_user_data["sub"]
        assert decoded.username == test_user_data["username"]
        assert decoded.email == test_user_data["email"]
        assert decoded.roles == test_user_data["roles"]
        assert decoded.type == "access"
    
    def test_create_access_token_with_custom_expiration(self, jwt_handler, test_user_data):
        """Test creating an access token with custom expiration."""
        expires_delta = timedelta(minutes=60)
        token = jwt_handler.create_access_token(test_user_data, expires_delta)
        
        decoded = jwt_handler.verify_token(token, token_type="access")
        assert decoded is not None
        
        # Check expiration is approximately 60 minutes from now
        expected_exp = datetime.now(timezone.utc) + expires_delta
        assert abs((decoded.exp - expected_exp).total_seconds()) < 5  # Allow 5 second tolerance
    
    def test_create_refresh_token(self, jwt_handler, test_user_data):
        """Test creating a refresh token."""
        token = jwt_handler.create_refresh_token(test_user_data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Verify token can be decoded
        decoded = jwt_handler.verify_token(token, token_type="refresh")
        assert decoded is not None
        assert decoded.sub == test_user_data["sub"]
        assert decoded.type == "refresh"
    
    def test_verify_valid_token(self, jwt_handler, test_user_data):
        """Test verifying a valid token."""
        token = jwt_handler.create_access_token(test_user_data)
        decoded = jwt_handler.verify_token(token, token_type="access")
        
        assert decoded is not None
        assert isinstance(decoded, TokenData)
        assert decoded.sub == test_user_data["sub"]
        assert decoded.username == test_user_data["username"]
        assert decoded.email == test_user_data["email"]
        assert decoded.roles == test_user_data["roles"]
        assert decoded.permissions == test_user_data["permissions"]
    
    def test_verify_expired_token(self, jwt_handler, test_user_data):
        """Test verifying an expired token."""
        # Create token that expires immediately
        token = jwt_handler.create_access_token(
            test_user_data, 
            expires_delta=timedelta(seconds=-1)
        )
        
        decoded = jwt_handler.verify_token(token, token_type="access")
        assert decoded is None
    
    def test_verify_token_wrong_type(self, jwt_handler, test_user_data):
        """Test verifying token with wrong type."""
        # Create access token but verify as refresh
        token = jwt_handler.create_access_token(test_user_data)
        decoded = jwt_handler.verify_token(token, token_type="refresh")
        
        assert decoded is None
    
    def test_verify_invalid_token(self, jwt_handler):
        """Test verifying an invalid token."""
        decoded = jwt_handler.verify_token("invalid.token.here", token_type="access")
        assert decoded is None
    
    def test_verify_token_wrong_secret(self, test_user_data):
        """Test verifying token with wrong secret key."""
        handler1 = JWTHandler(JWTConfig(secret_key="secret1"))
        handler2 = JWTHandler(JWTConfig(secret_key="secret2"))
        
        token = handler1.create_access_token(test_user_data)
        decoded = handler2.verify_token(token, token_type="access")
        
        assert decoded is None
    
    def test_refresh_access_token(self, jwt_handler, test_user_data):
        """Test refreshing an access token."""
        refresh_token = jwt_handler.create_refresh_token(test_user_data)
        
        result = jwt_handler.refresh_access_token(refresh_token)
        assert result is not None
        
        new_access_token, new_refresh_token = result
        assert isinstance(new_access_token, str)
        assert isinstance(new_refresh_token, str)
        
        # Verify new tokens
        access_decoded = jwt_handler.verify_token(new_access_token, token_type="access")
        refresh_decoded = jwt_handler.verify_token(new_refresh_token, token_type="refresh")
        
        assert access_decoded is not None
        assert refresh_decoded is not None
        assert access_decoded.sub == test_user_data["sub"]
        assert refresh_decoded.sub == test_user_data["sub"]
    
    def test_refresh_with_invalid_token(self, jwt_handler):
        """Test refreshing with an invalid token."""
        result = jwt_handler.refresh_access_token("invalid.refresh.token")
        assert result is None
    
    def test_token_includes_standard_claims(self, jwt_handler, test_user_data):
        """Test that tokens include standard JWT claims."""
        token = jwt_handler.create_access_token(test_user_data)
        decoded = jwt_handler.verify_token(token, token_type="access")
        
        assert decoded.iat is not None  # Issued at
        assert decoded.exp is not None  # Expiration
        assert decoded.jti is not None  # JWT ID
    
    def test_module_level_functions(self, test_user_data):
        """Test module-level convenience functions."""
        # Test create_access_token
        token = create_access_token(test_user_data)
        assert isinstance(token, str)
        
        # Test verify_token
        decoded = verify_token(token)
        assert decoded is not None
        assert decoded.sub == test_user_data["sub"]