"""Comprehensive security tests for V2 infrastructure."""

import pytest
import jwt
import bcrypt
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any

from src.infrastructure.security.jwt_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.infrastructure.security.password_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.infrastructure.security.rbac import RoleBasedAccessControl, Role, Permission
from src.infrastructure.security.rate_limiter import RateLimiter
from src.infrastructure.security.api_key_manager import APIKeyManager
from src.infrastructure.security.security_headers import SecurityHeadersMiddleware
from src.core.domain.shared.exceptions import SecurityError, AuthenticationError


class TestJWTHandlerSecurity:
    """Comprehensive JWT security tests."""
    
    def test_jwt_creation_and_validation(self):
        """Test JWT token creation and validation."""
        jwt_handler = JWTHandler(secret_key="test-secret-key", algorithm="HS256")
        
        payload = {
            "user_id": "user123",
            "username": "testuser",
            "role": "analyst",
            "permissions": ["read:markets", "write:analysis"]
        }
        
        # Create token
        token = jwt_handler.create_token(payload, expires_in_minutes=30)
        assert token is not None
        assert isinstance(token, str)
        
        # Validate token
        decoded_payload = jwt_handler.validate_token(token)
        assert decoded_payload["user_id"] == payload["user_id"]
        assert decoded_payload["username"] == payload["username"]
        assert decoded_payload["role"] == payload["role"]
        assert "exp" in decoded_payload
        assert "iat" in decoded_payload
    
    def test_jwt_expiration_handling(self):
        """Test JWT token expiration."""
        jwt_handler = JWTHandler(secret_key="test-secret-key", algorithm="HS256")
        
        payload = {"user_id": "user123"}
        
        # Create token with very short expiration
        token = jwt_handler.create_token(payload, expires_in_minutes=0.01)  # 0.6 seconds
        
        # Token should be valid immediately
        decoded = jwt_handler.validate_token(token)
        assert decoded["user_id"] == "user123"
        
        # Wait for token to expire
        time.sleep(1)
        
        # Token should now be expired
        with pytest.raises(AuthenticationError):
            jwt_handler.validate_token(token)
    
    def test_jwt_tampering_detection(self):
        """Test detection of tampered JWT tokens."""
        jwt_handler = JWTHandler(secret_key="test-secret-key", algorithm="HS256")
        
        payload = {"user_id": "user123", "role": "analyst"}
        token = jwt_handler.create_token(payload)
        
        # Tamper with token by changing a character
        tampered_token = token[:-10] + "tampered123"
        
        # Should detect tampering
        with pytest.raises(AuthenticationError):
            jwt_handler.validate_token(tampered_token)
    
    def test_jwt_algorithm_security(self):
        """Test JWT algorithm security."""
        # Test that 'none' algorithm is rejected
        with pytest.raises(ValueError):
            JWTHandler(secret_key="test-key", algorithm="none")
        
        # Test that weak algorithms are rejected
        with pytest.raises(ValueError):
            JWTHandler(secret_key="test-key", algorithm="HS1")
    
    def test_jwt_secret_key_requirements(self):
        """Test JWT secret key security requirements."""
        # Test weak secret key rejection
        with pytest.raises(ValueError):
            JWTHandler(secret_key="weak", algorithm="HS256")
        
        # Test empty secret key rejection
        with pytest.raises(ValueError):
            JWTHandler(secret_key="", algorithm="HS256")
        
        # Test None secret key rejection
        with pytest.raises(ValueError):
            JWTHandler(secret_key=None, algorithm="HS256")
    
    def test_jwt_refresh_token_security(self):
        """Test refresh token security."""
        jwt_handler = JWTHandler(secret_key="test-secret-key", algorithm="HS256")
        
        payload = {"user_id": "user123", "type": "refresh"}
        refresh_token = jwt_handler.create_refresh_token(payload)
        
        # Refresh token should have longer expiration
        decoded = jwt_handler.validate_refresh_token(refresh_token)
        assert decoded["user_id"] == "user123"
        assert decoded["type"] == "refresh"
        
        # Access token should not be valid as refresh token
        access_token = jwt_handler.create_token({"user_id": "user123"})
        with pytest.raises(AuthenticationError):
            jwt_handler.validate_refresh_token(access_token)


class TestPasswordSecurity:
    """Comprehensive password security tests."""
    
    def test_password_hashing_security(self):
        """Test password hashing security."""
        password_handler = PasswordHandler()
        
        password = "SecurePassword123!"
        hashed = password_handler.hash_password(password)
        
        # Hash should be different from original password
        assert hashed != password
        
        # Hash should be bcrypt format
        assert hashed.startswith('$2b$')
        
        # Should verify correctly
        assert password_handler.verify_password(password, hashed)
        
        # Should not verify with wrong password
        assert not password_handler.verify_password("WrongPassword", hashed)
    
    def test_password_strength_requirements(self):
        """Test password strength validation."""
        password_handler = PasswordHandler()
        
        # Test weak passwords
        weak_passwords = [
            "123456",           # Too simple
            "password",         # Dictionary word
            "pass",            # Too short
            "12345678",        # Only numbers
            "abcdefgh",        # Only lowercase
            "ABCDEFGH",        # Only uppercase
        ]
        
        for weak_password in weak_passwords:
            with pytest.raises(ValueError):
                password_handler.validate_password_strength(weak_password)
        
        # Test strong passwords
        strong_passwords = [
            "SecurePassword123!",
            "MyP@ssw0rd2024",
            "Str0ng&Secure#Pass",
        ]
        
        for strong_password in strong_passwords:
            # Should not raise exception
            password_handler.validate_password_strength(strong_password)
    
    def test_password_timing_attack_resistance(self):
        """Test resistance to timing attacks."""
        password_handler = PasswordHandler()
        
        # Hash a password
        correct_password = "CorrectPassword123!"
        hashed = password_handler.hash_password(correct_password)
        
        # Measure time for correct password
        start_time = time.time()
        password_handler.verify_password(correct_password, hashed)
        correct_time = time.time() - start_time
        
        # Measure time for incorrect password
        start_time = time.time()
        password_handler.verify_password("IncorrectPassword", hashed)
        incorrect_time = time.time() - start_time
        
        # Times should be similar (within reasonable variance)
        time_ratio = max(correct_time, incorrect_time) / min(correct_time, incorrect_time)
        assert time_ratio < 2.0  # Should not differ by more than 2x
    
    def test_password_salt_uniqueness(self):
        """Test that password salts are unique."""
        password_handler = PasswordHandler()
        
        password = "SamePassword123!"
        
        # Hash same password multiple times
        hashes = []
        for _ in range(10):
            hashed = password_handler.hash_password(password)
            hashes.append(hashed)
        
        # All hashes should be different (due to unique salts)
        assert len(set(hashes)) == len(hashes)


class TestRoleBasedAccessControl:
    """Test RBAC security implementation."""
    
    def test_permission_hierarchy(self):
        """Test permission hierarchy and inheritance."""
        rbac = RoleBasedAccessControl()
        
        # Define roles with different permission levels
        viewer_role = Role(
            name="viewer",
            permissions=[Permission("read:markets"), Permission("read:prices")]
        )
        
        analyst_role = Role(
            name="analyst",
            permissions=[
                Permission("read:markets"),
                Permission("read:prices"),
                Permission("write:analysis"),
                Permission("read:analysis")
            ]
        )
        
        admin_role = Role(
            name="admin",
            permissions=[
                Permission("read:*"),
                Permission("write:*"),
                Permission("delete:*"),
                Permission("admin:*")
            ]
        )
        
        rbac.add_role(viewer_role)
        rbac.add_role(analyst_role)
        rbac.add_role(admin_role)
        
        # Test permission checks
        assert rbac.has_permission("viewer", Permission("read:markets"))
        assert not rbac.has_permission("viewer", Permission("write:analysis"))
        
        assert rbac.has_permission("analyst", Permission("read:markets"))
        assert rbac.has_permission("analyst", Permission("write:analysis"))
        assert not rbac.has_permission("analyst", Permission("delete:analysis"))
        
        assert rbac.has_permission("admin", Permission("read:markets"))
        assert rbac.has_permission("admin", Permission("write:analysis"))
        assert rbac.has_permission("admin", Permission("delete:analysis"))
    
    def test_dynamic_permission_checking(self):
        """Test dynamic permission checking."""
        rbac = RoleBasedAccessControl()
        
        # Test resource-specific permissions
        user_permissions = [
            Permission("read:markets:sanaa"),
            Permission("write:analysis:own"),
            Permission("read:prices:wheat")
        ]
        
        user_role = Role(name="regional_analyst", permissions=user_permissions)
        rbac.add_role(user_role)
        
        # Test specific resource access
        assert rbac.has_permission("regional_analyst", Permission("read:markets:sanaa"))
        assert not rbac.has_permission("regional_analyst", Permission("read:markets:aden"))
        assert rbac.has_permission("regional_analyst", Permission("write:analysis:own"))
        assert not rbac.has_permission("regional_analyst", Permission("write:analysis:all"))
    
    def test_role_modification_security(self):
        """Test security of role modifications."""
        rbac = RoleBasedAccessControl()
        
        # Only admin should be able to modify roles
        admin_role = Role(name="admin", permissions=[Permission("admin:roles")])
        user_role = Role(name="user", permissions=[Permission("read:markets")])
        
        rbac.add_role(admin_role)
        rbac.add_role(user_role)
        
        # Admin can modify roles
        assert rbac.can_modify_roles("admin")
        
        # Regular user cannot modify roles
        assert not rbac.can_modify_roles("user")


class TestRateLimitingSecurity:
    """Test rate limiting security measures."""
    
    @pytest.mark.asyncio
    async def test_basic_rate_limiting(self):
        """Test basic rate limiting functionality."""
        rate_limiter = RateLimiter(max_requests=5, time_window_seconds=60)
        
        client_id = "test_client"
        
        # First 5 requests should be allowed
        for _ in range(5):
            assert await rate_limiter.is_allowed(client_id)
        
        # 6th request should be denied
        assert not await rate_limiter.is_allowed(client_id)
    
    @pytest.mark.asyncio
    async def test_rate_limit_window_reset(self):
        """Test rate limit window reset."""
        rate_limiter = RateLimiter(max_requests=2, time_window_seconds=1)
        
        client_id = "test_client"
        
        # Use up rate limit
        assert await rate_limiter.is_allowed(client_id)
        assert await rate_limiter.is_allowed(client_id)
        assert not await rate_limiter.is_allowed(client_id)
        
        # Wait for window to reset
        await asyncio.sleep(1.1)
        
        # Should be allowed again
        assert await rate_limiter.is_allowed(client_id)
    
    @pytest.mark.asyncio
    async def test_different_client_isolation(self):
        """Test that different clients have separate rate limits."""
        rate_limiter = RateLimiter(max_requests=2, time_window_seconds=60)
        
        client1 = "client1"
        client2 = "client2"
        
        # Use up client1's rate limit
        assert await rate_limiter.is_allowed(client1)
        assert await rate_limiter.is_allowed(client1)
        assert not await rate_limiter.is_allowed(client1)
        
        # Client2 should still be allowed
        assert await rate_limiter.is_allowed(client2)
        assert await rate_limiter.is_allowed(client2)
        assert not await rate_limiter.is_allowed(client2)
    
    @pytest.mark.asyncio
    async def test_progressive_rate_limiting(self):
        """Test progressive rate limiting for repeated violations."""
        rate_limiter = RateLimiter(
            max_requests=3,
            time_window_seconds=60,
            progressive_penalties=True
        )
        
        client_id = "test_client"
        
        # First violation - standard penalty
        for _ in range(4):  # Exceed limit by 1
            await rate_limiter.is_allowed(client_id)
        
        penalty1 = await rate_limiter.get_penalty_duration(client_id)
        
        # Reset and violate again
        await rate_limiter.reset_client(client_id)
        for _ in range(4):
            await rate_limiter.is_allowed(client_id)
        
        penalty2 = await rate_limiter.get_penalty_duration(client_id)
        
        # Second penalty should be longer
        assert penalty2 > penalty1


class TestAPIKeySecurity:
    """Test API key security measures."""
    
    def test_api_key_generation(self):
        """Test secure API key generation."""
        api_key_manager = APIKeyManager()
        
        # Generate multiple keys
        keys = []
        for _ in range(10):
            key = api_key_manager.generate_api_key()
            keys.append(key)
        
        # All keys should be unique
        assert len(set(keys)) == len(keys)
        
        # Keys should have appropriate length and format
        for key in keys:
            assert len(key) >= 32  # Minimum length
            assert key.isalnum() or '-' in key or '_' in key  # Valid characters
    
    def test_api_key_validation(self):
        """Test API key validation."""
        api_key_manager = APIKeyManager()
        
        # Generate and store a key
        api_key = api_key_manager.generate_api_key()
        user_id = "user123"
        permissions = ["read:markets", "write:analysis"]
        
        api_key_manager.store_api_key(api_key, user_id, permissions)
        
        # Valid key should authenticate
        auth_result = api_key_manager.authenticate_api_key(api_key)
        assert auth_result["valid"]
        assert auth_result["user_id"] == user_id
        assert auth_result["permissions"] == permissions
        
        # Invalid key should not authenticate
        invalid_key = "invalid_key_123"
        auth_result = api_key_manager.authenticate_api_key(invalid_key)
        assert not auth_result["valid"]
    
    def test_api_key_expiration(self):
        """Test API key expiration."""
        api_key_manager = APIKeyManager()
        
        api_key = api_key_manager.generate_api_key()
        user_id = "user123"
        
        # Store key with short expiration
        expiration = datetime.now() + timedelta(seconds=1)
        api_key_manager.store_api_key(
            api_key, user_id, [], expiration_date=expiration
        )
        
        # Key should be valid initially
        auth_result = api_key_manager.authenticate_api_key(api_key)
        assert auth_result["valid"]
        
        # Wait for expiration
        time.sleep(1.1)
        
        # Key should now be expired
        auth_result = api_key_manager.authenticate_api_key(api_key)
        assert not auth_result["valid"]
        assert "expired" in auth_result.get("reason", "")
    
    def test_api_key_revocation(self):
        """Test API key revocation."""
        api_key_manager = APIKeyManager()
        
        api_key = api_key_manager.generate_api_key()
        user_id = "user123"
        
        api_key_manager.store_api_key(api_key, user_id, [])
        
        # Key should be valid
        auth_result = api_key_manager.authenticate_api_key(api_key)
        assert auth_result["valid"]
        
        # Revoke key
        api_key_manager.revoke_api_key(api_key)
        
        # Key should no longer be valid
        auth_result = api_key_manager.authenticate_api_key(api_key)
        assert not auth_result["valid"]
        assert "revoked" in auth_result.get("reason", "")


class TestSecurityHeaders:
    """Test security headers middleware."""
    
    def test_security_headers_application(self):
        """Test that security headers are properly applied."""
        middleware = SecurityHeadersMiddleware()
        
        # Mock request and response
        request = Mock()
        response = Mock()
        response.headers = {}
        
        # Apply security headers
        middleware.add_security_headers(response)
        
        # Check that security headers are present
        expected_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options",
            "X-XSS-Protection",
            "Strict-Transport-Security",
            "Content-Security-Policy",
            "Referrer-Policy"
        ]
        
        for header in expected_headers:
            assert header in response.headers
    
    def test_csp_header_configuration(self):
        """Test Content Security Policy header configuration."""
        middleware = SecurityHeadersMiddleware()
        
        response = Mock()
        response.headers = {}
        
        middleware.add_security_headers(response)
        
        csp_header = response.headers.get("Content-Security-Policy")
        assert csp_header is not None
        
        # Check for secure CSP directives
        assert "default-src 'self'" in csp_header
        assert "script-src" in csp_header
        assert "object-src 'none'" in csp_header
        assert "base-uri 'self'" in csp_header
    
    def test_hsts_header_configuration(self):
        """Test HTTP Strict Transport Security header."""
        middleware = SecurityHeadersMiddleware()
        
        response = Mock()
        response.headers = {}
        
        middleware.add_security_headers(response)
        
        hsts_header = response.headers.get("Strict-Transport-Security")
        assert hsts_header is not None
        assert "max-age=" in hsts_header
        assert "includeSubDomains" in hsts_header


class TestInputValidationSecurity:
    """Test input validation and sanitization."""
    
    def test_sql_injection_prevention(self):
        """Test SQL injection prevention."""
        from src.infrastructure.persistence.repositories.postgres.market_repository import PostgresMarketRepository
        
        # Test that repository properly sanitizes inputs
        # This would typically be done through parameterized queries
        malicious_inputs = [
            "'; DROP TABLE markets; --",
            "1' OR '1'='1",
            "1; DELETE FROM prices; --",
            "UNION SELECT * FROM users --"
        ]
        
        # In a real test, we would verify that these inputs are properly escaped
        # For now, we'll test that they don't cause obvious issues
        for malicious_input in malicious_inputs:
            # Repository should handle these safely through parameterization
            assert malicious_input != malicious_input  # Placeholder assertion
    
    def test_xss_prevention(self):
        """Test XSS prevention in API responses."""
        malicious_scripts = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "<svg onload=alert('xss')>"
        ]
        
        # API should properly escape or reject these inputs
        for script in malicious_scripts:
            # In real implementation, these should be escaped or rejected
            escaped = html.escape(script)
            assert "<script>" not in escaped or "&lt;script&gt;" in escaped
    
    def test_path_traversal_prevention(self):
        """Test path traversal attack prevention."""
        malicious_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fbooths",
            "....//....//....//etc/passwd"
        ]
        
        # File access functions should validate and sanitize paths
        for malicious_path in malicious_paths:
            # Should reject or sanitize these paths
            assert ".." not in malicious_path.replace("%2e", "").replace("....", "")


class TestSecurityAuditing:
    """Test security auditing and logging."""
    
    def test_authentication_attempt_logging(self):
        """Test logging of authentication attempts."""
        with patch('src.infrastructure.logging.get_logger') as mock_logger:
            jwt_handler = JWTHandler(secret_key="test-secret-key", algorithm="HS256")
            
            # Valid authentication
            payload = {"user_id": "user123"}
            token = jwt_handler.create_token(payload)
            jwt_handler.validate_token(token)
            
            # Invalid authentication
            try:
                jwt_handler.validate_token("invalid_token")
            except AuthenticationError:
                pass
            
            # Verify logging calls were made
            assert mock_logger.return_value.info.called or mock_logger.return_value.warning.called
    
    def test_authorization_failure_logging(self):
        """Test logging of authorization failures."""
        with patch('src.infrastructure.logging.get_logger') as mock_logger:
            rbac = RoleBasedAccessControl()
            
            # Attempt unauthorized action
            result = rbac.has_permission("user", Permission("admin:delete"))
            assert not result
            
            # Should log authorization attempt
            assert mock_logger.return_value.warning.called
    
    def test_rate_limit_violation_logging(self):
        """Test logging of rate limit violations."""
        with patch('src.infrastructure.logging.get_logger') as mock_logger:
            rate_limiter = RateLimiter(max_requests=1, time_window_seconds=60)
            
            client_id = "test_client"
            
            # Trigger rate limit violation
            asyncio.run(rate_limiter.is_allowed(client_id))  # Allowed
            asyncio.run(rate_limiter.is_allowed(client_id))  # Denied
            
            # Should log rate limit violation
            assert mock_logger.return_value.warning.called or mock_logger.return_value.error.called


import asyncio
import html