"""Tests for Domain Repository Interfaces."""

import pytest
from abc import ABC
from datetime import datetime
from uuid import uuid4
from typing import List, Optional

from src.core.domain.market.repositories import MarketRepository, PriceRepository
from src.core.domain.conflict.repositories import ConflictRepository
from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.conflict.entities import ConflictEvent
from src.core.domain.market.value_objects import MarketId, Commodity, Price, Coordinates, MarketType
from src.core.domain.conflict.value_objects import ConflictEventId, EventType, Severity
from src.core.domain.shared.exceptions import RepositoryError, EntityNotFoundError


class TestMarketRepository:
    """Test market repository interface compliance."""
    
    def test_market_repository_is_abstract(self):
        """Test that MarketRepository is abstract."""
        assert ABC in MarketRepository.__bases__
        
        # Should not be able to instantiate directly
        with pytest.raises(TypeError):
            MarketRepository()
    
    def test_market_repository_methods_exist(self):
        """Test that all required methods exist on MarketRepository."""
        required_methods = [
            'add',
            'get_by_id',
            'get_by_name',
            'get_all',
            'get_by_governorate',
            'get_active_markets',
            'update',
            'delete',
            'search_by_coordinates'
        ]
        
        for method in required_methods:
            assert hasattr(MarketRepository, method)
            assert callable(getattr(MarketRepository, method))


class TestPriceRepository:
    """Test price repository interface compliance."""
    
    def test_price_repository_is_abstract(self):
        """Test that PriceRepository is abstract."""
        assert ABC in PriceRepository.__bases__
        
        # Should not be able to instantiate directly
        with pytest.raises(TypeError):
            PriceRepository()
    
    def test_price_repository_methods_exist(self):
        """Test that all required methods exist on PriceRepository."""
        required_methods = [
            'add',
            'get_by_id',
            'get_by_market_and_commodity',
            'get_price_series',
            'get_market_prices',
            'get_commodity_prices',
            'get_price_range',
            'bulk_insert',
            'update',
            'delete'
        ]
        
        for method in required_methods:
            assert hasattr(PriceRepository, method)
            assert callable(getattr(PriceRepository, method))


class TestConflictRepository:
    """Test conflict repository interface compliance."""
    
    def test_conflict_repository_is_abstract(self):
        """Test that ConflictRepository is abstract."""
        assert ABC in ConflictRepository.__bases__
        
        # Should not be able to instantiate directly
        with pytest.raises(TypeError):
            ConflictRepository()
    
    def test_conflict_repository_methods_exist(self):
        """Test that all required methods exist on ConflictRepository."""
        required_methods = [
            'add',
            'get_by_id',
            'get_events_by_location',
            'get_events_by_date_range',
            'get_events_by_type',
            'get_nearby_events',
            'bulk_insert',
            'update',
            'delete'
        ]
        
        for method in required_methods:
            assert hasattr(ConflictRepository, method)
            assert callable(getattr(ConflictRepository, method))


class MockMarketRepository(MarketRepository):
    """Mock implementation for testing repository behavior."""
    
    def __init__(self):
        self._markets = {}
        self._call_log = []
    
    async def add(self, market: Market) -> None:
        self._call_log.append(('add', market.market_id))
        self._markets[market.market_id.value] = market
    
    async def get_by_id(self, market_id: MarketId) -> Optional[Market]:
        self._call_log.append(('get_by_id', market_id))
        return self._markets.get(market_id.value)
    
    async def get_by_name(self, name: str) -> Optional[Market]:
        self._call_log.append(('get_by_name', name))
        for market in self._markets.values():
            if market.name == name:
                return market
        return None
    
    async def get_all(self) -> List[Market]:
        self._call_log.append(('get_all',))
        return list(self._markets.values())
    
    async def get_by_governorate(self, governorate: str) -> List[Market]:
        self._call_log.append(('get_by_governorate', governorate))
        return [m for m in self._markets.values() if m.governorate == governorate]
    
    async def get_active_markets(self) -> List[Market]:
        self._call_log.append(('get_active_markets',))
        return [m for m in self._markets.values() if m.is_active]
    
    async def update(self, market: Market) -> None:
        self._call_log.append(('update', market.market_id))
        if market.market_id.value in self._markets:
            self._markets[market.market_id.value] = market
        else:
            raise EntityNotFoundError(f"Market {market.market_id.value} not found")
    
    async def delete(self, market_id: MarketId) -> None:
        self._call_log.append(('delete', market_id))
        if market_id.value in self._markets:
            del self._markets[market_id.value]
        else:
            raise EntityNotFoundError(f"Market {market_id.value} not found")
    
    async def search_by_coordinates(self, lat: float, lon: float, radius_km: float) -> List[Market]:
        self._call_log.append(('search_by_coordinates', lat, lon, radius_km))
        # Simple distance calculation for testing
        results = []
        for market in self._markets.values():
            # Simplified distance check (not geographically accurate)
            lat_diff = abs(market.coordinates.latitude - lat)
            lon_diff = abs(market.coordinates.longitude - lon)
            if lat_diff < radius_km/100 and lon_diff < radius_km/100:  # Rough approximation
                results.append(market)
        return results


class TestRepositoryBehavior:
    """Test repository behavior using mock implementations."""
    
    @pytest.fixture
    def mock_market_repo(self):
        return MockMarketRepository()
    
    @pytest.fixture
    def sample_market(self):
        return Market(
            market_id=MarketId("TEST_MARKET"),
            name="Test Market",
            coordinates=Coordinates(latitude=15.0, longitude=44.0),
            market_type=MarketType.RETAIL,
            governorate="Test Governorate",
            district="Test District",
            active_since=datetime(2020, 1, 1)
        )
    
    async def test_add_and_retrieve_market(self, mock_market_repo, sample_market):
        """Test adding and retrieving a market."""
        # Add market
        await mock_market_repo.add(sample_market)
        
        # Retrieve by ID
        retrieved = await mock_market_repo.get_by_id(sample_market.market_id)
        assert retrieved is not None
        assert retrieved.market_id == sample_market.market_id
        assert retrieved.name == sample_market.name
        
        # Verify call log
        assert ('add', sample_market.market_id) in mock_market_repo._call_log
        assert ('get_by_id', sample_market.market_id) in mock_market_repo._call_log
    
    async def test_get_nonexistent_market(self, mock_market_repo):
        """Test retrieving a market that doesn't exist."""
        non_existent_id = MarketId("NON_EXISTENT")
        result = await mock_market_repo.get_by_id(non_existent_id)
        assert result is None
    
    async def test_update_market(self, mock_market_repo, sample_market):
        """Test updating a market."""
        # Add market first
        await mock_market_repo.add(sample_market)
        
        # Update market name
        sample_market.name = "Updated Market Name"
        await mock_market_repo.update(sample_market)
        
        # Retrieve and verify update
        retrieved = await mock_market_repo.get_by_id(sample_market.market_id)
        assert retrieved.name == "Updated Market Name"
    
    async def test_update_nonexistent_market(self, mock_market_repo, sample_market):
        """Test updating a market that doesn't exist."""
        with pytest.raises(EntityNotFoundError):
            await mock_market_repo.update(sample_market)
    
    async def test_delete_market(self, mock_market_repo, sample_market):
        """Test deleting a market."""
        # Add market first
        await mock_market_repo.add(sample_market)
        
        # Verify it exists
        retrieved = await mock_market_repo.get_by_id(sample_market.market_id)
        assert retrieved is not None
        
        # Delete market
        await mock_market_repo.delete(sample_market.market_id)
        
        # Verify it's gone
        retrieved = await mock_market_repo.get_by_id(sample_market.market_id)
        assert retrieved is None
    
    async def test_delete_nonexistent_market(self, mock_market_repo):
        """Test deleting a market that doesn't exist."""
        non_existent_id = MarketId("NON_EXISTENT")
        with pytest.raises(EntityNotFoundError):
            await mock_market_repo.delete(non_existent_id)
    
    async def test_get_markets_by_governorate(self, mock_market_repo):
        """Test retrieving markets by governorate."""
        # Add markets in different governorates
        market1 = Market(
            market_id=MarketId("MARKET1"),
            name="Market 1",
            coordinates=Coordinates(latitude=15.0, longitude=44.0),
            market_type=MarketType.RETAIL,
            governorate="Sana'a",
            district="District 1",
            active_since=datetime(2020, 1, 1)
        )
        
        market2 = Market(
            market_id=MarketId("MARKET2"),
            name="Market 2",
            coordinates=Coordinates(latitude=16.0, longitude=45.0),
            market_type=MarketType.WHOLESALE,
            governorate="Sana'a",
            district="District 2",
            active_since=datetime(2020, 1, 1)
        )
        
        market3 = Market(
            market_id=MarketId("MARKET3"),
            name="Market 3",
            coordinates=Coordinates(latitude=17.0, longitude=46.0),
            market_type=MarketType.RETAIL,
            governorate="Aden",
            district="District 3",
            active_since=datetime(2020, 1, 1)
        )
        
        await mock_market_repo.add(market1)
        await mock_market_repo.add(market2)
        await mock_market_repo.add(market3)
        
        # Get markets in Sana'a
        sanaa_markets = await mock_market_repo.get_by_governorate("Sana'a")
        assert len(sanaa_markets) == 2
        assert all(m.governorate == "Sana'a" for m in sanaa_markets)
        
        # Get markets in Aden
        aden_markets = await mock_market_repo.get_by_governorate("Aden")
        assert len(aden_markets) == 1
        assert aden_markets[0].governorate == "Aden"
    
    async def test_search_by_coordinates(self, mock_market_repo):
        """Test searching markets by coordinates."""
        # Add markets at different locations
        market1 = Market(
            market_id=MarketId("MARKET1"),
            name="Market 1",
            coordinates=Coordinates(latitude=15.0, longitude=44.0),
            market_type=MarketType.RETAIL,
            governorate="Test",
            district="Test",
            active_since=datetime(2020, 1, 1)
        )
        
        market2 = Market(
            market_id=MarketId("MARKET2"),
            name="Market 2",
            coordinates=Coordinates(latitude=15.1, longitude=44.1),  # Close to market1
            market_type=MarketType.RETAIL,
            governorate="Test",
            district="Test",
            active_since=datetime(2020, 1, 1)
        )
        
        market3 = Market(
            market_id=MarketId("MARKET3"),
            name="Market 3",
            coordinates=Coordinates(latitude=20.0, longitude=50.0),  # Far from others
            market_type=MarketType.RETAIL,
            governorate="Test",
            district="Test",
            active_since=datetime(2020, 1, 1)
        )
        
        await mock_market_repo.add(market1)
        await mock_market_repo.add(market2)
        await mock_market_repo.add(market3)
        
        # Search near market1
        nearby = await mock_market_repo.search_by_coordinates(15.0, 44.0, 50)  # 50km radius
        assert len(nearby) >= 1  # Should find at least market1
        
        # Verify all results are within reasonable distance
        for market in nearby:
            lat_diff = abs(market.coordinates.latitude - 15.0)
            lon_diff = abs(market.coordinates.longitude - 44.0)
            assert lat_diff < 0.5 or lon_diff < 0.5  # Rough distance check