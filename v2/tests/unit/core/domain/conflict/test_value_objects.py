"""Unit tests for conflict value objects."""

import pytest

from v2.src.core.domain.conflict.value_objects import (
    ConflictType, ConflictIntensity, ImpactRadius
)
from v2.src.core.domain.shared.exceptions import ValidationException


class TestConflictType:
    """Test ConflictType enum."""
    
    def test_conflict_types(self):
        """Test conflict type enum values."""
        assert ConflictType.BATTLE.value == "battle"
        assert ConflictType.EXPLOSION.value == "explosion"
        assert ConflictType.VIOLENCE_AGAINST_CIVILIANS.value == "violence_against_civilians"
        assert ConflictType.PROTESTS.value == "protests"
        assert ConflictType.RIOTS.value == "riots"
        assert ConflictType.STRATEGIC_DEVELOPMENT.value == "strategic_development"


class TestConflictIntensity:
    """Test ConflictIntensity enum."""
    
    def test_intensity_values(self):
        """Test intensity enum values."""
        assert ConflictIntensity.LOW.value == "low"
        assert ConflictIntensity.MEDIUM.value == "medium"
        assert ConflictIntensity.HIGH.value == "high"
        assert ConflictIntensity.SEVERE.value == "severe"
    
    def test_intensity_from_fatalities(self):
        """Test determining intensity from fatality count."""
        assert ConflictIntensity.from_fatalities(0) == ConflictIntensity.LOW
        assert ConflictIntensity.from_fatalities(1) == ConflictIntensity.LOW
        assert ConflictIntensity.from_fatalities(5) == ConflictIntensity.LOW
        assert ConflictIntensity.from_fatalities(6) == ConflictIntensity.MEDIUM
        assert ConflictIntensity.from_fatalities(25) == ConflictIntensity.MEDIUM
        assert ConflictIntensity.from_fatalities(26) == ConflictIntensity.HIGH
        assert ConflictIntensity.from_fatalities(100) == ConflictIntensity.HIGH
        assert ConflictIntensity.from_fatalities(101) == ConflictIntensity.SEVERE
        assert ConflictIntensity.from_fatalities(500) == ConflictIntensity.SEVERE


class TestImpactRadius:
    """Test ImpactRadius value object."""
    
    def test_valid_impact_radius(self):
        """Test creating valid impact radius."""
        radius = ImpactRadius(
            immediate_km=10.0,
            moderate_km=30.0,
            marginal_km=50.0
        )
        
        assert radius.immediate_km == 10.0
        assert radius.moderate_km == 30.0
        assert radius.marginal_km == 50.0
    
    def test_impact_radius_validation(self):
        """Test impact radius validation rules."""
        # Immediate radius must be positive
        with pytest.raises(ValidationException, match="Immediate radius must be positive"):
            ImpactRadius(
                immediate_km=0,
                moderate_km=20,
                marginal_km=30
            )
        
        # Moderate must be greater than immediate
        with pytest.raises(ValidationException, match="Moderate radius must be greater than immediate"):
            ImpactRadius(
                immediate_km=20,
                moderate_km=15,
                marginal_km=30
            )
        
        # Marginal must be greater than moderate
        with pytest.raises(ValidationException, match="Marginal radius must be greater than moderate"):
            ImpactRadius(
                immediate_km=10,
                moderate_km=30,
                marginal_km=25
            )
    
    def test_default_radius_for_types(self):
        """Test default impact radius for different conflict types."""
        # Battle should have large radius
        battle_radius = ImpactRadius.default_for_type(ConflictType.BATTLE)
        assert battle_radius.immediate_km == 10
        assert battle_radius.moderate_km == 30
        assert battle_radius.marginal_km == 50
        
        # Explosion should have medium radius
        explosion_radius = ImpactRadius.default_for_type(ConflictType.EXPLOSION)
        assert explosion_radius.immediate_km == 5
        assert explosion_radius.moderate_km == 15
        assert explosion_radius.marginal_km == 25
        
        # Violence against civilians should have smaller radius
        violence_radius = ImpactRadius.default_for_type(ConflictType.VIOLENCE_AGAINST_CIVILIANS)
        assert violence_radius.immediate_km == 5
        assert violence_radius.moderate_km == 10
        assert violence_radius.marginal_km == 20
        
        # Protests should have small radius
        protest_radius = ImpactRadius.default_for_type(ConflictType.PROTESTS)
        assert protest_radius.immediate_km == 2
        assert protest_radius.moderate_km == 5
        assert protest_radius.marginal_km == 10
        
        # Riots should have small radius
        riot_radius = ImpactRadius.default_for_type(ConflictType.RIOTS)
        assert riot_radius.immediate_km == 2
        assert riot_radius.moderate_km == 5
        assert riot_radius.marginal_km == 10
        
        # Strategic development should have minimal radius
        strategic_radius = ImpactRadius.default_for_type(ConflictType.STRATEGIC_DEVELOPMENT)
        assert strategic_radius.immediate_km == 1
        assert strategic_radius.moderate_km == 3
        assert strategic_radius.marginal_km == 5
    
    def test_impact_radius_ordering(self):
        """Test that different conflict types have appropriate radius ordering."""
        battle = ImpactRadius.default_for_type(ConflictType.BATTLE)
        explosion = ImpactRadius.default_for_type(ConflictType.EXPLOSION)
        violence = ImpactRadius.default_for_type(ConflictType.VIOLENCE_AGAINST_CIVILIANS)
        protests = ImpactRadius.default_for_type(ConflictType.PROTESTS)
        
        # Battle should have largest impact
        assert battle.marginal_km > explosion.marginal_km
        assert explosion.marginal_km > violence.marginal_km
        assert violence.marginal_km > protests.marginal_km
    
    def test_impact_radius_immutable(self):
        """Test that impact radius is immutable."""
        radius = ImpactRadius(
            immediate_km=10.0,
            moderate_km=30.0,
            marginal_km=50.0
        )
        
        with pytest.raises(AttributeError):
            radius.immediate_km = 15.0