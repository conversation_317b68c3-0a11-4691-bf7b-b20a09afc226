"""Unit tests for market entities."""

import pytest
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from uuid import UUID

from v2.src.core.domain.market.entities import (
    Market, PriceObservation, ExchangeRateObservation,
    MarketCreatedEvent, MarketDeactivatedEvent, ControlStatusChangedEvent
)
from v2.src.core.domain.market.value_objects import (
    MarketId, Coordinates, MarketType, ControlStatus, Currency,
    Price, Commodity, ExchangeRate
)
from v2.src.core.domain.shared.exceptions import BusinessRuleViolation, ValidationException


class TestMarket:
    """Test Market aggregate root."""
    
    def test_create_valid_market(self):
        """Test creating a valid market."""
        market = Market(
            market_id=MarketId("M001"),
            name="Sana'a Central Market",
            coordinates=Coordinates(15.3694, 44.1910),
            market_type=MarketType.WHOLESALE,
            governorate="Sana'a",
            district="Old City",
            active_since=datetime(2020, 1, 1)
        )
        
        assert market.market_id.value == "M001"
        assert market.name == "Sana'a Central Market"
        assert market.market_type == MarketType.WHOLESALE
        assert market.control_status == ControlStatus.UNKNOWN
        assert market.active_until is None
        
        # Check that creation event was raised
        assert len(market.events) == 1
        event = market.events[0]
        assert isinstance(event, MarketCreatedEvent)
        assert event.market_id == market.market_id
    
    def test_market_validation(self):
        """Test market validation rules."""
        base_date = datetime(2020, 1, 1)
        
        # Empty name
        with pytest.raises(ValidationException, match="Market name is required"):
            Market(
                market_id=MarketId("M001"),
                name="",
                coordinates=Coordinates(15.3694, 44.1910),
                market_type=MarketType.WHOLESALE,
                governorate="Sana'a",
                district="Old City",
                active_since=base_date
            )
        
        # Empty governorate
        with pytest.raises(ValidationException, match="Governorate is required"):
            Market(
                market_id=MarketId("M001"),
                name="Test Market",
                coordinates=Coordinates(15.3694, 44.1910),
                market_type=MarketType.WHOLESALE,
                governorate="",
                district="Old City",
                active_since=base_date
            )
        
        # Future activation date
        with pytest.raises(ValidationException, match="Active since date cannot be in the future"):
            Market(
                market_id=MarketId("M001"),
                name="Test Market",
                coordinates=Coordinates(15.3694, 44.1910),
                market_type=MarketType.WHOLESALE,
                governorate="Sana'a",
                district="Old City",
                active_since=datetime.utcnow() + timedelta(days=1)
            )
    
    def test_deactivate_market(self):
        """Test deactivating a market."""
        market = Market(
            market_id=MarketId("M001"),
            name="Test Market",
            coordinates=Coordinates(15.3694, 44.1910),
            market_type=MarketType.RETAIL,
            governorate="Sana'a",
            district="Old City",
            active_since=datetime(2020, 1, 1)
        )
        
        # Clear initial event
        market.clear_events()
        
        # Deactivate
        market.deactivate("Security concerns")
        
        assert market.active_until is not None
        assert len(market.events) == 1
        
        event = market.events[0]
        assert isinstance(event, MarketDeactivatedEvent)
        assert event.reason == "Security concerns"
    
    def test_cannot_deactivate_twice(self):
        """Test that market cannot be deactivated twice."""
        market = Market(
            market_id=MarketId("M001"),
            name="Test Market",
            coordinates=Coordinates(15.3694, 44.1910),
            market_type=MarketType.RETAIL,
            governorate="Sana'a",
            district="Old City",
            active_since=datetime(2020, 1, 1)
        )
        
        market.deactivate("First reason")
        
        with pytest.raises(BusinessRuleViolation, match="Market is already deactivated"):
            market.deactivate("Second reason")
    
    def test_is_active_at(self):
        """Test checking if market is active at specific date."""
        start_date = datetime(2020, 1, 1)
        end_date = datetime(2023, 12, 31)
        
        market = Market(
            market_id=MarketId("M001"),
            name="Test Market",
            coordinates=Coordinates(15.3694, 44.1910),
            market_type=MarketType.RETAIL,
            governorate="Sana'a",
            district="Old City",
            active_since=start_date,
            active_until=end_date
        )
        
        # Before activation
        assert not market.is_active_at(datetime(2019, 12, 31))
        
        # During active period
        assert market.is_active_at(datetime(2021, 6, 15))
        
        # After deactivation
        assert not market.is_active_at(datetime(2024, 1, 1))
    
    def test_commodity_trading_rules(self):
        """Test business rules for commodity trading."""
        port_market = Market(
            market_id=MarketId("M001"),
            name="Aden Port",
            coordinates=Coordinates(12.7855, 45.0187),
            market_type=MarketType.PORT,
            governorate="Aden",
            district="Port",
            active_since=datetime(2020, 1, 1)
        )
        
        rural_market = Market(
            market_id=MarketId("M002"),
            name="Rural Market",
            coordinates=Coordinates(15.0, 44.0),
            market_type=MarketType.RURAL,
            governorate="Ibb",
            district="Rural",
            active_since=datetime(2020, 1, 1)
        )
        
        # Port markets can only trade imported commodities
        imported_wheat = Commodity("WHT001", "Wheat", "imported", "kg")
        local_produce = Commodity("VEG001", "Vegetables", "local", "kg")
        
        assert port_market.can_trade_commodity(imported_wheat)
        assert not port_market.can_trade_commodity(local_produce)
        
        # Rural markets can trade local and agricultural
        assert rural_market.can_trade_commodity(local_produce)
        assert not rural_market.can_trade_commodity(imported_wheat)
    
    def test_update_control_status(self):
        """Test updating market control status."""
        market = Market(
            market_id=MarketId("M001"),
            name="Test Market",
            coordinates=Coordinates(15.3694, 44.1910),
            market_type=MarketType.RETAIL,
            governorate="Sana'a",
            district="Old City",
            active_since=datetime(2020, 1, 1),
            control_status=ControlStatus.GOVERNMENT
        )
        
        # Clear initial event
        market.clear_events()
        
        # Update control status
        change_date = datetime(2021, 6, 15)
        market.update_control_status(ControlStatus.HOUTHI, change_date)
        
        assert market.control_status == ControlStatus.HOUTHI
        assert market.control_status_date == change_date
        
        # Check event
        assert len(market.events) == 1
        event = market.events[0]
        assert isinstance(event, ControlStatusChangedEvent)
        assert event.previous_status == ControlStatus.GOVERNMENT
        assert event.new_status == ControlStatus.HOUTHI
    
    def test_control_status_validation(self):
        """Test control status change validation."""
        market = Market(
            market_id=MarketId("M001"),
            name="Test Market",
            coordinates=Coordinates(15.3694, 44.1910),
            market_type=MarketType.RETAIL,
            governorate="Sana'a",
            district="Old City",
            active_since=datetime(2020, 1, 1)
        )
        
        # Cannot change control before market activation
        with pytest.raises(BusinessRuleViolation, match="Control status change date cannot be before market activation"):
            market.update_control_status(ControlStatus.CONTESTED, datetime(2019, 12, 31))


class TestPriceObservation:
    """Test PriceObservation entity."""
    
    def test_create_valid_observation(self):
        """Test creating valid price observation."""
        commodity = Commodity("WHT001", "Wheat", "imported", "kg")
        price = Price(Decimal("100"), Currency.YER, "kg")
        
        observation = PriceObservation(
            market_id=MarketId("M001"),
            commodity=commodity,
            price=price,
            observed_date=datetime(2023, 6, 15),
            source="WFP",
            quality="standard",
            observations_count=5
        )
        
        assert observation.market_id.value == "M001"
        assert observation.commodity == commodity
        assert observation.price == price
        assert observation.source == "WFP"
        assert observation.observations_count == 5
    
    def test_observation_validation(self):
        """Test price observation validation."""
        commodity = Commodity("WHT001", "Wheat", "imported", "kg")
        price = Price(Decimal("100"), Currency.YER, "kg")
        
        # Future date
        with pytest.raises(ValidationException, match="Observation date cannot be in the future"):
            PriceObservation(
                market_id=MarketId("M001"),
                commodity=commodity,
                price=price,
                observed_date=datetime.utcnow() + timedelta(days=1),
                source="WFP"
            )
        
        # Invalid observation count
        with pytest.raises(ValidationException, match="Observations count must be at least 1"):
            PriceObservation(
                market_id=MarketId("M001"),
                commodity=commodity,
                price=price,
                observed_date=datetime(2023, 6, 15),
                source="WFP",
                observations_count=0
            )
        
        # Invalid quality
        with pytest.raises(ValidationException, match="Invalid quality"):
            PriceObservation(
                market_id=MarketId("M001"),
                commodity=commodity,
                price=price,
                observed_date=datetime(2023, 6, 15),
                source="WFP",
                quality="invalid"
            )
    
    def test_outlier_detection(self):
        """Test price outlier detection."""
        commodity = Commodity("WHT001", "Wheat", "imported", "kg")
        observation = PriceObservation(
            market_id=MarketId("M001"),
            commodity=commodity,
            price=Price(Decimal("500"), Currency.YER, "kg"),
            observed_date=datetime(2023, 6, 15),
            source="WFP"
        )
        
        # Test with mean=100, std=50, threshold=3
        mean_price = Decimal("100")
        std_dev = Decimal("50")
        
        # Price of 500 is (500-100)/50 = 8 standard deviations away
        assert observation.is_outlier(mean_price, std_dev, threshold=3.0)
        assert not observation.is_outlier(mean_price, std_dev, threshold=10.0)
    
    def test_merge_observations(self):
        """Test merging price observations."""
        commodity = Commodity("WHT001", "Wheat", "imported", "kg")
        date = datetime(2023, 6, 15)
        
        obs1 = PriceObservation(
            market_id=MarketId("M001"),
            commodity=commodity,
            price=Price(Decimal("100"), Currency.YER, "kg"),
            observed_date=date,
            source="WFP",
            observations_count=3
        )
        
        obs2 = PriceObservation(
            market_id=MarketId("M001"),
            commodity=commodity,
            price=Price(Decimal("120"), Currency.YER, "kg"),
            observed_date=date,
            source="FAO",
            observations_count=2
        )
        
        merged = obs1.merge_with(obs2)
        
        # Weighted average: (100*3 + 120*2) / 5 = 108
        assert merged.price.amount == Decimal("108")
        assert merged.observations_count == 5
        assert "WFP" in merged.source
        assert "FAO" in merged.source
    
    def test_cannot_merge_different_commodities(self):
        """Test that different commodities cannot be merged."""
        wheat = Commodity("WHT001", "Wheat", "imported", "kg")
        rice = Commodity("RIC001", "Rice", "imported", "kg")
        date = datetime(2023, 6, 15)
        
        obs1 = PriceObservation(
            market_id=MarketId("M001"),
            commodity=wheat,
            price=Price(Decimal("100"), Currency.YER, "kg"),
            observed_date=date,
            source="WFP"
        )
        
        obs2 = PriceObservation(
            market_id=MarketId("M001"),
            commodity=rice,
            price=Price(Decimal("150"), Currency.YER, "kg"),
            observed_date=date,
            source="WFP"
        )
        
        with pytest.raises(BusinessRuleViolation, match="Cannot merge observations of different commodities"):
            obs1.merge_with(obs2)


class TestExchangeRateObservation:
    """Test ExchangeRateObservation entity."""
    
    def test_create_valid_exchange_observation(self):
        """Test creating valid exchange rate observation."""
        exchange_rate = ExchangeRate(
            from_currency=Currency.YER,
            to_currency=Currency.USD,
            rate=Decimal("535"),
            rate_type="official_cby_sanaa"
        )
        
        observation = ExchangeRateObservation(
            market_id=MarketId("M001"),
            exchange_rate=exchange_rate,
            observed_date=datetime(2023, 6, 15),
            source="CBY",
            black_market_premium=Decimal("0.10")
        )
        
        assert observation.market_id.value == "M001"
        assert observation.exchange_rate == exchange_rate
        assert observation.black_market_premium == Decimal("0.10")
    
    def test_exchange_observation_validation(self):
        """Test exchange rate observation validation."""
        exchange_rate = ExchangeRate(
            from_currency=Currency.YER,
            to_currency=Currency.USD,
            rate=Decimal("535"),
            rate_type="parallel"
        )
        
        # Future date
        with pytest.raises(ValidationException, match="Observation date cannot be in the future"):
            ExchangeRateObservation(
                market_id=MarketId("M001"),
                exchange_rate=exchange_rate,
                observed_date=datetime.utcnow() + timedelta(days=1),
                source="Market"
            )
        
        # Empty source
        with pytest.raises(ValidationException, match="Source is required"):
            ExchangeRateObservation(
                market_id=MarketId("M001"),
                exchange_rate=exchange_rate,
                observed_date=datetime(2023, 6, 15),
                source=""
            )
        
        # Negative black market premium
        with pytest.raises(ValidationException, match="Black market premium cannot be negative"):
            ExchangeRateObservation(
                market_id=MarketId("M001"),
                exchange_rate=exchange_rate,
                observed_date=datetime(2023, 6, 15),
                source="Market",
                black_market_premium=Decimal("-0.05")
            )
    
    def test_get_effective_rate(self):
        """Test getting effective exchange rate with premium."""
        exchange_rate = ExchangeRate(
            from_currency=Currency.YER,
            to_currency=Currency.USD,
            rate=Decimal("500"),
            rate_type="official_cby_aden"
        )
        
        # Without premium
        obs1 = ExchangeRateObservation(
            market_id=MarketId("M001"),
            exchange_rate=exchange_rate,
            observed_date=datetime(2023, 6, 15),
            source="CBY"
        )
        assert obs1.get_effective_rate() == Decimal("500")
        
        # With 10% premium
        obs2 = ExchangeRateObservation(
            market_id=MarketId("M001"),
            exchange_rate=exchange_rate,
            observed_date=datetime(2023, 6, 15),
            source="Market",
            black_market_premium=Decimal("0.10")
        )
        assert obs2.get_effective_rate() == Decimal("550")  # 500 * 1.10