"""Unit tests for market value objects."""

import pytest
from decimal import Decimal
from datetime import datetime

from v2.src.core.domain.market.value_objects import (
    MarketId, Coordinates, MarketType, ControlStatus, Currency,
    Price, Commodity, MarketPair, ExchangeRate, IntegrationScore
)
from v2.src.core.domain.shared.exceptions import ValidationException


class TestMarketId:
    """Test MarketId value object."""
    
    def test_valid_market_id(self):
        """Test creating valid market ID."""
        market_id = MarketId("M001")
        assert market_id.value == "M001"
    
    def test_empty_market_id(self):
        """Test that empty market ID raises exception."""
        with pytest.raises(ValidationException, match="Market ID cannot be empty"):
            MarketId("")
    
    def test_whitespace_market_id(self):
        """Test that whitespace-only market ID raises exception."""
        with pytest.raises(ValidationException, match="Market ID cannot be empty"):
            MarketId("   ")
    
    def test_long_market_id(self):
        """Test that overly long market ID raises exception."""
        with pytest.raises(ValidationException, match="Market ID cannot exceed 50 characters"):
            MarketId("M" * 51)
    
    def test_market_id_equality(self):
        """Test market ID equality."""
        id1 = MarketId("M001")
        id2 = MarketId("M001")
        id3 = MarketId("M002")
        
        assert id1 == id2
        assert id1 != id3
    
    def test_market_id_immutable(self):
        """Test that market ID is immutable."""
        market_id = MarketId("M001")
        with pytest.raises(AttributeError):
            market_id.value = "M002"


class TestCoordinates:
    """Test Coordinates value object."""
    
    def test_valid_coordinates(self):
        """Test creating valid coordinates."""
        coords = Coordinates(latitude=15.3694, longitude=44.1910)
        assert coords.latitude == 15.3694
        assert coords.longitude == 44.1910
    
    def test_invalid_latitude(self):
        """Test invalid latitude values."""
        with pytest.raises(ValidationException, match="Invalid latitude"):
            Coordinates(latitude=91, longitude=44.1910)
        
        with pytest.raises(ValidationException, match="Invalid latitude"):
            Coordinates(latitude=-91, longitude=44.1910)
    
    def test_invalid_longitude(self):
        """Test invalid longitude values."""
        with pytest.raises(ValidationException, match="Invalid longitude"):
            Coordinates(latitude=15.3694, longitude=181)
        
        with pytest.raises(ValidationException, match="Invalid longitude"):
            Coordinates(latitude=15.3694, longitude=-181)
    
    def test_distance_calculation(self):
        """Test distance calculation between coordinates."""
        sanaa = Coordinates(latitude=15.3694, longitude=44.1910)
        aden = Coordinates(latitude=12.7855, longitude=45.0187)
        
        distance = sanaa.distance_to(aden)
        
        # Distance should be approximately 300km
        assert 290 < distance < 310
    
    def test_distance_to_same_point(self):
        """Test distance to same point is zero."""
        coords = Coordinates(latitude=15.3694, longitude=44.1910)
        assert coords.distance_to(coords) < 0.001


class TestCurrency:
    """Test Currency enum."""
    
    def test_currency_values(self):
        """Test currency enum values."""
        assert Currency.YER.value == "YER"
        assert Currency.USD.value == "USD"
        assert Currency.SAR.value == "SAR"
    
    def test_currency_symbols(self):
        """Test currency symbols."""
        assert Currency.YER.symbol == "﷼"
        assert Currency.USD.symbol == "$"
        assert Currency.SAR.symbol == "ر.س"


class TestPrice:
    """Test Price value object."""
    
    def test_valid_price(self):
        """Test creating valid price."""
        price = Price(
            amount=Decimal("100.50"),
            currency=Currency.YER,
            unit="kg"
        )
        assert price.amount == Decimal("100.50")
        assert price.currency == Currency.YER
        assert price.unit == "kg"
    
    def test_negative_price(self):
        """Test that negative price raises exception."""
        with pytest.raises(ValidationException, match="Price amount cannot be negative"):
            Price(amount=Decimal("-10"), currency=Currency.YER, unit="kg")
    
    def test_missing_unit(self):
        """Test that missing unit raises exception."""
        with pytest.raises(ValidationException, match="Unit is required"):
            Price(amount=Decimal("100"), currency=Currency.YER, unit="")
    
    def test_currency_conversion(self):
        """Test price currency conversion."""
        price_yer = Price(
            amount=Decimal("1000"),
            currency=Currency.YER,
            unit="kg"
        )
        
        # Convert YER to USD with rate 500 YER/USD
        price_usd = price_yer.convert_to(Currency.USD, Decimal("500"))
        
        assert price_usd.amount == Decimal("2")
        assert price_usd.currency == Currency.USD
        assert price_usd.unit == "kg"
    
    def test_same_currency_conversion(self):
        """Test converting to same currency returns same price."""
        price = Price(amount=Decimal("100"), currency=Currency.USD, unit="kg")
        converted = price.convert_to(Currency.USD, Decimal("500"))
        
        assert price == converted
    
    def test_invalid_exchange_rate(self):
        """Test that invalid exchange rate raises exception."""
        price = Price(amount=Decimal("100"), currency=Currency.YER, unit="kg")
        
        with pytest.raises(ValidationException, match="Exchange rate must be positive"):
            price.convert_to(Currency.USD, Decimal("0"))
    
    def test_unit_conversion(self):
        """Test price unit conversion."""
        price_bag = Price(
            amount=Decimal("5000"),
            currency=Currency.YER,
            unit="50kg bag"
        )
        
        # Convert from 50kg bag to kg
        price_kg = price_bag.to_standard_unit(Decimal("50"), "kg")
        
        assert price_kg.amount == Decimal("100")
        assert price_kg.unit == "kg"
    
    def test_price_immutable(self):
        """Test that price is immutable."""
        price = Price(amount=Decimal("100"), currency=Currency.YER, unit="kg")
        with pytest.raises(AttributeError):
            price.amount = Decimal("200")


class TestCommodity:
    """Test Commodity value object."""
    
    def test_valid_commodity(self):
        """Test creating valid commodity."""
        commodity = Commodity(
            code="WHT001",
            name="Wheat",
            category="imported",
            standard_unit="kg",
            perishable=False
        )
        assert commodity.code == "WHT001"
        assert commodity.name == "Wheat"
        assert commodity.category == "imported"
        assert commodity.standard_unit == "kg"
        assert commodity.perishable is False
    
    def test_invalid_category(self):
        """Test invalid commodity category."""
        with pytest.raises(ValidationException, match="Invalid commodity category"):
            Commodity(
                code="TEST",
                name="Test",
                category="invalid",
                standard_unit="kg"
            )
    
    def test_missing_required_fields(self):
        """Test missing required fields."""
        with pytest.raises(ValidationException, match="Commodity code is required"):
            Commodity(code="", name="Test", category="local", standard_unit="kg")
        
        with pytest.raises(ValidationException, match="Commodity name is required"):
            Commodity(code="TEST", name="", category="local", standard_unit="kg")
        
        with pytest.raises(ValidationException, match="Standard unit is required"):
            Commodity(code="TEST", name="Test", category="local", standard_unit="")


class TestMarketPair:
    """Test MarketPair value object."""
    
    def test_valid_market_pair(self):
        """Test creating valid market pair."""
        pair = MarketPair(
            source=MarketId("M001"),
            target=MarketId("M002"),
            distance_km=150.5
        )
        assert pair.source.value == "M001"
        assert pair.target.value == "M002"
        assert pair.distance_km == 150.5
    
    def test_same_market_pair(self):
        """Test that same source and target raises exception."""
        market_id = MarketId("M001")
        with pytest.raises(ValidationException, match="Source and target markets must be different"):
            MarketPair(source=market_id, target=market_id)
    
    def test_negative_distance(self):
        """Test that negative distance raises exception."""
        with pytest.raises(ValidationException, match="Distance cannot be negative"):
            MarketPair(
                source=MarketId("M001"),
                target=MarketId("M002"),
                distance_km=-10
            )


class TestExchangeRate:
    """Test ExchangeRate value object."""
    
    def test_valid_exchange_rate(self):
        """Test creating valid exchange rate."""
        rate = ExchangeRate(
            from_currency=Currency.YER,
            to_currency=Currency.USD,
            rate=Decimal("535"),
            rate_type="official_cby_sanaa"
        )
        assert rate.from_currency == Currency.YER
        assert rate.to_currency == Currency.USD
        assert rate.rate == Decimal("535")
        assert rate.rate_type == "official_cby_sanaa"
    
    def test_same_currency_exchange(self):
        """Test that same currency exchange raises exception."""
        with pytest.raises(ValidationException, match="Cannot have exchange rate between same currency"):
            ExchangeRate(
                from_currency=Currency.USD,
                to_currency=Currency.USD,
                rate=Decimal("1"),
                rate_type="official_cby_aden"
            )
    
    def test_invalid_rate(self):
        """Test invalid exchange rates."""
        with pytest.raises(ValidationException, match="Exchange rate must be positive"):
            ExchangeRate(
                from_currency=Currency.YER,
                to_currency=Currency.USD,
                rate=Decimal("0"),
                rate_type="parallel"
            )
    
    def test_invalid_rate_type(self):
        """Test invalid rate type."""
        with pytest.raises(ValidationException, match="Invalid rate type"):
            ExchangeRate(
                from_currency=Currency.YER,
                to_currency=Currency.USD,
                rate=Decimal("500"),
                rate_type="invalid_type"
            )
    
    def test_inverse_rate(self):
        """Test inverse exchange rate calculation."""
        rate = ExchangeRate(
            from_currency=Currency.YER,
            to_currency=Currency.USD,
            rate=Decimal("500"),
            rate_type="parallel"
        )
        
        inverse = rate.inverse()
        
        assert inverse.from_currency == Currency.USD
        assert inverse.to_currency == Currency.YER
        assert inverse.rate == Decimal("0.002")
        assert inverse.rate_type == "parallel"


class TestIntegrationScore:
    """Test IntegrationScore value object."""
    
    def test_valid_integration_score(self):
        """Test creating valid integration score."""
        score = IntegrationScore(
            score=0.85,
            method="correlation",
            confidence=0.95
        )
        assert score.score == 0.85
        assert score.method == "correlation"
        assert score.confidence == 0.95
    
    def test_invalid_score_range(self):
        """Test invalid score values."""
        with pytest.raises(ValidationException, match="Integration score must be between 0 and 1"):
            IntegrationScore(score=1.5, method="correlation", confidence=0.9)
        
        with pytest.raises(ValidationException, match="Integration score must be between 0 and 1"):
            IntegrationScore(score=-0.1, method="correlation", confidence=0.9)
    
    def test_invalid_confidence_range(self):
        """Test invalid confidence values."""
        with pytest.raises(ValidationException, match="Confidence must be between 0 and 1"):
            IntegrationScore(score=0.8, method="correlation", confidence=1.1)
    
    def test_invalid_method(self):
        """Test invalid integration method."""
        with pytest.raises(ValidationException, match="Invalid integration method"):
            IntegrationScore(score=0.8, method="invalid", confidence=0.9)
    
    def test_integration_level(self):
        """Test integration level classification."""
        high = IntegrationScore(score=0.85, method="correlation", confidence=0.9)
        moderate = IntegrationScore(score=0.6, method="correlation", confidence=0.9)
        low = IntegrationScore(score=0.3, method="correlation", confidence=0.9)
        none = IntegrationScore(score=0.1, method="correlation", confidence=0.9)
        
        assert high.integration_level == "high"
        assert moderate.integration_level == "moderate"
        assert low.integration_level == "low"
        assert none.integration_level == "none"