"""Unit tests for market domain services."""

import pytest
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List
import numpy as np

from v2.src.core.domain.market.services import (
    MarketIntegrationService, PriceTransmissionService,
    PriceValidationService, CurrencyConversionService,
    ConflictImpactService, MarketNetworkAnalysisService,
    TransmissionMetrics, IntegrationMetrics
)
from v2.src.core.domain.market.entities import (
    Market, PriceObservation, ExchangeRateObservation
)
from v2.src.core.domain.market.value_objects import (
    MarketId, Coordinates, MarketType, ControlStatus, Currency,
    Price, Commodity, MarketPair, ExchangeRate, IntegrationScore
)
from v2.src.core.domain.shared.exceptions import BusinessRuleViolation, ValidationException


class TestMarketIntegrationService:
    """Test MarketIntegrationService."""
    
    @pytest.fixture
    def integration_service(self):
        """Create integration service instance."""
        return MarketIntegrationService()
    
    @pytest.fixture
    def sample_commodity(self):
        """Create sample commodity."""
        return Commodity("WHT001", "Wheat", "imported", "kg")
    
    @pytest.fixture
    def sample_prices(self, sample_commodity):
        """Create sample price observations."""
        dates = [datetime(2023, 1, i) for i in range(1, 31)]
        
        # Create correlated price series
        base_prices = [100 + i * 0.5 + np.random.normal(0, 5) for i in range(30)]
        correlated_prices = [p * 1.1 + np.random.normal(0, 3) for p in base_prices]
        
        source_prices = [
            PriceObservation(
                market_id=MarketId("M001"),
                commodity=sample_commodity,
                price=Price(Decimal(str(p)), Currency.YER, "kg"),
                observed_date=date,
                source="WFP"
            )
            for date, p in zip(dates, base_prices)
        ]
        
        target_prices = [
            PriceObservation(
                market_id=MarketId("M002"),
                commodity=sample_commodity,
                price=Price(Decimal(str(p)), Currency.YER, "kg"),
                observed_date=date,
                source="WFP"
            )
            for date, p in zip(dates, correlated_prices)
        ]
        
        return source_prices, target_prices
    
    def test_calculate_integration_correlation(self, integration_service, sample_prices):
        """Test calculating integration using correlation method."""
        source_prices, target_prices = sample_prices
        market_pair = MarketPair(MarketId("M001"), MarketId("M002"), 100.0)
        
        score = integration_service.calculate_integration(
            market_pair,
            source_prices,
            target_prices,
            method="correlation"
        )
        
        assert isinstance(score, IntegrationScore)
        assert score.method == "correlation"
        assert 0 <= score.score <= 1
        assert 0 <= score.confidence <= 1
        assert score.score > 0.7  # Should be highly correlated
    
    def test_calculate_integration_insufficient_data(self, integration_service, sample_commodity):
        """Test integration calculation with insufficient data."""
        # Only 5 observations
        dates = [datetime(2023, 1, i) for i in range(1, 6)]
        prices = [
            PriceObservation(
                market_id=MarketId("M001"),
                commodity=sample_commodity,
                price=Price(Decimal("100"), Currency.YER, "kg"),
                observed_date=date,
                source="WFP"
            )
            for date in dates
        ]
        
        market_pair = MarketPair(MarketId("M001"), MarketId("M002"), 100.0)
        
        with pytest.raises(BusinessRuleViolation, match="Insufficient overlapping observations"):
            integration_service.calculate_integration(
                market_pair,
                prices,
                prices,
                method="correlation"
            )
    
    def test_calculate_integration_no_data(self, integration_service):
        """Test integration calculation with no data."""
        market_pair = MarketPair(MarketId("M001"), MarketId("M002"), 100.0)
        
        with pytest.raises(ValidationException, match="Cannot calculate integration without price data"):
            integration_service.calculate_integration(
                market_pair,
                [],
                [],
                method="correlation"
            )
    
    def test_invalid_integration_method(self, integration_service, sample_prices):
        """Test invalid integration method."""
        source_prices, target_prices = sample_prices
        market_pair = MarketPair(MarketId("M001"), MarketId("M002"), 100.0)
        
        with pytest.raises(ValidationException, match="Unsupported integration method"):
            integration_service.calculate_integration(
                market_pair,
                source_prices,
                target_prices,
                method="invalid_method"
            )


class TestPriceTransmissionService:
    """Test PriceTransmissionService."""
    
    @pytest.fixture
    def transmission_service(self):
        """Create transmission service instance."""
        return PriceTransmissionService()
    
    @pytest.fixture
    def sample_commodity(self):
        """Create sample commodity."""
        return Commodity("WHT001", "Wheat", "imported", "kg")
    
    @pytest.fixture
    def transmission_data(self, sample_commodity):
        """Create price data for transmission analysis."""
        dates = [datetime(2023, 1, i) for i in range(1, 61)]  # 60 days
        
        # Create price series with transmission relationship
        source_prices = []
        target_prices = []
        
        for i, date in enumerate(dates):
            # Source market price
            source_price = 100 + i * 0.5 + np.random.normal(0, 2)
            source_obs = PriceObservation(
                market_id=MarketId("M001"),
                commodity=sample_commodity,
                price=Price(Decimal(str(source_price)), Currency.YER, "kg"),
                observed_date=date,
                source="WFP"
            )
            source_prices.append(source_obs)
            
            # Target market price with lag and adjustment
            if i > 0:
                # Price transmission with adjustment speed
                target_price = 0.8 * source_price + 0.2 * float(target_prices[-1].price.amount)
                target_price += np.random.normal(0, 1)
            else:
                target_price = source_price * 1.05
            
            target_obs = PriceObservation(
                market_id=MarketId("M002"),
                commodity=sample_commodity,
                price=Price(Decimal(str(target_price)), Currency.YER, "kg"),
                observed_date=date,
                source="WFP"
            )
            target_prices.append(target_obs)
        
        return source_prices, target_prices
    
    def test_calculate_transmission(self, transmission_service, transmission_data):
        """Test calculating price transmission metrics."""
        source_prices, target_prices = transmission_data
        market_pair = MarketPair(MarketId("M001"), MarketId("M002"), 150.0)
        
        metrics = transmission_service.calculate_transmission(
            source_prices,
            target_prices,
            market_pair
        )
        
        assert isinstance(metrics, TransmissionMetrics)
        assert metrics.market_pair == market_pair
        assert metrics.commodity.code == "WHT001"
        assert -1 <= metrics.correlation <= 1
        assert metrics.beta_coefficient > 0  # Positive transmission
        assert 0 <= metrics.adjustment_speed <= 1
        assert metrics.half_life_days > 0
    
    def test_transmission_insufficient_data(self, transmission_service, sample_commodity):
        """Test transmission with insufficient data."""
        # Only 5 observations
        dates = [datetime(2023, 1, i) for i in range(1, 6)]
        prices = [
            PriceObservation(
                market_id=MarketId("M001"),
                commodity=sample_commodity,
                price=Price(Decimal("100"), Currency.YER, "kg"),
                observed_date=date,
                source="WFP"
            )
            for date in dates
        ]
        
        market_pair = MarketPair(MarketId("M001"), MarketId("M002"), 100.0)
        
        with pytest.raises(BusinessRuleViolation, match="Insufficient data for transmission analysis"):
            transmission_service.calculate_transmission(
                prices,
                prices,
                market_pair
            )
    
    def test_identify_transmission_breaks(self, transmission_service):
        """Test identifying structural breaks in transmission."""
        market_pair = MarketPair(MarketId("M001"), MarketId("M002"), 100.0)
        commodity = Commodity("WHT001", "Wheat", "imported", "kg")
        
        # Create transmission series with a break
        metrics_list = [
            TransmissionMetrics(
                market_pair=market_pair,
                commodity=commodity,
                period_start=datetime(2023, 1, 1),
                period_end=datetime(2023, 1, 31),
                correlation=0.8,
                beta_coefficient=0.9,
                adjustment_speed=0.5,
                half_life_days=1.4
            ),
            TransmissionMetrics(
                market_pair=market_pair,
                commodity=commodity,
                period_start=datetime(2023, 2, 1),
                period_end=datetime(2023, 2, 28),
                correlation=0.3,  # Significant drop
                beta_coefficient=0.4,  # Significant drop
                adjustment_speed=0.2,
                half_life_days=3.5
            ),
            TransmissionMetrics(
                market_pair=market_pair,
                commodity=commodity,
                period_start=datetime(2023, 3, 1),
                period_end=datetime(2023, 3, 31),
                correlation=0.35,
                beta_coefficient=0.45,
                adjustment_speed=0.25,
                half_life_days=2.8
            )
        ]
        
        breaks = transmission_service.identify_transmission_breaks(metrics_list, threshold=0.4)
        
        assert len(breaks) == 1
        assert breaks[0] == datetime(2023, 2, 1)


class TestPriceValidationService:
    """Test PriceValidationService."""
    
    @pytest.fixture
    def validation_service(self):
        """Create validation service instance."""
        return PriceValidationService(outlier_threshold=3.0)
    
    @pytest.fixture
    def sample_commodity(self):
        """Create sample commodity."""
        return Commodity("WHT001", "Wheat", "imported", "kg")
    
    @pytest.fixture
    def historical_prices(self, sample_commodity):
        """Create historical price data."""
        dates = [datetime(2023, 1, i) for i in range(1, 21)]
        
        # Normal prices around 100
        prices = []
        for date in dates:
            price_amount = 100 + np.random.normal(0, 5)
            obs = PriceObservation(
                market_id=MarketId("M001"),
                commodity=sample_commodity,
                price=Price(Decimal(str(price_amount)), Currency.YER, "kg"),
                observed_date=date,
                source="WFP"
            )
            prices.append(obs)
        
        return prices
    
    def test_validate_normal_price(self, validation_service, sample_commodity, historical_prices):
        """Test validating a normal price."""
        normal_obs = PriceObservation(
            market_id=MarketId("M001"),
            commodity=sample_commodity,
            price=Price(Decimal("105"), Currency.YER, "kg"),
            observed_date=datetime(2023, 1, 21),
            source="WFP"
        )
        
        is_valid = validation_service.validate_price_observation(
            normal_obs,
            historical_prices,
            sample_commodity
        )
        
        assert is_valid
    
    def test_validate_outlier_price(self, validation_service, sample_commodity, historical_prices):
        """Test detecting an outlier price."""
        outlier_obs = PriceObservation(
            market_id=MarketId("M001"),
            commodity=sample_commodity,
            price=Price(Decimal("500"), Currency.YER, "kg"),  # Way above normal
            observed_date=datetime(2023, 1, 21),
            source="WFP"
        )
        
        is_valid = validation_service.validate_price_observation(
            outlier_obs,
            historical_prices,
            sample_commodity
        )
        
        assert not is_valid
    
    def test_detect_price_anomalies(self, validation_service, sample_commodity):
        """Test detecting multiple price anomalies."""
        market = Market(
            market_id=MarketId("M001"),
            name="Test Market",
            coordinates=Coordinates(15.3694, 44.1910),
            market_type=MarketType.RETAIL,
            governorate="Sana'a",
            district="Old City",
            active_since=datetime(2020, 1, 1)
        )
        
        # Create observations with some anomalies
        observations = []
        for i in range(1, 41):  # 40 days
            if i in [15, 25, 35]:  # Anomaly days
                price_amount = 300  # Outlier
            else:
                price_amount = 100 + np.random.normal(0, 5)
            
            obs = PriceObservation(
                market_id=MarketId("M001"),
                commodity=sample_commodity,
                price=Price(Decimal(str(price_amount)), Currency.YER, "kg"),
                observed_date=datetime(2023, 1, i),
                source="WFP"
            )
            observations.append(obs)
        
        anomalies = validation_service.detect_price_anomalies(
            market,
            observations,
            window_days=30
        )
        
        # Should detect the anomalies (except first one due to insufficient history)
        assert len(anomalies) >= 2


class TestCurrencyConversionService:
    """Test CurrencyConversionService."""
    
    @pytest.fixture
    def conversion_service(self):
        """Create conversion service instance."""
        return CurrencyConversionService()
    
    @pytest.fixture
    def sample_commodity(self):
        """Create sample commodity."""
        return Commodity("WHT001", "Wheat", "imported", "kg")
    
    @pytest.fixture
    def exchange_rates(self):
        """Create sample exchange rate observations."""
        rates = []
        
        # YER to USD rates for different dates
        for i in range(1, 11):
            rate = ExchangeRate(
                from_currency=Currency.YER,
                to_currency=Currency.USD,
                rate=Decimal("500") + Decimal(str(i * 5)),  # Increasing rate
                rate_type="parallel"
            )
            
            obs = ExchangeRateObservation(
                market_id=MarketId("M001"),
                exchange_rate=rate,
                observed_date=datetime(2023, 1, i * 3),
                source="Market"
            )
            rates.append(obs)
        
        return rates
    
    def test_convert_prices_to_usd(self, conversion_service, sample_commodity, exchange_rates):
        """Test converting prices to USD."""
        # Create YER price observations
        yer_observations = [
            PriceObservation(
                market_id=MarketId("M001"),
                commodity=sample_commodity,
                price=Price(Decimal("50000"), Currency.YER, "kg"),
                observed_date=datetime(2023, 1, 5),
                source="WFP"
            ),
            PriceObservation(
                market_id=MarketId("M001"),
                commodity=sample_commodity,
                price=Price(Decimal("100"), Currency.USD, "kg"),  # Already USD
                observed_date=datetime(2023, 1, 10),
                source="WFP"
            )
        ]
        
        converted = conversion_service.convert_prices_to_common_currency(
            yer_observations,
            exchange_rates,
            Currency.USD
        )
        
        assert len(converted) == 2
        
        # First observation should be converted
        assert converted[0].price.currency == Currency.USD
        assert converted[0].price.amount < Decimal("200")  # Rough check
        assert "(converted)" in converted[0].source
        
        # Second observation should remain unchanged
        assert converted[1].price.currency == Currency.USD
        assert converted[1].price.amount == Decimal("100")
        assert "(converted)" not in converted[1].source
    
    def test_no_exchange_rate_available(self, conversion_service, sample_commodity):
        """Test handling missing exchange rates."""
        # No exchange rates for this market
        observations = [
            PriceObservation(
                market_id=MarketId("M999"),
                commodity=sample_commodity,
                price=Price(Decimal("50000"), Currency.YER, "kg"),
                observed_date=datetime(2023, 1, 5),
                source="WFP"
            )
        ]
        
        converted = conversion_service.convert_prices_to_common_currency(
            observations,
            [],  # No exchange rates
            Currency.USD
        )
        
        # Should skip the observation
        assert len(converted) == 0


class TestConflictImpactService:
    """Test ConflictImpactService."""
    
    @pytest.fixture
    def impact_service(self):
        """Create impact service instance."""
        return ConflictImpactService()
    
    @pytest.fixture
    def sample_market(self):
        """Create sample market."""
        return Market(
            market_id=MarketId("M001"),
            name="Test Market",
            coordinates=Coordinates(15.3694, 44.1910),
            market_type=MarketType.RETAIL,
            governorate="Sana'a",
            district="Old City",
            active_since=datetime(2020, 1, 1),
            control_status=ControlStatus.GOVERNMENT
        )
    
    def test_assess_market_accessibility(self, impact_service, sample_market):
        """Test assessing market accessibility based on control changes."""
        # Control changes over time
        control_changes = [
            (datetime(2023, 1, 1), ControlStatus.GOVERNMENT),
            (datetime(2023, 3, 1), ControlStatus.CONTESTED),  # 2 months contested
            (datetime(2023, 5, 1), ControlStatus.HOUTHI),      # Back to controlled
        ]
        
        accessibility = impact_service.assess_market_accessibility(
            sample_market,
            control_changes
        )
        
        # Should reflect time-weighted accessibility
        assert 0 < accessibility < 1
        assert accessibility > 0.7  # Mostly accessible
    
    def test_identify_trade_disruptions(self, impact_service):
        """Test identifying trade disruptions between markets."""
        source_market = Market(
            market_id=MarketId("M001"),
            name="Source Market",
            coordinates=Coordinates(15.3694, 44.1910),
            market_type=MarketType.WHOLESALE,
            governorate="Sana'a",
            district="Old City",
            active_since=datetime(2020, 1, 1),
            control_status=ControlStatus.HOUTHI
        )
        
        target_market = Market(
            market_id=MarketId("M002"),
            name="Target Market",
            coordinates=Coordinates(12.7855, 45.0187),
            market_type=MarketType.RETAIL,
            governorate="Aden",
            district="Port",
            active_since=datetime(2020, 1, 1),
            control_status=ControlStatus.GOVERNMENT
        )
        
        control_changes = [
            (datetime(2023, 1, 15), MarketId("M001"), ControlStatus.CONTESTED),
            (datetime(2023, 2, 1), MarketId("M002"), ControlStatus.CONTESTED),
        ]
        
        disruptions = impact_service.identify_trade_disruptions(
            source_market,
            target_market,
            control_changes
        )
        
        # Should identify multiple disruptions
        assert len(disruptions) >= 3  # Two control changes + cross-line trade
        
        # Check for cross-line trade disruption
        cross_line_disruptions = [d for d in disruptions if "Cross-line" in d[1]]
        assert len(cross_line_disruptions) > 0