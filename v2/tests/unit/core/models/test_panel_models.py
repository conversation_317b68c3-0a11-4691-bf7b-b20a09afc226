"""Unit tests for panel models."""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime

from src.core.models.panel import PooledPanelModel, FixedEffectsModel
from src.core.models.interfaces import ModelSpecification


@pytest.fixture
def sample_panel_data():
    """Create sample panel data for testing."""
    np.random.seed(42)
    n_entities = 5
    n_periods = 10
    
    data = []
    for entity in range(n_entities):
        for period in range(n_periods):
            data.append({
                'entity': f'market_{entity}',
                'time': period,
                'price': 100 + entity * 10 + period * 2 + np.random.normal(0, 5),
                'quantity': 50 + entity * 5 + period + np.random.normal(0, 3),
                'conflict': np.random.binomial(1, 0.3),
                'rainfall': 20 + np.random.normal(0, 5)
            })
    
    return pd.DataFrame(data)


@pytest.fixture
def model_specification():
    """Create model specification."""
    return ModelSpecification(
        dependent_variable='price',
        independent_variables=['quantity', 'conflict', 'rainfall'],
        fixed_effects=['entity'],
        time_effects=False,
        clustered_errors='entity'
    )


class TestPooledPanelModel:
    """Test pooled panel model."""
    
    def test_initialization(self, model_specification):
        """Test model initialization."""
        model = PooledPanelModel(specification=model_specification)
        
        assert model.specification == model_specification
        assert model.name == "Pooled Panel Model"
        assert not model.is_estimated()
    
    def test_validate_data(self, model_specification, sample_panel_data):
        """Test data validation."""
        model = PooledPanelModel(specification=model_specification)
        
        # Should not raise
        model.validate_data(sample_panel_data)
        
        # Missing dependent variable
        bad_data = sample_panel_data.drop(columns=['price'])
        with pytest.raises(ValueError, match="Missing dependent variable"):
            model.validate_data(bad_data)
        
        # Missing independent variable
        bad_data = sample_panel_data.drop(columns=['quantity'])
        with pytest.raises(ValueError, match="Missing independent variables"):
            model.validate_data(bad_data)
    
    def test_get_model_info(self, model_specification):
        """Test model info retrieval."""
        model = PooledPanelModel(specification=model_specification)
        info = model.get_model_info()
        
        assert info['name'] == "Pooled Panel Model"
        assert info['type'] == "panel"
        assert info['specification'] == model_specification
        assert 'description' in info


class TestFixedEffectsModel:
    """Test fixed effects model."""
    
    def test_initialization(self, model_specification):
        """Test model initialization."""
        model = FixedEffectsModel(
            specification=model_specification,
            entity_effects=True,
            time_effects=False
        )
        
        assert model.entity_effects is True
        assert model.time_effects is False
        assert model.name == "Fixed Effects Model"
    
    def test_two_way_fixed_effects(self, model_specification):
        """Test two-way fixed effects."""
        spec_with_time = ModelSpecification(
            dependent_variable='price',
            independent_variables=['quantity', 'conflict'],
            fixed_effects=['entity'],
            time_effects=True,
            clustered_errors='entity'
        )
        
        model = FixedEffectsModel(
            specification=spec_with_time,
            entity_effects=True,
            time_effects=True
        )
        
        assert model.entity_effects is True
        assert model.time_effects is True
        assert "Two-Way" in model.name
    
    def test_validate_panel_structure(self, model_specification, sample_panel_data):
        """Test panel structure validation."""
        model = FixedEffectsModel(specification=model_specification)
        
        # Add panel index
        panel_data = sample_panel_data.set_index(['entity', 'time'])
        
        # Should not raise
        model.validate_data(panel_data)
        
        # Test with unbalanced panel
        unbalanced = panel_data.iloc[:-5]  # Remove some observations
        # Should still work but might warn
        model.validate_data(unbalanced)


@pytest.mark.parametrize("model_class", [PooledPanelModel, FixedEffectsModel])
def test_model_interface_compliance(model_class, model_specification):
    """Test that models comply with interface."""
    model = model_class(specification=model_specification)
    
    # Check required methods exist
    assert hasattr(model, 'validate_data')
    assert hasattr(model, 'get_model_info')
    assert hasattr(model, 'is_estimated')
    assert hasattr(model, 'specification')