"""Tests for Analysis Orchestrator Service."""

import pytest
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4
from datetime import datetime

from src.application.services.analysis_orchestrator import AnalysisOrchestrator
from src.core.domain.market.entities import Market, PriceObservation
from src.core.domain.market.value_objects import MarketId, Commodity, Price
from src.core.domain.shared.exceptions import DomainError, ValidationError


@pytest.fixture
def mock_dependencies():
    """Create mock dependencies for analysis orchestrator."""
    return {
        'data_prep_service': AsyncMock(),
        'model_estimator_service': AsyncMock(),
        'three_tier_service': AsyncMock(),
        'event_bus': AsyncMock(),
        'logger': MagicMock(),
        'uow': AsyncMock()
    }


@pytest.fixture
def analysis_orchestrator(mock_dependencies):
    """Create analysis orchestrator with mocked dependencies."""
    return AnalysisOrchestrator(
        data_preparation_service=mock_dependencies['data_prep_service'],
        model_estimator_service=mock_dependencies['model_estimator_service'],
        three_tier_service=mock_dependencies['three_tier_service'],
        event_bus=mock_dependencies['event_bus'],
        logger=mock_dependencies['logger']
    )


class TestAnalysisOrchestrator:
    """Test cases for Analysis Orchestrator."""
    
    async def test_run_full_analysis_success(self, analysis_orchestrator, mock_dependencies):
        """Test successful full analysis execution."""
        # Arrange
        analysis_id = str(uuid4())
        mock_dependencies['data_prep_service'].prepare_analysis_data.return_value = {
            'panel_data': MagicMock(),
            'metadata': {'markets': 10, 'commodities': 5}
        }
        mock_dependencies['three_tier_service'].run_analysis.return_value = {
            'tier1_results': {'coefficient': 0.5, 'p_value': 0.01},
            'tier2_results': {'cointegration': True},
            'tier3_results': {'validation_score': 0.85}
        }
        
        # Act
        result = await analysis_orchestrator.run_full_analysis(
            analysis_id=analysis_id,
            markets=['market1', 'market2'],
            commodities=['wheat', 'rice'],
            start_date=datetime(2020, 1, 1),
            end_date=datetime(2023, 12, 31)
        )
        
        # Assert
        assert result['analysis_id'] == analysis_id
        assert result['status'] == 'completed'
        assert 'tier1_results' in result
        assert 'tier2_results' in result
        assert 'tier3_results' in result
        
        mock_dependencies['data_prep_service'].prepare_analysis_data.assert_called_once()
        mock_dependencies['three_tier_service'].run_analysis.assert_called_once()
        mock_dependencies['event_bus'].publish.assert_called()
    
    async def test_run_analysis_with_data_preparation_failure(self, analysis_orchestrator, mock_dependencies):
        """Test analysis with data preparation failure."""
        # Arrange
        analysis_id = str(uuid4())
        mock_dependencies['data_prep_service'].prepare_analysis_data.side_effect = ValidationError("Invalid data")
        
        # Act & Assert
        with pytest.raises(ValidationError):
            await analysis_orchestrator.run_full_analysis(
                analysis_id=analysis_id,
                markets=['market1'],
                commodities=['wheat'],
                start_date=datetime(2020, 1, 1),
                end_date=datetime(2023, 12, 31)
            )
        
        mock_dependencies['event_bus'].publish.assert_called()  # Error event should be published
    
    async def test_run_tier_specific_analysis(self, analysis_orchestrator, mock_dependencies):
        """Test running specific tier analysis."""
        # Arrange
        analysis_id = str(uuid4())
        mock_dependencies['three_tier_service'].run_tier1_only.return_value = {
            'tier1_results': {'coefficient': 0.3, 'p_value': 0.05}
        }
        
        # Act
        result = await analysis_orchestrator.run_tier_analysis(
            analysis_id=analysis_id,
            tier=1,
            data={'panel_data': MagicMock()}
        )
        
        # Assert
        assert result['analysis_id'] == analysis_id
        assert 'tier1_results' in result
        mock_dependencies['three_tier_service'].run_tier1_only.assert_called_once()
    
    async def test_analysis_status_tracking(self, analysis_orchestrator, mock_dependencies):
        """Test analysis status tracking throughout execution."""
        # Arrange
        analysis_id = str(uuid4())
        
        # Setup side effects to simulate long-running process
        async def slow_preparation(*args, **kwargs):
            await analysis_orchestrator._update_analysis_status(analysis_id, 'preparing_data')
            return {'panel_data': MagicMock(), 'metadata': {}}
        
        async def slow_analysis(*args, **kwargs):
            await analysis_orchestrator._update_analysis_status(analysis_id, 'running_models')
            return {'tier1_results': {}, 'tier2_results': {}, 'tier3_results': {}}
        
        mock_dependencies['data_prep_service'].prepare_analysis_data.side_effect = slow_preparation
        mock_dependencies['three_tier_service'].run_analysis.side_effect = slow_analysis
        
        # Act
        result = await analysis_orchestrator.run_full_analysis(
            analysis_id=analysis_id,
            markets=['market1'],
            commodities=['wheat'],
            start_date=datetime(2020, 1, 1),
            end_date=datetime(2023, 12, 31)
        )
        
        # Assert
        assert result['status'] == 'completed'
        # Verify status updates were called
        assert mock_dependencies['event_bus'].publish.call_count >= 3  # Started, preparing, running, completed
    
    async def test_concurrent_analysis_handling(self, analysis_orchestrator, mock_dependencies):
        """Test handling multiple concurrent analyses."""
        # Arrange
        analysis_id_1 = str(uuid4())
        analysis_id_2 = str(uuid4())
        
        mock_dependencies['data_prep_service'].prepare_analysis_data.return_value = {
            'panel_data': MagicMock(), 'metadata': {}
        }
        mock_dependencies['three_tier_service'].run_analysis.return_value = {
            'tier1_results': {}, 'tier2_results': {}, 'tier3_results': {}
        }
        
        # Act - Start both analyses concurrently
        import asyncio
        results = await asyncio.gather(
            analysis_orchestrator.run_full_analysis(
                analysis_id=analysis_id_1,
                markets=['market1'],
                commodities=['wheat'],
                start_date=datetime(2020, 1, 1),
                end_date=datetime(2023, 12, 31)
            ),
            analysis_orchestrator.run_full_analysis(
                analysis_id=analysis_id_2,
                markets=['market2'],
                commodities=['rice'],
                start_date=datetime(2020, 1, 1),
                end_date=datetime(2023, 12, 31)
            )
        )
        
        # Assert
        assert len(results) == 2
        assert results[0]['analysis_id'] == analysis_id_1
        assert results[1]['analysis_id'] == analysis_id_2
        assert all(r['status'] == 'completed' for r in results)
    
    async def test_analysis_validation_errors(self, analysis_orchestrator):
        """Test validation of analysis parameters."""
        # Test invalid date range
        with pytest.raises(ValidationError):
            await analysis_orchestrator.run_full_analysis(
                analysis_id=str(uuid4()),
                markets=['market1'],
                commodities=['wheat'],
                start_date=datetime(2023, 12, 31),
                end_date=datetime(2020, 1, 1)  # End before start
            )
        
        # Test empty markets
        with pytest.raises(ValidationError):
            await analysis_orchestrator.run_full_analysis(
                analysis_id=str(uuid4()),
                markets=[],
                commodities=['wheat'],
                start_date=datetime(2020, 1, 1),
                end_date=datetime(2023, 12, 31)
            )
        
        # Test empty commodities
        with pytest.raises(ValidationError):
            await analysis_orchestrator.run_full_analysis(
                analysis_id=str(uuid4()),
                markets=['market1'],
                commodities=[],
                start_date=datetime(2020, 1, 1),
                end_date=datetime(2023, 12, 31)
            )
    
    async def test_analysis_cleanup_on_failure(self, analysis_orchestrator, mock_dependencies):
        """Test proper cleanup when analysis fails."""
        # Arrange
        analysis_id = str(uuid4())
        mock_dependencies['data_prep_service'].prepare_analysis_data.return_value = {
            'panel_data': MagicMock(), 'metadata': {}
        }
        mock_dependencies['three_tier_service'].run_analysis.side_effect = RuntimeError("Model failed")
        
        # Act & Assert
        with pytest.raises(RuntimeError):
            await analysis_orchestrator.run_full_analysis(
                analysis_id=analysis_id,
                markets=['market1'],
                commodities=['wheat'],
                start_date=datetime(2020, 1, 1),
                end_date=datetime(2023, 12, 31)
            )
        
        # Verify cleanup was called
        mock_dependencies['event_bus'].publish.assert_called()  # Error event should be published
    
    async def test_get_analysis_status(self, analysis_orchestrator, mock_dependencies):
        """Test retrieving analysis status."""
        # Arrange
        analysis_id = str(uuid4())
        mock_dependencies['uow'].return_value.__aenter__.return_value.analysis_status.get.return_value = {
            'id': analysis_id,
            'status': 'running_models',
            'progress': 50,
            'started_at': datetime.now(),
            'estimated_completion': datetime.now()
        }
        
        # Act
        status = await analysis_orchestrator.get_analysis_status(analysis_id)
        
        # Assert
        assert status['id'] == analysis_id
        assert status['status'] == 'running_models'
        assert status['progress'] == 50
    
    async def test_cancel_analysis(self, analysis_orchestrator, mock_dependencies):
        """Test cancelling a running analysis."""
        # Arrange
        analysis_id = str(uuid4())
        
        # Act
        result = await analysis_orchestrator.cancel_analysis(analysis_id)
        
        # Assert
        assert result['analysis_id'] == analysis_id
        assert result['status'] == 'cancelled'
        mock_dependencies['event_bus'].publish.assert_called()  # Cancellation event should be published