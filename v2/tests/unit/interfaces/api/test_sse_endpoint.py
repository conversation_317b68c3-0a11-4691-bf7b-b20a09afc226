"""
Unit tests for Server-Sent Events (SSE) analysis status endpoint.
"""

import asyncio
import json
from datetime import datetime
from typing import AsyncGenerator
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient
from httpx import AsyncClient

from v2.src.interfaces.api.rest.routes.analysis import router
from v2.src.core.domain.shared.events import DomainEvent
from v2.src.infrastructure.messaging.event_bus import AsyncEventBus


@pytest.fixture
def mock_orchestrator():
    """Create a mock analysis orchestrator."""
    orchestrator = AsyncMock()
    orchestrator.get_analysis_status = AsyncMock(return_value={
        "id": "test-analysis-123",
        "status": "running",
        "progress": 25,
        "start_time": datetime.utcnow(),
        "tiers_progress": {
            "tier1": {"status": "completed", "progress": 100},
            "tier2": {"status": "running", "progress": 50},
            "tier3": {"status": "pending", "progress": 0}
        }
    })
    return orchestrator


@pytest.fixture
def mock_event_bus():
    """Create a mock event bus."""
    event_bus = MagicMock(spec=AsyncEventBus)
    event_bus.subscribe = MagicMock()
    return event_bus


@pytest.fixture
def app(mock_orchestrator, mock_event_bus):
    """Create FastAPI app with mocked dependencies."""
    app = FastAPI()
    app.include_router(router, prefix="/api/v2")
    
    # Override dependencies
    from v2.src.shared.container import Container
    Container.analysis_orchestrator = lambda: mock_orchestrator
    Container.event_bus = lambda: mock_event_bus
    
    return app


class TestSSEEndpoint:
    """Test suite for SSE endpoint functionality."""
    
    @pytest.mark.asyncio
    async def test_sse_endpoint_initial_response(self, app, mock_orchestrator):
        """Test SSE endpoint sends initial status."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            async with client.stream("GET", "/api/v2/analyses/test-analysis-123/status") as response:
                assert response.status_code == 200
                assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
                
                # Read first event (initial status)
                data = []
                async for line in response.aiter_lines():
                    data.append(line)
                    if line == "":  # Empty line marks end of event
                        break
                
                # Parse the event data
                event_data = "".join(data).strip()
                assert "data: {" in event_data
                assert '"event": "initial"' in event_data
                assert '"analysis_id": "test-analysis-123"' in event_data
                assert '"status": "running"' in event_data
                assert '"progress": 25' in event_data
    
    @pytest.mark.asyncio
    async def test_sse_endpoint_not_found(self, app, mock_orchestrator):
        """Test SSE endpoint returns 404 for non-existent analysis."""
        mock_orchestrator.get_analysis_status.return_value = None
        
        async with AsyncClient(app=app, base_url="http://test") as client:
            response = await client.get("/api/v2/analyses/non-existent/status")
            assert response.status_code == 404
            assert "not found" in response.json()["detail"].lower()
    
    def test_sse_event_subscriptions(self, app, mock_event_bus):
        """Test that SSE endpoint subscribes to correct events."""
        with TestClient(app) as client:
            # Start streaming (will timeout, but that's OK for this test)
            with client.stream("GET", "/api/v2/analyses/test-analysis-123/status") as response:
                # Check that subscribe was called for all expected event types
                expected_events = [
                    "analysis.progress.test-analysis-123",
                    "analysis.status.test-analysis-123",
                    "analysis.completed.test-analysis-123",
                    "analysis.failed.test-analysis-123"
                ]
                
                # Verify all subscriptions were made
                assert mock_event_bus.subscribe.call_count >= len(expected_events)
                
                # Check specific event subscriptions
                called_events = [call[0][0] for call in mock_event_bus.subscribe.call_args_list]
                for event in expected_events:
                    assert event in called_events
    
    @pytest.mark.asyncio
    async def test_sse_heartbeat(self, app):
        """Test SSE endpoint sends heartbeats to keep connection alive."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Use a very short timeout to trigger heartbeat
            start_time = datetime.utcnow()
            heartbeat_received = False
            
            async with client.stream("GET", "/api/v2/analyses/test-analysis-123/status") as response:
                async for line in response.aiter_lines():
                    if line.startswith(": heartbeat"):
                        heartbeat_received = True
                        break
                    
                    # Stop after reasonable time
                    if (datetime.utcnow() - start_time).seconds > 35:
                        break
            
            # Note: Heartbeat timing depends on implementation
            # This test might need adjustment based on actual timeout values
    
    @pytest.mark.asyncio
    async def test_sse_event_formatting(self):
        """Test SSE event formatting for different event types."""
        # Create test events
        progress_event = DomainEvent(
            event_name="analysis.progress.test-123",
            occurred_at=datetime.utcnow()
        )
        progress_event.analysis_id = "test-123"
        progress_event.progress = 75
        progress_event.tier = "tier2"
        progress_event.message = "Processing commodity: Wheat"
        
        # Test formatting logic (extracted from endpoint)
        formatted_lines = []
        formatted_lines.append('data: {')
        formatted_lines.append(f'  "event": "{progress_event.event_name}",')
        formatted_lines.append(f'  "analysis_id": "{progress_event.analysis_id}",')
        formatted_lines.append(f'  "progress": {progress_event.progress},')
        formatted_lines.append(f'  "tier": "{progress_event.tier}",')
        formatted_lines.append(f'  "message": "{progress_event.message}",')
        formatted_lines.append(f'  "timestamp": "{progress_event.occurred_at.isoformat()}"')
        formatted_lines.append('}')
        
        event_str = "\n".join(formatted_lines)
        
        # Verify JSON is valid
        json_str = event_str.replace("data: ", "")
        parsed = json.loads(json_str)
        assert parsed["event"] == "analysis.progress.test-123"
        assert parsed["progress"] == 75
        assert parsed["tier"] == "tier2"
    
    @pytest.mark.asyncio
    async def test_sse_connection_cleanup(self, app, mock_event_bus):
        """Test SSE endpoint cleans up on disconnect."""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # Start and quickly cancel the stream
            async with client.stream("GET", "/api/v2/analyses/test-analysis-123/status") as response:
                # Read initial event
                async for line in response.aiter_lines():
                    if line == "":
                        break
                
                # Cancel the stream
                response.close()
        
        # In a real implementation, we'd verify unsubscribe was called
        # For now, we just ensure no exceptions were raised
    
    @pytest.mark.asyncio
    async def test_sse_complete_on_final_events(self):
        """Test SSE stream completes when analysis finishes."""
        # Create a custom event bus that can send events
        event_queue = asyncio.Queue()
        
        async def mock_event_generator():
            # Send initial data
            yield 'data: {"event": "initial", "status": "running"}\n\n'
            
            # Wait for and send queued events
            while True:
                try:
                    event = await asyncio.wait_for(event_queue.get(), timeout=0.1)
                    if event == "complete":
                        yield 'data: {"event": "analysis.completed", "status": "completed"}\n\n'
                        break
                except asyncio.TimeoutError:
                    continue
        
        # Test that stream ends after completed event
        event_count = 0
        async for event in mock_event_generator():
            event_count += 1
            if event_count == 1:
                # After initial event, queue completion
                await event_queue.put("complete")
        
        assert event_count == 2  # Initial + completed