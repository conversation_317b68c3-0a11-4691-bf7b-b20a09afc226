#!/usr/bin/env python3
"""Simple test runner for domain models."""

import sys
import traceback
from decimal import Decimal
from datetime import datetime

# Add the v2 source to Python path
sys.path.insert(0, '/Users/<USER>/Documents/GitHub/yemen-market-integration/v2')

def test_currency_enum():
    """Test Currency enum functionality."""
    try:
        from src.core.domain.market.value_objects import Currency
        
        # Test enum values
        assert Currency.YER.value == "YER"
        assert Currency.USD.value == "USD"
        assert Currency.SAR.value == "SAR"
        
        # Test symbols
        assert Currency.YER.symbol == "﷼"
        assert Currency.USD.symbol == "$"
        assert Currency.SAR.symbol == "ر.س"
        
        print("✓ Currency enum tests passed")
        return True
    except Exception as e:
        print(f"✗ Currency enum tests failed: {e}")
        traceback.print_exc()
        return False

def test_price_value_object():
    """Test Price value object functionality."""
    try:
        from src.core.domain.market.value_objects import Price, Currency
        
        # Test valid price
        price = Price(
            amount=Decimal("100.50"),
            currency=Currency.YER,
            unit="kg"
        )
        assert price.amount == Decimal("100.50")
        assert price.currency == Currency.YER
        assert price.unit == "kg"
        
        # Test currency conversion
        price_usd = price.convert_to(Currency.USD, Decimal("500"))
        assert price_usd.currency == Currency.USD
        assert price_usd.amount == Decimal("0.201")  # 100.50 / 500
        
        print("✓ Price value object tests passed")
        return True
    except Exception as e:
        print(f"✗ Price value object tests failed: {e}")
        traceback.print_exc()
        return False

def test_market_entity():
    """Test Market entity functionality."""
    try:
        from src.core.domain.market.entities import Market
        from src.core.domain.market.value_objects import (
            MarketId, Coordinates, MarketType, ControlStatus
        )
        
        # Test market creation
        market = Market(
            market_id=MarketId("M001"),
            name="Sana'a Central Market",
            coordinates=Coordinates(15.3694, 44.1910),
            market_type=MarketType.WHOLESALE,
            governorate="Sana'a",
            district="Old City",
            active_since=datetime(2020, 1, 1)
        )
        
        assert market.market_id.value == "M001"
        assert market.name == "Sana'a Central Market"
        assert market.control_status == ControlStatus.UNKNOWN
        assert len(market.events) == 1  # MarketCreatedEvent
        
        # Test control status update
        market.update_control_status(ControlStatus.HOUTHI, datetime(2021, 6, 15))
        assert market.control_status == ControlStatus.HOUTHI
        assert len(market.events) == 2  # Added ControlStatusChangedEvent
        
        print("✓ Market entity tests passed")
        return True
    except Exception as e:
        print(f"✗ Market entity tests failed: {e}")
        traceback.print_exc()
        return False

def test_price_observation():
    """Test PriceObservation entity functionality."""
    try:
        from src.core.domain.market.entities import PriceObservation
        from src.core.domain.market.value_objects import (
            MarketId, Price, Currency, Commodity
        )
        
        commodity = Commodity("WHT001", "Wheat", "imported", "kg")
        price = Price(Decimal("100"), Currency.YER, "kg")
        
        observation = PriceObservation(
            market_id=MarketId("M001"),
            commodity=commodity,
            price=price,
            observed_date=datetime(2023, 6, 15),
            source="WFP"
        )
        
        assert observation.market_id.value == "M001"
        assert observation.commodity.name == "Wheat"
        assert observation.price.amount == Decimal("100")
        
        # Test outlier detection
        is_outlier = observation.is_outlier(Decimal("100"), Decimal("10"), 3.0)
        assert not is_outlier  # Should not be outlier (0 std devs away)
        
        is_outlier_extreme = observation.is_outlier(Decimal("50"), Decimal("10"), 3.0)
        assert is_outlier_extreme  # Should be outlier (5 std devs away)
        
        print("✓ PriceObservation entity tests passed")
        return True
    except Exception as e:
        print(f"✗ PriceObservation entity tests failed: {e}")
        traceback.print_exc()
        return False

def test_conflict_entities():
    """Test conflict domain entities."""
    try:
        from src.core.domain.conflict.entities import ConflictEvent
        from src.core.domain.conflict.value_objects import (
            ConflictType, ConflictIntensity
        )
        from src.core.domain.market.value_objects import Coordinates
        
        event = ConflictEvent(
            event_date=datetime(2023, 6, 15),
            location=Coordinates(15.3694, 44.1910),
            conflict_type=ConflictType.BATTLE,
            intensity=ConflictIntensity.MEDIUM,
            fatalities=15,
            actors=["Actor A", "Actor B"],
            description="Armed clash in city center",
            source="ACLED"
        )
        
        assert event.conflict_type == ConflictType.BATTLE
        assert event.intensity == ConflictIntensity.MEDIUM
        assert event.fatalities == 15
        assert event.impact_radius is not None
        
        # Test impact level calculation
        same_location = event.location
        assert event.get_impact_level(same_location) == "immediate"
        
        # Test distance calculation
        distant_point = Coordinates(12.7855, 45.0187)  # Aden
        distance = event.distance_to_point(distant_point)
        assert 290 < distance < 310  # ~300km
        
        print("✓ Conflict entity tests passed")
        return True
    except Exception as e:
        print(f"✗ Conflict entity tests failed: {e}")
        traceback.print_exc()
        return False

def test_validation_rules():
    """Test domain validation rules."""
    try:
        from src.core.domain.shared.validators import PriceValidator
        from decimal import Decimal
        
        # Test price outlier detection with varied historical prices
        historical_prices = [Decimal(str(95 + i)) for i in range(20)]  # 95-114
        
        # Normal price should pass
        is_valid = PriceValidator.validate_outlier(
            Decimal("105"), historical_prices, threshold=3.0
        )
        assert is_valid  # 105 is within the range
        
        # Extreme outlier should fail
        is_valid_outlier = PriceValidator.validate_outlier(
            Decimal("500"), historical_prices, threshold=3.0
        )
        assert not is_valid_outlier
        
        # Test price consistency
        is_consistent = PriceValidator.validate_price_consistency(
            Decimal("2.50"), "USD", "imported"
        )
        assert is_consistent
        
        print("✓ Validation rules tests passed")
        return True
    except Exception as e:
        print(f"✗ Validation rules tests failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all domain tests."""
    print("Running Yemen Market Integration V2 Domain Model Tests")
    print("=" * 60)
    
    tests = [
        test_currency_enum,
        test_price_value_object,
        test_market_entity,
        test_price_observation,
        test_conflict_entities,
        test_validation_rules,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        if test():
            passed += 1
        else:
            failed += 1
        print()
    
    print("=" * 60)
    print(f"Tests completed: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All domain model tests passed successfully!")
        return 0
    else:
        print(f"❌ {failed} test(s) failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())