"""Data source plugin interface."""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from datetime import datetime

from ...src.shared.plugins.interfaces import DataSourcePlugin


class MarketDataSourcePlugin(DataSourcePlugin):
    """Base class for market data source plugins."""
    
    @abstractmethod
    async def fetch_price_data(
        self,
        start_date: datetime,
        end_date: datetime,
        markets: Optional[List[str]] = None,
        commodities: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Fetch price data from the source.
        
        Args:
            start_date: Start date for data
            end_date: End date for data
            markets: Optional list of market IDs to filter
            commodities: Optional list of commodity codes to filter
            
        Returns:
            List of price observations
        """
        pass
    
    @abstractmethod
    async def fetch_market_metadata(self) -> List[Dict[str, Any]]:
        """Fetch market metadata (locations, types, etc.)."""
        pass
    
    def get_supported_data_types(self) -> List[str]:
        """Get supported data types."""
        return ['prices', 'markets', 'metadata']