"""World Bank data source plugin."""

from datetime import datetime
from typing import Dict, Any, List, Optional
import httpx

from ....__plugin_interface import MarketDataSourcePlugin
from ....src.shared.plugins.interfaces import PluginMetadata


class WorldBankDataPlugin(MarketDataSourcePlugin):
    """Plugin for fetching data from World Bank APIs."""
    
    @property
    def metadata(self) -> PluginMetadata:
        """Get plugin metadata."""
        return PluginMetadata(
            name="world_bank_data",
            version="1.0.0",
            author="Yemen Market Integration Team",
            description="Fetches market and price data from World Bank APIs",
            dependencies=["httpx>=0.25.0"],
            config_schema={
                "api_base_url": {
                    "type": "string",
                    "default": "https://api.worldbank.org/v2"
                },
                "timeout": {
                    "type": "integer",
                    "default": 30
                }
            }
        )
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize plugin with configuration."""
        self.base_url = config.get("api_base_url", "https://api.worldbank.org/v2")
        self.timeout = config.get("timeout", 30)
        self.client = httpx.AsyncClient(timeout=self.timeout)
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate plugin configuration."""
        if "api_base_url" in config:
            if not isinstance(config["api_base_url"], str):
                return False
        if "timeout" in config:
            if not isinstance(config["timeout"], int) or config["timeout"] <= 0:
                return False
        return True
    
    async def fetch_data(
        self,
        query: Dict[str, Any],
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Fetch data from World Bank API."""
        # Build query parameters
        params = {
            "format": "json",
            "per_page": 1000
        }
        
        if start_date:
            params["date"] = f"{start_date}:{end_date or start_date}"
        
        # Add custom query parameters
        params.update(query)
        
        # Make request
        response = await self.client.get(
            f"{self.base_url}/country/YEM/indicator/{query.get('indicator', '')}",
            params=params
        )
        
        if response.status_code != 200:
            raise Exception(f"API request failed: {response.status_code}")
        
        # Parse response
        data = response.json()
        if len(data) > 1 and isinstance(data[1], list):
            return data[1]
        
        return []
    
    async def fetch_price_data(
        self,
        start_date: datetime,
        end_date: datetime,
        markets: Optional[List[str]] = None,
        commodities: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Fetch price data from World Bank."""
        # World Bank uses different indicator codes for commodities
        indicator_map = {
            "WHEAT": "PWHEAMT",
            "RICE": "PRICENPQ", 
            "OIL": "POILWTI",
            "SUGAR": "PSUGAUSA"
        }
        
        all_data = []
        
        for commodity, indicator in indicator_map.items():
            if commodities and commodity not in commodities:
                continue
            
            data = await self.fetch_data(
                {"indicator": indicator},
                start_date.strftime("%Y"),
                end_date.strftime("%Y")
            )
            
            # Transform to standard format
            for item in data:
                if item.get("value") is not None:
                    all_data.append({
                        "date": f"{item['date']}-01-01",
                        "commodity": commodity,
                        "price": float(item["value"]),
                        "unit": "USD",
                        "source": "world_bank"
                    })
        
        return all_data
    
    async def fetch_market_metadata(self) -> List[Dict[str, Any]]:
        """Fetch market metadata."""
        # World Bank doesn't have specific market data for Yemen
        # Return empty list or fetch country-level data
        return []
    
    def validate_query(self, query: Dict[str, Any]) -> bool:
        """Validate query parameters."""
        required = ["indicator"]
        return all(key in query for key in required)
    
    def on_unload(self) -> None:
        """Cleanup when plugin is unloaded."""
        if hasattr(self, 'client'):
            # Close HTTP client
            import asyncio
            asyncio.create_task(self.client.aclose())