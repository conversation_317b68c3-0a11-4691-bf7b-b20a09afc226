"""Output plugin interface."""

from abc import abstractmethod
from typing import Dict, Any, Optional

from ...src.shared.plugins.interfaces import OutputPlugin


class ReportOutputPlugin(OutputPlugin):
    """Base class for report output plugins."""
    
    @abstractmethod
    def export_analysis_results(
        self,
        results: Dict[str, Any],
        output_path: str,
        options: Dict[str, Any] = None
    ) -> None:
        """Export analysis results to specified format.
        
        Args:
            results: Analysis results dictionary
            output_path: Path to save the output
            options: Format-specific options
        """
        pass
    
    @abstractmethod
    def supports_interactive(self) -> bool:
        """Check if format supports interactive features."""
        pass
    
    def validate_results(self, results: Dict[str, Any]) -> bool:
        """Validate results structure before export."""
        required_keys = ['tier1', 'tier2', 'tier3', 'metadata']
        return all(key in results for key in required_keys)