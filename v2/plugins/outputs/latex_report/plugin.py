"""LaTeX report output plugin."""

from typing import Dict, Any, Optional
import os
from datetime import datetime

from ....__plugin_interface import ReportOutputPlugin
from ....src.shared.plugins.interfaces import PluginMetadata


class LaTeXReportPlugin(ReportOutputPlugin):
    """Plugin for generating LaTeX reports from analysis results."""
    
    @property
    def metadata(self) -> PluginMetadata:
        """Get plugin metadata."""
        return PluginMetadata(
            name="latex_report",
            version="1.0.0",
            author="Yemen Market Integration Team",
            description="Generates professional LaTeX reports from analysis results",
            dependencies=[],
            config_schema={
                "template": {
                    "type": "string",
                    "default": "academic"
                },
                "include_code": {
                    "type": "boolean",
                    "default": False
                }
            }
        )
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize plugin with configuration."""
        self.template = config.get("template", "academic")
        self.include_code = config.get("include_code", False)
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate plugin configuration."""
        valid_templates = ["academic", "technical", "executive"]
        if "template" in config:
            if config["template"] not in valid_templates:
                return False
        return True
    
    def export(
        self,
        data: Any,
        output_path: str,
        options: Dict[str, Any] = None
    ) -> None:
        """Export data to LaTeX format."""
        options = options or {}
        
        # Generate LaTeX content
        latex_content = self._generate_latex(data, options)
        
        # Write to file
        with open(output_path, 'w') as f:
            f.write(latex_content)
        
        # Optionally compile to PDF
        if options.get("compile_pdf", False):
            self._compile_pdf(output_path)
    
    def export_analysis_results(
        self,
        results: Dict[str, Any],
        output_path: str,
        options: Dict[str, Any] = None
    ) -> None:
        """Export three-tier analysis results."""
        self.export(results, output_path, options)
    
    def get_file_extension(self) -> str:
        """Get file extension."""
        return ".tex"
    
    def get_mime_type(self) -> str:
        """Get MIME type."""
        return "application/x-latex"
    
    def supports_interactive(self) -> bool:
        """LaTeX doesn't support interactive features."""
        return False
    
    def _generate_latex(self, data: Dict[str, Any], options: Dict[str, Any]) -> str:
        """Generate LaTeX content from results."""
        # Start with document class and packages
        latex = self._get_preamble()
        
        # Add title and metadata
        latex += self._generate_title_page(data.get('metadata', {}))
        
        # Add abstract
        latex += self._generate_abstract(data)
        
        # Add main content
        latex += "\\section{Introduction}\n"
        latex += self._generate_introduction(data)
        
        # Tier 1 results
        latex += "\\section{Tier 1: Pooled Panel Analysis}\n"
        latex += self._generate_tier1_section(data.get('tier1', {}))
        
        # Tier 2 results
        latex += "\\section{Tier 2: Commodity-Specific Analysis}\n"
        latex += self._generate_tier2_section(data.get('tier2', {}))
        
        # Tier 3 results
        latex += "\\section{Tier 3: Validation and Robustness}\n"
        latex += self._generate_tier3_section(data.get('tier3', {}))
        
        # Conclusions
        latex += "\\section{Conclusions}\n"
        latex += self._generate_conclusions(data)
        
        # End document
        latex += "\\end{document}\n"
        
        return latex
    
    def _get_preamble(self) -> str:
        """Get LaTeX preamble."""
        return """\\documentclass[12pt]{article}
\\usepackage{amsmath}
\\usepackage{amssymb}
\\usepackage{graphicx}
\\usepackage{booktabs}
\\usepackage{float}
\\usepackage{hyperref}
\\usepackage{natbib}

\\begin{document}
"""
    
    def _generate_title_page(self, metadata: Dict[str, Any]) -> str:
        """Generate title page."""
        title = metadata.get('title', 'Yemen Market Integration Analysis')
        authors = metadata.get('authors', ['Yemen Market Integration Team'])
        date = metadata.get('date', datetime.now().strftime('%B %Y'))
        
        latex = f"\\title{{{title}}}\n"
        latex += f"\\author{{{' \\\\and '.join(authors)}}}\n"
        latex += f"\\date{{{date}}}\n"
        latex += "\\maketitle\n\n"
        
        return latex
    
    def _generate_abstract(self, data: Dict[str, Any]) -> str:
        """Generate abstract section."""
        return """\\begin{abstract}
This report presents a comprehensive three-tier econometric analysis of market integration 
in Yemen, examining price transmission mechanisms, commodity-specific dynamics, and 
validation through factor analysis and conflict assessment.
\\end{abstract}

"""
    
    def _generate_introduction(self, data: Dict[str, Any]) -> str:
        """Generate introduction."""
        return """This analysis employs a three-tier approach to understanding market integration:
\\begin{enumerate}
\\item Pooled panel models to establish baseline relationships
\\item Commodity-specific VECM models for detailed dynamics
\\item Validation through factor analysis and conflict assessment
\\end{enumerate}

"""
    
    def _generate_tier1_section(self, tier1_data: Dict[str, Any]) -> str:
        """Generate Tier 1 results section."""
        latex = ""
        
        # Add regression table
        if 'pooled_model' in tier1_data:
            latex += self._format_regression_table(
                tier1_data['pooled_model'],
                "Pooled Panel Regression Results"
            )
        
        return latex
    
    def _generate_tier2_section(self, tier2_data: Dict[str, Any]) -> str:
        """Generate Tier 2 results section."""
        latex = ""
        
        for commodity, results in tier2_data.items():
            latex += f"\\subsection{{{commodity}}}\n"
            
            if 'vecm' in results:
                latex += "VECM estimation results...\n"
        
        return latex
    
    def _generate_tier3_section(self, tier3_data: Dict[str, Any]) -> str:
        """Generate Tier 3 results section."""
        latex = ""
        
        if 'factor_analysis' in tier3_data:
            latex += "\\subsection{Factor Analysis}\n"
            latex += "Principal component analysis reveals...\n"
        
        return latex
    
    def _generate_conclusions(self, data: Dict[str, Any]) -> str:
        """Generate conclusions."""
        return """The analysis reveals significant market integration patterns across Yemen, 
with important variations by commodity and region. Policy implications include...

"""
    
    def _format_regression_table(self, model_results: Dict[str, Any], 
                                caption: str) -> str:
        """Format regression results as LaTeX table."""
        # Simplified table generation
        return f"""\\begin{{table}}[H]
\\centering
\\caption{{{caption}}}
\\begin{{tabular}}{{lcc}}
\\toprule
Variable & Coefficient & Std. Error \\\\
\\midrule
% Add rows here
\\bottomrule
\\end{{tabular}}
\\end{{table}}

"""
    
    def _compile_pdf(self, tex_path: str) -> None:
        """Compile LaTeX to PDF."""
        import subprocess
        
        # Run pdflatex
        tex_dir = os.path.dirname(tex_path)
        tex_file = os.path.basename(tex_path)
        
        subprocess.run(
            ['pdflatex', '-interaction=nonstopmode', tex_file],
            cwd=tex_dir,
            capture_output=True
        )