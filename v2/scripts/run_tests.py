#!/usr/bin/env python3
"""
Comprehensive test runner script for Yemen Market Integration V2.

This script provides a unified interface for running different types of tests
with appropriate configurations and reporting.
"""

import argparse
import asyncio
import json
import os
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import psutil


class TestRunner:
    """Comprehensive test runner for V2 system."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.test_dir = project_root / "tests"
        self.results = {}
        self.start_time = None
        
    def run_command(self, command: List[str], timeout: int = 300) -> Tuple[int, str, str]:
        """Run a command and capture output."""
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.project_root
            )
            return result.returncode, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return 1, "", f"Command timed out after {timeout} seconds"
        except Exception as e:
            return 1, "", str(e)
    
    def check_dependencies(self) -> bool:
        """Check if all required dependencies are available."""
        print("🔍 Checking dependencies...")
        
        # Check Python version
        if sys.version_info < (3, 11):
            print("❌ Python 3.11+ required")
            return False
        
        # Check Poetry
        returncode, _, _ = self.run_command(["poetry", "--version"])
        if returncode != 0:
            print("❌ Poetry not found. Please install Poetry.")
            return False
        
        # Check if virtual environment is activated or available
        returncode, _, _ = self.run_command(["poetry", "env", "info"])
        if returncode != 0:
            print("⚠️  Poetry environment not found. Installing dependencies...")
            returncode, _, stderr = self.run_command(["poetry", "install"])
            if returncode != 0:
                print(f"❌ Failed to install dependencies: {stderr}")
                return False
        
        print("✅ Dependencies OK")
        return True
    
    def run_code_quality_checks(self) -> Dict[str, bool]:
        """Run code quality checks."""
        print("\n🎯 Running code quality checks...")
        
        checks = {}
        
        # Linting with Ruff
        print("  Running Ruff linting...")
        returncode, stdout, stderr = self.run_command([
            "poetry", "run", "ruff", "check", "src/", "tests/", "--output-format=json"
        ])
        checks["ruff"] = returncode == 0
        if returncode != 0:
            print(f"    ❌ Ruff found issues: {stderr}")
        else:
            print("    ✅ Ruff linting passed")
        
        # Type checking with MyPy
        print("  Running MyPy type checking...")
        returncode, stdout, stderr = self.run_command([
            "poetry", "run", "mypy", "src/", "--show-error-codes"
        ])
        checks["mypy"] = returncode == 0
        if returncode != 0:
            print(f"    ❌ MyPy found type issues: {stderr}")
        else:
            print("    ✅ MyPy type checking passed")
        
        # Code formatting with Black
        print("  Checking code formatting...")
        returncode, stdout, stderr = self.run_command([
            "poetry", "run", "black", "--check", "src/", "tests/"
        ])
        checks["black"] = returncode == 0
        if returncode != 0:
            print("    ❌ Code formatting issues found")
        else:
            print("    ✅ Code formatting is correct")
        
        # Import sorting with isort
        print("  Checking import sorting...")
        returncode, stdout, stderr = self.run_command([
            "poetry", "run", "isort", "--check-only", "src/", "tests/"
        ])
        checks["isort"] = returncode == 0
        if returncode != 0:
            print("    ❌ Import sorting issues found")
        else:
            print("    ✅ Import sorting is correct")
        
        return checks
    
    def run_unit_tests(self, verbose: bool = False, coverage: bool = True) -> Dict[str, any]:
        """Run unit tests."""
        print("\n🧪 Running unit tests...")
        
        command = ["poetry", "run", "pytest", "tests/unit/"]
        
        if coverage:
            command.extend([
                "--cov=src",
                "--cov-report=term-missing",
                "--cov-report=xml:coverage.xml",
                "--cov-report=html:htmlcov"
            ])
        
        if verbose:
            command.append("-v")
        
        command.extend([
            "--junit-xml=junit-unit.xml",
            "--tb=short"
        ])
        
        start_time = time.time()
        returncode, stdout, stderr = self.run_command(command, timeout=600)
        duration = time.time() - start_time
        
        # Parse test results
        success = returncode == 0
        
        # Try to extract test count from output
        test_count = self._extract_test_count(stdout)
        
        result = {
            "success": success,
            "duration": duration,
            "test_count": test_count,
            "output": stdout,
            "errors": stderr
        }
        
        if success:
            print(f"    ✅ Unit tests passed ({test_count} tests in {duration:.2f}s)")
        else:
            print(f"    ❌ Unit tests failed: {stderr}")
        
        return result
    
    def run_integration_tests(self, verbose: bool = False) -> Dict[str, any]:
        """Run integration tests."""
        print("\n🔗 Running integration tests...")
        
        # Check if required services are running
        if not self._check_services():
            print("    ⚠️  Required services not available, skipping integration tests")
            return {"success": False, "skipped": True, "reason": "Services not available"}
        
        command = [
            "poetry", "run", "pytest", "tests/integration/",
            "--junit-xml=junit-integration.xml",
            "--tb=short"
        ]
        
        if verbose:
            command.append("-v")
        
        start_time = time.time()
        returncode, stdout, stderr = self.run_command(command, timeout=900)
        duration = time.time() - start_time
        
        success = returncode == 0
        test_count = self._extract_test_count(stdout)
        
        result = {
            "success": success,
            "duration": duration,
            "test_count": test_count,
            "output": stdout,
            "errors": stderr
        }
        
        if success:
            print(f"    ✅ Integration tests passed ({test_count} tests in {duration:.2f}s)")
        else:
            print(f"    ❌ Integration tests failed: {stderr}")
        
        return result
    
    def run_e2e_tests(self, verbose: bool = False) -> Dict[str, any]:
        """Run end-to-end tests."""
        print("\n🎭 Running E2E tests...")
        
        command = [
            "poetry", "run", "pytest", "tests/e2e/",
            "--junit-xml=junit-e2e.xml",
            "--tb=short"
        ]
        
        if verbose:
            command.append("-v")
        
        start_time = time.time()
        returncode, stdout, stderr = self.run_command(command, timeout=1200)
        duration = time.time() - start_time
        
        success = returncode == 0
        test_count = self._extract_test_count(stdout)
        
        result = {
            "success": success,
            "duration": duration,
            "test_count": test_count,
            "output": stdout,
            "errors": stderr
        }
        
        if success:
            print(f"    ✅ E2E tests passed ({test_count} tests in {duration:.2f}s)")
        else:
            print(f"    ❌ E2E tests failed: {stderr}")
        
        return result
    
    def run_performance_tests(self, verbose: bool = False) -> Dict[str, any]:
        """Run performance tests."""
        print("\n⚡ Running performance tests...")
        
        command = [
            "poetry", "run", "pytest", "tests/performance/",
            "--junit-xml=junit-performance.xml",
            "--tb=short",
            "-m", "performance"
        ]
        
        if verbose:
            command.append("-v")
        
        start_time = time.time()
        returncode, stdout, stderr = self.run_command(command, timeout=1800)
        duration = time.time() - start_time
        
        success = returncode == 0
        test_count = self._extract_test_count(stdout)
        
        result = {
            "success": success,
            "duration": duration,
            "test_count": test_count,
            "output": stdout,
            "errors": stderr
        }
        
        if success:
            print(f"    ✅ Performance tests passed ({test_count} tests in {duration:.2f}s)")
        else:
            print(f"    ❌ Performance tests failed: {stderr}")
        
        return result
    
    def run_security_tests(self, verbose: bool = False) -> Dict[str, any]:
        """Run security tests."""
        print("\n🔒 Running security tests...")
        
        command = [
            "poetry", "run", "pytest", 
            "tests/unit/infrastructure/test_security_comprehensive.py",
            "--junit-xml=junit-security.xml",
            "--tb=short"
        ]
        
        if verbose:
            command.append("-v")
        
        start_time = time.time()
        returncode, stdout, stderr = self.run_command(command, timeout=600)
        duration = time.time() - start_time
        
        success = returncode == 0
        test_count = self._extract_test_count(stdout)
        
        result = {
            "success": success,
            "duration": duration,
            "test_count": test_count,
            "output": stdout,
            "errors": stderr
        }
        
        if success:
            print(f"    ✅ Security tests passed ({test_count} tests in {duration:.2f}s)")
        else:
            print(f"    ❌ Security tests failed: {stderr}")
        
        return result
    
    def generate_coverage_report(self) -> Dict[str, any]:
        """Generate comprehensive coverage report."""
        print("\n📊 Generating coverage report...")
        
        # Check if coverage.xml exists
        coverage_file = self.project_root / "coverage.xml"
        if not coverage_file.exists():
            print("    ⚠️  No coverage data found")
            return {"success": False, "reason": "No coverage data"}
        
        # Generate HTML report
        command = ["poetry", "run", "coverage", "html", "-d", "htmlcov"]
        returncode, stdout, stderr = self.run_command(command)
        
        if returncode == 0:
            print("    ✅ Coverage report generated in htmlcov/")
        
        # Get coverage percentage
        command = ["poetry", "run", "coverage", "report", "--show-missing"]
        returncode, stdout, stderr = self.run_command(command)
        
        coverage_percentage = self._extract_coverage_percentage(stdout)
        
        return {
            "success": returncode == 0,
            "coverage_percentage": coverage_percentage,
            "html_report": "htmlcov/index.html"
        }
    
    def _check_services(self) -> bool:
        """Check if required services (PostgreSQL, Redis) are available."""
        services_ok = True
        
        # Check PostgreSQL (simplified check)
        try:
            import psycopg2
            # This is a simplified check - in practice you'd try to connect
            print("    ✅ PostgreSQL driver available")
        except ImportError:
            print("    ⚠️  PostgreSQL driver not available")
            services_ok = False
        
        # Check Redis (simplified check)
        try:
            import redis
            print("    ✅ Redis driver available")
        except ImportError:
            print("    ⚠️  Redis driver not available")
            services_ok = False
        
        return services_ok
    
    def _extract_test_count(self, output: str) -> int:
        """Extract test count from pytest output."""
        lines = output.split('\n')
        for line in reversed(lines):
            if 'passed' in line and 'failed' in line:
                # Try to extract numbers from summary line
                import re
                numbers = re.findall(r'\d+', line)
                if numbers:
                    return sum(int(n) for n in numbers)
        return 0
    
    def _extract_coverage_percentage(self, output: str) -> float:
        """Extract coverage percentage from coverage report."""
        lines = output.split('\n')
        for line in reversed(lines):
            if 'TOTAL' in line:
                import re
                percentages = re.findall(r'(\d+)%', line)
                if percentages:
                    return float(percentages[-1])
        return 0.0
    
    def save_results(self, filename: str = "test_results.json"):
        """Save test results to JSON file."""
        results_file = self.project_root / filename
        
        summary = {
            "timestamp": datetime.now().isoformat(),
            "total_duration": time.time() - self.start_time if self.start_time else 0,
            "results": self.results,
            "system_info": {
                "python_version": sys.version,
                "platform": sys.platform,
                "cpu_count": psutil.cpu_count(),
                "memory_gb": psutil.virtual_memory().total / (1024**3)
            }
        }
        
        with open(results_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"\n📄 Test results saved to {filename}")
        return summary
    
    def print_summary(self):
        """Print test execution summary."""
        print("\n" + "="*60)
        print("📋 TEST EXECUTION SUMMARY")
        print("="*60)
        
        total_tests = 0
        total_duration = 0
        all_passed = True
        
        for test_type, result in self.results.items():
            if isinstance(result, dict) and 'success' in result:
                status = "✅ PASSED" if result['success'] else "❌ FAILED"
                duration = result.get('duration', 0)
                test_count = result.get('test_count', 0)
                
                print(f"{test_type.replace('_', ' ').title():<20} {status:<10} "
                      f"{test_count:>3} tests  {duration:>6.2f}s")
                
                total_tests += test_count
                total_duration += duration
                
                if not result['success']:
                    all_passed = False
        
        print("-" * 60)
        print(f"{'TOTAL':<20} {'✅ ALL PASSED' if all_passed else '❌ SOME FAILED':<10} "
              f"{total_tests:>3} tests  {total_duration:>6.2f}s")
        
        # Coverage summary
        if 'coverage' in self.results:
            coverage = self.results['coverage'].get('coverage_percentage', 0)
            coverage_status = "✅" if coverage >= 90 else "⚠️" if coverage >= 80 else "❌"
            print(f"\nCoverage: {coverage_status} {coverage:.1f}%")
        
        print("="*60)


def main():
    """Main entry point for test runner."""
    parser = argparse.ArgumentParser(description="Comprehensive test runner for Yemen Market Integration V2")
    
    parser.add_argument(
        "test_types",
        nargs="*",
        choices=["quality", "unit", "integration", "e2e", "performance", "security", "all"],
        default=["all"],
        help="Types of tests to run"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--no-coverage",
        action="store_true",
        help="Skip coverage reporting"
    )
    
    parser.add_argument(
        "--fail-fast",
        action="store_true",
        help="Stop on first failure"
    )
    
    parser.add_argument(
        "--output", "-o",
        default="test_results.json",
        help="Output file for test results"
    )
    
    args = parser.parse_args()
    
    # Determine which tests to run
    if "all" in args.test_types:
        test_types = ["quality", "unit", "integration", "e2e"]
    else:
        test_types = args.test_types
    
    # Initialize test runner
    project_root = Path(__file__).parent.parent
    runner = TestRunner(project_root)
    runner.start_time = time.time()
    
    print("🚀 Starting comprehensive test execution...")
    print(f"📁 Project root: {project_root}")
    print(f"🎯 Test types: {', '.join(test_types)}")
    
    # Check dependencies
    if not runner.check_dependencies():
        sys.exit(1)
    
    # Run selected tests
    try:
        if "quality" in test_types:
            runner.results["code_quality"] = runner.run_code_quality_checks()
            if args.fail_fast and not all(runner.results["code_quality"].values()):
                print("💥 Code quality checks failed, stopping execution")
                sys.exit(1)
        
        if "unit" in test_types:
            runner.results["unit_tests"] = runner.run_unit_tests(
                verbose=args.verbose,
                coverage=not args.no_coverage
            )
            if args.fail_fast and not runner.results["unit_tests"]["success"]:
                print("💥 Unit tests failed, stopping execution")
                sys.exit(1)
        
        if "integration" in test_types:
            runner.results["integration_tests"] = runner.run_integration_tests(verbose=args.verbose)
            if args.fail_fast and not runner.results["integration_tests"]["success"]:
                print("💥 Integration tests failed, stopping execution")
                sys.exit(1)
        
        if "e2e" in test_types:
            runner.results["e2e_tests"] = runner.run_e2e_tests(verbose=args.verbose)
            if args.fail_fast and not runner.results["e2e_tests"]["success"]:
                print("💥 E2E tests failed, stopping execution")
                sys.exit(1)
        
        if "performance" in test_types:
            runner.results["performance_tests"] = runner.run_performance_tests(verbose=args.verbose)
        
        if "security" in test_types:
            runner.results["security_tests"] = runner.run_security_tests(verbose=args.verbose)
        
        # Generate coverage report if coverage was collected
        if not args.no_coverage and any(t in test_types for t in ["unit", "integration"]):
            runner.results["coverage"] = runner.generate_coverage_report()
        
    except KeyboardInterrupt:
        print("\n⏹️  Test execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
    
    # Generate summary and save results
    runner.print_summary()
    summary = runner.save_results(args.output)
    
    # Exit with appropriate code
    all_tests_passed = all(
        result.get("success", True) if isinstance(result, dict) else True
        for result in runner.results.values()
    )
    
    sys.exit(0 if all_tests_passed else 1)


if __name__ == "__main__":
    main()