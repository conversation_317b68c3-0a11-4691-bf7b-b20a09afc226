# Service Level Objectives (SLOs) for Yemen Market Integration

# SLO 1: API Availability
# Target: 99% of requests should succeed (non-5xx) over a 30-day window
api_availability:
  name: "API Availability"
  description: "Percentage of successful API requests"
  target: 0.99
  window: "30d"
  error_budget_burn_rate_thresholds:
    - window: "1h"
      burn_rate: 14.4  # 14.4x burn rate over 1h
      severity: "critical"
    - window: "6h"
      burn_rate: 6     # 6x burn rate over 6h
      severity: "warning"
  query: |
    1 - (
      sum(rate(yemen_market_http_requests_total{status=~"5.."}[{{.window}}]))
      /
      sum(rate(yemen_market_http_requests_total[{{.window}}]))
    )

# SLO 2: Analysis Latency
# Target: 95% of analyses should complete within 300 seconds over a 7-day window
analysis_latency:
  name: "Analysis Latency"
  description: "Percentage of analyses completing within 300 seconds"
  target: 0.95
  threshold: 300  # seconds
  window: "7d"
  error_budget_burn_rate_thresholds:
    - window: "1h"
      burn_rate: 14.4
      severity: "critical"
    - window: "3h"
      burn_rate: 6
      severity: "warning"
  query: |
    histogram_quantile(0.95,
      sum(rate(yemen_market_analysis_duration_seconds_bucket[{{.window}}])) by (le)
    ) <= {{.threshold}}

# SLO 3: Data Quality
# Target: 95% average data coverage across all commodities and regions over 24h
data_quality:
  name: "Data Quality Coverage"
  description: "Average data coverage across commodities and regions"
  target: 0.95
  window: "24h"
  error_budget_burn_rate_thresholds:
    - window: "1h"
      burn_rate: 24
      severity: "critical"
    - window: "6h"
      burn_rate: 4
      severity: "warning"
  query: |
    avg(yemen_market_data_coverage_ratio)

# SLO 4: Model Convergence
# Target: 90% of models should converge successfully over a 24h window
model_convergence:
  name: "Model Convergence Rate"
  description: "Percentage of models converging successfully"
  target: 0.90
  window: "24h"
  error_budget_burn_rate_thresholds:
    - window: "1h"
      burn_rate: 24
      severity: "warning"
    - window: "3h"
      burn_rate: 8
      severity: "info"
  query: |
    sum(rate(yemen_market_model_convergence_total{converged="True"}[{{.window}}]))
    /
    sum(rate(yemen_market_model_convergence_total[{{.window}}]))

# SLO 5: External API Reliability
# Target: 95% of external API calls should succeed over a 24h window
external_api_reliability:
  name: "External API Reliability"
  description: "Success rate of external API calls"
  target: 0.95
  window: "24h"
  error_budget_burn_rate_thresholds:
    - window: "30m"
      burn_rate: 48
      severity: "critical"
    - window: "2h"
      burn_rate: 12
      severity: "warning"
  query: |
    sum(rate(yemen_market_external_api_calls_total{status="200"}[{{.window}}]))
    /
    sum(rate(yemen_market_external_api_calls_total[{{.window}}]))

# SLO 6: Data Freshness
# Target: 90% of data should be less than 48 hours old
data_freshness:
  name: "Data Freshness"
  description: "Percentage of data updated within 48 hours"
  target: 0.90
  threshold: 48  # hours
  window: "7d"
  error_budget_burn_rate_thresholds:
    - window: "6h"
      burn_rate: 28
      severity: "warning"
    - window: "24h"
      burn_rate: 7
      severity: "info"
  query: |
    count(yemen_market_data_freshness_hours <= {{.threshold}})
    /
    count(yemen_market_data_freshness_hours)

# SLO 7: Request Latency (P95)
# Target: 95th percentile latency should be under 2 seconds
request_latency_p95:
  name: "API Request Latency P95"
  description: "95th percentile API request latency"
  target: 2.0  # seconds
  window: "1h"
  error_budget_burn_rate_thresholds:
    - window: "5m"
      burn_rate: 12
      severity: "critical"
    - window: "30m"
      burn_rate: 2
      severity: "warning"
  query: |
    histogram_quantile(0.95,
      sum(rate(yemen_market_http_request_duration_seconds_bucket[{{.window}}])) by (le)
    )

# SLO 8: Database Performance
# Target: 99% of database queries should complete within 1 second
database_performance:
  name: "Database Query Performance"
  description: "Percentage of database queries completing within 1 second"
  target: 0.99
  threshold: 1.0  # seconds
  window: "1h"
  error_budget_burn_rate_thresholds:
    - window: "5m"
      burn_rate: 12
      severity: "critical"
    - window: "15m"
      burn_rate: 4
      severity: "warning"
  query: |
    histogram_quantile(0.99,
      sum(rate(yemen_market_database_query_duration_seconds_bucket[{{.window}}])) by (le)
    ) <= {{.threshold}}

# Error Budget Policy
error_budget_policy:
  description: "Actions to take when error budget is consumed"
  thresholds:
    - remaining_budget: 0.25  # 25% remaining
      actions:
        - "Freeze non-critical deployments"
        - "Focus on reliability improvements"
        - "Daily SLO review meetings"
    - remaining_budget: 0.10  # 10% remaining
      actions:
        - "Freeze all deployments except fixes"
        - "Dedicate 50% of engineering time to reliability"
        - "Executive escalation"
    - remaining_budget: 0.00  # Budget exhausted
      actions:
        - "Emergency response mode"
        - "All hands on reliability"
        - "Post-mortem required"

# SLI Recording Rules for efficient calculation
recording_rules:
  - record: "yemen_market:api_availability:rate30d"
    expr: |
      1 - (
        sum(rate(yemen_market_http_requests_total{status=~"5.."}[30d]))
        /
        sum(rate(yemen_market_http_requests_total[30d]))
      )
  
  - record: "yemen_market:analysis_latency:p95_7d"
    expr: |
      histogram_quantile(0.95,
        sum(rate(yemen_market_analysis_duration_seconds_bucket[7d])) by (le)
      )
  
  - record: "yemen_market:data_coverage:avg_24h"
    expr: |
      avg(yemen_market_data_coverage_ratio)
  
  - record: "yemen_market:model_convergence:rate24h"
    expr: |
      sum(rate(yemen_market_model_convergence_total{converged="True"}[24h]))
      /
      sum(rate(yemen_market_model_convergence_total[24h]))

# Multi-Window Multi-Burn-Rate Alerts
alerts:
  - name: "SLOBurnRate"
    rules:
      - alert: "HighErrorBudgetBurnRate"
        expr: |
          (
            yemen_market:api_availability:rate1h < 0.99 * 14.4
            AND
            yemen_market:api_availability:rate6h < 0.99 * 6
          )
        labels:
          severity: "critical"
          slo: "api_availability"
        annotations:
          summary: "High error budget burn rate for API availability"
          description: "Error budget is being consumed 14.4x faster than sustainable"