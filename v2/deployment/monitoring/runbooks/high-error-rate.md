# Runbook: High Error Rate

## Alert Details
- **Alert Name**: HighErrorRate
- **Severity**: Critical
- **Team**: Platform
- **SLO Impact**: Availability

## Overview
This alert fires when the API error rate (5xx responses) exceeds 5% for 5 minutes.

## Impact
- User requests are failing at an elevated rate
- Analysis jobs may be failing
- Data quality could be compromised
- SLO budget is being consumed rapidly

## Diagnosis

### 1. Check Current Error Rate
```bash
# View current error rate
kubectl exec -it prometheus-0 -- promtool query instant \
  'sum(rate(yemen_market_http_requests_total{status=~"5.."}[5m])) / sum(rate(yemen_market_http_requests_total[5m]))'

# Check error rate by endpoint
kubectl exec -it prometheus-0 -- promtool query instant \
  'sum(rate(yemen_market_http_requests_total{status=~"5.."}[5m])) by (endpoint) / sum(rate(yemen_market_http_requests_total[5m])) by (endpoint)'
```

### 2. Check Application Logs
```bash
# View recent errors
kubectl logs -l app=yemen-market-api --tail=100 | grep ERROR

# Check for specific error patterns
kubectl logs -l app=yemen-market-api --tail=1000 | jq '. | select(.level=="ERROR")'

# View errors with correlation IDs
kubectl logs -l app=yemen-market-api --tail=1000 | jq '. | select(.level=="ERROR") | {correlation_id, error, timestamp}'
```

### 3. Check Sentry for Error Details
1. Go to [Sentry Dashboard](https://sentry.io/organizations/yemen-market/issues/)
2. Filter by: `environment:production` and `timeframe:last_hour`
3. Look for error spikes or new error types
4. Check error grouping and affected users

### 4. Check System Resources
```bash
# CPU and Memory usage
kubectl top nodes
kubectl top pods -l app=yemen-market-api

# Database connections
kubectl exec -it postgres-0 -- psql -U yemen_market -c "SELECT count(*) FROM pg_stat_activity;"

# Redis status
kubectl exec -it redis-0 -- redis-cli info stats
```

## Mitigation

### Immediate Actions

1. **Scale Up if Resource Constrained**
   ```bash
   # Scale API pods
   kubectl scale deployment yemen-market-api --replicas=5
   
   # Scale worker pods if needed
   kubectl scale deployment yemen-market-worker --replicas=3
   ```

2. **Enable Circuit Breaker**
   ```bash
   # Temporarily disable problematic endpoints
   kubectl exec -it yemen-market-api-0 -- \
     curl -X POST http://localhost:8080/admin/circuit-breaker/enable \
     -d '{"endpoints": ["/api/v2/analysis/complex"]}'
   ```

3. **Clear Problematic Cache**
   ```bash
   # Clear Redis cache if corruption suspected
   kubectl exec -it redis-0 -- redis-cli FLUSHDB
   ```

### Root Cause Analysis

1. **Database Issues**
   - Check for long-running queries
   - Verify connection pool health
   - Look for deadlocks

2. **External Service Failures**
   - Check WFP API status
   - Verify HDX connectivity
   - Test ACLED data access

3. **Code Deployment Issues**
   - Check recent deployments
   - Review configuration changes
   - Verify migration status

## Recovery

1. **Verify Error Rate Decreasing**
   ```bash
   # Monitor error rate recovery
   watch -n 10 'kubectl exec -it prometheus-0 -- promtool query instant \
     "sum(rate(yemen_market_http_requests_total{status=~\"5..\"}[1m])) / sum(rate(yemen_market_http_requests_total[1m]))"'
   ```

2. **Check SLO Status**
   ```bash
   # View availability SLO
   kubectl exec -it yemen-market-api-0 -- \
     curl http://localhost:8080/metrics/slo/availability
   ```

3. **Notify Stakeholders**
   - Update status page
   - Send recovery notification
   - Document incident

## Prevention

1. **Improve Error Handling**
   - Add retry logic for transient failures
   - Implement better circuit breakers
   - Add request validation

2. **Capacity Planning**
   - Review resource limits
   - Plan for traffic spikes
   - Implement auto-scaling

3. **Testing**
   - Add chaos engineering tests
   - Improve integration test coverage
   - Regular disaster recovery drills

## Related Documentation
- [Architecture Overview](/docs/01-architecture/overview.md)
- [Error Handling Guide](/docs/02-user-guides/troubleshooting.md)
- [SLO Definitions](/docs/06-deployment/monitoring/slo-definitions.md)