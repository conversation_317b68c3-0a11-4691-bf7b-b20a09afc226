{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 3, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "avg(yemen_market_data_coverage_ratio{data_source=\"$data_source\"}) * 100", "refId": "A"}], "title": "Overall Data Coverage", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 24}, {"color": "red", "value": 48}]}, "unit": "h"}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "max(yemen_market_data_freshness_hours{data_source=\"$data_source\"})", "refId": "A"}], "title": "Max Data Age", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(increase(yemen_market_missing_data_points_total[24h]))", "refId": "A"}], "title": "Missing Data Points (24h)", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 5, "options": {"tooltip": {"mode": "multi"}, "legend": {"calcs": ["mean"], "displayMode": "table", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "yemen_market_data_coverage_ratio{commodity=\"$commodity\"} * 100", "legendFormat": "{{commodity}} - {{region}}", "refId": "A"}], "title": "Data Coverage by Region", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "viz": false, "legend": false}}, "mappings": [], "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 6, "options": {"pieType": "donut", "tooltip": {"mode": "single"}, "legend": {"displayMode": "table", "placement": "right", "values": ["value", "percent"]}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(increase(yemen_market_missing_data_points_total[24h])) by (reason)", "legendFormat": "{{reason}}", "refId": "A"}], "title": "Missing Data Reasons (24h)", "type": "piechart"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 7, "options": {"tooltip": {"mode": "multi"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(rate(yemen_market_external_api_calls_total[5m])) by (api, status)", "legendFormat": "{{api}} - {{status}}", "refId": "A"}], "title": "External API Call Rate", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ms"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 8, "options": {"tooltip": {"mode": "multi"}, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(yemen_market_external_api_duration_seconds_bucket[5m])) by (api, le)) * 1000", "legendFormat": "{{api}} - P95", "refId": "A"}], "title": "External API Latency P95", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "id": 9, "options": {"tooltip": {"mode": "single"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "(sum(yemen_market_cache_hits_total) by (cache_type) / (sum(yemen_market_cache_hits_total) by (cache_type) + sum(yemen_market_cache_misses_total) by (cache_type))) * 100", "legendFormat": "{{cache_type}}", "refId": "A"}], "title": "<PERSON><PERSON> Hit Rate by Type", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 27, "style": "dark", "tags": ["yemen-market", "data"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(yemen_market_data_coverage_ratio, data_source)", "hide": 0, "includeAll": true, "label": "Data Source", "multi": false, "name": "data_source", "options": [], "query": {"query": "label_values(yemen_market_data_coverage_ratio, data_source)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(yemen_market_data_coverage_ratio, commodity)", "hide": 0, "includeAll": true, "label": "Commodity", "multi": false, "name": "commodity", "options": [], "query": {"query": "label_values(yemen_market_data_coverage_ratio, commodity)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Yemen Market Integration - Data Pipeline Health", "version": 0}