{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 2, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"tooltip": {"mode": "single"}, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(yemen_market_analysis_duration_seconds_bucket{tier=\"$tier\"}[5m])) by (analysis_type, le))", "legendFormat": "{{analysis_type}} - P95", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(yemen_market_analysis_duration_seconds_bucket{tier=\"$tier\"}[5m])) by (analysis_type, le))", "legendFormat": "{{analysis_type}} - P99", "refId": "B"}], "title": "Analysis Duration Percentiles", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "id": 3, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "(sum(rate(yemen_market_model_convergence_total{converged=\"True\"}[1h])) / sum(rate(yemen_market_model_convergence_total[1h]))) * 100", "refId": "A"}], "title": "Model Convergence Rate", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 300}, {"color": "red", "value": 600}]}, "unit": "s"}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "avg(yemen_market_analysis_duration_seconds{tier=\"$tier\"})", "refId": "A"}], "title": "Average Analysis Duration", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "viz": false, "legend": false}}, "mappings": [], "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 5, "options": {"displayMode": "lcd", "pieType": "donut", "tooltip": {"mode": "single"}, "legend": {"displayMode": "table", "placement": "right", "values": ["value", "percent"]}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(increase(yemen_market_analysis_requests_total{commodity=\"$commodity\"}[1h])) by (status)", "legendFormat": "{{status}}", "refId": "A"}], "title": "Analysis Request Status Distribution", "type": "piechart"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 6, "options": {"tooltip": {"mode": "single"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(yemen_market_model_estimation_seconds_bucket[5m])) by (model_type, le))", "legendFormat": "{{model_type}}", "refId": "A"}], "title": "Model Estimation Time P95", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "mbytes"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 7, "options": {"tooltip": {"mode": "multi"}, "legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "yemen_market_memory_usage_mb", "legendFormat": "{{component}} - {{operation}}", "refId": "A"}], "title": "Memory Usage by Component", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 8, "options": {"tooltip": {"mode": "multi"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(rate(yemen_market_cointegration_tests_total[1h])) by (result)", "legendFormat": "{{result}}", "refId": "A"}], "title": "Cointegration Test Results", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 27, "style": "dark", "tags": ["yemen-market", "analysis"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(yemen_market_analysis_duration_seconds_bucket, tier)", "hide": 0, "includeAll": true, "label": "Tier", "multi": false, "name": "tier", "options": [], "query": {"query": "label_values(yemen_market_analysis_duration_seconds_bucket, tier)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(yemen_market_analysis_requests_total, commodity)", "hide": 0, "includeAll": true, "label": "Commodity", "multi": false, "name": "commodity", "options": [], "query": {"query": "label_values(yemen_market_analysis_requests_total, commodity)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Yemen Market Integration - Analysis Performance", "version": 0}