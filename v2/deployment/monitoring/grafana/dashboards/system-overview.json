{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 1, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}, "unit": "percent"}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "(1 - (sum(rate(yemen_market_http_requests_total{status=~\"5..\"}[5m])) / sum(rate(yemen_market_http_requests_total[5m])))) * 100", "refId": "A"}], "title": "Service Availability", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 6, "y": 0}, "id": 3, "options": {"tooltip": {"mode": "single"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(rate(yemen_market_http_requests_total[5m])) by (method)", "legendFormat": "{{method}}", "refId": "A"}], "title": "Request Rate by Method", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 2000}]}, "unit": "ms"}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 0}, "id": 4, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(yemen_market_http_request_duration_seconds_bucket[5m])) by (le)) * 1000", "refId": "A"}], "title": "P95 Latency", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 10}]}}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 4}, "id": 5, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "yemen_market_active_analyses", "refId": "A"}], "title": "Active Analyses", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 6, "options": {"tooltip": {"mode": "single"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "yemen_market_data_coverage_ratio * 100", "legendFormat": "{{commodity}} - {{region}}", "refId": "A"}], "title": "Data Coverage by Commodity and Region", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"tooltip": false, "viz": false, "legend": false}}, "mappings": [], "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 7, "options": {"pieType": "pie", "tooltip": {"mode": "single"}, "legend": {"displayMode": "list", "placement": "right"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(rate(yemen_market_analysis_requests_total[1h])) by (analysis_type)", "legendFormat": "{{analysis_type}}", "refId": "A"}], "title": "Analysis Requests by Type (1h)", "type": "piechart"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "YER/USD"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 8, "options": {"tooltip": {"mode": "single"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "yemen_market_exchange_rate", "legendFormat": "{{currency_zone}} - {{rate_type}}", "refId": "A"}], "title": "Exchange Rates by Currency Zone", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 9, "options": {"tooltip": {"mode": "single"}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(increase(yemen_market_alerts_triggered_total[24h])) by (alert_type, severity)", "legendFormat": "{{alert_type}} - {{severity}}", "refId": "A"}], "title": "<PERSON><PERSON><PERSON> (24h)", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 27, "style": "dark", "tags": ["yemen-market", "system"], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Yemen Market Integration - System Overview", "version": 0}