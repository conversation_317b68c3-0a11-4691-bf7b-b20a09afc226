# Canary Deployment Strategy for Yemen Market Integration
---
# Flagger for Progressive Delivery
apiVersion: flagger.app/v1beta1
kind: Canary
metadata:
  name: yemen-market-api-canary
  namespace: yemen-market-v2
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: yemen-market-api
  progressDeadlineSeconds: 600
  service:
    port: 8000
    targetPort: 8000
    gateways:
      - public-gateway.istio-system.svc.cluster.local
    hosts:
      - api.yemen-market.example.com
  analysis:
    # Schedule interval for metric analysis
    interval: 1m
    # Number of iterations before promotion
    threshold: 10
    # Max number of failed checks before rollback
    maxWeight: 50
    # Canary increment step
    stepWeight: 10
    metrics:
      - name: request-success-rate
        thresholdRange:
          min: 99
        interval: 1m
      - name: request-duration
        thresholdRange:
          max: 500
        interval: 30s
      - name: error-rate
        thresholdRange:
          max: 1
        interval: 1m
    # Load testing during canary analysis
    webhooks:
      - name: load-test
        url: http://flagger-loadtester.test/
        timeout: 5s
        metadata:
          cmd: "hey -z 1m -q 10 -c 2 http://yemen-market-api-canary.yemen-market-v2:8000/health"
      - name: acceptance-test
        type: pre-rollout
        url: http://flagger-loadtester.test/
        timeout: 30s
        metadata:
          type: bash
          cmd: "curl -s http://yemen-market-api-canary.yemen-market-v2:8000/ready | grep ok"
      - name: smoke-test
        type: post-rollout
        url: http://flagger-loadtester.test/
        timeout: 30s
        metadata:
          type: bash
          cmd: "curl -s http://yemen-market-api.yemen-market-v2:8000/health | grep healthy"

---
# Blue-Green Deployment Alternative
apiVersion: v1
kind: Service
metadata:
  name: yemen-market-api-active
  namespace: yemen-market-v2
spec:
  selector:
    app: yemen-market-api
    version: active
  ports:
    - port: 8000
      targetPort: 8000

---
apiVersion: v1
kind: Service
metadata:
  name: yemen-market-api-preview
  namespace: yemen-market-v2
spec:
  selector:
    app: yemen-market-api
    version: preview
  ports:
    - port: 8000
      targetPort: 8000

---
# Rollout Strategy with Argo Rollouts
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: yemen-market-api-rollout
  namespace: yemen-market-v2
spec:
  replicas: 5
  strategy:
    canary:
      canaryService: yemen-market-api-preview
      stableService: yemen-market-api-active
      trafficRouting:
        nginx:
          stableIngress: yemen-market-api-ingress
      steps:
        - setWeight: 10
        - pause: {duration: 5m}
        - analysis:
            templates:
              - templateName: success-rate
            args:
              - name: service-name
                value: yemen-market-api-preview
        - setWeight: 20
        - pause: {duration: 5m}
        - setWeight: 40
        - pause: {duration: 5m}
        - setWeight: 60
        - pause: {duration: 5m}
        - setWeight: 80
        - pause: {duration: 5m}
      analysis:
        templates:
          - templateName: success-rate
        startingStep: 2
        args:
          - name: service-name
            value: yemen-market-api-preview
  selector:
    matchLabels:
      app: yemen-market-api
  template:
    metadata:
      labels:
        app: yemen-market-api
    spec:
      containers:
        - name: api
          image: yemen-market-v2:latest
          ports:
            - containerPort: 8000

---
# Analysis Template for Rollout
apiVersion: argoproj.io/v1alpha1
kind: AnalysisTemplate
metadata:
  name: success-rate
  namespace: yemen-market-v2
spec:
  args:
    - name: service-name
  metrics:
    - name: success-rate
      interval: 5m
      successCondition: result[0] >= 0.95
      failureLimit: 3
      provider:
        prometheus:
          address: http://prometheus:9090
          query: |
            sum(rate(
              http_requests_total{job="{{args.service-name}}",status!~"5.."}[5m]
            )) / 
            sum(rate(
              http_requests_total{job="{{args.service-name}}"}[5m]
            ))
    - name: latency
      interval: 5m
      successCondition: result[0] < 0.5
      failureLimit: 3
      provider:
        prometheus:
          address: http://prometheus:9090
          query: |
            histogram_quantile(0.95,
              sum(rate(http_request_duration_seconds_bucket{job="{{args.service-name}}"}[5m]))
              by (le)
            )

---
# GitOps with ArgoCD Application
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: yemen-market-v2
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/yemen-market/infrastructure
    targetRevision: HEAD
    path: kubernetes/overlays/production
  destination:
    server: https://kubernetes.default.svc
    namespace: yemen-market-v2
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - Validate=true
      - CreateNamespace=false
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10

---
# Kustomization for Environment-Specific Configurations
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: yemen-market-v2

resources:
  - api-deployment.yaml
  - worker-deployment.yaml
  - postgres.yaml
  - redis-ha.yaml
  - monitoring-enhanced.yaml
  - network-policies.yaml
  - sealed-secrets.yaml

patchesStrategicMerge:
  - |-
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: yemen-market-api
      namespace: yemen-market-v2
    spec:
      replicas: 5
      template:
        spec:
          containers:
            - name: api
              resources:
                requests:
                  memory: "1Gi"
                  cpu: "500m"
                limits:
                  memory: "2Gi"
                  cpu: "2000m"

configMapGenerator:
  - name: yemen-market-config
    behavior: merge
    literals:
      - API_ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - ENABLE_METRICS=true
      - ENABLE_TRACING=true

secretGenerator:
  - name: yemen-market-secrets
    behavior: merge
    envs:
      - secrets.env

images:
  - name: yemen-market-v2
    newTag: v2.1.0

replicas:
  - name: yemen-market-api
    count: 5
  - name: yemen-market-worker
    count: 10