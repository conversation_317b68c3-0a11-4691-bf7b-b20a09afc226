---
# Prometheus ServiceMonitor for Yemen Market Integration
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: yemen-market-api
  namespace: monitoring
  labels:
    app: yemen-market
    prometheus: kube-prometheus
spec:
  namespaceSelector:
    matchNames:
      - yemen-market
  selector:
    matchLabels:
      app: yemen-market-api
  endpoints:
    - port: metrics
      interval: 30s
      path: /metrics
      scheme: http
      relabelings:
        - sourceLabels: [__address__]
          targetLabel: instance
          regex: '([^:]+)(?::\d+)?'
          replacement: '${1}'
---
# Grafana ConfigMap for dashboards
apiVersion: v1
kind: ConfigMap
metadata:
  name: yemen-market-dashboards
  namespace: monitoring
  labels:
    grafana_dashboard: "1"
data:
  system-overview.json: |
    {{ .Files.Get "deployment/monitoring/grafana/dashboards/system-overview.json" | indent 4 }}
  analysis-performance.json: |
    {{ .Files.Get "deployment/monitoring/grafana/dashboards/analysis-performance.json" | indent 4 }}
  data-pipeline-health.json: |
    {{ .Files.Get "deployment/monitoring/grafana/dashboards/data-pipeline-health.json" | indent 4 }}
---
# Prometheus Rules
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: yemen-market-alerts
  namespace: monitoring
  labels:
    prometheus: kube-prometheus
    role: alert-rules
spec:
  groups:
    {{ .Files.Get "deployment/monitoring/prometheus/alerts.yml" | indent 4 }}
---
# PodMonitor for Yemen Market pods
apiVersion: monitoring.coreos.com/v1
kind: PodMonitor
metadata:
  name: yemen-market-pods
  namespace: monitoring
spec:
  namespaceSelector:
    matchNames:
      - yemen-market
  selector:
    matchLabels:
      app.kubernetes.io/part-of: yemen-market
  podMetricsEndpoints:
    - port: metrics
      interval: 30s
      path: /metrics
---
# Service for Prometheus metrics aggregation
apiVersion: v1
kind: Service
metadata:
  name: yemen-market-metrics
  namespace: yemen-market
  labels:
    app: yemen-market-api
spec:
  selector:
    app: yemen-market-api
  ports:
    - name: metrics
      port: 9090
      targetPort: 9090
      protocol: TCP
---
# NetworkPolicy for monitoring access
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-prometheus-scraping
  namespace: yemen-market
spec:
  podSelector:
    matchLabels:
      app: yemen-market-api
  policyTypes:
    - Ingress
  ingress:
    - from:
        - namespaceSelector:
            matchLabels:
              name: monitoring
      ports:
        - protocol: TCP
          port: 9090
---
# Jaeger configuration for distributed tracing
apiVersion: jaegertracing.io/v1
kind: Jaeger
metadata:
  name: yemen-market-jaeger
  namespace: monitoring
spec:
  strategy: production
  storage:
    type: elasticsearch
    options:
      es:
        server-urls: http://elasticsearch:9200
        index-prefix: yemen-market
  ingress:
    enabled: true
    hosts:
      - jaeger.yemen-market.local
  agent:
    strategy: DaemonSet
  collector:
    replicas: 2
    resources:
      limits:
        cpu: 1
        memory: 1Gi
      requests:
        cpu: 500m
        memory: 512Mi
  query:
    replicas: 2
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 200m
        memory: 256Mi
---
# Loki Stack for log aggregation
apiVersion: v1
kind: ConfigMap
metadata:
  name: loki-config
  namespace: monitoring
data:
  loki.yaml: |
    auth_enabled: false
    server:
      http_listen_port: 3100
    ingester:
      chunk_idle_period: 3m
      chunk_retain_period: 1m
      max_transfer_retries: 0
      lifecycler:
        ring:
          kvstore:
            store: inmemory
          replication_factor: 1
    schema_config:
      configs:
        - from: 2020-05-15
          store: boltdb-shipper
          object_store: s3
          schema: v11
          index:
            prefix: index_
            period: 24h
    storage_config:
      boltdb_shipper:
        active_index_directory: /loki/boltdb-shipper-active
        cache_location: /loki/boltdb-shipper-cache
        shared_store: s3
      aws:
        s3: s3://yemen-market-logs
        region: us-east-1
    limits_config:
      enforce_metric_name: false
      reject_old_samples: true
      reject_old_samples_max_age: 168h
---
# Promtail DaemonSet for log collection
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: promtail
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: promtail
  template:
    metadata:
      labels:
        app: promtail
    spec:
      serviceAccountName: promtail
      containers:
        - name: promtail
          image: grafana/promtail:2.9.0
          args:
            - -config.file=/etc/promtail/config.yml
          volumeMounts:
            - name: config
              mountPath: /etc/promtail
            - name: varlog
              mountPath: /var/log
              readOnly: true
            - name: varlibdockercontainers
              mountPath: /var/lib/docker/containers
              readOnly: true
          env:
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
      volumes:
        - name: config
          configMap:
            name: promtail-config
        - name: varlog
          hostPath:
            path: /var/log
        - name: varlibdockercontainers
          hostPath:
            path: /var/lib/docker/containers
---
# Alertmanager configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: monitoring
data:
  alertmanager.yml: |
    global:
      resolve_timeout: 5m
      slack_api_url: ${SLACK_WEBHOOK_URL}
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 12h
      receiver: 'team-platform'
      routes:
        - match:
            team: data
          receiver: team-data
        - match:
            team: analysis
          receiver: team-analysis
        - match:
            severity: critical
          receiver: pagerduty
    receivers:
      - name: 'team-platform'
        slack_configs:
          - channel: '#platform-alerts'
            title: 'Yemen Market Platform Alert'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
            send_resolved: true
      - name: 'team-data'
        slack_configs:
          - channel: '#data-alerts'
            title: 'Yemen Market Data Alert'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'
            send_resolved: true
      - name: 'team-analysis'
        email_configs:
          - to: '<EMAIL>'
            headers:
              Subject: 'Yemen Market Analysis Alert: {{ .GroupLabels.alertname }}'
      - name: 'pagerduty'
        pagerduty_configs:
          - service_key: ${PAGERDUTY_SERVICE_KEY}
            description: '{{ .GroupLabels.alertname }}: {{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'