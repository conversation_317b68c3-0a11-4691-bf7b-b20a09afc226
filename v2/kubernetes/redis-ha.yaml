# Redis High Availability with Sentinel
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-ha-config
  namespace: yemen-market-v2
data:
  redis.conf: |
    # Redis Master Configuration
    port 6379
    bind 0.0.0.0
    protected-mode yes
    requirepass REDIS_PASSWORD_PLACEHOLDER
    masterauth REDIS_PASSWORD_PLACEHOLDER
    
    # Persistence
    save 900 1
    save 300 10
    save 60 10000
    appendonly yes
    appendfsync everysec
    no-appendfsync-on-rewrite no
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb
    
    # Memory management
    maxmemory 2gb
    maxmemory-policy allkeys-lru
    
    # Performance
    tcp-keepalive 60
    timeout 300
    tcp-backlog 511
    
    # Logging
    loglevel notice
    logfile ""
    syslog-enabled yes
    syslog-ident redis
    
    # Slow log
    slowlog-log-slower-than 10000
    slowlog-max-len 128
    
    # Client output buffer limits
    client-output-buffer-limit normal 0 0 0
    client-output-buffer-limit replica 256mb 64mb 60
    client-output-buffer-limit pubsub 32mb 8mb 60
    
    # Threaded I/O
    io-threads 4
    io-threads-do-reads yes
    
  sentinel.conf: |
    # Redis Sentinel Configuration
    port 26379
    bind 0.0.0.0
    sentinel announce-ip SENTINEL_IP_PLACEHOLDER
    sentinel announce-port 26379
    
    # Monitor Redis master
    sentinel monitor mymaster redis-master-0.redis-headless 6379 2
    sentinel auth-pass mymaster REDIS_PASSWORD_PLACEHOLDER
    
    # Sentinel settings
    sentinel down-after-milliseconds mymaster 5000
    sentinel parallel-syncs mymaster 1
    sentinel failover-timeout mymaster 10000
    
    # Notification script
    sentinel notification-script mymaster /scripts/notify.sh
    
  notify.sh: |
    #!/bin/sh
    # Send notification on failover
    curl -X POST -H 'Content-type: application/json' \
      --data "{\"text\":\"Redis failover detected: $1 $2 $3 $4 $5 $6\"}" \
      "$SLACK_WEBHOOK_URL"

---
apiVersion: v1
kind: Service
metadata:
  name: redis-headless
  namespace: yemen-market-v2
  labels:
    app: redis
spec:
  clusterIP: None
  ports:
    - port: 6379
      targetPort: 6379
      name: redis
    - port: 26379
      targetPort: 26379
      name: sentinel
  selector:
    app: redis

---
apiVersion: v1
kind: Service
metadata:
  name: redis-master
  namespace: yemen-market-v2
  labels:
    app: redis
    role: master
spec:
  type: ClusterIP
  ports:
    - port: 6379
      targetPort: 6379
  selector:
    app: redis
    role: master

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-master
  namespace: yemen-market-v2
spec:
  serviceName: redis-headless
  replicas: 1
  selector:
    matchLabels:
      app: redis
      role: master
  template:
    metadata:
      labels:
        app: redis
        role: master
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9121"
    spec:
      initContainers:
        - name: config-init
          image: busybox:1.35
          command: ['sh', '-c']
          args:
            - |
              cp /tmp/redis.conf /etc/redis/redis.conf
              sed -i "s/REDIS_PASSWORD_PLACEHOLDER/$REDIS_PASSWORD/g" /etc/redis/redis.conf
              chmod 644 /etc/redis/redis.conf
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: REDIS_PASSWORD
          volumeMounts:
            - name: redis-config
              mountPath: /tmp/redis.conf
              subPath: redis.conf
            - name: config
              mountPath: /etc/redis
      containers:
        - name: redis
          image: redis:7-alpine
          command: ["redis-server"]
          args: ["/etc/redis/redis.conf"]
          ports:
            - containerPort: 6379
              name: redis
          startupProbe:
            tcpSocket:
              port: 6379
            initialDelaySeconds: 5
            periodSeconds: 5
            failureThreshold: 30
          livenessProbe:
            exec:
              command:
                - sh
                - -c
                - redis-cli -a $REDIS_PASSWORD ping
            initialDelaySeconds: 0
            periodSeconds: 10
            failureThreshold: 3
          readinessProbe:
            exec:
              command:
                - sh
                - -c
                - redis-cli -a $REDIS_PASSWORD ping
            initialDelaySeconds: 0
            periodSeconds: 5
            failureThreshold: 3
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: REDIS_PASSWORD
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "2Gi"
              cpu: "1000m"
          volumeMounts:
            - name: config
              mountPath: /etc/redis
            - name: data
              mountPath: /data
          securityContext:
            runAsNonRoot: true
            runAsUser: 999
            runAsGroup: 999
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL
        - name: redis-exporter
          image: oliver006/redis_exporter:v1.54.0
          ports:
            - containerPort: 9121
              name: metrics
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: REDIS_PASSWORD
          resources:
            requests:
              memory: "64Mi"
              cpu: "50m"
            limits:
              memory: "128Mi"
              cpu: "100m"
      volumes:
        - name: redis-config
          configMap:
            name: redis-ha-config
        - name: config
          emptyDir: {}
      securityContext:
        fsGroup: 999
        fsGroupChangePolicy: "OnRootMismatch"
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: ["ReadWriteOnce"]
        storageClassName: fast-ssd
        resources:
          requests:
            storage: 10Gi

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-replica
  namespace: yemen-market-v2
spec:
  serviceName: redis-headless
  replicas: 2
  selector:
    matchLabels:
      app: redis
      role: replica
  template:
    metadata:
      labels:
        app: redis
        role: replica
    spec:
      initContainers:
        - name: config-init
          image: busybox:1.35
          command: ['sh', '-c']
          args:
            - |
              cp /tmp/redis.conf /etc/redis/redis.conf
              sed -i "s/REDIS_PASSWORD_PLACEHOLDER/$REDIS_PASSWORD/g" /etc/redis/redis.conf
              echo "replicaof redis-master-0.redis-headless 6379" >> /etc/redis/redis.conf
              chmod 644 /etc/redis/redis.conf
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: REDIS_PASSWORD
          volumeMounts:
            - name: redis-config
              mountPath: /tmp/redis.conf
              subPath: redis.conf
            - name: config
              mountPath: /etc/redis
      containers:
        - name: redis
          image: redis:7-alpine
          command: ["redis-server"]
          args: ["/etc/redis/redis.conf"]
          ports:
            - containerPort: 6379
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: REDIS_PASSWORD
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "2Gi"
              cpu: "1000m"
          volumeMounts:
            - name: config
              mountPath: /etc/redis
            - name: data
              mountPath: /data
      volumes:
        - name: redis-config
          configMap:
            name: redis-ha-config
        - name: config
          emptyDir: {}
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: ["ReadWriteOnce"]
        storageClassName: fast-ssd
        resources:
          requests:
            storage: 10Gi

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-sentinel
  namespace: yemen-market-v2
spec:
  replicas: 3
  selector:
    matchLabels:
      app: redis-sentinel
  template:
    metadata:
      labels:
        app: redis-sentinel
    spec:
      initContainers:
        - name: config-init
          image: busybox:1.35
          command: ['sh', '-c']
          args:
            - |
              cp /tmp/sentinel.conf /etc/redis/sentinel.conf
              sed -i "s/REDIS_PASSWORD_PLACEHOLDER/$REDIS_PASSWORD/g" /etc/redis/sentinel.conf
              sed -i "s/SENTINEL_IP_PLACEHOLDER/$POD_IP/g" /etc/redis/sentinel.conf
              chmod 644 /etc/redis/sentinel.conf
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: yemen-market-secrets
                  key: REDIS_PASSWORD
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
          volumeMounts:
            - name: sentinel-config
              mountPath: /tmp/sentinel.conf
              subPath: sentinel.conf
            - name: config
              mountPath: /etc/redis
      containers:
        - name: sentinel
          image: redis:7-alpine
          command: ["redis-sentinel"]
          args: ["/etc/redis/sentinel.conf"]
          ports:
            - containerPort: 26379
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "256Mi"
              cpu: "200m"
          volumeMounts:
            - name: config
              mountPath: /etc/redis
      volumes:
        - name: sentinel-config
          configMap:
            name: redis-ha-config
        - name: config
          emptyDir: {}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - redis-sentinel
              topologyKey: kubernetes.io/hostname