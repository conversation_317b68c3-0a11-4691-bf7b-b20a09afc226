# Network Policies for Yemen Market Integration
---
# Default deny all ingress and egress
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: yemen-market-v2
spec:
  podSelector: {}
  policyTypes:
    - Ingress
    - Egress

---
# API Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: yemen-market-api-network-policy
  namespace: yemen-market-v2
spec:
  podSelector:
    matchLabels:
      app: yemen-market-api
  policyTypes:
    - Ingress
    - Egress
  ingress:
    # Allow from ingress controller
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: ingress-nginx
      ports:
        - protocol: TCP
          port: 8000
    # Allow from Prometheus
    - from:
        - podSelector:
            matchLabels:
              app: prometheus
      ports:
        - protocol: TCP
          port: 8000
    # Allow from other API pods (for distributed operations)
    - from:
        - podSelector:
            matchLabels:
              app: yemen-market-api
      ports:
        - protocol: TCP
          port: 8000
  egress:
    # Allow to PostgreSQL
    - to:
        - podSelector:
            matchLabels:
              app: postgres
      ports:
        - protocol: TCP
          port: 5432
    # Allow to Redis
    - to:
        - podSelector:
            matchLabels:
              app: redis
      ports:
        - protocol: TCP
          port: 6379
    # Allow to DNS
    - to:
        - namespaceSelector: {}
          podSelector:
            matchLabels:
              k8s-app: kube-dns
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    # Allow HTTPS to external APIs
    - to:
        - ipBlock:
            cidr: 0.0.0.0/0
            except:
              - 10.0.0.0/8
              - **********/12
              - ***********/16
              - ***************/32  # Block cloud metadata service
      ports:
        - protocol: TCP
          port: 443
    # Allow to OpenTelemetry collector
    - to:
        - podSelector:
            matchLabels:
              app: opentelemetry-collector
      ports:
        - protocol: TCP
          port: 4317  # gRPC
        - protocol: TCP
          port: 4318  # HTTP

---
# Worker Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: yemen-market-worker-network-policy
  namespace: yemen-market-v2
spec:
  podSelector:
    matchLabels:
      app: yemen-market-worker
  policyTypes:
    - Ingress
    - Egress
  ingress:
    # Allow from Prometheus for metrics
    - from:
        - podSelector:
            matchLabels:
              app: prometheus
      ports:
        - protocol: TCP
          port: 9090
  egress:
    # Allow to PostgreSQL
    - to:
        - podSelector:
            matchLabels:
              app: postgres
      ports:
        - protocol: TCP
          port: 5432
    # Allow to Redis
    - to:
        - podSelector:
            matchLabels:
              app: redis
      ports:
        - protocol: TCP
          port: 6379
    # Allow to DNS
    - to:
        - namespaceSelector: {}
          podSelector:
            matchLabels:
              k8s-app: kube-dns
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    # Allow HTTPS to external APIs
    - to:
        - ipBlock:
            cidr: 0.0.0.0/0
            except:
              - 10.0.0.0/8
              - **********/12
              - ***********/16
              - ***************/32
      ports:
        - protocol: TCP
          port: 443
    # Allow to S3 for data storage
    - to:
        - ipBlock:
            cidr: 0.0.0.0/0
      ports:
        - protocol: TCP
          port: 443

---
# Database Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: postgres-network-policy
  namespace: yemen-market-v2
spec:
  podSelector:
    matchLabels:
      app: postgres
  policyTypes:
    - Ingress
    - Egress
  ingress:
    # Allow from API pods
    - from:
        - podSelector:
            matchLabels:
              app: yemen-market-api
      ports:
        - protocol: TCP
          port: 5432
    # Allow from Worker pods
    - from:
        - podSelector:
            matchLabels:
              app: yemen-market-worker
      ports:
        - protocol: TCP
          port: 5432
    # Allow from backup jobs
    - from:
        - podSelector:
            matchLabels:
              job: backup
      ports:
        - protocol: TCP
          port: 5432
    # Allow from other Postgres pods (for replication)
    - from:
        - podSelector:
            matchLabels:
              app: postgres
      ports:
        - protocol: TCP
          port: 5432
    # Allow from Prometheus postgres exporter
    - from:
        - podSelector:
            matchLabels:
              app: postgres-exporter
      ports:
        - protocol: TCP
          port: 5432
  egress:
    # Allow to DNS
    - to:
        - namespaceSelector: {}
          podSelector:
            matchLabels:
              k8s-app: kube-dns
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    # Allow to other Postgres pods (for replication)
    - to:
        - podSelector:
            matchLabels:
              app: postgres
      ports:
        - protocol: TCP
          port: 5432

---
# Redis Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: redis-network-policy
  namespace: yemen-market-v2
spec:
  podSelector:
    matchLabels:
      app: redis
  policyTypes:
    - Ingress
    - Egress
  ingress:
    # Allow from API pods
    - from:
        - podSelector:
            matchLabels:
              app: yemen-market-api
      ports:
        - protocol: TCP
          port: 6379
    # Allow from Worker pods
    - from:
        - podSelector:
            matchLabels:
              app: yemen-market-worker
      ports:
        - protocol: TCP
          port: 6379
    # Allow from Redis Sentinel
    - from:
        - podSelector:
            matchLabels:
              app: redis-sentinel
      ports:
        - protocol: TCP
          port: 6379
    # Allow from other Redis pods (for replication)
    - from:
        - podSelector:
            matchLabels:
              app: redis
      ports:
        - protocol: TCP
          port: 6379
    # Allow from Prometheus redis exporter
    - from:
        - podSelector:
            matchLabels:
              app: redis-exporter
      ports:
        - protocol: TCP
          port: 6379
  egress:
    # Allow to DNS
    - to:
        - namespaceSelector: {}
          podSelector:
            matchLabels:
              k8s-app: kube-dns
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    # Allow to other Redis pods (for replication)
    - to:
        - podSelector:
            matchLabels:
              app: redis
      ports:
        - protocol: TCP
          port: 6379
    # Allow to Redis Sentinel
    - to:
        - podSelector:
            matchLabels:
              app: redis-sentinel
      ports:
        - protocol: TCP
          port: 26379

---
# Monitoring Stack Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: monitoring-network-policy
  namespace: yemen-market-v2
spec:
  podSelector:
    matchLabels:
      component: monitoring
  policyTypes:
    - Ingress
    - Egress
  ingress:
    # Allow from internal ingress for dashboards
    - from:
        - namespaceSelector:
            matchLabels:
              name: ingress-nginx-internal
      ports:
        - protocol: TCP
          port: 3000  # Grafana
        - protocol: TCP
          port: 9090  # Prometheus
        - protocol: TCP
          port: 9093  # Alertmanager
  egress:
    # Allow to all pods in namespace for metrics scraping
    - to:
        - podSelector: {}
      ports:
        - protocol: TCP
    # Allow to DNS
    - to:
        - namespaceSelector: {}
          podSelector:
            matchLabels:
              k8s-app: kube-dns
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    # Allow HTTPS for external integrations (Slack, PagerDuty)
    - to:
        - ipBlock:
            cidr: 0.0.0.0/0
      ports:
        - protocol: TCP
          port: 443