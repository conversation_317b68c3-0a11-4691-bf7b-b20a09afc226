# Enhanced Monitoring Stack
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: yemen-market-v2
data:
  alertmanager.yml: |
    global:
      resolve_timeout: 5m
      slack_api_url: 'SLACK_WEBHOOK_URL_PLACEHOLDER'
    
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 12h
      receiver: 'default'
      routes:
        - match:
            severity: critical
          receiver: pagerduty
          continue: true
        - match:
            severity: warning
          receiver: slack
          continue: true
        - match:
            alertname: Watchdog
          receiver: 'null'
    
    receivers:
      - name: 'null'
      
      - name: 'default'
        slack_configs:
          - channel: '#yemen-market-alerts'
            title: 'Yemen Market Alert'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}{{ end }}'
            
      - name: 'slack'
        slack_configs:
          - channel: '#yemen-market-warnings'
            title: 'Yemen Market Warning'
            text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ .Annotations.description }}{{ end }}'
            send_resolved: true
            
      - name: 'pagerduty'
        pagerduty_configs:
          - service_key: 'PAGERDUTY_SERVICE_KEY_PLACEHOLDER'
            description: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alertmanager
  namespace: yemen-market-v2
spec:
  replicas: 3
  selector:
    matchLabels:
      app: alertmanager
  template:
    metadata:
      labels:
        app: alertmanager
    spec:
      containers:
        - name: alertmanager
          image: prom/alertmanager:v0.26.0
          args:
            - '--config.file=/etc/alertmanager/alertmanager.yml'
            - '--storage.path=/alertmanager'
            - '--cluster.peer=alertmanager-0.alertmanager-headless:9094'
            - '--cluster.peer=alertmanager-1.alertmanager-headless:9094'
            - '--cluster.peer=alertmanager-2.alertmanager-headless:9094'
          ports:
            - containerPort: 9093
              name: http
            - containerPort: 9094
              name: mesh
          volumeMounts:
            - name: config
              mountPath: /etc/alertmanager
            - name: storage
              mountPath: /alertmanager
          resources:
            requests:
              memory: "128Mi"
              cpu: "100m"
            limits:
              memory: "256Mi"
              cpu: "200m"
      volumes:
        - name: config
          configMap:
            name: alertmanager-config
        - name: storage
          emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: yemen-market-v2
spec:
  selector:
    app: alertmanager
  ports:
    - port: 9093
      targetPort: 9093

---
apiVersion: v1
kind: Service
metadata:
  name: alertmanager-headless
  namespace: yemen-market-v2
spec:
  clusterIP: None
  selector:
    app: alertmanager
  ports:
    - port: 9093
      targetPort: 9093
    - port: 9094
      targetPort: 9094

---
# Enhanced Prometheus Rules
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules-enhanced
  namespace: yemen-market-v2
data:
  slo-alerts.yml: |
    groups:
      - name: slo_alerts
        interval: 30s
        rules:
          # API Availability SLO
          - alert: APIAvailabilitySLOBreach
            expr: |
              (1 - (
                sum(rate(http_requests_total{job="yemen-market-api",status!~"5.."}[5m]))
                /
                sum(rate(http_requests_total{job="yemen-market-api"}[5m]))
              )) < 0.999
            for: 5m
            labels:
              severity: critical
              slo: availability
            annotations:
              summary: API availability SLO breach
              description: "API availability is {{ $value | humanizePercentage }}, below 99.9% SLO"
          
          # API Latency SLO
          - alert: APILatencySLOBreach
            expr: |
              histogram_quantile(0.95, 
                sum(rate(http_request_duration_seconds_bucket{job="yemen-market-api"}[5m])) 
                by (le)
              ) > 0.5
            for: 5m
            labels:
              severity: warning
              slo: latency
            annotations:
              summary: API latency SLO breach
              description: "95th percentile latency is {{ $value }}s, above 500ms SLO"
          
          # Analysis Processing Time SLO
          - alert: AnalysisProcessingTimeSLOBreach
            expr: |
              histogram_quantile(0.95,
                sum(rate(analysis_processing_duration_seconds_bucket[5m]))
                by (le)
              ) > 300
            for: 10m
            labels:
              severity: warning
              slo: processing_time
            annotations:
              summary: Analysis processing time SLO breach
              description: "95th percentile processing time is {{ $value }}s, above 5 minute SLO"
  
  capacity-alerts.yml: |
    groups:
      - name: capacity_alerts
        interval: 60s
        rules:
          # Database Connection Pool Exhaustion
          - alert: DatabaseConnectionPoolExhaustion
            expr: |
              (pg_stat_database_numbackends{datname="yemen_market_v2"} 
              / 
              pg_settings_max_connections) > 0.8
            for: 5m
            labels:
              severity: warning
              component: database
            annotations:
              summary: Database connection pool near exhaustion
              description: "Database using {{ $value | humanizePercentage }} of max connections"
          
          # Disk Space Prediction
          - alert: DiskSpaceRunningOut
            expr: |
              predict_linear(
                node_filesystem_avail_bytes{mountpoint="/"}[1h], 
                4 * 3600
              ) < 0
            for: 15m
            labels:
              severity: warning
              component: infrastructure
            annotations:
              summary: Disk space predicted to run out in 4 hours
              description: "Filesystem {{ $labels.mountpoint }} on {{ $labels.instance }} predicted to run out of space"
          
          # Memory Pressure
          - alert: MemoryPressureHigh
            expr: |
              (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) > 0.9
            for: 10m
            labels:
              severity: warning
              component: infrastructure
            annotations:
              summary: High memory pressure detected
              description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"
  
  business-alerts.yml: |
    groups:
      - name: business_alerts
        interval: 300s
        rules:
          # Data Freshness
          - alert: DataFreshnessViolation
            expr: |
              (time() - max(data_last_updated_timestamp) by (data_source)) > 86400
            for: 30m
            labels:
              severity: warning
              component: data
            annotations:
              summary: Data freshness violation for {{ $labels.data_source }}
              description: "{{ $labels.data_source }} data hasn't been updated for {{ $value | humanizeDuration }}"
          
          # Analysis Success Rate
          - alert: LowAnalysisSuccessRate
            expr: |
              (
                sum(rate(analysis_completed_total{status="success"}[1h]))
                /
                sum(rate(analysis_completed_total[1h]))
              ) < 0.95
            for: 15m
            labels:
              severity: warning
              component: analysis
            annotations:
              summary: Low analysis success rate
              description: "Analysis success rate is {{ $value | humanizePercentage }}, below 95% threshold"
          
          # API Usage Anomaly
          - alert: APIUsageAnomaly
            expr: |
              abs(
                rate(http_requests_total{job="yemen-market-api"}[5m]) 
                - 
                avg_over_time(rate(http_requests_total{job="yemen-market-api"}[5m])[1h:5m])
              ) > (2 * stddev_over_time(rate(http_requests_total{job="yemen-market-api"}[5m])[1h:5m]))
            for: 10m
            labels:
              severity: info
              component: api
            annotations:
              summary: Anomalous API usage detected
              description: "API request rate deviates significantly from normal patterns"

---
# Custom Grafana Dashboards
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: yemen-market-v2
data:
  yemen-market-overview.json: |
    {
      "dashboard": {
        "title": "Yemen Market Integration - Overview",
        "panels": [
          {
            "title": "API Request Rate",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{job=\"yemen-market-api\"}[5m])) by (method, status)"
              }
            ]
          },
          {
            "title": "API Latency (p50, p95, p99)",
            "targets": [
              {
                "expr": "histogram_quantile(0.5, sum(rate(http_request_duration_seconds_bucket{job=\"yemen-market-api\"}[5m])) by (le))"
              },
              {
                "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job=\"yemen-market-api\"}[5m])) by (le))"
              },
              {
                "expr": "histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket{job=\"yemen-market-api\"}[5m])) by (le))"
              }
            ]
          },
          {
            "title": "Analysis Processing Queue",
            "targets": [
              {
                "expr": "celery_queue_length{queue=\"default\"}"
              }
            ]
          },
          {
            "title": "Database Connections",
            "targets": [
              {
                "expr": "pg_stat_database_numbackends{datname=\"yemen_market_v2\"}"
              }
            ]
          }
        ]
      }
    }
  
  yemen-market-analysis.json: |
    {
      "dashboard": {
        "title": "Yemen Market Integration - Analysis Metrics",
        "panels": [
          {
            "title": "Analysis Success Rate",
            "targets": [
              {
                "expr": "sum(rate(analysis_completed_total{status=\"success\"}[5m])) / sum(rate(analysis_completed_total[5m]))"
              }
            ]
          },
          {
            "title": "Analysis Processing Time Distribution",
            "targets": [
              {
                "expr": "histogram_quantile(0.5, sum(rate(analysis_processing_duration_seconds_bucket[5m])) by (le, analysis_type))"
              }
            ]
          },
          {
            "title": "Data Source Freshness",
            "targets": [
              {
                "expr": "time() - max(data_last_updated_timestamp) by (data_source)"
              }
            ]
          },
          {
            "title": "Model Performance Metrics",
            "targets": [
              {
                "expr": "model_prediction_accuracy{model_type=~\".*\"}"
              }
            ]
          }
        ]
      }
    }

---
# ServiceMonitor for Prometheus Operator
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: yemen-market-api
  namespace: yemen-market-v2
spec:
  selector:
    matchLabels:
      app: yemen-market-api
  endpoints:
    - port: http
      path: /metrics
      interval: 30s
      scrapeTimeout: 10s

---
# PrometheusRule for Prometheus Operator
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: yemen-market-rules
  namespace: yemen-market-v2
spec:
  groups:
    - name: yemen_market_recording_rules
      interval: 30s
      rules:
        - record: instance:http_requests:rate5m
          expr: |
            sum(rate(http_requests_total[5m])) by (instance, job, method, status)
        
        - record: job:http_request_duration:p95_5m
          expr: |
            histogram_quantile(0.95,
              sum(rate(http_request_duration_seconds_bucket[5m])) by (job, le)
            )
        
        - record: job:analysis_success_rate:5m
          expr: |
            sum(rate(analysis_completed_total{status="success"}[5m])) by (job)
            /
            sum(rate(analysis_completed_total[5m])) by (job)