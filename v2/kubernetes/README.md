# Yemen Market Integration V2 - Kubernetes Production Configuration

This directory contains production-ready Kubernetes configurations for deploying the Yemen Market Integration V2 application.

## Overview

The deployment includes:
- High-availability API service with horizontal pod autoscaling
- Distributed worker deployment for analysis processing
- PostgreSQL StatefulSet with backup strategies
- Redis with Sentinel for high availability
- Comprehensive monitoring with Prometheus and Grafana
- Network policies for security
- Sealed secrets management
- Progressive deployment strategies

## Prerequisites

- Kubernetes 1.28+
- kubectl configured with cluster access
- Helm 3.x (for cert-manager, sealed-secrets)
- Storage classes: `fast-ssd` for databases
- Ingress controller (NGINX recommended)
- Prometheus Operator (optional but recommended)

## Quick Start

1. Create namespace:
```bash
kubectl apply -f namespace.yaml
```

2. Install prerequisites:
```bash
# Install cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Install sealed-secrets controller
kubectl apply -f https://github.com/bitnami-labs/sealed-secrets/releases/download/v0.24.0/controller.yaml

# Install Prometheus Operator (optional)
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm install kube-prometheus-stack prometheus-community/kube-prometheus-stack -n monitoring
```

3. Configure secrets:
```bash
# Create actual secrets file (do not commit!)
cp secrets.yaml secrets.prod.yaml
# Edit secrets.prod.yaml with real values

# For sealed secrets:
kubeseal --format=yaml < secrets.prod.yaml > sealed-secrets.yaml
```

4. Deploy core infrastructure:
```bash
kubectl apply -f configmap.yaml
kubectl apply -f sealed-secrets.yaml
kubectl apply -f postgres.yaml
kubectl apply -f redis-ha.yaml
```

5. Deploy application:
```bash
kubectl apply -f api-deployment.yaml
kubectl apply -f worker-deployment.yaml
kubectl apply -f worker-hpa.yaml
```

6. Configure networking:
```bash
kubectl apply -f network-policies.yaml
kubectl apply -f ingress.yaml
```

7. Setup monitoring:
```bash
kubectl apply -f monitoring-enhanced.yaml
```

8. Configure backups:
```bash
kubectl apply -f backup-strategy.yaml
```

## Architecture

### API Deployment
- **Replicas**: 3-10 (auto-scaling)
- **Resources**: 512Mi-1Gi memory, 250m-1000m CPU
- **Health checks**: Startup, liveness, and readiness probes
- **Security**: Non-root user, read-only filesystem, dropped capabilities
- **Features**: Rolling updates, pod disruption budget, anti-affinity

### Worker Deployment
- **Replicas**: 2-20 (auto-scaling based on queue length)
- **Resources**: 1Gi-2Gi memory, 500m-2000m CPU
- **Scaling metrics**: CPU, memory, queue length, task failure rate
- **Features**: Graceful shutdown, startup probes

### Database (PostgreSQL)
- **Type**: StatefulSet with persistent storage
- **Storage**: 20Gi SSD
- **Backup**: Twice daily to S3
- **High Availability**: Support for read replicas
- **Security**: SCRAM-SHA-256 authentication

### Cache (Redis)
- **Type**: StatefulSet with Sentinel for HA
- **Replicas**: 1 master, 2 replicas, 3 sentinels
- **Storage**: 10Gi SSD per instance
- **Persistence**: AOF enabled
- **Backup**: Every 6 hours to S3

### Monitoring
- **Prometheus**: Metrics collection and alerting
- **Grafana**: Visualization dashboards
- **Alertmanager**: Alert routing to Slack/PagerDuty
- **Custom metrics**: Business and SLO metrics

## Deployment Strategies

### Canary Deployments (Recommended)
Uses Flagger for progressive delivery:
```bash
kubectl apply -f deployment-strategy.yaml
```

Features:
- Gradual traffic shifting (10% → 20% → 40% → 60% → 80% → 100%)
- Automated rollback on metric degradation
- Integration with load testing

### Blue-Green Deployments
Alternative zero-downtime deployment:
- Maintains two identical production environments
- Instant rollback capability
- Suitable for major version updates

## Security

### Network Policies
- Default deny all traffic
- Explicit allow rules for required communication
- Separate policies per component
- Blocks access to cloud metadata services

### Secrets Management
Options provided:
1. **Sealed Secrets**: Encrypted secrets in Git
2. **External Secrets Operator**: Integration with AWS Secrets Manager/Azure Key Vault
3. **Standard K8s Secrets**: For development only

### Pod Security
- Non-root containers
- Read-only root filesystem
- Dropped Linux capabilities
- Security contexts enforced

## Monitoring and Alerting

### Metrics
- API request rates and latencies
- Worker queue lengths and processing times
- Database connections and query performance
- Resource utilization (CPU, memory, disk)

### Alerts
Configured alerts include:
- SLO breaches (availability < 99.9%, latency > 500ms)
- High error rates
- Resource exhaustion warnings
- Data freshness violations
- Analysis failure rates

### Dashboards
Pre-configured Grafana dashboards:
- System Overview
- API Performance
- Analysis Metrics
- Database Performance
- Worker Queue Status

## Backup and Recovery

### Backup Schedule
- **PostgreSQL**: Twice daily (2 AM, 2 PM)
- **Redis**: Every 6 hours
- **Full cluster**: Daily via Velero
- **Data exports**: Weekly (Parquet format)

### Backup Storage
- S3 with lifecycle policies
- Transition to Glacier after 30 days
- 1-year retention for PostgreSQL
- 30-day retention for Redis

### Disaster Recovery
- Monthly DR tests (15th of each month)
- Automated restore verification
- Slack notifications for backup status

## Scaling

### Horizontal Pod Autoscaling
API pods scale based on:
- CPU utilization (70%)
- Memory utilization (80%)
- Request rate (100 req/s per pod)
- 95th percentile latency (500ms)
- Queue length (1000 items)

Worker pods scale based on:
- CPU utilization (80%)
- Memory utilization (85%)
- Queue length (50 items per worker)
- Task processing rate (10 tasks/s)
- Task failure rate (< 10%)

### Vertical Pod Autoscaling
Automatically adjusts resource requests/limits based on actual usage.

## Troubleshooting

### Common Issues

1. **Pods not starting**
   - Check secrets are properly configured
   - Verify storage classes exist
   - Check resource quotas

2. **Database connection errors**
   - Verify network policies
   - Check PostgreSQL is ready
   - Validate connection string in secrets

3. **High memory usage**
   - Review VPA recommendations
   - Check for memory leaks
   - Adjust worker concurrency

4. **Backup failures**
   - Verify S3 credentials
   - Check S3 bucket permissions
   - Review backup job logs

### Useful Commands

```bash
# Check pod status
kubectl get pods -n yemen-market-v2

# View pod logs
kubectl logs -n yemen-market-v2 <pod-name> --tail=100 -f

# Describe pod for events
kubectl describe pod -n yemen-market-v2 <pod-name>

# Check HPA status
kubectl get hpa -n yemen-market-v2

# View current metrics
kubectl top pods -n yemen-market-v2

# Test service connectivity
kubectl run -it --rm debug --image=busybox --restart=Never -n yemen-market-v2 -- sh
```

## Maintenance

### Regular Tasks
- Review and rotate secrets (monthly)
- Update container images
- Check backup integrity
- Review monitoring alerts
- Update dependencies

### Upgrade Procedure
1. Test in staging environment
2. Create backup
3. Apply canary deployment
4. Monitor metrics
5. Complete rollout or rollback

## Support

For issues or questions:
- Check application logs
- Review Kubernetes events
- Consult monitoring dashboards
- Contact DevOps team

## License

This configuration is part of the Yemen Market Integration project.