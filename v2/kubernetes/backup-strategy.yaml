# Comprehensive Backup Strategy for Yemen Market Integration
---
# PostgreSQL Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgres-backup
  namespace: yemen-market-v2
spec:
  schedule: "0 2,14 * * *"  # Twice daily at 2 AM and 2 PM
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      backoffLimit: 3
      template:
        metadata:
          labels:
            job: backup
            app: postgres-backup
        spec:
          restartPolicy: OnFailure
          securityContext:
            runAsNonRoot: true
            runAsUser: 999
            fsGroup: 999
          initContainers:
            - name: wait-for-db
              image: busybox:1.35
              command: ['sh', '-c', 'until nc -z postgres-service 5432; do echo waiting for db; sleep 2; done']
          containers:
            - name: postgres-backup
              image: postgres:15-alpine
              command:
                - /bin/bash
                - -c
                - |
                  set -e
                  echo "Starting backup at $(date)"
                  
                  # Set backup filename with timestamp
                  BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
                  BACKUP_FILE="yemen_market_v2_${BACKUP_DATE}.sql.gz"
                  
                  # Perform backup
                  PGPASSWORD=$POSTGRES_PASSWORD pg_dump \
                    -h postgres-service \
                    -U yemen_market \
                    -d yemen_market_v2 \
                    --verbose \
                    --no-owner \
                    --no-acl \
                    --clean \
                    --if-exists \
                    | gzip > /backup/${BACKUP_FILE}
                  
                  # Upload to S3
                  aws s3 cp /backup/${BACKUP_FILE} \
                    s3://${S3_BUCKET_NAME}/postgres-backups/${BACKUP_FILE} \
                    --storage-class STANDARD_IA
                  
                  # Create restore point marker
                  echo "Backup completed: ${BACKUP_FILE}" > /backup/latest.txt
                  aws s3 cp /backup/latest.txt \
                    s3://${S3_BUCKET_NAME}/postgres-backups/latest.txt
                  
                  # Cleanup old local backups (keep last 7 days)
                  find /backup -name "*.sql.gz" -mtime +7 -delete
                  
                  # Send notification
                  curl -X POST -H 'Content-type: application/json' \
                    --data "{\"text\":\"PostgreSQL backup completed: ${BACKUP_FILE}\"}" \
                    "$SLACK_WEBHOOK_URL" || true
                  
                  echo "Backup completed successfully"
              env:
                - name: POSTGRES_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: DB_PASSWORD
                - name: AWS_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: AWS_ACCESS_KEY_ID
                - name: AWS_SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: AWS_SECRET_ACCESS_KEY
                - name: S3_BUCKET_NAME
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: S3_BUCKET_NAME
                - name: SLACK_WEBHOOK_URL
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: SLACK_WEBHOOK_URL
              volumeMounts:
                - name: backup
                  mountPath: /backup
              resources:
                requests:
                  memory: "256Mi"
                  cpu: "100m"
                limits:
                  memory: "1Gi"
                  cpu: "500m"
          volumes:
            - name: backup
              emptyDir:
                sizeLimit: 10Gi

---
# Redis Backup CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: redis-backup
  namespace: yemen-market-v2
spec:
  schedule: "30 */6 * * *"  # Every 6 hours at 30 minutes past
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      backoffLimit: 3
      template:
        metadata:
          labels:
            job: backup
            app: redis-backup
        spec:
          restartPolicy: OnFailure
          containers:
            - name: redis-backup
              image: redis:7-alpine
              command:
                - /bin/sh
                - -c
                - |
                  set -e
                  echo "Starting Redis backup at $(date)"
                  
                  # Set backup filename
                  BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
                  BACKUP_FILE="redis_${BACKUP_DATE}.rdb"
                  
                  # Trigger BGSAVE
                  redis-cli -h redis-service -a $REDIS_PASSWORD BGSAVE
                  
                  # Wait for backup to complete
                  while [ $(redis-cli -h redis-service -a $REDIS_PASSWORD LASTSAVE) -eq $(redis-cli -h redis-service -a $REDIS_PASSWORD LASTSAVE) ]; do
                    echo "Waiting for BGSAVE to complete..."
                    sleep 1
                  done
                  
                  # Copy the dump file
                  redis-cli -h redis-service -a $REDIS_PASSWORD --rdb /backup/${BACKUP_FILE}
                  
                  # Compress the backup
                  gzip /backup/${BACKUP_FILE}
                  
                  # Upload to S3
                  aws s3 cp /backup/${BACKUP_FILE}.gz \
                    s3://${S3_BUCKET_NAME}/redis-backups/${BACKUP_FILE}.gz \
                    --storage-class STANDARD_IA
                  
                  echo "Redis backup completed: ${BACKUP_FILE}.gz"
              env:
                - name: REDIS_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: REDIS_PASSWORD
                - name: AWS_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: AWS_ACCESS_KEY_ID
                - name: AWS_SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: AWS_SECRET_ACCESS_KEY
                - name: S3_BUCKET_NAME
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: S3_BUCKET_NAME
              volumeMounts:
                - name: backup
                  mountPath: /backup
              resources:
                requests:
                  memory: "128Mi"
                  cpu: "100m"
                limits:
                  memory: "512Mi"
                  cpu: "250m"
          volumes:
            - name: backup
              emptyDir:
                sizeLimit: 5Gi

---
# Velero Backup Schedule for Cluster-Level Backups
apiVersion: velero.io/v1
kind: Schedule
metadata:
  name: yemen-market-daily-backup
  namespace: velero
spec:
  schedule: "0 3 * * *"  # Daily at 3 AM
  template:
    includedNamespaces:
      - yemen-market-v2
    includedResources:
      - "*"
    excludedResources:
      - events
      - events.events.k8s.io
    storageLocation: default
    volumeSnapshotLocations:
      - default
    ttl: 720h  # Keep backups for 30 days
    hooks:
      resources:
        - name: postgres-freeze
          includedNamespaces:
            - yemen-market-v2
          labelSelector:
            matchLabels:
              app: postgres
          pre:
            - exec:
                container: postgres
                command:
                  - /bin/bash
                  - -c
                  - "psql -U yemen_market -c 'CHECKPOINT;'"
                onError: Continue
                timeout: 30s

---
# Application Data Export Job
apiVersion: batch/v1
kind: CronJob
metadata:
  name: data-export
  namespace: yemen-market-v2
spec:
  schedule: "0 4 * * 1"  # Weekly on Monday at 4 AM
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
            - name: data-exporter
              image: yemen-market-v2:latest
              command:
                - python
                - -m
                - src.infrastructure.backup.export_data
              args:
                - --format=parquet
                - --compress=snappy
                - --include-metadata
              env:
                - name: DATABASE_URL
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: DATABASE_URL
                - name: AWS_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: AWS_ACCESS_KEY_ID
                - name: AWS_SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: AWS_SECRET_ACCESS_KEY
                - name: S3_BUCKET_NAME
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: S3_BUCKET_NAME
              resources:
                requests:
                  memory: "2Gi"
                  cpu: "1000m"
                limits:
                  memory: "4Gi"
                  cpu: "2000m"

---
# Disaster Recovery Test Job
apiVersion: batch/v1
kind: CronJob
metadata:
  name: dr-test
  namespace: yemen-market-v2
spec:
  schedule: "0 5 15 * *"  # Monthly on the 15th at 5 AM
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          restartPolicy: OnFailure
          containers:
            - name: dr-tester
              image: yemen-market-v2:latest
              command:
                - python
                - -m
                - src.infrastructure.backup.test_restore
              args:
                - --test-db=yemen_market_dr_test
                - --verify-data
                - --cleanup
              env:
                - name: DATABASE_URL
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: DATABASE_URL
                - name: AWS_ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: AWS_ACCESS_KEY_ID
                - name: AWS_SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: AWS_SECRET_ACCESS_KEY
                - name: S3_BUCKET_NAME
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: S3_BUCKET_NAME
                - name: SLACK_WEBHOOK_URL
                  valueFrom:
                    secretKeyRef:
                      name: yemen-market-secrets
                      key: SLACK_WEBHOOK_URL
              resources:
                requests:
                  memory: "1Gi"
                  cpu: "500m"
                limits:
                  memory: "2Gi"
                  cpu: "1000m"

---
# S3 Lifecycle Policy for Backup Retention
apiVersion: v1
kind: ConfigMap
metadata:
  name: s3-lifecycle-policy
  namespace: yemen-market-v2
data:
  policy.json: |
    {
      "Rules": [
        {
          "Id": "PostgresBackupRetention",
          "Status": "Enabled",
          "Prefix": "postgres-backups/",
          "Transitions": [
            {
              "Days": 7,
              "StorageClass": "STANDARD_IA"
            },
            {
              "Days": 30,
              "StorageClass": "GLACIER"
            }
          ],
          "Expiration": {
            "Days": 365
          },
          "NoncurrentVersionExpiration": {
            "NoncurrentDays": 30
          }
        },
        {
          "Id": "RedisBackupRetention",
          "Status": "Enabled",
          "Prefix": "redis-backups/",
          "Transitions": [
            {
              "Days": 3,
              "StorageClass": "STANDARD_IA"
            }
          ],
          "Expiration": {
            "Days": 30
          }
        },
        {
          "Id": "DataExportRetention",
          "Status": "Enabled",
          "Prefix": "data-exports/",
          "Transitions": [
            {
              "Days": 30,
              "StorageClass": "GLACIER"
            }
          ],
          "Expiration": {
            "Days": 730
          }
        }
      ]
    }