"""Example usage of V2 PostgreSQL repository layer."""

import asyncio
import logging
from datetime import datetime, timedelta
from decimal import Decimal

from v2.src.infrastructure.persistence import (
    RepositoryFactory, DatabaseConfig, initialize_persistence_layer
)
from v2.src.core.domain.market.entities import Market, PriceObservation
from v2.src.core.domain.market.value_objects import (
    MarketId, Coordinates, MarketType, Commodity, Price
)
from v2.src.core.domain.conflict.entities import ConflictEvent
from v2.src.core.domain.conflict.value_objects import (
    ConflictEventId, EventType, ConflictLocation, FatalityCount
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def demonstrate_repository_usage():
    """Demonstrate repository layer functionality."""
    
    # 1. Initialize persistence layer
    logger.info("Initializing persistence layer...")
    
    config = DatabaseConfig(
        host="localhost",
        port=5432,
        database="yemen_market_v2_demo",
        username="postgres",
        password="password"
    )
    
    factory = await initialize_persistence_layer(config)
    
    # 2. Run migrations
    logger.info("Running database migrations...")
    migration_success = await factory.run_migrations()
    if not migration_success:
        logger.error("Migration failed")
        return
    
    # 3. Health check
    logger.info("Performing health check...")
    health = await factory.health_check()
    logger.info(f"Health status: {health}")
    
    # 4. Demonstrate repository operations
    async with factory.create_unit_of_work() as uow:
        
        # Create commodities
        logger.info("Creating commodities...")
        commodities = [
            Commodity(
                code='WHEAT',
                name='Wheat',
                category='Cereals',
                standard_unit='kg'
            ),
            Commodity(
                code='RICE',
                name='Rice (Imported)',
                category='Cereals',
                standard_unit='kg'
            )
        ]
        
        await uow.commodities.save_batch(commodities)
        
        # Create markets
        logger.info("Creating markets...")
        markets = [
            Market(
                market_id=MarketId("YE001"),
                name="Sana'a Central Market",
                coordinates=Coordinates(latitude=15.3694, longitude=44.1910),
                market_type=MarketType.WHOLESALE,
                governorate="Sana'a",
                district="Old City",
                active_since=datetime(2019, 1, 1),
                active_until=None
            ),
            Market(
                market_id=MarketId("YE002"),
                name="Aden Port Market",
                coordinates=Coordinates(latitude=12.7797, longitude=45.0367),
                market_type=MarketType.PORT,
                governorate="Aden",
                district="Crater",
                active_since=datetime(2019, 1, 1),
                active_until=None
            )
        ]
        
        for market in markets:
            await uow.markets.save(market)
        
        # Create price observations
        logger.info("Creating price observations...")
        observations = []
        
        for i in range(30):  # 30 days of data
            for market in markets:
                for commodity in commodities:
                    obs = PriceObservation(
                        market_id=market.market_id,
                        commodity=commodity,
                        price=Price(
                            amount=Decimal(f"{100 + i * 2}.50"),
                            currency="YER",
                            unit=commodity.standard_unit
                        ),
                        observed_date=datetime(2024, 1, 1) + timedelta(days=i),
                        source="WFP",
                        quality="standard",
                        observations_count=1
                    )
                    observations.append(obs)
        
        await uow.prices.save_batch(observations)
        
        # Create conflict events
        logger.info("Creating conflict events...")
        events = []
        
        for i in range(10):
            event = ConflictEvent(
                event_id=ConflictEventId(f"ACLED_{i:04d}"),
                event_type=EventType.BATTLES,
                sub_event_type="Armed clash",
                event_date=datetime(2024, 1, 1) + timedelta(days=i * 3),
                location=ConflictLocation(
                    latitude=15.3694 + (i * 0.01),
                    longitude=44.1910 + (i * 0.01),
                    governorate="Sana'a",
                    district=f"District {i}"
                ),
                fatality_count=FatalityCount(i + 1),
                notes=f"Test conflict event {i}",
                source="ACLED",
                source_scale="National"
            )
            events.append(event)
        
        await uow.conflict_events.save_batch(events)
        
        # Commit transaction
        await uow.commit()
        logger.info("All data saved successfully")
    
    # 5. Demonstrate queries
    logger.info("Demonstrating queries...")
    
    async with factory.create_unit_of_work() as uow:
        
        # Find markets by governorate
        sanaa_markets = await uow.markets.find_by_governorate("Sana'a")
        logger.info(f"Found {len(sanaa_markets)} markets in Sana'a")
        
        # Find markets by control zone
        houthi_markets = await uow.markets.find_by_control_zone("houthi")
        logger.info(f"Found {len(houthi_markets)} markets in Houthi-controlled areas")
        
        # Find price observations
        wheat = await uow.commodities.find_by_code('WHEAT')
        if wheat and sanaa_markets:
            prices = await uow.prices.find_by_market_and_commodity(
                sanaa_markets[0].market_id,
                wheat,
                datetime(2024, 1, 1),
                datetime(2024, 1, 31)
            )
            logger.info(f"Found {len(prices)} wheat price observations in Sana'a")
            
            # Get price statistics
            stats = await uow.prices.get_price_statistics(
                sanaa_markets[0].market_id,
                wheat,
                datetime(2024, 1, 1),
                datetime(2024, 1, 31)
            )
            logger.info(f"Price statistics: {stats}")
        
        # Find conflict events
        conflict_events = await uow.conflict_events.find_by_governorate_and_date_range(
            "Sana'a",
            datetime(2024, 1, 1),
            datetime(2024, 1, 31)
        )
        logger.info(f"Found {len(conflict_events)} conflict events in Sana'a")
        
        # Get aggregated conflict statistics
        conflict_stats = await uow.conflict_events.get_aggregated_statistics(
            governorate="Sana'a",
            start_date=datetime(2024, 1, 1),
            end_date=datetime(2024, 1, 31),
            groupby_period='week'
        )
        logger.info(f"Conflict statistics: {conflict_stats}")
    
    # 6. Get database statistics
    logger.info("Getting database statistics...")
    db_stats = await factory.get_database_statistics()
    logger.info(f"Database statistics: {db_stats}")
    
    # 7. Cleanup
    logger.info("Cleaning up...")
    await factory.close()
    logger.info("Repository demonstration completed successfully")


async def demonstrate_bulk_operations():
    """Demonstrate bulk operations for performance."""
    
    logger.info("Demonstrating bulk operations...")
    
    config = DatabaseConfig.from_environment()
    factory = await initialize_persistence_layer(config)
    
    # Create large dataset
    markets = []
    for i in range(100):
        market = Market(
            market_id=MarketId(f"BULK_{i:04d}"),
            name=f"Bulk Market {i}",
            coordinates=Coordinates(
                latitude=15.0 + (i * 0.01),
                longitude=44.0 + (i * 0.01)
            ),
            market_type=MarketType.RETAIL,
            governorate="Test Gov",
            district=f"District {i}",
            active_since=datetime(2024, 1, 1),
            active_until=None
        )
        markets.append(market)
    
    # Use bulk operations
    async with factory.create_unit_of_work() as uow:
        from v2.src.infrastructure.persistence.bulk_operations import BulkOperationManager
        
        bulk_manager = BulkOperationManager(uow._connection)
        
        # Bulk insert markets
        start_time = datetime.now()
        stats = await bulk_manager.bulk_insert_markets(markets, on_conflict='ignore')
        end_time = datetime.now()
        
        logger.info(f"Bulk insert completed in {(end_time - start_time).total_seconds():.2f}s")
        logger.info(f"Stats: {stats.processed_records} processed, {stats.success_rate:.1f}% success rate")
    
    await factory.close()


async def demonstrate_error_handling():
    """Demonstrate error handling and resilience."""
    
    logger.info("Demonstrating error handling...")
    
    from v2.src.infrastructure.persistence.error_handling import resilient_operation, CircuitBreaker
    
    # Create a function that sometimes fails
    failure_count = 0
    
    @resilient_operation(max_retries=3)
    async def unreliable_operation():
        nonlocal failure_count
        failure_count += 1
        
        if failure_count < 3:
            raise Exception(f"Simulated failure {failure_count}")
        
        return "Success!"
    
    try:
        result = await unreliable_operation()
        logger.info(f"Resilient operation result: {result}")
    except Exception as e:
        logger.error(f"Operation failed permanently: {e}")
    
    # Demonstrate circuit breaker
    circuit_breaker = CircuitBreaker(failure_threshold=2, timeout_seconds=5)
    
    @circuit_breaker
    async def circuit_protected_operation():
        raise Exception("Always fails")
    
    # This will trigger the circuit breaker
    for i in range(5):
        try:
            await circuit_protected_operation()
        except Exception as e:
            logger.info(f"Attempt {i + 1}: {e}")


if __name__ == "__main__":
    # Run demonstrations
    asyncio.run(demonstrate_repository_usage())
    asyncio.run(demonstrate_bulk_operations())
    asyncio.run(demonstrate_error_handling())