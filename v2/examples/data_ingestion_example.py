"""Example usage of the V2 data ingestion service.

This example demonstrates how to configure and use the data ingestion pipeline
for processing WFP, ACLED, and ACAPS data.
"""

import asyncio
from datetime import datetime, timedelta
from pathlib import Path

from application.services.data_ingestion_service import DataIngestionService
from application.services.ingestion_orchestrator import (
    IngestionOrchestrator, IngestionPriority
)
from infrastructure.processors.wfp_processor import WFPProcessor, WFPProcessingConfig
from infrastructure.processors.acled_processor import ACLEDProcessor, ACLEDProcessingConfig
from infrastructure.processors.acaps_processor import ACAPSProcessor, ACAPSProcessingConfig
from infrastructure.monitoring.data_quality_monitor import DataQualityMonitor
from infrastructure.observability.metrics import MetricsCollector
from infrastructure.messaging.event_bus import EventBus
from infrastructure.caching.memory_cache import MemoryCache
from shared.container import Container


async def setup_ingestion_service() -> DataIngestionService:
    """Setup and configure the data ingestion service."""
    
    # Initialize infrastructure services
    metrics = MetricsCollector()
    event_bus = EventBus()
    cache = MemoryCache()
    container = Container()
    
    # Configure processors
    wfp_config = WFPProcessingConfig(
        min_market_coverage=0.3,
        outlier_threshold=3.0,
        enable_quality_checks=True,
        key_commodities=[
            'Wheat', 'Wheat Flour', 'Rice (Imported)', 'Sugar', 
            'Oil (Vegetable)', 'Beans (Kidney Red)', 'Salt'
        ]
    )
    
    acled_config = ACLEDProcessingConfig(
        spatial_radius_km=50.0,
        temporal_window_days=30,
        enable_spatial_analysis=True,
        enable_quality_checks=True
    )
    
    acaps_config = ACAPSProcessingConfig(
        data_directory=Path("data/raw/acaps"),
        temporal_resolution="monthly",
        enable_change_detection=True,
        enable_quality_checks=True
    )
    
    # Create processors
    wfp_processor = WFPProcessor(wfp_config, metrics)
    acled_processor = ACLEDProcessor(acled_config, metrics)
    acaps_processor = ACAPSProcessor(acaps_config, metrics)
    
    # Register processors in container
    container.register_service("wfp_processor", wfp_processor)
    container.register_service("acled_processor", acled_processor)
    container.register_service("acaps_processor", acaps_processor)
    
    # Create repositories (mock for example)
    from unittest.mock import AsyncMock
    market_repo = AsyncMock()
    price_repo = AsyncMock()
    conflict_repo = AsyncMock()
    geography_repo = AsyncMock()
    
    # Create external clients (mock for example)
    wfp_client = AsyncMock()
    acled_client = AsyncMock()
    
    # Create ingestion service
    ingestion_service = DataIngestionService(
        market_repository=market_repo,
        price_repository=price_repo,
        conflict_repository=conflict_repo,
        geography_repository=geography_repo,
        wfp_client=wfp_client,
        acled_client=acled_client,
        metrics=metrics,
        container=container
    )
    
    return ingestion_service


async def basic_ingestion_example():
    """Example of basic data ingestion."""
    
    print("=== Basic Data Ingestion Example ===")
    
    # Setup service
    ingestion_service = await setup_ingestion_service()
    
    # Define time range
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=30)
    
    print(f"Ingesting data from {start_date.date()} to {end_date.date()}")
    
    try:
        # Ingest all sources
        results = await ingestion_service.ingest_all_sources(
            start_date=start_date,
            end_date=end_date,
            force_refresh=True
        )
        
        # Display results
        print("\nIngestion Results:")
        print("-" * 50)
        
        for source, result in results.items():
            status = "✅ SUCCESS" if result.success else "❌ FAILED"
            print(f"{source.upper()}: {status}")
            print(f"  Records processed: {result.records_processed}")
            print(f"  Records saved: {result.records_saved}")
            print(f"  Processing time: {result.processing_time_seconds:.1f}s")
            print(f"  Quality score: {result.data_quality_score:.1%}")
            
            if result.errors:
                print(f"  Errors: {len(result.errors)}")
                for error in result.errors[:3]:  # Show first 3 errors
                    print(f"    - {error}")
            print()
        
    except Exception as e:
        print(f"Ingestion failed: {e}")


async def orchestrated_ingestion_example():
    """Example of orchestrated ingestion with job management."""
    
    print("=== Orchestrated Ingestion Example ===")
    
    # Setup services
    ingestion_service = await setup_ingestion_service()
    metrics = MetricsCollector()
    event_bus = EventBus()
    cache = MemoryCache()
    
    # Create orchestrator
    orchestrator = IngestionOrchestrator(
        data_ingestion_service=ingestion_service,
        metrics=metrics,
        event_bus=event_bus,
        cache=cache
    )
    
    # Start orchestrator
    await orchestrator.start()
    
    try:
        print("Submitting ingestion jobs...")
        
        # Submit individual jobs with different priorities
        wfp_job_id = await orchestrator.submit_job(
            source="wfp",
            priority=IngestionPriority.HIGH,
            start_date=datetime.utcnow() - timedelta(days=7)
        )
        
        acled_job_id = await orchestrator.submit_job(
            source="acled",
            priority=IngestionPriority.NORMAL,
            start_date=datetime.utcnow() - timedelta(days=30)
        )
        
        # Submit batch job
        batch_job_ids = await orchestrator.submit_batch_job(
            sources=["acaps", "geography"],
            priority=IngestionPriority.LOW
        )
        
        all_job_ids = [wfp_job_id, acled_job_id] + batch_job_ids
        
        print(f"Submitted {len(all_job_ids)} jobs")
        
        # Monitor job progress
        print("\nMonitoring job progress...")
        
        completed_jobs = []
        max_wait_time = 300  # 5 minutes
        waited = 0
        
        while len(completed_jobs) < len(all_job_ids) and waited < max_wait_time:
            for job_id in all_job_ids:
                if job_id not in completed_jobs:
                    job = await orchestrator.get_job_status(job_id)
                    if job and job.is_complete:
                        completed_jobs.append(job_id)
                        status = "✅" if job.status.value == "completed" else "❌"
                        print(f"  Job {job.source} {status} in {job.duration_seconds}s")
            
            await asyncio.sleep(2)
            waited += 2
        
        # Display final status
        print(f"\nCompleted {len(completed_jobs)}/{len(all_job_ids)} jobs")
        
        # Get active jobs
        active_jobs = await orchestrator.get_active_jobs()
        if active_jobs:
            print(f"Still running: {len(active_jobs)} jobs")
        
    finally:
        await orchestrator.stop()


async def quality_monitoring_example():
    """Example of data quality monitoring."""
    
    print("=== Data Quality Monitoring Example ===")
    
    # Setup services (mock repositories for example)
    from unittest.mock import AsyncMock
    market_repo = AsyncMock()
    price_repo = AsyncMock()
    conflict_repo = AsyncMock()
    metrics = MetricsCollector()
    event_bus = EventBus()
    
    # Create quality monitor
    quality_monitor = DataQualityMonitor(
        market_repository=market_repo,
        price_repository=price_repo,
        conflict_repository=conflict_repo,
        metrics=metrics,
        event_bus=event_bus
    )
    
    try:
        print("Running quality checks...")
        
        # Run comprehensive quality check
        quality_metrics = await quality_monitor.run_quality_check()
        
        print(f"Generated {len(quality_metrics)} quality metrics")
        
        # Display quality metrics by source
        sources = set(metric.source for metric in quality_metrics.values())
        
        for source in sources:
            source_metrics = [
                metric for metric in quality_metrics.values()
                if metric.source == source
            ]
            
            if source_metrics:
                avg_score = sum(m.current_value for m in source_metrics) / len(source_metrics)
                print(f"\n{source.upper()} Quality: {avg_score:.1%}")
                
                for metric in source_metrics:
                    quality_level = metric.quality_level.value
                    alert_indicator = "⚠️" if metric.alert_severity else "✅"
                    print(f"  {alert_indicator} {metric.name}: {metric.current_value:.1%} ({quality_level})")
        
        # Get quality dashboard
        dashboard = await quality_monitor.get_quality_dashboard()
        
        print(f"\nOverall Quality Score: {dashboard['overall_score']:.1%}")
        
        if dashboard['active_alerts']:
            print(f"Active Alerts: {len(dashboard['active_alerts'])}")
            for alert in dashboard['active_alerts'][:3]:  # Show first 3
                print(f"  - {alert['severity'].upper()}: {alert['title']}")
        else:
            print("No active quality alerts ✅")
        
    except Exception as e:
        print(f"Quality monitoring failed: {e}")


async def scheduled_ingestion_example():
    """Example of scheduled ingestion setup."""
    
    print("=== Scheduled Ingestion Example ===")
    
    # Setup orchestrator
    ingestion_service = await setup_ingestion_service()
    metrics = MetricsCollector()
    event_bus = EventBus()
    cache = MemoryCache()
    
    orchestrator = IngestionOrchestrator(
        data_ingestion_service=ingestion_service,
        metrics=metrics,
        event_bus=event_bus,
        cache=cache
    )
    
    await orchestrator.start()
    
    try:
        print("Creating ingestion schedules...")
        
        # Daily WFP price updates
        daily_schedule_id = await orchestrator.create_schedule(
            name="Daily WFP Price Updates",
            sources=["wfp"],
            cron_expression="0 6 * * *",  # Daily at 6 AM
            config={
                "force_refresh": False,
                "lookback_days": 7
            }
        )
        
        # Weekly comprehensive update
        weekly_schedule_id = await orchestrator.create_schedule(
            name="Weekly Full Data Sync",
            sources=["wfp", "acled", "acaps"],
            cron_expression="0 2 * * 0",  # Weekly on Sunday at 2 AM
            config={
                "force_refresh": True,
                "lookback_days": 30
            }
        )
        
        # Monthly data validation
        monthly_schedule_id = await orchestrator.create_schedule(
            name="Monthly Data Validation",
            sources=["geography"],
            cron_expression="0 1 1 * *",  # Monthly on 1st at 1 AM
            config={
                "validation_only": True
            }
        )
        
        print(f"Created 3 schedules:")
        print(f"  - Daily WFP updates: {daily_schedule_id}")
        print(f"  - Weekly full sync: {weekly_schedule_id}")
        print(f"  - Monthly validation: {monthly_schedule_id}")
        
        # The orchestrator would handle these schedules automatically
        print("\nSchedules are now active and will run automatically")
        
    finally:
        await orchestrator.stop()


async def error_handling_example():
    """Example of error handling and recovery."""
    
    print("=== Error Handling Example ===")
    
    # Setup service with intentional failures
    ingestion_service = await setup_ingestion_service()
    
    # Mock some failures
    ingestion_service.wfp_client.fetch_price_data.side_effect = Exception("Network timeout")
    
    try:
        # Attempt ingestion that will fail
        result = await ingestion_service._ingest_wfp_data(
            start_date=datetime.utcnow() - timedelta(days=7),
            end_date=datetime.utcnow()
        )
        
    except Exception as e:
        print(f"Expected failure occurred: {e}")
        
        # Show how the service handles errors
        print("The service would:")
        print("  1. Log the error with full context")
        print("  2. Update failure metrics")
        print("  3. Emit failure events for monitoring")
        print("  4. Return structured error result")
        print("  5. Allow retry with exponential backoff")


async def main():
    """Run all examples."""
    
    print("Yemen Market Integration - V2 Data Ingestion Examples")
    print("=" * 60)
    
    examples = [
        basic_ingestion_example,
        orchestrated_ingestion_example,
        quality_monitoring_example,
        scheduled_ingestion_example,
        error_handling_example
    ]
    
    for example in examples:
        try:
            await example()
            print("\n" + "=" * 60)
        except Exception as e:
            print(f"Example failed: {e}")
            print("\n" + "=" * 60)


if __name__ == "__main__":
    asyncio.run(main())