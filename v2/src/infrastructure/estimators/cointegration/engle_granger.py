"""Engle-Granger two-step cointegration test implementation."""

from typing import Dict, Any, Tuple
import pandas as pd
import numpy as np
import statsmodels.api as sm
from statsmodels.tsa.stattools import adfuller

from ....core.models.interfaces import EstimationResult, Estimator, Model
from ...logging import Logger
from .helpers.engle_granger_helpers import perform_engle_granger_test

logger = Logger(__name__)


class EngleGrangerEstimator(Estimator):
    """Estimator for the Engle-Granger two-step cointegration test."""
    
    def __init__(self):
        """Initialize Engle-Granger estimator."""
        pass
        
    def estimate(self, model: Model, data: pd.DataFrame) -> EstimationResult:
        """Perform Engle-Granger cointegration test.
        
        Args:
            model: The model specification (not directly used, but for interface compatibility)
            data: DataFrame with two time series for cointegration test.
                  Assumes the first column is y and the second is x.
            
        Returns:
            EstimationResult with test results.
        """
        logger.info("Running Engle-Granger cointegration test...")
        
        if data.shape[1] != 2:
            raise ValueError("Engle-Granger test requires exactly two time series.")
            
        y = data.iloc[:, 0].dropna()
        x = data.iloc[:, 1].dropna()
        
        if len(y) < 10 or len(x) < 10:
            raise ValueError("Insufficient data for Engle-Granger test (need at least 10 observations).")
            
        # Ensure indices are aligned
        common_index = y.index.intersection(x.index)
        y = y.loc[common_index]
        x = x.loc[common_index]

        results = perform_engle_granger_test(y, x)
        
        # Extract relevant info for EstimationResult
        eg_result = results.get('engle_granger', {})
        
        return EstimationResult(
            model_name="Engle-Granger Cointegration Test",
            estimation_method="Two-Step OLS and ADF",
            coefficients={'adf_statistic': eg_result.get('test_statistic', np.nan)},
            standard_errors={},
            t_statistics={},
            p_values={'adf_p_value': eg_result.get('p_value', np.nan)},
            n_observations=len(y),
            diagnostics=results,
            metadata={
                'interpretation': eg_result.get('interpretation', 'N/A'),
                'reject_null': eg_result.get('reject_null', False)
            }
        )
        
    def diagnose(self, model: Model, result: EstimationResult) -> Dict[str, Any]:
        """Diagnostics for Engle-Granger test (returns the test results themselves)."""
        return result.diagnostics
        
    def predict(self, model: Model, result: EstimationResult, new_data: pd.DataFrame) -> pd.Series:
        """Prediction is not applicable for cointegration tests."""
        raise NotImplementedError("Prediction is not applicable for Engle-Granger cointegration test.")
