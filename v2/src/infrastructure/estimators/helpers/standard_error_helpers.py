"""Helper functions for standard error estimation."""

from typing import Any, Dict, List, Optional, Tuple
import pandas as pd
import numpy as np
from scipy import stats
import statsmodels.api as sm


def heteroskedasticity_robust(
    residuals: np.n<PERSON><PERSON>,
    X: np.n<PERSON><PERSON>,
    hc_type: str = 'HC3'
) -> Dict[str, Any]:
    """
    Calculate heteroskedasticity-robust standard errors (<PERSON>'s, HC0-HC3).

    Parameters
    ----------
    residuals : np.ndarray
        Array of model residuals.
    X : np.ndarray
        Array of independent variables (design matrix).
    hc_type : str, optional
        Type of heteroskedasticity-consistent covariance matrix.
        'HC0': <PERSON>'s original
        'HC1': <PERSON><PERSON><PERSON><PERSON> and <PERSON> (1985)
        'HC2': <PERSON><PERSON><PERSON><PERSON> and <PERSON> (1985)
        'HC3': <PERSON><PERSON><PERSON><PERSON> and <PERSON> (1985) (default, recommended for small samples)

    Returns
    -------
    Dict[str, Any]
        Dictionary containing robust standard errors and covariance matrix.
    """
    n_obs, n_params = X.shape
    
    # Calculate hat matrix diagonal (leverages)
    H = X @ np.linalg.pinv(X.T @ X) @ X.T
    h_ii = np.diag(H)
    
    # Calculate sandwich matrix
    if hc_type == 'HC0':
        omega = np.diag(residuals**2)
    elif hc_type == 'HC1':
        omega = np.diag(residuals**2 * (n_obs / (n_obs - n_params)))
    elif hc_type == 'HC2':
        omega = np.diag(residuals**2 / (1 - h_ii))
    elif hc_type == 'HC3':
        omega = np.diag(residuals**2 / (1 - h_ii)**2)
    else:
        raise ValueError(f"Unsupported hc_type: {hc_type}")
    
    # Robust covariance matrix
    X_prime_X_inv = np.linalg.pinv(X.T @ X)
    robust_cov = X_prime_X_inv @ X.T @ omega @ X @ X_prime_X_inv
    
    # Robust standard errors
    robust_se = np.sqrt(np.diag(robust_cov))
    
    return {
        'standard_errors': robust_se,
        'covariance_matrix': robust_cov,
        'hc_type': hc_type
    }


def clustered(
    residuals: np.ndarray,
    X: np.ndarray,
    cluster_ids: np.ndarray
) -> Dict[str, Any]:
    """
    Calculate cluster-robust standard errors.

    Parameters
    ----------
    residuals : np.ndarray
        Array of model residuals.
    X : np.ndarray
        Array of independent variables (design matrix).
    cluster_ids : np.ndarray
        Array of cluster identifiers for each observation.

    Returns
    -------
    Dict[str, Any]
        Dictionary containing clustered standard errors and covariance matrix.
    """
    n_obs, n_params = X.shape
    
    # Group residuals and X by cluster
    unique_clusters = np.unique(cluster_ids)
    G = len(unique_clusters) # Number of clusters
    
    # Calculate score contributions for each cluster
    # U_g = sum_{i in g} X_i * e_i
    score_contributions = np.zeros((G, n_params))
    
    for g_idx, cluster_id in enumerate(unique_clusters):
        cluster_mask = (cluster_ids == cluster_id)
        X_g = X[cluster_mask]
        residuals_g = residuals[cluster_mask]
        
        score_contributions[g_idx] = (X_g.T @ residuals_g)
    
    # Calculate the "meat" of the sandwich estimator
    meat = score_contributions.T @ score_contributions
    
    # Calculate the "bread" of the sandwich estimator
    X_prime_X_inv = np.linalg.pinv(X.T @ X)
    
    # Clustered covariance matrix
    # V_c = (X'X)^-1 * (sum_{g=1}^G U_g U_g') * (X'X)^-1 * (G / (G-1)) * ((N-1)/(N-K))
    # The (G / (G-1)) * ((N-1)/(N-K)) is a small sample adjustment (Stata's default)
    
    adjustment_factor = (G / (G - 1)) * ((n_obs - 1) / (n_obs - n_params))
    
    clustered_cov = X_prime_X_inv @ meat @ X_prime_X_inv * adjustment_factor
    
    # Clustered standard errors
    clustered_se = np.sqrt(np.diag(clustered_cov))
    
    return {
        'standard_errors': clustered_se,
        'covariance_matrix': clustered_cov,
        'n_clusters': G
    }


def driscoll_kraay(
    residuals: np.ndarray,
    X: np.ndarray,
    entity_ids: np.ndarray,
    time_ids: np.ndarray,
    bandwidth: Optional[int] = None
) -> Dict[str, Any]:
    """
    Calculate Driscoll-Kraay (1998) standard errors.

    These standard errors are robust to arbitrary forms of cross-sectional
    and serial correlation.

    Parameters
    ----------
    residuals : np.ndarray
        Array of model residuals.
    X : np.ndarray
        Array of independent variables (design matrix).
    entity_ids : np.ndarray
        Array of entity identifiers for each observation.
    time_ids : np.ndarray
        Array of time identifiers for each observation.
    bandwidth : Optional[int], optional
        Bandwidth for the Newey-West kernel. If None, a default is chosen.

    Returns
    -------
    Dict[str, Any]
        Dictionary containing Driscoll-Kraay standard errors and covariance matrix.
    """
    n_obs, n_params = X.shape
    
    # Create a DataFrame for easier grouping
    df = pd.DataFrame({
        'residuals': residuals,
        'entity_id': entity_ids,
        'time_id': time_ids
    })
    
    # Calculate the score contributions for each time period
    # S_t = sum_{i=1}^N X_{it} * e_{it}
    
    # Ensure X is aligned with residuals and has the same index if possible
    # For simplicity, assume X is already aligned and has the same number of rows as residuals
    
    # Create a temporary DataFrame for X with time_id for grouping
    X_df = pd.DataFrame(X, index=df.index)
    X_df['time_id'] = df['time_id']
    
    # Calculate S_t for each time period
    # S_t is a vector of size (n_params,)
    time_scores = {}
    for time_id_val in np.unique(time_ids):
        time_mask = (df['time_id'] == time_id_val)
        X_t = X_df[time_mask].drop(columns=['time_id']).values
        residuals_t = df['residuals'][time_mask].values
        
        if X_t.shape[0] > 0: # Ensure there are observations for this time period
            time_scores[time_id_val] = X_t.T @ residuals_t
        else:
            time_scores[time_id_val] = np.zeros(n_params) # No observations for this time
            
    # Convert time_scores to a matrix (T x n_params)
    # Ensure the order of time periods is correct
    sorted_time_ids = sorted(time_scores.keys())
    S_t_matrix = np.array([time_scores[t] for t in sorted_time_ids])
    
    # Calculate the long-run covariance matrix of S_t using Newey-West
    # This is the "meat" of the Driscoll-Kraay estimator
    
    # Default bandwidth (Newey-West recommendation)
    if bandwidth is None:
        bandwidth = int(4 * (n_obs / 100)**(2/9)) # Common heuristic
        if bandwidth < 1: bandwidth = 1
    
    # Calculate the Newey-West covariance matrix for S_t
    # G_0 = (1/T) * sum_{t=1}^T S_t S_t' + (1/T) * sum_{l=1}^L w_l * sum_{t=l+1}^T (S_t S_{t-l}' + S_{t-l} S_t')
    # where w_l = 1 - l / (L+1) (Bartlett kernel)
    
    T_actual = S_t_matrix.shape[0]
    
    if T_actual == 0:
        raise ValueError("No valid time periods for Driscoll-Kraay estimation.")

    G_0 = (S_t_matrix.T @ S_t_matrix) / T_actual
    
    meat = G_0.copy()
    for l in range(1, bandwidth + 1):
        if l >= T_actual: # Ensure lag is within bounds
            break
        weight = 1 - (l / (bandwidth + 1))
        
        # Autocovariance term
        gamma_l = (S_t_matrix[l:].T @ S_t_matrix[:-l]) / T_actual
        
        meat += weight * (gamma_l + gamma_l.T)
    
    # Calculate the "bread" of the sandwich estimator
    X_prime_X_inv = np.linalg.pinv(X.T @ X)
    
    # Driscoll-Kraay covariance matrix
    # V_DK = (X'X)^-1 * T * meat * (X'X)^-1
    # Note: The T factor is because meat is averaged by T
    
    driscoll_kraay_cov = X_prime_X_inv @ meat @ X_prime_X_inv * T_actual
    
    # Driscoll-Kraay standard errors
    driscoll_kraay_se = np.sqrt(np.diag(driscoll_kraay_cov))
    
    return {
        'standard_errors': driscoll_kraay_se,
        'covariance_matrix': driscoll_kraay_cov,
        'bandwidth': bandwidth
    }
