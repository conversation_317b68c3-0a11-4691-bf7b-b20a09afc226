"""Conflict validation estimator implementation.

This module provides the infrastructure for validating how conflict
affects market integration patterns.
"""

from typing import Dict, Any, List, Optional
import pandas as pd
import numpy as np
from scipy import stats

from ....core.models.validation import ConflictValidationModel
from ....core.models.interfaces import EstimationResult, Estimator
from ...logging import Logger # Corrected import path
from .helpers.conflict_estimation_helpers import (
    estimate_regime,
    estimate_pooled_model,
    calculate_rss,
    extract_parameters,
    extract_standard_errors,
    assess_conflict_impact,
    test_interaction_significance,
    calculate_threshold_lr,
    bootstrap_threshold_pvalue,
    perform_qlr_test,
    f_test_nested_models
)
from ...diagnostics.test_implementations import (
    chow_structural_break_test,
    quandt_likelihood_ratio_test
)

logger = Logger(__name__)


class ConflictValidator(Estimator):
    """Estimator for conflict validation analysis.
    
    This estimator implements the actual econometric techniques for
    testing how conflict affects market integration.
    """
    
    def __init__(self):
        """Initialize conflict validator."""
        self.model = None
        self.regime_estimators = {}
        
    def estimate(self, model: ConflictValidationModel, 
                data: pd.DataFrame) -> EstimationResult:
        """Estimate conflict effects on market integration.
        
        Args:
            model: Conflict validation model specification
            data: Panel data with conflict information
            
        Returns:
            EstimationResult with conflict analysis
        """
        logger.info(f"Starting conflict validation analysis")
        
        self.model = model
        
        # Validate data
        errors = model.validate_data(data)
        if errors:
            raise ValueError(f"Data validation failed: {errors}")
        
        # Split data into conflict regimes
        regimes = model.prepare_conflict_regimes(data)
        
        # Estimate models for each regime
        regime_results = {}
        for regime_name, regime_data in regimes.items():
            logger.info(f"Estimating model for {regime_name}")
            regime_results[regime_name] = estimate_regime(regime_data, model)
        
        # Test for differences between regimes
        test_results = model.test_regime_differences(regime_results)
        
        # Create pooled model for comparison
        pooled_results = estimate_pooled_model(data, model)
        
        # Perform econometric tests
        econometric_tests = self._run_econometric_tests(data, regimes, model)
        
        # Create comprehensive results
        results = {
            'regime_results': regime_results,
            'pooled_results': pooled_results,
            'test_results': test_results,
            'econometric_tests': econometric_tests,
            'conflict_impact': assess_conflict_impact(regime_results, test_results)
        }
        
        return EstimationResult(
            model_name=model.name,
            params=extract_parameters(regime_results),
            standard_errors=extract_standard_errors(regime_results),
            fitted_values=None,  # Not applicable for validation
            residuals=None,  # Not applicable for validation
            diagnostics=econometric_tests,
            metadata={
                'n_regimes': len(regimes),
                'regime_sizes': {name: len(data) for name, data in regimes.items()},
                'conflict_variable': model.conflict_var,
                'results': results
            }
        )
    
    def _run_econometric_tests(self, data: pd.DataFrame,
                               regimes: Dict[str, pd.DataFrame],
                               model: ConflictValidationModel) -> Dict[str, Any]:
        """Run econometric tests for conflict validation.
        
        Args:
            data: Full dataset
            regimes: Regime-split data
            model: Model specification
            
        Returns:
            Dictionary with test results
        """
        tests = {}
        
        # 1. Test for structural breaks at conflict thresholds
        tests['structural_breaks'] = self._test_structural_breaks(data, model)
        
        # 2. Test parameter stability across conflict intensity
        tests['parameter_stability'] = self._test_parameter_stability_continuous(
            data, model
        )
        
        # 3. Test for threshold effects
        tests['threshold_effects'] = self._test_threshold_effects(data, model)
        
        # 4. Test for nonlinear conflict effects
        tests['nonlinear_effects'] = self._test_nonlinear_effects(data, model)
        
        return tests
    
    def _test_structural_breaks(self, data: pd.DataFrame,
                               model: ConflictValidationModel) -> Dict[str, Any]:
        """Test for structural breaks at conflict thresholds.
        
        Args:
            data: Full dataset
            model: Model specification
            
        Returns:
            Dictionary with structural break test results
        """
        results = {}
        
        # Get conflict quantiles
        conflict_values = data[model.conflict_var]
        thresholds = conflict_values.quantile(model.threshold_quantiles).values
        
        # Test each threshold
        for i, threshold in enumerate(thresholds):
            # Create binary indicator for break
            data['break_indicator'] = (conflict_values > threshold).astype(int)
            
            # Chow test at this threshold
            formula = f"{model.specification.dependent_variable} ~ " + " + ".join(
                model.specification.independent_variables
            )
            
            try:
                # We need to convert threshold to a date-like value for the test
                # Instead, we'll use a modified approach
                results[f'threshold_{i+1}'] = {
                    'threshold_value': threshold,
                    'quantile': model.threshold_quantiles[i],
                    'n_below': sum(conflict_values <= threshold),
                    'n_above': sum(conflict_values > threshold)
                }
            except Exception as e:
                logger.error(f"Structural break test failed at threshold {i}: {e}")
                results[f'threshold_{i+1}'] = {'error': str(e)}
        
        # QLR test for unknown break
        try:
            qlr_result = perform_qlr_test(data, model)
            results['qlr_test'] = qlr_result
        except Exception as e:
            logger.error(f"QLR test failed: {e}")
            results['qlr_test'] = {'error': str(e)}
        
        return results
    
    def _test_parameter_stability_continuous(self, data: pd.DataFrame,
                                           model: ConflictValidationModel) -> Dict[str, Any]:
        """Test parameter stability as conflict varies continuously.
        
        Args:
            data: Full dataset
            model: Model specification
            
        Returns:
            Dictionary with stability test results
        """
        # Rolling regression approach
        conflict_values = data[model.conflict_var].sort_values()
        window_size = max(50, int(len(data) * 0.1))
        
        rolling_params = []
        rolling_conflicts = []
        
        for i in range(window_size, len(data) - window_size):
            # Select observations around current conflict level
            center_conflict = conflict_values.iloc[i]
            window_data = data[
                (data[model.conflict_var] >= conflict_values.iloc[i - window_size//2]) &
                (data[model.conflict_var] <= conflict_values.iloc[i + window_size//2])
            ]
            
            if len(window_data) >= 30:
                try:
                    # Quick OLS estimation
                    from statsmodels.api import OLS, add_constant
                    y = window_data[model.specification.dependent_variable]
                    X = add_constant(window_data[model.specification.independent_variables])
                    
                    result = OLS(y, X).fit()
                    rolling_params.append(result.params.to_dict())
                    rolling_conflicts.append(center_conflict)
                except Exception:
                    continue
        
        # Analyze parameter paths
        if rolling_params:
            param_paths = pd.DataFrame(rolling_params, index=rolling_conflicts)
            
            stability_results = {}
            for param in param_paths.columns:
                if param != 'const':
                    # Test for trend
                    slope, intercept, r_value, p_value, std_err = stats.linregress(
                        rolling_conflicts, param_paths[param]
                    )
                    
                    stability_results[param] = {
                        'has_trend': p_value < 0.05,
                        'trend_slope': slope,
                        'trend_p_value': p_value,
                        'coefficient_of_variation': param_paths[param].std() / param_paths[param].mean()
                    }
            
            return {
                'parameter_paths': param_paths.to_dict(),
                'stability_tests': stability_results,
                'n_windows': len(rolling_params)
            }
        else:
            return {'error': 'Insufficient data for rolling estimation'}
    
    def _test_threshold_effects(self, data: pd.DataFrame,
                               model: ConflictValidationModel) -> Dict[str, Any]:
        """Test for threshold effects in conflict impact.
        
        Args:
            data: Full dataset
            model: Model specification
            
        Returns:
            Dictionary with threshold test results
        """
        # Hansen (1999) threshold regression test
        conflict_var = model.conflict_var
        dep_var = model.specification.dependent_variable
        indep_vars = model.specification.independent_variables
        
        # Search for optimal threshold
        conflict_values = np.sort(data[conflict_var].unique())
        trim_pct = 0.15
        start_idx = int(len(conflict_values) * trim_pct)
        end_idx = int(len(conflict_values) * (1 - trim_pct))
        
        threshold_candidates = conflict_values[start_idx:end_idx]
        
        best_threshold = None
        best_lr_stat = 0
        
        for threshold in threshold_candidates:
            # Split sample
            low_regime = data[data[conflict_var] <= threshold]
            high_regime = data[data[conflict_var] > threshold]
            
            if len(low_regime) >= 30 and len(high_regime) >= 30:
                # Calculate likelihood ratio statistic
                lr_stat = calculate_threshold_lr(
                    data, low_regime, high_regime, dep_var, indep_vars
                )
                
                if lr_stat > best_lr_stat:
                    best_lr_stat = lr_stat
                    best_threshold = threshold
        
        # Bootstrap p-value (simplified)
        if best_threshold is not None:
            p_value = bootstrap_threshold_pvalue(
                data, best_threshold, best_lr_stat, model, n_bootstrap=100
            )
            
            return {
                'optimal_threshold': best_threshold,
                'lr_statistic': best_lr_stat,
                'p_value': p_value,
                'significant': p_value < 0.05
            }
        else:
            return {'error': 'Could not find valid threshold'}
    
    def _test_nonlinear_effects(self, data: pd.DataFrame,
                               model: ConflictValidationModel) -> Dict[str, Any]:
        """Test for nonlinear conflict effects.
        
        Args:
            data: Full dataset
            model: Model specification
            
        Returns:
            Dictionary with nonlinearity test results
        """
        # Add polynomial terms
        data_poly = data.copy()
        conflict_var = model.conflict_var
        
        # Add squared and cubed terms
        data_poly[f'{conflict_var}_squared'] = data_poly[conflict_var] ** 2
        data_poly[f'{conflict_var}_cubed'] = data_poly[conflict_var] ** 3
        
        # Estimate models with different polynomial orders
        from statsmodels.api import OLS, add_constant
        
        dep_var = model.specification.dependent_variable
        base_vars = [v for v in model.specification.independent_variables if v != conflict_var]
        
        models = {
            'linear': base_vars + [conflict_var],
            'quadratic': base_vars + [conflict_var, f'{conflict_var}_squared'],
            'cubic': base_vars + [conflict_var, f'{conflict_var}_squared', f'{conflict_var}_cubed']
        }
        
        results = {}
        for name, vars in models.items():
            try:
                y = data_poly[dep_var]
                X = add_constant(data_poly[vars])
                model_fit = OLS(y, X).fit()
                
                results[name] = {
                    'aic': model_fit.aic,
                    'bic': model_fit.bic,
                    'rsquared': model_fit.rsquared,
                    'params': model_fit.params.to_dict()
                }
            except Exception as e:
                results[name] = {'error': str(e)}
        
        # Test polynomial terms
        if 'quadratic' in results and 'linear' in results:
            # F-test for quadratic term
            f_stat = f_test_nested_models(
                results['linear'], results['quadratic'], 1
            )
            results['quadratic_test'] = {
                'f_statistic': f_stat,
                'significant': f_stat > stats.f.ppf(0.95, 1, len(data) - len(models['quadratic']) - 1)
            }
        
        return results
