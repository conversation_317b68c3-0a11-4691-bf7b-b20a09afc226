"""Dynamic Factor Model estimator implementation.

This module provides the infrastructure for performing Dynamic Factor Model
analysis on time series data.
"""

from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
import statsmodels.api as sm
from statsmodels.tsa.statespace.dynamic_factor import DynamicFactor

from ....core.models.validation import DynamicFactorModel
from ....core.models.interfaces import EstimationResult, Estimator
from ...logging import Logger # Corrected import path

logger = Logger(__name__)


class DynamicFactorAnalyzer(Estimator):
    """Estimator for Dynamic Factor Model.
    
    This estimator implements Dynamic Factor Model to capture time-varying factors
    and structural changes in market integration.
    """
    
    def __init__(self):
        """Initialize Dynamic Factor analyzer."""
        self.model = None
        self.scaler: Optional[StandardScaler] = None
        self.dfm_object: Optional[DynamicFactor] = None
        self.dfm_results: Optional[Any] = None # statsmodels results object
        
    def estimate(self, model: DynamicFactorModel, 
                data: pd.DataFrame) -> EstimationResult:
        """Perform Dynamic Factor Model estimation.
        
        Args:
            model: Dynamic Factor Model specification
            data: Time series data for DFM
            
        Returns:
            EstimationResult with DFM results
        """
        logger.info(f"Starting Dynamic Factor Model estimation with {model.n_factors} factors and AR({model.factor_order})")
        
        self.model = model
        
        # Validate data
        errors = model.validate_data(data)
        if errors:
            raise ValueError(f"Data validation failed: {errors}")
        
        # Prepare data
        prepared_data = model.prepare_data(data)
        
        # Store scaler if standardization was applied
        if model.standardize:
            self.scaler = model.scaler
        
        # Initialize DynamicFactor model
        self.dfm_object = DynamicFactor(
            prepared_data,
            k_factors=model.n_factors,
            factor_order=model.factor_order,
            error_order=0 # For simplicity, no MA component in errors
        )
        
        # Fit the model
        self.dfm_results = self.dfm_object.fit(disp=False) # disp=False to suppress convergence output
        logger.info("Dynamic Factor Model fitted successfully.")
        
        # Extract results
        factor_scores = self.dfm_results.smoothed_state.iloc[self.dfm_object.ssm.k_states - model.n_factors:, :].T
        factor_scores.columns = [f'DynamicFactor_{i+1}' for i in range(model.n_factors)]
        factor_scores.index = prepared_data.index
        
        # Get diagnostics from the model itself
        diagnostics = model.get_diagnostics()
        
        return EstimationResult(
            model_name=model.name,
            estimation_method="Maximum Likelihood",
            coefficients=self.dfm_results.params.to_dict(),
            standard_errors=self.dfm_results.bse.to_dict(),
            t_statistics=self.dfm_results.tvalues.to_dict(),
            p_values=self.dfm_results.pvalues.to_dict(),
            fitted_values=factor_scores,
            residuals=pd.DataFrame(self.dfm_results.resid, index=prepared_data.index, columns=prepared_data.columns),
            diagnostics=diagnostics,
            metadata={
                'n_factors': model.n_factors,
                'factor_order': model.factor_order,
                'standardized': model.standardize,
                'aic': self.dfm_results.aic,
                'bic': self.dfm_results.bic,
                'hqic': self.dfm_results.hqic,
                'results_object': self.dfm_results # Store full results object
            }
        )
    
    def diagnose(self, model: DynamicFactorModel, result: EstimationResult) -> Dict[str, Any]:
        """Run diagnostic tests for Dynamic Factor Model."""
        # The diagnostics are already part of the model's get_diagnostics method
        return model.get_diagnostics()
        
    def predict(self, model: DynamicFactorModel, result: EstimationResult, new_data: pd.DataFrame) -> pd.DataFrame:
        """Generate predictions (factor scores) from the fitted model."""
        if self.dfm_object is None or self.dfm_results is None:
            raise RuntimeError("Estimator must be fitted before prediction.")
            
        logger.info("Generating factor score predictions for new data.")
        
        # Prepare new data using the same scaler if standardization was applied
        numeric_data = new_data.select_dtypes(include=[np.number])
        clean_data = numeric_data.dropna()
        
        if self.scaler:
            scaled_data = self.scaler.transform(clean_data)
            data_for_prediction = pd.DataFrame(
                scaled_data,
                index=clean_data.index,
                columns=clean_data.columns
            )
        else:
            data_for_prediction = clean_data
            
        # To get out-of-sample predictions (forecasts) or filtered states for new data,
        # one would typically use `forecast` or `filter` methods of the results object.
        # For simplicity, we'll use the `predict` method of the model which internally
        # handles the transformation using the fitted PCA object.
        
        # Note: statsmodels DFM `predict` method is for in-sample or forecasting from end of sample.
        # For transforming new data, it's more common to use `filter` or `transform` if available.
        # Since `model.predict` is implemented to return factor scores for new data, we'll use that.
        
        return model.predict(new_data)
