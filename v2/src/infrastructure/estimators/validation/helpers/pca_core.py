"""Core PCA functionality helpers."""

from typing import Dict, <PERSON>, Tu<PERSON>, Optional
import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler

from .....infrastructure.logging import Logger

logger = Logger(__name__)


def prepare_pca_data(data: pd.DataFrame, 
                     handle_missing: str = 'drop',
                     standardize: bool = True) -> <PERSON>ple[pd.DataFrame, StandardScaler]:
    """Prepare data for PCA analysis.
    
    Args:
        data: Raw data
        handle_missing: How to handle missing values ('drop', 'interpolate', 'mean')
        standardize: Whether to standardize the data
        
    Returns:
        Tuple of (prepared_data, scaler)
    """
    # Handle missing values
    if handle_missing == 'drop':
        data_clean = data.dropna()
    elif handle_missing == 'interpolate':
        data_clean = data.interpolate(method='linear', limit_direction='both')
    elif handle_missing == 'mean':
        data_clean = data.fillna(data.mean())
    else:
        raise ValueError(f"Unknown missing value method: {handle_missing}")
    
    if len(data_clean) < len(data):
        logger.warning(f"Dropped {len(data) - len(data_clean)} rows with missing values")
    
    # Standardize if requested
    scaler = StandardScaler() if standardize else None
    if standardize:
        data_array = scaler.fit_transform(data_clean)
        data_clean = pd.DataFrame(
            data_array, 
            index=data_clean.index, 
            columns=data_clean.columns
        )
    
    return data_clean, scaler


def perform_pca(data: pd.DataFrame, 
                n_components: Optional[int] = None,
                variance_threshold: float = 0.95) -> Tuple[PCA, pd.DataFrame, pd.DataFrame]:
    """Perform PCA analysis.
    
    Args:
        data: Standardized data
        n_components: Number of components (None for automatic)
        variance_threshold: Variance threshold for automatic selection
        
    Returns:
        Tuple of (pca_model, components_df, transformed_data)
    """
    # Determine number of components
    if n_components is None:
        # First fit to determine components needed
        pca_temp = PCA()
        pca_temp.fit(data)
        
        # Find components for variance threshold
        cumsum = np.cumsum(pca_temp.explained_variance_ratio_)
        n_components = np.argmax(cumsum >= variance_threshold) + 1
        logger.info(f"Selected {n_components} components for {variance_threshold:.1%} variance")
    
    # Fit final PCA
    pca = PCA(n_components=n_components)
    transformed = pca.fit_transform(data)
    
    # Create component DataFrame
    components_df = pd.DataFrame(
        pca.components_,
        columns=data.columns,
        index=[f'PC{i+1}' for i in range(n_components)]
    )
    
    # Create transformed DataFrame
    transformed_df = pd.DataFrame(
        transformed,
        index=data.index,
        columns=[f'PC{i+1}' for i in range(n_components)]
    )
    
    return pca, components_df, transformed_df


def calculate_loadings(pca: PCA, feature_names: list) -> pd.DataFrame:
    """Calculate and format PCA loadings.
    
    Args:
        pca: Fitted PCA model
        feature_names: Names of original features
        
    Returns:
        DataFrame with loadings
    """
    # Loadings = components * sqrt(eigenvalues)
    loadings = pca.components_.T * np.sqrt(pca.explained_variance_)
    
    loadings_df = pd.DataFrame(
        loadings,
        index=feature_names,
        columns=[f'PC{i+1}' for i in range(pca.n_components_)]
    )
    
    return loadings_df


def determine_significant_components(pca: PCA, method: str = 'kaiser') -> int:
    """Determine number of significant components.
    
    Args:
        pca: Fitted PCA model
        method: Method to use ('kaiser', 'scree', 'variance')
        
    Returns:
        Number of significant components
    """
    if method == 'kaiser':
        # Kaiser criterion: eigenvalues > 1
        return np.sum(pca.explained_variance_ > 1)
    
    elif method == 'scree':
        # Scree test: look for elbow
        variances = pca.explained_variance_ratio_
        if len(variances) > 2:
            # Calculate second derivatives
            second_diff = np.diff(variances, 2)
            # Find elbow (maximum second derivative)
            elbow = np.argmax(second_diff) + 2
            return min(elbow, len(variances))
        return len(variances)
    
    elif method == 'variance':
        # 90% variance explained
        cumsum = np.cumsum(pca.explained_variance_ratio_)
        return np.argmax(cumsum >= 0.9) + 1
    
    else:
        raise ValueError(f"Unknown method: {method}")


def calculate_component_scores(transformed_data: pd.DataFrame,
                             interpretation: Dict[str, str]) -> pd.DataFrame:
    """Calculate interpretable component scores.
    
    Args:
        transformed_data: PCA-transformed data
        interpretation: Component interpretations
        
    Returns:
        DataFrame with named component scores
    """
    scores = transformed_data.copy()
    
    # Rename columns based on interpretation
    rename_map = {}
    for pc, interp in interpretation.items():
        if pc in scores.columns:
            # Create short name from interpretation
            short_name = interp.split(',')[0].strip()
            rename_map[pc] = f"{pc}_{short_name}"
    
    scores = scores.rename(columns=rename_map)
    
    return scores