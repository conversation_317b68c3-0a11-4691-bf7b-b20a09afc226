"""Helper functions for estimating conflict validation models."""

from typing import Dict, Any, List, Optional
import pandas as pd
import numpy as np
from scipy import stats

from .....core.models.validation import ConflictValidationModel
from .....core.models.interfaces import EstimationResult
from ....logging import Logger # Corrected import path
from ....estimators.implementations.panel_estimators import PooledPanelEstimator as PanelEstimator # Corrected import path
from ....estimators.implementations.panel_estimators import VECMEstimator # Corrected import path

logger = Logger(__name__)


def estimate_regime(data: pd.DataFrame, 
                    model: ConflictValidationModel) -> Dict[str, Any]:
    """Estimate model for a single conflict regime.
    
    Args:
        data: Data for this regime
        model: Model specification
        
    Returns:
        Dictionary with regime estimation results
    """
    if 'vecm' in model.specification.model_type.lower():
        estimator = VECMEstimator()
    else:
        estimator = PanelEstimator()
    
    try:
        result = estimator.estimate(model, data)
        
        return {
            'params': result.params,
            'standard_errors': result.standard_errors,
            'n_obs': len(data),
            'rss': calculate_rss(result.residuals) if result.residuals is not None else None,
            'r_squared': result.metadata.get('r_squared'),
            'adjustment_speed': result.metadata.get('adjustment_speed'),
            'estimation_result': result
        }
    except Exception as e:
        logger.error(f"Regime estimation failed: {e}")
        return {
            'error': str(e),
            'n_obs': len(data)
        }


def estimate_pooled_model(data: pd.DataFrame,
                          model: ConflictValidationModel) -> Dict[str, Any]:
    """Estimate pooled model ignoring conflict regimes.
    
    Args:
        data: Full dataset
        model: Model specification
        
    Returns:
        Dictionary with pooled estimation results
    """
    logger.info("Estimating pooled model for comparison")
    
    pooled_data = data.copy()
    
    for var in model.specification.independent_variables:
        if var != model.conflict_var:
            pooled_data[f'{var}_x_{model.conflict_var}'] = (
                pooled_data[var] * pooled_data[model.conflict_var]
            )
    
    pooled_spec = model.specification.copy()
    interaction_vars = [f'{var}_x_{model.conflict_var}' 
                      for var in model.specification.independent_variables 
                      if var != model.conflict_var]
    pooled_spec.independent_variables.extend(interaction_vars)
    
    try:
        estimator = PanelEstimator()
        result = estimator.estimate(model, pooled_data)
        
        interaction_test = test_interaction_significance(
            result, interaction_vars
        )
        
        return {
            'estimation_result': result,
            'interaction_test': interaction_test,
            'rss_pooled': calculate_rss(result.residuals) if result.residuals is not None else None
        }
    except Exception as e:
        logger.error(f"Pooled estimation failed: {e}")
        return {'error': str(e)}


def calculate_rss(residuals: Optional[pd.Series]) -> Optional[float]:
    """Calculate residual sum of squares."""
    if residuals is None:
        return None
    return float(np.sum(residuals ** 2))


def extract_parameters(regime_results: Dict[str, Any]) -> pd.Series:
    """Extract parameters from regime results."""
    all_params = {}
    for regime_name, results in regime_results.items():
        if 'params' in results and results['params'] is not None:
            for param_name, value in results['params'].items():
                all_params[f"{regime_name}_{param_name}"] = value
    
    return pd.Series(all_params)


def extract_standard_errors(regime_results: Dict[str, Any]) -> pd.Series:
    """Extract standard errors from regime results."""
    all_ses = {}
    for regime_name, results in regime_results.items():
        if 'standard_errors' in results and results['standard_errors'] is not None:
            for param_name, value in results['standard_errors'].items():
                all_ses[f"{regime_name}_{param_name}"] = value
    
    return pd.Series(all_ses)


def assess_conflict_impact(regime_results: Dict[str, Any],
                           test_results: Dict[str, Any]) -> Dict[str, Any]:
    """Assess overall impact of conflict on market integration.
    
    Args:
        regime_results: Results by regime
        test_results: Statistical test results
        
    Returns:
        Dictionary with impact assessment
    """
    impact = {
        'significant_differences': False,
        'direction': 'none',
        'magnitude': 0,
        'policy_implications': []
    }
    
    chow_tests = test_results.get('chow_tests', {})
    significant_tests = [test for test in chow_tests.values() 
                       if test.get('reject_equality', False)]
    
    if significant_tests:
        impact['significant_differences'] = True
        
        if 'low_conflict' in regime_results and 'high_conflict' in regime_results:
            low_speed = regime_results['low_conflict'].get('adjustment_speed', 0)
            high_speed = regime_results['high_conflict'].get('adjustment_speed', 0)
            
            if low_speed and high_speed:
                if abs(high_speed) < abs(low_speed):
                    impact['direction'] = 'negative'
                    impact['magnitude'] = (abs(low_speed) - abs(high_speed)) / abs(low_speed)
                    impact['policy_implications'].append(
                        "Conflict reduces market integration speed"
                    )
                else:
                    impact['direction'] = 'positive'
                    impact['magnitude'] = (abs(high_speed) - abs(low_speed)) / abs(low_speed)
                    impact['policy_implications'].append(
                        "Markets adapt to conflict, maintaining integration"
                    )
    
    if impact['magnitude'] > 0.5:
        impact['policy_implications'].append(
            "Strong conflict effects warrant targeted interventions"
        )
    elif impact['magnitude'] > 0.2:
        impact['policy_implications'].append(
            "Moderate conflict effects suggest need for monitoring"
        )
    
    return impact


def test_interaction_significance(result: EstimationResult,
                                  interaction_vars: List[str]) -> Dict[str, Any]:
    """Test joint significance of interaction terms."""
    significant_interactions = []
    for var in interaction_vars:
        if var in result.params.index:
            t_stat = result.params[var] / result.standard_errors[var]
            if abs(t_stat) > 1.96:
                significant_interactions.append(var)
    
    return {
        'n_interactions': len(interaction_vars),
        'n_significant': len(significant_interactions),
        'significant_vars': significant_interactions,
        'jointly_significant': len(significant_interactions) > len(interaction_vars) * 0.3
    }


def calculate_threshold_lr(full_data: pd.DataFrame,
                           regime1_data: pd.DataFrame,
                           regime2_data: pd.DataFrame,
                           dep_var: str,
                           indep_vars: List[str]) -> float:
    """Calculate likelihood ratio statistic for threshold test."""
    from statsmodels.api import OLS, add_constant
    
    try:
        y1 = regime1_data[dep_var]
        X1 = add_constant(regime1_data[indep_vars])
        model1 = OLS(y1, X1).fit()
        
        y2 = regime2_data[dep_var]
        X2 = add_constant(regime2_data[indep_vars])
        model2 = OLS(y2, X2).fit()
        
        y_full = full_data[dep_var]
        X_full = add_constant(full_data[indep_vars])
        model_full = OLS(y_full, X_full).fit()
        
        lr = -2 * (model_full.llf - (model1.llf + model2.llf))
        
        return lr
    except Exception:
        return 0


def bootstrap_threshold_pvalue(data: pd.DataFrame,
                               threshold: float,
                               lr_stat: float,
                               model: ConflictValidationModel,
                               n_bootstrap: int = 100) -> float:
    """Bootstrap p-value for threshold test."""
    bootstrap_stats = []
    
    for _ in range(n_bootstrap):
        bootstrap_data = data.sample(len(data), replace=True)
        
        low_regime = bootstrap_data[bootstrap_data[model.conflict_var] <= threshold]
        high_regime = bootstrap_data[bootstrap_data[model.conflict_var] > threshold]
        
        if len(low_regime) >= 30 and len(high_regime) >= 30:
            boot_lr = calculate_threshold_lr(
                bootstrap_data, low_regime, high_regime,
                model.specification.dependent_variable,
                model.specification.independent_variables
            )
            bootstrap_stats.append(boot_lr)
    
    if bootstrap_stats:
        p_value = sum(s >= lr_stat for s in bootstrap_stats) / len(bootstrap_stats)
        return p_value
    else:
        return 1.0


def perform_qlr_test(data: pd.DataFrame,
                     model: ConflictValidationModel) -> Dict[str, Any]:
    """Perform Quandt Likelihood Ratio test."""
    return {
        'test_performed': False,
        'reason': 'QLR test requires time series structure'
    }


def f_test_nested_models(restricted_results: Dict[str, Any],
                         unrestricted_results: Dict[str, Any],
                         n_restrictions: int) -> float:
    """F-test for nested models."""
    if 'rsquared' not in restricted_results or 'rsquared' not in unrestricted_results:
        return np.nan
    
    r2_r = restricted_results['rsquared']
    r2_u = unrestricted_results['rsquared']
    
    f_stat = (r2_u - r2_r) / (1 - r2_u) * 100
    
    return f_stat
