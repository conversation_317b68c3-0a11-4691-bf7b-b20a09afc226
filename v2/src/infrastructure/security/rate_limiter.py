"""Rate limiting for API endpoints."""

import time
from typing import Optional, Dict, Any, Callable
from functools import wraps

from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>, Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from ...infrastructure.logging import Logger
from ...infrastructure.caching.redis_cache import RedisCache
from ...infrastructure.caching.memory_cache import MemoryCache

logger = Logger(__name__)


class RateLimitConfig:
    """Rate limit configuration."""
    
    def __init__(
        self,
        requests_per_minute: int = 60,
        requests_per_hour: int = 1000,
        requests_per_day: int = 10000,
        burst_size: int = 10
    ):
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        self.requests_per_day = requests_per_day
        self.burst_size = burst_size


class RateLimiter:
    """Token bucket rate limiter implementation."""
    
    def __init__(
        self,
        cache: Optional[RedisCache] = None,
        default_config: Optional[RateLimitConfig] = None
    ):
        """
        Initialize rate limiter.
        
        Args:
            cache: Redis cache for distributed rate limiting
            default_config: Default rate limit configuration
        """
        self.cache = cache or MemoryCache()  # Fallback to memory cache
        self.default_config = default_config or RateLimitConfig()
        
    def check_rate_limit(
        self,
        identifier: str,
        config: Optional[RateLimitConfig] = None
    ) -> tuple[bool, Dict[str, Any]]:
        """
        Check if a request is within rate limits.
        
        Args:
            identifier: Unique identifier (IP, user ID, API key, etc.)
            config: Rate limit configuration (uses default if None)
            
        Returns:
            Tuple of (is_allowed, rate_limit_info)
        """
        config = config or self.default_config
        current_time = time.time()
        
        # Check minute rate limit
        minute_allowed, minute_info = self._check_window_limit(
            identifier, "minute", 60, config.requests_per_minute, current_time
        )
        
        # Check hour rate limit
        hour_allowed, hour_info = self._check_window_limit(
            identifier, "hour", 3600, config.requests_per_hour, current_time
        )
        
        # Check day rate limit
        day_allowed, day_info = self._check_window_limit(
            identifier, "day", 86400, config.requests_per_day, current_time
        )
        
        # Check burst limit using token bucket
        burst_allowed, burst_info = self._check_token_bucket(
            identifier, config.burst_size, config.requests_per_minute / 60, current_time
        )
        
        # Aggregate results
        is_allowed = all([minute_allowed, hour_allowed, day_allowed, burst_allowed])
        
        rate_limit_info = {
            "allowed": is_allowed,
            "limits": {
                "minute": minute_info,
                "hour": hour_info,
                "day": day_info,
                "burst": burst_info
            },
            "retry_after": self._calculate_retry_after(
                minute_info, hour_info, day_info, burst_info
            ) if not is_allowed else None
        }
        
        return is_allowed, rate_limit_info
    
    def _check_window_limit(
        self,
        identifier: str,
        window: str,
        window_seconds: int,
        limit: int,
        current_time: float
    ) -> tuple[bool, Dict[str, Any]]:
        """Check rate limit for a specific time window."""
        key = f"rate_limit:{identifier}:{window}"
        
        # Get current count
        count_data = self.cache.get(key)
        if count_data:
            count = int(count_data)
            window_start = current_time - window_seconds
        else:
            count = 0
            window_start = current_time
        
        # Check if within limit
        if count >= limit:
            return False, {
                "limit": limit,
                "remaining": 0,
                "reset": int(window_start + window_seconds),
                "used": count
            }
        
        # Increment counter
        new_count = count + 1
        self.cache.set(key, str(new_count), ttl=window_seconds)
        
        return True, {
            "limit": limit,
            "remaining": limit - new_count,
            "reset": int(window_start + window_seconds),
            "used": new_count
        }
    
    def _check_token_bucket(
        self,
        identifier: str,
        capacity: int,
        refill_rate: float,
        current_time: float
    ) -> tuple[bool, Dict[str, Any]]:
        """Check token bucket for burst protection."""
        key = f"token_bucket:{identifier}"
        
        # Get bucket state
        bucket_data = self.cache.get(key)
        if bucket_data:
            import json
            bucket = json.loads(bucket_data)
            tokens = bucket["tokens"]
            last_refill = bucket["last_refill"]
        else:
            tokens = capacity
            last_refill = current_time
        
        # Refill tokens
        time_passed = current_time - last_refill
        tokens_to_add = time_passed * refill_rate
        tokens = min(capacity, tokens + tokens_to_add)
        
        # Check if token available
        if tokens < 1:
            return False, {
                "capacity": capacity,
                "available": 0,
                "refill_rate": refill_rate,
                "wait_time": (1 - tokens) / refill_rate
            }
        
        # Consume token
        tokens -= 1
        
        # Save bucket state
        import json
        bucket_state = {
            "tokens": tokens,
            "last_refill": current_time
        }
        self.cache.set(key, json.dumps(bucket_state), ttl=3600)
        
        return True, {
            "capacity": capacity,
            "available": int(tokens),
            "refill_rate": refill_rate,
            "wait_time": 0
        }
    
    def _calculate_retry_after(self, *infos: Dict[str, Any]) -> int:
        """Calculate retry-after time in seconds."""
        retry_times = []
        
        for info in infos:
            if "reset" in info and info.get("remaining", 1) == 0:
                retry_times.append(info["reset"] - time.time())
            elif "wait_time" in info and info["wait_time"] > 0:
                retry_times.append(info["wait_time"])
        
        return int(max(retry_times)) if retry_times else 60
    
    def get_identifier_from_request(self, request: Request) -> str:
        """
        Extract identifier from request for rate limiting.
        
        Priority:
        1. Authenticated user ID
        2. API key
        3. IP address
        """
        # Check for authenticated user
        if hasattr(request.state, "user") and request.state.user:
            return f"user:{request.state.user.get('sub', 'unknown')}"
        
        # Check for API key
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return f"api_key:{api_key[:8]}"  # Use prefix only
        
        # Fall back to IP address
        client_ip = request.client.host if request.client else "unknown"
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        return f"ip:{client_ip}"


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware for FastAPI."""
    
    def __init__(
        self,
        app,
        rate_limiter: Optional[RateLimiter] = None,
        exclude_paths: Optional[list[str]] = None
    ):
        super().__init__(app)
        self.rate_limiter = rate_limiter or RateLimiter()
        self.exclude_paths = exclude_paths or ["/health", "/docs", "/openapi.json"]
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """Apply rate limiting to requests."""
        # Skip rate limiting for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Get identifier
        identifier = self.rate_limiter.get_identifier_from_request(request)
        
        # Check rate limit
        is_allowed, rate_limit_info = self.rate_limiter.check_rate_limit(identifier)
        
        # Add rate limit headers
        headers = {
            "X-RateLimit-Limit": str(rate_limit_info["limits"]["minute"]["limit"]),
            "X-RateLimit-Remaining": str(rate_limit_info["limits"]["minute"]["remaining"]),
            "X-RateLimit-Reset": str(rate_limit_info["limits"]["minute"]["reset"]),
        }
        
        if not is_allowed:
            # Rate limit exceeded
            headers["Retry-After"] = str(rate_limit_info["retry_after"])
            
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "detail": "Rate limit exceeded",
                    "retry_after": rate_limit_info["retry_after"]
                },
                headers=headers
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers to response
        for key, value in headers.items():
            response.headers[key] = value
        
        return response


def rate_limit(
    requests_per_minute: Optional[int] = None,
    requests_per_hour: Optional[int] = None,
    requests_per_day: Optional[int] = None
) -> Callable:
    """
    Decorator for custom rate limits on specific endpoints.
    
    Usage:
        @router.get("/expensive-operation")
        @rate_limit(requests_per_minute=10)
        async def expensive_operation():
            return {"result": "success"}
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            # Create custom config
            config = RateLimitConfig()
            if requests_per_minute is not None:
                config.requests_per_minute = requests_per_minute
            if requests_per_hour is not None:
                config.requests_per_hour = requests_per_hour
            if requests_per_day is not None:
                config.requests_per_day = requests_per_day
            
            # Get rate limiter (would be injected in production)
            rate_limiter = RateLimiter()
            identifier = rate_limiter.get_identifier_from_request(request)
            
            # Check rate limit
            is_allowed, rate_limit_info = rate_limiter.check_rate_limit(identifier, config)
            
            if not is_allowed:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Rate limit exceeded",
                    headers={"Retry-After": str(rate_limit_info["retry_after"])}
                )
            
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator