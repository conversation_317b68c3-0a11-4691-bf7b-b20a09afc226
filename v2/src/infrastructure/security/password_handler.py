"""Password hashing and verification using bcrypt."""

import bcrypt
from typing import Optional

from ...infrastructure.logging import Logger

logger = Logger(__name__)


class PasswordHandler:
    """Handles password hashing and verification using bcrypt."""
    
    def __init__(self, rounds: int = 12):
        """
        Initialize password handler.
        
        Args:
            rounds: Number of bcrypt rounds (default: 12)
        """
        self.rounds = rounds
        if rounds < 10:
            logger.warning(f"Using low bcrypt rounds ({rounds}). Consider using at least 10 for production.")
        elif rounds > 15:
            logger.warning(f"Using high bcrypt rounds ({rounds}). This may impact performance.")
    
    def hash_password(self, password: str) -> str:
        """
        Hash a password using bcrypt.
        
        Args:
            password: Plain text password
            
        Returns:
            Hashed password string
        """
        # Generate salt and hash password
        salt = bcrypt.gensalt(rounds=self.rounds)
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        
        # Return as string
        return hashed.decode('utf-8')
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        Verify a password against a hash.
        
        Args:
            plain_password: Plain text password to verify
            hashed_password: Hashed password to compare against
            
        Returns:
            True if password matches, False otherwise
        """
        try:
            # Check password
            return bcrypt.checkpw(
                plain_password.encode('utf-8'),
                hashed_password.encode('utf-8')
            )
        except Exception as e:
            logger.error(f"Error verifying password: {e}")
            return False
    
    def needs_rehash(self, hashed_password: str) -> bool:
        """
        Check if a password hash needs to be rehashed (e.g., rounds changed).
        
        Args:
            hashed_password: Current password hash
            
        Returns:
            True if rehashing is recommended
        """
        try:
            # Extract rounds from hash
            # bcrypt format: $2b$[rounds]$[salt][hash]
            parts = hashed_password.split('$')
            if len(parts) >= 3:
                current_rounds = int(parts[2])
                return current_rounds != self.rounds
            return True
        except Exception as e:
            logger.error(f"Error checking rehash need: {e}")
            return False
    
    def generate_temp_password(self, length: int = 16) -> str:
        """
        Generate a temporary password.
        
        Args:
            length: Password length (default: 16)
            
        Returns:
            Random password string
        """
        import secrets
        import string
        
        # Use a mix of characters
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(alphabet) for _ in range(length))
        
        return password
    
    def validate_password_strength(
        self,
        password: str,
        min_length: int = 8,
        require_uppercase: bool = True,
        require_lowercase: bool = True,
        require_digit: bool = True,
        require_special: bool = True
    ) -> tuple[bool, Optional[str]]:
        """
        Validate password strength against requirements.
        
        Args:
            password: Password to validate
            min_length: Minimum password length
            require_uppercase: Require at least one uppercase letter
            require_lowercase: Require at least one lowercase letter
            require_digit: Require at least one digit
            require_special: Require at least one special character
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check length
        if len(password) < min_length:
            return False, f"Password must be at least {min_length} characters long"
        
        # Check uppercase
        if require_uppercase and not any(c.isupper() for c in password):
            return False, "Password must contain at least one uppercase letter"
        
        # Check lowercase
        if require_lowercase and not any(c.islower() for c in password):
            return False, "Password must contain at least one lowercase letter"
        
        # Check digit
        if require_digit and not any(c.isdigit() for c in password):
            return False, "Password must contain at least one digit"
        
        # Check special character
        if require_special:
            special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
            if not any(c in special_chars for c in password):
                return False, "Password must contain at least one special character"
        
        return True, None


# Module-level convenience functions
_password_handler = PasswordHandler()

def hash_password(password: str) -> str:
    """Hash a password."""
    return _password_handler.hash_password(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password."""
    return _password_handler.verify_password(plain_password, hashed_password)