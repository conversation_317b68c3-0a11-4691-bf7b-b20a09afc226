"""Data quality monitoring and validation service.

This service provides comprehensive data quality monitoring, anomaly detection,
and alert generation for the data ingestion pipeline.
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Tuple, Any, Callable
import pandas as pd
import numpy as np
from uuid import uuid4, UUID
import json

from core.domain.market.entities import Market, PriceObservation
from core.domain.market.repositories import MarketRepository, PriceRepository
from core.domain.conflict.entities import ConflictEvent
from core.domain.conflict.repositories import ConflictRepository
from infrastructure.observability.metrics import MetricsCollector
from infrastructure.messaging.event_bus import EventBus
from infrastructure.logging import get_logger


class QualityLevel(Enum):
    """Data quality levels."""
    
    EXCELLENT = "excellent"  # > 95%
    GOOD = "good"           # 85-95%
    FAIR = "fair"           # 70-85%
    POOR = "poor"           # 50-70%
    CRITICAL = "critical"    # < 50%


class AlertSeverity(Enum):
    """Alert severity levels."""
    
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class QualityMetric:
    """Represents a data quality metric."""
    
    metric_id: UUID = field(default_factory=uuid4)
    name: str = ""
    description: str = ""
    source: str = ""  # wfp, acled, acaps
    category: str = ""  # completeness, accuracy, consistency, etc.
    
    # Values
    current_value: float = 0.0
    target_value: float = 0.95  # Default 95% target
    threshold_warning: float = 0.85
    threshold_critical: float = 0.70
    
    # Metadata
    measured_at: datetime = field(default_factory=datetime.utcnow)
    unit: str = "percentage"
    tags: Dict[str, str] = field(default_factory=dict)
    
    @property
    def quality_level(self) -> QualityLevel:
        """Get quality level based on current value."""
        if self.current_value >= 0.95:
            return QualityLevel.EXCELLENT
        elif self.current_value >= 0.85:
            return QualityLevel.GOOD
        elif self.current_value >= 0.70:
            return QualityLevel.FAIR
        elif self.current_value >= 0.50:
            return QualityLevel.POOR
        else:
            return QualityLevel.CRITICAL
    
    @property
    def alert_severity(self) -> Optional[AlertSeverity]:
        """Get alert severity if thresholds are breached."""
        if self.current_value < self.threshold_critical:
            return AlertSeverity.CRITICAL
        elif self.current_value < self.threshold_warning:
            return AlertSeverity.WARNING
        else:
            return None


@dataclass
class QualityAlert:
    """Represents a data quality alert."""
    
    alert_id: UUID = field(default_factory=uuid4)
    metric_id: UUID = None
    severity: AlertSeverity = AlertSeverity.INFO
    title: str = ""
    description: str = ""
    source: str = ""
    
    # Context
    current_value: float = 0.0
    expected_value: float = 0.0
    deviation: float = 0.0
    
    # Lifecycle
    created_at: datetime = field(default_factory=datetime.utcnow)
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    
    # Metadata
    tags: Dict[str, str] = field(default_factory=dict)
    context: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_active(self) -> bool:
        """Check if alert is still active."""
        return self.resolved_at is None


@dataclass
class AnomalyDetection:
    """Configuration for anomaly detection."""
    
    method: str = "zscore"  # zscore, iqr, isolation_forest
    threshold: float = 3.0
    window_size: int = 30  # days
    min_samples: int = 10
    enabled: bool = True


class DataQualityMonitor:
    """Monitors data quality across all sources and generates alerts."""
    
    def __init__(
        self,
        market_repository: MarketRepository,
        price_repository: PriceRepository,
        conflict_repository: ConflictRepository,
        metrics: MetricsCollector,
        event_bus: EventBus,
        logger: Optional = None
    ):
        self.market_repo = market_repository
        self.price_repo = price_repository
        self.conflict_repo = conflict_repository
        self.metrics = metrics
        self.event_bus = event_bus
        self.logger = logger or get_logger(__name__)
        
        # Quality tracking
        self.quality_metrics: Dict[UUID, QualityMetric] = {}
        self.active_alerts: Dict[UUID, QualityAlert] = {}
        self.alert_history: List[QualityAlert] = []
        
        # Configuration
        self.monitoring_enabled = True
        self.check_interval_minutes = 15
        self.anomaly_detection = AnomalyDetection()
        
        # Background task
        self.monitoring_task: Optional[asyncio.Task] = None
    
    async def start_monitoring(self) -> None:
        """Start the data quality monitoring service."""
        
        self.logger.info("Starting data quality monitoring")
        
        # Initialize baseline metrics
        await self._initialize_metrics()
        
        # Start monitoring loop
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        self.logger.info("Data quality monitoring started")
    
    async def stop_monitoring(self) -> None:
        """Stop the data quality monitoring service."""
        
        self.logger.info("Stopping data quality monitoring")
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Data quality monitoring stopped")
    
    async def run_quality_check(
        self,
        source: Optional[str] = None,
        category: Optional[str] = None
    ) -> Dict[str, QualityMetric]:
        """Run a comprehensive data quality check."""
        
        self.logger.info("Running data quality check", extra={
            "source": source,
            "category": category
        })
        
        results = {}
        
        # WFP data quality checks
        if not source or source == "wfp":
            wfp_metrics = await self._check_wfp_quality()
            results.update(wfp_metrics)
        
        # ACLED data quality checks
        if not source or source == "acled":
            acled_metrics = await self._check_acled_quality()
            results.update(acled_metrics)
        
        # ACAPS data quality checks
        if not source or source == "acaps":
            acaps_metrics = await self._check_acaps_quality()
            results.update(acaps_metrics)
        
        # Cross-source consistency checks
        if not source:
            consistency_metrics = await self._check_cross_source_consistency()
            results.update(consistency_metrics)
        
        # Update stored metrics
        for metric_id, metric in results.items():
            self.quality_metrics[metric_id] = metric
        
        # Check for alerts
        await self._check_quality_alerts(results.values())
        
        # Update monitoring metrics
        await self._update_monitoring_metrics()
        
        return results
    
    async def _check_wfp_quality(self) -> Dict[UUID, QualityMetric]:
        """Check WFP data quality."""
        
        metrics = {}
        
        try:
            # Get recent WFP data
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)
            
            markets = await self.market_repo.find_all()
            prices = await self.price_repo.find_by_date_range(start_date, end_date)
            
            # Completeness metrics
            
            # 1. Market coordinate completeness
            markets_with_coords = sum(1 for m in markets if m.coordinates is not None)
            coord_completeness = markets_with_coords / len(markets) if markets else 0
            
            coord_metric = QualityMetric(
                name="WFP Market Coordinate Completeness",
                description="Percentage of markets with valid coordinates",
                source="wfp",
                category="completeness",
                current_value=coord_completeness,
                tags={"data_type": "markets", "field": "coordinates"}
            )
            metrics[coord_metric.metric_id] = coord_metric
            
            # 2. Price data completeness
            if prices:
                total_expected = len(markets) * 30 * 10  # Rough estimate: markets * days * commodities
                price_completeness = len(prices) / total_expected
                
                price_metric = QualityMetric(
                    name="WFP Price Data Completeness",
                    description="Completeness of price observations",
                    source="wfp",
                    category="completeness", 
                    current_value=min(price_completeness, 1.0),  # Cap at 100%
                    tags={"data_type": "prices"}
                )
                metrics[price_metric.metric_id] = price_metric
                
                # 3. USD price availability
                usd_prices = sum(1 for p in prices if p.price.currency.value == "USD")
                usd_availability = usd_prices / len(prices)
                
                usd_metric = QualityMetric(
                    name="WFP USD Price Availability",
                    description="Percentage of prices available in USD",
                    source="wfp",
                    category="completeness",
                    current_value=usd_availability,
                    tags={"data_type": "prices", "currency": "USD"}
                )
                metrics[usd_metric.metric_id] = usd_metric
            
            # Accuracy metrics
            
            # 4. Price outlier detection
            if prices:
                outlier_rate = await self._detect_price_outliers(prices)
                
                outlier_metric = QualityMetric(
                    name="WFP Price Outlier Rate",
                    description="Percentage of prices identified as outliers",
                    source="wfp",
                    category="accuracy",
                    current_value=1.0 - outlier_rate,  # Lower outlier rate = higher quality
                    threshold_warning=0.90,  # 10% outliers is concerning
                    threshold_critical=0.80,  # 20% outliers is critical
                    tags={"data_type": "prices", "check": "outliers"}
                )
                metrics[outlier_metric.metric_id] = outlier_metric
            
            # Timeliness metrics
            
            # 5. Data freshness
            if prices:
                latest_price_date = max(p.observed_date for p in prices)
                days_since_latest = (datetime.utcnow() - latest_price_date).days
                freshness = max(0, 1.0 - days_since_latest / 7.0)  # Expect weekly updates
                
                freshness_metric = QualityMetric(
                    name="WFP Data Freshness",
                    description="How recent the latest price data is",
                    source="wfp",
                    category="timeliness",
                    current_value=freshness,
                    threshold_warning=0.80,  # > 1.4 days old
                    threshold_critical=0.60,  # > 2.8 days old
                    tags={"data_type": "prices", "latest_date": latest_price_date.isoformat()}
                )
                metrics[freshness_metric.metric_id] = freshness_metric
            
        except Exception as e:
            self.logger.error("Error checking WFP quality", extra={"error": str(e)})
        
        return metrics
    
    async def _check_acled_quality(self) -> Dict[UUID, QualityMetric]:
        """Check ACLED data quality."""
        
        metrics = {}
        
        try:
            # Get recent ACLED data
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)
            
            events = await self.conflict_repo.find_by_date_range(start_date, end_date)
            
            if events:
                # 1. Coordinate precision
                high_precision_events = sum(
                    1 for event in events
                    if self._has_high_precision_coordinates(event.location)
                )
                precision_rate = high_precision_events / len(events)
                
                precision_metric = QualityMetric(
                    name="ACLED Coordinate Precision",
                    description="Percentage of events with high-precision coordinates",
                    source="acled",
                    category="accuracy",
                    current_value=precision_rate,
                    tags={"data_type": "events", "field": "coordinates"}
                )
                metrics[precision_metric.metric_id] = precision_metric
                
                # 2. Actor information completeness
                events_with_actors = sum(
                    1 for event in events 
                    if event.actors and len(event.actors) > 0 and event.actors[0] != "Unknown"
                )
                actor_completeness = events_with_actors / len(events)
                
                actor_metric = QualityMetric(
                    name="ACLED Actor Information Completeness",
                    description="Percentage of events with known actors",
                    source="acled",
                    category="completeness",
                    current_value=actor_completeness,
                    tags={"data_type": "events", "field": "actors"}
                )
                metrics[actor_metric.metric_id] = actor_metric
                
                # 3. Temporal distribution consistency
                daily_counts = {}
                for event in events:
                    date_key = event.event_date.date()
                    daily_counts[date_key] = daily_counts.get(date_key, 0) + 1
                
                if len(daily_counts) > 1:
                    counts = list(daily_counts.values())
                    cv = np.std(counts) / np.mean(counts) if np.mean(counts) > 0 else 0
                    consistency = max(0, 1.0 - cv)  # Lower coefficient of variation = higher consistency
                    
                    consistency_metric = QualityMetric(
                        name="ACLED Temporal Consistency",
                        description="Consistency of event distribution over time",
                        source="acled",
                        category="consistency",
                        current_value=consistency,
                        threshold_warning=0.70,
                        threshold_critical=0.50,
                        tags={"data_type": "events", "check": "temporal_distribution"}
                    )
                    metrics[consistency_metric.metric_id] = consistency_metric
            
        except Exception as e:
            self.logger.error("Error checking ACLED quality", extra={"error": str(e)})
        
        return metrics
    
    async def _check_acaps_quality(self) -> Dict[UUID, QualityMetric]:
        """Check ACAPS data quality."""
        
        metrics = {}
        
        try:
            # For ACAPS, we would check the processed control area data
            # This is a placeholder implementation
            
            # 1. Geographic coverage
            coverage_metric = QualityMetric(
                name="ACAPS Geographic Coverage",
                description="Percentage of Yemen districts covered",
                source="acaps",
                category="completeness",
                current_value=0.85,  # Placeholder value
                tags={"data_type": "control_areas", "field": "districts"}
            )
            metrics[coverage_metric.metric_id] = coverage_metric
            
            # 2. Update frequency
            frequency_metric = QualityMetric(
                name="ACAPS Update Frequency",
                description="Regularity of control area updates",
                source="acaps",
                category="timeliness",
                current_value=0.90,  # Placeholder value
                tags={"data_type": "control_areas", "check": "update_frequency"}
            )
            metrics[frequency_metric.metric_id] = frequency_metric
            
        except Exception as e:
            self.logger.error("Error checking ACAPS quality", extra={"error": str(e)})
        
        return metrics
    
    async def _check_cross_source_consistency(self) -> Dict[UUID, QualityMetric]:
        """Check consistency across data sources."""
        
        metrics = {}
        
        try:
            # 1. Market location consistency
            # Check if markets from WFP data align with geographic references
            
            # 2. Temporal alignment
            # Check if data sources cover similar time periods
            
            # 3. Administrative boundary consistency
            # Check if governorate/district names are consistent
            
            # Placeholder implementation
            consistency_metric = QualityMetric(
                name="Cross-Source Geographic Consistency",
                description="Consistency of geographic references across sources",
                source="cross_source",
                category="consistency",
                current_value=0.88,  # Placeholder value
                tags={"check": "geographic_alignment"}
            )
            metrics[consistency_metric.metric_id] = consistency_metric
            
        except Exception as e:
            self.logger.error("Error checking cross-source consistency", extra={"error": str(e)})
        
        return metrics
    
    async def _detect_price_outliers(self, prices: List[PriceObservation]) -> float:
        """Detect price outliers and return outlier rate."""
        
        if len(prices) < 10:
            return 0.0
        
        # Group by commodity for outlier detection
        commodity_groups = {}
        for price in prices:
            key = price.commodity.code
            if key not in commodity_groups:
                commodity_groups[key] = []
            commodity_groups[key].append(float(price.price.amount))
        
        total_outliers = 0
        total_prices = 0
        
        for commodity, price_values in commodity_groups.items():
            if len(price_values) < 3:
                continue
            
            # Use IQR method for outlier detection
            q1 = np.percentile(price_values, 25)
            q3 = np.percentile(price_values, 75)
            iqr = q3 - q1
            
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            outliers = sum(1 for p in price_values if p < lower_bound or p > upper_bound)
            total_outliers += outliers
            total_prices += len(price_values)
        
        return total_outliers / total_prices if total_prices > 0 else 0.0
    
    async def _check_quality_alerts(self, metrics: List[QualityMetric]) -> None:
        """Check metrics for alert conditions."""
        
        for metric in metrics:
            alert_severity = metric.alert_severity
            
            if alert_severity:
                # Create new alert
                alert = QualityAlert(
                    metric_id=metric.metric_id,
                    severity=alert_severity,
                    title=f"Data Quality Alert: {metric.name}",
                    description=f"{metric.description} is below threshold",
                    source=metric.source,
                    current_value=metric.current_value,
                    expected_value=metric.target_value,
                    deviation=abs(metric.current_value - metric.target_value),
                    tags=metric.tags,
                    context={
                        "metric_name": metric.name,
                        "category": metric.category,
                        "quality_level": metric.quality_level.value
                    }
                )
                
                # Check if this is a new alert
                existing_alert = self._find_existing_alert(metric.metric_id)
                if not existing_alert:
                    # New alert
                    self.active_alerts[alert.alert_id] = alert
                    await self._send_alert(alert)
                    
                    self.logger.warning("Quality alert triggered", extra={
                        "alert_id": str(alert.alert_id),
                        "metric": metric.name,
                        "severity": alert_severity.value,
                        "current_value": metric.current_value,
                        "expected_value": metric.target_value
                    })
            else:
                # Check if we can resolve any existing alerts for this metric
                existing_alert = self._find_existing_alert(metric.metric_id)
                if existing_alert and existing_alert.is_active:
                    # Resolve the alert
                    existing_alert.resolved_at = datetime.utcnow()
                    self.alert_history.append(existing_alert)
                    del self.active_alerts[existing_alert.alert_id]
                    
                    await self._send_alert_resolution(existing_alert)
                    
                    self.logger.info("Quality alert resolved", extra={
                        "alert_id": str(existing_alert.alert_id),
                        "metric": metric.name
                    })
    
    def _find_existing_alert(self, metric_id: UUID) -> Optional[QualityAlert]:
        """Find existing active alert for a metric."""
        for alert in self.active_alerts.values():
            if alert.metric_id == metric_id:
                return alert
        return None
    
    async def _send_alert(self, alert: QualityAlert) -> None:
        """Send alert notification."""
        
        await self.event_bus.publish("data_quality.alert_triggered", {
            "alert_id": str(alert.alert_id),
            "severity": alert.severity.value,
            "title": alert.title,
            "description": alert.description,
            "source": alert.source,
            "current_value": alert.current_value,
            "expected_value": alert.expected_value,
            "created_at": alert.created_at.isoformat()
        })
        
        self.metrics.increment_counter("data_quality.alerts_triggered", {
            "severity": alert.severity.value,
            "source": alert.source
        })
    
    async def _send_alert_resolution(self, alert: QualityAlert) -> None:
        """Send alert resolution notification."""
        
        await self.event_bus.publish("data_quality.alert_resolved", {
            "alert_id": str(alert.alert_id),
            "severity": alert.severity.value,
            "title": alert.title,
            "source": alert.source,
            "resolved_at": alert.resolved_at.isoformat()
        })
        
        self.metrics.increment_counter("data_quality.alerts_resolved", {
            "severity": alert.severity.value,
            "source": alert.source
        })
    
    def _has_high_precision_coordinates(self, coords) -> bool:
        """Check if coordinates have high precision."""
        lat_precision = len(str(coords.latitude).split('.')[-1]) if '.' in str(coords.latitude) else 0
        lon_precision = len(str(coords.longitude).split('.')[-1]) if '.' in str(coords.longitude) else 0
        return lat_precision >= 3 and lon_precision >= 3
    
    async def _monitoring_loop(self) -> None:
        """Background monitoring loop."""
        
        while self.monitoring_enabled:
            try:
                # Run quality checks
                await self.run_quality_check()
                
                # Sleep for check interval
                await asyncio.sleep(self.check_interval_minutes * 60)
                
            except Exception as e:
                self.logger.error("Error in monitoring loop", extra={"error": str(e)})
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _initialize_metrics(self) -> None:
        """Initialize baseline quality metrics."""
        
        self.logger.info("Initializing baseline quality metrics")
        
        # Run initial quality check
        await self.run_quality_check()
        
        self.logger.info("Baseline quality metrics initialized", extra={
            "metrics_count": len(self.quality_metrics)
        })
    
    async def _update_monitoring_metrics(self) -> None:
        """Update monitoring metrics."""
        
        # Overall quality score
        if self.quality_metrics:
            quality_scores = [m.current_value for m in self.quality_metrics.values()]
            overall_score = np.mean(quality_scores)
            self.metrics.gauge("data_quality.overall_score", overall_score)
        
        # Quality by source
        source_scores = {}
        for metric in self.quality_metrics.values():
            if metric.source not in source_scores:
                source_scores[metric.source] = []
            source_scores[metric.source].append(metric.current_value)
        
        for source, scores in source_scores.items():
            avg_score = np.mean(scores)
            self.metrics.gauge(f"data_quality.{source}_score", avg_score)
        
        # Alert counts
        self.metrics.gauge("data_quality.active_alerts", len(self.active_alerts))
        
        # Alert breakdown by severity
        severity_counts = {}
        for alert in self.active_alerts.values():
            severity = alert.severity.value
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        for severity, count in severity_counts.items():
            self.metrics.gauge(f"data_quality.alerts_{severity}", count)
    
    async def get_quality_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive quality dashboard data."""
        
        # Run fresh quality check
        await self.run_quality_check()
        
        # Prepare dashboard data
        dashboard = {
            "overall_score": 0.0,
            "quality_by_source": {},
            "quality_by_category": {},
            "active_alerts": [],
            "recent_trends": {},
            "generated_at": datetime.utcnow().isoformat()
        }
        
        if self.quality_metrics:
            # Overall score
            scores = [m.current_value for m in self.quality_metrics.values()]
            dashboard["overall_score"] = float(np.mean(scores))
            
            # By source
            source_metrics = {}
            for metric in self.quality_metrics.values():
                if metric.source not in source_metrics:
                    source_metrics[metric.source] = []
                source_metrics[metric.source].append(metric.current_value)
            
            dashboard["quality_by_source"] = {
                source: float(np.mean(scores))
                for source, scores in source_metrics.items()
            }
            
            # By category
            category_metrics = {}
            for metric in self.quality_metrics.values():
                if metric.category not in category_metrics:
                    category_metrics[metric.category] = []
                category_metrics[metric.category].append(metric.current_value)
            
            dashboard["quality_by_category"] = {
                category: float(np.mean(scores))
                for category, scores in category_metrics.items()
            }
        
        # Active alerts
        dashboard["active_alerts"] = [
            {
                "alert_id": str(alert.alert_id),
                "severity": alert.severity.value,
                "title": alert.title,
                "source": alert.source,
                "created_at": alert.created_at.isoformat(),
                "current_value": alert.current_value,
                "expected_value": alert.expected_value
            }
            for alert in self.active_alerts.values()
        ]
        
        return dashboard