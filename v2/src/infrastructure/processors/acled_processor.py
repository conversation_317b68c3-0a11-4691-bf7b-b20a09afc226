"""ACLED conflict event processor for V2 architecture.

This processor handles ACLED conflict data, converting it to V2 domain entities
with proper spatial analysis, temporal aggregation, and validation.
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple
import pandas as pd
import numpy as np
from uuid import uuid4
import math

from core.domain.conflict.entities import ConflictEvent
from core.domain.conflict.value_objects import ConflictType, ConflictIntensity, ImpactRadius
from core.domain.market.value_objects import Coordinates, MarketId
from core.domain.shared.exceptions import ValidationException, DataQualityException
from infrastructure.observability.metrics import MetricsCollector
from infrastructure.logging import get_logger


@dataclass
class ACLEDProcessingConfig:
    """Configuration for ACLED data processing."""
    
    spatial_radius_km: float = 50.0
    temporal_window_days: int = 30
    min_fatalities_threshold: int = 0
    batch_size: int = 1000
    enable_spatial_analysis: bool = True
    enable_quality_checks: bool = True
    
    # Event type mappings
    event_type_mappings: Dict[str, ConflictType] = None
    
    # Actor keyword mappings
    actor_keywords: Dict[str, List[str]] = None
    
    def __post_init__(self):
        if self.event_type_mappings is None:
            self.event_type_mappings = {
                'Battles': ConflictType.BATTLE,
                'Violence against civilians': ConflictType.VIOLENCE_AGAINST_CIVILIANS,
                'Explosions/Remote violence': ConflictType.EXPLOSION,
                'Protests': ConflictType.PROTEST,
                'Riots': ConflictType.RIOT,
                'Strategic developments': ConflictType.STRATEGIC_DEVELOPMENT
            }
        
        if self.actor_keywords is None:
            self.actor_keywords = {
                'houthis': ['houthi', 'ansar allah', 'dfa'],
                'government': ['government', 'irg', 'hadi'],
                'coalition': ['coalition', 'saudi', 'uae'],
                'aqap': ['aqap', 'al-qaeda', 'qaeda'],
                'isis': ['isis', 'is', 'islamic state'],
                'stc': ['stc', 'southern transitional']
            }


@dataclass
class ConflictProcessingMetrics:
    """Metrics for ACLED data processing."""
    
    raw_events: int = 0
    processed_events: int = 0
    validation_errors: int = 0
    spatial_matches: int = 0
    temporal_aggregations: int = 0
    quality_warnings: int = 0
    processing_time_seconds: float = 0.0


class ACLEDProcessor:
    """Processes ACLED conflict data into V2 domain entities."""
    
    def __init__(
        self,
        config: ACLEDProcessingConfig,
        metrics: MetricsCollector,
        logger: Optional = None
    ):
        self.config = config
        self.metrics = metrics
        self.logger = logger or get_logger(__name__)
    
    async def process_conflict_events(
        self,
        raw_data: pd.DataFrame,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[ConflictEvent]:
        """Main processing pipeline for ACLED conflict data."""
        
        start_time = datetime.utcnow()
        metrics = ConflictProcessingMetrics(raw_events=len(raw_data))
        
        try:
            self.logger.info("Starting ACLED data processing", extra={
                "raw_events": len(raw_data),
                "start_date": start_date,
                "end_date": end_date
            })
            
            # Step 1: Clean and validate raw data
            cleaned_data = await self._clean_raw_data(raw_data, start_date, end_date)
            self.metrics.gauge("acled.cleaned_events", len(cleaned_data))
            
            # Step 2: Extract conflict events
            conflict_events = await self._extract_conflict_events(cleaned_data)
            metrics.processed_events = len(conflict_events)
            self.metrics.gauge("acled.conflict_events_extracted", len(conflict_events))
            
            # Step 3: Quality validation
            if self.config.enable_quality_checks:
                await self._validate_data_quality(conflict_events, cleaned_data)
            
            # Step 4: Spatial and temporal analysis
            if self.config.enable_spatial_analysis:
                await self._enhance_spatial_information(conflict_events)
            
            end_time = datetime.utcnow()
            metrics.processing_time_seconds = (end_time - start_time).total_seconds()
            
            # Update metrics
            self._update_processing_metrics(metrics)
            
            self.logger.info("ACLED data processing completed", extra={
                "metrics": metrics.__dict__
            })
            
            return conflict_events
            
        except Exception as e:
            self.metrics.increment_counter("acled.processing_errors", {"error_type": type(e).__name__})
            self.logger.error("ACLED data processing failed", extra={
                "error": str(e),
                "events": len(raw_data)
            })
            raise
    
    async def _clean_raw_data(
        self,
        raw_data: pd.DataFrame,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """Clean and standardize raw ACLED data."""
        
        df = raw_data.copy()
        
        # Ensure required columns exist
        required_cols = ['event_date', 'latitude', 'longitude', 'event_type', 'fatalities']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            raise DataQualityException(f"Missing required columns: {missing_cols}")
        
        # Convert data types
        df['event_date'] = pd.to_datetime(df['event_date'], errors='coerce')
        df['latitude'] = pd.to_numeric(df['latitude'], errors='coerce')
        df['longitude'] = pd.to_numeric(df['longitude'], errors='coerce')
        df['fatalities'] = pd.to_numeric(df['fatalities'], errors='coerce').fillna(0).astype(int)
        
        # Filter by date range
        if start_date:
            df = df[df['event_date'] >= start_date]
        if end_date:
            df = df[df['event_date'] <= end_date]
        
        # Filter by geography (Yemen bounding box)
        yemen_bounds = {
            'lat_min': 12.0, 'lat_max': 19.0,
            'lon_min': 42.0, 'lon_max': 55.0
        }
        
        df = df[
            (df['latitude'] >= yemen_bounds['lat_min']) &
            (df['latitude'] <= yemen_bounds['lat_max']) &
            (df['longitude'] >= yemen_bounds['lon_min']) &
            (df['longitude'] <= yemen_bounds['lon_max'])
        ]
        
        # Remove invalid records
        df = df.dropna(subset=['event_date', 'latitude', 'longitude', 'event_type'])
        
        # Standardize text fields
        if 'event_type' in df.columns:
            df['event_type'] = df['event_type'].astype(str).str.strip()
        
        # Fill missing actor information
        actor_cols = ['actor1', 'actor2']
        for col in actor_cols:
            if col in df.columns:
                df[col] = df[col].fillna('Unknown').astype(str)
        
        # Add derived fields
        df['conflict_intensity'] = df['fatalities'].apply(self._calculate_base_intensity)
        
        self.logger.info("ACLED data cleaning completed", extra={
            "original_events": len(raw_data),
            "cleaned_events": len(df),
            "filtered_out": len(raw_data) - len(df)
        })
        
        return df
    
    async def _extract_conflict_events(self, df: pd.DataFrame) -> List[ConflictEvent]:
        """Extract conflict events from cleaned ACLED data."""
        
        events = []
        
        for _, row in df.iterrows():
            try:
                # Map event type
                event_type = self._map_event_type(row['event_type'])
                
                # Create coordinates
                coordinates = Coordinates(
                    latitude=float(row['latitude']),
                    longitude=float(row['longitude'])
                )
                
                # Determine intensity
                intensity = ConflictIntensity.from_fatalities(int(row['fatalities']))
                
                # Extract actors
                actors = self._extract_actors(row)
                
                # Create description
                description = self._create_event_description(row)
                
                # Create impact radius
                impact_radius = ImpactRadius.default_for_type(event_type)
                
                # Create conflict event
                event = ConflictEvent(
                    id=uuid4(),
                    event_date=row['event_date'].to_pydatetime(),
                    location=coordinates,
                    conflict_type=event_type,
                    intensity=intensity,
                    fatalities=int(row['fatalities']),
                    actors=actors,
                    description=description,
                    source="ACLED",
                    impact_radius=impact_radius
                )
                
                events.append(event)
                
            except (ValidationException, ValueError, TypeError) as e:
                self.logger.warning("Failed to create conflict event", extra={
                    "event_date": row.get('event_date'),
                    "event_type": row.get('event_type'),
                    "error": str(e)
                })
                self.metrics.increment_counter("acled.event_validation_errors")
                continue
        
        self.logger.info("Conflict event extraction completed", extra={
            "events_extracted": len(events),
            "event_records_processed": len(df)
        })
        
        return events
    
    async def calculate_market_conflict_metrics(
        self,
        conflict_events: List[ConflictEvent],
        markets: List[Tuple[MarketId, Coordinates]],
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Dict]:
        """Calculate conflict metrics for each market."""
        
        market_metrics = {}
        
        # Create time periods (monthly)
        periods = pd.period_range(start=start_date, end=end_date, freq='M')
        
        for market_id, market_coords in markets:
            market_metrics[market_id.value] = {}
            
            for period in periods:
                period_start = period.to_timestamp()
                period_end = (period + 1).to_timestamp()
                
                # Filter events for this period
                period_events = [
                    event for event in conflict_events
                    if period_start <= event.event_date < period_end
                ]
                
                # Calculate metrics
                metrics = await self._calculate_period_metrics(
                    period_events, market_coords, period_start
                )
                
                market_metrics[market_id.value][str(period)] = metrics
        
        return market_metrics
    
    async def _calculate_period_metrics(
        self,
        events: List[ConflictEvent],
        market_coords: Coordinates,
        period_date: datetime
    ) -> Dict:
        """Calculate conflict metrics for a specific period and market."""
        
        # Find events within impact radius
        nearby_events = []
        for event in events:
            distance = event.distance_to_point(market_coords)
            if distance <= self.config.spatial_radius_km:
                nearby_events.append((event, distance))
        
        # Calculate basic metrics
        total_events = len(nearby_events)
        total_fatalities = sum(event.fatalities for event, _ in nearby_events)
        
        # Count by event type
        event_type_counts = {}
        for event, _ in nearby_events:
            event_type = event.conflict_type.value
            event_type_counts[event_type] = event_type_counts.get(event_type, 0) + 1
        
        # Calculate intensity-weighted metrics
        intensity_score = sum(
            event.intensity.value * self._distance_weight(distance)
            for event, distance in nearby_events
        )
        
        # Average distance to conflicts
        avg_distance = np.mean([distance for _, distance in nearby_events]) if nearby_events else None
        
        return {
            'total_events': total_events,
            'total_fatalities': total_fatalities,
            'intensity_score': intensity_score,
            'avg_distance_km': avg_distance,
            'event_types': event_type_counts,
            'period_date': period_date.isoformat()
        }
    
    async def _enhance_spatial_information(self, events: List[ConflictEvent]) -> None:
        """Enhance spatial information for conflict events."""
        
        # Add administrative boundary information if available
        # This would integrate with geography service
        for event in events:
            # For now, add basic spatial context
            event.spatial_context = {
                'latitude': event.location.latitude,
                'longitude': event.location.longitude,
                'precision': 'coordinates'
            }
    
    async def _validate_data_quality(
        self,
        events: List[ConflictEvent],
        raw_data: pd.DataFrame
    ) -> None:
        """Validate overall data quality."""
        
        quality_issues = []
        
        # Check processing success rate
        processing_rate = len(events) / len(raw_data) if len(raw_data) > 0 else 0
        if processing_rate < 0.8:
            quality_issues.append(f"Low processing success rate: {processing_rate:.1%}")
        
        # Check coordinate precision
        if events:
            high_precision_events = sum(
                1 for event in events
                if self._has_high_precision_coordinates(event.location)
            )
            precision_rate = high_precision_events / len(events)
            
            if precision_rate < 0.9:
                quality_issues.append(f"Low coordinate precision: {precision_rate:.1%}")
        
        # Check temporal distribution
        if events:
            dates = [event.event_date.date() for event in events]
            unique_dates = len(set(dates))
            date_range = (max(dates) - min(dates)).days if dates else 0
            
            temporal_density = unique_dates / max(1, date_range)
            if temporal_density < 0.05:  # Less than 5% of days have events
                quality_issues.append(f"Low temporal density: {temporal_density:.2%}")
        
        # Check actor information completeness
        if events:
            events_with_actors = sum(1 for event in events if event.actors and event.actors != ['Unknown'])
            actor_completeness = events_with_actors / len(events)
            
            if actor_completeness < 0.7:
                quality_issues.append(f"Low actor information: {actor_completeness:.1%}")
        
        # Log quality issues
        if quality_issues:
            self.logger.warning("Data quality issues detected", extra={
                "issues": quality_issues
            })
            self.metrics.gauge("acled.quality_issues", len(quality_issues))
        else:
            self.logger.info("Data quality validation passed")
            self.metrics.gauge("acled.quality_issues", 0)
    
    def _map_event_type(self, event_type_str: str) -> ConflictType:
        """Map ACLED event type to domain ConflictType."""
        
        # Direct mapping
        if event_type_str in self.config.event_type_mappings:
            return self.config.event_type_mappings[event_type_str]
        
        # Fuzzy matching
        event_type_lower = event_type_str.lower()
        
        if 'battle' in event_type_lower:
            return ConflictType.BATTLE
        elif 'violence' in event_type_lower and 'civilian' in event_type_lower:
            return ConflictType.VIOLENCE_AGAINST_CIVILIANS
        elif 'explosion' in event_type_lower or 'remote' in event_type_lower:
            return ConflictType.EXPLOSION
        elif 'protest' in event_type_lower:
            return ConflictType.PROTEST
        elif 'riot' in event_type_lower:
            return ConflictType.RIOT
        else:
            return ConflictType.STRATEGIC_DEVELOPMENT
    
    def _extract_actors(self, row: pd.Series) -> List[str]:
        """Extract and standardize actor information."""
        
        actors = []
        
        # Extract from actor columns
        actor_cols = ['actor1', 'actor2']
        for col in actor_cols:
            if col in row and pd.notna(row[col]) and row[col] != 'Unknown':
                actors.append(str(row[col]).strip())
        
        # Standardize actor names using keywords
        standardized_actors = []
        for actor in actors:
            standardized = self._standardize_actor_name(actor)
            if standardized and standardized not in standardized_actors:
                standardized_actors.append(standardized)
        
        return standardized_actors if standardized_actors else ['Unknown']
    
    def _standardize_actor_name(self, actor_name: str) -> Optional[str]:
        """Standardize actor name using keyword matching."""
        
        actor_lower = actor_name.lower()
        
        for standard_name, keywords in self.config.actor_keywords.items():
            for keyword in keywords:
                if keyword in actor_lower:
                    return standard_name.title()
        
        # Return original if no standardization found
        return actor_name if actor_name != 'Unknown' else None
    
    def _create_event_description(self, row: pd.Series) -> str:
        """Create event description from available fields."""
        
        parts = []
        
        # Event type
        if 'event_type' in row:
            parts.append(f"Event: {row['event_type']}")
        
        # Actors
        actors = []
        for col in ['actor1', 'actor2']:
            if col in row and pd.notna(row[col]) and row[col] != 'Unknown':
                actors.append(str(row[col]))
        
        if actors:
            parts.append(f"Actors: {', '.join(actors)}")
        
        # Fatalities
        if 'fatalities' in row and row['fatalities'] > 0:
            parts.append(f"Fatalities: {row['fatalities']}")
        
        # Location details
        if 'admin1' in row and pd.notna(row['admin1']):
            parts.append(f"Location: {row['admin1']}")
        
        return "; ".join(parts) if parts else "Conflict event"
    
    def _calculate_base_intensity(self, fatalities: int) -> float:
        """Calculate base intensity score from fatalities."""
        if fatalities == 0:
            return 1.0
        elif fatalities <= 5:
            return 2.0
        elif fatalities <= 25:
            return 3.0
        else:
            return 4.0
    
    def _distance_weight(self, distance_km: float) -> float:
        """Calculate distance-based weight for conflict impact."""
        # Exponential decay function
        return math.exp(-distance_km / 20.0)  # 20km characteristic distance
    
    def _has_high_precision_coordinates(self, coords: Coordinates) -> bool:
        """Check if coordinates have high precision (more than 3 decimal places)."""
        lat_precision = len(str(coords.latitude).split('.')[-1]) if '.' in str(coords.latitude) else 0
        lon_precision = len(str(coords.longitude).split('.')[-1]) if '.' in str(coords.longitude) else 0
        
        return lat_precision >= 3 and lon_precision >= 3
    
    def _update_processing_metrics(self, metrics: ConflictProcessingMetrics) -> None:
        """Update processing metrics."""
        self.metrics.gauge("acled.processing_time_seconds", metrics.processing_time_seconds)
        self.metrics.gauge("acled.raw_events", metrics.raw_events)
        self.metrics.gauge("acled.processed_events", metrics.processed_events)
        self.metrics.gauge("acled.validation_errors", metrics.validation_errors)
        
        # Calculate success rate
        success_rate = metrics.processed_events / metrics.raw_events if metrics.raw_events > 0 else 0
        self.metrics.gauge("acled.processing_success_rate", success_rate)