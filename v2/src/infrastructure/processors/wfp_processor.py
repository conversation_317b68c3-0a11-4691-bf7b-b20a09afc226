"""WFP data processor for V2 architecture.

This processor handles WFP price data, converting it to V2 domain entities
with proper validation, error handling, and monitoring.
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Set, Tuple
import pandas as pd
import numpy as np
from uuid import uuid4

from core.domain.market.entities import Market, PriceObservation, ExchangeRateObservation
from core.domain.market.value_objects import (
    MarketId, Commodity, Price, Currency, ExchangeRate, 
    Coordinates, MarketType, ControlStatus
)
from core.domain.shared.exceptions import ValidationException, DataQualityException
from infrastructure.observability.metrics import MetricsCollector
from infrastructure.logging import get_logger


@dataclass
class WFPProcessingConfig:
    """Configuration for WFP data processing."""
    
    min_market_coverage: float = 0.3
    outlier_threshold: float = 3.0
    currency_conversion_tolerance: float = 0.1
    coordinate_precision: int = 6
    batch_size: int = 1000
    enable_quality_checks: bool = True
    
    # Governorate name mappings
    governorate_mappings: Dict[str, str] = None
    
    # Key commodities for analysis
    key_commodities: List[str] = None
    
    def __post_init__(self):
        if self.governorate_mappings is None:
            self.governorate_mappings = {
                "Al Dhale'e": "Ad Dale'",
                "Al Hudaydah": "Al Hodeidah", 
                "Amanat Al Asimah": "Sana'a City",
                "Hadramaut": "Hadramawt",
                "Sa'ada": "Sa'dah",
                "Taizz": "Ta'iz"
            }
        
        if self.key_commodities is None:
            self.key_commodities = [
                'Wheat', 'Wheat Flour', 'Rice (Imported)', 'Sugar', 'Oil (Vegetable)',
                'Beans (Kidney Red)', 'Beans (White)', 'Salt', 'Fuel (Diesel)', 
                'Fuel (Petrol-Gasoline)', 'Lentils', 'Onions', 'Potatoes'
            ]


@dataclass
class ProcessingMetrics:
    """Metrics for WFP data processing."""
    
    raw_records: int = 0
    processed_records: int = 0
    markets_created: int = 0
    price_observations: int = 0
    exchange_rate_observations: int = 0
    validation_errors: int = 0
    quality_warnings: int = 0
    processing_time_seconds: float = 0.0


class WFPProcessor:
    """Processes WFP price data into V2 domain entities."""
    
    def __init__(
        self,
        config: WFPProcessingConfig,
        metrics: MetricsCollector,
        logger: Optional = None
    ):
        self.config = config
        self.metrics = metrics
        self.logger = logger or get_logger(__name__)
        
        # Control zone mappings
        self.houthi_governorates = {
            "Sana'a", "Sa'dah", "Hajjah", "Al Mahwit", "Dhamar",
            "Raymah", "Ibb", "Amran", "Al Hodeidah"
        }
        
        self.government_governorates = {
            "Aden", "Lahj", "Abyan", "Shabwah", "Hadramawt",
            "Al Maharah", "Socotra", "Ad Dale'", "Marib", "Al Jawf"
        }
    
    async def process_price_data(
        self,
        raw_data: pd.DataFrame,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, List]:
        """Main processing pipeline for WFP price data."""
        
        start_time = datetime.utcnow()
        metrics = ProcessingMetrics(raw_records=len(raw_data))
        
        try:
            self.logger.info("Starting WFP data processing", extra={
                "raw_records": len(raw_data),
                "start_date": start_date,
                "end_date": end_date
            })
            
            # Step 1: Clean and validate raw data
            cleaned_data = await self._clean_raw_data(raw_data, start_date, end_date)
            self.metrics.gauge("wfp.cleaned_records", len(cleaned_data))
            
            # Step 2: Extract markets
            markets = await self._extract_markets(cleaned_data)
            metrics.markets_created = len(markets)
            self.metrics.gauge("wfp.markets_extracted", len(markets))
            
            # Step 3: Extract price observations
            price_observations = await self._extract_price_observations(cleaned_data, markets)
            metrics.price_observations = len(price_observations)
            self.metrics.gauge("wfp.price_observations_extracted", len(price_observations))
            
            # Step 4: Extract exchange rate observations
            exchange_rate_observations = await self._extract_exchange_rates(cleaned_data, markets)
            metrics.exchange_rate_observations = len(exchange_rate_observations)
            self.metrics.gauge("wfp.exchange_rates_extracted", len(exchange_rate_observations))
            
            # Step 5: Quality validation
            if self.config.enable_quality_checks:
                await self._validate_data_quality(
                    markets, price_observations, exchange_rate_observations
                )
            
            end_time = datetime.utcnow()
            metrics.processing_time_seconds = (end_time - start_time).total_seconds()
            metrics.processed_records = len(cleaned_data)
            
            # Update metrics
            self._update_processing_metrics(metrics)
            
            self.logger.info("WFP data processing completed", extra={
                "metrics": metrics.__dict__
            })
            
            return {
                "markets": markets,
                "price_observations": price_observations,
                "exchange_rate_observations": exchange_rate_observations,
                "processing_metrics": metrics
            }
            
        except Exception as e:
            self.metrics.increment_counter("wfp.processing_errors", {"error_type": type(e).__name__})
            self.logger.error("WFP data processing failed", extra={
                "error": str(e),
                "records": len(raw_data)
            })
            raise
    
    async def _clean_raw_data(
        self,
        raw_data: pd.DataFrame,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> pd.DataFrame:
        """Clean and standardize raw WFP data."""
        
        df = raw_data.copy()
        
        # Skip header rows if present
        df = df[df['market'] != '#adm2+name']
        
        # Standardize column names
        column_mapping = {
            'date': 'date',
            'admin1': 'governorate',
            'admin2': 'district', 
            'market': 'market_name',
            'latitude': 'lat',
            'longitude': 'lon',
            'commodity': 'commodity',
            'unit': 'unit',
            'price': 'price_local',
            'usdprice': 'price_usd',
            'currency': 'currency'
        }
        
        # Rename columns that exist
        rename_dict = {k: v for k, v in column_mapping.items() if k in df.columns}
        df = df.rename(columns=rename_dict)
        
        # Convert data types
        df['date'] = pd.to_datetime(df['date'], errors='coerce')
        
        numeric_cols = ['price_local', 'price_usd', 'lat', 'lon']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Filter by date range
        if start_date:
            df = df[df['date'] >= start_date]
        if end_date:
            df = df[df['date'] <= end_date]
        
        # Standardize text fields
        text_cols = ['governorate', 'district', 'market_name', 'commodity']
        for col in text_cols:
            if col in df.columns:
                df[col] = df[col].astype(str).str.strip().str.title()
        
        # Apply governorate name mappings
        if 'governorate' in df.columns:
            df['governorate'] = df['governorate'].replace(self.config.governorate_mappings)
        
        # Filter out invalid records
        df = df.dropna(subset=['date', 'governorate', 'market_name'])
        
        # Add derived fields
        df['market_id'] = (df['governorate'] + '_' + df['market_name']).str.replace(' ', '_')
        df['year_month'] = df['date'].dt.to_period('M')
        
        self.logger.info("Data cleaning completed", extra={
            "original_records": len(raw_data),
            "cleaned_records": len(df),
            "filtered_out": len(raw_data) - len(df)
        })
        
        return df
    
    async def _extract_markets(self, df: pd.DataFrame) -> List[Market]:
        """Extract unique markets from WFP data."""
        
        # Get unique market records with most complete information
        market_groups = df.groupby('market_id').agg({
            'governorate': 'first',
            'district': 'first', 
            'market_name': 'first',
            'lat': lambda x: x.dropna().iloc[0] if not x.dropna().empty else None,
            'lon': lambda x: x.dropna().iloc[0] if not x.dropna().empty else None,
            'date': 'min'  # First appearance date
        }).reset_index()
        
        markets = []
        
        for _, row in market_groups.iterrows():
            try:
                # Create coordinates if available
                coordinates = None
                if pd.notna(row['lat']) and pd.notna(row['lon']):
                    coordinates = Coordinates(
                        latitude=round(float(row['lat']), self.config.coordinate_precision),
                        longitude=round(float(row['lon']), self.config.coordinate_precision)
                    )
                
                # Determine control status from governorate
                control_status = self._determine_control_status(row['governorate'])
                
                # Determine market type (default to retail)
                market_type = MarketType.RETAIL
                if 'port' in row['market_name'].lower():
                    market_type = MarketType.PORT
                elif 'wholesale' in row['market_name'].lower():
                    market_type = MarketType.WHOLESALE
                elif 'rural' in row['district'].lower():
                    market_type = MarketType.RURAL
                
                market = Market(
                    id=uuid4(),
                    market_id=MarketId(row['market_id']),
                    name=row['market_name'],
                    coordinates=coordinates,
                    market_type=market_type,
                    governorate=row['governorate'],
                    district=row['district'],
                    active_since=row['date'].to_pydatetime(),
                    control_status=control_status,
                    control_status_date=row['date'].to_pydatetime()
                )
                
                markets.append(market)
                
            except (ValidationException, ValueError) as e:
                self.logger.warning("Failed to create market", extra={
                    "market_id": row['market_id'],
                    "error": str(e)
                })
                self.metrics.increment_counter("wfp.market_validation_errors")
                continue
        
        self.logger.info("Market extraction completed", extra={
            "markets_extracted": len(markets),
            "unique_market_records": len(market_groups)
        })
        
        return markets
    
    async def _extract_price_observations(
        self, 
        df: pd.DataFrame, 
        markets: List[Market]
    ) -> List[PriceObservation]:
        """Extract price observations from WFP data."""
        
        # Create market lookup
        market_lookup = {m.market_id.value: m for m in markets}
        
        # Filter for commodity price data (not exchange rates)
        price_data = df[
            (df['commodity'] != 'Exchange rate (unofficial)') &
            (df['commodity'].isin(self.config.key_commodities)) &
            (df['price_usd'].notna() | df['price_local'].notna())
        ].copy()
        
        observations = []
        
        for _, row in price_data.iterrows():
            try:
                market_id = MarketId(row['market_id'])
                
                # Skip if market not found
                if market_id.value not in market_lookup:
                    continue
                
                # Create commodity
                commodity = Commodity(
                    code=row['commodity'].replace(' ', '_').lower(),
                    name=row['commodity'],
                    category=self._determine_commodity_category(row['commodity']),
                    unit=row.get('unit', 'kg')
                )
                
                # Create price (prefer USD, fallback to local)
                price_amount = None
                currency = Currency.USD
                
                if pd.notna(row['price_usd']) and row['price_usd'] > 0:
                    price_amount = Decimal(str(row['price_usd']))
                    currency = Currency.USD
                elif pd.notna(row['price_local']) and row['price_local'] > 0:
                    price_amount = Decimal(str(row['price_local']))
                    currency = Currency.YER
                else:
                    continue  # Skip if no valid price
                
                price = Price(
                    amount=price_amount,
                    currency=currency,
                    unit=commodity.unit
                )
                
                # Create observation
                observation = PriceObservation(
                    id=uuid4(),
                    market_id=market_id,
                    commodity=commodity,
                    price=price,
                    observed_date=row['date'].to_pydatetime(),
                    source="WFP",
                    quality="standard"
                )
                
                observations.append(observation)
                
            except (ValidationException, ValueError, TypeError) as e:
                self.logger.warning("Failed to create price observation", extra={
                    "market_id": row.get('market_id'),
                    "commodity": row.get('commodity'),
                    "error": str(e)
                })
                self.metrics.increment_counter("wfp.price_validation_errors")
                continue
        
        # Remove outliers if enabled
        if self.config.enable_quality_checks:
            observations = await self._remove_price_outliers(observations)
        
        self.logger.info("Price observation extraction completed", extra={
            "observations_extracted": len(observations),
            "price_records_processed": len(price_data)
        })
        
        return observations
    
    async def _extract_exchange_rates(
        self,
        df: pd.DataFrame,
        markets: List[Market]
    ) -> List[ExchangeRateObservation]:
        """Extract exchange rate observations from WFP data."""
        
        # Create market lookup
        market_lookup = {m.market_id.value: m for m in markets}
        
        observations = []
        
        # Method 1: Direct exchange rate data
        exchange_data = df[df['commodity'] == 'Exchange rate (unofficial)'].copy()
        
        for _, row in exchange_data.iterrows():
            try:
                market_id = MarketId(row['market_id'])
                
                if market_id.value not in market_lookup:
                    continue
                
                # Use parallel rate if available, otherwise official rate
                rate_value = row.get('price_usd', row.get('price_local'))
                if pd.isna(rate_value) or rate_value <= 0:
                    continue
                
                exchange_rate = ExchangeRate(
                    from_currency=Currency.YER,
                    to_currency=Currency.USD,
                    rate=Decimal(str(rate_value))
                )
                
                observation = ExchangeRateObservation(
                    id=uuid4(),
                    market_id=market_id,
                    exchange_rate=exchange_rate,
                    observed_date=row['date'].to_pydatetime(),
                    source="WFP"
                )
                
                observations.append(observation)
                
            except (ValidationException, ValueError, TypeError) as e:
                self.logger.warning("Failed to create exchange rate observation", extra={
                    "market_id": row.get('market_id'),
                    "error": str(e)
                })
                continue
        
        # Method 2: Implied exchange rates from price pairs
        implied_rates = await self._calculate_implied_exchange_rates(df, market_lookup)
        observations.extend(implied_rates)
        
        self.logger.info("Exchange rate extraction completed", extra={
            "observations_extracted": len(observations),
            "direct_rates": len(exchange_data),
            "implied_rates": len(implied_rates)
        })
        
        return observations
    
    async def _calculate_implied_exchange_rates(
        self,
        df: pd.DataFrame,
        market_lookup: Dict[str, Market]
    ) -> List[ExchangeRateObservation]:
        """Calculate implied exchange rates from price pairs."""
        
        # Find records with both local and USD prices
        paired_prices = df[
            (df['commodity'] != 'Exchange rate (unofficial)') &
            (df['price_local'].notna()) &
            (df['price_usd'].notna()) &
            (df['price_local'] > 0) &
            (df['price_usd'] > 0)
        ].copy()
        
        observations = []
        
        # Calculate implied rates by market-date
        for (market_id, date), group in paired_prices.groupby(['market_id', 'date']):
            if market_id not in market_lookup:
                continue
            
            # Calculate median implied rate for this market-date
            group['implied_rate'] = group['price_local'] / group['price_usd']
            
            # Filter outliers
            median_rate = group['implied_rate'].median()
            mad = (group['implied_rate'] - median_rate).abs().median()
            threshold = median_rate + 3 * mad
            
            valid_rates = group[group['implied_rate'] <= threshold]['implied_rate']
            
            if len(valid_rates) == 0:
                continue
            
            final_rate = valid_rates.median()
            
            # Validate rate is reasonable for Yemen (100-2500 YER/USD)
            if not (100 <= final_rate <= 2500):
                continue
            
            try:
                exchange_rate = ExchangeRate(
                    from_currency=Currency.YER,
                    to_currency=Currency.USD,
                    rate=Decimal(str(final_rate))
                )
                
                observation = ExchangeRateObservation(
                    id=uuid4(),
                    market_id=MarketId(market_id),
                    exchange_rate=exchange_rate,
                    observed_date=date.to_pydatetime(),
                    source="WFP_IMPLIED"
                )
                
                observations.append(observation)
                
            except (ValidationException, ValueError) as e:
                continue
        
        return observations
    
    async def _remove_price_outliers(
        self,
        observations: List[PriceObservation]
    ) -> List[PriceObservation]:
        """Remove price outliers using statistical methods."""
        
        if not observations:
            return observations
        
        # Group by commodity for outlier detection
        commodity_groups = {}
        for obs in observations:
            key = (obs.commodity.code, obs.price.currency.value)
            if key not in commodity_groups:
                commodity_groups[key] = []
            commodity_groups[key].append(obs)
        
        filtered_observations = []
        
        for (commodity_code, currency), group in commodity_groups.items():
            if len(group) < 3:  # Need minimum observations for outlier detection
                filtered_observations.extend(group)
                continue
            
            # Calculate statistics
            prices = [float(obs.price.amount) for obs in group]
            mean_price = Decimal(str(np.mean(prices)))
            std_price = Decimal(str(np.std(prices)))
            
            # Filter outliers
            valid_observations = []
            for obs in group:
                if not obs.is_outlier(mean_price, std_price, self.config.outlier_threshold):
                    valid_observations.append(obs)
                else:
                    self.logger.debug("Removed price outlier", extra={
                        "commodity": commodity_code,
                        "price": float(obs.price.amount),
                        "mean": float(mean_price),
                        "std": float(std_price)
                    })
                    self.metrics.increment_counter("wfp.outliers_removed")
            
            filtered_observations.extend(valid_observations)
        
        return filtered_observations
    
    async def _validate_data_quality(
        self,
        markets: List[Market],
        price_observations: List[PriceObservation],
        exchange_rate_observations: List[ExchangeRateObservation]
    ) -> None:
        """Validate overall data quality."""
        
        quality_issues = []
        
        # Check market coordinate coverage
        markets_with_coords = sum(1 for m in markets if m.coordinates is not None)
        coord_coverage = markets_with_coords / len(markets) if markets else 0
        
        if coord_coverage < 0.8:
            quality_issues.append(f"Low coordinate coverage: {coord_coverage:.1%}")
            self.metrics.gauge("wfp.coordinate_coverage", coord_coverage)
        
        # Check price currency distribution
        if price_observations:
            usd_prices = sum(1 for p in price_observations if p.price.currency == Currency.USD)
            usd_ratio = usd_prices / len(price_observations)
            
            if usd_ratio < 0.5:
                quality_issues.append(f"Low USD price coverage: {usd_ratio:.1%}")
            
            self.metrics.gauge("wfp.usd_price_ratio", usd_ratio)
        
        # Check commodity coverage
        commodities = set(p.commodity.code for p in price_observations)
        expected_commodities = set(c.replace(' ', '_').lower() for c in self.config.key_commodities)
        missing_commodities = expected_commodities - commodities
        
        if missing_commodities:
            quality_issues.append(f"Missing commodities: {missing_commodities}")
        
        # Check temporal coverage
        if price_observations:
            dates = [p.observed_date for p in price_observations]
            date_range_days = (max(dates) - min(dates)).days
            unique_dates = len(set(d.date() for d in dates))
            
            temporal_density = unique_dates / max(1, date_range_days)
            if temporal_density < 0.1:  # Less than 10% of days have data
                quality_issues.append(f"Low temporal density: {temporal_density:.2%}")
        
        # Log quality issues
        if quality_issues:
            self.logger.warning("Data quality issues detected", extra={
                "issues": quality_issues
            })
            self.metrics.gauge("wfp.quality_issues", len(quality_issues))
        else:
            self.logger.info("Data quality validation passed")
            self.metrics.gauge("wfp.quality_issues", 0)
    
    def _determine_control_status(self, governorate: str) -> ControlStatus:
        """Determine control status based on governorate."""
        if governorate in self.houthi_governorates:
            return ControlStatus.HOUTHI
        elif governorate in self.government_governorates:
            return ControlStatus.GOVERNMENT
        else:
            return ControlStatus.CONTESTED
    
    def _determine_commodity_category(self, commodity_name: str) -> str:
        """Determine commodity category from name."""
        name_lower = commodity_name.lower()
        
        if any(grain in name_lower for grain in ['wheat', 'rice', 'sorghum', 'millet']):
            return 'grains'
        elif any(legume in name_lower for legume in ['beans', 'lentils', 'peas']):
            return 'legumes'
        elif any(fuel in name_lower for fuel in ['fuel', 'diesel', 'petrol', 'gas']):
            return 'fuel'
        elif 'oil' in name_lower:
            return 'oils'
        elif any(vegetable in name_lower for vegetable in ['onions', 'potatoes', 'tomatoes']):
            return 'vegetables'
        elif any(protein in name_lower for protein in ['meat', 'chicken', 'eggs']):
            return 'proteins'
        else:
            return 'other'
    
    def _update_processing_metrics(self, metrics: ProcessingMetrics) -> None:
        """Update processing metrics."""
        self.metrics.gauge("wfp.processing_time_seconds", metrics.processing_time_seconds)
        self.metrics.gauge("wfp.raw_records", metrics.raw_records)
        self.metrics.gauge("wfp.processed_records", metrics.processed_records)
        self.metrics.gauge("wfp.markets_created", metrics.markets_created)
        self.metrics.gauge("wfp.price_observations", metrics.price_observations)
        self.metrics.gauge("wfp.exchange_rate_observations", metrics.exchange_rate_observations)
        self.metrics.gauge("wfp.validation_errors", metrics.validation_errors)
        
        # Calculate success rate
        success_rate = metrics.processed_records / metrics.raw_records if metrics.raw_records > 0 else 0
        self.metrics.gauge("wfp.processing_success_rate", success_rate)