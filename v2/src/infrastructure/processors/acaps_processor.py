"""ACAPS control area processor for V2 architecture.

This processor handles ACAPS territorial control data, converting it to V2 domain entities
with proper temporal alignment, change detection, and validation.
"""

import asyncio
import zipfile
import shutil
from dataclasses import dataclass
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Union
import pandas as pd
import numpy as np
from uuid import uuid4

from core.domain.market.value_objects import ControlStatus
from core.domain.geography.entities import TerritorialControl
from core.domain.shared.exceptions import ValidationException, DataQualityException
from infrastructure.observability.metrics import MetricsCollector
from infrastructure.logging import get_logger


@dataclass
class ControlAreaRecord:
    """Represents a control area record from ACAPS data."""
    
    governorate: str
    district: str
    control_status: ControlStatus
    effective_date: datetime
    source_file: str
    confidence: str = "high"
    
    def __post_init__(self):
        """Validate control area record."""
        if not self.governorate:
            raise ValidationException("Governorate is required")
        if not self.district:
            raise ValidationException("District is required")
        if self.effective_date > datetime.utcnow():
            raise ValidationException("Effective date cannot be in the future")


@dataclass
class ACAPSProcessingConfig:
    """Configuration for ACAPS data processing."""
    
    data_directory: Path = None
    temporal_resolution: str = "monthly"  # "monthly" or "biweekly"
    enable_change_detection: bool = True
    enable_quality_checks: bool = True
    max_file_age_days: int = 365
    
    # Control zone mappings
    control_zone_mappings: Dict[str, ControlStatus] = None
    
    # Governorate name standardization
    governorate_mappings: Dict[str, str] = None
    
    def __post_init__(self):
        if self.control_zone_mappings is None:
            self.control_zone_mappings = {
                'DFA': ControlStatus.HOUTHI,
                'IRG': ControlStatus.GOVERNMENT,
                'STC': ControlStatus.STC,
                'AQAP': ControlStatus.AQAP,
                'contested': ControlStatus.CONTESTED,
                'mixed': ControlStatus.CONTESTED,
                'disputed': ControlStatus.CONTESTED,
                'unknown': ControlStatus.UNKNOWN
            }
        
        if self.governorate_mappings is None:
            self.governorate_mappings = {
                "Al Dhale'e": "Ad Dale'",
                "Al Hudaydah": "Al Hodeidah", 
                "Amanat Al Asimah": "Sana'a City",
                "Hadramaut": "Hadramawt",
                "Sa'ada": "Sa'dah",
                "Taizz": "Ta'iz"
            }


@dataclass
class ControlProcessingMetrics:
    """Metrics for ACAPS data processing."""
    
    files_processed: int = 0
    raw_records: int = 0
    processed_records: int = 0
    control_changes_detected: int = 0
    validation_errors: int = 0
    quality_warnings: int = 0
    processing_time_seconds: float = 0.0


class ACAPSProcessor:
    """Processes ACAPS territorial control data into V2 domain entities."""
    
    def __init__(
        self,
        config: ACAPSProcessingConfig,
        metrics: MetricsCollector,
        logger: Optional = None
    ):
        self.config = config
        self.metrics = metrics
        self.logger = logger or get_logger(__name__)
        
        # Temporary extraction directory
        self.temp_dir = Path("/tmp/acaps_extraction")
        self.temp_dir.mkdir(exist_ok=True)
    
    async def process_control_areas(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[ControlAreaRecord]:
        """Main processing pipeline for ACAPS control area data."""
        
        start_time = datetime.utcnow()
        metrics = ControlProcessingMetrics()
        
        try:
            self.logger.info("Starting ACAPS data processing", extra={
                "start_date": start_date,
                "end_date": end_date,
                "data_directory": str(self.config.data_directory)
            })
            
            # Step 1: Find and process all ACAPS files
            acaps_files = await self._find_acaps_files()
            metrics.files_processed = len(acaps_files)
            
            if not acaps_files:
                self.logger.warning("No ACAPS files found")
                return []
            
            # Step 2: Extract and combine data from all files
            all_control_data = []
            for file_path in acaps_files:
                file_data = await self._process_single_file(file_path, start_date, end_date)
                all_control_data.extend(file_data)
                metrics.raw_records += len(file_data)
            
            self.metrics.gauge("acaps.files_processed", len(acaps_files))
            self.metrics.gauge("acaps.raw_records", metrics.raw_records)
            
            # Step 3: Standardize and validate data
            standardized_data = await self._standardize_control_data(all_control_data)
            
            # Step 4: Temporal alignment
            aligned_data = await self._align_temporal_data(standardized_data)
            metrics.processed_records = len(aligned_data)
            
            # Step 5: Change detection
            if self.config.enable_change_detection:
                change_events = await self._detect_control_changes(aligned_data)
                metrics.control_changes_detected = len(change_events)
                self.metrics.gauge("acaps.control_changes", len(change_events))
            
            # Step 6: Quality validation
            if self.config.enable_quality_checks:
                await self._validate_data_quality(aligned_data)
            
            end_time = datetime.utcnow()
            metrics.processing_time_seconds = (end_time - start_time).total_seconds()
            
            # Update metrics
            self._update_processing_metrics(metrics)
            
            self.logger.info("ACAPS data processing completed", extra={
                "metrics": metrics.__dict__
            })
            
            return aligned_data
            
        except Exception as e:
            self.metrics.increment_counter("acaps.processing_errors", {"error_type": type(e).__name__})
            self.logger.error("ACAPS data processing failed", extra={
                "error": str(e)
            })
            raise
        finally:
            # Cleanup temporary files
            await self._cleanup_temp_files()
    
    async def _find_acaps_files(self) -> List[Path]:
        """Find all ACAPS control area files in the data directory."""
        
        if not self.config.data_directory or not self.config.data_directory.exists():
            raise DataQualityException(f"ACAPS data directory not found: {self.config.data_directory}")
        
        # Look for ACAPS files with various naming patterns
        patterns = [
            "*Areas*control*.zip",
            "*Yemen*Analysis*.zip",
            "*ACAPS*.zip",
            "*areas*control*.zip"
        ]
        
        files = []
        for pattern in patterns:
            files.extend(self.config.data_directory.glob(pattern))
        
        # Remove duplicates and filter by age
        unique_files = list(set(files))
        recent_files = []
        
        cutoff_date = datetime.utcnow() - timedelta(days=self.config.max_file_age_days)
        
        for file_path in unique_files:
            # Get file modification time
            mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
            if mod_time >= cutoff_date:
                recent_files.append(file_path)
        
        # Sort by modification time (newest first)
        recent_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
        
        self.logger.info("Found ACAPS files", extra={
            "total_files": len(unique_files),
            "recent_files": len(recent_files),
            "cutoff_date": cutoff_date.isoformat()
        })
        
        return recent_files
    
    async def _process_single_file(
        self,
        file_path: Path,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[ControlAreaRecord]:
        """Process a single ACAPS ZIP file."""
        
        try:
            self.logger.debug("Processing ACAPS file", extra={"file": file_path.name})
            
            # Extract date from filename
            file_date = self._extract_date_from_filename(file_path.name)
            
            # Filter by date range if specified
            if start_date and file_date and file_date < start_date:
                return []
            if end_date and file_date and file_date > end_date:
                return []
            
            # Extract and process file contents
            extracted_data = await self._extract_file_contents(file_path)
            
            # Convert to control area records
            control_records = []
            for record in extracted_data:
                try:
                    control_record = ControlAreaRecord(
                        governorate=record.get('governorate', ''),
                        district=record.get('district', ''),
                        control_status=self._map_control_status(record.get('control_zone', '')),
                        effective_date=file_date or datetime.utcnow(),
                        source_file=file_path.name,
                        confidence=record.get('confidence', 'medium')
                    )
                    control_records.append(control_record)
                    
                except ValidationException as e:
                    self.logger.warning("Invalid control record", extra={
                        "file": file_path.name,
                        "record": record,
                        "error": str(e)
                    })
                    continue
            
            return control_records
            
        except Exception as e:
            self.logger.error("Failed to process ACAPS file", extra={
                "file": file_path.name,
                "error": str(e)
            })
            return []
    
    async def _extract_file_contents(self, file_path: Path) -> List[Dict]:
        """Extract data from ACAPS ZIP file."""
        
        extraction_dir = self.temp_dir / f"extract_{file_path.stem}"
        extraction_dir.mkdir(exist_ok=True)
        
        try:
            # Extract main ZIP
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                zip_file.extractall(extraction_dir)
            
            # Look for nested ZIPs and extract them
            nested_zips = list(extraction_dir.rglob("*.zip"))
            for nested_zip in nested_zips:
                if 'shapefile' in nested_zip.name.lower():
                    nested_dir = nested_zip.parent / nested_zip.stem
                    nested_dir.mkdir(exist_ok=True)
                    with zipfile.ZipFile(nested_zip, 'r') as nz:
                        nz.extractall(nested_dir)
            
            # Look for data files (shapefiles, Excel, CSV)
            data_files = []
            data_files.extend(list(extraction_dir.rglob("*.shp")))
            data_files.extend(list(extraction_dir.rglob("*.xlsx")))
            data_files.extend(list(extraction_dir.rglob("*.csv")))
            
            if not data_files:
                raise DataQualityException(f"No data files found in {file_path}")
            
            # Process data files
            all_records = []
            
            for data_file in data_files:
                try:
                    if data_file.suffix.lower() == '.xlsx':
                        records = await self._process_excel_file(data_file)
                    elif data_file.suffix.lower() == '.csv':
                        records = await self._process_csv_file(data_file)
                    elif data_file.suffix.lower() == '.shp':
                        records = await self._process_shapefile(data_file)
                    else:
                        continue
                    
                    all_records.extend(records)
                    
                except Exception as e:
                    self.logger.warning("Failed to process data file", extra={
                        "file": data_file.name,
                        "error": str(e)
                    })
                    continue
            
            return all_records
            
        finally:
            # Cleanup extraction directory
            if extraction_dir.exists():
                shutil.rmtree(extraction_dir)
    
    async def _process_excel_file(self, file_path: Path) -> List[Dict]:
        """Process Excel file containing control data."""
        
        try:
            # Try reading different sheets
            df = pd.read_excel(file_path, sheet_name=0)
            return await self._process_dataframe(df)
            
        except Exception as e:
            self.logger.warning("Failed to process Excel file", extra={
                "file": file_path.name,
                "error": str(e)
            })
            return []
    
    async def _process_csv_file(self, file_path: Path) -> List[Dict]:
        """Process CSV file containing control data."""
        
        try:
            df = pd.read_csv(file_path)
            return await self._process_dataframe(df)
            
        except Exception as e:
            self.logger.warning("Failed to process CSV file", extra={
                "file": file_path.name,
                "error": str(e)
            })
            return []
    
    async def _process_shapefile(self, file_path: Path) -> List[Dict]:
        """Process shapefile containing control data."""
        
        try:
            import geopandas as gpd
            gdf = gpd.read_file(file_path)
            
            # Convert to regular DataFrame for processing
            df = pd.DataFrame(gdf.drop(columns='geometry'))
            return await self._process_dataframe(df)
            
        except ImportError:
            self.logger.warning("GeoPandas not available, skipping shapefile")
            return []
        except Exception as e:
            self.logger.warning("Failed to process shapefile", extra={
                "file": file_path.name,
                "error": str(e)
            })
            return []
    
    async def _process_dataframe(self, df: pd.DataFrame) -> List[Dict]:
        """Process DataFrame containing control data."""
        
        # Standardize column names
        column_mapping = {
            'ADM1_EN': 'governorate',
            'ADM2_EN': 'district',
            'adm1_name': 'governorate',
            'adm2_name': 'district',
            'irg_or_dfa': 'control_zone',
            'Control': 'control_zone',
            'control': 'control_zone',
            'Actor': 'control_zone',
            'actor': 'control_zone'
        }
        
        # Apply column mapping (case-insensitive)
        for col in df.columns:
            for old_name, new_name in column_mapping.items():
                if col.upper() == old_name.upper():
                    df = df.rename(columns={col: new_name})
                    break
        
        # Extract records
        records = []
        for _, row in df.iterrows():
            record = {
                'governorate': self._standardize_governorate(row.get('governorate', '')),
                'district': str(row.get('district', '')).strip(),
                'control_zone': str(row.get('control_zone', '')).strip(),
                'confidence': 'medium'
            }
            
            # Skip empty records
            if not record['governorate'] or not record['district']:
                continue
            
            records.append(record)
        
        return records
    
    async def _standardize_control_data(self, raw_data: List[ControlAreaRecord]) -> List[ControlAreaRecord]:
        """Standardize and validate control data."""
        
        standardized = []
        
        for record in raw_data:
            try:
                # Standardize governorate name
                std_governorate = self._standardize_governorate(record.governorate)
                
                # Create standardized record
                std_record = ControlAreaRecord(
                    governorate=std_governorate,
                    district=record.district.strip().title(),
                    control_status=record.control_status,
                    effective_date=record.effective_date,
                    source_file=record.source_file,
                    confidence=record.confidence
                )
                
                standardized.append(std_record)
                
            except ValidationException as e:
                self.logger.warning("Invalid control record during standardization", extra={
                    "record": record.__dict__,
                    "error": str(e)
                })
                self.metrics.increment_counter("acaps.validation_errors")
                continue
        
        return standardized
    
    async def _align_temporal_data(self, control_data: List[ControlAreaRecord]) -> List[ControlAreaRecord]:
        """Align control data to specified temporal resolution."""
        
        if self.config.temporal_resolution == "monthly":
            return await self._align_to_monthly(control_data)
        else:
            # Keep original bi-weekly resolution
            return control_data
    
    async def _align_to_monthly(self, control_data: List[ControlAreaRecord]) -> List[ControlAreaRecord]:
        """Align bi-weekly data to monthly resolution."""
        
        # Group by district and month
        monthly_data = {}
        
        for record in control_data:
            # Convert to first day of month
            month_key = record.effective_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            district_key = (record.governorate, record.district)
            
            key = (district_key, month_key)
            
            if key not in monthly_data:
                monthly_data[key] = []
            
            monthly_data[key].append(record)
        
        # For each district-month, take the latest record
        aligned_records = []
        
        for (district_key, month_date), records in monthly_data.items():
            # Sort by effective date and take the latest
            latest_record = max(records, key=lambda r: r.effective_date)
            
            # Update effective date to month start
            aligned_record = ControlAreaRecord(
                governorate=latest_record.governorate,
                district=latest_record.district,
                control_status=latest_record.control_status,
                effective_date=month_date,
                source_file=latest_record.source_file,
                confidence=latest_record.confidence
            )
            
            aligned_records.append(aligned_record)
        
        return aligned_records
    
    async def _detect_control_changes(self, control_data: List[ControlAreaRecord]) -> List[Dict]:
        """Detect control changes between time periods."""
        
        # Group by district
        district_data = {}
        for record in control_data:
            district_key = (record.governorate, record.district)
            if district_key not in district_data:
                district_data[district_key] = []
            district_data[district_key].append(record)
        
        change_events = []
        
        for district_key, records in district_data.items():
            # Sort by effective date
            sorted_records = sorted(records, key=lambda r: r.effective_date)
            
            # Detect changes
            for i in range(1, len(sorted_records)):
                prev_record = sorted_records[i-1]
                curr_record = sorted_records[i]
                
                if prev_record.control_status != curr_record.control_status:
                    change_event = {
                        'governorate': curr_record.governorate,
                        'district': curr_record.district,
                        'previous_control': prev_record.control_status,
                        'new_control': curr_record.control_status,
                        'change_date': curr_record.effective_date,
                        'days_in_previous_control': (curr_record.effective_date - prev_record.effective_date).days
                    }
                    
                    change_events.append(change_event)
        
        return change_events
    
    async def _validate_data_quality(self, control_data: List[ControlAreaRecord]) -> None:
        """Validate overall data quality."""
        
        quality_issues = []
        
        if not control_data:
            quality_issues.append("No control data processed")
            return
        
        # Check geographic coverage
        unique_governorates = set(record.governorate for record in control_data)
        if len(unique_governorates) < 15:  # Yemen has ~22 governorates
            quality_issues.append(f"Low governorate coverage: {len(unique_governorates)}")
        
        unique_districts = set((record.governorate, record.district) for record in control_data)
        if len(unique_districts) < 50:  # Yemen has many districts
            quality_issues.append(f"Low district coverage: {len(unique_districts)}")
        
        # Check temporal coverage
        dates = [record.effective_date for record in control_data]
        date_range_days = (max(dates) - min(dates)).days
        unique_dates = len(set(d.date() for d in dates))
        
        temporal_density = unique_dates / max(1, date_range_days)
        if temporal_density < 0.05:  # Less than 5% of days covered
            quality_issues.append(f"Low temporal density: {temporal_density:.2%}")
        
        # Check control status distribution
        control_counts = {}
        for record in control_data:
            status = record.control_status.value
            control_counts[status] = control_counts.get(status, 0) + 1
        
        total_records = len(control_data)
        unknown_ratio = control_counts.get('unknown', 0) / total_records
        if unknown_ratio > 0.3:
            quality_issues.append(f"High unknown control ratio: {unknown_ratio:.1%}")
        
        # Log quality issues
        if quality_issues:
            self.logger.warning("Data quality issues detected", extra={
                "issues": quality_issues
            })
            self.metrics.gauge("acaps.quality_issues", len(quality_issues))
        else:
            self.logger.info("Data quality validation passed")
            self.metrics.gauge("acaps.quality_issues", 0)
    
    def _extract_date_from_filename(self, filename: str) -> Optional[datetime]:
        """Extract date from ACAPS filename."""
        
        import re
        
        # Remove 'b' suffix if present
        filename = filename.replace('b ', ' ').replace('b_', '_')
        
        # Try different date patterns
        patterns = [
            r'(\d{8})',  # YYYYMMDD
            r'(\d{4})-?(\d{2})-?(\d{2})',  # YYYY-MM-DD
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename)
            if match:
                try:
                    if len(match.groups()) == 1:
                        # YYYYMMDD format
                        date_str = match.group(1)
                        return datetime.strptime(date_str, '%Y%m%d')
                    else:
                        # YYYY-MM-DD format
                        year, month, day = match.groups()
                        return datetime(int(year), int(month), int(day))
                except ValueError:
                    continue
        
        return None
    
    def _map_control_status(self, control_zone_str: str) -> ControlStatus:
        """Map control zone string to ControlStatus enum."""
        
        if not control_zone_str:
            return ControlStatus.UNKNOWN
        
        zone_lower = control_zone_str.lower().strip()
        
        # Direct mapping
        if zone_lower in self.config.control_zone_mappings:
            return self.config.control_zone_mappings[zone_lower]
        
        # Fuzzy matching
        if 'dfa' in zone_lower or 'houthi' in zone_lower:
            return ControlStatus.HOUTHI
        elif 'irg' in zone_lower or 'government' in zone_lower:
            return ControlStatus.GOVERNMENT
        elif 'stc' in zone_lower:
            return ControlStatus.STC
        elif 'aqap' in zone_lower:
            return ControlStatus.AQAP
        elif any(term in zone_lower for term in ['contested', 'mixed', 'disputed']):
            return ControlStatus.CONTESTED
        else:
            return ControlStatus.UNKNOWN
    
    def _standardize_governorate(self, governorate: str) -> str:
        """Standardize governorate name."""
        
        if not governorate:
            return ""
        
        # Apply mappings
        standardized = self.config.governorate_mappings.get(governorate, governorate)
        
        return standardized.strip().title()
    
    async def _cleanup_temp_files(self) -> None:
        """Clean up temporary extraction files."""
        
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                self.temp_dir.mkdir(exist_ok=True)
        except Exception as e:
            self.logger.warning("Failed to cleanup temp files", extra={"error": str(e)})
    
    def _update_processing_metrics(self, metrics: ControlProcessingMetrics) -> None:
        """Update processing metrics."""
        self.metrics.gauge("acaps.processing_time_seconds", metrics.processing_time_seconds)
        self.metrics.gauge("acaps.files_processed", metrics.files_processed)
        self.metrics.gauge("acaps.raw_records", metrics.raw_records)
        self.metrics.gauge("acaps.processed_records", metrics.processed_records)
        self.metrics.gauge("acaps.control_changes_detected", metrics.control_changes_detected)
        self.metrics.gauge("acaps.validation_errors", metrics.validation_errors)
        
        # Calculate success rate
        success_rate = metrics.processed_records / metrics.raw_records if metrics.raw_records > 0 else 0
        self.metrics.gauge("acaps.processing_success_rate", success_rate)