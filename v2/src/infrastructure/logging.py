"""Enhanced logging infrastructure for Yemen Market Integration v2.

This module provides a centralized logging system with structured logging,
performance monitoring, and integration with the v2 architecture.
"""

import json
import logging
import sys
import time
from contextlib import contextmanager
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional, Union
from functools import wraps

import structlog
from structlog.processors import CallsiteParameter, CallsiteParameterAdder


class Logger:
    """Enhanced logger with structured logging and performance monitoring.
    
    This logger provides:
    - Structured logging with JSON output
    - Performance timing decorators
    - Context binding for correlation
    - Integration with external monitoring systems
    """
    
    def __init__(self, name: str, level: str = "INFO"):
        """Initialize logger with structured logging configuration.
        
        Args:
            name: Logger name (typically __name__)
            level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        self.name = name
        self._setup_structured_logging(level)
        self.logger = structlog.get_logger(name)
        
    def _setup_structured_logging(self, level: str) -> None:
        """Configure structured logging with appropriate processors."""
        # Configure structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                CallsiteParameterAdder(
                    parameters=[CallsiteParameter.FILENAME, CallsiteParameter.LINENO]
                ),
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        # Configure standard logging
        logging.basicConfig(
            format="%(message)s",
            stream=sys.stdout,
            level=getattr(logging, level.upper()),
        )
    
    def bind(self, **kwargs) -> 'Logger':
        """Bind contextual information to the logger.
        
        Args:
            **kwargs: Key-value pairs to bind to all subsequent log messages
            
        Returns:
            Self for chaining
        """
        self.logger = self.logger.bind(**kwargs)
        return self
    
    def unbind(self, *keys) -> 'Logger':
        """Remove bound contextual information.
        
        Args:
            *keys: Keys to unbind from the logger
            
        Returns:
            Self for chaining
        """
        self.logger = self.logger.unbind(*keys)
        return self
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message with optional structured data."""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message with optional structured data."""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message with optional structured data."""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """Log error message with optional structured data."""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """Log critical message with optional structured data."""
        self.logger.critical(message, **kwargs)
    
    def exception(self, message: str, **kwargs) -> None:
        """Log exception with traceback."""
        self.logger.exception(message, **kwargs)
    
    @contextmanager
    def timer(self, operation: str, **extra_fields):
        """Context manager for timing operations.
        
        Args:
            operation: Name of the operation being timed
            **extra_fields: Additional fields to log
            
        Example:
            with logger.timer("database_query", query_type="select"):
                results = db.query("SELECT * FROM markets")
        """
        start_time = time.time()
        self.info(f"Starting {operation}", operation=operation, **extra_fields)
        
        try:
            yield
        finally:
            duration_ms = (time.time() - start_time) * 1000
            self.info(
                f"Completed {operation}",
                operation=operation,
                duration_ms=round(duration_ms, 2),
                **extra_fields
            )
    
    def log_performance(self, func):
        """Decorator for automatic performance logging.
        
        Args:
            func: Function to wrap with performance logging
            
        Returns:
            Wrapped function
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            with self.timer(f"{func.__module__}.{func.__name__}"):
                return func(*args, **kwargs)
        return wrapper
    
    def log_data_shape(self, data_name: str, data: Any) -> None:
        """Log the shape and basic statistics of data structures.
        
        Args:
            data_name: Name of the data being logged
            data: Data structure (DataFrame, array, etc.)
        """
        import pandas as pd
        import numpy as np
        
        log_data = {"data_name": data_name}
        
        if isinstance(data, pd.DataFrame):
            log_data.update({
                "type": "DataFrame",
                "shape": data.shape,
                "columns": list(data.columns),
                "dtypes": {col: str(dtype) for col, dtype in data.dtypes.items()},
                "null_counts": data.isnull().sum().to_dict(),
                "memory_usage_mb": data.memory_usage(deep=True).sum() / 1024 / 1024
            })
        elif isinstance(data, pd.Series):
            log_data.update({
                "type": "Series",
                "shape": data.shape,
                "dtype": str(data.dtype),
                "null_count": data.isnull().sum(),
                "unique_values": data.nunique()
            })
        elif isinstance(data, np.ndarray):
            log_data.update({
                "type": "ndarray",
                "shape": data.shape,
                "dtype": str(data.dtype),
                "size": data.size
            })
        elif isinstance(data, (list, tuple)):
            log_data.update({
                "type": type(data).__name__,
                "length": len(data)
            })
        elif isinstance(data, dict):
            log_data.update({
                "type": "dict",
                "keys": list(data.keys()),
                "length": len(data)
            })
        else:
            log_data.update({
                "type": type(data).__name__
            })
        
        self.debug("Data shape logged", **log_data)
    
    def log_model_metrics(
        self,
        model_name: str,
        metrics: Dict[str, Union[float, int]],
        dataset: str = "unknown"
    ) -> None:
        """Log model performance metrics.
        
        Args:
            model_name: Name of the model
            metrics: Dictionary of metric names and values
            dataset: Dataset identifier
        """
        self.info(
            "Model metrics",
            model_name=model_name,
            dataset=dataset,
            metrics=metrics,
            timestamp=datetime.utcnow().isoformat()
        )
    
    def log_api_request(
        self,
        method: str,
        endpoint: str,
        status_code: int,
        duration_ms: float,
        request_id: Optional[str] = None,
        **extra_fields
    ) -> None:
        """Log API request details.
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            status_code: Response status code
            duration_ms: Request duration in milliseconds
            request_id: Optional request ID for correlation
            **extra_fields: Additional fields to log
        """
        self.info(
            "API request",
            method=method,
            endpoint=endpoint,
            status_code=status_code,
            duration_ms=round(duration_ms, 2),
            request_id=request_id,
            **extra_fields
        )
    
    def log_database_query(
        self,
        query_type: str,
        table: str,
        duration_ms: float,
        rows_affected: Optional[int] = None,
        **extra_fields
    ) -> None:
        """Log database query details.
        
        Args:
            query_type: Type of query (SELECT, INSERT, UPDATE, DELETE)
            table: Table name
            duration_ms: Query duration in milliseconds
            rows_affected: Number of rows affected
            **extra_fields: Additional fields to log
        """
        self.debug(
            "Database query",
            query_type=query_type,
            table=table,
            duration_ms=round(duration_ms, 2),
            rows_affected=rows_affected,
            **extra_fields
        )


class AuditLogger:
    """Specialized logger for audit trails and compliance logging."""
    
    def __init__(self, audit_file: Optional[Path] = None):
        """Initialize audit logger.
        
        Args:
            audit_file: Optional path to audit log file
        """
        self.logger = Logger("audit")
        self.audit_file = audit_file or Path("logs/audit.jsonl")
        self.audit_file.parent.mkdir(parents=True, exist_ok=True)
    
    def log_event(
        self,
        event_type: str,
        user_id: Optional[str] = None,
        resource: Optional[str] = None,
        action: Optional[str] = None,
        result: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log an audit event.
        
        Args:
            event_type: Type of event (e.g., "data_access", "model_training")
            user_id: ID of user performing action
            resource: Resource being accessed/modified
            action: Action performed
            result: Result of action (success/failure)
            metadata: Additional event metadata
        """
        audit_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type,
            "user_id": user_id,
            "resource": resource,
            "action": action,
            "result": result,
            "metadata": metadata or {}
        }
        
        # Log to structured logger
        self.logger.info("Audit event", **audit_entry)
        
        # Also write to audit file
        with open(self.audit_file, "a") as f:
            f.write(json.dumps(audit_entry) + "\n")


# Global logger instance for module-level logging
_module_logger = Logger(__name__)

# Convenience functions for module-level logging
debug = _module_logger.debug
info = _module_logger.info
warning = _module_logger.warning
error = _module_logger.error
critical = _module_logger.critical
exception = _module_logger.exception
timer = _module_logger.timer
log_performance = _module_logger.log_performance
log_data_shape = _module_logger.log_data_shape
log_model_metrics = _module_logger.log_model_metrics