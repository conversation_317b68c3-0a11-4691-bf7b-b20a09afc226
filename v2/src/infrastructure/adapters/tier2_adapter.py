"""Tier 2 Commodity-Specific Model Adapter.

Adapts V1 commodity-specific threshold VECM models for use in V2 architecture.
"""

import sys
from pathlib import Path
from typing import Any, Dict, List, Optional
import pandas as pd
import numpy as np
from datetime import datetime

from .v1_model_adapter import V1ModelAdapter
from ...core.domain.market.entities import PanelData
from ...core.domain.market.value_objects import Currency, Commodity
from ...infrastructure.logging import Logger

logger = Logger(__name__)


class Tier2Adapter(V1ModelAdapter):
    """Adapter for V1 Tier 2 commodity-specific models.
    
    This adapter integrates:
    - Commodity-specific threshold VECM models
    - Cointegration testing
    - Regime-dependent dynamics
    - Cross-commodity heterogeneity
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Tier 2 adapter.
        
        Parameters
        ----------
        config : dict, optional
            Configuration including:
            - min_markets: Minimum markets required (default: 3)
            - min_periods: Minimum time periods (default: 50)
            - threshold_test: Run threshold tests (default: True)
            - max_lags: Maximum lags for VECM (default: 4)
        """
        super().__init__(config)
        
        # Tier 2 specific configuration
        self.min_markets = self.config.get('min_markets', 3)
        self.min_periods = self.config.get('min_periods', 50)
        self.threshold_test = self.config.get('threshold_test', True)
        self.max_lags = self.config.get('max_lags', 4)
        
        # Ensure V1 code is available
        self._ensure_v1_available()
        
        logger.info(
            f"Initialized Tier2Adapter with min_markets={self.min_markets}, "
            f"threshold_test={self.threshold_test}"
        )
    
    def _ensure_v1_available(self) -> None:
        """Ensure V1 code is available for import."""
        v1_src = Path(__file__).parent.parent.parent.parent.parent / "src"
        if v1_src.exists() and str(v1_src) not in sys.path:
            sys.path.insert(0, str(v1_src))
    
    async def prepare_data(self, panel_data: PanelData, 
                          commodity: Optional[str] = None) -> pd.DataFrame:
        """Convert V2 panel data to V1 format for commodity-specific analysis.
        
        Parameters
        ----------
        panel_data : PanelData
            V2 domain model panel data
        commodity : str, optional
            Specific commodity to extract. If None, includes all.
            
        Returns
        -------
        pd.DataFrame
            Data formatted for commodity-specific analysis
        """
        logger.info(f"Preparing data for Tier 2 analysis (commodity={commodity})")
        
        # Initialize lists for DataFrame construction
        data_records = []
        
        # Extract observations
        for obs in panel_data.observations:
            # Filter by commodity if specified
            if commodity and obs.commodity.code != commodity:
                continue
            
            # Convert price to USD
            price_usd = obs.price.convert_to(Currency.USD).amount
            
            record = {
                'market': str(obs.market_id),
                'commodity': obs.commodity.code,
                'commodity_name': obs.commodity.name,
                'date': obs.observed_date,
                'usd_price': price_usd,
                'log_price': np.log(price_usd) if price_usd > 0 else np.nan,
                'conflict_intensity': obs.conflict_intensity if hasattr(obs, 'conflict_intensity') else 0,
                'exchange_rate': obs.exchange_rate if hasattr(obs, 'exchange_rate') else 1,
                'global_price': obs.global_price_index if hasattr(obs, 'global_price_index') else 100
            }
            
            data_records.append(record)
        
        # Create DataFrame
        df = pd.DataFrame(data_records)
        
        if df.empty:
            logger.warning(f"No data found for commodity={commodity}")
            return df
        
        # For commodity-specific analysis, pivot to wide format (markets as columns)
        if commodity:
            # Pivot price data
            price_wide = df.pivot(
                index='date',
                columns='market',
                values='usd_price'
            )
            
            # Add commodity metadata
            price_wide.attrs['commodity'] = commodity
            price_wide.attrs['n_markets'] = price_wide.columns.nunique()
            price_wide.attrs['n_periods'] = len(price_wide)
            
            logger.info(
                f"Prepared commodity data: {commodity} with "
                f"{price_wide.attrs['n_markets']} markets, "
                f"{price_wide.attrs['n_periods']} periods"
            )
            
            return price_wide
        else:
            # Return full panel for all commodities
            return df
    
    async def run_model(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Execute V1 commodity-specific model.
        
        Parameters
        ----------
        data : pd.DataFrame
            Either wide-format price data for single commodity
            or full panel data
        **kwargs
            Additional parameters:
            - commodity: Specific commodity to analyze
            - test_threshold: Whether to test for threshold effects
            
        Returns
        -------
        dict
            Raw results from commodity analysis
        """
        try:
            from yemen_market.models.three_tier.tier2_commodity import (
                CommodityExtractor, CommodityExtractorConfig
            )
            
            # Configure extractor
            config_dict = {
                'min_markets': self.min_markets,
                'min_periods': self.min_periods,
                'threshold_test': self.threshold_test,
                'max_lags': self.max_lags,
                'required_columns': ['market', 'commodity', 'date', 'usd_price']
            }
            
            extractor_config = CommodityExtractorConfig(**config_dict)
            extractor = CommodityExtractor(extractor_config)
            
            # Get commodity to analyze
            commodity = kwargs.get('commodity')
            
            if commodity:
                # Single commodity analysis
                logger.info(f"Running commodity-specific analysis for {commodity}")
                
                # If data is already in wide format, convert back for extractor
                if isinstance(data.columns, pd.Index) and 'commodity' not in data.columns:
                    # Reconstruct long format
                    data_long = data.reset_index().melt(
                        id_vars=['date'],
                        var_name='market',
                        value_name='usd_price'
                    )
                    data_long['commodity'] = commodity
                    data_long = data_long.dropna()
                else:
                    data_long = data
                
                # Run analysis
                results = extractor.analyze_commodity(data_long, commodity)
                
                return {
                    'commodity': commodity,
                    'results': results,
                    'extractor': extractor,
                    'metadata': {
                        'n_markets': results.get('n_markets', 0),
                        'n_periods': results.get('n_periods', 0),
                        'estimation_time': datetime.utcnow().isoformat()
                    }
                }
            else:
                # Analyze all commodities
                logger.info("Running analysis for all commodities")
                
                all_results = {}
                commodities = data['commodity'].unique()
                
                for comm in commodities:
                    try:
                        comm_results = extractor.analyze_commodity(data, comm)
                        all_results[comm] = comm_results
                    except Exception as e:
                        logger.warning(f"Failed to analyze {comm}: {str(e)}")
                        all_results[comm] = {'error': str(e)}
                
                return {
                    'all_commodities': all_results,
                    'extractor': extractor,
                    'metadata': {
                        'n_commodities': len(commodities),
                        'estimation_time': datetime.utcnow().isoformat()
                    }
                }
                
        except Exception as e:
            logger.error(f"Error running Tier 2 model: {str(e)}")
            raise
    
    async def extract_results(self, raw_results: Any) -> Dict[str, Any]:
        """Extract and format Tier 2 results."""
        logger.info("Extracting Tier 2 results")
        
        # Handle single commodity results
        if 'commodity' in raw_results:
            return await self._extract_commodity_results(
                raw_results['commodity'],
                raw_results['results'],
                raw_results.get('metadata', {})
            )
        
        # Handle multiple commodity results
        elif 'all_commodities' in raw_results:
            extracted_results = {}
            
            for commodity, comm_results in raw_results['all_commodities'].items():
                if 'error' not in comm_results:
                    extracted_results[commodity] = await self._extract_commodity_results(
                        commodity,
                        comm_results,
                        {}
                    )
                else:
                    extracted_results[commodity] = {
                        'error': comm_results['error'],
                        'commodity': commodity
                    }
            
            return {
                'commodity_results': extracted_results,
                'summary': self._generate_tier2_summary(extracted_results),
                'metadata': raw_results.get('metadata', {}),
                'tier': 2,
                'timestamp': datetime.utcnow().isoformat()
            }
        
        else:
            raise ValueError("Invalid Tier 2 results format")
    
    async def _extract_commodity_results(self, commodity: str, 
                                       results: Dict[str, Any],
                                       metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Extract results for a single commodity."""
        
        # Extract key metrics
        extracted = {
            'commodity': commodity,
            'integration_level': results.get('integration_level', 'Unknown'),
            'model_type': results.get('model_type', 'threshold_vecm'),
            'n_markets': results.get('n_markets', 0),
            'n_observations': results.get('n_observations', 0),
            'metadata': metadata
        }
        
        # Extract threshold information if available
        if 'threshold_test' in results:
            threshold_info = results['threshold_test']
            extracted['threshold'] = {
                'has_threshold': threshold_info.get('has_threshold', False),
                'threshold_value': threshold_info.get('threshold_value'),
                'p_value': threshold_info.get('p_value'),
                'regimes': threshold_info.get('regimes', {})
            }
        
        # Extract cointegration results
        if 'cointegration' in results:
            coint_info = results['cointegration']
            extracted['cointegration'] = {
                'is_cointegrated': coint_info.get('is_cointegrated', False),
                'rank': coint_info.get('rank', 0),
                'test_statistic': coint_info.get('test_statistic'),
                'critical_values': coint_info.get('critical_values', {})
            }
        
        # Extract model coefficients if available
        if 'coefficients' in results:
            extracted['coefficients'] = results['coefficients']
            extracted['standard_errors'] = results.get('standard_errors', {})
            extracted['p_values'] = results.get('p_values', {})
        
        # Run diagnostics if model results available
        if 'model_results' in results:
            try:
                # Note: Need to pass appropriate data format for diagnostics
                diagnostics = await self.run_diagnostics(
                    results['model_results'],
                    results.get('data', pd.DataFrame())
                )
                extracted['diagnostics'] = diagnostics
            except Exception as e:
                logger.warning(f"Failed to run diagnostics for {commodity}: {str(e)}")
        
        # Determine if results meet quality thresholds
        extracted['meets_quality_threshold'] = (
            extracted['n_markets'] >= self.min_markets and
            extracted['n_observations'] >= self.min_periods
        )
        
        return extracted
    
    def _generate_tier2_summary(self, commodity_results: Dict[str, Dict]) -> Dict[str, Any]:
        """Generate summary statistics across all commodities."""
        
        # Count commodities by integration level
        integration_counts = {
            'High': 0,
            'Medium': 0,
            'Low': 0,
            'Unknown': 0
        }
        
        threshold_count = 0
        cointegrated_count = 0
        quality_count = 0
        
        for comm_name, comm_results in commodity_results.items():
            if 'error' not in comm_results:
                # Integration level
                level = comm_results.get('integration_level', 'Unknown')
                integration_counts[level] += 1
                
                # Threshold effects
                if comm_results.get('threshold', {}).get('has_threshold'):
                    threshold_count += 1
                
                # Cointegration
                if comm_results.get('cointegration', {}).get('is_cointegrated'):
                    cointegrated_count += 1
                
                # Quality threshold
                if comm_results.get('meets_quality_threshold'):
                    quality_count += 1
        
        total_commodities = len(commodity_results)
        
        return {
            'total_commodities': total_commodities,
            'integration_distribution': integration_counts,
            'threshold_effects_pct': (threshold_count / total_commodities * 100 
                                    if total_commodities > 0 else 0),
            'cointegration_pct': (cointegrated_count / total_commodities * 100 
                                if total_commodities > 0 else 0),
            'quality_threshold_pct': (quality_count / total_commodities * 100 
                                    if total_commodities > 0 else 0),
            'high_integration_commodities': [
                comm for comm, results in commodity_results.items()
                if results.get('integration_level') == 'High'
            ]
        }
    
    async def analyze_commodities_parallel(self, panel_data: PanelData,
                                         commodities: List[str]) -> Dict[str, Any]:
        """Analyze multiple commodities in parallel (future enhancement)."""
        # TODO: Implement parallel processing using asyncio
        # For now, fall back to sequential processing
        results = {}
        
        for commodity in commodities:
            try:
                data = await self.prepare_data(panel_data, commodity)
                if not data.empty:
                    comm_results = await self.run_model(data, commodity=commodity)
                    results[commodity] = await self.extract_results(comm_results)
            except Exception as e:
                logger.error(f"Failed to analyze {commodity}: {str(e)}")
                results[commodity] = {'error': str(e)}
        
        return results