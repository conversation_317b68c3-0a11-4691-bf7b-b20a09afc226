"""Tier 1 Pooled Panel Model Adapter.

Adapts V1 pooled panel models for use in V2 architecture, ensuring
backward compatibility and numerical accuracy.
"""

import sys
from pathlib import Path
from typing import Any, Dict, Optional
import pandas as pd
import numpy as np
from datetime import datetime

from .v1_model_adapter import V1ModelAdapter
from ...core.domain.market.entities import PanelData
from ...core.domain.market.value_objects import Currency
from ...infrastructure.logging import Logger

logger = Logger(__name__)


class Tier1Adapter(V1ModelAdapter):
    """Adapter for V1 Tier 1 pooled panel models.
    
    This adapter integrates the V1 PooledPanelModel which implements:
    - Multi-way fixed effects (entity + time)
    - Clustered standard errors
    - Comprehensive diagnostics
    - The validated 35% conflict effect
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Tier 1 adapter.
        
        Parameters
        ----------
        config : dict, optional
            Configuration including:
            - entity_effects: Include entity fixed effects (default: True)
            - time_effects: Include time fixed effects (default: True)
            - cluster_entity: Cluster standard errors at entity level (default: True)
            - standard_errors: Type of standard errors (default: 'cluster_robust')
        """
        super().__init__(config)
        
        # Tier 1 specific defaults
        self.entity_effects = self.config.get('entity_effects', True)
        self.time_effects = self.config.get('time_effects', True)
        self.cluster_entity = self.config.get('cluster_entity', True)
        self.standard_errors = self.config.get('standard_errors', 'cluster_robust')
        
        # Ensure V1 code is available
        self._ensure_v1_available()
        
        logger.info(
            f"Initialized Tier1Adapter with entity_effects={self.entity_effects}, "
            f"time_effects={self.time_effects}"
        )
    
    def _ensure_v1_available(self) -> None:
        """Ensure V1 code is available for import."""
        v1_src = Path(__file__).parent.parent.parent.parent.parent / "src"
        if v1_src.exists() and str(v1_src) not in sys.path:
            sys.path.insert(0, str(v1_src))
    
    async def prepare_data(self, panel_data: PanelData) -> pd.DataFrame:
        """Convert V2 panel data to V1 format for pooled panel analysis.
        
        The V1 pooled panel model expects:
        - Multi-index: (entity, time)
        - Columns: dependent_var, regressors, conflict_intensity
        - Balanced panel structure
        """
        logger.info("Preparing data for Tier 1 pooled panel analysis")
        
        # Initialize lists for DataFrame construction
        data_records = []
        
        # Extract observations from panel data
        for obs in panel_data.observations:
            # Convert price to USD for consistency
            price_usd = obs.price.convert_to(Currency.USD).amount
            
            # Create entity identifier (market × commodity)
            entity_id = f"{obs.market_id}_{obs.commodity.code}"
            
            # Build record
            record = {
                'entity': entity_id,
                'time': obs.observed_date,
                'market': str(obs.market_id),
                'commodity': obs.commodity.code,
                'commodity_name': obs.commodity.name,
                'date': obs.observed_date,
                'usd_price': price_usd,
                'log_price': np.log(price_usd) if price_usd > 0 else np.nan,
                'conflict_intensity': obs.conflict_intensity if hasattr(obs, 'conflict_intensity') else 0,
                'lag_price': obs.lag_price if hasattr(obs, 'lag_price') else np.nan,
                'global_price_index': obs.global_price_index if hasattr(obs, 'global_price_index') else 100,
                'exchange_rate': obs.exchange_rate if hasattr(obs, 'exchange_rate') else 1,
                'seasonality': obs.observed_date.month  # Month as seasonality proxy
            }
            
            data_records.append(record)
        
        # Create DataFrame
        df = pd.DataFrame(data_records)
        
        # Set multi-index for panel structure
        df = df.set_index(['entity', 'time']).sort_index()
        
        # Validate panel structure
        n_entities = df.index.get_level_values('entity').nunique()
        n_periods = df.index.get_level_values('time').nunique()
        n_obs = len(df)
        
        logger.info(
            f"Prepared panel data: {n_entities} entities, {n_periods} periods, "
            f"{n_obs} observations (balance={n_obs / (n_entities * n_periods):.2%})"
        )
        
        # Log any missing values
        missing_counts = df.isnull().sum()
        if missing_counts.any():
            logger.warning(f"Missing values detected: {missing_counts[missing_counts > 0].to_dict()}")
        
        return df
    
    async def run_model(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Execute V1 pooled panel model.
        
        Parameters
        ----------
        data : pd.DataFrame
            Prepared panel data
        **kwargs
            Additional parameters:
            - dependent_var: Name of dependent variable (default: 'log_price')
            - regressors: List of regressor names
            - weights: Optional weights
            
        Returns
        -------
        dict
            Raw results from V1 model
        """
        try:
            from yemen_market.models.three_tier.tier1_pooled import PooledPanelModel
            
            # Configure model
            model_config = {
                'entity_effects': self.entity_effects,
                'time_effects': self.time_effects,
                'cluster_entity': self.cluster_entity,
                'standard_errors': self.standard_errors,
                'drop_multicollinear': True,
                'iterations': kwargs.get('iterations', 100)
            }
            
            # Initialize model
            model = PooledPanelModel(config=model_config)
            
            # Set dependent variable and regressors
            dependent_var = kwargs.get('dependent_var', 'log_price')
            
            # Default regressors based on V1 specification
            regressors = kwargs.get('regressors', [
                'conflict_intensity',
                'lag_price',
                'global_price_index',
                'exchange_rate'
            ])
            
            # Ensure required columns exist
            available_cols = data.columns.tolist()
            regressors = [col for col in regressors if col in available_cols]
            
            if dependent_var not in available_cols:
                raise ValueError(f"Dependent variable '{dependent_var}' not found in data")
            
            logger.info(
                f"Running pooled panel model with dependent_var='{dependent_var}', "
                f"regressors={regressors}"
            )
            
            # Fit model
            model.fit(data, dependent_var=dependent_var, regressors=regressors)
            
            # Get results
            results = model.get_results()
            
            # Extract key components for return
            return {
                'model': model,
                'results': results,
                'coefficients': results.params if hasattr(results, 'params') else {},
                'std_errors': results.bse if hasattr(results, 'bse') else {},
                'p_values': results.pvalues if hasattr(results, 'pvalues') else {},
                'diagnostics': results.diagnostics if hasattr(results, 'diagnostics') else {},
                'metadata': {
                    'n_observations': len(data),
                    'n_entities': data.index.get_level_values('entity').nunique(),
                    'n_periods': data.index.get_level_values('time').nunique(),
                    'dependent_var': dependent_var,
                    'regressors': regressors,
                    'estimation_time': datetime.utcnow().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error running Tier 1 model: {str(e)}")
            raise
    
    async def extract_results(self, raw_results: Any) -> Dict[str, Any]:
        """Extract and validate Tier 1 results.
        
        Ensures conflict effect matches V1 baseline of 35% (p < 0.001).
        """
        logger.info("Extracting Tier 1 results")
        
        # Extract coefficients
        coefficients = raw_results.get('coefficients', {})
        std_errors = raw_results.get('std_errors', {})
        p_values = raw_results.get('p_values', {})
        
        # Get conflict effect
        conflict_coef = coefficients.get('conflict_intensity', np.nan)
        conflict_se = std_errors.get('conflict_intensity', np.nan)
        conflict_p = p_values.get('conflict_intensity', np.nan)
        
        # Validate conflict effect
        is_valid, validation_msg = self.validate_conflict_effect(conflict_coef, conflict_p)
        
        if not is_valid:
            logger.warning(f"Conflict effect validation failed: {validation_msg}")
        else:
            logger.info(f"Conflict effect validated: {validation_msg}")
        
        # Extract model fit statistics
        model_results = raw_results.get('results')
        if hasattr(model_results, 'rsquared'):
            r_squared = model_results.rsquared
        elif hasattr(model_results, 'r_squared'):
            r_squared = model_results.r_squared
        else:
            r_squared = np.nan
        
        # Run diagnostics if available
        diagnostics = {}
        if 'model' in raw_results and 'results' in raw_results:
            try:
                data = raw_results.get('data')  # Should be passed along
                if data is not None:
                    diagnostics = await self.run_diagnostics(
                        raw_results['results'],
                        data
                    )
            except Exception as e:
                logger.warning(f"Failed to run diagnostics: {str(e)}")
        
        # Compile final results
        tier1_results = {
            'coefficients': coefficients,
            'standard_errors': std_errors,
            'p_values': p_values,
            'conflict_effect': {
                'coefficient': conflict_coef,
                'std_error': conflict_se,
                'p_value': conflict_p,
                'is_valid': is_valid,
                'validation_message': validation_msg,
                'percentage_effect': conflict_coef * 100 if not np.isnan(conflict_coef) else None
            },
            'model_fit': {
                'r_squared': r_squared,
                'n_observations': raw_results.get('metadata', {}).get('n_observations', 0),
                'n_entities': raw_results.get('metadata', {}).get('n_entities', 0),
                'n_periods': raw_results.get('metadata', {}).get('n_periods', 0)
            },
            'diagnostics': diagnostics,
            'metadata': raw_results.get('metadata', {}),
            'tier': 1,
            'model_type': 'pooled_panel',
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Check for critical diagnostic failures
        if diagnostics and diagnostics.get('summary', {}).get('has_critical_failures'):
            logger.warning("Critical diagnostic failures detected - corrections may be needed")
            tier1_results['needs_correction'] = True
            tier1_results['suggested_corrections'] = self._suggest_corrections(diagnostics)
        
        return tier1_results
    
    def _suggest_corrections(self, diagnostics: Dict[str, Any]) -> Dict[str, Any]:
        """Suggest corrections based on diagnostic failures."""
        corrections = {}
        test_results = diagnostics.get('test_results', {})
        
        # Check specific test failures
        failures = {
            'serial_correlation': False,
            'cross_sectional_dependence': False,
            'heteroskedasticity': False,
            'unit_roots': False
        }
        
        for test_name, result in test_results.items():
            if not result.get('passed', True):
                if 'wooldridge' in test_name.lower():
                    failures['serial_correlation'] = True
                elif 'pesaran' in test_name.lower() and 'cd' in test_name.lower():
                    failures['cross_sectional_dependence'] = True
                elif 'wald' in test_name.lower():
                    failures['heteroskedasticity'] = True
                elif 'ips' in test_name.lower() or 'im-pesaran' in test_name.lower():
                    failures['unit_roots'] = True
        
        # Apply correction logic
        return self.apply_corrections(self.config, failures)