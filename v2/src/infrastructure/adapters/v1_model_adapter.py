"""V1 Model Adapter Base Class for Three-Tier Analysis.

This module provides the base adapter interface for integrating V1 econometric
models into the V2 architecture, ensuring backward compatibility while maintaining
the validated 35% conflict effect findings.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Tuple
from datetime import datetime
import pandas as pd
import numpy as np

from ...core.domain.market.entities import PanelData
from ...infrastructure.logging import Logger

logger = Logger(__name__)


class V1ModelAdapter(ABC):
    """Base adapter for V1 econometric models.
    
    This adapter provides a consistent interface for:
    1. Data format conversion (V2 domain objects → V1 DataFrames)
    2. Model invocation with proper configuration
    3. Result extraction and validation
    4. Diagnostic integration
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize adapter with configuration.
        
        Parameters
        ----------
        config : dict, optional
            Model-specific configuration including:
            - tolerance: Numerical tolerance for validation (default: 0.001)
            - validate_conflict_effect: Whether to validate 35% effect (default: True)
            - diagnostic_config: Configuration for diagnostic tests
        """
        self.config = config or {}
        self.tolerance = self.config.get('tolerance', 0.001)
        self.validate_conflict_effect = self.config.get('validate_conflict_effect', True)
        self.diagnostic_config = self.config.get('diagnostic_config', {})
        
        # Expected conflict effect from V1 validation
        self.expected_conflict_effect = 0.35  # 35% increase
        self.conflict_effect_tolerance = 0.05  # ±5% tolerance
        
        logger.info(f"Initialized {self.__class__.__name__} with tolerance={self.tolerance}")
    
    @abstractmethod
    async def prepare_data(self, panel_data: PanelData) -> pd.DataFrame:
        """Convert V2 panel data to V1 format.
        
        Parameters
        ----------
        panel_data : PanelData
            V2 domain model panel data
            
        Returns
        -------
        pd.DataFrame
            Data in V1 expected format with required columns
        """
        pass
    
    @abstractmethod
    async def run_model(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Execute V1 model and return raw results.
        
        Parameters
        ----------
        data : pd.DataFrame
            Prepared data in V1 format
        **kwargs
            Additional model-specific parameters
            
        Returns
        -------
        dict
            Raw model results including coefficients, diagnostics, etc.
        """
        pass
    
    @abstractmethod
    async def extract_results(self, raw_results: Any) -> Dict[str, Any]:
        """Convert V1 results to V2 format with validation.
        
        Parameters
        ----------
        raw_results : Any
            Raw output from V1 model
            
        Returns
        -------
        dict
            Standardized results with validated metrics
        """
        pass
    
    async def run_diagnostics(self, model_results: Any, data: pd.DataFrame) -> Dict[str, Any]:
        """Run diagnostic tests on model results.
        
        Parameters
        ----------
        model_results : Any
            Model estimation results
        data : pd.DataFrame
            Original data used for estimation
            
        Returns
        -------
        dict
            Diagnostic test results
        """
        try:
            from yemen_market.models.three_tier.diagnostics import ThreeTierPanelDiagnostics
            
            # Determine tier from adapter class name
            tier = self._get_tier_number()
            
            diagnostics = ThreeTierPanelDiagnostics(
                tier=tier,
                config=self.diagnostic_config
            )
            
            diag_report = diagnostics.run_diagnostics(
                model_results,
                data,
                self.diagnostic_config
            )
            
            return diag_report.to_dict() if diag_report else {}
            
        except Exception as e:
            logger.warning(f"Failed to run diagnostics: {str(e)}")
            return {"error": str(e)}
    
    def validate_numerical_accuracy(self, v2_value: float, v1_value: float, 
                                  metric_name: str) -> bool:
        """Validate numerical accuracy between V1 and V2 results.
        
        Parameters
        ----------
        v2_value : float
            Value computed in V2
        v1_value : float
            Expected value from V1
        metric_name : str
            Name of metric for logging
            
        Returns
        -------
        bool
            True if values match within tolerance
        """
        if np.isnan(v2_value) or np.isnan(v1_value):
            logger.warning(f"{metric_name}: NaN values detected")
            return False
            
        relative_error = abs(v2_value - v1_value) / abs(v1_value) if v1_value != 0 else abs(v2_value)
        
        if relative_error > self.tolerance:
            logger.warning(
                f"{metric_name} mismatch: V2={v2_value:.6f}, V1={v1_value:.6f}, "
                f"relative_error={relative_error:.6f}"
            )
            return False
            
        return True
    
    def validate_conflict_effect(self, conflict_coefficient: float, 
                               p_value: float) -> Tuple[bool, str]:
        """Validate that conflict effect matches expected 35% finding.
        
        Parameters
        ----------
        conflict_coefficient : float
            Estimated conflict effect
        p_value : float
            Statistical significance
            
        Returns
        -------
        tuple
            (is_valid, message)
        """
        if not self.validate_conflict_effect:
            return True, "Conflict validation disabled"
            
        # Check significance
        if p_value >= 0.001:
            return False, f"Conflict effect not significant (p={p_value:.4f})"
            
        # Check magnitude
        lower_bound = self.expected_conflict_effect - self.conflict_effect_tolerance
        upper_bound = self.expected_conflict_effect + self.conflict_effect_tolerance
        
        if not (lower_bound <= conflict_coefficient <= upper_bound):
            return False, (
                f"Conflict effect {conflict_coefficient:.3f} outside expected range "
                f"[{lower_bound:.3f}, {upper_bound:.3f}]"
            )
            
        return True, f"Conflict effect validated: {conflict_coefficient:.1%} (p<0.001)"
    
    def _get_tier_number(self) -> int:
        """Extract tier number from adapter class name."""
        class_name = self.__class__.__name__.lower()
        if 'tier1' in class_name:
            return 1
        elif 'tier2' in class_name:
            return 2
        elif 'tier3' in class_name:
            return 3
        else:
            return 1  # Default to tier 1
    
    def apply_corrections(self, model_config: Dict[str, Any], 
                         diagnostic_failures: Dict[str, Any]) -> Dict[str, Any]:
        """Apply econometric corrections based on diagnostic failures.
        
        Parameters
        ----------
        model_config : dict
            Current model configuration
        diagnostic_failures : dict
            Failed diagnostic tests
            
        Returns
        -------
        dict
            Updated configuration with corrections
        """
        corrections = {}
        
        # Serial correlation → Newey-West HAC
        if diagnostic_failures.get('serial_correlation'):
            corrections['standard_errors'] = 'newey_west'
            corrections['lags'] = 'auto'
            
        # Cross-sectional dependence → Driscoll-Kraay
        if diagnostic_failures.get('cross_sectional_dependence'):
            corrections['standard_errors'] = 'driscoll_kraay'
            corrections['bandwidth'] = 'auto'
            
        # Both issues → Driscoll-Kraay (handles both)
        if (diagnostic_failures.get('serial_correlation') and 
            diagnostic_failures.get('cross_sectional_dependence')):
            corrections['standard_errors'] = 'driscoll_kraay'
            
        # Heteroskedasticity → Cluster-robust
        if diagnostic_failures.get('heteroskedasticity'):
            if 'standard_errors' not in corrections:  # Don't override stronger corrections
                corrections['standard_errors'] = 'cluster_robust'
                
        # Unit roots → First-differencing
        if diagnostic_failures.get('unit_roots'):
            corrections['transformation'] = 'first_difference'
            corrections['include_constant'] = False
            
        logger.info(f"Applying corrections: {corrections}")
        
        # Merge with existing config
        updated_config = model_config.copy()
        updated_config.update(corrections)
        
        return updated_config