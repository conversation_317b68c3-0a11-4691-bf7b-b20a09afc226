"""Tier 3 Validation Model Adapter.

Adapts V1 validation models (factor analysis, PCA, conflict validation) for V2.
"""

import sys
from pathlib import Path
from typing import Any, Dict, Optional, List
import pandas as pd
import numpy as np
from datetime import datetime

from .v1_model_adapter import V1ModelAdapter
from ...core.domain.market.entities import PanelData
from ...core.domain.market.value_objects import Currency
from ...infrastructure.logging import Logger

logger = Logger(__name__)


class Tier3Adapter(V1ModelAdapter):
    """Adapter for V1 Tier 3 validation models.
    
    This adapter integrates:
    - Static and dynamic factor models
    - PCA-based integration analysis
    - Conflict validation models
    - Cross-tier validation
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Tier 3 adapter.
        
        Parameters
        ----------
        config : dict, optional
            Configuration including:
            - n_factors: Number of factors for factor model (default: 2)
            - n_components: Number of PCA components (default: 3)
            - conflict_validation: Run conflict validation (default: True)
            - rolling_window: Window for rolling analysis (default: 12)
        """
        super().__init__(config)
        
        # Tier 3 specific configuration
        self.n_factors = self.config.get('n_factors', 2)
        self.n_components = self.config.get('n_components', 3)
        self.conflict_validation = self.config.get('conflict_validation', True)
        self.rolling_window = self.config.get('rolling_window', 12)
        
        # Ensure V1 code is available
        self._ensure_v1_available()
        
        logger.info(
            f"Initialized Tier3Adapter with n_factors={self.n_factors}, "
            f"conflict_validation={self.conflict_validation}"
        )
    
    def _ensure_v1_available(self) -> None:
        """Ensure V1 code is available for import."""
        v1_src = Path(__file__).parent.parent.parent.parent.parent / "src"
        if v1_src.exists() and str(v1_src) not in sys.path:
            sys.path.insert(0, str(v1_src))
    
    async def prepare_data(self, panel_data: PanelData,
                          tier1_results: Optional[Dict[str, Any]] = None,
                          tier2_results: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Convert V2 panel data to V1 format for validation analysis.
        
        Parameters
        ----------
        panel_data : PanelData
            V2 domain model panel data
        tier1_results : dict, optional
            Results from Tier 1 for cross-validation
        tier2_results : dict, optional
            Results from Tier 2 for cross-validation
            
        Returns
        -------
        pd.DataFrame
            Data formatted for validation analysis
        """
        logger.info("Preparing data for Tier 3 validation analysis")
        
        # Initialize records
        data_records = []
        
        # Extract observations
        for obs in panel_data.observations:
            # Convert price to USD
            price_usd = obs.price.convert_to(Currency.USD).amount
            
            # Create governorate identifier (for V1 compatibility)
            governorate = obs.market_id.split('_')[0] if '_' in str(obs.market_id) else str(obs.market_id)
            
            record = {
                'governorate': governorate,
                'market': str(obs.market_id),
                'commodity': obs.commodity.code,
                'date': obs.observed_date,
                'usd_price': price_usd,
                'log_price': np.log(price_usd) if price_usd > 0 else np.nan,
                'conflict_intensity': obs.conflict_intensity if hasattr(obs, 'conflict_intensity') else 0
            }
            
            # Add residuals from Tier 1 if available
            if tier1_results and 'residuals' in tier1_results:
                entity_id = f"{obs.market_id}_{obs.commodity.code}"
                if entity_id in tier1_results['residuals']:
                    record['tier1_residual'] = tier1_results['residuals'][entity_id].get(
                        obs.observed_date, np.nan
                    )
            
            data_records.append(record)
        
        # Create DataFrame
        df = pd.DataFrame(data_records)
        
        # Log data shape
        logger.info(
            f"Prepared validation data: {len(df)} observations, "
            f"{df['governorate'].nunique()} governorates, "
            f"{df['commodity'].nunique()} commodities"
        )
        
        return df
    
    async def run_model(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Execute V1 validation models.
        
        Parameters
        ----------
        data : pd.DataFrame
            Prepared panel data
        **kwargs
            Additional parameters:
            - model_type: Type of validation ('factor', 'pca', 'conflict')
            - conflict_data: Conflict event data for validation
            
        Returns
        -------
        dict
            Raw results from validation models
        """
        try:
            # Import V1 validation models
            from yemen_market.models.three_tier.tier3_validation import (
                StaticFactorModel, PCAMarketIntegration, ConflictIntegrationValidator
            )
            
            model_type = kwargs.get('model_type', 'all')
            results = {}
            
            # 1. Static Factor Analysis
            if model_type in ['all', 'factor']:
                logger.info("Running static factor analysis")
                
                factor_config = {
                    'n_factors': self.n_factors,
                    'rotation': 'varimax',
                    'method': 'ml'  # Maximum likelihood
                }
                
                factor_model = StaticFactorModel(factor_config)
                factor_model.fit(data)
                
                results['static_factors'] = {
                    'model': factor_model,
                    'results': factor_model.get_results(),
                    'loadings': factor_model.loadings if hasattr(factor_model, 'loadings') else None,
                    'scores': factor_model.scores if hasattr(factor_model, 'scores') else None
                }
            
            # 2. PCA Integration Analysis
            if model_type in ['all', 'pca']:
                logger.info("Running PCA integration analysis")
                
                pca_config = {
                    'n_components': self.n_components,
                    'rolling_window': self.rolling_window,
                    'min_variance_explained': 0.8
                }
                
                pca_analyzer = PCAMarketIntegration(pca_config)
                integration_report = pca_analyzer.generate_integration_report(data)
                
                results['pca_integration'] = {
                    'analyzer': pca_analyzer,
                    'report': integration_report,
                    'rolling_scores': pca_analyzer.rolling_pca_analysis(data) 
                                     if hasattr(pca_analyzer, 'rolling_pca_analysis') else None
                }
            
            # 3. Conflict Validation
            if model_type in ['all', 'conflict'] and self.conflict_validation:
                conflict_data = kwargs.get('conflict_data')
                
                if conflict_data is not None:
                    logger.info("Running conflict validation analysis")
                    
                    conflict_config = {
                        'event_window': 30,  # Days before/after event
                        'min_events': 5,
                        'integration_metric': 'pca_score'
                    }
                    
                    conflict_validator = ConflictIntegrationValidator(conflict_config)
                    
                    # Get integration scores from PCA if available
                    integration_scores = None
                    if 'pca_integration' in results:
                        integration_scores = results['pca_integration']['rolling_scores']
                    
                    conflict_validator.fit(
                        data,
                        conflict_data=conflict_data,
                        integration_scores=integration_scores
                    )
                    
                    results['conflict_validation'] = {
                        'validator': conflict_validator,
                        'results': conflict_validator.get_results(),
                        'event_study': conflict_validator.event_study 
                                      if hasattr(conflict_validator, 'event_study') else None
                    }
                else:
                    logger.warning("Conflict validation requested but no conflict data provided")
            
            return {
                'validation_results': results,
                'metadata': {
                    'models_run': list(results.keys()),
                    'n_observations': len(data),
                    'estimation_time': datetime.utcnow().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Error running Tier 3 models: {str(e)}")
            raise
    
    async def extract_results(self, raw_results: Any) -> Dict[str, Any]:
        """Extract and format Tier 3 validation results."""
        logger.info("Extracting Tier 3 validation results")
        
        validation_results = raw_results.get('validation_results', {})
        extracted = {
            'tier': 3,
            'model_type': 'validation',
            'timestamp': datetime.utcnow().isoformat(),
            'metadata': raw_results.get('metadata', {})
        }
        
        # Extract factor analysis results
        if 'static_factors' in validation_results:
            factor_results = validation_results['static_factors']['results']
            extracted['static_factors'] = self._extract_factor_results(factor_results)
        
        # Extract PCA integration results
        if 'pca_integration' in validation_results:
            pca_report = validation_results['pca_integration']['report']
            extracted['pca_integration'] = self._extract_pca_results(pca_report)
        
        # Extract conflict validation results
        if 'conflict_validation' in validation_results:
            conflict_results = validation_results['conflict_validation']['results']
            extracted['conflict_validation'] = self._extract_conflict_results(conflict_results)
        
        # Generate overall validation summary
        extracted['validation_summary'] = self._generate_validation_summary(extracted)
        
        return extracted
    
    def _extract_factor_results(self, factor_results: Any) -> Dict[str, Any]:
        """Extract key results from factor analysis."""
        if not factor_results:
            return {}
        
        extracted = {
            'n_factors': self.n_factors,
            'model_type': 'static_factor'
        }
        
        # Extract from ResultsContainer if that's the format
        if hasattr(factor_results, 'tier_specific'):
            tier_data = factor_results.tier_specific
            
            extracted.update({
                'variance_explained': tier_data.get('variance_explained', {}),
                'factor_interpretation': tier_data.get('factor_interpretation', {}),
                'kmo_statistic': tier_data.get('kmo_statistic'),
                'bartlett_p_value': tier_data.get('bartlett_p_value')
            })
            
            # Extract loadings if available
            if 'loadings' in tier_data:
                # Get top loadings for each factor
                loadings_df = tier_data['loadings']
                if isinstance(loadings_df, pd.DataFrame):
                    top_loadings = {}
                    for factor in loadings_df.columns:
                        top_loadings[factor] = loadings_df[factor].abs().nlargest(5).to_dict()
                    extracted['top_loadings'] = top_loadings
        
        return extracted
    
    def _extract_pca_results(self, pca_report: Any) -> Dict[str, Any]:
        """Extract key results from PCA integration analysis."""
        if not pca_report:
            return {}
        
        extracted = {
            'n_components': self.n_components,
            'model_type': 'pca_integration'
        }
        
        # Extract from report structure
        if hasattr(pca_report, 'tier_specific'):
            tier_data = pca_report.tier_specific
            
            # Overall integration metrics
            overall = tier_data.get('overall_integration', {})
            extracted.update({
                'integration_level': overall.get('integration_level'),
                'pc1_variance_explained': overall.get('pc1_variance_explained'),
                'mean_correlation': overall.get('mean_correlation'),
                'integration_trend': overall.get('integration_trend')
            })
            
            # Commodity integration
            commodity_integration = tier_data.get('commodity_integration', {})
            if commodity_integration:
                extracted['high_integration_commodities'] = [
                    comm for comm, metrics in commodity_integration.items()
                    if metrics.get('integration_score', 0) > 0.7
                ]
            
            # Temporal patterns
            temporal = tier_data.get('temporal_patterns', {})
            if temporal:
                extracted['integration_volatility'] = temporal.get('volatility')
                extracted['trend_direction'] = temporal.get('trend')
        
        return extracted
    
    def _extract_conflict_results(self, conflict_results: Any) -> Dict[str, Any]:
        """Extract key results from conflict validation."""
        if not conflict_results:
            return {}
        
        extracted = {
            'model_type': 'conflict_validation'
        }
        
        # Extract from ResultsContainer if that's the format
        if hasattr(conflict_results, 'tier_specific'):
            tier_data = conflict_results.tier_specific
            
            # Conflict impact metrics
            extracted.update({
                'conflict_reduces_integration': tier_data.get('conflict_impact', {}).get(
                    'reduces_integration', False
                ),
                'avg_impact_magnitude': tier_data.get('conflict_impact', {}).get(
                    'avg_magnitude'
                ),
                'impact_duration_days': tier_data.get('conflict_impact', {}).get(
                    'duration_days'
                )
            })
            
            # Event study results
            event_study = tier_data.get('event_study', {})
            if event_study:
                extracted['pct_events_reducing_integration'] = event_study.get(
                    'pct_events_reducing_integration'
                )
                extracted['significant_events'] = event_study.get('n_significant_events', 0)
        
        return extracted
    
    def _generate_validation_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of validation findings."""
        summary = {
            'models_completed': [],
            'key_findings': [],
            'integration_assessment': 'Unknown'
        }
        
        # Check which models completed
        if 'static_factors' in results:
            summary['models_completed'].append('factor_analysis')
            
            # Add factor findings
            variance_explained = results['static_factors'].get('variance_explained', {})
            if variance_explained:
                total_var = sum(variance_explained.values())
                summary['key_findings'].append(
                    f"Factor analysis: {len(variance_explained)} factors explain "
                    f"{total_var:.1%} of variance"
                )
        
        if 'pca_integration' in results:
            summary['models_completed'].append('pca_integration')
            
            # Add PCA findings
            integration_level = results['pca_integration'].get('integration_level')
            if integration_level:
                summary['integration_assessment'] = integration_level
                summary['key_findings'].append(
                    f"PCA analysis: Market integration level is {integration_level}"
                )
            
            # Add commodity findings
            high_integration = results['pca_integration'].get('high_integration_commodities', [])
            if high_integration:
                summary['key_findings'].append(
                    f"High integration commodities: {', '.join(high_integration[:3])}"
                )
        
        if 'conflict_validation' in results:
            summary['models_completed'].append('conflict_validation')
            
            # Add conflict findings
            if results['conflict_validation'].get('conflict_reduces_integration'):
                pct_events = results['conflict_validation'].get('pct_events_reducing_integration', 0)
                summary['key_findings'].append(
                    f"Conflict validation: {pct_events:.1f}% of events reduce integration"
                )
        
        return summary
    
    async def cross_validate_tiers(self, tier1_results: Dict[str, Any],
                                  tier2_results: Dict[str, Any],
                                  tier3_results: Dict[str, Any]) -> Dict[str, Any]:
        """Perform cross-tier validation (future enhancement)."""
        validation = {
            'consistency_checks': {},
            'integration_alignment': {},
            'recommendations': []
        }
        
        # TODO: Implement cross-tier validation logic
        # - Compare integration metrics across tiers
        # - Check coefficient consistency
        # - Validate factor loadings against commodity results
        
        return validation