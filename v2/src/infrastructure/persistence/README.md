# V2 PostgreSQL Repository Implementation

This directory contains the complete PostgreSQL repository implementation for Yemen Market Integration V2, providing a robust, high-performance data access layer that matches V1's 88.4% data coverage.

## 🏗️ Architecture Overview

The repository layer follows Domain-Driven Design (DDD) principles with a clean architecture pattern:

```
├── repositories/postgres/          # PostgreSQL implementations
│   ├── market_repository.py       # Market aggregate repository
│   ├── price_repository.py        # Price observation repository
│   ├── commodity_repository.py    # Commodity reference data
│   └── conflict_repository.py     # Conflict event repository
├── migrations/                    # Database schema migrations
├── unit_of_work.py               # Transaction management
├── repository_factory.py         # Factory and configuration
├── bulk_operations.py            # Performance optimizations
├── error_handling.py             # Resilience and error handling
└── v1_to_v2_data_migration.py   # V1 data migration tools
```

## 🚀 Key Features

### ✅ Complete Repository Implementation
- **MarketRepository**: CRUD operations, spatial queries, control zone tracking
- **PriceRepository**: Bulk insert, time series data, missing data handling
- **ConflictRepository**: Event storage, spatial-temporal aggregation
- **CommodityRepository**: Reference data management

### ✅ Advanced Data Operations
- **Bulk Operations**: High-performance batch inserts using PostgreSQL COPY
- **Spatial Queries**: PostGIS integration for location-based queries
- **Temporal Queries**: Optimized time-series data access
- **Aggregation**: Built-in statistical and conflict aggregation functions

### ✅ Transaction Management
- **Unit of Work Pattern**: ACID transaction guarantees
- **Optimistic Locking**: Prevents concurrent modification conflicts
- **Connection Pooling**: Efficient database connection management

### ✅ Performance Optimization
- **Query Optimization**: Automatic query performance monitoring
- **Index Suggestions**: Database performance analysis tools
- **Connection Pooling**: Configurable pool sizes for scalability
- **Batch Processing**: Efficient bulk operations with configurable batch sizes

### ✅ Resilience & Error Handling
- **Circuit Breaker Pattern**: Prevents cascade failures
- **Retry Strategies**: Configurable exponential backoff with jitter
- **Error Classification**: Automatic error categorization and severity assessment
- **Health Monitoring**: Real-time database health checks

### ✅ Data Migration
- **V1 to V2 Migration**: Complete data mapping from existing V1 structures
- **Validation**: Comprehensive migration validation and rollback capabilities
- **Data Quality**: Automatic data quality checks and reporting

## 🔧 Configuration

### Database Configuration

```python
from v2.src.infrastructure.persistence import DatabaseConfig, initialize_persistence_layer

# From environment variables
config = DatabaseConfig.from_environment()

# Or explicit configuration
config = DatabaseConfig(
    host="localhost",
    port=5432,
    database="yemen_market_v2",
    username="postgres",
    password="password",
    pool_min_size=10,
    pool_max_size=20
)

# Initialize persistence layer
factory = await initialize_persistence_layer(config)
```

### Environment Variables

```bash
# Database connection
DATABASE_URL=postgresql://user:pass@host:port/database
# Or individual components
DB_HOST=localhost
DB_PORT=5432
DB_NAME=yemen_market_v2
DB_USER=postgres
DB_PASSWORD=password

# Connection pool settings
DB_POOL_MIN_SIZE=10
DB_POOL_MAX_SIZE=20
DB_COMMAND_TIMEOUT=60
```

## 📊 Data Coverage & Compatibility

### V1 Data Compatibility
- **Markets**: 100% coverage of V1 market entities
- **Prices**: Full price observation history preservation
- **Commodities**: Complete commodity reference data mapping
- **Conflicts**: Enhanced ACLED event storage with spatial indexing

### Data Quality Metrics
- **Spatial Coverage**: All 333 governorates and districts
- **Temporal Coverage**: 2019-2024 analysis period
- **Data Completeness**: 88.4% coverage matching V1 benchmarks
- **Quality Validation**: Automatic data quality checks and reporting

## 🔍 Usage Examples

### Basic Repository Operations

```python
from v2.src.infrastructure.persistence import get_repository_factory

factory = get_repository_factory()

# Using Unit of Work for transactions
async with factory.create_unit_of_work() as uow:
    # Find markets by governorate
    markets = await uow.markets.find_by_governorate("Sana'a")
    
    # Get price observations
    prices = await uow.prices.find_by_market_and_commodity(
        market_id=markets[0].market_id,
        commodity=wheat_commodity,
        start_date=datetime(2024, 1, 1),
        end_date=datetime(2024, 1, 31)
    )
    
    # Find conflict events within radius
    conflicts = await uow.conflict_events.find_by_location_radius(
        latitude=15.3694,
        longitude=44.1910,
        radius_km=50.0
    )
    
    await uow.commit()
```

### Bulk Operations for Performance

```python
from v2.src.infrastructure.persistence.bulk_operations import BulkOperationManager

async with factory.create_unit_of_work() as uow:
    bulk_manager = BulkOperationManager(uow._connection)
    
    # Bulk insert price observations
    stats = await bulk_manager.bulk_insert_price_observations(
        observations=price_list,
        on_conflict='update'
    )
    
    print(f"Processed {stats.processed_records} records in {stats.duration_seconds:.2f}s")
```

### Resilient Operations

```python
from v2.src.infrastructure.persistence.error_handling import resilient_operation

@resilient_operation(max_retries=3)
async def critical_data_operation():
    async with factory.create_unit_of_work() as uow:
        # Critical operation with automatic retry
        result = await uow.markets.find_by_id(market_id)
        return result
```

## 🗃️ Database Schema

### Core Tables
- **markets**: Market entities with spatial indexing
- **commodities**: Reference data for commodity types
- **price_observations**: Time-series price data
- **conflict_events**: ACLED conflict events with spatial data
- **domain_events**: Event sourcing for domain events
- **analysis_results**: Cached analysis results

### Indexes & Performance
- **Spatial Indexes**: PostGIS GIST indexes for location queries
- **Temporal Indexes**: B-tree indexes on date columns
- **Composite Indexes**: Multi-column indexes for common query patterns
- **Partial Indexes**: Optimized indexes for filtered queries

## 🧪 Testing

### Integration Tests
```bash
# Run repository integration tests
pytest v2/tests/integration/test_postgres_repositories.py -v

# Test with real database
pytest v2/tests/integration/ --database-url="postgresql://..."
```

### Performance Tests
```bash
# Benchmark bulk operations
python v2/examples/repository_usage_example.py
```

## 🔄 Migration from V1

### Data Migration Process
```python
from v2.src.infrastructure.persistence.migrations.v1_to_v2_data_migration import run_migration

# Run complete V1 to V2 migration
results = await run_migration(
    v1_data_path="/path/to/v1/data/processed",
    v2_connection_string="postgresql://..."
)

print(f"Migrated: {results}")
```

### Migration Validation
```python
# Validate migration results
validation = await migrator.validate_migration()
assert all(validation.values()), f"Migration validation failed: {validation}"
```

## 📈 Monitoring & Health Checks

### Health Monitoring
```python
# Database health check
health = await factory.health_check()
print(f"Database status: {health}")

# Error monitoring
from v2.src.infrastructure.persistence.error_handling import get_error_collector
error_collector = get_error_collector()
summary = error_collector.get_error_summary(hours=24)
health_score = error_collector.get_health_score()
```

### Performance Monitoring
```python
# Database statistics
stats = await factory.get_database_statistics()
print(f"Table sizes: {stats['tables']}")
print(f"Connection pool: {stats['pool_info']}")
```

## 🔒 Security & Best Practices

### Connection Security
- SSL/TLS encryption support
- Connection string parameter validation
- Credential management through environment variables

### SQL Injection Prevention
- Parameterized queries only
- Input validation at domain boundaries
- Type-safe query construction

### Performance Best Practices
- Connection pooling with configurable limits
- Prepared statement caching
- Bulk operations for large datasets
- Query performance monitoring

## 🚧 Development Notes

### Adding New Repositories
1. Define repository interface in `core/domain/*/repositories.py`
2. Implement PostgreSQL version in `repositories/postgres/`
3. Add to Unit of Work in `unit_of_work.py`
4. Update factory in `repository_factory.py`
5. Add integration tests

### Schema Evolution
1. Create new migration file in `migrations/`
2. Update entity mappings in repositories
3. Test migration with existing data
4. Update integration tests

### Performance Optimization
1. Use `QueryOptimizer` to identify slow queries
2. Add appropriate database indexes
3. Implement bulk operations for large datasets
4. Monitor with error collector and health checks

---

This repository implementation provides a solid foundation for the Yemen Market Integration V2 system, offering high performance, reliability, and maintainability while ensuring full compatibility with existing V1 data structures.