"""Repository factory and configuration for PostgreSQL persistence layer."""

import os
import logging
from typing import Dict, Optional, Any
from dataclasses import dataclass
from urllib.parse import urlparse

import asyncpg

from .unit_of_work import PostgresUnitOfWork
from .repositories.postgres import (
    PostgresMarketRepository,
    PostgresPriceRepository,
    PostgresCommodityRepository,
    PostgresConflictEventRepository
)

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """Database configuration."""
    host: str = "localhost"
    port: int = 5432
    database: str = "yemen_market_v2"
    username: str = "postgres"
    password: str = ""
    ssl_mode: str = "prefer"
    pool_min_size: int = 10
    pool_max_size: int = 20
    command_timeout: int = 60
    connection_timeout: int = 10
    
    @classmethod
    def from_url(cls, database_url: str) -> 'DatabaseConfig':
        """Create config from database URL."""
        parsed = urlparse(database_url)
        
        return cls(
            host=parsed.hostname or "localhost",
            port=parsed.port or 5432,
            database=parsed.path.lstrip('/') or "yemen_market_v2",
            username=parsed.username or "postgres",
            password=parsed.password or "",
            ssl_mode="require" if parsed.scheme.endswith('s') else "prefer"
        )
    
    @classmethod
    def from_environment(cls) -> 'DatabaseConfig':
        """Create config from environment variables."""
        database_url = os.getenv('DATABASE_URL')
        if database_url:
            return cls.from_url(database_url)
        
        return cls(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', '5432')),
            database=os.getenv('DB_NAME', 'yemen_market_v2'),
            username=os.getenv('DB_USER', 'postgres'),
            password=os.getenv('DB_PASSWORD', ''),
            ssl_mode=os.getenv('DB_SSL_MODE', 'prefer'),
            pool_min_size=int(os.getenv('DB_POOL_MIN_SIZE', '10')),
            pool_max_size=int(os.getenv('DB_POOL_MAX_SIZE', '20')),
            command_timeout=int(os.getenv('DB_COMMAND_TIMEOUT', '60')),
            connection_timeout=int(os.getenv('DB_CONNECTION_TIMEOUT', '10'))
        )
    
    def to_connection_string(self) -> str:
        """Convert to asyncpg connection string."""
        return (
            f"postgresql://{self.username}:{self.password}@"
            f"{self.host}:{self.port}/{self.database}"
            f"?sslmode={self.ssl_mode}"
        )


class RepositoryFactory:
    """Factory for creating repository instances."""
    
    def __init__(self, config: Optional[DatabaseConfig] = None):
        """Initialize repository factory.
        
        Args:
            config: Database configuration. If None, loads from environment.
        """
        self.config = config or DatabaseConfig.from_environment()
        self._pool: Optional[asyncpg.Pool] = None
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def initialize(self) -> None:
        """Initialize the connection pool."""
        try:
            self._pool = await asyncpg.create_pool(
                self.config.to_connection_string(),
                min_size=self.config.pool_min_size,
                max_size=self.config.pool_max_size,
                command_timeout=self.config.command_timeout
            )
            
            # Test connection
            async with self._pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            
            self.logger.info(
                f"Database connection pool initialized: "
                f"{self.config.host}:{self.config.port}/{self.config.database}"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database pool: {e}")
            raise
    
    async def close(self) -> None:
        """Close the connection pool."""
        if self._pool:
            await self._pool.close()
            self._pool = None
            self.logger.info("Database connection pool closed")
    
    def create_unit_of_work(self) -> PostgresUnitOfWork:
        """Create a new Unit of Work instance."""
        return PostgresUnitOfWork(self.config.to_connection_string())
    
    async def create_market_repository(self) -> PostgresMarketRepository:
        """Create market repository with pooled connection."""
        if not self._pool:
            await self.initialize()
        
        connection = await self._pool.acquire()
        return PostgresMarketRepository(connection)
    
    async def create_price_repository(self) -> PostgresPriceRepository:
        """Create price repository with pooled connection."""
        if not self._pool:
            await self.initialize()
        
        connection = await self._pool.acquire()
        return PostgresPriceRepository(connection)
    
    async def create_commodity_repository(self) -> PostgresCommodityRepository:
        """Create commodity repository with pooled connection."""
        if not self._pool:
            await self.initialize()
        
        connection = await self._pool.acquire()
        return PostgresCommodityRepository(connection)
    
    async def create_conflict_repository(self) -> PostgresConflictEventRepository:
        """Create conflict event repository with pooled connection."""
        if not self._pool:
            await self.initialize()
        
        connection = await self._pool.acquire()
        return PostgresConflictEventRepository(connection)
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on database connection."""
        health_status = {
            'database': 'unknown',
            'connection_pool': 'unknown',
            'query_test': 'unknown',
            'migration_status': 'unknown'
        }
        
        try:
            if not self._pool:
                await self.initialize()
            
            health_status['connection_pool'] = 'healthy'
            
            async with self._pool.acquire() as conn:
                # Test basic query
                result = await conn.fetchval("SELECT 1")
                if result == 1:
                    health_status['query_test'] = 'healthy'
                
                # Check if core tables exist
                tables_exist = await conn.fetchval("""
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_name IN ('markets', 'commodities', 'price_observations', 'conflict_events')
                """)
                
                if tables_exist >= 4:
                    health_status['migration_status'] = 'complete'
                    health_status['database'] = 'healthy'
                elif tables_exist > 0:
                    health_status['migration_status'] = 'partial'
                    health_status['database'] = 'degraded'
                else:
                    health_status['migration_status'] = 'pending'
                    health_status['database'] = 'needs_migration'
        
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            health_status['database'] = 'unhealthy'
            health_status['error'] = str(e)
        
        return health_status
    
    async def run_migrations(self, migration_path: str = None) -> bool:
        """Run database migrations."""
        try:
            if not self._pool:
                await self.initialize()
            
            migration_file = migration_path or os.path.join(
                os.path.dirname(__file__),
                'migrations',
                '001_initial_schema.sql'
            )
            
            if not os.path.exists(migration_file):
                self.logger.error(f"Migration file not found: {migration_file}")
                return False
            
            with open(migration_file, 'r') as f:
                migration_sql = f.read()
            
            async with self._pool.acquire() as conn:
                await conn.execute(migration_sql)
            
            self.logger.info("Database migrations completed successfully")
            return True
        
        except Exception as e:
            self.logger.error(f"Migration failed: {e}")
            return False
    
    async def get_database_statistics(self) -> Dict[str, Any]:
        """Get database statistics for monitoring."""
        stats = {}
        
        try:
            if not self._pool:
                await self.initialize()
            
            async with self._pool.acquire() as conn:
                # Table sizes
                tables = await conn.fetch("""
                    SELECT 
                        schemaname,
                        tablename,
                        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                    FROM pg_tables 
                    WHERE schemaname = 'public'
                    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
                """)
                
                stats['tables'] = [dict(row) for row in tables]
                
                # Row counts
                for table in ['markets', 'commodities', 'price_observations', 'conflict_events']:
                    try:
                        count = await conn.fetchval(f"SELECT COUNT(*) FROM {table}")
                        stats[f'{table}_count'] = count
                    except:
                        stats[f'{table}_count'] = 0
                
                # Database size
                db_size = await conn.fetchrow("""
                    SELECT 
                        pg_size_pretty(pg_database_size(current_database())) as size,
                        pg_database_size(current_database()) as size_bytes
                """)
                
                stats['database_size'] = dict(db_size)
                
                # Connection info
                stats['pool_info'] = {
                    'min_size': self.config.pool_min_size,
                    'max_size': self.config.pool_max_size,
                    'current_size': self._pool.get_size() if self._pool else 0,
                    'idle_connections': self._pool.get_idle_size() if self._pool else 0
                }
        
        except Exception as e:
            self.logger.error(f"Failed to get database statistics: {e}")
            stats['error'] = str(e)
        
        return stats


# Global factory instance
_factory_instance: Optional[RepositoryFactory] = None


def get_repository_factory(config: Optional[DatabaseConfig] = None) -> RepositoryFactory:
    """Get the global repository factory instance."""
    global _factory_instance
    
    if _factory_instance is None:
        _factory_instance = RepositoryFactory(config)
    
    return _factory_instance


async def initialize_persistence_layer(config: Optional[DatabaseConfig] = None) -> RepositoryFactory:
    """Initialize the persistence layer with database connection."""
    factory = get_repository_factory(config)
    await factory.initialize()
    return factory


async def cleanup_persistence_layer():
    """Clean up the persistence layer resources."""
    global _factory_instance
    
    if _factory_instance:
        await _factory_instance.close()
        _factory_instance = None