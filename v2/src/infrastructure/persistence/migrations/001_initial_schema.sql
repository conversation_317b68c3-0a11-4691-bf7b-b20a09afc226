-- Initial database schema for Yemen Market Integration v2

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Markets table
CREATE TABLE IF NOT EXISTS markets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    market_id VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    market_type VARCHAR(50) NOT NULL,
    governorate VARCHAR(100) NOT NULL,
    district VARCHAR(100) NOT NULL,
    active_since TIMESTAMP NOT NULL,
    active_until TIMESTAMP,
    version INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_coordinates CHECK (
        latitude >= -90 AND latitude <= 90 AND
        longitude >= -180 AND longitude <= 180
    ),
    CONSTRAINT valid_market_type CHECK (
        market_type IN ('wholesale', 'retail', 'border', 'port', 'rural', 'urban')
    )
);

-- Indexes for markets
CREATE INDEX idx_markets_governorate ON markets(governorate);
CREATE INDEX idx_markets_district ON markets(district);
CREATE INDEX idx_markets_active ON markets(active_since, active_until);
CREATE INDEX idx_markets_location ON markets USING GIST (
    point(longitude, latitude)
);

-- Commodities table
CREATE TABLE IF NOT EXISTS commodities (
    code VARCHAR(50) PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    category VARCHAR(100) NOT NULL,
    standard_unit VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Price observations table
CREATE TABLE IF NOT EXISTS price_observations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    market_id VARCHAR(50) NOT NULL,
    commodity_code VARCHAR(50) NOT NULL,
    price_amount DECIMAL(10, 2) NOT NULL,
    price_currency VARCHAR(3) NOT NULL,
    price_unit VARCHAR(50) NOT NULL,
    observed_date TIMESTAMP NOT NULL,
    source VARCHAR(100) NOT NULL,
    quality VARCHAR(20) DEFAULT 'standard',
    observations_count INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT positive_price CHECK (price_amount >= 0),
    CONSTRAINT valid_quality CHECK (quality IN ('high', 'standard', 'low')),
    CONSTRAINT positive_count CHECK (observations_count > 0),
    CONSTRAINT unique_observation UNIQUE (
        market_id, commodity_code, observed_date, source
    )
);

-- Indexes for price observations
CREATE INDEX idx_prices_market_commodity ON price_observations(market_id, commodity_code);
CREATE INDEX idx_prices_date ON price_observations(observed_date);
CREATE INDEX idx_prices_commodity_date ON price_observations(commodity_code, observed_date);

-- Foreign key constraints (deferred to allow flexibility during data loading)
ALTER TABLE price_observations
    ADD CONSTRAINT fk_price_market 
    FOREIGN KEY (market_id) 
    REFERENCES markets(market_id) 
    DEFERRABLE INITIALLY DEFERRED;

ALTER TABLE price_observations
    ADD CONSTRAINT fk_price_commodity 
    FOREIGN KEY (commodity_code) 
    REFERENCES commodities(code) 
    DEFERRABLE INITIALLY DEFERRED;

-- Domain events table for event sourcing
CREATE TABLE IF NOT EXISTS domain_events (
    event_id UUID PRIMARY KEY,
    aggregate_id UUID NOT NULL,
    event_name VARCHAR(200) NOT NULL,
    event_data JSONB NOT NULL,
    occurred_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for domain events
CREATE INDEX idx_events_aggregate ON domain_events(aggregate_id);
CREATE INDEX idx_events_occurred ON domain_events(occurred_at);
CREATE INDEX idx_events_name ON domain_events(event_name);

-- Analysis results table (for caching)
CREATE TABLE IF NOT EXISTS analysis_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_type VARCHAR(100) NOT NULL,
    parameters JSONB NOT NULL,
    results JSONB NOT NULL,
    status VARCHAR(50) NOT NULL,
    started_at TIMESTAMP NOT NULL,
    completed_at TIMESTAMP,
    error_message TEXT,
    created_by VARCHAR(200),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    CONSTRAINT valid_status CHECK (
        status IN ('pending', 'running', 'completed', 'failed', 'cancelled')
    )
);

-- Indexes for analysis results
CREATE INDEX idx_analysis_type ON analysis_results(analysis_type);
CREATE INDEX idx_analysis_status ON analysis_results(status);
CREATE INDEX idx_analysis_created ON analysis_results(created_at);
CREATE INDEX idx_analysis_expires ON analysis_results(expires_at);

-- Conflict events table
CREATE TABLE IF NOT EXISTS conflict_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id VARCHAR(50) UNIQUE NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    sub_event_type VARCHAR(100),
    event_date TIMESTAMP NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    governorate VARCHAR(100) NOT NULL,
    district VARCHAR(100),
    fatalities INTEGER DEFAULT 0,
    notes TEXT,
    source VARCHAR(100) NOT NULL,
    source_scale VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT valid_coordinates_conflict CHECK (
        latitude >= -90 AND latitude <= 90 AND
        longitude >= -180 AND longitude <= 180
    ),
    CONSTRAINT non_negative_fatalities CHECK (fatalities >= 0)
);

-- Indexes for conflict events
CREATE INDEX idx_conflict_governorate ON conflict_events(governorate);
CREATE INDEX idx_conflict_date ON conflict_events(event_date);
CREATE INDEX idx_conflict_type ON conflict_events(event_type);
CREATE INDEX idx_conflict_location ON conflict_events USING GIST (
    point(longitude, latitude)
);
CREATE INDEX idx_conflict_fatalities ON conflict_events(fatalities);

-- Update timestamp trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_markets_updated_at 
    BEFORE UPDATE ON markets 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();