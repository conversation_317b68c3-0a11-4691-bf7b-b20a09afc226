"""Repository implementation for policy analysis results."""

import json
from datetime import datetime
from typing import List, Optional, Dict, Any
from pathlib import Path
import pandas as pd

from ...core.domain.shared.exceptions import RepositoryError
from ...infrastructure.logging import Logger

logger = Logger(__name__)


class PolicyResultsRepository:
    """Repository for storing and retrieving policy analysis results."""
    
    def __init__(self, storage_path: str = "data/policy_results"):
        """Initialize repository with storage path."""
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories for different result types
        self.welfare_path = self.storage_path / "welfare_impacts"
        self.welfare_path.mkdir(exist_ok=True)
        
        self.early_warning_path = self.storage_path / "early_warnings"
        self.early_warning_path.mkdir(exist_ok=True)
        
        self.monitoring_path = self.storage_path / "monitoring"
        self.monitoring_path.mkdir(exist_ok=True)
        
        self.comparisons_path = self.storage_path / "comparisons"
        self.comparisons_path.mkdir(exist_ok=True)
    
    async def save_welfare_analysis(self, 
                                  analysis_id: str,
                                  results: Dict[str, Any]) -> None:
        """Save welfare impact analysis results.
        
        Args:
            analysis_id: Unique identifier for the analysis
            results: Analysis results dictionary
        """
        try:
            # Add metadata
            results['_metadata'] = {
                'analysis_id': analysis_id,
                'saved_at': datetime.utcnow().isoformat(),
                'version': '1.0'
            }
            
            # Save main results as JSON
            file_path = self.welfare_path / f"{analysis_id}.json"
            with open(file_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            # Save distributional effects as CSV for easy analysis
            if 'welfare_impact' in results and 'distributional_effects' in results['welfare_impact']:
                dist_effects = results['welfare_impact']['distributional_effects']
                df = pd.DataFrame([dist_effects])
                df.to_csv(self.welfare_path / f"{analysis_id}_distribution.csv", index=False)
            
            logger.info(f"Saved welfare analysis {analysis_id}")
            
        except Exception as e:
            logger.error(f"Failed to save welfare analysis: {e}")
            raise RepositoryError(f"Could not save welfare analysis: {e}")
    
    async def save_early_warning(self,
                               warning_id: str,
                               results: Dict[str, Any]) -> None:
        """Save early warning analysis results.
        
        Args:
            warning_id: Unique identifier for the warning
            results: Warning results dictionary
        """
        try:
            # Add metadata
            results['_metadata'] = {
                'warning_id': warning_id,
                'saved_at': datetime.utcnow().isoformat(),
                'version': '1.0'
            }
            
            # Save main results
            file_path = self.early_warning_path / f"{warning_id}.json"
            with open(file_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            # Save alerts summary as CSV
            if 'alerts' in results:
                alerts_data = []
                for alert in results['alerts']:
                    alert_row = {
                        'alert_level': alert['alert_level'],
                        'alert_type': alert['alert_type'],
                        'probability': alert['probability'],
                        'time_horizon': alert['time_horizon'],
                        'affected_markets': ', '.join(alert['affected_markets'][:3]),
                        'affected_commodities': ', '.join(alert['affected_commodities'][:3])
                    }
                    alerts_data.append(alert_row)
                
                if alerts_data:
                    df = pd.DataFrame(alerts_data)
                    df.to_csv(self.early_warning_path / f"{warning_id}_alerts.csv", index=False)
            
            logger.info(f"Saved early warning {warning_id}")
            
        except Exception as e:
            logger.error(f"Failed to save early warning: {e}")
            raise RepositoryError(f"Could not save early warning: {e}")
    
    async def save_policy_comparison(self,
                                   comparison_id: str,
                                   results: Dict[str, Any]) -> None:
        """Save policy comparison results.
        
        Args:
            comparison_id: Unique identifier for the comparison
            results: Comparison results dictionary
        """
        try:
            # Add metadata
            results['_metadata'] = {
                'comparison_id': comparison_id,
                'saved_at': datetime.utcnow().isoformat(),
                'version': '1.0'
            }
            
            # Save main results
            file_path = self.comparisons_path / f"{comparison_id}.json"
            with open(file_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            # Save comparison table as CSV
            if 'interventions' in results:
                comparison_data = []
                for item in results['interventions']:
                    row = {
                        'intervention_type': item['intervention']['type'],
                        'target_markets': ', '.join(item['intervention']['target_markets'][:3]),
                        'magnitude': item['intervention']['magnitude'],
                        'duration_months': item['intervention']['duration_months'],
                        'net_welfare_change': item['impact']['net_welfare_change'],
                        'government_cost': item['impact']['government_cost'],
                        'cost_effectiveness': item['cost_effectiveness'],
                        'beneficiaries': item['impact']['beneficiary_count']
                    }
                    comparison_data.append(row)
                
                if comparison_data:
                    df = pd.DataFrame(comparison_data)
                    df.to_csv(self.comparisons_path / f"{comparison_id}_summary.csv", index=False)
            
            logger.info(f"Saved policy comparison {comparison_id}")
            
        except Exception as e:
            logger.error(f"Failed to save policy comparison: {e}")
            raise RepositoryError(f"Could not save policy comparison: {e}")
    
    async def save_monitoring_results(self,
                                    monitoring_id: str,
                                    results: Dict[str, Any]) -> None:
        """Save policy monitoring results.
        
        Args:
            monitoring_id: Unique identifier for the monitoring session
            results: Monitoring results dictionary
        """
        try:
            # Add metadata
            results['_metadata'] = {
                'monitoring_id': monitoring_id,
                'saved_at': datetime.utcnow().isoformat(),
                'version': '1.0'
            }
            
            # Save main results
            file_path = self.monitoring_path / f"{monitoring_id}.json"
            with open(file_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"Saved monitoring results {monitoring_id}")
            
        except Exception as e:
            logger.error(f"Failed to save monitoring results: {e}")
            raise RepositoryError(f"Could not save monitoring results: {e}")
    
    async def get_welfare_analysis(self, analysis_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve welfare analysis results by ID."""
        try:
            file_path = self.welfare_path / f"{analysis_id}.json"
            if file_path.exists():
                with open(file_path, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logger.error(f"Failed to load welfare analysis: {e}")
            return None
    
    async def get_early_warning(self, warning_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve early warning results by ID."""
        try:
            file_path = self.early_warning_path / f"{warning_id}.json"
            if file_path.exists():
                with open(file_path, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logger.error(f"Failed to load early warning: {e}")
            return None
    
    async def get_policy_comparison(self, comparison_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve policy comparison results by ID."""
        try:
            file_path = self.comparisons_path / f"{comparison_id}.json"
            if file_path.exists():
                with open(file_path, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logger.error(f"Failed to load policy comparison: {e}")
            return None
    
    async def get_recent_early_warnings(self, 
                                      days: int = 7,
                                      alert_level_min: int = 2) -> List[Dict[str, Any]]:
        """Get recent early warnings above specified alert level.
        
        Args:
            days: Number of days to look back
            alert_level_min: Minimum alert level (1=LOW, 2=MEDIUM, 3=HIGH, 4=CRITICAL)
            
        Returns:
            List of recent warnings
        """
        recent_warnings = []
        cutoff_date = datetime.utcnow() - pd.Timedelta(days=days)
        
        try:
            for file_path in self.early_warning_path.glob("*.json"):
                if '_alerts.csv' not in str(file_path):  # Skip CSV files
                    with open(file_path, 'r') as f:
                        warning = json.load(f)
                    
                    # Check date
                    saved_at = datetime.fromisoformat(warning['_metadata']['saved_at'])
                    if saved_at >= cutoff_date:
                        # Check alert levels
                        if 'alerts' in warning:
                            max_level = max(alert['alert_level'] for alert in warning['alerts'])
                            if max_level >= alert_level_min:
                                recent_warnings.append(warning)
            
            # Sort by date, most recent first
            recent_warnings.sort(
                key=lambda x: x['_metadata']['saved_at'],
                reverse=True
            )
            
            return recent_warnings
            
        except Exception as e:
            logger.error(f"Failed to get recent warnings: {e}")
            return []
    
    async def get_intervention_history(self,
                                     intervention_type: Optional[str] = None,
                                     days: int = 30) -> List[Dict[str, Any]]:
        """Get history of welfare interventions.
        
        Args:
            intervention_type: Optional filter by intervention type
            days: Number of days to look back
            
        Returns:
            List of intervention analyses
        """
        interventions = []
        cutoff_date = datetime.utcnow() - pd.Timedelta(days=days)
        
        try:
            # Check welfare analyses
            for file_path in self.welfare_path.glob("*.json"):
                if '_distribution.csv' not in str(file_path):
                    with open(file_path, 'r') as f:
                        analysis = json.load(f)
                    
                    saved_at = datetime.fromisoformat(analysis['_metadata']['saved_at'])
                    if saved_at >= cutoff_date:
                        # Check intervention type if specified
                        if intervention_type is None or \
                           (analysis.get('welfare_impact') and 
                            analysis['welfare_impact'].get('intervention_type') == intervention_type):
                            interventions.append(analysis)
            
            # Check comparisons
            for file_path in self.comparisons_path.glob("*.json"):
                if '_summary.csv' not in str(file_path):
                    with open(file_path, 'r') as f:
                        comparison = json.load(f)
                    
                    saved_at = datetime.fromisoformat(comparison['_metadata']['saved_at'])
                    if saved_at >= cutoff_date:
                        interventions.append(comparison)
            
            # Sort by date
            interventions.sort(
                key=lambda x: x['_metadata']['saved_at'],
                reverse=True
            )
            
            return interventions
            
        except Exception as e:
            logger.error(f"Failed to get intervention history: {e}")
            return []
    
    async def get_performance_metrics(self,
                                    start_date: datetime,
                                    end_date: datetime) -> Dict[str, Any]:
        """Calculate aggregate performance metrics for a time period.
        
        Args:
            start_date: Start of period
            end_date: End of period
            
        Returns:
            Aggregate metrics dictionary
        """
        metrics = {
            'welfare_analyses': 0,
            'early_warnings': 0,
            'policy_comparisons': 0,
            'total_beneficiaries': 0,
            'total_government_cost': 0,
            'average_cost_effectiveness': 0,
            'critical_alerts': 0,
            'high_alerts': 0
        }
        
        try:
            # Count welfare analyses
            for file_path in self.welfare_path.glob("*.json"):
                if '_distribution.csv' not in str(file_path):
                    with open(file_path, 'r') as f:
                        analysis = json.load(f)
                    
                    saved_at = datetime.fromisoformat(analysis['_metadata']['saved_at'])
                    if start_date <= saved_at <= end_date:
                        metrics['welfare_analyses'] += 1
                        
                        if 'welfare_impact' in analysis:
                            impact = analysis['welfare_impact']
                            metrics['total_beneficiaries'] += impact.get('beneficiary_count', 0)
                            metrics['total_government_cost'] += impact.get('government_cost', 0)
            
            # Count early warnings
            for file_path in self.early_warning_path.glob("*.json"):
                if '_alerts.csv' not in str(file_path):
                    with open(file_path, 'r') as f:
                        warning = json.load(f)
                    
                    saved_at = datetime.fromisoformat(warning['_metadata']['saved_at'])
                    if start_date <= saved_at <= end_date:
                        metrics['early_warnings'] += 1
                        
                        if 'alerts' in warning:
                            for alert in warning['alerts']:
                                if alert['alert_level'] == 4:
                                    metrics['critical_alerts'] += 1
                                elif alert['alert_level'] == 3:
                                    metrics['high_alerts'] += 1
            
            # Count comparisons
            for file_path in self.comparisons_path.glob("*.json"):
                if '_summary.csv' not in str(file_path):
                    with open(file_path, 'r') as f:
                        comparison = json.load(f)
                    
                    saved_at = datetime.fromisoformat(comparison['_metadata']['saved_at'])
                    if start_date <= saved_at <= end_date:
                        metrics['policy_comparisons'] += 1
            
            # Calculate average cost effectiveness
            if metrics['total_beneficiaries'] > 0 and metrics['total_government_cost'] > 0:
                metrics['average_cost_effectiveness'] = (
                    metrics['total_government_cost'] / metrics['total_beneficiaries']
                )
            
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to calculate performance metrics: {e}")
            return metrics
    
    async def export_results_summary(self,
                                   output_path: str,
                                   start_date: Optional[datetime] = None,
                                   end_date: Optional[datetime] = None) -> None:
        """Export summary of all results to CSV.
        
        Args:
            output_path: Path for output CSV file
            start_date: Optional start date filter
            end_date: Optional end date filter
        """
        summary_data = []
        
        try:
            # Collect all analyses
            for analysis_type, path in [
                ('welfare', self.welfare_path),
                ('early_warning', self.early_warning_path),
                ('comparison', self.comparisons_path),
                ('monitoring', self.monitoring_path)
            ]:
                for file_path in path.glob("*.json"):
                    if '.csv' not in str(file_path):
                        with open(file_path, 'r') as f:
                            data = json.load(f)
                        
                        saved_at = datetime.fromisoformat(data['_metadata']['saved_at'])
                        
                        # Apply date filter
                        if start_date and saved_at < start_date:
                            continue
                        if end_date and saved_at > end_date:
                            continue
                        
                        # Extract summary info
                        summary = {
                            'analysis_type': analysis_type,
                            'analysis_id': data['_metadata'].get('analysis_id') or 
                                         data['_metadata'].get('warning_id') or
                                         data['_metadata'].get('comparison_id') or
                                         data['_metadata'].get('monitoring_id'),
                            'saved_at': saved_at.isoformat(),
                            'data_quality_score': data.get('metadata', {}).get('data_quality_score'),
                            'markets_covered': data.get('metadata', {}).get('coverage', {}).get('markets'),
                            'commodities_covered': data.get('metadata', {}).get('coverage', {}).get('commodities')
                        }
                        
                        # Add type-specific fields
                        if analysis_type == 'welfare' and 'welfare_impact' in data:
                            summary['net_welfare_change'] = data['welfare_impact'].get('net_welfare_change')
                            summary['beneficiaries'] = data['welfare_impact'].get('beneficiary_count')
                        elif analysis_type == 'early_warning' and 'alerts' in data:
                            summary['alert_count'] = len(data['alerts'])
                            summary['max_alert_level'] = max(a['alert_level'] for a in data['alerts']) if data['alerts'] else 0
                        
                        summary_data.append(summary)
            
            # Save to CSV
            if summary_data:
                df = pd.DataFrame(summary_data)
                df.to_csv(output_path, index=False)
                logger.info(f"Exported {len(summary_data)} results to {output_path}")
            else:
                logger.warning("No results found to export")
                
        except Exception as e:
            logger.error(f"Failed to export results summary: {e}")
            raise RepositoryError(f"Could not export results: {e}")