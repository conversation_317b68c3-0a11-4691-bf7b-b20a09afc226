"""Metrics collection and export."""

from typing import Dict, Any, Optional
import time
from prometheus_client import Counter, Histogram, Gauge, Info
from prometheus_client import CollectorRegistry, generate_latest

from ..logging import Logger

logger = Logger(__name__)

# Create registry
REGISTRY = CollectorRegistry()

# Define metrics
request_count = Counter(
    'yemen_market_http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status'],
    registry=REGISTRY
)

request_duration = Histogram(
    'yemen_market_http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint'],
    registry=REGISTRY
)

active_analyses = Gauge(
    'yemen_market_active_analyses',
    'Number of active analyses',
    registry=REGISTRY
)

analysis_duration = Histogram(
    'yemen_market_analysis_duration_seconds',
    'Analysis duration by type',
    ['analysis_type', 'tier'],
    registry=REGISTRY
)

model_estimation_duration = Histogram(
    'yemen_market_model_estimation_seconds',
    'Model estimation duration',
    ['model_type', 'estimator'],
    registry=REGISTRY
)

cache_hits = Counter(
    'yemen_market_cache_hits_total',
    'Cache hit count',
    ['cache_type', 'operation'],
    registry=REGISTRY
)

cache_misses = Counter(
    'yemen_market_cache_misses_total',
    'Cache miss count',
    ['cache_type', 'operation'],
    registry=REGISTRY
)

database_queries = Counter(
    'yemen_market_database_queries_total',
    'Database query count',
    ['query_type', 'table'],
    registry=REGISTRY
)

database_query_duration = Histogram(
    'yemen_market_database_query_duration_seconds',
    'Database query duration',
    ['query_type', 'table'],
    registry=REGISTRY
)

external_api_calls = Counter(
    'yemen_market_external_api_calls_total',
    'External API call count',
    ['api', 'endpoint', 'status'],
    registry=REGISTRY
)

external_api_duration = Histogram(
    'yemen_market_external_api_duration_seconds',
    'External API call duration',
    ['api', 'endpoint'],
    registry=REGISTRY
)

app_info = Info(
    'yemen_market_app',
    'Application information',
    registry=REGISTRY
)

# Set app info
app_info.info({
    'version': '2.0.0',
    'name': 'yemen_market_integration',
    'environment': 'production'
})


class MetricsCollector:
    """Collector for application metrics."""
    
    def __init__(self):
        """Initialize metrics collector."""
        self.registry = REGISTRY
    
    def record_request(self, method: str, endpoint: str, 
                      status: int, duration: float) -> None:
        """Record HTTP request metrics."""
        request_count.labels(
            method=method,
            endpoint=endpoint,
            status=str(status)
        ).inc()
        
        request_duration.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
    
    def record_analysis_start(self) -> None:
        """Record analysis start."""
        active_analyses.inc()
    
    def record_analysis_end(self, analysis_type: str, 
                          tier: str, duration: float) -> None:
        """Record analysis completion."""
        active_analyses.dec()
        analysis_duration.labels(
            analysis_type=analysis_type,
            tier=tier
        ).observe(duration)
    
    def record_model_estimation(self, model_type: str, 
                              estimator: str, duration: float) -> None:
        """Record model estimation metrics."""
        model_estimation_duration.labels(
            model_type=model_type,
            estimator=estimator
        ).observe(duration)
    
    def record_cache_hit(self, cache_type: str, operation: str) -> None:
        """Record cache hit."""
        cache_hits.labels(
            cache_type=cache_type,
            operation=operation
        ).inc()
    
    def record_cache_miss(self, cache_type: str, operation: str) -> None:
        """Record cache miss."""
        cache_misses.labels(
            cache_type=cache_type,
            operation=operation
        ).inc()
    
    def record_database_query(self, query_type: str, 
                            table: str, duration: float) -> None:
        """Record database query metrics."""
        database_queries.labels(
            query_type=query_type,
            table=table
        ).inc()
        
        database_query_duration.labels(
            query_type=query_type,
            table=table
        ).observe(duration)
    
    def record_external_api_call(self, api: str, endpoint: str,
                               status: int, duration: float) -> None:
        """Record external API call metrics."""
        external_api_calls.labels(
            api=api,
            endpoint=endpoint,
            status=str(status)
        ).inc()
        
        external_api_duration.labels(
            api=api,
            endpoint=endpoint
        ).observe(duration)
    
    def get_metrics(self) -> bytes:
        """Get metrics in Prometheus format."""
        return generate_latest(self.registry)


# Global collector instance
metrics_collector = MetricsCollector()


# Context managers for timing
class TimedMetric:
    """Context manager for timing operations."""
    
    def __init__(self, record_func, *args, **kwargs):
        """Initialize timer."""
        self.record_func = record_func
        self.args = args
        self.kwargs = kwargs
        self.start_time = None
    
    def __enter__(self):
        """Start timing."""
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Stop timing and record."""
        duration = time.time() - self.start_time
        self.record_func(*self.args, duration=duration, **self.kwargs)


def timed_model_estimation(model_type: str, estimator: str):
    """Create timer for model estimation."""
    return TimedMetric(
        metrics_collector.record_model_estimation,
        model_type=model_type,
        estimator=estimator
    )


def timed_database_query(query_type: str, table: str):
    """Create timer for database query."""
    return TimedMetric(
        metrics_collector.record_database_query,
        query_type=query_type,
        table=table
    )


def timed_external_api(api: str, endpoint: str, status: int):
    """Create timer for external API call."""
    return TimedMetric(
        metrics_collector.record_external_api_call,
        api=api,
        endpoint=endpoint,
        status=status
    )