"""Enhanced structured logging with correlation IDs and distributed tracing integration."""

import json
import logging
import sys
import uuid
from contextvars import ContextV<PERSON>
from datetime import datetime
from typing import Any, Dict, Optional, Union
from pathlib import Path

import structlog
from structlog.processors import CallsiteParameter, CallsiteParameterAdder
from opentelemetry import trace

# Context variables for correlation
correlation_id_var: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)
request_id_var: ContextVar[Optional[str]] = ContextVar('request_id', default=None)
user_id_var: ContextVar[Optional[str]] = ContextVar('user_id', default=None)


def generate_correlation_id() -> str:
    """Generate a new correlation ID."""
    return str(uuid.uuid4())


def set_correlation_id(correlation_id: Optional[str] = None) -> str:
    """Set correlation ID in context."""
    if correlation_id is None:
        correlation_id = generate_correlation_id()
    correlation_id_var.set(correlation_id)
    return correlation_id


def get_correlation_id() -> Optional[str]:
    """Get current correlation ID."""
    return correlation_id_var.get()


def set_request_id(request_id: str) -> None:
    """Set request ID in context."""
    request_id_var.set(request_id)


def get_request_id() -> Optional[str]:
    """Get current request ID."""
    return request_id_var.get()


def set_user_id(user_id: str) -> None:
    """Set user ID in context."""
    user_id_var.set(user_id)


def get_user_id() -> Optional[str]:
    """Get current user ID."""
    return user_id_var.get()


class CorrelationProcessor:
    """Processor to add correlation IDs to log entries."""
    
    def __call__(self, logger, method_name, event_dict):
        """Add correlation IDs to event dict."""
        event_dict['correlation_id'] = get_correlation_id()
        event_dict['request_id'] = get_request_id()
        event_dict['user_id'] = get_user_id()
        
        # Add trace context if available
        span = trace.get_current_span()
        if span and span.is_recording():
            span_context = span.get_span_context()
            event_dict['trace_id'] = format(span_context.trace_id, '032x')
            event_dict['span_id'] = format(span_context.span_id, '016x')
        
        return event_dict


class EnvironmentProcessor:
    """Processor to add environment information."""
    
    def __init__(self, environment: str = "production", 
                 service_name: str = "yemen-market-integration",
                 service_version: str = "2.0.0"):
        self.environment = environment
        self.service_name = service_name
        self.service_version = service_version
    
    def __call__(self, logger, method_name, event_dict):
        """Add environment info to event dict."""
        event_dict['environment'] = self.environment
        event_dict['service_name'] = self.service_name
        event_dict['service_version'] = self.service_version
        return event_dict


class SecurityProcessor:
    """Processor to sanitize sensitive data."""
    
    SENSITIVE_FIELDS = {
        'password', 'token', 'api_key', 'secret', 'authorization',
        'credit_card', 'ssn', 'bank_account'
    }
    
    def __call__(self, logger, method_name, event_dict):
        """Sanitize sensitive fields."""
        self._sanitize_dict(event_dict)
        return event_dict
    
    def _sanitize_dict(self, data: Dict[str, Any]) -> None:
        """Recursively sanitize dictionary."""
        for key, value in list(data.items()):
            if any(sensitive in key.lower() for sensitive in self.SENSITIVE_FIELDS):
                data[key] = "***REDACTED***"
            elif isinstance(value, dict):
                self._sanitize_dict(value)
            elif isinstance(value, list):
                for item in value:
                    if isinstance(item, dict):
                        self._sanitize_dict(item)


class ErrorDetailsProcessor:
    """Processor to enhance error logging."""
    
    def __call__(self, logger, method_name, event_dict):
        """Add detailed error information."""
        if method_name == "exception" or event_dict.get("exc_info"):
            import traceback
            
            if event_dict.get("exc_info"):
                exc_type, exc_value, exc_tb = event_dict["exc_info"]
                
                # Add structured error details
                event_dict["error"] = {
                    "type": exc_type.__name__ if exc_type else None,
                    "message": str(exc_value) if exc_value else None,
                    "module": exc_type.__module__ if exc_type else None,
                    "traceback": traceback.format_tb(exc_tb) if exc_tb else None
                }
                
                # Add first occurrence of error in our code
                if exc_tb:
                    for frame in traceback.extract_tb(exc_tb):
                        if "yemen_market" in frame.filename:
                            event_dict["error"]["first_occurrence"] = {
                                "file": frame.filename,
                                "line": frame.lineno,
                                "function": frame.name,
                                "code": frame.line
                            }
                            break
        
        return event_dict


class MetricsProcessor:
    """Processor to add metrics context."""
    
    def __call__(self, logger, method_name, event_dict):
        """Add metrics information."""
        # Add timing information if available
        if "duration_ms" in event_dict:
            event_dict["metrics"] = event_dict.get("metrics", {})
            event_dict["metrics"]["duration_ms"] = event_dict["duration_ms"]
        
        # Add memory usage if tracking
        if "memory_usage_mb" in event_dict:
            event_dict["metrics"] = event_dict.get("metrics", {})
            event_dict["metrics"]["memory_usage_mb"] = event_dict["memory_usage_mb"]
        
        return event_dict


class LogAggregator:
    """Aggregator for log search and analysis."""
    
    def __init__(self, log_dir: Path = Path("logs")):
        self.log_dir = log_dir
        self.log_dir.mkdir(parents=True, exist_ok=True)
    
    def search_logs(self, 
                   correlation_id: Optional[str] = None,
                   request_id: Optional[str] = None,
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None,
                   level: Optional[str] = None,
                   text_search: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search logs with various criteria."""
        results = []
        
        for log_file in self.log_dir.glob("*.jsonl"):
            with open(log_file, 'r') as f:
                for line in f:
                    try:
                        log_entry = json.loads(line)
                        
                        # Apply filters
                        if correlation_id and log_entry.get('correlation_id') != correlation_id:
                            continue
                        
                        if request_id and log_entry.get('request_id') != request_id:
                            continue
                        
                        if level and log_entry.get('level') != level:
                            continue
                        
                        if start_time or end_time:
                            timestamp = datetime.fromisoformat(log_entry.get('timestamp', ''))
                            if start_time and timestamp < start_time:
                                continue
                            if end_time and timestamp > end_time:
                                continue
                        
                        if text_search and text_search not in json.dumps(log_entry):
                            continue
                        
                        results.append(log_entry)
                    
                    except (json.JSONDecodeError, KeyError):
                        continue
        
        return results
    
    def get_request_trace(self, request_id: str) -> List[Dict[str, Any]]:
        """Get all logs for a specific request."""
        return self.search_logs(request_id=request_id)
    
    def get_correlation_trace(self, correlation_id: str) -> List[Dict[str, Any]]:
        """Get all logs for a correlation ID."""
        return self.search_logs(correlation_id=correlation_id)


def configure_structured_logging(
    environment: str = "production",
    service_name: str = "yemen-market-integration",
    service_version: str = "2.0.0",
    log_level: str = "INFO",
    log_file: Optional[Path] = None
) -> None:
    """Configure structured logging with all processors."""
    
    # Create processors
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        CallsiteParameterAdder(
            parameters=[
                CallsiteParameter.FILENAME,
                CallsiteParameter.LINENO,
                CallsiteParameter.FUNC_NAME
            ]
        ),
        CorrelationProcessor(),
        EnvironmentProcessor(environment, service_name, service_version),
        SecurityProcessor(),
        ErrorDetailsProcessor(),
        MetricsProcessor(),
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ]
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    handlers = [logging.StreamHandler(sys.stdout)]
    
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        handlers.append(file_handler)
    
    logging.basicConfig(
        format="%(message)s",
        handlers=handlers,
        level=getattr(logging, log_level.upper()),
    )


# Enhanced logger class
class StructuredLogger:
    """Enhanced structured logger with correlation support."""
    
    def __init__(self, name: str):
        """Initialize structured logger."""
        self.logger = structlog.get_logger(name)
    
    def bind(self, **kwargs) -> 'StructuredLogger':
        """Bind context to logger."""
        self.logger = self.logger.bind(**kwargs)
        return self
    
    def unbind(self, *keys) -> 'StructuredLogger':
        """Unbind context from logger."""
        self.logger = self.logger.unbind(*keys)
        return self
    
    def with_correlation(self, correlation_id: Optional[str] = None) -> 'StructuredLogger':
        """Create logger with correlation ID."""
        if correlation_id is None:
            correlation_id = get_correlation_id() or generate_correlation_id()
        set_correlation_id(correlation_id)
        return self.bind(correlation_id=correlation_id)
    
    def debug(self, message: str, **kwargs) -> None:
        """Log debug message."""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs) -> None:
        """Log info message."""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs) -> None:
        """Log warning message."""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs) -> None:
        """Log error message."""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """Log critical message."""
        self.logger.critical(message, **kwargs)
    
    def exception(self, message: str, **kwargs) -> None:
        """Log exception with traceback."""
        self.logger.exception(message, **kwargs)
    
    def log_analysis_event(self, event_type: str, analysis_id: str,
                         commodity: str, **kwargs) -> None:
        """Log analysis-specific event."""
        self.info(
            f"Analysis event: {event_type}",
            event_type=event_type,
            analysis_id=analysis_id,
            commodity=commodity,
            **kwargs
        )
    
    def log_data_quality_issue(self, issue_type: str, dataset: str,
                             severity: str, **kwargs) -> None:
        """Log data quality issue."""
        self.warning(
            f"Data quality issue: {issue_type}",
            issue_type=issue_type,
            dataset=dataset,
            severity=severity,
            **kwargs
        )
    
    def log_model_event(self, model_type: str, event: str,
                       performance_metrics: Optional[Dict[str, float]] = None,
                       **kwargs) -> None:
        """Log model-related event."""
        self.info(
            f"Model event: {event}",
            model_type=model_type,
            event=event,
            performance_metrics=performance_metrics,
            **kwargs
        )