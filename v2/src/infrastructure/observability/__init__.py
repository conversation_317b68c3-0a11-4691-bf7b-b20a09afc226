"""Observability infrastructure for monitoring, metrics, and tracing."""

from .metrics import (
    metrics_collector,
    timed_model_estimation,
    timed_database_query,
    timed_external_api
)
from .tracing import (
    tracing_manager,
    traced,
    get_current_span,
    add_span_attribute,
    add_span_event,
    record_exception
)

__all__ = [
    # Metrics
    'metrics_collector',
    'timed_model_estimation',
    'timed_database_query',
    'timed_external_api',
    # Tracing
    'tracing_manager',
    'traced',
    'get_current_span',
    'add_span_attribute',
    'add_span_event',
    'record_exception'
]