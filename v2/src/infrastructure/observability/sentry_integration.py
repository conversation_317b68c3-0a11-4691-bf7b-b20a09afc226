"""Sentry integration for error tracking and performance monitoring."""

import os
from typing import Optional, Dict, Any, Callable
from functools import wraps

import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
from sentry_sdk.integrations.redis import RedisIntegration
from sentry_sdk.integrations.logging import LoggingIntegration
from sentry_sdk.integrations.excepthook import ExcepthookIntegration
from sentry_sdk import set_tag, set_context, set_user, capture_exception
from sentry_sdk import capture_message, add_breadcrumb

from ..logging import Logger
from .structured_logging import get_correlation_id, get_request_id, get_user_id

logger = Logger(__name__)


class SentryManager:
    """Manager for Sentry error tracking and performance monitoring."""
    
    def __init__(self, 
                 dsn: Optional[str] = None,
                 environment: str = "production",
                 release: str = "yemen-market-integration@2.0.0",
                 traces_sample_rate: float = 0.1,
                 profiles_sample_rate: float = 0.1):
        """Initialize Sentry manager."""
        self.dsn = dsn or os.environ.get("SENTRY_DSN")
        self.environment = environment
        self.release = release
        self.traces_sample_rate = traces_sample_rate
        self.profiles_sample_rate = profiles_sample_rate
        
        if self.dsn:
            self._initialize_sentry()
        else:
            logger.warning("Sentry DSN not provided, error tracking disabled")
    
    def _initialize_sentry(self) -> None:
        """Initialize Sentry SDK."""
        sentry_sdk.init(
            dsn=self.dsn,
            environment=self.environment,
            release=self.release,
            traces_sample_rate=self.traces_sample_rate,
            profiles_sample_rate=self.profiles_sample_rate,
            integrations=[
                FastApiIntegration(transaction_style="endpoint"),
                SqlalchemyIntegration(),
                RedisIntegration(),
                LoggingIntegration(
                    level=logging.INFO,
                    event_level=logging.ERROR
                ),
                ExcepthookIntegration(always_run=True)
            ],
            before_send=self._before_send,
            before_send_transaction=self._before_send_transaction,
            attach_stacktrace=True,
            send_default_pii=False,  # Don't send personally identifiable information
            max_breadcrumbs=100,
            debug=False,
            # Custom options for Yemen analysis
            _experiments={
                "profiles_sample_rate": self.profiles_sample_rate,
                "custom_sampling_context": True
            }
        )
        
        logger.info(f"Sentry initialized for environment: {self.environment}")
    
    def _before_send(self, event: Dict[str, Any], hint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process event before sending to Sentry."""
        # Add correlation IDs
        if correlation_id := get_correlation_id():
            event.setdefault('tags', {})['correlation_id'] = correlation_id
        
        if request_id := get_request_id():
            event.setdefault('tags', {})['request_id'] = request_id
        
        # Add user context
        if user_id := get_user_id():
            event['user'] = {'id': user_id}
        
        # Filter out sensitive data
        self._filter_sensitive_data(event)
        
        # Add custom fingerprinting for better grouping
        self._add_custom_fingerprint(event)
        
        return event
    
    def _before_send_transaction(self, event: Dict[str, Any], hint: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process transaction before sending."""
        # Add performance context
        if 'contexts' in event:
            trace_context = event['contexts'].get('trace', {})
            
            # Add analysis-specific context
            if 'analysis' in trace_context.get('data', {}):
                event.setdefault('tags', {}).update({
                    'analysis_type': trace_context['data']['analysis'].get('type'),
                    'commodity': trace_context['data']['analysis'].get('commodity'),
                    'tier': trace_context['data']['analysis'].get('tier')
                })
        
        return event
    
    def _filter_sensitive_data(self, event: Dict[str, Any]) -> None:
        """Filter sensitive data from event."""
        sensitive_fields = {'password', 'token', 'api_key', 'secret'}
        
        # Filter request data
        if 'request' in event and 'data' in event['request']:
            for field in list(event['request']['data'].keys()):
                if any(s in field.lower() for s in sensitive_fields):
                    event['request']['data'][field] = '[FILTERED]'
        
        # Filter extra context
        if 'extra' in event:
            for field in list(event['extra'].keys()):
                if any(s in field.lower() for s in sensitive_fields):
                    event['extra'][field] = '[FILTERED]'
    
    def _add_custom_fingerprint(self, event: Dict[str, Any]) -> None:
        """Add custom fingerprint for better error grouping."""
        if 'exception' in event and event['exception'].get('values'):
            exc = event['exception']['values'][0]
            exc_type = exc.get('type', '')
            exc_module = exc.get('module', '')
            
            # Custom fingerprinting for Yemen-specific errors
            if 'DataQuality' in exc_type:
                event['fingerprint'] = ['data-quality', exc_type, '{{ default }}']
            elif 'ModelConvergence' in exc_type:
                event['fingerprint'] = ['model-convergence', exc_type, '{{ default }}']
            elif 'Integration' in exc_type:
                event['fingerprint'] = ['integration-error', exc_type, '{{ default }}']
    
    def set_analysis_context(self, analysis_id: str, analysis_type: str,
                           commodity: str, tier: Optional[str] = None) -> None:
        """Set analysis-specific context."""
        set_context("analysis", {
            "id": analysis_id,
            "type": analysis_type,
            "commodity": commodity,
            "tier": tier
        })
        
        set_tag("analysis_type", analysis_type)
        set_tag("commodity", commodity)
        if tier:
            set_tag("tier", tier)
    
    def set_data_context(self, data_source: str, dataset: str,
                        coverage: float, quality_score: float) -> None:
        """Set data-specific context."""
        set_context("data", {
            "source": data_source,
            "dataset": dataset,
            "coverage": coverage,
            "quality_score": quality_score
        })
        
        set_tag("data_source", data_source)
        set_tag("dataset", dataset)
    
    def add_breadcrumb(self, message: str, category: str,
                      level: str = "info", data: Optional[Dict[str, Any]] = None) -> None:
        """Add breadcrumb for debugging."""
        add_breadcrumb(
            message=message,
            category=category,
            level=level,
            data=data or {}
        )
    
    def capture_data_quality_issue(self, issue_type: str, dataset: str,
                                 details: Dict[str, Any], severity: str = "warning") -> None:
        """Capture data quality issue."""
        self.add_breadcrumb(
            f"Data quality issue: {issue_type}",
            category="data.quality",
            data={"dataset": dataset, **details}
        )
        
        capture_message(
            f"Data quality issue in {dataset}: {issue_type}",
            level=severity,
            extras=details
        )
    
    def capture_model_error(self, model_type: str, error_type: str,
                          commodity: str, details: Dict[str, Any]) -> None:
        """Capture model-specific error."""
        self.add_breadcrumb(
            f"Model error: {error_type}",
            category="model.error",
            data={"model_type": model_type, "commodity": commodity, **details}
        )
        
        set_tag("model_type", model_type)
        set_tag("error_type", error_type)
        
        capture_message(
            f"Model error in {model_type} for {commodity}: {error_type}",
            level="error",
            extras=details
        )
    
    def capture_integration_error(self, service: str, operation: str,
                                error: Exception, context: Dict[str, Any]) -> None:
        """Capture integration error with external service."""
        self.add_breadcrumb(
            f"Integration error with {service}",
            category="integration.error",
            data={"operation": operation, **context}
        )
        
        set_tag("integration_service", service)
        set_tag("integration_operation", operation)
        
        capture_exception(error, extras=context)


# Decorators for Sentry integration
def track_performance(operation_name: str, op_type: str = "function"):
    """Decorator to track performance of operations."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            with sentry_sdk.start_transaction(
                op=op_type,
                name=operation_name
            ) as transaction:
                transaction.set_tag("function", func.__name__)
                transaction.set_tag("module", func.__module__)
                
                try:
                    result = await func(*args, **kwargs)
                    transaction.set_status("ok")
                    return result
                except Exception as e:
                    transaction.set_status("internal_error")
                    raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            with sentry_sdk.start_transaction(
                op=op_type,
                name=operation_name
            ) as transaction:
                transaction.set_tag("function", func.__name__)
                transaction.set_tag("module", func.__module__)
                
                try:
                    result = func(*args, **kwargs)
                    transaction.set_status("ok")
                    return result
                except Exception as e:
                    transaction.set_status("internal_error")
                    raise
        
        import asyncio
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


def capture_errors(capture_args: bool = False):
    """Decorator to automatically capture errors."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                extras = {"function": func.__name__, "module": func.__module__}
                
                if capture_args:
                    extras["args"] = str(args)
                    extras["kwargs"] = str(kwargs)
                
                capture_exception(e, extras=extras)
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                extras = {"function": func.__name__, "module": func.__module__}
                
                if capture_args:
                    extras["args"] = str(args)
                    extras["kwargs"] = str(kwargs)
                
                capture_exception(e, extras=extras)
                raise
        
        import asyncio
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    
    return decorator


# Alert rules configuration
ALERT_RULES = {
    "high_error_rate": {
        "condition": "error_rate > 5%",
        "window": "5m",
        "severity": "critical",
        "notification": ["email", "slack"]
    },
    "data_quality_degradation": {
        "condition": "data_quality_score < 0.7",
        "window": "15m",
        "severity": "warning",
        "notification": ["email"]
    },
    "model_convergence_failure": {
        "condition": "convergence_rate < 0.8",
        "window": "1h",
        "severity": "error",
        "notification": ["email", "slack"]
    },
    "api_latency": {
        "condition": "p95_latency > 2000ms",
        "window": "5m",
        "severity": "warning",
        "notification": ["slack"]
    },
    "memory_usage": {
        "condition": "memory_usage > 80%",
        "window": "10m",
        "severity": "warning",
        "notification": ["email"]
    }
}


# Global Sentry manager
import logging
sentry_manager = SentryManager()