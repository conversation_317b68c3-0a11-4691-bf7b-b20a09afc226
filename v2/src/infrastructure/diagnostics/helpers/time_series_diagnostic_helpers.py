"""Helper functions for time series diagnostic tests."""

from typing import <PERSON>, Di<PERSON>, Optional, Tuple
import pandas as pd
import numpy as np
from statsmodels.tsa.stattools import adfuller, kpss, acf, pacf, coint
from statsmodels.stats.diagnostic import acorr_ljungbox, het_arch
from statsmodels.stats.stattools import jarque_bera
import statsmodels.api as sm

from ...logging import Logger

logger = Logger(__name__)


def test_stationarity(series: pd.Series) -> Dict[str, Any]:
    """Augmented <PERSON><PERSON>-Fuller (ADF) and KPSS tests for stationarity."""
    results = {}
    
    # ADF test (Null: unit root)
    try:
        adf_result = adfuller(series.dropna())
        results['adf'] = {
            'test_name': 'Augmented Dickey-Fuller Test',
            'test_statistic': float(adf_result[0]),
            'p_value': float(adf_result[1]),
            'critical_values': {str(k): float(v) for k, v in adf_result[4].items()},
            'reject_null': adf_result[1] < 0.05,
            'interpretation': "Stationary" if adf_result[1] < 0.05 else "Non-stationary (unit root)"
        }
    except Exception as e:
        logger.warning(f"ADF test failed: {e}")
        results['adf'] = {'error': str(e)}
        
    # KPSS test (Null: stationary)
    try:
        kpss_result = kpss(series.dropna(), regression='c') # 'c' for constant
        results['kpss'] = {
            'test_name': 'KPSS Test',
            'test_statistic': float(kpss_result[0]),
            'p_value': float(kpss_result[1]),
            'critical_values': {str(k): float(v) for k, v in kpss_result[3].items()},
            'reject_null': kpss_result[1] < 0.05,
            'interpretation': "Non-stationary" if kpss_result[1] < 0.05 else "Stationary"
        }
    except Exception as e:
        logger.warning(f"KPSS test failed: {e}")
        results['kpss'] = {'error': str(e)}
        
    return results


def test_autocorrelation(residuals: pd.Series, lags: int = 20) -> Dict[str, Any]:
    """Ljung-Box test for autocorrelation in residuals."""
    results = {}
    try:
        lb_test = acorr_ljungbox(residuals.dropna(), lags=[lags], return_df=True)
        results['ljung_box'] = {
            'test_name': 'Ljung-Box Test',
            'test_statistic': float(lb_test['lb_stat'].iloc[0]),
            'p_value': float(lb_test['lb_pvalue'].iloc[0]),
            'reject_null': lb_test['lb_pvalue'].iloc[0] < 0.05,
            'interpretation': "Autocorrelation detected" if lb_test['lb_pvalue'].iloc[0] < 0.05 else "No autocorrelation"
        }
    except Exception as e:
        logger.warning(f"Ljung-Box test failed: {e}")
        results['ljung_box'] = {'error': str(e)}
        
    return results


def test_heteroskedasticity_ts(residuals: pd.Series, lags: int = 10) -> Dict[str, Any]:
    """ARCH test for conditional heteroskedasticity."""
    results = {}
    try:
        # het_arch requires exogenous variables, use lagged squared residuals as proxy
        # Or simply use the residuals themselves if no exog is available
        if len(residuals.dropna()) > lags:
            arch_test = het_arch(residuals.dropna(), lags=lags)
            results['arch'] = {
                'test_name': 'ARCH Test',
                'test_statistic': float(arch_test[0]),
                'p_value': float(arch_test[1]),
                'reject_null': arch_test[1] < 0.05,
                'interpretation': "ARCH effects detected" if arch_test[1] < 0.05 else "No ARCH effects"
            }
        else:
            results['arch'] = {'error': 'Insufficient data for ARCH test'}
    except Exception as e:
        logger.warning(f"ARCH test failed: {e}")
        results['arch'] = {'error': str(e)}
        
    return results


def test_normality(residuals: pd.Series) -> Dict[str, Any]:
    """Jarque-Bera test for normality of residuals."""
    results = {}
    try:
        jb_test = jarque_bera(residuals.dropna())
        results['jarque_bera'] = {
            'test_name': 'Jarque-Bera Test',
            'test_statistic': float(jb_test[0]),
            'p_value': float(jb_test[1]),
            'reject_null': jb_test[1] < 0.05,
            'interpretation': "Non-normal residuals" if jb_test[1] < 0.05 else "Normal residuals"
        }
    except Exception as e:
        logger.warning(f"Jarque-Bera test failed: {e}")
        results['jarque_bera'] = {'error': str(e)}
        
    return results


def test_cointegration_engle_granger(y: pd.Series, x: pd.Series) -> Dict[str, Any]:
    """Engle-Granger two-step cointegration test."""
    results = {}
    try:
        # Step 1: Regress y on x to get residuals
        model = sm.OLS(y, sm.add_constant(x)).fit()
        residuals = model.resid
        
        # Step 2: Test residuals for unit root (ADF test)
        adf_result = adfuller(residuals.dropna())
        
        results['engle_granger'] = {
            'test_name': 'Engle-Granger Cointegration Test',
            'test_statistic': float(adf_result[0]),
            'p_value': float(adf_result[1]),
            'critical_values': {str(k): float(v) for k, v in adf_result[4].items()},
            'reject_null': adf_result[1] < 0.05, # Null is no cointegration (unit root in residuals)
            'interpretation': "Cointegrated" if adf_result[1] < 0.05 else "Not cointegrated"
        }
    except Exception as e:
        logger.warning(f"Engle-Granger test failed: {e}")
        results['engle_granger'] = {'error': str(e)}
        
    return results


def test_cointegration_johansen(data: pd.DataFrame, det_order: int = 0, k_ar_diff: int = 1) -> Dict[str, Any]:
    """Johansen test for cointegration rank."""
    results = {}
    try:
        from statsmodels.tsa.vector_ar.vecm import coint_johansen
        
        johansen_result = coint_johansen(data.dropna().values, det_order=det_order, k_ar_diff=k_ar_diff)
        
        results['johansen'] = {
            'test_name': 'Johansen Cointegration Test',
            'trace_statistic': johansen_result.lr1.tolist(),
            'trace_critical_values': johansen_result.cvt.tolist(),
            'max_eigen_statistic': johansen_result.lr2.tolist(),
            'max_eigen_critical_values': johansen_result.cvm.tolist(),
            'eigenvalues': johansen_result.eig.tolist(),
            'eigenvectors': johansen_result.evec.tolist(),
            'interpretation': "See trace and max-eigenvalue statistics for cointegration rank"
        }
    except Exception as e:
        logger.warning(f"Johansen test failed: {e}")
        results['johansen'] = {'error': str(e)}
        
    return results


def calculate_granger_causality(data: pd.DataFrame, max_lags: int = 5) -> Dict[str, Any]:
    """Granger causality test."""
    results = {}
    try:
        from statsmodels.tsa.stattools import grangercausalitytests
        
        # Iterate over all pairs of variables
        for col1 in data.columns:
            for col2 in data.columns:
                if col1 == col2:
                    continue
                
                test_data = data[[col1, col2]].dropna()
                if len(test_data) < max_lags + 2:
                    continue
                
                # Test if col2 Granger-causes col1
                test_result = grangercausalitytests(test_data[[col1, col2]], max_lags, verbose=False)
                
                # Extract p-value for the best lag (smallest p-value)
                min_p_value = 1.0
                for lag in range(1, max_lags + 1):
                    if lag in test_result and 'ssr_ftest' in test_result[lag][0]:
                        p_val = test_result[lag][0]['ssr_ftest'][1]
                        if p_val < min_p_value:
                            min_p_value = p_val
                
                results[f'{col2}_causes_{col1}'] = {
                    'test_name': f'Granger Causality ({col2} -> {col1})',
                    'p_value': float(min_p_value),
                    'reject_null': min_p_value < 0.05,
                    'interpretation': f"{col2} Granger-causes {col1}" if min_p_value < 0.05 else f"{col2} does not Granger-cause {col1}"
                }
    except Exception as e:
        logger.warning(f"Granger causality test failed: {e}")
        results['granger_causality'] = {'error': str(e)}
        
    return results
