"""
Quandt Likelihood Ratio (QLR) test for unknown structural break date.
"""

from typing import Tuple, List
import pandas as pd
import numpy as np
import statsmodels.api as sm
from scipy import stats
import patsy

from ...logging import Logger
from .chow_structural_break import chow_structural_break_test

logger = Logger(__name__)


def quandt_likelihood_ratio_test(
    data: pd.DataFrame,
    formula: str,
    trim_pct: float = 0.15,
    entity_col: str = 'entity',
    time_col: str = 'date'
) -> Tuple[float, str, float, str]:
    """
    Quandt Likelihood Ratio (QLR) test for unknown structural break date.
    
    Tests for structural breaks at all possible dates, selecting the maximum
    test statistic.
    
    Parameters
    ----------
    data : pd.DataFrame
        Panel data
    formula : str
        Model formula
    trim_pct : float
        Percentage to trim from start/end (default: 0.15)
    entity_col : str
        Name of entity column
    time_col : str
        Name of time column
        
    Returns
    -------
    <PERSON><PERSON>[float, str, float, str]
        Max F-statistic, break date, p-value approximation, recommendation
    """
    logger.info(f"Running Quandt Likelihood Ratio test for unknown break date...")
    
    try:
        # Get unique sorted dates
        dates = sorted(data[time_col].unique())
        n_dates = len(dates)
        
        # Determine trim points
        trim_n = int(n_dates * trim_pct)
        test_dates = dates[trim_n:-trim_n]
        
        if len(test_dates) < 5:
            return np.nan, "", np.nan, "Insufficient dates for QLR test"
        
        # Test each potential break date
        f_stats = []
        
        for break_date in test_dates:
            f_stat, _, _ = chow_structural_break_test(
                data, formula, str(break_date), entity_col, time_col
            )
            if not np.isnan(f_stat):
                f_stats.append((f_stat, break_date))
        
        if not f_stats:
            return np.nan, "", np.nan, "No valid F-statistics computed"
        
        # Find maximum F-statistic
        max_f_stat, max_break_date = max(f_stats, key=lambda x: x[0])
        
        # Andrews (1993) critical values for QLR test
        if max_f_stat > 12.0:
            p_value_approx = 0.01
            recommendation = (
                f"Strong evidence of structural break around {max_break_date} (p < 0.01). "
                "Implement regime-specific models or time-varying parameters."
            )
        elif max_f_stat > 9.0:
            p_value_approx = 0.05
            recommendation = (
                f"Evidence of structural break around {max_break_date} (p < 0.05). "
                "Consider parameter instability in inference."
            )
        elif max_f_stat > 7.5:
            p_value_approx = 0.10
            recommendation = (
                f"Weak evidence of structural break around {max_break_date} (p < 0.10). "
                "Monitor parameter stability."
            )
        else:
            p_value_approx = 0.20
            recommendation = "No significant evidence of structural breaks."
        
        logger.info(f"QLR test: Max F = {max_f_stat:.4f} at {max_break_date}")
        
        return max_f_stat, str(max_break_date), p_value_approx, recommendation
        
    except Exception as e:
        logger.error(f"Error in QLR test: {e}")
        return np.nan, "", np.nan, f"Error: {e}"
