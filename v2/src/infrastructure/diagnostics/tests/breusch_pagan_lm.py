"""
Breusch-Pagan LM test for cross-sectional dependence (alternative to Pesaran CD).
"""

from typing import Tuple, Any, Dict
import pandas as pd
import numpy as np
from scipy import stats

from ...logging import Logger

logger = Logger(__name__)

PanelInfo = Dict[str, Any]


def breusch_pagan_lm_test(
    residuals: pd.Series,
    panel_info: PanelInfo,
    scaled: bool = True
) -> <PERSON><PERSON>[float, float, str]:
    """
    Breusch-Pagan LM test for cross-sectional dependence (alternative to Pesaran CD).

    This test is more suitable for panels where N is small relative to T.
    The null hypothesis is no cross-sectional dependence.

    Parameters
    ----------
    residuals : pd.Series
        The residuals from the panel model, indexed by entity and time.
    panel_info : PanelInfo
        A dictionary containing panel structure information.
    scaled : bool, default=True
        If True, use the scaled version of the test (better for finite samples).

    Returns
    -------
    Tuple[float, float, str]
        Test statistic, p-value, and a recommendation string.

    References
    ----------
    <PERSON><PERSON><PERSON>, T. S., & <PERSON>, <PERSON><PERSON> (1980). The Lagrange multiplier test and its
    applications to model specification in econometrics. Review of Economic Studies, 47(1), 239-253.
    """
    logger.info("Running Breusch-Pagan LM test for cross-sectional dependence...")
    entity_id = panel_info.get("entity_id", "entity")
    time_id = panel_info.get("time_id", "time")
    N = panel_info.get("N")

    if not isinstance(residuals.index, pd.MultiIndex):
        logger.error("Breusch-Pagan LM test requires residuals with a MultiIndex (entity, time).")
        return np.nan, np.nan, "Error: Residuals must have MultiIndex."

    if N is None or N < 2:
        logger.warning("Not enough entities for Breusch-Pagan LM test.")
        return np.nan, np.nan, "Not enough entities for test (N<2)."

    # Reshape residuals to wide format
    resid_df = residuals.reset_index()
    try:
        wide_residuals = resid_df.pivot(index=time_id, columns=entity_id, values=residuals.name or 'resid')
    except Exception as e:
        logger.error(f"Could not pivot residuals for Breusch-Pagan LM test: {e}")
        return np.nan, np.nan, f"Error pivoting data: {e}"

    # Drop entities/times with all NaNs
    wide_residuals = wide_residuals.dropna(axis=1, how='all')
    wide_residuals = wide_residuals.dropna(axis=0, how='all')

    N_actual = wide_residuals.shape[1]
    if N_actual < 2:
        logger.warning("Not enough entities with non-missing residuals for Breusch-Pagan LM test.")
        return np.nan, np.nan, "Not enough entities after handling NaNs."

    # Calculate correlation matrix of residuals
    corr_matrix = wide_residuals.corr()

    lm_stat = 0
    T_avg = 0
    num_pairs = 0

    entities = wide_residuals.columns
    for i in range(N_actual):
        for j in range(i + 1, N_actual):
            rho_ij_sq = corr_matrix.iloc[i, j]**2

            if not np.isnan(rho_ij_sq):
                # Count common observations for this pair
                resid_i = wide_residuals[entities[i]]
                resid_j = wide_residuals[entities[j]]
                common_mask = ~resid_i.isnull() & ~resid_j.isnull()
                T_ij = common_mask.sum()

                if T_ij >= 2:
                    lm_stat += T_ij * rho_ij_sq
                    T_avg += T_ij
                    num_pairs += 1

    if num_pairs == 0:
        logger.warning("No valid pairs for Breusch-Pagan LM test.")
        return np.nan, np.nan, "No valid pairs for test."

    T_avg = T_avg / num_pairs if num_pairs > 0 else 0

    if scaled:
        lm_stat_scaled = np.sqrt(1 / (N_actual * (N_actual - 1))) * (lm_stat - num_pairs)
        p_value = 2 * (1 - stats.norm.cdf(np.abs(lm_stat_scaled)))
        test_stat = lm_stat_scaled
    else:
        df = N_actual * (N_actual - 1) / 2
        p_value = 1 - stats.chi2.cdf(lm_stat, df)
        test_stat = lm_stat

    recommendation = ""
    if p_value <= 0.01:
        recommendation = (
            "Strong evidence of cross-sectional dependence (p < 0.01). "
            "Use Driscoll-Kraay standard errors or spatial panel models. "
            "Consider common correlated effects (CCE) estimators."
        )
    elif p_value <= 0.05:
        recommendation = (
            "Evidence of cross-sectional dependence (p < 0.05). "
            "Use Driscoll-Kraay standard errors or consider spatial correlation."
        )
    else:
        recommendation = "No significant evidence of cross-sectional dependence."

    return test_stat, p_value, recommendation
