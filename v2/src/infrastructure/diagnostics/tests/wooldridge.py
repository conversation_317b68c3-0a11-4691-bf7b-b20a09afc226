"""
<PERSON><PERSON><PERSON> test for serial correlation in panel data (<PERSON><PERSON><PERSON> 2003 implementation).
"""

from typing import <PERSON>ple, Any, Dict
import pandas as pd
import numpy as np
import statsmodels.api as sm
from scipy import stats

from ...logging import Logger

logger = Logger(__name__)

PanelInfo = Dict[str, Any]


def wooldridge_serial_correlation(
    residuals: pd.Series, panel_info: PanelInfo
) -> <PERSON><PERSON>[float, float, str]:
    """
    W<PERSON><PERSON> test for serial correlation in panel data (<PERSON><PERSON><PERSON> 2003 implementation).

    This implements the exact specification from <PERSON><PERSON><PERSON> (2003) "Testing for serial
    correlation in linear panel-data models" which is used in Stata's xtserial command.
    The test is designed for fixed effects models and tests for AR(1) serial correlation
    in the idiosyncratic errors.

    Parameters
    ----------
    residuals : pd.Series
        The residuals from the panel model (preferably from a fixed effects model),
        indexed by entity and time.
    panel_info : PanelInfo
        A dictionary containing panel structure information.

    Returns
    -------
    <PERSON><PERSON>[float, float, str]
        F-statistic, p-value, and a recommendation string.

    References
    ----------
    <PERSON><PERSON><PERSON>, <PERSON><PERSON> (2003). Testing for serial correlation in linear panel-data models.
    The Stata Journal, 3(2), 168-177.

    <PERSON><PERSON>, <PERSON><PERSON> (2002). Econometric Analysis of Cross Section and Panel Data.
    MIT Press, Chapter 10.5.4.
    """
    logger.info("Running Wooldridge test for serial correlation (Drukker 2003 specification)...")
    entity_id = panel_info.get("entity_id", "entity")
    time_id = panel_info.get("time_id", "time")
    N = panel_info.get("N")

    # Validate inputs
    if not isinstance(residuals.index, pd.MultiIndex):
        logger.error("Wooldridge test requires residuals with a MultiIndex (entity, time).")
        return np.nan, np.nan, "Error: Residuals must have MultiIndex."

    if N is None or N < 2:
        logger.error("Wooldridge test requires at least 2 entities.")
        return np.nan, np.nan, "Error: Need at least 2 entities for test."

    # Create DataFrame and ensure proper sorting
    df = residuals.reset_index(name='resid')
    df = df.sort_values(by=[entity_id, time_id])

    # Step 1: First-difference the residuals within each entity
    df['resid_diff'] = df.groupby(entity_id)['resid'].diff()
    df['resid_diff_lag1'] = df.groupby(entity_id)['resid_diff'].shift(1)

    # Drop missing values from differencing and lagging
    df_clean = df.dropna(subset=['resid_diff', 'resid_diff_lag1'])

    if len(df_clean) < 10:  # Arbitrary minimum for meaningful test
        logger.warning("Not enough observations for Wooldridge test after differencing.")
        return np.nan, np.nan, "Not enough data for test (need at least 10 obs after differencing)."

    # Count observations per entity after cleaning
    obs_per_entity = df_clean.groupby(entity_id).size()
    entities_with_data = len(obs_per_entity)

    if entities_with_data < 2:
        logger.warning("Less than 2 entities have sufficient data after differencing.")
        return np.nan, np.nan, "Not enough entities with data after differencing."

    # Step 2: Run the auxiliary regression
    X = sm.add_constant(df_clean['resid_diff_lag1'])
    y = df_clean['resid_diff']

    try:
        aux_model = sm.OLS(y, X).fit()

        rho_hat = aux_model.params['resid_diff_lag1']
        se_rho = aux_model.bse['resid_diff_lag1']

        t_stat = (rho_hat - (-0.5)) / se_rho

        f_stat = t_stat ** 2

        df1 = 1
        df2 = len(df_clean) - 2

        p_value = 1 - stats.f.cdf(f_stat, df1, df2)

        logger.info(f"Wooldridge test: rho_hat = {rho_hat:.4f} (H0: rho = -0.5)")
        logger.info(f"Number of entities used: {entities_with_data}, Total obs: {len(df_clean)}")

        recommendation = ""
        if p_value <= 0.01:
            recommendation = (
                "Strong evidence of serial correlation (p < 0.01). "
                "Use cluster-robust standard errors or Driscoll-Kraay standard errors. "
                "Consider dynamic panel models (e.g., Arellano-Bond) if appropriate."
            )
        elif p_value <= 0.05:
            recommendation = (
                "Evidence of serial correlation (p < 0.05). "
                "Use cluster-robust standard errors at minimum. "
                "Consider Driscoll-Kraay SEs if cross-sectional dependence is also present."
            )
        elif p_value <= 0.10:
            recommendation = (
                "Weak evidence of serial correlation (p < 0.10). "
                "Consider using cluster-robust standard errors as a precaution."
            )
        else:
            recommendation = (
                "No significant evidence of serial correlation. "
                "Standard errors from the model are likely reliable, "
                "but cluster-robust SEs are still recommended for panel data."
            )

        return f_stat, p_value, recommendation

    except Exception as e:
        logger.error(f"Error in Wooldridge test auxiliary regression: {e}")
        return np.nan, np.nan, f"Error during test: {e}"
