"""
Modified Wald test for groupwise heteroskedasticity in panel data.
"""

from typing import <PERSON>ple, Any, Dict, Optional
import pandas as pd
import numpy as np
from scipy import stats

from ...logging import Logger

logger = Logger(__name__)

PanelInfo = Dict[str, Any]


def modified_wald_heteroskedasticity(
    residuals: pd.Series,
    panel_info: PanelInfo,
    fitted_values: Optional[pd.Series] = None
) -> <PERSON>ple[float, float, str]:
    """
    Modified Wald test for groupwise heteroskedasticity in panel data.

    This test checks whether the variance of errors differs across entities
    (cross-sectional units). It's particularly important for fixed effects models.
    The null hypothesis is homoskedasticity (equal variances across groups).

    Parameters
    ----------
    residuals : pd.Series
        The residuals from the panel model, indexed by entity and time.
    panel_info : PanelInfo
        A dictionary containing panel structure information.
    fitted_values : pd.Series, optional
        Fitted values from the model (not used in basic version but kept for API consistency).

    Returns
    -------
    <PERSON><PERSON>[float, float, str]
        Chi-squared statistic, p-value, and a recommendation string.

    References
    ----------
    <PERSON>, W<PERSON> (2003). Econometric Analysis (5th ed.). Prentice Hall.
    Baum, C. F. (2001). Residual diagnostics for cross-section time series regression models.
    The Stata Journal, 1(1), 101-104.
    """
    logger.info("Running Modified Wald test for groupwise heteroskedasticity...")
    entity_id = panel_info.get("entity_id", "entity")
    time_id = panel_info.get("time_id", "time")
    N = panel_info.get("N")

    # Validate inputs
    if not isinstance(residuals.index, pd.MultiIndex):
        logger.error("Modified Wald test requires residuals with a MultiIndex (entity, time).")
        return np.nan, np.nan, "Error: Residuals must have MultiIndex."

    if N is None or N < 2:
        logger.error("Modified Wald test requires at least 2 entities.")
        return np.nan, np.nan, "Error: Need at least 2 entities for test."

    # Create DataFrame from residuals
    df = residuals.reset_index(name='resid')
    df = df.sort_values(by=[entity_id, time_id])

    # Calculate group-specific variances
    group_variances = df.groupby(entity_id)['resid'].var()
    group_counts = df.groupby(entity_id).size()

    # Remove groups with insufficient observations
    valid_groups = group_counts[group_counts >= 2].index
    group_variances = group_variances[valid_groups]
    group_counts = group_counts[valid_groups]

    N_valid = len(valid_groups)
    if N_valid < 2:
        logger.warning("Less than 2 entities have sufficient observations for variance calculation.")
        return np.nan, np.nan, "Not enough entities with sufficient data."

    # Calculate the overall variance (under H0 of homoskedasticity)
    overall_variance = df[df[entity_id].isin(valid_groups)]['resid'].var()

    chi2_stat = 0
    for entity in valid_groups:
        T_i = group_counts[entity]
        sigma_i_sq = group_variances[entity]

        chi2_stat += (T_i - 1) * (sigma_i_sq / overall_variance - 1)**2

    df = N_valid - 1

    p_value = 1 - stats.chi2.cdf(chi2_stat, df)

    logger.info(f"Modified Wald test: Chi2({df}) = {chi2_stat:.4f}")
    logger.info(f"Number of entities tested: {N_valid}")
    logger.info(f"Variance ratio range: [{group_variances.min()/overall_variance:.2f}, {group_variances.max()/overall_variance:.2f}]")

    recommendation = ""
    if p_value <= 0.01:
        recommendation = (
            "Strong evidence of groupwise heteroskedasticity (p < 0.01). "
            "Use robust standard errors clustered by entity. "
            "Consider weighted least squares or feasible GLS if efficiency is important."
        )
    elif p_value <= 0.05:
        recommendation = (
            "Evidence of groupwise heteroskedasticity (p < 0.05). "
            "Use robust standard errors clustered by entity."
        )
    elif p_value <= 0.10:
        recommendation = (
            "Weak evidence of groupwise heteroskedasticity (p < 0.10). "
            "Consider using robust standard errors as a precaution."
        )
    else:
        recommendation = (
            "No significant evidence of groupwise heteroskedasticity. "
            "Standard errors from the model are likely reliable, "
            "but robust SEs are still recommended for panel data."
        )

    return chi2_stat, p_value, recommendation
