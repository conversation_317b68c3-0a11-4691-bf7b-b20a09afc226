"""Query to retrieve market prices."""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, Dict, Any # Added Dict, Any

from ....core.domain.market.repositories import PriceRepository, CommodityRepository
from ....core.domain.market.value_objects import MarketId, Commodity
from ...interfaces import Query, QueryHandler
from ....infrastructure.logging import Logger

logger = Logger(__name__)


@dataclass
class GetMarketPricesQuery(Query):
    """Query to get market prices for specific markets and commodities within a date range."""
    
    market_ids: Optional[List[str]] = None
    commodity_codes: Optional[List[str]] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None


class GetMarketPricesQueryHandler(QueryHandler):
    """Handler for GetMarketPricesQuery."""
    
    def __init__(self, price_repo: PriceRepository, commodity_repo: CommodityRepository):
        self.price_repo = price_repo
        self.commodity_repo = commodity_repo
    
    async def handle(self, query: GetMarketPricesQuery) -> List[Dict[str, Any]]:
        """Execute the query to retrieve market prices."""
        # Convert string IDs to domain MarketId and Commodity objects if provided
        market_ids_domain = [MarketId(mid) for mid in query.market_ids] if query.market_ids else None
        
        # Fetch full Commodity objects from repository
        commodity_objects = []
        if query.commodity_codes:
            for code in query.commodity_codes:
                commodity = await self.commodity_repo.find_by_code(code)
                if commodity:
                    commodity_objects.append(commodity)
                else:
                    logger.warning(f"Commodity with code '{code}' not found")
        
        # Fetch price observations using appropriate repository methods
        all_observations = []
        
        try:
            if market_ids_domain and commodity_objects:
                # Fetch for specific markets and commodities
                for market_id in market_ids_domain:
                    for commodity in commodity_objects:
                        observations = await self.price_repo.find_by_market_and_commodity(
                            market_id=market_id,
                            commodity=commodity,
                            start_date=query.start_date,
                            end_date=query.end_date
                        )
                        all_observations.extend(observations)
                        
            elif market_ids_domain:
                # Fetch all commodities for given markets
                observations = await self.price_repo.find_by_markets(
                    market_ids=market_ids_domain,
                    start_date=query.start_date,
                    end_date=query.end_date
                )
                all_observations.extend(observations)
                
            elif commodity_objects:
                # Fetch all markets for given commodities
                observations = await self.price_repo.find_by_commodities(
                    commodities=commodity_objects,
                    start_date=query.start_date,
                    end_date=query.end_date
                )
                all_observations.extend(observations)
                
            else:
                # Fetch all prices within date range (with pagination)
                page_size = 1000  # Reasonable page size
                offset = 0
                
                while True:
                    observations = await self.price_repo.find_by_date_range(
                        start_date=query.start_date,
                        end_date=query.end_date,
                        limit=page_size,
                        offset=offset
                    )
                    
                    if not observations:
                        break
                        
                    all_observations.extend(observations)
                    
                    # Limit total results to prevent memory issues
                    if len(all_observations) >= 10000:
                        logger.warning("Result set limited to 10,000 records")
                        break
                        
                    offset += page_size
                    
        except Exception as e:
            logger.error(f"Error fetching price observations: {e}")
            raise

        # Convert domain objects to a serializable dictionary format
        results = []
        for obs in all_observations:
            results.append({
                "market_id": obs.market_id.value,
                "commodity_code": obs.commodity.code,
                "commodity_name": obs.commodity.name,
                "price_amount": float(obs.price.amount),
                "price_currency": obs.price.currency,
                "price_unit": obs.price.unit,
                "observed_date": obs.observed_date.isoformat(),
                "source": obs.source,
                "quality": obs.quality,
                "observations_count": obs.observations_count
            })
        
        return results
