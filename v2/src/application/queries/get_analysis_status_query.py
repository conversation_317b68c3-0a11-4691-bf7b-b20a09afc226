"""Query to retrieve analysis job status."""

from dataclasses import dataclass
from typing import Dict, Any, Optional

from ...interfaces import Query, QueryHandler
from ...application.services import AnalysisOrchestrator


@dataclass
class GetAnalysisStatusQuery(Query):
    """Query to get the status of a specific analysis job."""
    
    job_id: str


class GetAnalysisStatusQueryHandler(QueryHandler):
    """Handler for GetAnalysisStatusQuery."""
    
    def __init__(self, orchestrator: AnalysisOrchestrator):
        self.orchestrator = orchestrator
    
    async def handle(self, query: GetAnalysisStatusQuery) -> Optional[Dict[str, Any]]:
        """Execute the query to retrieve analysis job status."""
        status = await self.orchestrator.get_analysis_status(query.job_id)
        return status
