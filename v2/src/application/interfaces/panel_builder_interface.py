"""Interface for panel builder service."""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional
import pandas as pd

from ..services.panel_builder_service import PanelConfiguration, PanelCoverage


class PanelBuilderInterface(ABC):
    """Interface for panel building services."""
    
    @abstractmethod
    async def create_balanced_panel(
        self,
        config: PanelConfiguration,
        commodities: Optional[List[str]] = None,
        markets: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Create a balanced panel dataset.
        
        Args:
            config: Panel configuration parameters
            commodities: List of commodities to include (None for auto-selection)
            markets: List of markets to include (None for auto-selection)
            
        Returns:
            Balanced panel DataFrame
        """
        pass
    
    @abstractmethod
    async def create_model_specific_panels(
        self,
        base_panel: pd.DataFrame
    ) -> Dict[str, pd.DataFrame]:
        """Create model-specific panel datasets.
        
        Args:
            base_panel: Base integrated panel
            
        Returns:
            Dictionary of model-specific panels
        """
        pass
    
    @abstractmethod
    async def validate_against_v1(
        self,
        v2_panel: pd.DataFrame,
        v1_panel_path: str
    ) -> Dict[str, Any]:
        """Validate V2 panel against V1 output.
        
        Args:
            v2_panel: V2 generated panel
            v1_panel_path: Path to V1 panel file
            
        Returns:
            Dictionary with validation results
        """
        pass