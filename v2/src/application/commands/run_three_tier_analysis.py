"""Command to run the complete three-tier econometric analysis."""

from dataclasses import dataclass, field # Added field
from datetime import datetime
from typing import Any, Dict, List, Optional

from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.models import (
    PooledPanelModel,
    TwoWayFixedEffectsModel,
    ThresholdVECMModel,
)
from ...core.models.interfaces import ModelSpecification
from ...core.models.validation import (
    ConflictValidationModel,
    FactorModel,
    PCAModel,
    CrossValidationModel
)
from ...infrastructure.diagnostics import PanelDiagnosticTests, TimeSeriesDiagnosticTests
from ...infrastructure.estimators.standard_errors import StandardErrorEstimator
from ..interfaces import Command, CommandHandler
from ..services import AnalysisOrchestrator, ModelEstimatorService
from ..analysis_tiers.tier1_runner import Tier1Runner
from ..analysis_tiers.tier2_runner import Tier2Runner
from ..analysis_tiers.tier3_runner import Tier3Runner


@dataclass
class RunThreeTierAnalysisCommand(Command):
    """Command to execute three-tier econometric analysis."""
    
    start_date: datetime
    end_date: datetime
    market_ids: Optional[List[str]] = None  # None means all markets
    commodity_ids: Optional[List[str]] = None  # None means all commodities
    
    # Tier-specific configurations
    tier1_config: Dict[str, Any] = field(default_factory=dict)
    tier2_config: Dict[str, Any] = field(default_factory=dict)
    tier3_config: Dict[str, Any] = field(default_factory=dict)
    
    # Analysis options
    run_diagnostics: bool = True
    apply_corrections: bool = True
    save_intermediate: bool = True
    
    def __post_init__(self):
        """Initialize default configurations."""
        if not self.tier1_config:
            self.tier1_config = {
                "model": "two_way_fixed_effects",
                "se_type": "driscoll_kraay",
                "entity_trends": False,
                "log_transform": True
            }
        
        if not self.tier2_config:
            self.tier2_config = {
                "model": "threshold_vecm",
                "min_obs": 100,
                "n_regimes": 2,
                "threshold_variable": "conflict_intensity"
            }
        
        if not self.tier3_config:
            self.tier3_config = {
                "validation_methods": ["cross_validation", "structural_break"],
                "factor_analysis": True,
                "spatial_analysis": True,
                "dynamic_factor_analysis": True # Enable dynamic factor analysis by default
            }


class RunThreeTierAnalysisHandler(CommandHandler):
    """Handler for three-tier analysis command."""
    
    def __init__(
        self,
        market_repo: MarketRepository,
        price_repo: PriceRepository,
        orchestrator: AnalysisOrchestrator,
        estimator_service: ModelEstimatorService
    ):
        """Initialize handler with dependencies."""
        self.market_repo = market_repo
        self.price_repo = price_repo
        self.orchestrator = orchestrator
        self.estimator_service = estimator_service
        self.tier1_runner = Tier1Runner(market_repo, price_repo, orchestrator, estimator_service)
        self.tier2_runner = Tier2Runner(market_repo, price_repo, orchestrator, estimator_service)
        self.tier3_runner = Tier3Runner(market_repo, price_repo, orchestrator, estimator_service)
    
    async def handle(self, command: RunThreeTierAnalysisCommand) -> str:
        """Execute three-tier analysis."""
        analysis_id = await self.orchestrator.start_analysis(
            analysis_type="three_tier",
            parameters={
                "start_date": command.start_date,
                "end_date": command.end_date,
                "market_ids": command.market_ids,
                "commodity_ids": command.commodity_ids,
                "tier_configs": {
                    "tier1": command.tier1_config,
                    "tier2": command.tier2_config,
                    "tier3": command.tier3_config
                },
                "options": {
                    "diagnostics": command.run_diagnostics,
                    "corrections": command.apply_corrections,
                    "save_intermediate": command.save_intermediate
                }
            }
        )
        
        try:
            tier1_result = await self.tier1_runner.run(command, analysis_id)
            
            tier2_results = await self.tier2_runner.run(
                command, analysis_id, tier1_result
            )
            
            tier3_result = await self.tier3_runner.run(
                command, analysis_id, tier1_result, tier2_results
            )
            
            await self.orchestrator.complete_analysis(
                analysis_id,
                results={
                    "tier1": tier1_result,
                    "tier2": tier2_results,
                    "tier3": tier3_result
                }
            )
            
        except Exception as e:
            await self.orchestrator.fail_analysis(analysis_id, str(e))
            raise
        
        return analysis_id
