"""Command handler for running three-tier analysis using V1 adapters.

This command ensures backward compatibility by using V1 econometric models
through the adapter pattern while providing V2 architecture benefits.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.domain.market.entities import PanelData
from ...infrastructure.logging import Logger
from ..services.three_tier_analysis_service import ThreeTierAnalysisService
from ..services.data_preparation_service import DataPreparationService
from ..services.analysis_orchestrator import AnalysisOrchestrator

logger = Logger(__name__)


@dataclass
class RunThreeTierAnalysisCommand:
    """Command to run three-tier econometric analysis.
    
    Attributes
    ----------
    market_ids : List[str], optional
        Specific markets to analyze. If None, includes all markets.
    commodity_codes : List[str], optional  
        Specific commodities to analyze. If None, includes all commodities.
    start_date : datetime
        Start date for analysis period
    end_date : datetime
        End date for analysis period
    config : Dict[str, Any], optional
        Analysis configuration including:
        - validate_conflict_effect: Validate 35% effect (default: True)
        - apply_corrections: Apply diagnostic corrections (default: True)
        - run_diagnostics: Run diagnostic tests (default: True)
        - include_conflict_data: Include conflict validation (default: True)
    analysis_id : UUID, optional
        Unique identifier for this analysis run
    """
    
    start_date: datetime
    end_date: datetime
    market_ids: Optional[List[str]] = None
    commodity_codes: Optional[List[str]] = None
    config: Optional[Dict[str, Any]] = None
    analysis_id: Optional[UUID] = None
    
    def __post_init__(self):
        """Validate command and set defaults."""
        if self.analysis_id is None:
            self.analysis_id = uuid4()
        
        if self.config is None:
            self.config = {}
        
        # Set default configuration
        defaults = {
            'validate_conflict_effect': True,
            'apply_corrections': True,
            'run_diagnostics': True,
            'include_conflict_data': True,
            'parallel_commodity_analysis': False
        }
        
        for key, value in defaults.items():
            if key not in self.config:
                self.config[key] = value
        
        # Validate dates
        if self.start_date >= self.end_date:
            raise ValueError("Start date must be before end date")


class RunThreeTierAnalysisHandler:
    """Handler for three-tier analysis command.
    
    This handler coordinates:
    1. Data preparation from V2 repositories
    2. Panel construction
    3. Three-tier analysis execution via V1 adapters
    4. Result persistence and reporting
    """
    
    def __init__(self,
                 market_repo: MarketRepository,
                 price_repo: PriceRepository,
                 data_prep_service: DataPreparationService,
                 analysis_service: ThreeTierAnalysisService,
                 orchestrator: AnalysisOrchestrator):
        """Initialize handler with dependencies.
        
        Parameters
        ----------
        market_repo : MarketRepository
            Repository for market data
        price_repo : PriceRepository
            Repository for price observations
        data_prep_service : DataPreparationService
            Service for data preparation
        analysis_service : ThreeTierAnalysisService
            Three-tier analysis service with V1 adapters
        orchestrator : AnalysisOrchestrator
            Analysis orchestration service
        """
        self.market_repo = market_repo
        self.price_repo = price_repo
        self.data_prep_service = data_prep_service
        self.analysis_service = analysis_service
        self.orchestrator = orchestrator
        
        logger.info("Initialized RunThreeTierAnalysisHandler")
    
    async def handle(self, command: RunThreeTierAnalysisCommand) -> Dict[str, Any]:
        """Execute three-tier analysis command.
        
        Parameters
        ----------
        command : RunThreeTierAnalysisCommand
            Command with analysis parameters
            
        Returns
        -------
        dict
            Analysis results including all three tiers
        """
        logger.info(
            f"Handling three-tier analysis command {command.analysis_id} "
            f"for period {command.start_date} to {command.end_date}"
        )
        
        # Start analysis job
        job_id = await self.orchestrator.start_analysis(
            'three_tier_v1_adapter',
            {
                'command_id': str(command.analysis_id),
                'start_date': command.start_date.isoformat(),
                'end_date': command.end_date.isoformat(),
                'markets': command.market_ids,
                'commodities': command.commodity_codes,
                'config': command.config
            }
        )
        
        try:
            # Step 1: Load markets
            await self.orchestrator.update_progress(
                job_id, 'tier1', 0, "Loading market data"
            )
            
            if command.market_ids:
                markets = []
                for market_id in command.market_ids:
                    market = await self.market_repo.get_by_id(market_id)
                    if market:
                        markets.append(market)
            else:
                markets = await self.market_repo.get_all()
            
            logger.info(f"Loaded {len(markets)} markets")
            
            # Step 2: Prepare panel data
            await self.orchestrator.update_progress(
                job_id, 'tier1', 10, "Preparing panel data"
            )
            
            panel_data = await self.data_prep_service.prepare_panel_data(
                markets=markets,
                start_date=command.start_date,
                end_date=command.end_date,
                commodity_codes=command.commodity_codes
            )
            
            logger.info(
                f"Prepared panel data with {len(panel_data.observations)} observations"
            )
            
            # Step 3: Load conflict data if requested
            conflict_data = None
            if command.config.get('include_conflict_data'):
                await self.orchestrator.update_progress(
                    job_id, 'tier3', 0, "Loading conflict data"
                )
                
                # TODO: Load conflict data from appropriate source
                # conflict_data = await self.conflict_repo.get_events(
                #     start_date=command.start_date,
                #     end_date=command.end_date
                # )
            
            # Step 4: Run three-tier analysis
            logger.info("Starting three-tier analysis via V1 adapters")
            
            results = await self.analysis_service.run_analysis(
                panel_data=panel_data,
                config=command.config,
                conflict_data=conflict_data
            )
            
            # Step 5: Enhance results with metadata
            enhanced_results = self._enhance_results(results, command)
            
            # Complete job
            await self.orchestrator.complete_analysis(job_id, enhanced_results)
            
            logger.info(
                f"Three-tier analysis {command.analysis_id} completed successfully"
            )
            
            return enhanced_results
            
        except Exception as e:
            error_msg = f"Three-tier analysis failed: {str(e)}"
            logger.error(error_msg)
            
            await self.orchestrator.fail_analysis(job_id, error_msg)
            raise
    
    def _enhance_results(self, results: Dict[str, Any], 
                        command: RunThreeTierAnalysisCommand) -> Dict[str, Any]:
        """Enhance results with command metadata and interpretation.
        
        Parameters
        ----------
        results : dict
            Raw analysis results
        command : RunThreeTierAnalysisCommand
            Original command
            
        Returns
        -------
        dict
            Enhanced results with metadata
        """
        enhanced = results.copy()
        
        # Add command metadata
        enhanced['command'] = {
            'analysis_id': str(command.analysis_id),
            'start_date': command.start_date.isoformat(),
            'end_date': command.end_date.isoformat(),
            'markets': command.market_ids,
            'commodities': command.commodity_codes,
            'config': command.config
        }
        
        # Add interpretation
        interpretation = []
        
        # Interpret conflict effect
        conflict_effect = results['tiers']['tier1']['conflict_effect']
        if conflict_effect['is_valid']:
            interpretation.append(
                f"✓ Conflict effect validated: {conflict_effect['percentage_effect']:.1f}% "
                f"price increase (p < {conflict_effect['p_value']:.3f})"
            )
        else:
            interpretation.append(
                f"⚠️ Conflict effect validation failed: {conflict_effect['validation_message']}"
            )
        
        # Interpret market integration
        if 'summary' in results['tiers']['tier2']:
            tier2_summary = results['tiers']['tier2']['summary']
            high_integration = tier2_summary.get('high_integration_commodities', [])
            if high_integration:
                interpretation.append(
                    f"✓ High market integration detected for {len(high_integration)} "
                    f"commodities: {', '.join(high_integration[:3])}"
                )
        
        # Interpret validation results
        if 'validation_summary' in results['tiers']['tier3']:
            val_summary = results['tiers']['tier3']['validation_summary']
            integration_level = val_summary.get('integration_assessment', 'Unknown')
            interpretation.append(
                f"✓ Overall market integration level: {integration_level}"
            )
        
        # Add policy recommendations
        recommendations = self._generate_recommendations(results)
        
        enhanced['interpretation'] = {
            'findings': interpretation,
            'recommendations': recommendations,
            'confidence': results.get('confidence_scores', {}).get('overall', 0)
        }
        
        return enhanced
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate policy recommendations based on results.
        
        Parameters
        ----------
        results : dict
            Analysis results
            
        Returns
        -------
        list
            Policy recommendations
        """
        recommendations = []
        
        # Based on conflict effect
        conflict_coef = results['tiers']['tier1']['conflict_effect']['coefficient']
        if conflict_coef > 0.3:
            recommendations.append(
                "Consider targeted interventions in high-conflict markets to "
                "mitigate price increases"
            )
        
        # Based on market integration
        if 'summary' in results['tiers']['tier2']:
            integration_dist = results['tiers']['tier2']['summary'].get(
                'integration_distribution', {}
            )
            
            if integration_dist.get('Low', 0) > integration_dist.get('High', 0):
                recommendations.append(
                    "Improve transport infrastructure to enhance market connectivity"
                )
        
        # Based on commodity patterns
        high_integration = results['tiers']['tier2'].get('summary', {}).get(
            'high_integration_commodities', []
        )
        
        if 'Wheat' in high_integration or 'Rice' in high_integration:
            recommendations.append(
                "Leverage well-integrated staple food markets for efficient "
                "humanitarian aid distribution"
            )
        
        return recommendations