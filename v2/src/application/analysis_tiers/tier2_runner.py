"""Tier 2 analysis runner."""

from typing import Any, Dict, List
import pandas as pd
import numpy as np

from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.models import ThresholdVECMModel, VECMModel
from ...core.models.interfaces import ModelSpecification
from ...infrastructure.diagnostics import TimeSeriesDiagnosticTests
from ..services import AnalysisOrchestrator, ModelEstimatorService
from ..commands.run_three_tier_analysis import RunThreeTierAnalysisCommand # Import for type hinting


class Tier2Runner:
    """Encapsulates logic for running Tier 2 econometric analysis."""

    def __init__(
        self,
        market_repo: MarketRepository,
        price_repo: PriceRepository,
        orchestrator: AnalysisOrchestrator,
        estimator_service: ModelEstimatorService
    ):
        self.market_repo = market_repo
        self.price_repo = price_repo
        self.orchestrator = orchestrator
        self.estimator_service = estimator_service

    async def run(
        self,
        command: RunThreeTierAnalysisCommand,
        analysis_id: str,
        tier1_result: Dict[str, Any] # Tier 1 results might be needed for context
    ) -> Dict[str, Any]:
        """Run Tier 2: Commodity-specific analysis."""
        await self.orchestrator.update_progress(
            analysis_id, "tier2", 0, "Starting commodity-specific analysis..."
        )
        
        commodities = command.commodity_ids or await self._get_all_commodities()
        results = {}
        
        for i, commodity in enumerate(commodities):
            progress = int((i / len(commodities)) * 100)
            await self.orchestrator.update_progress(
                analysis_id, "tier2", progress, f"Analyzing {commodity}..."
            )
            
            commodity_data = await self._load_commodity_data(command, commodity)
            
            if len(commodity_data) < command.tier2_config.get("min_obs", 100):
                results[commodity] = {
                    "error": "Insufficient data for analysis",
                    "status": "skipped"
                }
                continue
            
            # Create TVECM specification
            spec = ModelSpecification(
                model_type="threshold_vecm",
                dependent_variable=None,  # VECM uses multiple endogenous
                independent_variables=[],
                parameters={
                    "endogenous_vars": ["price_source", "price_target"],
                    "threshold_variable": command.tier2_config.get("threshold_variable"),
                    "n_regimes": command.tier2_config.get("n_regimes", 2),
                    **command.tier2_config
                }
            )
            
            model = ThresholdVECMModel(spec) # Or VECMModel based on config
            
            try:
                result = await self.estimator_service.estimate(model, commodity_data)
                
                diagnostics = None
                if command.run_diagnostics:
                    diagnostics = await self.estimator_service.diagnose(model, result)
                
                results[commodity] = {
                    "model": model.name,
                    "result": result,
                    "diagnostics": diagnostics,
                    "metadata": {
                        "n_observations": len(commodity_data),
                        "n_market_pairs": commodity_data["market_pair"].nunique() if "market_pair" in commodity_data.columns else 0
                    }
                }
                
            except Exception as e:
                results[commodity] = {
                    "error": str(e),
                    "status": "failed"
                }
        
        await self.orchestrator.update_progress(
            analysis_id, "tier2", 100, "Tier 2 complete"
        )
        
        return results

    async def _load_commodity_data(
        self,
        command: RunThreeTierAnalysisCommand,
        commodity: str
    ) -> pd.DataFrame:
        """Load commodity-specific time series data."""
        from ...infrastructure.logging import Logger
        logger = Logger(__name__)
        
        try:
            # Get all markets
            if command.market_ids:
                markets = await self.market_repo.find_by_ids(command.market_ids)
            else:
                markets = await self.market_repo.find_all()
            
            # Get price data for the commodity
            from ....core.domain.market.value_objects import Commodity as CommodityVO
            commodity_obj = await self.price_repo.find_commodity_by_code(commodity)
            
            if not commodity_obj:
                logger.warning(f"Commodity {commodity} not found, creating default")
                commodity_obj = CommodityVO(
                    code=commodity,
                    name=commodity.replace('_', ' ').title(),
                    category="food",
                    standard_unit="kg"
                )
            
            # Get price observations for all markets
            price_data = []
            for market in markets:
                observations = await self.price_repo.find_by_market_and_commodity(
                    market_id=market.market_id,
                    commodity=commodity_obj,
                    start_date=command.start_date,
                    end_date=command.end_date
                )
                
                for obs in observations:
                    price_data.append({
                        'date': obs.observed_date,
                        'market_id': str(market.market_id.value),
                        'market_name': market.name,
                        'price': obs.price.amount,
                        'latitude': market.coordinates.latitude,
                        'longitude': market.coordinates.longitude
                    })
            
            if not price_data:
                raise ValueError(f"No price data found for commodity {commodity}")
            
            df = pd.DataFrame(price_data)
            
            # Create market pairs for cointegration analysis
            market_pairs = []
            unique_markets = df['market_id'].unique()
            
            # Create pairs of geographically close markets
            for i, source_market in enumerate(unique_markets):
                source_data = df[df['market_id'] == source_market]
                source_lat = source_data['latitude'].iloc[0]
                source_lon = source_data['longitude'].iloc[0]
                
                for j, target_market in enumerate(unique_markets[i+1:], i+1):
                    target_data = df[df['market_id'] == target_market]
                    target_lat = target_data['latitude'].iloc[0]
                    target_lon = target_data['longitude'].iloc[0]
                    
                    # Calculate distance
                    distance = self._haversine_distance(
                        source_lat, source_lon, target_lat, target_lon
                    )
                    
                    # Only consider pairs within reasonable distance (e.g., 300km)
                    if distance <= 300:
                        # Merge price data for the pair
                        merged = pd.merge(
                            source_data[['date', 'price']],
                            target_data[['date', 'price']],
                            on='date',
                            suffixes=('_source', '_target')
                        )
                        
                        if len(merged) >= 30:  # Minimum observations for time series
                            merged['market_pair'] = f"{source_market}-{target_market}"
                            merged['distance_km'] = distance
                            
                            # Add conflict data if available
                            try:
                                from ....core.domain.conflict.repositories import ConflictEventRepository
                                conflict_repo = self.orchestrator.container.conflict_repository()
                                
                                # Get conflicts for both markets
                                source_market_obj = next(m for m in markets if str(m.market_id.value) == source_market)
                                conflicts = await conflict_repo.find_by_governorate_and_date_range(
                                    governorate=source_market_obj.governorate,
                                    start_date=command.start_date,
                                    end_date=command.end_date
                                )
                                
                                # Aggregate conflict intensity by date
                                conflict_by_date = {}
                                for conflict in conflicts:
                                    date_key = conflict.date.date()
                                    if date_key not in conflict_by_date:
                                        conflict_by_date[date_key] = 0
                                    conflict_by_date[date_key] += conflict.fatalities / 100.0
                                
                                merged['conflict_intensity'] = merged['date'].dt.date.map(
                                    lambda d: min(conflict_by_date.get(d, 0.0), 1.0)
                                )
                            except Exception as e:
                                logger.warning(f"Could not load conflict data: {e}")
                                merged['conflict_intensity'] = 0.0
                            
                            market_pairs.append(merged)
            
            if not market_pairs:
                raise ValueError(f"No valid market pairs found for commodity {commodity}")
            
            # Combine all market pairs
            result_df = pd.concat(market_pairs, ignore_index=True)
            result_df = result_df.set_index('date').sort_index()
            
            logger.info(
                f"Loaded commodity data for {commodity}: "
                f"{len(result_df)} observations, {len(market_pairs)} market pairs"
            )
            
            return result_df
            
        except Exception as e:
            logger.error(f"Error loading commodity data: {e}")
            raise
    
    def _haversine_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two points in kilometers."""
        from math import radians, sin, cos, sqrt, atan2
        
        R = 6371  # Earth's radius in kilometers
        
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * atan2(sqrt(a), sqrt(1-a))
        
        return R * c
    
    async def _get_all_commodities(self) -> List[str]:
        """Get list of all commodities."""
        from ...infrastructure.logging import Logger
        logger = Logger(__name__)
        
        try:
            # Query distinct commodities from price repository
            commodities = await self.price_repo.get_distinct_commodities()
            return [c.code for c in commodities]
        except Exception as e:
            logger.warning(f"Could not fetch commodities from repository: {e}")
            # Return default list if repository method not available
            return ["wheat_flour", "rice", "sugar", "fuel_diesel", "beans_kidney_red", "oil_vegetable"]
