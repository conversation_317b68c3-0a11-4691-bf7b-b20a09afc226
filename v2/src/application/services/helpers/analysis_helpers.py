"""Helper functions for analysis orchestration."""

from typing import Any, Dict, List, Optional # Added Optional
import pandas as pd
import numpy as np

from ....core.domain.market.repositories import MarketRepository, PriceRepository
from ....core.models.interfaces import ModelSpecification
from ....infrastructure.diagnostics import PanelDiagnosticTests, TimeSeriesDiagnosticTests
from ....infrastructure.estimators.standard_errors import StandardErrorEstimator


async def load_panel_data(
    command: Any, # Use Any to avoid circular dependency on RunThreeTierAnalysisCommand
    market_repo: MarketRepository,
    price_repo: PriceRepository
) -> pd.DataFrame:
    """Load and prepare panel data for analysis.
    
    This is a wrapper that delegates to the Tier1Runner implementation
    to avoid code duplication.
    """
    # Import here to avoid circular dependency
    from ...analysis_tiers.tier1_runner import Tier1Runner
    from ..analysis_orchestrator import AnalysisOrchestrator
    from ..model_estimator_service import ModelEstimatorService
    
    # Create minimal orchestrator and estimator for data loading
    # These are only needed to satisfy the constructor, not used for data loading
    orchestrator = type('MockOrchestrator', (), {'container': None})()
    estimator = ModelEstimatorService()
    
    # Create runner instance
    tier1_runner = Tier1Runner(
        market_repo=market_repo,
        price_repo=price_repo,
        orchestrator=orchestrator,
        estimator_service=estimator
    )
    
    # Delegate to the actual implementation
    return await tier1_runner._load_panel_data(command)


async def load_commodity_data(
    command: Any, # Use Any to avoid circular dependency
    commodity: str,
    market_repo: MarketRepository,
    price_repo: PriceRepository
) -> pd.DataFrame:
    """Load commodity-specific time series data.
    
    This is a wrapper that delegates to the Tier2Runner implementation
    to avoid code duplication.
    """
    # Import here to avoid circular dependency
    from ...analysis_tiers.tier2_runner import Tier2Runner
    from ..analysis_orchestrator import AnalysisOrchestrator
    from ..model_estimator_service import ModelEstimatorService
    
    # Create minimal orchestrator and estimator for data loading
    orchestrator = type('MockOrchestrator', (), {'container': None})()
    estimator = ModelEstimatorService()
    
    # Create runner instance
    tier2_runner = Tier2Runner(
        market_repo=market_repo,
        price_repo=price_repo,
        orchestrator=orchestrator,
        estimator_service=estimator
    )
    
    # Delegate to the actual implementation
    return await tier2_runner._load_commodity_data(command, commodity)


async def get_all_commodities(price_repo: PriceRepository) -> List[str]:
    """Get list of all commodities."""
    try:
        # Query distinct commodities from price repository
        commodities = await price_repo.get_distinct_commodities()
        return [c.code for c in commodities]
    except Exception:
        # Return default list if repository method not available
        return ["wheat_flour", "rice", "sugar", "fuel_diesel", "beans_kidney_red", "oil_vegetable"]


def get_tier1_variables(config: Dict[str, Any]) -> List[str]:
    """Get independent variables for Tier 1."""
    variables = ["conflict_intensity", "distance_to_port"]
    
    # Only include rainfall if available in config
    if config.get("include_weather", True):
        variables.append("rainfall")
    
    if config.get("interactions", False):
        variables.extend([
            "conflict_intensity_x_distance",
            "conflict_intensity_x_commodity"
        ])
    
    return variables


def needs_correction(diagnostics: Dict[str, Any]) -> bool:
    """Check if model needs correction based on diagnostics."""
    if diagnostics.get("serial_correlation", {}).get("reject_null", False):
        return True
    
    if diagnostics.get("cross_sectional_dependence", {}).get("reject_null", False):
        return True
    
    if diagnostics.get("heteroskedasticity", {}).get("reject_null", False):
        return True
    
    return False


def extract_residuals_for_factor_analysis(
    tier1_result: Dict[str, Any],
    tier2_results: Dict[str, Any]
) -> pd.DataFrame:
    """Extract and combine residuals from Tier 1 and Tier 2 for factor analysis.
    
    This is a wrapper that delegates to the Tier3Runner implementation
    to avoid code duplication.
    """
    # Import here to avoid circular dependency
    from ...analysis_tiers.tier3_runner import Tier3Runner
    
    # Create a minimal runner instance just for the extraction method
    tier3_runner = Tier3Runner(None, None, None, None)
    
    # Delegate to the actual implementation
    return tier3_runner._extract_residuals_for_factor_analysis(tier1_result, tier2_results)
