"""Three-Tier Analysis Service.

Orchestrates the complete three-tier econometric analysis using V1 adapters,
ensuring results match the validated 35% conflict effect findings.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple
from uuid import uuid4
import asyncio

from ...core.domain.market.entities import PanelData
from ...core.domain.shared.events import DomainEvent
from ...infrastructure.adapters.tier1_adapter import Tier1Adapter
from ...infrastructure.adapters.tier2_adapter import Tier2Adapter
from ...infrastructure.adapters.tier3_adapter import Tier3Adapter
from ...infrastructure.messaging import EventBus
from ...infrastructure.logging import Logger
from .analysis_orchestrator import AnalysisOrchestrator

logger = Logger(__name__)


class AnalysisStartedEvent(DomainEvent):
    """Event emitted when analysis starts."""
    
    def __init__(self, analysis_id: str, analysis_type: str, parameters: Dict[str, Any]):
        super().__init__()
        self.analysis_id = analysis_id
        self.analysis_type = analysis_type
        self.parameters = parameters


class AnalysisProgressEvent(DomainEvent):
    """Event emitted for analysis progress updates."""
    
    def __init__(self, analysis_id: str, tier: str, progress: int, message: str):
        super().__init__()
        self.analysis_id = analysis_id
        self.tier = tier
        self.progress = progress
        self.message = message


class AnalysisCompletedEvent(DomainEvent):
    """Event emitted when analysis completes."""
    
    def __init__(self, analysis_id: str, results: Dict[str, Any]):
        super().__init__()
        self.analysis_id = analysis_id
        self.results = results


class AnalysisFailedEvent(DomainEvent):
    """Event emitted when analysis fails."""
    
    def __init__(self, analysis_id: str, error: str):
        super().__init__()
        self.analysis_id = analysis_id
        self.error = error


class ThreeTierAnalysisService:
    """Orchestrate three-tier econometric analysis.
    
    This service coordinates the execution of:
    1. Tier 1: Pooled panel analysis with fixed effects
    2. Tier 2: Commodity-specific threshold models
    3. Tier 3: Validation through factor analysis
    
    It ensures results match V1 findings, particularly the 35% conflict effect.
    """
    
    def __init__(self,
                 tier1_adapter: Optional[Tier1Adapter] = None,
                 tier2_adapter: Optional[Tier2Adapter] = None,
                 tier3_adapter: Optional[Tier3Adapter] = None,
                 event_bus: Optional[EventBus] = None,
                 orchestrator: Optional[AnalysisOrchestrator] = None):
        """Initialize service with adapters and dependencies.
        
        Parameters
        ----------
        tier1_adapter : Tier1Adapter, optional
            Adapter for pooled panel models
        tier2_adapter : Tier2Adapter, optional
            Adapter for commodity-specific models
        tier3_adapter : Tier3Adapter, optional
            Adapter for validation models
        event_bus : EventBus, optional
            Event bus for publishing analysis events
        orchestrator : AnalysisOrchestrator, optional
            Orchestrator for managing analysis state
        """
        self.tier1_adapter = tier1_adapter or Tier1Adapter()
        self.tier2_adapter = tier2_adapter or Tier2Adapter()
        self.tier3_adapter = tier3_adapter or Tier3Adapter()
        self.event_bus = event_bus
        self.orchestrator = orchestrator
        
        # Analysis configuration
        self.config = {
            'validate_conflict_effect': True,
            'expected_conflict_effect': 0.35,
            'run_diagnostics': True,
            'apply_corrections': True,
            'parallel_commodity_analysis': False  # Can be enabled for performance
        }
        
        logger.info("Initialized ThreeTierAnalysisService")
    
    async def run_analysis(self,
                          panel_data: PanelData,
                          config: Optional[Dict[str, Any]] = None,
                          conflict_data: Optional[Any] = None) -> Dict[str, Any]:
        """Execute complete three-tier analysis.
        
        Parameters
        ----------
        panel_data : PanelData
            Panel data in V2 domain format
        config : dict, optional
            Analysis configuration overrides
        conflict_data : Any, optional
            Conflict event data for Tier 3 validation
            
        Returns
        -------
        dict
            Complete analysis results including all tiers
        """
        # Generate analysis ID
        analysis_id = str(uuid4())
        
        # Merge configuration
        analysis_config = {**self.config, **(config or {})}
        
        # Start analysis tracking
        if self.orchestrator:
            job_id = await self.orchestrator.start_analysis(
                'three_tier_analysis',
                {
                    'analysis_id': analysis_id,
                    'config': analysis_config,
                    'n_observations': len(panel_data.observations)
                }
            )
        else:
            job_id = analysis_id
        
        # Emit start event
        await self._emit_event(
            AnalysisStartedEvent(
                analysis_id=analysis_id,
                analysis_type='three_tier',
                parameters=analysis_config
            )
        )
        
        try:
            logger.info(f"Starting three-tier analysis {analysis_id}")
            
            # Run Tier 1
            await self._update_progress(job_id, 'tier1', 0, "Starting pooled panel analysis")
            tier1_results = await self._run_tier1(panel_data, analysis_config)
            await self._update_progress(job_id, 'tier1', 100, "Pooled panel analysis complete")
            
            # Run Tier 2
            await self._update_progress(job_id, 'tier2', 0, "Starting commodity-specific analysis")
            tier2_results = await self._run_tier2(panel_data, analysis_config)
            await self._update_progress(job_id, 'tier2', 100, "Commodity analysis complete")
            
            # Run Tier 3
            await self._update_progress(job_id, 'tier3', 0, "Starting validation analysis")
            tier3_results = await self._run_tier3(
                panel_data, 
                tier1_results, 
                tier2_results,
                conflict_data,
                analysis_config
            )
            await self._update_progress(job_id, 'tier3', 100, "Validation analysis complete")
            
            # Aggregate and validate results
            final_results = await self._aggregate_results(
                tier1_results,
                tier2_results,
                tier3_results,
                analysis_config
            )
            
            # Validate critical findings
            validation_passed = self._validate_critical_findings(final_results)
            
            if not validation_passed:
                logger.warning("Critical findings validation failed")
                final_results['validation_warning'] = (
                    "Results do not match expected V1 findings. "
                    "Review model configuration and data quality."
                )
            
            # Complete analysis
            if self.orchestrator:
                await self.orchestrator.complete_analysis(job_id, final_results)
            
            # Emit completion event
            await self._emit_event(
                AnalysisCompletedEvent(
                    analysis_id=analysis_id,
                    results=final_results
                )
            )
            
            logger.info(f"Three-tier analysis {analysis_id} completed successfully")
            
            return final_results
            
        except Exception as e:
            error_msg = f"Three-tier analysis failed: {str(e)}"
            logger.error(error_msg)
            
            # Update failure status
            if self.orchestrator:
                await self.orchestrator.fail_analysis(job_id, error_msg)
            
            # Emit failure event
            await self._emit_event(
                AnalysisFailedEvent(
                    analysis_id=analysis_id,
                    error=error_msg
                )
            )
            
            raise
    
    async def _run_tier1(self, panel_data: PanelData, 
                        config: Dict[str, Any]) -> Dict[str, Any]:
        """Run Tier 1 pooled panel analysis."""
        logger.info("Running Tier 1: Pooled panel analysis")
        
        try:
            # Prepare data
            tier1_data = await self.tier1_adapter.prepare_data(panel_data)
            
            # Run model
            tier1_raw = await self.tier1_adapter.run_model(
                tier1_data,
                dependent_var='log_price',
                regressors=['conflict_intensity', 'lag_price', 'global_price_index']
            )
            
            # Add data for diagnostics
            tier1_raw['data'] = tier1_data
            
            # Extract and validate results
            tier1_results = await self.tier1_adapter.extract_results(tier1_raw)
            
            # Apply corrections if needed
            if (config.get('apply_corrections') and 
                tier1_results.get('needs_correction')):
                
                logger.info("Applying econometric corrections to Tier 1")
                
                # Get suggested corrections
                corrections = tier1_results.get('suggested_corrections', {})
                
                # Update adapter configuration
                corrected_config = {**self.tier1_adapter.config, **corrections}
                self.tier1_adapter.config = corrected_config
                
                # Re-run with corrections
                tier1_raw_corrected = await self.tier1_adapter.run_model(
                    tier1_data,
                    dependent_var='log_price',
                    regressors=['conflict_intensity', 'lag_price', 'global_price_index']
                )
                
                tier1_raw_corrected['data'] = tier1_data
                tier1_results = await self.tier1_adapter.extract_results(tier1_raw_corrected)
                tier1_results['corrections_applied'] = corrections
            
            return tier1_results
            
        except Exception as e:
            logger.error(f"Tier 1 analysis failed: {str(e)}")
            raise
    
    async def _run_tier2(self, panel_data: PanelData,
                        config: Dict[str, Any]) -> Dict[str, Any]:
        """Run Tier 2 commodity-specific analysis."""
        logger.info("Running Tier 2: Commodity-specific analysis")
        
        try:
            # Get unique commodities
            commodities = list(set(obs.commodity.code for obs in panel_data.observations))
            logger.info(f"Analyzing {len(commodities)} commodities")
            
            if config.get('parallel_commodity_analysis'):
                # Run commodities in parallel
                tier2_results = await self.tier2_adapter.analyze_commodities_parallel(
                    panel_data,
                    commodities
                )
            else:
                # Sequential analysis
                # Prepare full data
                tier2_data = await self.tier2_adapter.prepare_data(panel_data)
                
                # Run model for all commodities
                tier2_raw = await self.tier2_adapter.run_model(
                    tier2_data,
                    test_threshold=config.get('threshold_test', True)
                )
                
                # Extract results
                tier2_results = await self.tier2_adapter.extract_results(tier2_raw)
            
            return tier2_results
            
        except Exception as e:
            logger.error(f"Tier 2 analysis failed: {str(e)}")
            raise
    
    async def _run_tier3(self, panel_data: PanelData,
                        tier1_results: Dict[str, Any],
                        tier2_results: Dict[str, Any],
                        conflict_data: Optional[Any],
                        config: Dict[str, Any]) -> Dict[str, Any]:
        """Run Tier 3 validation analysis."""
        logger.info("Running Tier 3: Validation analysis")
        
        try:
            # Prepare data with cross-tier information
            tier3_data = await self.tier3_adapter.prepare_data(
                panel_data,
                tier1_results,
                tier2_results
            )
            
            # Run all validation models
            tier3_raw = await self.tier3_adapter.run_model(
                tier3_data,
                model_type='all',
                conflict_data=conflict_data
            )
            
            # Extract results
            tier3_results = await self.tier3_adapter.extract_results(tier3_raw)
            
            # Add cross-tier validation if all tiers completed
            if config.get('cross_tier_validation', True):
                cross_validation = await self.tier3_adapter.cross_validate_tiers(
                    tier1_results,
                    tier2_results,
                    tier3_results
                )
                tier3_results['cross_tier_validation'] = cross_validation
            
            return tier3_results
            
        except Exception as e:
            logger.error(f"Tier 3 analysis failed: {str(e)}")
            raise
    
    async def _aggregate_results(self,
                               tier1_results: Dict[str, Any],
                               tier2_results: Dict[str, Any],
                               tier3_results: Dict[str, Any],
                               config: Dict[str, Any]) -> Dict[str, Any]:
        """Aggregate results from all tiers."""
        logger.info("Aggregating three-tier results")
        
        # Build aggregated results
        results = {
            'analysis_type': 'three_tier',
            'version': '2.0',
            'timestamp': datetime.utcnow().isoformat(),
            'config': config,
            'tiers': {
                'tier1': tier1_results,
                'tier2': tier2_results,
                'tier3': tier3_results
            },
            'summary': {}
        }
        
        # Generate executive summary
        summary = {
            'conflict_effect': tier1_results['conflict_effect'],
            'model_fit': tier1_results['model_fit'],
            'commodity_integration': tier2_results.get('summary', {}),
            'validation_results': tier3_results.get('validation_summary', {}),
            'key_findings': []
        }
        
        # Extract key findings
        # From Tier 1
        if tier1_results['conflict_effect']['is_valid']:
            summary['key_findings'].append(
                f"Conflict increases prices by {tier1_results['conflict_effect']['percentage_effect']:.1f}% "
                f"(p < {tier1_results['conflict_effect']['p_value']:.3f})"
            )
        
        # From Tier 2
        if 'summary' in tier2_results:
            high_integration = tier2_results['summary'].get('high_integration_commodities', [])
            if high_integration:
                summary['key_findings'].append(
                    f"High market integration found for: {', '.join(high_integration[:5])}"
                )
        
        # From Tier 3
        if 'validation_summary' in tier3_results:
            val_summary = tier3_results['validation_summary']
            if val_summary.get('integration_assessment'):
                summary['key_findings'].append(
                    f"Overall market integration level: {val_summary['integration_assessment']}"
                )
        
        results['summary'] = summary
        
        # Calculate confidence scores
        results['confidence_scores'] = self._calculate_confidence_scores(
            tier1_results,
            tier2_results,
            tier3_results
        )
        
        return results
    
    def _validate_critical_findings(self, results: Dict[str, Any]) -> bool:
        """Validate that results match expected V1 findings."""
        validations = []
        
        # 1. Validate conflict effect (35% ± 5%)
        conflict_effect = results['tiers']['tier1']['conflict_effect']
        validations.append(conflict_effect['is_valid'])
        
        # 2. Validate statistical significance
        validations.append(conflict_effect['p_value'] < 0.001)
        
        # 3. Validate model fit (R² > 0.6 expected)
        r_squared = results['tiers']['tier1']['model_fit']['r_squared']
        validations.append(r_squared > 0.6)
        
        # 4. Validate commodity results exist
        commodity_results = results['tiers']['tier2'].get('commodity_results', {})
        validations.append(len(commodity_results) > 0)
        
        # All critical validations must pass
        return all(validations)
    
    def _calculate_confidence_scores(self,
                                   tier1: Dict[str, Any],
                                   tier2: Dict[str, Any],
                                   tier3: Dict[str, Any]) -> Dict[str, float]:
        """Calculate confidence scores for results."""
        scores = {}
        
        # Tier 1 confidence
        tier1_conf = 1.0
        if tier1.get('diagnostics', {}).get('summary', {}).get('has_critical_failures'):
            tier1_conf *= 0.8
        if tier1['conflict_effect']['is_valid']:
            tier1_conf *= 1.0
        else:
            tier1_conf *= 0.5
        scores['tier1'] = tier1_conf
        
        # Tier 2 confidence
        if 'summary' in tier2:
            quality_pct = tier2['summary'].get('quality_threshold_pct', 0) / 100
            scores['tier2'] = quality_pct
        else:
            scores['tier2'] = 0.5
        
        # Tier 3 confidence
        models_completed = len(tier3.get('validation_summary', {}).get('models_completed', []))
        scores['tier3'] = min(models_completed / 3, 1.0)
        
        # Overall confidence
        scores['overall'] = sum(scores.values()) / len(scores)
        
        return scores
    
    async def _emit_event(self, event: DomainEvent) -> None:
        """Emit event if event bus is available."""
        if self.event_bus:
            await self.event_bus.publish(event)
    
    async def _update_progress(self, job_id: str, tier: str, 
                             progress: int, message: str) -> None:
        """Update analysis progress."""
        if self.orchestrator:
            await self.orchestrator.update_progress(job_id, tier, progress, message)
        
        # Also emit progress event
        await self._emit_event(
            AnalysisProgressEvent(
                analysis_id=job_id,
                tier=tier,
                progress=progress,
                message=message
            )
        )