"""Orchestrator for policy analysis workflows."""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
import pandas as pd

from ...core.models.policy.welfare_impact_model import (
    WelfareImpactModel, PolicyIntervention, WelfareImpact
)
from ...core.models.policy.early_warning_system import (
    EarlyWarningSystem, EarlyWarningAlert, CrisisIndicators
)
from ...core.models.interfaces import ModelSpecification
from ...infrastructure.logging import Logger
from ...infrastructure.messaging import EventBus
from .policy_data_adapter import PolicyDataAdapter
from .analysis_orchestrator import AnalysisOrchestrator

logger = Logger(__name__)


class PolicyOrchestrator:
    """Orchestrates policy model execution with real data."""
    
    def __init__(self,
                 data_adapter: PolicyDataAdapter,
                 analysis_orchestrator: AnalysisOrchestrator,
                 event_bus: EventBus):
        """Initialize policy orchestrator."""
        self.data_adapter = data_adapter
        self.analysis_orchestrator = analysis_orchestrator
        self.event_bus = event_bus
        self.welfare_model = None
        self.early_warning = None
    
    async def analyze_policy_intervention(self,
                                        intervention: PolicyIntervention,
                                        panel_data: pd.DataFrame,
                                        household_survey_path: Optional[str] = None) -> Dict[str, Any]:
        """Analyze welfare impact of a policy intervention.
        
        Args:
            intervention: Policy intervention specification
            panel_data: Panel data from analysis pipeline
            household_survey_path: Optional path to household survey
            
        Returns:
            Analysis results including welfare impacts
        """
        job_id = await self.analysis_orchestrator.start_analysis(
            "policy_welfare_analysis",
            {
                "intervention_type": intervention.type,
                "target_markets": intervention.target_markets,
                "target_commodities": intervention.target_commodities
            }
        )
        
        try:
            # Prepare data for welfare model
            await self.analysis_orchestrator.update_progress(
                job_id, "policy", 10, "Preparing welfare analysis data"
            )
            
            welfare_data = await self.data_adapter.prepare_welfare_impact_data(
                panel_data, household_survey_path
            )
            
            # Initialize welfare model
            await self.analysis_orchestrator.update_progress(
                job_id, "policy", 30, "Initializing welfare impact model"
            )
            
            model_spec = ModelSpecification(
                model_type="welfare_impact",
                parameters={
                    "confidence_level": 0.95,
                    "bootstrap_iterations": 100
                }
            )
            
            self.welfare_model = WelfareImpactModel(
                specification=model_spec,
                demand_elasticities=welfare_data['demand_elasticities'],
                supply_elasticities=welfare_data['supply_elasticities'],
                household_data=welfare_data['household_data']
            )
            
            # Run welfare analysis
            await self.analysis_orchestrator.update_progress(
                job_id, "policy", 50, "Estimating welfare impacts"
            )
            
            welfare_impact = self.welfare_model.estimate_intervention_impact(
                intervention=intervention,
                baseline_prices=welfare_data['baseline_prices'],
                market_integration=welfare_data['market_integration']
            )
            
            # Optimize intervention if requested
            await self.analysis_orchestrator.update_progress(
                job_id, "policy", 70, "Optimizing intervention parameters"
            )
            
            optimal_intervention = None
            if intervention.budget_constraint:
                optimal_intervention = self.welfare_model.optimize_intervention(
                    intervention_type=intervention.type,
                    objective='max_welfare',
                    constraints={'budget': intervention.budget_constraint.amount},
                    baseline_prices=welfare_data['baseline_prices']
                )
            
            # Prepare results
            await self.analysis_orchestrator.update_progress(
                job_id, "policy", 90, "Compiling results"
            )
            
            results = {
                'welfare_impact': self._serialize_welfare_impact(welfare_impact),
                'optimal_intervention': self._serialize_intervention(optimal_intervention) if optimal_intervention else None,
                'metadata': welfare_data['metadata'],
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
            
            # Emit event
            await self.event_bus.publish({
                'event_type': 'PolicyAnalysisCompleted',
                'job_id': job_id,
                'intervention_type': intervention.type,
                'net_welfare_change': welfare_impact.net_welfare_change.amount
            })
            
            await self.analysis_orchestrator.complete_analysis(job_id, results)
            
            return results
            
        except Exception as e:
            logger.error(f"Policy analysis failed: {str(e)}")
            await self.analysis_orchestrator.fail_analysis(job_id, str(e))
            raise
    
    async def generate_early_warnings(self,
                                    panel_data: pd.DataFrame,
                                    conflict_data: Optional[pd.DataFrame] = None,
                                    climate_data: Optional[pd.DataFrame] = None,
                                    forecast_horizon: int = 30) -> Dict[str, Any]:
        """Generate early warning alerts from current data.
        
        Args:
            panel_data: Current panel data
            conflict_data: Optional ACLED conflict data
            climate_data: Optional climate data
            forecast_horizon: Days to forecast ahead
            
        Returns:
            Early warning alerts and crisis indicators
        """
        job_id = await self.analysis_orchestrator.start_analysis(
            "early_warning_analysis",
            {
                "forecast_horizon": forecast_horizon,
                "has_conflict_data": conflict_data is not None,
                "has_climate_data": climate_data is not None
            }
        )
        
        try:
            # Prepare data for early warning
            await self.analysis_orchestrator.update_progress(
                job_id, "early_warning", 10, "Preparing early warning data"
            )
            
            ews_data = await self.data_adapter.prepare_early_warning_data(
                panel_data, conflict_data, climate_data
            )
            
            # Initialize or train early warning system
            await self.analysis_orchestrator.update_progress(
                job_id, "early_warning", 30, "Training early warning models"
            )
            
            model_spec = ModelSpecification(
                model_type="early_warning",
                parameters={
                    "anomaly_threshold": 0.1,
                    "forecast_confidence": 0.8
                }
            )
            
            if self.early_warning is None:
                self.early_warning = EarlyWarningSystem(
                    specification=model_spec,
                    historical_crises=None  # Would load historical crisis data
                )
                
                # Train on historical data
                self.early_warning.train(ews_data['historical_data'])
            
            # Generate alerts
            await self.analysis_orchestrator.update_progress(
                job_id, "early_warning", 50, "Detecting anomalies and generating alerts"
            )
            
            alerts = self.early_warning.generate_alerts(
                ews_data['current_data'],
                forecast_horizon
            )
            
            # Calculate crisis indicators
            await self.analysis_orchestrator.update_progress(
                job_id, "early_warning", 70, "Calculating crisis indicators"
            )
            
            indicators = self.early_warning.calculate_crisis_indicators(
                ews_data['current_data']
            )
            
            # Prepare response recommendations
            await self.analysis_orchestrator.update_progress(
                job_id, "early_warning", 90, "Generating response recommendations"
            )
            
            recommendations = self._generate_policy_recommendations(alerts, indicators)
            
            results = {
                'alerts': [self._serialize_alert(alert) for alert in alerts],
                'crisis_indicators': self._serialize_indicators(indicators),
                'recommendations': recommendations,
                'metadata': ews_data['metadata'],
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
            
            # Emit critical alerts
            for alert in alerts:
                if alert.alert_level.value >= 3:  # HIGH or CRITICAL
                    await self.event_bus.publish({
                        'event_type': 'CriticalAlertGenerated',
                        'alert_type': alert.alert_type,
                        'alert_level': alert.alert_level.value,
                        'affected_markets': alert.affected_markets,
                        'time_horizon': alert.time_horizon
                    })
            
            await self.analysis_orchestrator.complete_analysis(job_id, results)
            
            return results
            
        except Exception as e:
            logger.error(f"Early warning analysis failed: {str(e)}")
            await self.analysis_orchestrator.fail_analysis(job_id, str(e))
            raise
    
    async def run_policy_comparison(self,
                                  interventions: List[PolicyIntervention],
                                  panel_data: pd.DataFrame,
                                  household_survey_path: Optional[str] = None) -> Dict[str, Any]:
        """Compare multiple policy interventions.
        
        Args:
            interventions: List of interventions to compare
            panel_data: Panel data
            household_survey_path: Optional household survey
            
        Returns:
            Comparison results
        """
        job_id = await self.analysis_orchestrator.start_analysis(
            "policy_comparison",
            {
                "num_interventions": len(interventions),
                "intervention_types": [i.type for i in interventions]
            }
        )
        
        try:
            # Prepare data once for all interventions
            await self.analysis_orchestrator.update_progress(
                job_id, "comparison", 10, "Preparing comparison data"
            )
            
            welfare_data = await self.data_adapter.prepare_welfare_impact_data(
                panel_data, household_survey_path
            )
            
            # Initialize model
            model_spec = ModelSpecification(
                model_type="welfare_impact",
                parameters={"confidence_level": 0.95}
            )
            
            model = WelfareImpactModel(
                specification=model_spec,
                demand_elasticities=welfare_data['demand_elasticities'],
                supply_elasticities=welfare_data['supply_elasticities'],
                household_data=welfare_data['household_data']
            )
            
            # Analyze each intervention
            results = []
            for i, intervention in enumerate(interventions):
                progress = 20 + (60 * i / len(interventions))
                await self.analysis_orchestrator.update_progress(
                    job_id, "comparison", int(progress),
                    f"Analyzing intervention {i+1}: {intervention.type}"
                )
                
                impact = model.estimate_intervention_impact(
                    intervention=intervention,
                    baseline_prices=welfare_data['baseline_prices'],
                    market_integration=welfare_data['market_integration']
                )
                
                results.append({
                    'intervention': self._serialize_intervention(intervention),
                    'impact': self._serialize_welfare_impact(impact),
                    'cost_effectiveness': impact.net_welfare_change.amount / impact.government_cost.amount
                    if impact.government_cost.amount > 0 else float('inf')
                })
            
            # Rank interventions
            await self.analysis_orchestrator.update_progress(
                job_id, "comparison", 85, "Ranking interventions"
            )
            
            results.sort(key=lambda x: x['impact']['net_welfare_change'], reverse=True)
            
            comparison_results = {
                'interventions': results,
                'best_welfare': results[0] if results else None,
                'most_cost_effective': max(results, key=lambda x: x['cost_effectiveness']) if results else None,
                'metadata': welfare_data['metadata'],
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
            
            await self.analysis_orchestrator.complete_analysis(job_id, comparison_results)
            
            return comparison_results
            
        except Exception as e:
            logger.error(f"Policy comparison failed: {str(e)}")
            await self.analysis_orchestrator.fail_analysis(job_id, str(e))
            raise
    
    async def monitor_policy_impacts(self,
                                   active_interventions: List[Dict[str, Any]],
                                   panel_data: pd.DataFrame) -> Dict[str, Any]:
        """Monitor ongoing policy interventions.
        
        Args:
            active_interventions: List of active intervention specs
            panel_data: Current panel data
            
        Returns:
            Monitoring results
        """
        monitoring_results = []
        
        for intervention_data in active_interventions:
            # Extract intervention details
            intervention = PolicyIntervention(**intervention_data['specification'])
            start_date = pd.to_datetime(intervention_data['start_date'])
            
            # Get data since intervention start
            intervention_data_subset = panel_data[panel_data.index >= start_date]
            
            if len(intervention_data_subset) > 0:
                # Calculate actual vs expected impacts
                actual_metrics = self._calculate_actual_impacts(
                    intervention, intervention_data_subset
                )
                
                expected_metrics = intervention_data.get('expected_impacts', {})
                
                # Compare actual vs expected
                performance = {
                    'intervention_id': intervention_data['id'],
                    'intervention_type': intervention.type,
                    'days_active': (datetime.utcnow() - start_date).days,
                    'actual_metrics': actual_metrics,
                    'expected_metrics': expected_metrics,
                    'performance_ratio': self._calculate_performance_ratio(
                        actual_metrics, expected_metrics
                    ),
                    'recommendations': self._generate_monitoring_recommendations(
                        actual_metrics, expected_metrics, intervention
                    )
                }
                
                monitoring_results.append(performance)
        
        return {
            'active_interventions': len(active_interventions),
            'monitoring_results': monitoring_results,
            'overall_performance': self._calculate_overall_performance(monitoring_results),
            'timestamp': datetime.utcnow().isoformat()
        }
    
    def _serialize_welfare_impact(self, impact: WelfareImpact) -> Dict[str, Any]:
        """Serialize welfare impact for JSON response."""
        return {
            'consumer_surplus_change': impact.consumer_surplus_change.amount,
            'producer_surplus_change': impact.producer_surplus_change.amount,
            'government_cost': impact.government_cost.amount,
            'deadweight_loss': impact.deadweight_loss.amount,
            'net_welfare_change': impact.net_welfare_change.amount,
            'distributional_effects': impact.distributional_effects,
            'beneficiary_count': impact.beneficiary_count,
            'cost_per_beneficiary': impact.cost_per_beneficiary.amount,
            'currency': impact.net_welfare_change.currency
        }
    
    def _serialize_intervention(self, intervention: PolicyIntervention) -> Dict[str, Any]:
        """Serialize intervention for JSON response."""
        return {
            'type': intervention.type,
            'target_markets': intervention.target_markets,
            'target_commodities': intervention.target_commodities,
            'magnitude': intervention.magnitude,
            'duration_months': intervention.duration_months,
            'targeting_criteria': intervention.targeting_criteria,
            'budget_constraint': intervention.budget_constraint.amount if intervention.budget_constraint else None
        }
    
    def _serialize_alert(self, alert: EarlyWarningAlert) -> Dict[str, Any]:
        """Serialize alert for JSON response."""
        return {
            'alert_level': alert.alert_level.value,
            'alert_type': alert.alert_type,
            'affected_markets': alert.affected_markets,
            'affected_commodities': alert.affected_commodities,
            'time_horizon': alert.time_horizon,
            'probability': alert.probability,
            'recommended_actions': alert.recommended_actions,
            'supporting_evidence': alert.supporting_evidence
        }
    
    def _serialize_indicators(self, indicators: CrisisIndicators) -> Dict[str, Any]:
        """Serialize crisis indicators for JSON response."""
        return {
            'price_spike_probability': indicators.price_spike_probability,
            'supply_disruption_risk': indicators.supply_disruption_risk,
            'food_security_phase': indicators.food_security_phase,
            'market_functionality_index': indicators.market_functionality_index,
            'humanitarian_needs_projection': indicators.humanitarian_needs_projection,
            'confidence_interval': {
                'lower': indicators.confidence_interval[0],
                'upper': indicators.confidence_interval[1]
            }
        }
    
    def _generate_policy_recommendations(self,
                                       alerts: List[EarlyWarningAlert],
                                       indicators: CrisisIndicators) -> List[Dict[str, Any]]:
        """Generate policy recommendations based on alerts and indicators."""
        recommendations = []
        
        # High-level strategic recommendations based on IPC phase
        if indicators.food_security_phase >= 3:
            recommendations.append({
                'priority': 'CRITICAL',
                'type': 'humanitarian_response',
                'action': 'Scale up emergency food assistance',
                'rationale': f'IPC Phase {indicators.food_security_phase} indicates crisis conditions',
                'timeline': 'Immediate',
                'resources_needed': f'{indicators.humanitarian_needs_projection:,} people require assistance'
            })
        
        # Alert-specific recommendations
        for alert in alerts:
            if alert.alert_level.value >= 3:  # HIGH or CRITICAL
                rec = {
                    'priority': alert.alert_level.name,
                    'type': f'{alert.alert_type}_response',
                    'action': alert.recommended_actions[0] if alert.recommended_actions else 'Monitor situation',
                    'rationale': f'{alert.alert_type} detected with {alert.probability:.0%} probability',
                    'timeline': f'Within {alert.time_horizon} days',
                    'affected_areas': ', '.join(alert.affected_markets[:3]) + ('...' if len(alert.affected_markets) > 3 else '')
                }
                recommendations.append(rec)
        
        # Market functionality recommendations
        if indicators.market_functionality_index < 0.5:
            recommendations.append({
                'priority': 'HIGH',
                'type': 'market_support',
                'action': 'Implement market support programs',
                'rationale': f'Market functionality at {indicators.market_functionality_index:.0%}',
                'timeline': '1-2 weeks',
                'resources_needed': 'Technical assistance and capital injection'
            })
        
        return recommendations
    
    def _calculate_actual_impacts(self,
                                intervention: PolicyIntervention,
                                data: pd.DataFrame) -> Dict[str, float]:
        """Calculate actual impacts of an intervention."""
        metrics = {}
        
        # Price changes
        for commodity in intervention.target_commodities:
            price_col = f"{commodity}_price"
            if price_col in data.columns:
                price_change = data[price_col].pct_change().mean()
                metrics[f'{commodity}_price_change'] = price_change
        
        # Market integration (coefficient of variation)
        price_cols = [f"{c}_price" for c in intervention.target_commodities 
                     if f"{c}_price" in data.columns]
        if price_cols:
            cv = data[price_cols].std(axis=1) / data[price_cols].mean(axis=1)
            metrics['market_integration_cv'] = cv.mean()
        
        # Volatility reduction
        for commodity in intervention.target_commodities:
            price_col = f"{commodity}_price"
            if price_col in data.columns:
                volatility = data[price_col].pct_change().std()
                metrics[f'{commodity}_volatility'] = volatility
        
        return metrics
    
    def _calculate_performance_ratio(self,
                                   actual: Dict[str, float],
                                   expected: Dict[str, float]) -> float:
        """Calculate performance ratio (actual/expected)."""
        if not expected:
            return 1.0
        
        ratios = []
        for key, expected_value in expected.items():
            if key in actual and expected_value != 0:
                # For negative metrics (like price increase), invert ratio
                if 'price_change' in key and expected_value < 0:
                    ratio = expected_value / actual[key] if actual[key] != 0 else 1
                else:
                    ratio = actual[key] / expected_value
                ratios.append(max(0, min(2, ratio)))  # Cap at 0-2 range
        
        return sum(ratios) / len(ratios) if ratios else 1.0
    
    def _generate_monitoring_recommendations(self,
                                           actual: Dict[str, float],
                                           expected: Dict[str, float],
                                           intervention: PolicyIntervention) -> List[str]:
        """Generate recommendations based on monitoring results."""
        recommendations = []
        
        # Check if intervention is underperforming
        performance = self._calculate_performance_ratio(actual, expected)
        
        if performance < 0.8:
            recommendations.append("Consider increasing intervention magnitude or coverage")
            
            # Specific recommendations based on metrics
            for commodity in intervention.target_commodities:
                price_key = f'{commodity}_price_change'
                if price_key in actual and price_key in expected:
                    if actual[price_key] > expected[price_key] * 1.5:
                        recommendations.append(f"Price increases for {commodity} exceed expectations - review supply constraints")
        
        elif performance > 1.2:
            recommendations.append("Intervention performing above expectations - consider scaling up")
        
        # Check volatility
        high_volatility_commodities = []
        for commodity in intervention.target_commodities:
            vol_key = f'{commodity}_volatility'
            if vol_key in actual and actual[vol_key] > 0.1:  # 10% volatility threshold
                high_volatility_commodities.append(commodity)
        
        if high_volatility_commodities:
            recommendations.append(f"High volatility detected in: {', '.join(high_volatility_commodities)}")
        
        return recommendations
    
    def _calculate_overall_performance(self,
                                     monitoring_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate overall performance across all interventions."""
        if not monitoring_results:
            return {'status': 'No active interventions'}
        
        performance_ratios = [r['performance_ratio'] for r in monitoring_results]
        
        return {
            'average_performance': sum(performance_ratios) / len(performance_ratios),
            'underperforming_count': sum(1 for r in performance_ratios if r < 0.8),
            'overperforming_count': sum(1 for r in performance_ratios if r > 1.2),
            'on_track_count': sum(1 for r in performance_ratios if 0.8 <= r <= 1.2)
        }