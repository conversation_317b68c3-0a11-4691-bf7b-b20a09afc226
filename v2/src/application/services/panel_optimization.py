"""Panel optimization utilities for performance and memory efficiency."""

import logging
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, ProcessPoolExecutor
from functools import lru_cache
from typing import Any, Dict, List, Optional, Tuple
import gc

import numpy as np
import pandas as pd
from numba import jit, prange
import dask.dataframe as dd

logger = logging.getLogger(__name__)


class PanelOptimizer:
    """Optimization utilities for panel operations."""
    
    def __init__(self, n_workers: int = 4, chunk_size: int = 10000):
        """Initialize optimizer.
        
        Args:
            n_workers: Number of parallel workers
            chunk_size: Size of chunks for batch processing
        """
        self.n_workers = n_workers
        self.chunk_size = chunk_size
        self._thread_pool = ThreadPoolExecutor(max_workers=n_workers)
        self._process_pool = ProcessPoolExecutor(max_workers=n_workers)
    
    def optimize_dtypes(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimize DataFrame dtypes to reduce memory usage.
        
        Args:
            df: Input DataFrame
            
        Returns:
            DataFrame with optimized dtypes
        """
        logger.info(f"Optimizing dtypes for DataFrame with {len(df)} rows")
        
        # Get initial memory usage
        initial_memory = df.memory_usage(deep=True).sum() / 1024**2  # MB
        
        # Optimize each column
        for col in df.columns:
            col_type = df[col].dtype
            
            # Optimize numeric types
            if col_type != object:
                c_min = df[col].min()
                c_max = df[col].max()
                
                if str(col_type)[:3] == 'int':
                    if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                        df[col] = df[col].astype(np.int8)
                    elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                        df[col] = df[col].astype(np.int16)
                    elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                        df[col] = df[col].astype(np.int32)
                    elif c_min > np.iinfo(np.int64).min and c_max < np.iinfo(np.int64).max:
                        df[col] = df[col].astype(np.int64)
                else:
                    if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:
                        df[col] = df[col].astype(np.float16)
                    elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                        df[col] = df[col].astype(np.float32)
                    else:
                        df[col] = df[col].astype(np.float64)
            
            # Optimize object types
            else:
                # Check if can be converted to category
                num_unique_values = len(df[col].unique())
                num_total_values = len(df[col])
                
                if num_unique_values / num_total_values < 0.5:  # Less than 50% unique
                    df[col] = df[col].astype('category')
        
        # Get final memory usage
        final_memory = df.memory_usage(deep=True).sum() / 1024**2  # MB
        
        logger.info(
            f"Memory usage reduced from {initial_memory:.2f} MB to {final_memory:.2f} MB "
            f"({(1 - final_memory/initial_memory) * 100:.1f}% reduction)"
        )
        
        return df
    
    @staticmethod
    @jit(nopython=True, parallel=True)
    def fast_interpolate(values: np.ndarray, limit: int = 2) -> np.ndarray:
        """Fast linear interpolation with limit using Numba.
        
        Args:
            values: Array of values with NaNs
            limit: Maximum number of consecutive NaNs to fill
            
        Returns:
            Interpolated array
        """
        n = len(values)
        result = values.copy()
        
        for i in prange(n):
            if np.isnan(result[i]):
                # Find previous non-NaN value
                prev_idx = -1
                for j in range(i-1, -1, -1):
                    if not np.isnan(result[j]):
                        prev_idx = j
                        break
                
                # Find next non-NaN value
                next_idx = -1
                for j in range(i+1, n):
                    if not np.isnan(result[j]):
                        next_idx = j
                        break
                
                # Interpolate if within limit
                if prev_idx != -1 and next_idx != -1:
                    gap = next_idx - prev_idx - 1
                    if gap <= limit:
                        # Linear interpolation
                        alpha = (i - prev_idx) / (next_idx - prev_idx)
                        result[i] = result[prev_idx] * (1 - alpha) + result[next_idx] * alpha
        
        return result
    
    def parallel_group_operation(
        self,
        df: pd.DataFrame,
        group_cols: List[str],
        operation: callable,
        **kwargs
    ) -> pd.DataFrame:
        """Perform group operation in parallel.
        
        Args:
            df: Input DataFrame
            group_cols: Columns to group by
            operation: Operation to apply to each group
            **kwargs: Additional arguments for operation
            
        Returns:
            DataFrame with operation results
        """
        # Split into groups
        groups = [group for _, group in df.groupby(group_cols)]
        
        # Process in parallel
        def process_group(group):
            return operation(group, **kwargs)
        
        # Use thread pool for I/O bound operations
        results = list(self._thread_pool.map(process_group, groups))
        
        # Combine results
        return pd.concat(results, ignore_index=True)
    
    def chunked_merge(
        self,
        left: pd.DataFrame,
        right: pd.DataFrame,
        on: List[str],
        how: str = 'left'
    ) -> pd.DataFrame:
        """Perform memory-efficient merge in chunks.
        
        Args:
            left: Left DataFrame
            right: Right DataFrame
            on: Columns to merge on
            how: Merge type
            
        Returns:
            Merged DataFrame
        """
        # If small enough, do regular merge
        if len(left) * len(right) < 1e8:  # 100M comparisons
            return left.merge(right, on=on, how=how)
        
        logger.info(f"Performing chunked merge on {len(left)} x {len(right)} rows")
        
        # Sort both DataFrames by merge keys for efficiency
        left = left.sort_values(on)
        right = right.sort_values(on)
        
        # Process in chunks
        chunks = []
        for i in range(0, len(left), self.chunk_size):
            left_chunk = left.iloc[i:i+self.chunk_size]
            
            # Find relevant right rows for this chunk
            if on[0] in right.columns:
                min_key = left_chunk[on[0]].min()
                max_key = left_chunk[on[0]].max()
                right_chunk = right[
                    (right[on[0]] >= min_key) & (right[on[0]] <= max_key)
                ]
            else:
                right_chunk = right
            
            # Merge chunk
            chunk_result = left_chunk.merge(right_chunk, on=on, how=how)
            chunks.append(chunk_result)
            
            # Force garbage collection
            if i % (self.chunk_size * 10) == 0:
                gc.collect()
        
        # Combine chunks
        result = pd.concat(chunks, ignore_index=True)
        
        return result
    
    @lru_cache(maxsize=128)
    def cached_distance_matrix(
        self,
        coords_tuple: Tuple[Tuple[float, float], ...]
    ) -> np.ndarray:
        """Calculate distance matrix with caching.
        
        Args:
            coords_tuple: Tuple of coordinate tuples (for hashability)
            
        Returns:
            Distance matrix
        """
        coords = np.array(coords_tuple)
        n = len(coords)
        
        # Use broadcasting for efficient calculation
        lat1 = coords[:, 0][:, np.newaxis]
        lat2 = coords[:, 0][np.newaxis, :]
        lon1 = coords[:, 1][:, np.newaxis]
        lon2 = coords[:, 1][np.newaxis, :]
        
        # Haversine formula
        R = 6371  # Earth radius in km
        dlat = np.radians(lat2 - lat1)
        dlon = np.radians(lon2 - lon1)
        
        a = np.sin(dlat/2)**2 + np.cos(np.radians(lat1)) * np.cos(np.radians(lat2)) * np.sin(dlon/2)**2
        c = 2 * np.arcsin(np.sqrt(a))
        
        return R * c
    
    def use_dask_for_large_panels(
        self,
        df: pd.DataFrame,
        operation: callable,
        npartitions: Optional[int] = None
    ) -> pd.DataFrame:
        """Use Dask for very large panel operations.
        
        Args:
            df: Input DataFrame
            operation: Operation to apply
            npartitions: Number of Dask partitions
            
        Returns:
            Result DataFrame
        """
        if npartitions is None:
            npartitions = self.n_workers * 2
        
        logger.info(f"Converting to Dask DataFrame with {npartitions} partitions")
        
        # Convert to Dask DataFrame
        ddf = dd.from_pandas(df, npartitions=npartitions)
        
        # Apply operation
        result_ddf = operation(ddf)
        
        # Compute result
        result = result_ddf.compute()
        
        return result
    
    def batch_fillna(
        self,
        df: pd.DataFrame,
        strategy: Dict[str, Any]
    ) -> pd.DataFrame:
        """Efficiently fill missing values with different strategies.
        
        Args:
            df: Input DataFrame
            strategy: Dictionary mapping column patterns to fill strategies
            
        Returns:
            DataFrame with filled values
        """
        for pattern, method in strategy.items():
            cols = [col for col in df.columns if pattern in col]
            
            if method == 'interpolate':
                for col in cols:
                    if df[col].dtype in [np.float16, np.float32, np.float64]:
                        values = df[col].values
                        df[col] = self.fast_interpolate(values)
            
            elif method == 'forward_fill':
                df[cols] = df[cols].fillna(method='ffill')
            
            elif method == 'zero':
                df[cols] = df[cols].fillna(0)
            
            elif isinstance(method, (int, float)):
                df[cols] = df[cols].fillna(method)
        
        return df
    
    def __del__(self):
        """Cleanup pools on deletion."""
        self._thread_pool.shutdown(wait=False)
        self._process_pool.shutdown(wait=False)


class PanelCache:
    """LRU cache for panel operations."""
    
    def __init__(self, max_size: int = 100):
        """Initialize cache.
        
        Args:
            max_size: Maximum number of items to cache
        """
        self.cache = {}
        self.max_size = max_size
        self.access_order = []
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        if key in self.cache:
            # Update access order
            self.access_order.remove(key)
            self.access_order.append(key)
            return self.cache[key]
        return None
    
    def put(self, key: str, value: Any) -> None:
        """Put item in cache."""
        if key in self.cache:
            # Update existing
            self.access_order.remove(key)
            self.access_order.append(key)
        else:
            # Add new
            if len(self.cache) >= self.max_size:
                # Remove least recently used
                lru_key = self.access_order.pop(0)
                del self.cache[lru_key]
            
            self.cache[key] = value
            self.access_order.append(key)
    
    def clear(self) -> None:
        """Clear cache."""
        self.cache.clear()
        self.access_order.clear()