"""Data adapter for converting panel data to policy model formats."""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime

from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.domain.market.value_objects import MarketId, Commodity
from ...infrastructure.logging import Logger

logger = Logger(__name__)


class PolicyDataAdapter:
    """Adapts panel data for policy model consumption."""
    
    def __init__(self, 
                 market_repo: MarketRepository,
                 price_repo: PriceRepository):
        """Initialize adapter with repositories."""
        self.market_repo = market_repo
        self.price_repo = price_repo
    
    async def prepare_welfare_impact_data(self,
                                        panel_data: pd.DataFrame,
                                        household_survey_path: Optional[str] = None) -> Dict[str, Any]:
        """Prepare data for WelfareImpactModel.
        
        Args:
            panel_data: Panel data with prices by market/commodity/time
            household_survey_path: Path to Yemen household survey data
            
        Returns:
            Dictionary with prepared data for welfare analysis
        """
        logger.info("Preparing data for welfare impact analysis")
        
        # Extract baseline prices
        baseline_prices = self._extract_baseline_prices(panel_data)
        
        # Calculate demand and supply elasticities from panel data
        demand_elasticities = await self._estimate_demand_elasticities(panel_data)
        supply_elasticities = await self._estimate_supply_elasticities(panel_data)
        
        # Load and process household data
        if household_survey_path:
            household_data = await self._load_household_survey(household_survey_path)
        else:
            # Use synthetic data based on Yemen statistics
            household_data = await self._generate_household_data(panel_data)
        
        # Extract market integration parameters from panel analysis
        market_integration = await self._extract_market_integration_params(panel_data)
        
        return {
            'baseline_prices': baseline_prices,
            'demand_elasticities': demand_elasticities,
            'supply_elasticities': supply_elasticities,
            'household_data': household_data,
            'market_integration': market_integration,
            'metadata': {
                'start_date': panel_data.index.min(),
                'end_date': panel_data.index.max(),
                'markets': panel_data['market_id'].unique().tolist(),
                'commodities': [col for col in panel_data.columns if col.endswith('_price')]
            }
        }
    
    async def prepare_early_warning_data(self,
                                       panel_data: pd.DataFrame,
                                       conflict_data: Optional[pd.DataFrame] = None,
                                       climate_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """Prepare data for EarlyWarningSystem.
        
        Args:
            panel_data: Panel data with prices
            conflict_data: ACLED conflict event data
            climate_data: Rainfall and temperature data
            
        Returns:
            Dictionary with prepared data for early warning
        """
        logger.info("Preparing data for early warning system")
        
        # Ensure datetime index
        if not isinstance(panel_data.index, pd.DatetimeIndex):
            panel_data['date'] = pd.to_datetime(panel_data['date'])
            panel_data.set_index('date', inplace=True)
        
        # Price features
        price_features = self._extract_price_features(panel_data)
        
        # Market functionality indicators
        market_indicators = await self._calculate_market_indicators(panel_data)
        
        # Merge conflict data if available
        if conflict_data is not None:
            conflict_features = self._process_conflict_data(conflict_data, panel_data.index)
            price_features = price_features.merge(conflict_features, 
                                                left_index=True, 
                                                right_index=True, 
                                                how='left')
        
        # Merge climate data if available
        if climate_data is not None:
            climate_features = self._process_climate_data(climate_data, panel_data.index)
            price_features = price_features.merge(climate_features,
                                                left_index=True,
                                                right_index=True,
                                                how='left')
        
        # Add market indicators
        price_features = price_features.merge(market_indicators,
                                            left_index=True,
                                            right_index=True,
                                            how='left')
        
        # Calculate composite indicators
        price_features['food_basket_cost'] = self._calculate_food_basket_cost(panel_data)
        price_features['stock_levels'] = self._estimate_stock_levels(panel_data)
        
        # Handle missing values
        price_features.fillna(method='ffill', inplace=True)
        price_features.fillna(0, inplace=True)
        
        return {
            'current_data': price_features,
            'historical_data': price_features,
            'metadata': {
                'update_time': datetime.utcnow(),
                'data_quality_score': self._assess_data_quality(price_features),
                'coverage': {
                    'temporal': len(price_features),
                    'markets': panel_data['market_id'].nunique(),
                    'commodities': len([col for col in panel_data.columns if col.endswith('_price')])
                }
            }
        }
    
    def _extract_baseline_prices(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Extract baseline (most recent) prices by commodity."""
        price_cols = [col for col in panel_data.columns if col.endswith('_price')]
        
        # Get most recent non-null prices
        baseline = pd.DataFrame()
        for col in price_cols:
            commodity = col.replace('_price', '')
            # Get median of last 30 days as baseline
            recent_prices = panel_data[col].tail(30)
            baseline[commodity] = [recent_prices.median()]
        
        return baseline
    
    async def _estimate_demand_elasticities(self, panel_data: pd.DataFrame) -> Dict[str, float]:
        """Estimate price elasticities of demand from panel data."""
        elasticities = {}
        
        # Default elasticities based on commodity types (Yemen-specific)
        default_elasticities = {
            'WHEAT': -0.35,      # Staple, relatively inelastic
            'WHEAT_FLOUR': -0.35,
            'RICE': -0.40,       # Slightly more elastic than wheat
            'SUGAR': -0.50,      # More elastic
            'OIL': -0.45,
            'BEANS': -0.55,      # Protein sources
            'MEAT': -0.65,       # Luxury good, more elastic
            'FUEL': -0.25,       # Very inelastic
            'SALT': -0.15        # Extremely inelastic
        }
        
        price_cols = [col for col in panel_data.columns if col.endswith('_price')]
        
        for col in price_cols:
            commodity = col.replace('_price', '').upper()
            
            # Try to estimate from data if enough observations
            if len(panel_data) > 100:
                try:
                    # Simple log-log regression for elasticity
                    prices = panel_data[col].dropna()
                    if len(prices) > 50:
                        # Use price changes and proxy for quantity (inverse of price)
                        log_prices = np.log(prices)
                        price_changes = log_prices.diff().dropna()
                        
                        # Estimate elasticity from price variance
                        # Higher variance suggests more elastic demand
                        price_cv = prices.std() / prices.mean()
                        estimated_elasticity = -0.2 - (price_cv * 0.8)  # Scale to reasonable range
                        elasticities[commodity] = max(-1.0, min(-0.1, estimated_elasticity))
                    else:
                        elasticities[commodity] = default_elasticities.get(commodity, -0.5)
                except Exception as e:
                    logger.warning(f"Could not estimate elasticity for {commodity}: {e}")
                    elasticities[commodity] = default_elasticities.get(commodity, -0.5)
            else:
                elasticities[commodity] = default_elasticities.get(commodity, -0.5)
        
        return elasticities
    
    async def _estimate_supply_elasticities(self, panel_data: pd.DataFrame) -> Dict[str, float]:
        """Estimate price elasticities of supply."""
        elasticities = {}
        
        # Yemen-specific supply elasticities (mostly imported goods)
        default_elasticities = {
            'WHEAT': 0.20,       # Mostly imported, limited response
            'WHEAT_FLOUR': 0.25, # Some local milling
            'RICE': 0.15,        # Fully imported
            'SUGAR': 0.15,       # Fully imported
            'OIL': 0.20,         # Imported
            'BEANS': 0.30,       # Some local production
            'MEAT': 0.40,        # Local production possible
            'FUEL': 0.10,        # Very limited supply response
            'SALT': 0.35         # Can increase local production
        }
        
        price_cols = [col for col in panel_data.columns if col.endswith('_price')]
        
        for col in price_cols:
            commodity = col.replace('_price', '').upper()
            elasticities[commodity] = default_elasticities.get(commodity, 0.25)
        
        return elasticities
    
    async def _load_household_survey(self, survey_path: str) -> pd.DataFrame:
        """Load Yemen household survey data."""
        try:
            # Attempt to load actual survey data
            survey_data = pd.read_csv(survey_path)
            
            # Standardize column names
            required_cols = ['income', 'household_size', 'market_id']
            
            # Map survey columns to required format
            column_mapping = {
                'hh_income': 'income',
                'household_income': 'income',
                'hhsize': 'household_size',
                'hh_size': 'household_size',
                'market': 'market_id',
                'location': 'market_id'
            }
            
            survey_data.rename(columns=column_mapping, inplace=True)
            
            # Ensure required columns exist
            for col in required_cols:
                if col not in survey_data.columns:
                    logger.warning(f"Missing column {col} in survey data")
            
            return survey_data
            
        except Exception as e:
            logger.error(f"Could not load household survey: {e}")
            # Fall back to synthetic data
            return await self._generate_household_data(pd.DataFrame())
    
    async def _generate_household_data(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Generate synthetic household data based on Yemen statistics."""
        # Yemen household statistics (from various sources)
        avg_household_size = 6.7
        avg_income_usd_monthly = 150  # Pre-crisis levels
        
        # Get unique markets
        if 'market_id' in panel_data.columns:
            markets = panel_data['market_id'].unique()
        else:
            markets = ['Sanaa', 'Aden', 'Taiz', 'Hodeidah', 'Ibb']
        
        # Generate synthetic households
        n_households_per_market = 100
        households = []
        
        for market in markets:
            for i in range(n_households_per_market):
                # Income distribution (log-normal)
                income = np.random.lognormal(np.log(avg_income_usd_monthly * 1500), 0.8)
                
                # Household size (poisson)
                hh_size = max(1, np.random.poisson(avg_household_size - 1) + 1)
                
                # Consumption patterns (as % of income)
                food_share = 0.65 - (np.log(income) - np.log(avg_income_usd_monthly * 1500)) * 0.05
                food_share = max(0.4, min(0.8, food_share))
                
                household = {
                    'household_id': f"{market}_{i}",
                    'market_id': market,
                    'income': income,
                    'household_size': hh_size,
                    'food_expenditure': income * food_share,
                    'urban': 1 if market in ['Sanaa', 'Aden'] else 0
                }
                
                # Add commodity-specific consumption
                commodities = {
                    'WHEAT_consumption': hh_size * 15,  # kg/month
                    'RICE_consumption': hh_size * 8,
                    'OIL_consumption': hh_size * 1.5,
                    'SUGAR_consumption': hh_size * 2,
                    'BEANS_consumption': hh_size * 3
                }
                
                household.update(commodities)
                households.append(household)
        
        return pd.DataFrame(households)
    
    async def _extract_market_integration_params(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Extract market integration parameters from panel results."""
        # This would come from VECM/cointegration analysis
        # For now, create a simple correlation-based measure
        
        price_cols = [col for col in panel_data.columns if col.endswith('_price')]
        markets = panel_data['market_id'].unique() if 'market_id' in panel_data.columns else []
        
        integration_params = pd.DataFrame()
        
        if len(markets) > 1 and len(price_cols) > 0:
            # Calculate pairwise market correlations
            for commodity_col in price_cols:
                commodity = commodity_col.replace('_price', '')
                
                # Get price series by market
                market_prices = {}
                for market in markets:
                    market_data = panel_data[panel_data['market_id'] == market]
                    if len(market_data) > 30:
                        market_prices[market] = market_data[commodity_col]
                
                # Calculate integration scores
                if len(market_prices) > 1:
                    market_list = list(market_prices.keys())
                    for i, market1 in enumerate(market_list):
                        for market2 in market_list[i+1:]:
                            try:
                                # Simple correlation as integration measure
                                corr = market_prices[market1].corr(market_prices[market2])
                                integration_params.loc[f"{market1}-{market2}", commodity] = corr
                            except:
                                pass
        
        return integration_params
    
    def _extract_price_features(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Extract price features for early warning system."""
        features = pd.DataFrame(index=panel_data.index)
        
        price_cols = [col for col in panel_data.columns if col.endswith('_price')]
        
        for col in price_cols:
            commodity = col.replace('_price', '')
            
            # Price level
            features[f'{commodity}_price_level'] = panel_data[col]
            
            # Price volatility (30-day rolling std)
            features[f'{commodity}_price_volatility'] = panel_data[col].rolling(30).std()
            
            # Price momentum (7-day rate of change)
            features[f'{commodity}_price_momentum'] = panel_data[col].pct_change(7)
            
            # Price acceleration
            features[f'{commodity}_price_acceleration'] = features[f'{commodity}_price_momentum'].diff()
        
        return features
    
    async def _calculate_market_indicators(self, panel_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate market functionality indicators."""
        indicators = pd.DataFrame(index=panel_data.index)
        
        # Market integration score (based on price convergence)
        price_cols = [col for col in panel_data.columns if col.endswith('_price')]
        
        if len(price_cols) > 0 and 'market_id' in panel_data.columns:
            # Calculate coefficient of variation across markets
            cv_scores = []
            for date in panel_data.index.unique():
                date_data = panel_data.loc[date]
                if len(date_data) > 1:
                    # Average CV across commodities
                    cvs = []
                    for col in price_cols:
                        prices = date_data[col]
                        if prices.notna().sum() > 1:
                            cv = prices.std() / prices.mean() if prices.mean() > 0 else 0
                            cvs.append(cv)
                    
                    if cvs:
                        # Lower CV = better integration
                        integration_score = 1 - min(1, np.mean(cvs))
                        cv_scores.append((date, integration_score))
            
            if cv_scores:
                integration_df = pd.DataFrame(cv_scores, columns=['date', 'integration_score'])
                integration_df.set_index('date', inplace=True)
                indicators['market_integration'] = integration_df['integration_score']
        
        # Market functionality index (composite measure)
        indicators['market_functionality_index'] = 0.8  # Default
        
        # Adjust based on missing data (proxy for market disruption)
        missing_ratio = panel_data[price_cols].isna().sum(axis=1) / len(price_cols)
        indicators['market_functionality_index'] *= (1 - missing_ratio)
        
        # Number of accessible markets
        if 'market_id' in panel_data.columns:
            markets_reporting = panel_data.groupby(panel_data.index)['market_id'].nunique()
            indicators['markets_accessible'] = markets_reporting
        
        return indicators
    
    def _process_conflict_data(self, conflict_data: pd.DataFrame, 
                              date_index: pd.DatetimeIndex) -> pd.DataFrame:
        """Process ACLED conflict data to match panel dates."""
        features = pd.DataFrame(index=date_index)
        
        # Ensure conflict data has datetime index
        if 'event_date' in conflict_data.columns:
            conflict_data['date'] = pd.to_datetime(conflict_data['event_date'])
        elif 'date' in conflict_data.columns:
            conflict_data['date'] = pd.to_datetime(conflict_data['date'])
        
        # Count events per day
        daily_events = conflict_data.groupby(conflict_data['date'].dt.date).size()
        daily_events.index = pd.to_datetime(daily_events.index)
        
        # Align with panel dates
        features['conflict_events'] = daily_events.reindex(date_index, fill_value=0)
        
        # Add fatalities if available
        if 'fatalities' in conflict_data.columns:
            daily_fatalities = conflict_data.groupby(conflict_data['date'].dt.date)['fatalities'].sum()
            daily_fatalities.index = pd.to_datetime(daily_fatalities.index)
            features['conflict_fatalities'] = daily_fatalities.reindex(date_index, fill_value=0)
        
        return features
    
    def _process_climate_data(self, climate_data: pd.DataFrame,
                            date_index: pd.DatetimeIndex) -> pd.DataFrame:
        """Process climate data to match panel dates."""
        features = pd.DataFrame(index=date_index)
        
        # Align climate data with panel dates
        if 'rainfall' in climate_data.columns:
            features['rainfall'] = climate_data['rainfall'].reindex(date_index, method='ffill')
            
            # Calculate anomaly
            rolling_mean = features['rainfall'].rolling(365, min_periods=30).mean()
            rolling_std = features['rainfall'].rolling(365, min_periods=30).std()
            features['rainfall_anomaly'] = (features['rainfall'] - rolling_mean) / rolling_std
        
        if 'temperature' in climate_data.columns:
            features['temperature'] = climate_data['temperature'].reindex(date_index, method='ffill')
        
        return features
    
    def _calculate_food_basket_cost(self, panel_data: pd.DataFrame) -> pd.Series:
        """Calculate cost of minimum food basket."""
        # Yemen minimum food basket composition (monthly per person)
        basket_composition = {
            'WHEAT': 12,      # kg
            'RICE': 3,        # kg
            'OIL': 1.5,       # liters
            'SUGAR': 1.5,     # kg
            'BEANS': 2,       # kg
            'SALT': 0.5       # kg
        }
        
        basket_cost = pd.Series(index=panel_data.index, dtype=float)
        
        for date in panel_data.index.unique():
            date_data = panel_data.loc[date]
            
            total_cost = 0
            items_found = 0
            
            for commodity, quantity in basket_composition.items():
                price_col = f"{commodity}_price"
                if price_col in date_data.columns:
                    prices = date_data[price_col]
                    if prices.notna().any():
                        # Use median price across markets
                        median_price = prices.median()
                        total_cost += median_price * quantity
                        items_found += 1
            
            if items_found > 0:
                # Scale up for missing items
                basket_cost[date] = total_cost * (len(basket_composition) / items_found)
        
        return basket_cost
    
    def _estimate_stock_levels(self, panel_data: pd.DataFrame) -> pd.Series:
        """Estimate stock levels from price patterns."""
        # Use inverse of price volatility as proxy for stock levels
        # High volatility = low stocks
        
        price_cols = [col for col in panel_data.columns if col.endswith('_price')]
        
        if price_cols:
            # Calculate average volatility
            volatilities = []
            for col in price_cols:
                vol = panel_data[col].rolling(30).std() / panel_data[col].rolling(30).mean()
                volatilities.append(vol)
            
            avg_volatility = pd.concat(volatilities, axis=1).mean(axis=1)
            
            # Convert to stock level indicator (0-100)
            stock_levels = 100 * (1 - avg_volatility.clip(0, 1))
        else:
            stock_levels = pd.Series(50, index=panel_data.index)  # Default
        
        return stock_levels
    
    def _assess_data_quality(self, data: pd.DataFrame) -> float:
        """Assess quality of prepared data."""
        # Simple quality score based on completeness and validity
        
        # Completeness score
        completeness = 1 - (data.isna().sum().sum() / data.size)
        
        # Validity score (check for outliers)
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        outlier_ratio = 0
        
        for col in numeric_cols:
            if data[col].std() > 0:
                z_scores = np.abs((data[col] - data[col].mean()) / data[col].std())
                outlier_ratio += (z_scores > 3).sum() / len(data)
        
        validity = 1 - (outlier_ratio / len(numeric_cols)) if numeric_cols.any() else 1
        
        # Overall quality score
        quality_score = (completeness + validity) / 2
        
        return float(quality_score)