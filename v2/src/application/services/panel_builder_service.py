"""Panel Builder Service for V2 - Creates balanced panels with 88.4% coverage target.

This service provides comprehensive panel construction functionality equivalent to V1's
PanelBuilder with modern architecture, domain-driven design, and optimized performance.
"""

from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union
import logging

import numpy as np
import pandas as pd
from scipy.spatial.distance import cdist

from ...core.domain.market.entities import Market, PriceObservation
from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...core.domain.market.value_objects import MarketId, Commodity, Currency
from ...core.domain.conflict.entities import ConflictEvent
from ...core.domain.conflict.repositories import ConflictRepository
from ...core.domain.geography.entities import District, Governorate
from ...core.domain.geography.services import SpatialService
from ...infrastructure.persistence.unit_of_work import UnitOfWork
from ..interfaces import PanelBuilderInterface


logger = logging.getLogger(__name__)


@dataclass
class PanelCoverage:
    """Panel coverage statistics."""
    
    total_observations: int
    expected_observations: int
    coverage_percentage: float
    missing_observations: int
    markets_count: int
    commodities_count: int
    time_periods_count: int
    missing_by_market: Dict[str, int]
    missing_by_commodity: Dict[str, int]
    missing_by_period: Dict[str, int]


@dataclass
class PanelConfiguration:
    """Configuration for panel building."""
    
    start_date: datetime
    end_date: datetime
    frequency: str = "M"  # Monthly by default
    min_coverage_pct: float = 85.0
    min_markets_per_commodity: int = 20
    min_commodities_per_market: int = 15
    interpolation_limit: int = 2
    seasonal_adjustment: bool = True
    include_spatial_features: bool = True
    include_conflict_data: bool = True
    include_exchange_rates: bool = True


class PanelBuilderService(PanelBuilderInterface):
    """Service for building balanced panels with comprehensive features.
    
    This service creates Market × Commodity × Time panels optimized for
    econometric analysis with support for:
    - Balanced panel creation with coverage optimization
    - Missing data handling with multiple imputation strategies
    - Temporal features (lags, moving averages, seasonal dummies)
    - Spatial features (distances, neighbors, spatial lags)
    - Conflict integration
    - Exchange rate differentials
    - V1 compatibility validation
    """
    
    def __init__(
        self,
        unit_of_work: UnitOfWork,
        spatial_service: SpatialService,
        cache_enabled: bool = True
    ):
        """Initialize panel builder service.
        
        Args:
            unit_of_work: Unit of work for repository access
            spatial_service: Service for spatial calculations
            cache_enabled: Whether to enable caching for performance
        """
        self.uow = unit_of_work
        self.spatial_service = spatial_service
        self._cache = {} if cache_enabled else None
        
    async def create_balanced_panel(
        self,
        config: PanelConfiguration,
        commodities: Optional[List[str]] = None,
        markets: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Create a perfectly balanced panel dataset.
        
        Args:
            config: Panel configuration parameters
            commodities: List of commodities to include (None for auto-selection)
            markets: List of markets to include (None for auto-selection)
            
        Returns:
            Balanced panel DataFrame with Market × Commodity × Time structure
        """
        logger.info(f"Creating balanced panel from {config.start_date} to {config.end_date}")
        
        async with self.uow:
            # Step 1: Select core entities if not provided
            if commodities is None:
                commodities = await self._select_core_commodities(config)
            if markets is None:
                markets = await self._select_core_markets(config, commodities)
            
            # Step 2: Create panel structure
            panel_index = self._create_panel_index(markets, commodities, config)
            
            # Step 3: Populate with price data
            panel = await self._populate_price_data(panel_index, config)
            
            # Step 4: Add geographic and administrative data
            panel = await self._add_geographic_features(panel)
            
            # Step 5: Handle missing data
            panel = self._handle_missing_data(panel, config)
            
            # Step 6: Add temporal features
            panel = self._add_temporal_features(panel, config)
            
            # Step 7: Add spatial features if requested
            if config.include_spatial_features:
                panel = await self._add_spatial_features(panel)
            
            # Step 8: Add conflict data if requested
            if config.include_conflict_data:
                panel = await self._integrate_conflict_data(panel, config)
            
            # Step 9: Add exchange rates if requested
            if config.include_exchange_rates:
                panel = await self._integrate_exchange_rates(panel, config)
            
            # Step 10: Calculate and report coverage
            coverage = self._calculate_coverage(panel)
            logger.info(f"Panel coverage: {coverage.coverage_percentage:.1f}%")
            
            if coverage.coverage_percentage < config.min_coverage_pct:
                logger.warning(
                    f"Coverage {coverage.coverage_percentage:.1f}% below target "
                    f"{config.min_coverage_pct}%"
                )
            
            return panel
    
    async def _select_core_commodities(
        self,
        config: PanelConfiguration
    ) -> List[str]:
        """Select commodities with sufficient data coverage.
        
        Args:
            config: Panel configuration
            
        Returns:
            List of selected commodity names
        """
        async with self.uow:
            # Get commodity statistics
            price_repo = self.uow.get_repository(PriceRepository)
            
            # Query commodity coverage
            commodity_stats = await price_repo.get_commodity_coverage(
                start_date=config.start_date,
                end_date=config.end_date
            )
            
            # Filter by coverage and market count
            selected = []
            for commodity, stats in commodity_stats.items():
                if (stats['coverage_pct'] >= config.min_coverage_pct and
                    stats['n_markets'] >= config.min_markets_per_commodity):
                    selected.append(commodity)
            
            logger.info(f"Selected {len(selected)} core commodities")
            return selected
    
    async def _select_core_markets(
        self,
        config: PanelConfiguration,
        commodities: List[str]
    ) -> List[str]:
        """Select markets with sufficient commodity coverage.
        
        Args:
            config: Panel configuration
            commodities: List of commodities to check
            
        Returns:
            List of selected market IDs
        """
        async with self.uow:
            # Get market statistics
            price_repo = self.uow.get_repository(PriceRepository)
            
            # Query market coverage for selected commodities
            market_stats = await price_repo.get_market_coverage(
                commodities=commodities,
                start_date=config.start_date,
                end_date=config.end_date
            )
            
            # Filter by coverage and commodity count
            selected = []
            for market_id, stats in market_stats.items():
                if (stats['coverage_pct'] >= config.min_coverage_pct and
                    stats['n_commodities'] >= config.min_commodities_per_market):
                    selected.append(market_id)
            
            logger.info(f"Selected {len(selected)} core markets")
            return selected
    
    def _create_panel_index(
        self,
        markets: List[str],
        commodities: List[str],
        config: PanelConfiguration
    ) -> pd.DataFrame:
        """Create the panel index structure.
        
        Args:
            markets: List of market IDs
            commodities: List of commodity names
            config: Panel configuration
            
        Returns:
            DataFrame with panel index
        """
        # Create date range based on frequency
        if config.frequency == 'M':
            # Monthly on 15th (WFP standard)
            dates = pd.date_range(
                start=config.start_date.replace(day=15),
                end=config.end_date,
                freq='MS'
            )
            dates = dates + pd.Timedelta(days=14)  # Shift to 15th
        elif config.frequency == 'W':
            dates = pd.date_range(
                start=config.start_date,
                end=config.end_date,
                freq='W'
            )
        else:
            dates = pd.date_range(
                start=config.start_date,
                end=config.end_date,
                freq=config.frequency
            )
        
        # Create multi-index
        from itertools import product
        index_data = list(product(markets, commodities, dates))
        
        panel_index = pd.DataFrame(
            index_data,
            columns=['market_id', 'commodity', 'date']
        )
        
        # Add time dimensions
        panel_index['year'] = panel_index['date'].dt.year
        panel_index['month'] = panel_index['date'].dt.month
        panel_index['year_month'] = panel_index['date'].dt.to_period('M')
        
        return panel_index
    
    async def _populate_price_data(
        self,
        panel_index: pd.DataFrame,
        config: PanelConfiguration
    ) -> pd.DataFrame:
        """Populate panel with price observations.
        
        Args:
            panel_index: Panel index structure
            config: Panel configuration
            
        Returns:
            Panel with price data
        """
        async with self.uow:
            price_repo = self.uow.get_repository(PriceRepository)
            
            # Get all prices for the period
            prices = await price_repo.get_prices_for_period(
                start_date=config.start_date,
                end_date=config.end_date,
                market_ids=panel_index['market_id'].unique().tolist(),
                commodities=panel_index['commodity'].unique().tolist()
            )
            
            # Convert to DataFrame
            price_data = []
            for price in prices:
                price_data.append({
                    'market_id': price.market_id.value,
                    'commodity': price.commodity.name,
                    'date': price.observed_date,
                    'price': float(price.price.amount),
                    'currency': price.price.currency.value,
                    'unit': price.price.unit,
                    'source': price.source,
                    'quality': price.quality
                })
            
            price_df = pd.DataFrame(price_data)
            
            # Merge with panel index
            panel = panel_index.merge(
                price_df,
                on=['market_id', 'commodity', 'date'],
                how='left'
            )
            
            # Add USD prices if YER prices available
            if 'currency' in panel.columns:
                panel['price_usd'] = panel.apply(
                    lambda row: row['price'] if row['currency'] == 'USD' else np.nan,
                    axis=1
                )
                panel['price_yer'] = panel.apply(
                    lambda row: row['price'] if row['currency'] == 'YER' else np.nan,
                    axis=1
                )
            
            return panel
    
    async def _add_geographic_features(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Add geographic and administrative features.
        
        Args:
            panel: Panel DataFrame
            
        Returns:
            Panel with geographic features
        """
        async with self.uow:
            market_repo = self.uow.get_repository(MarketRepository)
            
            # Get market details
            market_ids = panel['market_id'].unique()
            markets = await market_repo.get_by_ids(
                [MarketId(mid) for mid in market_ids]
            )
            
            # Create market info DataFrame
            market_info = []
            for market in markets:
                market_info.append({
                    'market_id': market.market_id.value,
                    'market_name': market.name,
                    'governorate': market.governorate,
                    'district': market.district,
                    'latitude': market.coordinates.latitude if market.coordinates else None,
                    'longitude': market.coordinates.longitude if market.coordinates else None,
                    'market_type': market.market_type.value
                })
            
            market_df = pd.DataFrame(market_info)
            
            # Merge with panel
            panel = panel.merge(market_df, on='market_id', how='left')
            
            # Calculate distance to capital (Sana'a)
            if 'latitude' in panel.columns and 'longitude' in panel.columns:
                sana_lat, sana_lon = 15.3694, 44.1910
                panel['distance_to_capital_km'] = self._calculate_haversine_distance(
                    panel['latitude'].values,
                    panel['longitude'].values,
                    sana_lat,
                    sana_lon
                )
            
            # Add port access indicator
            port_markets = ['ADEN_ADEN_CITY', 'HADHRAMAUT_MUKALLA_CITY', 'AL_HUDAYDAH_AL_HUDAYDAH_CITY']
            panel['has_port_access'] = panel['market_id'].isin(port_markets).astype(int)
            
            return panel
    
    def _calculate_haversine_distance(
        self,
        lat1: np.ndarray,
        lon1: np.ndarray,
        lat2: float,
        lon2: float
    ) -> np.ndarray:
        """Calculate Haversine distance between coordinates.
        
        Args:
            lat1: Array of latitudes
            lon1: Array of longitudes
            lat2: Target latitude
            lon2: Target longitude
            
        Returns:
            Array of distances in kilometers
        """
        R = 6371  # Earth radius in km
        
        lat1_rad = np.radians(lat1)
        lat2_rad = np.radians(lat2)
        lon1_rad = np.radians(lon1)
        lon2_rad = np.radians(lon2)
        
        dlat = lat2_rad - lat1_rad
        dlon = lon2_rad - lon1_rad
        
        a = np.sin(dlat/2)**2 + np.cos(lat1_rad) * np.cos(lat2_rad) * np.sin(dlon/2)**2
        c = 2 * np.arcsin(np.sqrt(a))
        
        return R * c
    
    def _handle_missing_data(
        self,
        panel: pd.DataFrame,
        config: PanelConfiguration
    ) -> pd.DataFrame:
        """Handle missing data with multiple strategies.
        
        Args:
            panel: Panel DataFrame
            config: Panel configuration
            
        Returns:
            Panel with handled missing data
        """
        # Track missing data before
        missing_before = panel.isnull().sum()
        
        # Sort for proper interpolation
        panel = panel.sort_values(['market_id', 'commodity', 'date'])
        
        # Strategy 1: Linear interpolation for prices
        if 'price' in panel.columns:
            panel['price'] = panel.groupby(['market_id', 'commodity'])['price'].transform(
                lambda x: x.interpolate(method='linear', limit=config.interpolation_limit)
            )
        
        if 'price_usd' in panel.columns:
            panel['price_usd'] = panel.groupby(['market_id', 'commodity'])['price_usd'].transform(
                lambda x: x.interpolate(method='linear', limit=config.interpolation_limit)
            )
        
        # Strategy 2: Forward fill for time-invariant features
        time_invariant = ['market_name', 'governorate', 'district', 'latitude', 'longitude', 'market_type']
        for col in time_invariant:
            if col in panel.columns:
                panel[col] = panel.groupby('market_id')[col].transform(
                    lambda x: x.ffill().bfill()
                )
        
        # Strategy 3: Seasonal adjustment for remaining gaps
        if config.seasonal_adjustment and 'price' in panel.columns:
            # Calculate seasonal averages
            seasonal_avg = panel.groupby(['commodity', 'month'])['price'].transform('mean')
            market_adjustment = panel.groupby(['market_id', 'commodity'])['price'].transform('mean') / \
                               panel.groupby('commodity')['price'].transform('mean')
            
            # Fill with seasonally adjusted values
            mask = panel['price'].isna()
            panel.loc[mask, 'price'] = seasonal_avg[mask] * market_adjustment[mask]
        
        # Report missing data reduction
        missing_after = panel.isnull().sum()
        for col in missing_before.index:
            if missing_before[col] > 0:
                reduction = missing_before[col] - missing_after[col]
                if reduction > 0:
                    logger.info(
                        f"Missing data reduced for {col}: "
                        f"{missing_before[col]} -> {missing_after[col]} "
                        f"(-{reduction})"
                    )
        
        return panel
    
    def _add_temporal_features(
        self,
        panel: pd.DataFrame,
        config: PanelConfiguration
    ) -> pd.DataFrame:
        """Add temporal features for time series analysis.
        
        Args:
            panel: Panel DataFrame
            config: Panel configuration
            
        Returns:
            Panel with temporal features
        """
        # Sort for proper lagging
        panel = panel.sort_values(['market_id', 'commodity', 'date'])
        
        # Create lags for key variables
        lag_vars = ['price', 'price_usd'] if 'price_usd' in panel.columns else ['price']
        
        for var in lag_vars:
            if var in panel.columns:
                # Add lags
                for lag in [1, 2, 3]:
                    panel[f'{var}_lag{lag}'] = panel.groupby(['market_id', 'commodity'])[var].shift(lag)
                
                # Add differences
                panel[f'{var}_diff'] = panel.groupby(['market_id', 'commodity'])[var].diff()
                panel[f'{var}_pct_change'] = panel.groupby(['market_id', 'commodity'])[var].pct_change()
                
                # Add moving averages
                panel[f'{var}_ma3'] = panel.groupby(['market_id', 'commodity'])[var].transform(
                    lambda x: x.rolling(window=3, min_periods=1).mean()
                )
                panel[f'{var}_ma6'] = panel.groupby(['market_id', 'commodity'])[var].transform(
                    lambda x: x.rolling(window=6, min_periods=1).mean()
                )
                
                # Add volatility (rolling std)
                panel[f'{var}_volatility'] = panel.groupby(['market_id', 'commodity'])[var].transform(
                    lambda x: x.rolling(window=3, min_periods=2).std()
                )
        
        # Add time trend
        panel['time_trend'] = panel.groupby(['market_id', 'commodity']).cumcount() + 1
        panel['time_trend_sq'] = panel['time_trend'] ** 2
        
        # Add seasonal indicators
        panel['quarter'] = panel['date'].dt.quarter
        panel['is_ramadan'] = panel['month'].isin([3, 4, 5]).astype(int)  # Approximate
        
        # Add month dummies
        for month in range(1, 13):
            panel[f'month_{month}'] = (panel['month'] == month).astype(int)
        
        return panel
    
    async def _add_spatial_features(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Add spatial features and relationships.
        
        Args:
            panel: Panel DataFrame
            
        Returns:
            Panel with spatial features
        """
        if 'latitude' not in panel.columns or 'longitude' not in panel.columns:
            logger.warning("No coordinates available for spatial features")
            return panel
        
        # Get unique markets with coordinates
        markets = panel[['market_id', 'latitude', 'longitude']].drop_duplicates()
        markets = markets.dropna(subset=['latitude', 'longitude'])
        
        if len(markets) < 2:
            return panel
        
        # Calculate distance matrix
        coords = markets[['latitude', 'longitude']].values
        distance_matrix = cdist(coords, coords, metric='euclidean') * 111  # Convert to km
        
        # Create distance DataFrame
        distance_df = pd.DataFrame(
            distance_matrix,
            index=markets['market_id'],
            columns=markets['market_id']
        )
        
        # Add nearest neighbor features
        for idx, market_id in enumerate(markets['market_id']):
            # Get distances for this market
            distances = distance_matrix[idx]
            
            # Find k nearest neighbors (excluding self)
            k = min(5, len(markets) - 1)
            nearest_indices = np.argpartition(distances, k+1)[:k+1]
            nearest_indices = nearest_indices[nearest_indices != idx][:k]
            
            # Add to panel
            mask = panel['market_id'] == market_id
            panel.loc[mask, 'nearest_market_distance'] = distances[nearest_indices[0]]
            panel.loc[mask, 'avg_distance_5_nearest'] = np.mean(distances[nearest_indices])
        
        # Add spatial lag of prices (average price in nearby markets)
        if 'price' in panel.columns:
            # For each market and time period, calculate spatial lag
            for date in panel['date'].unique():
                date_mask = panel['date'] == date
                
                for commodity in panel['commodity'].unique():
                    commodity_mask = panel['commodity'] == commodity
                    
                    for market_id in markets['market_id']:
                        market_mask = panel['market_id'] == market_id
                        combined_mask = date_mask & commodity_mask & market_mask
                        
                        if combined_mask.any():
                            # Get nearby markets (within 100km)
                            nearby_markets = distance_df[market_id][distance_df[market_id] < 100].index
                            nearby_markets = nearby_markets[nearby_markets != market_id]
                            
                            if len(nearby_markets) > 0:
                                # Calculate average price in nearby markets
                                nearby_mask = date_mask & commodity_mask & panel['market_id'].isin(nearby_markets)
                                if nearby_mask.any():
                                    spatial_lag = panel.loc[nearby_mask, 'price'].mean()
                                    panel.loc[combined_mask, 'price_spatial_lag'] = spatial_lag
        
        return panel
    
    async def _integrate_conflict_data(
        self,
        panel: pd.DataFrame,
        config: PanelConfiguration
    ) -> pd.DataFrame:
        """Integrate conflict data into panel.
        
        Args:
            panel: Panel DataFrame
            config: Panel configuration
            
        Returns:
            Panel with conflict data
        """
        async with self.uow:
            conflict_repo = self.uow.get_repository(ConflictRepository)
            
            # Get conflict events for the period
            conflicts = await conflict_repo.get_events_for_period(
                start_date=config.start_date,
                end_date=config.end_date
            )
            
            # Aggregate by market and month
            conflict_data = []
            for market_id in panel['market_id'].unique():
                market_coords = panel[panel['market_id'] == market_id][['latitude', 'longitude']].iloc[0]
                
                if pd.notna(market_coords['latitude']):
                    # Calculate conflict metrics for each month
                    for year_month in panel['year_month'].unique():
                        month_conflicts = [
                            c for c in conflicts
                            if c.event_date.strftime('%Y-%m') == str(year_month)
                        ]
                        
                        # Calculate metrics
                        events_total = len(month_conflicts)
                        fatalities_total = sum(c.fatalities for c in month_conflicts)
                        
                        # Count by type
                        events_battles = sum(1 for c in month_conflicts if c.event_type == 'battles')
                        events_explosions = sum(1 for c in month_conflicts if c.event_type == 'explosions')
                        events_against_civilians = sum(
                            1 for c in month_conflicts 
                            if c.event_type == 'violence_against_civilians'
                        )
                        
                        # Calculate average distance
                        distances = []
                        for conflict in month_conflicts:
                            if conflict.coordinates:
                                dist = self._calculate_haversine_distance(
                                    np.array([market_coords['latitude']]),
                                    np.array([market_coords['longitude']]),
                                    conflict.coordinates.latitude,
                                    conflict.coordinates.longitude
                                )[0]
                                distances.append(dist)
                        
                        avg_distance = np.mean(distances) if distances else None
                        
                        conflict_data.append({
                            'market_id': market_id,
                            'year_month': year_month,
                            'events_total': events_total,
                            'events_battles': events_battles,
                            'events_explosions': events_explosions,
                            'events_against_civilians': events_against_civilians,
                            'fatalities_total': fatalities_total,
                            'avg_distance_km': avg_distance
                        })
            
            conflict_df = pd.DataFrame(conflict_data)
            
            # Merge with panel
            panel = panel.merge(
                conflict_df,
                on=['market_id', 'year_month'],
                how='left'
            )
            
            # Fill missing conflict data with zeros
            conflict_cols = [
                'events_total', 'events_battles', 'events_explosions',
                'events_against_civilians', 'fatalities_total'
            ]
            for col in conflict_cols:
                if col in panel.columns:
                    panel[col] = panel[col].fillna(0)
            
            # Add conflict intensity measure
            if 'events_total' in panel.columns:
                # Normalized conflict intensity
                panel['conflict_intensity'] = panel.groupby('market_id')['events_total'].transform(
                    lambda x: (x - x.mean()) / (x.std() + 1e-8)
                )
                
                # Add conflict lags and moving averages
                panel['conflict_intensity_lag1'] = panel.groupby('market_id')['conflict_intensity'].shift(1)
                panel['conflict_intensity_lag2'] = panel.groupby('market_id')['conflict_intensity'].shift(2)
                panel['conflict_intensity_lag3'] = panel.groupby('market_id')['conflict_intensity'].shift(3)
                
                panel['conflict_ma3'] = panel.groupby('market_id')['conflict_intensity'].transform(
                    lambda x: x.rolling(window=3, min_periods=1).mean()
                )
            
            return panel
    
    async def _integrate_exchange_rates(
        self,
        panel: pd.DataFrame,
        config: PanelConfiguration
    ) -> pd.DataFrame:
        """Integrate exchange rate data into panel.
        
        Args:
            panel: Panel DataFrame
            config: Panel configuration
            
        Returns:
            Panel with exchange rate data
        """
        # This would integrate with exchange rate repository
        # For now, derive from price data if both YER and USD prices available
        
        if 'price_yer' in panel.columns and 'price_usd' in panel.columns:
            # Calculate implied exchange rate
            mask = (panel['price_yer'].notna()) & (panel['price_usd'].notna()) & (panel['price_usd'] > 0)
            panel.loc[mask, 'implied_exchange_rate'] = panel.loc[mask, 'price_yer'] / panel.loc[mask, 'price_usd']
            
            # Remove outliers
            if 'implied_exchange_rate' in panel.columns:
                Q1 = panel['implied_exchange_rate'].quantile(0.25)
                Q3 = panel['implied_exchange_rate'].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outlier_mask = (
                    (panel['implied_exchange_rate'] < lower_bound) |
                    (panel['implied_exchange_rate'] > upper_bound)
                )
                panel.loc[outlier_mask, 'implied_exchange_rate'] = np.nan
            
            # Add exchange rate features
            if 'implied_exchange_rate' in panel.columns:
                # Moving average
                panel['exchange_rate_ma3'] = panel.groupby('market_id')['implied_exchange_rate'].transform(
                    lambda x: x.rolling(window=3, min_periods=1).mean()
                )
                
                # Volatility
                panel['exchange_rate_volatility'] = panel.groupby('market_id')['implied_exchange_rate'].transform(
                    lambda x: x.rolling(window=3, min_periods=2).std()
                )
        
        return panel
    
    def _calculate_coverage(self, panel: pd.DataFrame) -> PanelCoverage:
        """Calculate panel coverage statistics.
        
        Args:
            panel: Panel DataFrame
            
        Returns:
            PanelCoverage object with statistics
        """
        # Total expected observations
        n_markets = panel['market_id'].nunique()
        n_commodities = panel['commodity'].nunique()
        n_periods = panel['date'].nunique()
        expected_obs = n_markets * n_commodities * n_periods
        
        # Count missing by dimension
        missing_by_market = {}
        missing_by_commodity = {}
        missing_by_period = {}
        
        # Check price column for missing data
        price_col = 'price_usd' if 'price_usd' in panel.columns else 'price'
        
        if price_col in panel.columns:
            # By market
            for market in panel['market_id'].unique():
                market_data = panel[panel['market_id'] == market]
                missing_count = market_data[price_col].isna().sum()
                if missing_count > 0:
                    missing_by_market[market] = missing_count
            
            # By commodity
            for commodity in panel['commodity'].unique():
                commodity_data = panel[panel['commodity'] == commodity]
                missing_count = commodity_data[price_col].isna().sum()
                if missing_count > 0:
                    missing_by_commodity[commodity] = missing_count
            
            # By period
            for period in panel['year_month'].unique():
                period_data = panel[panel['year_month'] == period]
                missing_count = period_data[price_col].isna().sum()
                if missing_count > 0:
                    missing_by_period[str(period)] = missing_count
            
            # Calculate coverage
            non_missing = panel[price_col].notna().sum()
            coverage_pct = (non_missing / len(panel)) * 100
            missing_obs = len(panel) - non_missing
        else:
            coverage_pct = 0
            missing_obs = len(panel)
        
        return PanelCoverage(
            total_observations=len(panel),
            expected_observations=expected_obs,
            coverage_percentage=coverage_pct,
            missing_observations=missing_obs,
            markets_count=n_markets,
            commodities_count=n_commodities,
            time_periods_count=n_periods,
            missing_by_market=missing_by_market,
            missing_by_commodity=missing_by_commodity,
            missing_by_period=missing_by_period
        )
    
    async def create_model_specific_panels(
        self,
        base_panel: pd.DataFrame
    ) -> Dict[str, pd.DataFrame]:
        """Create model-specific panel datasets.
        
        Args:
            base_panel: Base integrated panel
            
        Returns:
            Dictionary of model-specific panels
        """
        panels = {}
        
        # Price transmission panel (market pairs)
        if 'price_usd' in base_panel.columns:
            panels['price_transmission'] = await self._create_price_transmission_panel(base_panel)
        
        # Exchange rate pass-through panel
        if 'implied_exchange_rate' in base_panel.columns:
            panels['exchange_passthrough'] = self._create_passthrough_panel(base_panel)
        
        # Threshold cointegration panel
        panels['threshold_coint'] = self._create_threshold_panel(base_panel)
        
        # Spatial panel
        if 'latitude' in base_panel.columns and 'longitude' in base_panel.columns:
            panels['spatial'] = self._create_spatial_panel(base_panel)
        
        return panels
    
    async def _create_price_transmission_panel(
        self,
        panel: pd.DataFrame
    ) -> pd.DataFrame:
        """Create panel for price transmission analysis between market pairs."""
        from itertools import combinations
        
        # Focus on tradable commodities
        tradable = ['Wheat', 'Wheat Flour', 'Rice (Imported)', 'Sugar', 'Oil (Vegetable)']
        panel_tradable = panel[panel['commodity'].isin(tradable)].copy()
        
        if panel_tradable.empty:
            return pd.DataFrame()
        
        # Get unique markets
        markets = panel_tradable['market_id'].unique()
        
        # Create market pairs
        transmission_data = []
        
        for market1, market2 in combinations(markets, 2):
            for commodity in tradable:
                # Get price series for both markets
                m1_prices = panel_tradable[
                    (panel_tradable['market_id'] == market1) & 
                    (panel_tradable['commodity'] == commodity)
                ][['date', 'price_usd']].rename(columns={'price_usd': 'price1'})
                
                m2_prices = panel_tradable[
                    (panel_tradable['market_id'] == market2) & 
                    (panel_tradable['commodity'] == commodity)
                ][['date', 'price_usd']].rename(columns={'price_usd': 'price2'})
                
                # Merge on date
                pair_prices = m1_prices.merge(m2_prices, on='date', how='inner')
                
                if len(pair_prices) < 20:  # Need sufficient observations
                    continue
                
                # Calculate transmission metrics
                pair_prices['price_ratio'] = pair_prices['price2'] / pair_prices['price1']
                pair_prices['price_diff'] = pair_prices['price2'] - pair_prices['price1']
                pair_prices['log_price_diff'] = np.log(pair_prices['price2']) - np.log(pair_prices['price1'])
                
                # Add pair info
                pair_prices['market1_id'] = market1
                pair_prices['market2_id'] = market2
                pair_prices['market_pair'] = f"{market1}_{market2}"
                pair_prices['commodity'] = commodity
                
                transmission_data.append(pair_prices)
        
        if not transmission_data:
            return pd.DataFrame()
        
        return pd.concat(transmission_data, ignore_index=True)
    
    def _create_passthrough_panel(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Create panel for exchange rate pass-through analysis."""
        if 'price_usd' not in panel.columns:
            return pd.DataFrame()
        
        # Select relevant columns
        passthrough = panel[['date', 'market_id', 'commodity', 'price_usd']].copy()
        
        if 'implied_exchange_rate' in panel.columns:
            passthrough['exchange_rate'] = panel['implied_exchange_rate']
        else:
            return pd.DataFrame()
        
        # Sort for time series operations
        passthrough = passthrough.sort_values(['market_id', 'commodity', 'date'])
        
        # Calculate log prices and exchange rates
        passthrough['log_price'] = np.log(passthrough['price_usd'])
        passthrough['log_exchange_rate'] = np.log(passthrough['exchange_rate'])
        
        # Calculate changes
        passthrough['dlog_price'] = passthrough.groupby(['market_id', 'commodity'])['log_price'].diff()
        passthrough['dlog_exchange_rate'] = passthrough.groupby(['market_id', 'commodity'])['log_exchange_rate'].diff()
        
        # Add lags
        for lag in range(1, 4):
            passthrough[f'dlog_price_lag{lag}'] = passthrough.groupby(['market_id', 'commodity'])['dlog_price'].shift(lag)
            passthrough[f'dlog_exchange_rate_lag{lag}'] = passthrough.groupby(['market_id', 'commodity'])['dlog_exchange_rate'].shift(lag)
        
        return passthrough
    
    def _create_threshold_panel(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Create panel for threshold cointegration analysis."""
        if 'price_usd' not in panel.columns:
            return pd.DataFrame()
        
        # Base variables
        threshold = panel[['date', 'market_id', 'commodity', 'price_usd']].copy()
        
        # Sort for time series operations
        threshold = threshold.sort_values(['market_id', 'commodity', 'date'])
        
        # Create log prices
        threshold['log_price'] = np.log(threshold['price_usd'])
        
        # Create lags
        for lag in range(1, 5):
            threshold[f'log_price_lag{lag}'] = threshold.groupby(['market_id', 'commodity'])['log_price'].shift(lag)
        
        # First differences
        threshold['d_log_price'] = threshold.groupby(['market_id', 'commodity'])['log_price'].diff()
        
        # Add threshold variables if available
        if 'conflict_intensity' in panel.columns:
            threshold['conflict_intensity'] = panel['conflict_intensity']
            threshold['conflict_ma3'] = panel.get('conflict_ma3', 0)
        
        # Add time trend
        threshold['time_trend'] = threshold.groupby(['market_id', 'commodity']).cumcount()
        
        return threshold
    
    def _create_spatial_panel(self, panel: pd.DataFrame) -> pd.DataFrame:
        """Create panel with spatial information."""
        spatial_vars = ['latitude', 'longitude', 'distance_to_capital_km', 'has_port_access']
        available_vars = [v for v in spatial_vars if v in panel.columns]
        
        base_vars = ['date', 'market_id', 'commodity']
        price_vars = ['price_usd'] if 'price_usd' in panel.columns else ['price']
        
        return panel[base_vars + available_vars + price_vars].copy()
    
    async def validate_against_v1(
        self,
        v2_panel: pd.DataFrame,
        v1_panel_path: Path
    ) -> Dict[str, Any]:
        """Validate V2 panel against V1 output.
        
        Args:
            v2_panel: V2 generated panel
            v1_panel_path: Path to V1 panel file
            
        Returns:
            Dictionary with validation results
        """
        # Load V1 panel
        v1_panel = pd.read_parquet(v1_panel_path)
        
        # Compare dimensions
        validation = {
            'dimension_match': {
                'v1_shape': v1_panel.shape,
                'v2_shape': v2_panel.shape,
                'markets_match': set(v1_panel.get('market', [])) == set(v2_panel.get('market_id', [])),
                'commodities_match': set(v1_panel.get('commodity', [])) == set(v2_panel.get('commodity', [])),
            },
            'coverage_comparison': {
                'v1_coverage': (v1_panel.get('price', v1_panel.get('price_usd', pd.Series())).notna().sum() / len(v1_panel) * 100) if len(v1_panel) > 0 else 0,
                'v2_coverage': (v2_panel.get('price', v2_panel.get('price_usd', pd.Series())).notna().sum() / len(v2_panel) * 100) if len(v2_panel) > 0 else 0,
            },
            'feature_comparison': {
                'v1_columns': set(v1_panel.columns),
                'v2_columns': set(v2_panel.columns),
                'missing_in_v2': set(v1_panel.columns) - set(v2_panel.columns),
                'added_in_v2': set(v2_panel.columns) - set(v1_panel.columns)
            }
        }
        
        logger.info(f"V1 vs V2 validation: {validation}")
        
        return validation