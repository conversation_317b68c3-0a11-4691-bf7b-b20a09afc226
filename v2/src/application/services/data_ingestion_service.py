"""Data ingestion service for the V2 application layer.

This service orchestrates the ingestion of data from multiple sources into the V2 domain model.
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import pandas as pd

from core.domain.market.entities import Market, PriceObservation, ExchangeRateObservation
from core.domain.market.repositories import MarketRepository, PriceRepository
from core.domain.market.value_objects import MarketId, Commodity, Price, Currency, ExchangeRate, Coordinates
from core.domain.conflict.entities import ConflictEvent
from core.domain.conflict.repositories import ConflictRepository
from core.domain.geography.entities import AdministrativeUnit
from core.domain.geography.repositories import GeographyRepository
from infrastructure.external_services.wfp_client import WFPClient
from infrastructure.external_services.acled_client import ACLEDClient
from infrastructure.observability.metrics import MetricsCollector
from shared.container import Container


@dataclass
class IngestionResult:
    """Result of data ingestion operation."""
    
    source: str
    success: bool
    records_processed: int
    records_saved: int
    errors: List[str]
    processing_time_seconds: float
    data_quality_score: float


@dataclass
class IngestionProgress:
    """Progress tracking for ingestion operations."""
    
    total_steps: int
    completed_steps: int
    current_step: str
    success_rate: float
    estimated_completion: Optional[datetime] = None


class DataIngestionService:
    """Service for orchestrating data ingestion from multiple sources."""
    
    def __init__(
        self,
        market_repository: MarketRepository,
        price_repository: PriceRepository,
        conflict_repository: ConflictRepository,
        geography_repository: GeographyRepository,
        wfp_client: WFPClient,
        acled_client: ACLEDClient,
        metrics: MetricsCollector,
        container: Container
    ):
        self.market_repo = market_repository
        self.price_repo = price_repository
        self.conflict_repo = conflict_repository
        self.geography_repo = geography_repository
        self.wfp_client = wfp_client
        self.acled_client = acled_client
        self.metrics = metrics
        self.container = container
        
        # Get processors from container
        self.wfp_processor = container.get_service("wfp_processor")
        self.acled_processor = container.get_service("acled_processor")
        self.acaps_processor = container.get_service("acaps_processor")
    
    async def ingest_all_sources(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        force_refresh: bool = False
    ) -> Dict[str, IngestionResult]:
        """Ingest data from all sources in correct dependency order."""
        results = {}
        
        try:
            # Step 1: Ingest geography data (foundations)
            self.metrics.increment_counter("ingestion.started", {"source": "geography"})
            results["geography"] = await self._ingest_geography_data()
            
            # Step 2: Ingest WFP data (markets and prices)
            self.metrics.increment_counter("ingestion.started", {"source": "wfp"})
            results["wfp"] = await self._ingest_wfp_data(start_date, end_date, force_refresh)
            
            # Step 3: Ingest ACLED data (conflict events)
            self.metrics.increment_counter("ingestion.started", {"source": "acled"})
            results["acled"] = await self._ingest_acled_data(start_date, end_date, force_refresh)
            
            # Step 4: Ingest ACAPS data (control zones)
            self.metrics.increment_counter("ingestion.started", {"source": "acaps"})
            results["acaps"] = await self._ingest_acaps_data(start_date, end_date, force_refresh)
            
            # Calculate overall success metrics
            total_records = sum(r.records_processed for r in results.values())
            total_saved = sum(r.records_saved for r in results.values())
            overall_success_rate = total_saved / total_records if total_records > 0 else 0
            
            self.metrics.gauge("ingestion.overall_success_rate", overall_success_rate)
            self.metrics.gauge("ingestion.total_records_processed", total_records)
            
        except Exception as e:
            self.metrics.increment_counter("ingestion.errors", {"error_type": type(e).__name__})
            raise
        
        return results
    
    async def _ingest_geography_data(self) -> IngestionResult:
        """Ingest geography reference data."""
        start_time = datetime.utcnow()
        errors = []
        
        try:
            # Load Yemen administrative boundaries
            admin_units = await self._load_administrative_boundaries()
            
            # Save to repository
            for unit in admin_units:
                await self.geography_repo.save_administrative_unit(unit)
            
            end_time = datetime.utcnow()
            processing_time = (end_time - start_time).total_seconds()
            
            return IngestionResult(
                source="geography",
                success=True,
                records_processed=len(admin_units),
                records_saved=len(admin_units),
                errors=errors,
                processing_time_seconds=processing_time,
                data_quality_score=1.0  # Reference data assumed high quality
            )
            
        except Exception as e:
            errors.append(f"Geography ingestion failed: {str(e)}")
            return IngestionResult(
                source="geography",
                success=False,
                records_processed=0,
                records_saved=0,
                errors=errors,
                processing_time_seconds=0,
                data_quality_score=0.0
            )
    
    async def _ingest_wfp_data(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        force_refresh: bool = False
    ) -> IngestionResult:
        """Ingest WFP price and market data."""
        start_time = datetime.utcnow()
        errors = []
        records_processed = 0
        records_saved = 0
        
        try:
            # Load raw WFP data
            raw_data = await self.wfp_client.fetch_price_data(start_date, end_date)
            
            if raw_data.empty:
                raise ValueError("No WFP data available for the specified period")
            
            # Process data using V2 processor
            processed_data = await self.wfp_processor.process_price_data(
                raw_data, start_date, end_date
            )
            
            records_processed = len(raw_data)
            
            # Extract markets, prices, and exchange rates
            markets = processed_data.get("markets", [])
            price_observations = processed_data.get("price_observations", [])
            exchange_rate_observations = processed_data.get("exchange_rate_observations", [])
            
            # Save markets
            for market in markets:
                await self.market_repo.save(market)
                records_saved += 1
            
            # Save price observations in batches
            if price_observations:
                await self.price_repo.save_batch(price_observations)
                records_saved += len(price_observations)
            
            # Save exchange rate observations
            for ex_rate_obs in exchange_rate_observations:
                await self.market_repo.save_exchange_rate(ex_rate_obs)
                records_saved += 1
            
            # Calculate data quality score
            quality_score = self._calculate_wfp_quality_score(processed_data)
            
            end_time = datetime.utcnow()
            processing_time = (end_time - start_time).total_seconds()
            
            # Update metrics
            self.metrics.gauge("wfp.markets_ingested", len(markets))
            self.metrics.gauge("wfp.prices_ingested", len(price_observations))
            self.metrics.gauge("wfp.data_quality_score", quality_score)
            
            return IngestionResult(
                source="wfp",
                success=True,
                records_processed=records_processed,
                records_saved=records_saved,
                errors=errors,
                processing_time_seconds=processing_time,
                data_quality_score=quality_score
            )
            
        except Exception as e:
            errors.append(f"WFP ingestion failed: {str(e)}")
            self.metrics.increment_counter("wfp.ingestion_errors", {"error": str(e)})
            
            return IngestionResult(
                source="wfp",
                success=False,
                records_processed=records_processed,
                records_saved=records_saved,
                errors=errors,
                processing_time_seconds=0,
                data_quality_score=0.0
            )
    
    async def _ingest_acled_data(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        force_refresh: bool = False
    ) -> IngestionResult:
        """Ingest ACLED conflict event data."""
        start_time = datetime.utcnow()
        errors = []
        records_processed = 0
        records_saved = 0
        
        try:
            # Load raw ACLED data
            raw_data = await self.acled_client.fetch_events(
                country="YEM",
                start_date=start_date,
                end_date=end_date
            )
            
            if raw_data.empty:
                raise ValueError("No ACLED data available for the specified period")
            
            # Process data using V2 processor
            conflict_events = await self.acled_processor.process_conflict_events(
                raw_data, start_date, end_date
            )
            
            records_processed = len(raw_data)
            
            # Save conflict events in batches
            if conflict_events:
                await self.conflict_repo.save_batch(conflict_events)
                records_saved = len(conflict_events)
            
            # Calculate data quality score
            quality_score = self._calculate_acled_quality_score(raw_data, conflict_events)
            
            end_time = datetime.utcnow()
            processing_time = (end_time - start_time).total_seconds()
            
            # Update metrics
            self.metrics.gauge("acled.events_ingested", len(conflict_events))
            self.metrics.gauge("acled.data_quality_score", quality_score)
            
            return IngestionResult(
                source="acled",
                success=True,
                records_processed=records_processed,
                records_saved=records_saved,
                errors=errors,
                processing_time_seconds=processing_time,
                data_quality_score=quality_score
            )
            
        except Exception as e:
            errors.append(f"ACLED ingestion failed: {str(e)}")
            self.metrics.increment_counter("acled.ingestion_errors", {"error": str(e)})
            
            return IngestionResult(
                source="acled",
                success=False,
                records_processed=records_processed,
                records_saved=records_saved,
                errors=errors,
                processing_time_seconds=0,
                data_quality_score=0.0
            )
    
    async def _ingest_acaps_data(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        force_refresh: bool = False
    ) -> IngestionResult:
        """Ingest ACAPS control area data."""
        start_time = datetime.utcnow()
        errors = []
        records_processed = 0
        records_saved = 0
        
        try:
            # Process ACAPS data using V2 processor
            control_data = await self.acaps_processor.process_control_areas(
                start_date, end_date
            )
            
            if not control_data:
                raise ValueError("No ACAPS data available for the specified period")
            
            records_processed = len(control_data)
            
            # Update market control status
            for control_record in control_data:
                # Find affected markets
                affected_markets = await self.market_repo.find_in_district(
                    control_record.governorate,
                    control_record.district
                )
                
                # Update control status for each market
                for market in affected_markets:
                    market.update_control_status(
                        control_record.control_status,
                        control_record.effective_date
                    )
                    await self.market_repo.save(market)
                    records_saved += 1
            
            # Calculate data quality score
            quality_score = self._calculate_acaps_quality_score(control_data)
            
            end_time = datetime.utcnow()
            processing_time = (end_time - start_time).total_seconds()
            
            # Update metrics
            self.metrics.gauge("acaps.control_records_ingested", len(control_data))
            self.metrics.gauge("acaps.data_quality_score", quality_score)
            
            return IngestionResult(
                source="acaps",
                success=True,
                records_processed=records_processed,
                records_saved=records_saved,
                errors=errors,
                processing_time_seconds=processing_time,
                data_quality_score=quality_score
            )
            
        except Exception as e:
            errors.append(f"ACAPS ingestion failed: {str(e)}")
            self.metrics.increment_counter("acaps.ingestion_errors", {"error": str(e)})
            
            return IngestionResult(
                source="acaps",
                success=False,
                records_processed=records_processed,
                records_saved=records_saved,
                errors=errors,
                processing_time_seconds=0,
                data_quality_score=0.0
            )
    
    async def _load_administrative_boundaries(self) -> List[AdministrativeUnit]:
        """Load Yemen administrative boundaries from reference data."""
        # This would load from HDX or other reference data source
        # For now, return basic structure based on known Yemen geography
        admin_units = []
        
        # Yemen governorates and major districts
        yemen_admin = {
            "Sana'a": ["Al Wahdah", "As Sab'ayn", "As Safiya", "Ath Thawrah"],
            "Aden": ["Al Mansurah", "Al Mualla", "Ash Shaikh Uthman", "At Tawahi"],
            "Al Hodeidah": ["Al Hodeidah", "Az Zuhrah", "Bajil", "Bura"],
            "Ta'iz": ["Al Qahirah", "Al Mudhaffar", "As Silw", "Dimnat Khadir"],
            "Hajjah": ["Abs", "Aflah Al Yaman", "Aflah Ash Shawm", "Aslem"],
            "Ibb": ["Al Mashannah", "Al Qafr", "An Nadirah", "As Sayyani"],
            "Dhamar": ["Ans", "Damt", "Dhamar", "Jahran"],
            "Al Maharah": ["Al Ghaydah", "Haswayn", "Huswain", "Man'ar"],
            "Hadramawt": ["Al Mukalla", "Ash Shihr", "Say'un", "Tarim"],
            "Abyan": ["Ahwar", "Ja'ar", "Khanfar", "Lawdar"],
            "Lahj": ["Al Had", "Al Hawta", "Al Milah", "Al Qabbaytah"],
            "Marib": ["Bidbadah", "Harib", "Harib Al Qaramish", "Jabal Murad"],
            "Al Jawf": ["Al Ghayl", "Al Humaydat", "Al Maslub", "Al Maton"],
            "Sa'dah": ["Al Boqe'a", "As Safra", "Baqim", "Ghamr"],
            "Shabwah": ["Ar Rawdah", "Arma", "As Said", "Ataq"],
            "Ad Dale'": ["Ad Dale'", "Al Azariq", "Al Hussayniyah", "As Su'ayrah"],
            "Raymah": ["Al Jabeen", "As Salafiyah", "Bilad At Ta'am", "Kusmah"],
            "Al Mahwit": ["Al Khabt", "Al Mahwit", "Al Tawilah", "Hufash"],
            "Amran": ["Amran", "As Sudah", "Harf Sufyan", "Khamir"],
            "Socotra": ["Abd Al Kuri", "Homhil", "Qalansiyah", "Socotra"]
        }
        
        for governorate, districts in yemen_admin.items():
            # Create governorate unit
            gov_unit = AdministrativeUnit(
                name=governorate,
                level=1,
                parent_unit=None,
                pcode=f"YE{len(admin_units):02d}"
            )
            admin_units.append(gov_unit)
            
            # Create district units
            for i, district in enumerate(districts):
                dist_unit = AdministrativeUnit(
                    name=district,
                    level=2,
                    parent_unit=gov_unit,
                    pcode=f"YE{len(admin_units):02d}{i:02d}"
                )
                admin_units.append(dist_unit)
        
        return admin_units
    
    def _calculate_wfp_quality_score(self, processed_data: Dict) -> float:
        """Calculate data quality score for WFP data."""
        score = 1.0
        
        markets = processed_data.get("markets", [])
        prices = processed_data.get("price_observations", [])
        
        if not markets:
            score -= 0.5
        
        if not prices:
            score -= 0.5
        
        # Check for missing coordinates
        markets_with_coords = sum(1 for m in markets if m.coordinates is not None)
        if markets and markets_with_coords / len(markets) < 0.8:
            score -= 0.2
        
        # Check for price coverage
        if prices:
            prices_with_usd = sum(1 for p in prices if p.price.currency == Currency.USD)
            if prices_with_usd / len(prices) < 0.5:
                score -= 0.2
        
        return max(0.0, score)
    
    def _calculate_acled_quality_score(self, raw_data: pd.DataFrame, events: List[ConflictEvent]) -> float:
        """Calculate data quality score for ACLED data."""
        score = 1.0
        
        if raw_data.empty:
            return 0.0
        
        # Check coordinate completeness
        coord_completeness = (
            raw_data[['latitude', 'longitude']].notna().all(axis=1).mean()
        )
        if coord_completeness < 0.9:
            score -= 0.3
        
        # Check processing success rate
        processing_rate = len(events) / len(raw_data) if len(raw_data) > 0 else 0
        if processing_rate < 0.8:
            score -= 0.4
        
        # Check for required fields
        required_fields = ['event_date', 'event_type', 'fatalities']
        for field in required_fields:
            if field in raw_data.columns:
                completeness = raw_data[field].notna().mean()
                if completeness < 0.95:
                    score -= 0.1
        
        return max(0.0, score)
    
    def _calculate_acaps_quality_score(self, control_data: List) -> float:
        """Calculate data quality score for ACAPS data."""
        score = 1.0
        
        if not control_data:
            return 0.0
        
        # Check for temporal coverage (should have regular updates)
        dates = [record.effective_date for record in control_data]
        if len(set(dates)) < len(control_data) * 0.1:  # Expect some temporal variation
            score -= 0.3
        
        # Check for geographic coverage
        districts = set((record.governorate, record.district) for record in control_data)
        if len(districts) < 50:  # Yemen has many districts
            score -= 0.2
        
        return max(0.0, score)
    
    async def get_ingestion_progress(self, session_id: str) -> Optional[IngestionProgress]:
        """Get progress for an ongoing ingestion session."""
        # This would track progress in a session store
        # For now, return None indicating no active session
        return None
    
    async def schedule_ingestion(
        self,
        schedule_expression: str,
        sources: List[str],
        config: Dict
    ) -> str:
        """Schedule recurring data ingestion."""
        # This would integrate with a scheduler (e.g., Celery, APScheduler)
        # Return a task ID for tracking
        return f"scheduled_{datetime.utcnow().isoformat()}"