"""Data ingestion orchestration service with scheduling, error recovery, and monitoring.

This service coordinates data ingestion across multiple sources with proper
error handling, retry logic, and progress tracking.
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Callable, Any
from uuid import uuid4, UUID
import json

from application.services.data_ingestion_service import DataIngestionService, IngestionResult
from infrastructure.observability.metrics import MetricsCollector
from infrastructure.messaging.event_bus import EventBus
from infrastructure.caching.memory_cache import MemoryCache
from infrastructure.logging import get_logger


class IngestionStatus(Enum):
    """Status of ingestion jobs."""
    
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class IngestionPriority(Enum):
    """Priority levels for ingestion jobs."""
    
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class IngestionJob:
    """Represents a data ingestion job."""
    
    job_id: UUID = field(default_factory=uuid4)
    source: str = ""
    status: IngestionStatus = IngestionStatus.PENDING
    priority: IngestionPriority = IngestionPriority.NORMAL
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Configuration
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    force_refresh: bool = False
    config: Dict[str, Any] = field(default_factory=dict)
    
    # Execution tracking
    retry_count: int = 0
    max_retries: int = 3
    retry_delay_seconds: int = 60
    estimated_duration_seconds: Optional[int] = None
    
    # Results
    result: Optional[IngestionResult] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        """Initialize job."""
        if not self.source:
            raise ValueError("Source is required for ingestion job")
        
        # Set default estimated duration based on source
        if self.estimated_duration_seconds is None:
            duration_map = {
                "wfp": 300,      # 5 minutes
                "acled": 180,    # 3 minutes
                "acaps": 120,    # 2 minutes
                "geography": 60   # 1 minute
            }
            self.estimated_duration_seconds = duration_map.get(self.source, 300)
    
    @property
    def is_complete(self) -> bool:
        """Check if job is in a terminal state."""
        return self.status in [IngestionStatus.COMPLETED, IngestionStatus.FAILED, IngestionStatus.CANCELLED]
    
    @property
    def can_retry(self) -> bool:
        """Check if job can be retried."""
        return self.status == IngestionStatus.FAILED and self.retry_count < self.max_retries
    
    @property
    def duration_seconds(self) -> Optional[int]:
        """Get actual duration if job is complete."""
        if self.started_at and self.completed_at:
            return int((self.completed_at - self.started_at).total_seconds())
        return None


@dataclass
class IngestionSchedule:
    """Represents a scheduled ingestion configuration."""
    
    schedule_id: UUID = field(default_factory=uuid4)
    name: str = ""
    sources: List[str] = field(default_factory=list)
    cron_expression: str = ""
    enabled: bool = True
    
    # Configuration
    config: Dict[str, Any] = field(default_factory=dict)
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    
    # Metadata
    created_by: str = "system"
    created_at: datetime = field(default_factory=datetime.utcnow)


class IngestionOrchestrator:
    """Orchestrates data ingestion jobs with scheduling and error recovery."""
    
    def __init__(
        self,
        data_ingestion_service: DataIngestionService,
        metrics: MetricsCollector,
        event_bus: EventBus,
        cache: MemoryCache,
        logger: Optional = None
    ):
        self.ingestion_service = data_ingestion_service
        self.metrics = metrics
        self.event_bus = event_bus
        self.cache = cache
        self.logger = logger or get_logger(__name__)
        
        # Job tracking
        self.active_jobs: Dict[UUID, IngestionJob] = {}
        self.job_history: List[IngestionJob] = []
        self.schedules: Dict[UUID, IngestionSchedule] = {}
        
        # Execution control
        self.max_concurrent_jobs = 3
        self.running_jobs: Dict[UUID, asyncio.Task] = {}
        self.shutdown_requested = False
        
        # Background tasks
        self.scheduler_task: Optional[asyncio.Task] = None
        self.monitoring_task: Optional[asyncio.Task] = None
    
    async def start(self) -> None:
        """Start the orchestrator background tasks."""
        
        self.logger.info("Starting ingestion orchestrator")
        
        # Start background tasks
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        # Restore state from cache if available
        await self._restore_state()
        
        self.metrics.increment_counter("orchestrator.started")
        self.logger.info("Ingestion orchestrator started")
    
    async def stop(self) -> None:
        """Stop the orchestrator and cleanup resources."""
        
        self.logger.info("Stopping ingestion orchestrator")
        self.shutdown_requested = True
        
        # Cancel running jobs
        for job_id, task in self.running_jobs.items():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        
        # Cancel background tasks
        if self.scheduler_task:
            self.scheduler_task.cancel()
        if self.monitoring_task:
            self.monitoring_task.cancel()
        
        # Save state
        await self._save_state()
        
        self.metrics.increment_counter("orchestrator.stopped")
        self.logger.info("Ingestion orchestrator stopped")
    
    async def submit_job(
        self,
        source: str,
        priority: IngestionPriority = IngestionPriority.NORMAL,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        force_refresh: bool = False,
        config: Optional[Dict[str, Any]] = None
    ) -> UUID:
        """Submit a new ingestion job."""
        
        job = IngestionJob(
            source=source,
            priority=priority,
            start_date=start_date,
            end_date=end_date,
            force_refresh=force_refresh,
            config=config or {}
        )
        
        self.active_jobs[job.job_id] = job
        
        # Emit job submitted event
        await self.event_bus.publish("ingestion.job_submitted", {
            "job_id": str(job.job_id),
            "source": source,
            "priority": priority.value
        })
        
        self.metrics.increment_counter("orchestrator.jobs_submitted", {"source": source})
        self.logger.info("Ingestion job submitted", extra={
            "job_id": str(job.job_id),
            "source": source,
            "priority": priority.value
        })
        
        # Trigger immediate execution if capacity available
        await self._process_pending_jobs()
        
        return job.job_id
    
    async def submit_batch_job(
        self,
        sources: List[str],
        priority: IngestionPriority = IngestionPriority.NORMAL,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        force_refresh: bool = False,
        config: Optional[Dict[str, Any]] = None
    ) -> List[UUID]:
        """Submit multiple ingestion jobs as a batch."""
        
        job_ids = []
        
        for source in sources:
            job_id = await self.submit_job(
                source=source,
                priority=priority,
                start_date=start_date,
                end_date=end_date,
                force_refresh=force_refresh,
                config=config
            )
            job_ids.append(job_id)
        
        return job_ids
    
    async def cancel_job(self, job_id: UUID) -> bool:
        """Cancel a pending or running job."""
        
        job = self.active_jobs.get(job_id)
        if not job:
            return False
        
        if job.status == IngestionStatus.RUNNING:
            # Cancel running task
            task = self.running_jobs.get(job_id)
            if task:
                task.cancel()
                del self.running_jobs[job_id]
        
        job.status = IngestionStatus.CANCELLED
        job.completed_at = datetime.utcnow()
        
        # Move to history
        self.job_history.append(job)
        del self.active_jobs[job_id]
        
        await self.event_bus.publish("ingestion.job_cancelled", {
            "job_id": str(job_id)
        })
        
        self.metrics.increment_counter("orchestrator.jobs_cancelled")
        self.logger.info("Job cancelled", extra={"job_id": str(job_id)})
        
        return True
    
    async def get_job_status(self, job_id: UUID) -> Optional[IngestionJob]:
        """Get status of a specific job."""
        
        # Check active jobs first
        if job_id in self.active_jobs:
            return self.active_jobs[job_id]
        
        # Check history
        for job in self.job_history:
            if job.job_id == job_id:
                return job
        
        return None
    
    async def get_active_jobs(self) -> List[IngestionJob]:
        """Get all active jobs."""
        return list(self.active_jobs.values())
    
    async def get_job_history(
        self,
        limit: int = 100,
        source: Optional[str] = None,
        status: Optional[IngestionStatus] = None
    ) -> List[IngestionJob]:
        """Get job history with optional filtering."""
        
        history = self.job_history.copy()
        
        # Apply filters
        if source:
            history = [job for job in history if job.source == source]
        
        if status:
            history = [job for job in history if job.status == status]
        
        # Sort by completion time (newest first) and limit
        history.sort(key=lambda j: j.completed_at or datetime.utcnow(), reverse=True)
        
        return history[:limit]
    
    async def create_schedule(
        self,
        name: str,
        sources: List[str],
        cron_expression: str,
        config: Optional[Dict[str, Any]] = None,
        enabled: bool = True
    ) -> UUID:
        """Create a new ingestion schedule."""
        
        schedule = IngestionSchedule(
            name=name,
            sources=sources,
            cron_expression=cron_expression,
            config=config or {},
            enabled=enabled
        )
        
        # Calculate next run time
        schedule.next_run = self._calculate_next_run(cron_expression)
        
        self.schedules[schedule.schedule_id] = schedule
        
        self.metrics.increment_counter("orchestrator.schedules_created")
        self.logger.info("Schedule created", extra={
            "schedule_id": str(schedule.schedule_id),
            "name": name,
            "sources": sources,
            "cron": cron_expression
        })
        
        return schedule.schedule_id
    
    async def _scheduler_loop(self) -> None:
        """Background loop for processing scheduled jobs."""
        
        while not self.shutdown_requested:
            try:
                # Check for scheduled jobs that need to run
                now = datetime.utcnow()
                
                for schedule in self.schedules.values():
                    if (schedule.enabled and 
                        schedule.next_run and 
                        schedule.next_run <= now):
                        
                        # Submit jobs for this schedule
                        await self._execute_schedule(schedule)
                        
                        # Calculate next run time
                        schedule.last_run = now
                        schedule.next_run = self._calculate_next_run(schedule.cron_expression, now)
                
                # Process pending jobs
                await self._process_pending_jobs()
                
                # Sleep for a short interval
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error("Error in scheduler loop", extra={"error": str(e)})
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _monitoring_loop(self) -> None:
        """Background loop for monitoring job health and cleanup."""
        
        while not self.shutdown_requested:
            try:
                # Check for stalled jobs
                await self._check_stalled_jobs()
                
                # Cleanup completed jobs from history
                await self._cleanup_job_history()
                
                # Update metrics
                await self._update_metrics()
                
                # Save state periodically
                await self._save_state()
                
                # Sleep for monitoring interval
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error("Error in monitoring loop", extra={"error": str(e)})
                await asyncio.sleep(300)
    
    async def _process_pending_jobs(self) -> None:
        """Process pending jobs based on priority and capacity."""
        
        # Check if we have capacity for more jobs
        if len(self.running_jobs) >= self.max_concurrent_jobs:
            return
        
        # Get pending jobs sorted by priority
        pending_jobs = [
            job for job in self.active_jobs.values()
            if job.status == IngestionStatus.PENDING
        ]
        
        # Sort by priority (higher first) then by creation time
        pending_jobs.sort(key=lambda j: (-j.priority.value, j.created_at))
        
        # Start jobs up to capacity limit
        for job in pending_jobs:
            if len(self.running_jobs) >= self.max_concurrent_jobs:
                break
            
            await self._start_job(job)
    
    async def _start_job(self, job: IngestionJob) -> None:
        """Start executing a job."""
        
        job.status = IngestionStatus.RUNNING
        job.started_at = datetime.utcnow()
        
        # Create execution task
        task = asyncio.create_task(self._execute_job(job))
        self.running_jobs[job.job_id] = task
        
        await self.event_bus.publish("ingestion.job_started", {
            "job_id": str(job.job_id),
            "source": job.source
        })
        
        self.metrics.increment_counter("orchestrator.jobs_started", {"source": job.source})
        self.logger.info("Job started", extra={
            "job_id": str(job.job_id),
            "source": job.source
        })
    
    async def _execute_job(self, job: IngestionJob) -> None:
        """Execute a single ingestion job."""
        
        try:
            # Execute the actual ingestion
            if job.source == "all":
                results = await self.ingestion_service.ingest_all_sources(
                    start_date=job.start_date,
                    end_date=job.end_date,
                    force_refresh=job.force_refresh
                )
                # Use the first result for job tracking
                job.result = list(results.values())[0] if results else None
            else:
                # Execute single source ingestion
                job.result = await self._execute_single_source(job)
            
            # Mark as completed
            job.status = IngestionStatus.COMPLETED
            job.completed_at = datetime.utcnow()
            
            await self.event_bus.publish("ingestion.job_completed", {
                "job_id": str(job.job_id),
                "source": job.source,
                "success": job.result.success if job.result else False
            })
            
            self.metrics.increment_counter("orchestrator.jobs_completed", {
                "source": job.source,
                "success": str(job.result.success if job.result else False)
            })
            
        except Exception as e:
            # Mark as failed
            job.status = IngestionStatus.FAILED
            job.completed_at = datetime.utcnow()
            job.error_message = str(e)
            
            await self.event_bus.publish("ingestion.job_failed", {
                "job_id": str(job.job_id),
                "source": job.source,
                "error": str(e)
            })
            
            self.metrics.increment_counter("orchestrator.jobs_failed", {"source": job.source})
            
            # Schedule retry if possible
            if job.can_retry:
                await self._schedule_retry(job)
            
        finally:
            # Cleanup
            if job.job_id in self.running_jobs:
                del self.running_jobs[job.job_id]
            
            # Move to history if complete
            if job.is_complete:
                self.job_history.append(job)
                if job.job_id in self.active_jobs:
                    del self.active_jobs[job.job_id]
    
    async def _execute_single_source(self, job: IngestionJob) -> IngestionResult:
        """Execute ingestion for a single source."""
        
        if job.source == "wfp":
            result = await self.ingestion_service._ingest_wfp_data(
                job.start_date, job.end_date, job.force_refresh
            )
        elif job.source == "acled":
            result = await self.ingestion_service._ingest_acled_data(
                job.start_date, job.end_date, job.force_refresh
            )
        elif job.source == "acaps":
            result = await self.ingestion_service._ingest_acaps_data(
                job.start_date, job.end_date, job.force_refresh
            )
        elif job.source == "geography":
            result = await self.ingestion_service._ingest_geography_data()
        else:
            raise ValueError(f"Unknown source: {job.source}")
        
        return result
    
    async def _schedule_retry(self, job: IngestionJob) -> None:
        """Schedule a job for retry."""
        
        job.retry_count += 1
        job.status = IngestionStatus.RETRYING
        
        # Calculate retry delay (exponential backoff)
        delay = job.retry_delay_seconds * (2 ** (job.retry_count - 1))
        
        self.logger.info("Scheduling job retry", extra={
            "job_id": str(job.job_id),
            "retry_count": job.retry_count,
            "delay_seconds": delay
        })
        
        # Schedule retry
        asyncio.create_task(self._retry_job_after_delay(job, delay))
    
    async def _retry_job_after_delay(self, job: IngestionJob, delay_seconds: int) -> None:
        """Retry a job after the specified delay."""
        
        await asyncio.sleep(delay_seconds)
        
        if not self.shutdown_requested and job.job_id in self.active_jobs:
            job.status = IngestionStatus.PENDING
            job.error_message = None
            await self._process_pending_jobs()
    
    async def _execute_schedule(self, schedule: IngestionSchedule) -> None:
        """Execute a scheduled ingestion."""
        
        self.logger.info("Executing schedule", extra={
            "schedule_id": str(schedule.schedule_id),
            "name": schedule.name,
            "sources": schedule.sources
        })
        
        # Submit jobs for all sources in the schedule
        for source in schedule.sources:
            await self.submit_job(
                source=source,
                priority=IngestionPriority.NORMAL,
                config=schedule.config
            )
    
    async def _check_stalled_jobs(self) -> None:
        """Check for jobs that have been running too long."""
        
        now = datetime.utcnow()
        stalled_threshold = timedelta(hours=2)  # 2 hour timeout
        
        for job in self.active_jobs.values():
            if (job.status == IngestionStatus.RUNNING and 
                job.started_at and 
                now - job.started_at > stalled_threshold):
                
                self.logger.warning("Detected stalled job", extra={
                    "job_id": str(job.job_id),
                    "source": job.source,
                    "running_time": str(now - job.started_at)
                })
                
                # Cancel the stalled job
                await self.cancel_job(job.job_id)
                
                self.metrics.increment_counter("orchestrator.jobs_stalled")
    
    async def _cleanup_job_history(self) -> None:
        """Cleanup old jobs from history."""
        
        # Keep only last 1000 jobs
        if len(self.job_history) > 1000:
            self.job_history = self.job_history[-1000:]
        
        # Remove jobs older than 30 days
        cutoff_date = datetime.utcnow() - timedelta(days=30)
        self.job_history = [
            job for job in self.job_history
            if job.completed_at and job.completed_at > cutoff_date
        ]
    
    async def _update_metrics(self) -> None:
        """Update orchestrator metrics."""
        
        self.metrics.gauge("orchestrator.active_jobs", len(self.active_jobs))
        self.metrics.gauge("orchestrator.running_jobs", len(self.running_jobs))
        self.metrics.gauge("orchestrator.schedules", len(self.schedules))
        
        # Job status breakdown
        status_counts = {}
        for job in self.active_jobs.values():
            status = job.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        for status, count in status_counts.items():
            self.metrics.gauge(f"orchestrator.jobs_{status}", count)
    
    def _calculate_next_run(self, cron_expression: str, from_time: Optional[datetime] = None) -> datetime:
        """Calculate next run time from cron expression."""
        
        # Simple implementation - in production, use a proper cron library
        # For now, just add 1 hour as a placeholder
        base_time = from_time or datetime.utcnow()
        return base_time + timedelta(hours=1)
    
    async def _save_state(self) -> None:
        """Save orchestrator state to cache."""
        
        state = {
            "active_jobs": [self._serialize_job(job) for job in self.active_jobs.values()],
            "schedules": [self._serialize_schedule(schedule) for schedule in self.schedules.values()],
            "saved_at": datetime.utcnow().isoformat()
        }
        
        await self.cache.set("orchestrator_state", json.dumps(state, default=str), ttl=86400)
    
    async def _restore_state(self) -> None:
        """Restore orchestrator state from cache."""
        
        try:
            state_json = await self.cache.get("orchestrator_state")
            if state_json:
                state = json.loads(state_json)
                
                # Restore schedules
                for schedule_data in state.get("schedules", []):
                    schedule = self._deserialize_schedule(schedule_data)
                    self.schedules[schedule.schedule_id] = schedule
                
                self.logger.info("Orchestrator state restored", extra={
                    "schedules": len(self.schedules)
                })
                
        except Exception as e:
            self.logger.warning("Failed to restore orchestrator state", extra={"error": str(e)})
    
    def _serialize_job(self, job: IngestionJob) -> Dict:
        """Serialize job to dictionary."""
        return {
            "job_id": str(job.job_id),
            "source": job.source,
            "status": job.status.value,
            "priority": job.priority.value,
            "created_at": job.created_at.isoformat(),
            "config": job.config
        }
    
    def _serialize_schedule(self, schedule: IngestionSchedule) -> Dict:
        """Serialize schedule to dictionary."""
        return {
            "schedule_id": str(schedule.schedule_id),
            "name": schedule.name,
            "sources": schedule.sources,
            "cron_expression": schedule.cron_expression,
            "enabled": schedule.enabled,
            "config": schedule.config
        }
    
    def _deserialize_schedule(self, data: Dict) -> IngestionSchedule:
        """Deserialize schedule from dictionary."""
        return IngestionSchedule(
            schedule_id=UUID(data["schedule_id"]),
            name=data["name"],
            sources=data["sources"],
            cron_expression=data["cron_expression"],
            enabled=data["enabled"],
            config=data["config"]
        )