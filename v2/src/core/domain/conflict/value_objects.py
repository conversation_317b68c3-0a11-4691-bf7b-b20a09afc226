"""Value objects for the Conflict bounded context."""

from dataclasses import dataclass
from enum import Enum

from ..shared.entities import ValueObject
from ..shared.exceptions import ValidationException


class ConflictType(Enum):
    """Types of conflict events."""
    
    BATTLE = "battle"
    EXPLOSION = "explosion"
    VIOLENCE_AGAINST_CIVILIANS = "violence_against_civilians"
    PROTESTS = "protests"
    RIOTS = "riots"
    STRATEGIC_DEVELOPMENT = "strategic_development"


class ConflictIntensity(Enum):
    """Intensity levels of conflict."""
    
    LOW = "low"        # 1-5 fatalities
    MEDIUM = "medium"  # 6-25 fatalities
    HIGH = "high"      # 26-100 fatalities
    SEVERE = "severe"  # 100+ fatalities
    
    @classmethod
    def from_fatalities(cls, fatalities: int) -> 'ConflictIntensity':
        """Determine intensity from fatality count."""
        if fatalities <= 0:
            return cls.LOW
        elif fatalities <= 5:
            return cls.LOW
        elif fatalities <= 25:
            return cls.MEDIUM
        elif fatalities <= 100:
            return cls.HIGH
        else:
            return cls.SEVERE


@dataclass(frozen=True)
class ImpactRadius(ValueObject):
    """Radius of conflict impact in kilometers."""
    
    immediate_km: float  # Direct impact zone
    moderate_km: float   # Moderate impact zone
    marginal_km: float   # Marginal impact zone
    
    def __post_init__(self) -> None:
        """Validate impact radius."""
        if self.immediate_km <= 0:
            raise ValidationException("Immediate radius must be positive")
        if self.moderate_km <= self.immediate_km:
            raise ValidationException("Moderate radius must be greater than immediate")
        if self.marginal_km <= self.moderate_km:
            raise ValidationException("Marginal radius must be greater than moderate")
        # Validation complete
    
    @classmethod
    def default_for_type(cls, conflict_type: ConflictType) -> 'ImpactRadius':
        """Get default impact radius for conflict type."""
        if conflict_type == ConflictType.BATTLE:
            return cls(immediate_km=10, moderate_km=30, marginal_km=50)
        elif conflict_type == ConflictType.EXPLOSION:
            return cls(immediate_km=5, moderate_km=15, marginal_km=25)
        elif conflict_type == ConflictType.VIOLENCE_AGAINST_CIVILIANS:
            return cls(immediate_km=5, moderate_km=10, marginal_km=20)
        elif conflict_type in [ConflictType.PROTESTS, ConflictType.RIOTS]:
            return cls(immediate_km=2, moderate_km=5, marginal_km=10)
        else:
            return cls(immediate_km=1, moderate_km=3, marginal_km=5)