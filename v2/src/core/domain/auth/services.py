"""Authentication domain services."""

from datetime import datetime, timed<PERSON>ta
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from uuid import UUI<PERSON>
import hashlib

from .entities import User, <PERSON><PERSON>ey, RefreshToken
from .repositories import UserRepository, APIKeyRepository, RefreshTokenRepository
from .value_objects import Email, HashedPassword, UserRole, UserStatus
from ...infrastructure.logging import Logger

logger = Logger(__name__)


class AuthenticationService:
    """Domain service for authentication operations."""
    
    def __init__(
        self,
        user_repository: UserRepository,
        refresh_token_repository: RefreshTokenRepository
    ):
        self.user_repository = user_repository
        self.refresh_token_repository = refresh_token_repository
    
    async def authenticate_user(
        self,
        username: str,
        password: str,
        password_verifier
    ) -> Tuple[Optional[User], Optional[str]]:
        """
        Authenticate a user with username and password.
        
        Returns:
            Tuple of (user, error_message)
        """
        # Find user
        user = await self.user_repository.find_by_username(username)
        if not user:
            # Also try email
            try:
                email = Email(value=username)
                user = await self.user_repository.find_by_email(email)
            except ValueError:
                pass
        
        if not user:
            logger.warning(f"Authentication failed: User not found for {username}")
            return None, "Invalid username or password"
        
        # Check if user can login
        can_login, reason = user.can_login()
        if not can_login:
            logger.warning(f"Authentication failed for {username}: {reason}")
            return None, reason
        
        # Verify password
        if not password_verifier(password, user.password_hash.value):
            user.record_failed_login()
            await self.user_repository.save(user)
            logger.warning(f"Authentication failed: Invalid password for {username}")
            return None, "Invalid username or password"
        
        # Record successful login
        user.record_successful_login()
        await self.user_repository.save(user)
        
        logger.info(f"User {username} authenticated successfully")
        return user, None
    
    async def create_refresh_token(
        self,
        user_id: UUID,
        token_value: str,
        expires_in_days: int = 7,
        device_info: Optional[str] = None,
        ip_address: Optional[str] = None
    ) -> RefreshToken:
        """Create and store a refresh token."""
        token_hash = hashlib.sha256(token_value.encode()).hexdigest()
        
        refresh_token = RefreshToken(
            token_hash=token_hash,
            user_id=user_id,
            expires_at=datetime.utcnow() + timedelta(days=expires_in_days),
            device_info=device_info,
            ip_address=ip_address
        )
        
        await self.refresh_token_repository.save(refresh_token)
        return refresh_token
    
    async def validate_refresh_token(
        self,
        token_value: str
    ) -> Tuple[Optional[User], Optional[str]]:
        """
        Validate a refresh token and return the associated user.
        
        Returns:
            Tuple of (user, error_message)
        """
        token_hash = hashlib.sha256(token_value.encode()).hexdigest()
        
        refresh_token = await self.refresh_token_repository.find_by_token_hash(token_hash)
        if not refresh_token:
            return None, "Invalid refresh token"
        
        if not refresh_token.is_valid():
            return None, "Refresh token expired or revoked"
        
        user = await self.user_repository.find_by_id(refresh_token.user_id)
        if not user:
            return None, "User not found"
        
        can_login, reason = user.can_login()
        if not can_login:
            return None, reason
        
        return user, None
    
    async def revoke_refresh_token(self, token_value: str) -> bool:
        """Revoke a refresh token."""
        token_hash = hashlib.sha256(token_value.encode()).hexdigest()
        
        refresh_token = await self.refresh_token_repository.find_by_token_hash(token_hash)
        if refresh_token:
            refresh_token.revoke()
            await self.refresh_token_repository.save(refresh_token)
            return True
        
        return False
    
    async def revoke_all_user_tokens(self, user_id: UUID) -> None:
        """Revoke all refresh tokens for a user."""
        await self.refresh_token_repository.revoke_all_for_user(user_id)


class UserService:
    """Domain service for user management operations."""
    
    def __init__(
        self,
        user_repository: UserRepository,
        api_key_repository: APIKeyRepository
    ):
        self.user_repository = user_repository
        self.api_key_repository = api_key_repository
    
    async def create_user(
        self,
        username: str,
        email: str,
        password_hash: str,
        full_name: Optional[str] = None,
        roles: Optional[list[str]] = None,
        auto_verify_email: bool = False
    ) -> Tuple[Optional[User], Optional[str]]:
        """
        Create a new user.
        
        Returns:
            Tuple of (user, error_message)
        """
        # Validate email
        try:
            email_obj = Email(value=email)
        except ValueError as e:
            return None, str(e)
        
        # Validate password hash
        try:
            password_hash_obj = HashedPassword(value=password_hash)
        except ValueError as e:
            return None, str(e)
        
        # Check if username exists
        if await self.user_repository.exists_by_username(username):
            return None, "Username already exists"
        
        # Check if email exists
        if await self.user_repository.exists_by_email(email_obj):
            return None, "Email already registered"
        
        # Create user
        user = User(
            username=username,
            email=email_obj,
            password_hash=password_hash_obj,
            full_name=full_name,
            roles=[UserRole(role) for role in (roles or [UserRole.VIEWER.value])],
            status=UserStatus.ACTIVE,
            email_verified=auto_verify_email
        )
        
        await self.user_repository.save(user)
        logger.info(f"Created new user: {username}")
        
        return user, None
    
    async def update_user_roles(
        self,
        user_id: UUID,
        roles: list[str]
    ) -> Tuple[Optional[User], Optional[str]]:
        """Update user roles."""
        user = await self.user_repository.find_by_id(user_id)
        if not user:
            return None, "User not found"
        
        # Validate roles
        try:
            new_roles = [UserRole(role) for role in roles]
        except ValueError as e:
            return None, f"Invalid role: {e}"
        
        user.roles = new_roles
        await self.user_repository.save(user)
        
        logger.info(f"Updated roles for user {user.username}: {roles}")
        return user, None
    
    async def suspend_user(
        self,
        user_id: UUID,
        reason: Optional[str] = None
    ) -> Tuple[Optional[User], Optional[str]]:
        """Suspend a user account."""
        user = await self.user_repository.find_by_id(user_id)
        if not user:
            return None, "User not found"
        
        user.status = UserStatus.SUSPENDED
        if reason:
            user.metadata["suspension_reason"] = reason
        user.metadata["suspended_at"] = datetime.utcnow().isoformat()
        
        await self.user_repository.save(user)
        
        logger.info(f"Suspended user {user.username}: {reason}")
        return user, None
    
    async def reactivate_user(
        self,
        user_id: UUID
    ) -> Tuple[Optional[User], Optional[str]]:
        """Reactivate a suspended user account."""
        user = await self.user_repository.find_by_id(user_id)
        if not user:
            return None, "User not found"
        
        user.status = UserStatus.ACTIVE
        user.metadata.pop("suspension_reason", None)
        user.metadata.pop("suspended_at", None)
        
        await self.user_repository.save(user)
        
        logger.info(f"Reactivated user {user.username}")
        return user, None
    
    async def delete_user(
        self,
        user_id: UUID,
        cascade_api_keys: bool = True
    ) -> bool:
        """Delete a user and optionally their API keys."""
        user = await self.user_repository.find_by_id(user_id)
        if not user:
            return False
        
        # Delete API keys if requested
        if cascade_api_keys:
            api_keys = await self.api_key_repository.find_by_user_id(user_id)
            for key in api_keys:
                await self.api_key_repository.delete(key.id)
        
        # Delete user
        await self.user_repository.delete(user_id)
        
        logger.info(f"Deleted user {user.username}")
        return True