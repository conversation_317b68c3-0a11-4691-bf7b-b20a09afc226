"""Authentication repository interfaces."""

from abc import ABC, abstractmethod
from typing import Optional, List
from uuid import UUI<PERSON>

from .entities import User, <PERSON><PERSON><PERSON>, RefreshToken
from .value_objects import Email


class UserRepository(ABC):
    """Repository interface for User aggregate."""
    
    @abstractmethod
    async def find_by_id(self, user_id: UUID) -> Optional[User]:
        """Find user by ID."""
        pass
    
    @abstractmethod
    async def find_by_username(self, username: str) -> Optional[User]:
        """Find user by username."""
        pass
    
    @abstractmethod
    async def find_by_email(self, email: Email) -> Optional[User]:
        """Find user by email."""
        pass
    
    @abstractmethod
    async def save(self, user: User) -> None:
        """Save user."""
        pass
    
    @abstractmethod
    async def delete(self, user_id: UUID) -> None:
        """Delete user."""
        pass
    
    @abstractmethod
    async def list_users(
        self,
        skip: int = 0,
        limit: int = 100,
        include_inactive: bool = False
    ) -> List[User]:
        """List users with pagination."""
        pass
    
    @abstractmethod
    async def exists_by_username(self, username: str) -> bool:
        """Check if username exists."""
        pass
    
    @abstractmethod
    async def exists_by_email(self, email: Email) -> bool:
        """Check if email exists."""
        pass


class APIKeyRepository(ABC):
    """Repository interface for API keys."""
    
    @abstractmethod
    async def find_by_id(self, key_id: UUID) -> Optional[APIKey]:
        """Find API key by ID."""
        pass
    
    @abstractmethod
    async def find_by_prefix(self, prefix: str) -> Optional[APIKey]:
        """Find API key by prefix."""
        pass
    
    @abstractmethod
    async def find_by_user_id(self, user_id: UUID) -> List[APIKey]:
        """Find all API keys for a user."""
        pass
    
    @abstractmethod
    async def save(self, api_key: APIKey) -> None:
        """Save API key."""
        pass
    
    @abstractmethod
    async def delete(self, key_id: UUID) -> None:
        """Delete API key."""
        pass
    
    @abstractmethod
    async def list_active_keys(
        self,
        skip: int = 0,
        limit: int = 100
    ) -> List[APIKey]:
        """List active API keys."""
        pass


class RefreshTokenRepository(ABC):
    """Repository interface for refresh tokens."""
    
    @abstractmethod
    async def find_by_token_hash(self, token_hash: str) -> Optional[RefreshToken]:
        """Find refresh token by hash."""
        pass
    
    @abstractmethod
    async def find_by_user_id(self, user_id: UUID) -> List[RefreshToken]:
        """Find all refresh tokens for a user."""
        pass
    
    @abstractmethod
    async def save(self, refresh_token: RefreshToken) -> None:
        """Save refresh token."""
        pass
    
    @abstractmethod
    async def delete(self, token_id: UUID) -> None:
        """Delete refresh token."""
        pass
    
    @abstractmethod
    async def revoke_all_for_user(self, user_id: UUID) -> None:
        """Revoke all refresh tokens for a user."""
        pass
    
    @abstractmethod
    async def cleanup_expired(self) -> int:
        """Clean up expired tokens and return count deleted."""
        pass