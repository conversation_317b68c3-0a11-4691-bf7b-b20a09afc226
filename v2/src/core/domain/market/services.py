"""Market domain services - business logic that doesn't belong to a single entity."""

from dataclasses import dataclass
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
import numpy as np

from ..shared.exceptions import BusinessRuleViolation, ValidationException
from .entities import Market, PriceObservation, ExchangeRateObservation
from .value_objects import (
    MarketId, MarketPair, IntegrationScore, Price, 
    Currency, Commodity, ControlStatus
)


@dataclass
class TransmissionMetrics:
    """Price transmission metrics between markets."""
    
    market_pair: MarketPair
    commodity: Commodity
    period_start: datetime
    period_end: datetime
    correlation: float
    beta_coefficient: float
    adjustment_speed: float
    half_life_days: float
    threshold: Optional[float] = None
    regime: Optional[str] = None


@dataclass
class IntegrationMetrics:
    """Market integration metrics."""
    
    integrated_pairs: List[MarketPair]
    non_integrated_pairs: List[MarketPair]
    average_transmission_speed: float
    spatial_decay_parameter: float
    integration_score: float  # 0-1 score


class MarketIntegrationService:
    """Calculate market integration metrics between market pairs."""
    
    def calculate_integration(
        self,
        market_pair: MarketPair,
        prices_source: List[PriceObservation],
        prices_target: List[PriceObservation],
        method: str = "correlation"
    ) -> IntegrationScore:
        """
        Calculate integration score between two markets.
        
        Args:
            market_pair: The pair of markets to analyze
            prices_source: Price observations from source market
            prices_target: Price observations from target market
            method: Integration calculation method
            
        Returns:
            IntegrationScore with calculated metrics
        """
        if not prices_source or not prices_target:
            raise ValidationException("Cannot calculate integration without price data")
        
        # Align price series by date
        aligned_prices = self._align_price_series(prices_source, prices_target)
        
        if len(aligned_prices) < 10:
            raise BusinessRuleViolation(
                "Insufficient overlapping observations for integration analysis"
            )
        
        if method == "correlation":
            score, confidence = self._calculate_correlation(aligned_prices)
        elif method == "cointegration":
            score, confidence = self._calculate_cointegration(aligned_prices)
        else:
            raise ValidationException(f"Unsupported integration method: {method}")
        
        return IntegrationScore(
            score=score,
            method=method,
            confidence=confidence
        )
    
    def _align_price_series(
        self,
        prices1: List[PriceObservation],
        prices2: List[PriceObservation]
    ) -> List[Tuple[PriceObservation, PriceObservation]]:
        """Align two price series by matching observation dates."""
        # Create date-indexed dictionaries
        prices1_by_date = {p.observed_date.date(): p for p in prices1}
        prices2_by_date = {p.observed_date.date(): p for p in prices2}
        
        # Find common dates
        common_dates = set(prices1_by_date.keys()) & set(prices2_by_date.keys())
        
        # Return aligned pairs
        return [
            (prices1_by_date[date], prices2_by_date[date])
            for date in sorted(common_dates)
        ]
    
    def _calculate_correlation(
        self,
        aligned_prices: List[Tuple[PriceObservation, PriceObservation]]
    ) -> Tuple[float, float]:
        """Calculate correlation-based integration score."""
        prices1 = np.array([float(p[0].price.amount) for p in aligned_prices])
        prices2 = np.array([float(p[1].price.amount) for p in aligned_prices])
        
        # Calculate correlation coefficient
        correlation = np.corrcoef(prices1, prices2)[0, 1]
        
        # Calculate confidence based on sample size and correlation strength
        n = len(aligned_prices)
        confidence = min(1.0, (n / 100) * abs(correlation))
        
        # Transform correlation to 0-1 scale
        score = (correlation + 1) / 2
        
        return score, confidence
    
    def _calculate_cointegration(
        self,
        aligned_prices: List[Tuple[PriceObservation, PriceObservation]]
    ) -> Tuple[float, float]:
        """Calculate cointegration-based integration score."""
        # Simplified placeholder - real implementation would use statsmodels
        correlation_score, _ = self._calculate_correlation(aligned_prices)
        
        # Adjust for cointegration (placeholder logic)
        score = correlation_score * 0.9
        confidence = 0.8
        
        return score, confidence


class PriceTransmissionService:
    """Domain service for price transmission analysis."""
    
    def calculate_transmission(
        self,
        source_prices: List[PriceObservation],
        target_prices: List[PriceObservation],
        market_pair: MarketPair
    ) -> TransmissionMetrics:
        """Calculate price transmission metrics between markets."""
        if not source_prices or not target_prices:
            raise BusinessRuleViolation("Cannot calculate transmission without price data")
        
        # Ensure same commodity
        commodity = source_prices[0].commodity
        if any(p.commodity != commodity for p in source_prices + target_prices):
            raise BusinessRuleViolation("All prices must be for the same commodity")
        
        # Align time series
        source_series = self._create_price_series(source_prices)
        target_series = self._create_price_series(target_prices)
        aligned_source, aligned_target = self._align_series(source_series, target_series)
        
        if len(aligned_source) < 10:
            raise BusinessRuleViolation("Insufficient data for transmission analysis (need at least 10 observations)")
        
        # Calculate metrics
        correlation = np.corrcoef(aligned_source, aligned_target)[0, 1]
        beta_coefficient = self._calculate_beta(aligned_source, aligned_target)
        adjustment_speed = self._calculate_adjustment_speed(aligned_source, aligned_target)
        half_life_days = self._calculate_half_life(adjustment_speed)
        
        return TransmissionMetrics(
            market_pair=market_pair,
            commodity=commodity,
            period_start=min(p.observed_date for p in source_prices),
            period_end=max(p.observed_date for p in source_prices),
            correlation=correlation,
            beta_coefficient=beta_coefficient,
            adjustment_speed=adjustment_speed,
            half_life_days=half_life_days
        )
    
    def identify_transmission_breaks(
        self,
        transmission_series: List[TransmissionMetrics],
        threshold: float = 0.5
    ) -> List[datetime]:
        """Identify structural breaks in transmission."""
        if len(transmission_series) < 2:
            return []
        
        breaks = []
        for i in range(1, len(transmission_series)):
            prev = transmission_series[i-1]
            curr = transmission_series[i]
            
            # Check for significant change in transmission
            correlation_change = abs(curr.correlation - prev.correlation)
            beta_change = abs(curr.beta_coefficient - prev.beta_coefficient)
            
            if correlation_change > threshold or beta_change > threshold:
                breaks.append(curr.period_start)
        
        return breaks
    
    def _create_price_series(self, observations: List[PriceObservation]) -> Dict[datetime, Decimal]:
        """Create time series from observations."""
        series = {}
        for obs in observations:
            date = obs.observed_date.date()
            if date in series:
                # Average if multiple observations on same date
                series[date] = (series[date] + obs.price.amount) / 2
            else:
                series[date] = obs.price.amount
        return series
    
    def _align_series(
        self,
        series1: Dict[datetime, Decimal],
        series2: Dict[datetime, Decimal]
    ) -> Tuple[List[float], List[float]]:
        """Align two time series to common dates."""
        common_dates = sorted(set(series1.keys()) & set(series2.keys()))
        aligned1 = [float(series1[date]) for date in common_dates]
        aligned2 = [float(series2[date]) for date in common_dates]
        return aligned1, aligned2
    
    def _calculate_beta(self, source: List[float], target: List[float]) -> float:
        """Calculate beta coefficient (price transmission elasticity)."""
        # Simple OLS regression: target = alpha + beta * source
        source_array = np.array(source)
        target_array = np.array(target)
        
        # Add constant term
        X = np.column_stack([np.ones(len(source)), source_array])
        
        # OLS estimation
        beta_hat = np.linalg.lstsq(X, target_array, rcond=None)[0]
        return float(beta_hat[1])  # Return slope coefficient
    
    def _calculate_adjustment_speed(self, source: List[float], target: List[float]) -> float:
        """Calculate speed of adjustment using error correction model."""
        # Simplified ECM: Δtarget = α(target[-1] - β*source[-1]) + ε
        source_array = np.array(source)
        target_array = np.array(target)
        
        # Calculate first differences
        target_diff = np.diff(target_array)
        
        # Calculate error correction term
        beta = self._calculate_beta(source[:-1], target[:-1])
        error_term = target_array[:-1] - beta * source_array[:-1]
        
        # Estimate adjustment speed
        alpha = np.corrcoef(target_diff, error_term)[0, 1]
        return abs(float(alpha))  # Return absolute value
    
    def _calculate_half_life(self, adjustment_speed: float) -> float:
        """Calculate half-life of price shock."""
        if adjustment_speed <= 0 or adjustment_speed >= 1:
            return float('inf')
        return -np.log(2) / np.log(1 - adjustment_speed)


class PriceValidationService:
    """Validate and detect outliers in price observations."""
    
    def __init__(self, outlier_threshold: float = 3.0):
        """Initialize with outlier detection threshold (z-score)."""
        self.outlier_threshold = outlier_threshold
    
    def validate_price_observation(
        self,
        observation: PriceObservation,
        historical_prices: List[PriceObservation],
        commodity: Commodity
    ) -> bool:
        """
        Validate a price observation against historical data.
        
        Returns:
            True if price is valid, False if outlier
        """
        if not historical_prices:
            # No historical data - accept the price
            return True
        
        # Filter historical prices for same commodity
        same_commodity_prices = [
            p for p in historical_prices
            if p.commodity == commodity
        ]
        
        if len(same_commodity_prices) < 5:
            # Insufficient history for statistical validation
            return True
        
        # Calculate statistics
        prices = [float(p.price.amount) for p in same_commodity_prices]
        mean_price = np.mean(prices)
        std_price = np.std(prices)
        
        if std_price == 0:
            # No variation in historical prices
            return float(observation.price.amount) == mean_price
        
        # Check if observation is an outlier
        return observation.is_outlier(
            Decimal(str(mean_price)),
            Decimal(str(std_price)),
            self.outlier_threshold
        )
    
    def detect_price_anomalies(
        self,
        market: Market,
        observations: List[PriceObservation],
        window_days: int = 30
    ) -> List[PriceObservation]:
        """
        Detect anomalous price observations in a market.
        
        Args:
            market: The market to analyze
            observations: Price observations to check
            window_days: Historical window for comparison
            
        Returns:
            List of anomalous observations
        """
        anomalies = []
        
        for obs in observations:
            # Get historical window
            window_start = obs.observed_date - timedelta(days=window_days)
            historical = [
                p for p in observations
                if window_start <= p.observed_date < obs.observed_date
            ]
            
            if not self.validate_price_observation(obs, historical, obs.commodity):
                anomalies.append(obs)
        
        return anomalies


class CurrencyConversionService:
    """Handle currency conversions for price analysis."""
    
    def convert_prices_to_common_currency(
        self,
        observations: List[PriceObservation],
        exchange_rates: List[ExchangeRateObservation],
        target_currency: Currency = Currency.USD
    ) -> List[PriceObservation]:
        """
        Convert all price observations to a common currency.
        
        Args:
            observations: Price observations to convert
            exchange_rates: Available exchange rate observations
            target_currency: Target currency for conversion
            
        Returns:
            List of price observations in target currency
        """
        converted = []
        
        for obs in observations:
            if obs.price.currency == target_currency:
                converted.append(obs)
                continue
            
            # Find closest exchange rate
            rate_obs = self._find_closest_exchange_rate(
                obs.market_id,
                obs.observed_date,
                obs.price.currency,
                target_currency,
                exchange_rates
            )
            
            if rate_obs is None:
                # Skip if no exchange rate available
                continue
            
            # Convert price
            converted_price = obs.price.convert_to(
                target_currency,
                rate_obs.get_effective_rate()
            )
            
            # Create new observation with converted price
            converted_obs = PriceObservation(
                market_id=obs.market_id,
                commodity=obs.commodity,
                price=converted_price,
                observed_date=obs.observed_date,
                source=f"{obs.source} (converted)",
                quality=obs.quality,
                observations_count=obs.observations_count
            )
            
            converted.append(converted_obs)
        
        return converted
    
    def _find_closest_exchange_rate(
        self,
        market_id: MarketId,
        date: datetime,
        from_currency: Currency,
        to_currency: Currency,
        rates: List[ExchangeRateObservation]
    ) -> Optional[ExchangeRateObservation]:
        """Find the closest exchange rate observation for a given date and market."""
        # Filter relevant rates
        relevant_rates = [
            r for r in rates
            if r.market_id == market_id
            and r.exchange_rate.from_currency == from_currency
            and r.exchange_rate.to_currency == to_currency
        ]
        
        if not relevant_rates:
            return None
        
        # Find closest by date
        return min(
            relevant_rates,
            key=lambda r: abs((r.observed_date - date).total_seconds())
        )


class ConflictImpactService:
    """Analyze impact of conflict on market operations."""
    
    def assess_market_accessibility(
        self,
        market: Market,
        control_changes: List[Tuple[datetime, ControlStatus]]
    ) -> float:
        """
        Assess market accessibility score based on control status changes.
        
        Returns:
            Accessibility score (0-1, where 1 is fully accessible)
        """
        if not control_changes:
            return 1.0 if market.control_status != ControlStatus.CONTESTED else 0.5
        
        # Calculate time-weighted accessibility
        total_time = 0.0
        accessible_time = 0.0
        
        for i, (date, status) in enumerate(control_changes):
            if i < len(control_changes) - 1:
                duration = (control_changes[i + 1][0] - date).days
            else:
                duration = (datetime.utcnow() - date).days
            
            total_time += duration
            
            if status in [ControlStatus.GOVERNMENT, ControlStatus.HOUTHI]:
                accessible_time += duration
            elif status == ControlStatus.CONTESTED:
                accessible_time += duration * 0.5
        
        return accessible_time / total_time if total_time > 0 else 0.0
    
    def identify_trade_disruptions(
        self,
        source_market: Market,
        target_market: Market,
        control_changes: List[Tuple[datetime, MarketId, ControlStatus]]
    ) -> List[Tuple[datetime, str]]:
        """
        Identify potential trade disruptions between markets due to control changes.
        
        Returns:
            List of (date, disruption_reason) tuples
        """
        disruptions = []
        
        for date, market_id, new_status in control_changes:
            if market_id == source_market.market_id:
                if new_status == ControlStatus.CONTESTED:
                    disruptions.append((date, "Source market contested"))
                elif source_market.control_status != new_status:
                    disruptions.append((date, "Source market control changed"))
            
            elif market_id == target_market.market_id:
                if new_status == ControlStatus.CONTESTED:
                    disruptions.append((date, "Target market contested"))
                elif target_market.control_status != new_status:
                    disruptions.append((date, "Target market control changed"))
        
        # Check for cross-line trade restrictions
        if source_market.control_status != target_market.control_status:
            if source_market.control_status in [ControlStatus.GOVERNMENT, ControlStatus.HOUTHI]:
                if target_market.control_status in [ControlStatus.GOVERNMENT, ControlStatus.HOUTHI]:
                    disruptions.append((
                        datetime.utcnow(),
                        "Cross-line trade between different control zones"
                    ))
        
        return sorted(disruptions, key=lambda x: x[0])


class MarketNetworkAnalysisService:
    """Analyze network properties of market system."""
    
    def __init__(self, integration_service: MarketIntegrationService):
        """Initialize with integration service."""
        self.integration_service = integration_service
    
    def analyze_network_integration(
        self,
        markets: List[Market],
        price_observations: Dict[MarketId, List[PriceObservation]],
        distance_threshold: float = 500.0,
        correlation_threshold: float = 0.7
    ) -> IntegrationMetrics:
        """Analyze integration for entire market network."""
        integrated_pairs = []
        non_integrated_pairs = []
        transmission_speeds = []
        
        # Analyze all market pairs
        for i, market1 in enumerate(markets):
            for market2 in markets[i+1:]:
                # Calculate distance
                distance = market1.coordinates.distance_to(market2.coordinates)
                
                # Skip if too far apart
                if distance > distance_threshold:
                    continue
                
                market_pair = MarketPair(
                    source=market1.market_id,
                    target=market2.market_id,
                    distance_km=distance
                )
                
                # Get price observations
                prices1 = price_observations.get(market1.market_id, [])
                prices2 = price_observations.get(market2.market_id, [])
                
                if not prices1 or not prices2:
                    non_integrated_pairs.append(market_pair)
                    continue
                
                try:
                    # Calculate integration score
                    score = self.integration_service.calculate_integration(
                        market_pair, prices1, prices2
                    )
                    
                    # Classify as integrated or not
                    if score.score >= correlation_threshold:
                        integrated_pairs.append(market_pair)
                        transmission_speeds.append(score.confidence)
                    else:
                        non_integrated_pairs.append(market_pair)
                        
                except BusinessRuleViolation:
                    # Insufficient data
                    non_integrated_pairs.append(market_pair)
        
        # Calculate summary metrics
        avg_transmission_speed = (
            np.mean(transmission_speeds) if transmission_speeds else 0.0
        )
        
        # Estimate spatial decay parameter
        spatial_decay = self._estimate_spatial_decay(
            integrated_pairs, transmission_speeds
        )
        
        # Calculate integration score
        total_pairs = len(integrated_pairs) + len(non_integrated_pairs)
        integration_score = len(integrated_pairs) / total_pairs if total_pairs > 0 else 0.0
        
        return IntegrationMetrics(
            integrated_pairs=integrated_pairs,
            non_integrated_pairs=non_integrated_pairs,
            average_transmission_speed=float(avg_transmission_speed),
            spatial_decay_parameter=spatial_decay,
            integration_score=integration_score
        )
    
    def _estimate_spatial_decay(
        self,
        market_pairs: List[MarketPair],
        transmission_speeds: List[float]
    ) -> float:
        """Estimate how transmission speed decays with distance."""
        if not market_pairs or not transmission_speeds:
            return 0.0
        
        distances = [pair.distance_km for pair in market_pairs if pair.distance_km]
        
        if not distances:
            return 0.0
        
        # Simple exponential decay model: speed = exp(-decay * distance)
        # Taking log: log(speed) = -decay * distance
        log_speeds = np.log(transmission_speeds)
        
        # Estimate decay parameter
        decay = -np.corrcoef(distances, log_speeds)[0, 1]
        return max(0.0, float(decay))  # Ensure non-negative