"""Value objects for the Market bounded context."""

from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
from typing import Optional

from ..shared.entities import ValueObject
from ..shared.exceptions import ValidationException


@dataclass(frozen=True)
class MarketId(ValueObject):
    """Market identifier value object."""
    
    value: str
    
    def __post_init__(self) -> None:
        """Validate market ID."""
        if not self.value or not self.value.strip():
            raise ValidationException("Market ID cannot be empty")
        if len(self.value) > 50:
            raise ValidationException("Market ID cannot exceed 50 characters")


@dataclass(frozen=True)
class Coordinates(ValueObject):
    """Geographic coordinates value object."""
    
    latitude: float
    longitude: float
    
    def __post_init__(self) -> None:
        """Validate coordinates."""
        if not -90 <= self.latitude <= 90:
            raise ValidationException(f"Invalid latitude: {self.latitude}")
        if not -180 <= self.longitude <= 180:
            raise ValidationException(f"Invalid longitude: {self.longitude}")
    
    def distance_to(self, other: 'Coordinates') -> float:
        """Calculate distance to another coordinate in kilometers using Haversine formula."""
        from math import radians, sin, cos, sqrt, atan2
        
        R = 6371  # Earth's radius in kilometers
        
        lat1, lon1 = radians(self.latitude), radians(self.longitude)
        lat2, lon2 = radians(other.latitude), radians(other.longitude)
        
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * atan2(sqrt(a), sqrt(1-a))
        
        return R * c


class MarketType(Enum):
    """Market type enumeration."""
    
    WHOLESALE = "wholesale"
    RETAIL = "retail"
    BORDER = "border"
    PORT = "port"
    RURAL = "rural"
    URBAN = "urban"


class ControlStatus(Enum):
    """Market control status enumeration."""
    
    GOVERNMENT = "government"
    HOUTHI = "houthi"
    CONTESTED = "contested"
    UNKNOWN = "unknown"


class Currency(Enum):
    """Supported currencies."""
    
    YER = "YER"  # Yemeni Rial
    USD = "USD"  # US Dollar
    SAR = "SAR"  # Saudi Riyal
    
    @property
    def symbol(self) -> str:
        """Get currency symbol."""
        symbols = {
            "YER": "﷼",
            "USD": "$",
            "SAR": "ر.س"
        }
        return symbols.get(self.value, self.value)


@dataclass(frozen=True)
class Price(ValueObject):
    """Price value object with currency information."""
    
    amount: Decimal
    currency: Currency
    unit: str  # e.g., "kg", "50kg bag", "liter"
    
    def __post_init__(self) -> None:
        """Validate price."""
        if self.amount < 0:
            raise ValidationException("Price amount cannot be negative")
        if not self.unit:
            raise ValidationException("Unit is required")
        # Validation complete
    
    def convert_to(self, target_currency: Currency, exchange_rate: Decimal) -> 'Price':
        """Convert price to different currency."""
        if self.currency == target_currency:
            return self
        
        if exchange_rate <= 0:
            raise ValidationException("Exchange rate must be positive")
        
        # Convert based on exchange rate direction
        if self.currency == Currency.YER and target_currency == Currency.USD:
            converted_amount = self.amount / exchange_rate
        elif self.currency == Currency.USD and target_currency == Currency.YER:
            converted_amount = self.amount * exchange_rate
        else:
            # For other conversions, assume rate is from source to target
            converted_amount = self.amount * exchange_rate
        
        return Price(
            amount=converted_amount,
            currency=target_currency,
            unit=self.unit
        )
    
    def to_standard_unit(self, conversion_factor: Decimal, standard_unit: str) -> 'Price':
        """Convert price to standard unit (e.g., from 50kg bag to kg)."""
        if conversion_factor <= 0:
            raise ValidationException("Conversion factor must be positive")
        
        return Price(
            amount=self.amount / conversion_factor,
            currency=self.currency,
            unit=standard_unit
        )


@dataclass(frozen=True)
class Commodity(ValueObject):
    """Commodity value object."""
    
    code: str
    name: str
    category: str  # "imported", "local", "agricultural", "fuel", "other"
    standard_unit: str
    perishable: bool = False
    
    def __post_init__(self) -> None:
        """Validate commodity."""
        if not self.code:
            raise ValidationException("Commodity code is required")
        if not self.name:
            raise ValidationException("Commodity name is required")
        if self.category not in ["imported", "local", "agricultural", "fuel", "other"]:
            raise ValidationException(f"Invalid commodity category: {self.category}")
        if not self.standard_unit:
            raise ValidationException("Standard unit is required")
        # Validation complete


@dataclass(frozen=True)
class MarketPair(ValueObject):
    """Represents a pair of markets for transmission analysis."""
    
    source: MarketId
    target: MarketId
    distance_km: Optional[float] = None
    
    def __post_init__(self) -> None:
        """Validate market pair."""
        if self.source == self.target:
            raise ValidationException("Source and target markets must be different")
        if self.distance_km is not None and self.distance_km < 0:
            raise ValidationException("Distance cannot be negative")
        # Validation complete


@dataclass(frozen=True)
class ExchangeRate(ValueObject):
    """Exchange rate value object."""
    
    from_currency: Currency
    to_currency: Currency
    rate: Decimal
    rate_type: str  # "official_cby_aden", "official_cby_sanaa", "parallel"
    
    def __post_init__(self) -> None:
        """Validate exchange rate."""
        if self.from_currency == self.to_currency:
            raise ValidationException("Cannot have exchange rate between same currency")
        if self.rate <= 0:
            raise ValidationException("Exchange rate must be positive")
        if self.rate_type not in ["official_cby_aden", "official_cby_sanaa", "parallel"]:
            raise ValidationException(f"Invalid rate type: {self.rate_type}")
        # Validation complete
    
    def inverse(self) -> 'ExchangeRate':
        """Get inverse exchange rate."""
        return ExchangeRate(
            from_currency=self.to_currency,
            to_currency=self.from_currency,
            rate=Decimal("1") / self.rate,
            rate_type=self.rate_type
        )


@dataclass(frozen=True)
class IntegrationScore(ValueObject):
    """Market integration score value object."""
    
    score: float  # 0-1, where 1 is perfectly integrated
    method: str  # "correlation", "cointegration", "threshold_vecm"
    confidence: float  # Confidence level of the score (0-1)
    
    def __post_init__(self) -> None:
        """Validate integration score."""
        if not 0 <= self.score <= 1:
            raise ValidationException("Integration score must be between 0 and 1")
        if not 0 <= self.confidence <= 1:
            raise ValidationException("Confidence must be between 0 and 1")
        if self.method not in ["correlation", "cointegration", "threshold_vecm"]:
            raise ValidationException(f"Invalid integration method: {self.method}")
        # Validation complete
    
    @property
    def integration_level(self) -> str:
        """Get qualitative integration level."""
        if self.score >= 0.8:
            return "high"
        elif self.score >= 0.5:
            return "moderate"
        elif self.score >= 0.2:
            return "low"
        else:
            return "none"