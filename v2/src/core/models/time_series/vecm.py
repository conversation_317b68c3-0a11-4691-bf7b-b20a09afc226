"""Vector Error Correction Model (VECM) implementation."""

from typing import Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

from ..interfaces import Model, ModelSpecification


class VECMModel(Model):
    """
    Vector Error Correction Model for cointegrated time series.
    
    This is part of Tier 2 of the three-tier approach for commodity-specific
    market integration analysis.
    """
    
    @property
    def name(self) -> str:
        """Model name."""
        return "Vector Error Correction Model (VECM)"
    
    @property
    def required_data_structure(self) -> str:
        """Required data structure."""
        return "time_series"
    
    def __init__(self, specification: ModelSpecification):
        """Initialize VECM model."""
        super().__init__(specification)
        
        # VECM-specific parameters
        self.endogenous_vars = specification.parameters.get("endogenous_vars", [])
        self.exogenous_vars = specification.parameters.get("exogenous_vars", [])
        self.deterministic = specification.parameters.get("deterministic", "ci")
        self.seasons = specification.parameters.get("seasons", 0)
        
        # Lag order (k_ar_diff is lags in differences)
        self.k_ar_diff = specification.parameters.get("k_ar_diff", None)
        self.lag_order_selection = specification.parameters.get("lag_order_selection", "aic")
        
        # Cointegration rank
        self.coint_rank = specification.parameters.get("coint_rank", None)
        self.rank_test_method = specification.parameters.get("rank_test_method", "trace")
        
        # Restrictions
        self.alpha_restrictions = specification.parameters.get("alpha_restrictions", None)
        self.beta_restrictions = specification.parameters.get("beta_restrictions", None)
    
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate time series data for VECM."""
        errors = []
        
        # Check endogenous variables exist
        missing_vars = set(self.endogenous_vars) - set(data.columns)
        if missing_vars:
            errors.append(f"Missing endogenous variables: {missing_vars}")
        
        # Check exogenous variables if specified
        if self.exogenous_vars:
            missing_exog = set(self.exogenous_vars) - set(data.columns)
            if missing_exog:
                errors.append(f"Missing exogenous variables: {missing_exog}")
        
        # Check minimum observations
        n_vars = len(self.endogenous_vars)
        min_obs = 2 * n_vars * (self.k_ar_diff or 4) + n_vars + 1
        if len(data) < min_obs:
            errors.append(
                f"Insufficient observations: have {len(data)}, need at least {min_obs}"
            )
        
        # Check for missing values
        for var in self.endogenous_vars:
            if var in data.columns and data[var].isna().any():
                errors.append(f"Missing values in {var}")
        
        # Check stationarity properties (simplified)
        # In practice, would run unit root tests
        for var in self.endogenous_vars:
            if var in data.columns:
                # Simple check for constant series
                if data[var].nunique() == 1:
                    errors.append(f"No variation in {var}")
        
        return errors
    
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for VECM estimation."""
        prepared = data.copy()
        
        # Ensure datetime index
        if not isinstance(prepared.index, pd.DatetimeIndex):
            date_col = self.specification.parameters.get("date_column", "date")
            if date_col in prepared.columns:
                prepared[date_col] = pd.to_datetime(prepared[date_col])
                prepared = prepared.set_index(date_col)
                prepared = prepared.sort_index()
        
        # Handle frequency
        if self.specification.parameters.get("interpolate_missing", False):
            # Interpolate to regular frequency
            freq = self.specification.parameters.get("frequency", "D")
            idx = pd.date_range(prepared.index.min(), prepared.index.max(), freq=freq)
            prepared = prepared.reindex(idx)
            
            # Interpolate missing values
            for var in self.endogenous_vars:
                if var in prepared.columns:
                    prepared[var] = prepared[var].interpolate(method="time")
        
        # Add seasonal dummies if specified
        if self.seasons > 0:
            for s in range(1, self.seasons):
                prepared[f"season_{s}"] = (
                    (prepared.index.month % self.seasons) == s
                ).astype(int)
        
        # Add structural break dummies if specified
        break_dates = self.specification.parameters.get("structural_breaks", [])
        for i, break_date in enumerate(break_dates):
            break_date = pd.to_datetime(break_date)
            prepared[f"break_{i}"] = (prepared.index >= break_date).astype(int)
        
        # Select only required columns
        cols_to_keep = self.endogenous_vars.copy()
        if self.exogenous_vars:
            cols_to_keep.extend(self.exogenous_vars)
        
        # Add any created dummies
        dummy_cols = [col for col in prepared.columns if col.startswith(("season_", "break_"))]
        cols_to_keep.extend(dummy_cols)
        
        # Keep only columns that exist
        cols_to_keep = [col for col in cols_to_keep if col in prepared.columns]
        prepared = prepared[cols_to_keep]
        
        # Drop any remaining NaN values
        prepared = prepared.dropna()
        
        return prepared
    
    def get_diagnostics(self) -> List[str]:
        """Get applicable diagnostic tests for VECM."""
        return [
            "serial_correlation",  # Ljung-Box or LM test
            "normality",          # Jarque-Bera on residuals
            "heteroskedasticity", # ARCH test
            "stability",          # Parameter stability
            "weak_exogeneity",    # Test alpha restrictions
            "identification"      # Test beta restrictions
        ]
    
    def get_cointegration_tests(self) -> List[str]:
        """Get available cointegration tests."""
        return [
            "johansen_trace",
            "johansen_max_eigenvalue",
            "engle_granger",
            "phillips_ouliaris"
        ]
    
    def select_lag_order(self, data: pd.DataFrame, max_lags: int = 10) -> int:
        """Select optimal lag order using information criteria."""
        # This would implement lag order selection
        # For now, return default
        return self.k_ar_diff or 2
    
    def test_cointegration_rank(
        self,
        data: pd.DataFrame,
        method: str = "trace"
    ) -> Tuple[int, Dict[str, float]]:
        """Test for cointegration rank using Johansen test.
        
        Args:
            data: DataFrame with time series variables
            method: 'trace' or 'maxeig' for test type
            
        Returns:
            Tuple of (optimal_rank, test_results)
        """
        try:
            from statsmodels.tsa.vector_ar.vecm import coint_johansen
            
            # Prepare data
            y_data = data[self.specification.dependent_variable].values
            if len(y_data.shape) == 1:
                y_data = y_data.reshape(-1, 1)
            
            # Johansen test
            # det_order: -1 (no det), 0 (no trend), 1 (linear trend)
            det_order = 0 if self.deterministic == "ci" else 1
            
            joh_result = coint_johansen(y_data, det_order=det_order, k_ar_diff=self.n_lags-1)
            
            # Get test statistics
            if method == "trace":
                test_stats = joh_result.lr1  # Trace statistics
                crit_vals = joh_result.cvt  # Critical values for trace
            else:  # maxeig
                test_stats = joh_result.lr2  # Max eigenvalue statistics
                crit_vals = joh_result.cvm  # Critical values for max eigenvalue
            
            # Determine rank: number of test stats > critical value at 5%
            rank = 0
            test_results = {}
            
            for i, (stat, crit) in enumerate(zip(test_stats, crit_vals[:, 1])):  # 5% level
                test_results[f"r_{i}"] = {
                    "test_stat": float(stat),
                    "critical_value_5%": float(crit),
                    "reject_null": stat > crit
                }
                if stat > crit:
                    rank = i + 1
            
            # Add summary info
            test_results["method"] = method
            test_results["selected_rank"] = rank
            test_results["eigenvalues"] = joh_result.eig.tolist()
            
            return rank, test_results
            
        except Exception as e:
            # Fallback to simple heuristic
            n_vars = len(self.specification.dependent_variable) if isinstance(
                self.specification.dependent_variable, list
            ) else 1
            
            # Conservative: assume 1 cointegrating relation for 2+ variables
            default_rank = min(1, n_vars - 1) if n_vars > 1 else 0
            
            return default_rank, {
                "error": str(e),
                "test_stat": np.nan,
                "critical_value": np.nan,
                "p_value": np.nan,
                "selected_rank": default_rank
            }