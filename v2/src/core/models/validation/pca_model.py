"""Principal Component Analysis model for dimensionality reduction.

This model uses PCA to reduce the dimensionality of price data
and identify the main patterns of variation.
"""

from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler

from ..interfaces import Model, ModelSpecification
from ....infrastructure.logging import Logger # Corrected import path
from ....infrastructure.estimators.validation.helpers.pca_interpretation import interpret_components
from ....infrastructure.estimators.validation.helpers.pca_quality_metrics import calculate_quality_metrics, identify_outliers, create_composite_index
from ....infrastructure.estimators.validation.helpers.pca_plotting import plot_pca_results

logger = Logger(__name__)


class PCAModel(Model):
    """PCA model for analyzing price variation patterns.
    
    This model uses Principal Component Analysis to:
    - Reduce dimensionality of price data
    - Identify main sources of price variation
    - Create composite price indices
    - Visualize market relationships
    """
    
    def __init__(self, specification: ModelSpecification):
        """Initialize PCA model.
        
        Args:
            specification: Model specification with PCA parameters
        """
        self.specification = specification
        self.n_components = specification.features.get('n_components', None)
        self.variance_threshold = specification.features.get('variance_threshold', 0.95)
        self.standardize = specification.features.get('standardize', True)
        self.center = specification.features.get('center', True)
        
        self.pca_object: Optional[PCA] = None
        self.scaler: Optional[StandardScaler] = None
        
    @property
    def name(self) -> str:
        """Model name for identification."""
        n_comp_str = self.n_components if self.n_components else 'auto'
        return f"PCAModel_{n_comp_str}components"
    
    @property
    def model_type(self) -> str:
        """Get model type."""
        return "validation"
    
    def validate_data(self, data: pd.DataFrame) -> List[str]:
        """Validate that data meets PCA requirements.
        
        Args:
            data: Input data to validate
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        # Check for numeric columns
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) < 2:
            errors.append(f"Insufficient numeric variables for PCA: {len(numeric_cols)} < 2")
        
        # Check for missing values
        if data[numeric_cols].isnull().any().any():
            missing_pct = data[numeric_cols].isnull().sum().sum() / (len(data) * len(numeric_cols)) * 100
            errors.append(f"Missing values detected ({missing_pct:.1f}% of data). "
                         "Consider imputation before PCA.")
        
        # Check sample size
        n_obs = len(data)
        n_vars = len(numeric_cols)
        if n_obs < n_vars:
            errors.append(f"More variables ({n_vars}) than observations ({n_obs}). "
                         "PCA may be unstable.")
        
        # Check for zero variance columns
        zero_var_cols = [col for col in numeric_cols if data[col].var() == 0]
        if zero_var_cols:
            errors.append(f"Zero variance columns found: {zero_var_cols}")
        
        return errors
    
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare data for PCA estimation.
        
        Args:
            data: DataFrame with variables for PCA
            
        Returns:
            Prepared DataFrame
        """
        logger.info("Preparing data for PCA Model")
        
        numeric_data = data.select_dtypes(include=[np.number])
        clean_data = numeric_data.dropna()
        
        if self.standardize or self.center:
            self.scaler = StandardScaler(with_std=self.standardize, with_mean=self.center)
            scaled_data = self.scaler.fit_transform(clean_data)
            prepared_data = pd.DataFrame(
                scaled_data,
                index=clean_data.index,
                columns=clean_data.columns
            )
            logger.info("Data standardized/centered.")
        else:
            prepared_data = clean_data
        
        return prepared_data
    
    def fit(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Fit PCA model to the data.
        
        Args:
            data: Prepared DataFrame with variables for PCA
            
        Returns:
            Dictionary with PCA results
        """
        validation_errors = self.validate_data(data)
        if validation_errors:
            raise ValueError(f"Data validation failed: {'; '.join(validation_errors)}")
            
        prepared_data = self.prepare_data(data)
        
        logger.info(f"Fitting PCA model with n_components={self.n_components}")
        
        # Determine number of components
        n_components_to_fit = self.n_components
        if n_components_to_fit is None:
            # Use variance threshold to determine components
            pca_temp = PCA()
            pca_temp.fit(prepared_data)
            cumsum_var = np.cumsum(pca_temp.explained_variance_ratio_)
            n_components_to_fit = np.argmax(cumsum_var >= self.variance_threshold) + 1
            n_components_to_fit = min(n_components_to_fit, len(prepared_data.columns))
            logger.info(f"Auto-selected {n_components_to_fit} components to explain "
                       f"{self.variance_threshold*100}% of variance")
        
        # Fit PCA
        self.pca_object = PCA(n_components=n_components_to_fit, random_state=42)
        scores = self.pca_object.fit_transform(prepared_data)
        
        # Create results dictionary
        results = {
            'n_components': self.pca_object.n_components_,
            'loadings': pd.DataFrame(
                self.pca_object.components_.T,
                index=prepared_data.columns,
                columns=[f'PC{i+1}' for i in range(self.pca_object.n_components_)]
            ),
            'scores': pd.DataFrame(
                scores,
                index=prepared_data.index,
                columns=[f'PC{i+1}' for i in range(self.pca_object.n_components_)]
            ),
            'explained_variance': pd.Series(
                self.pca_object.explained_variance_,
                index=[f'PC{i+1}' for i in range(self.pca_object.n_components_)]
            ),
            'explained_variance_ratio': pd.Series(
                self.pca_object.explained_variance_ratio_,
                index=[f'PC{i+1}' for i in range(self.pca_object.n_components_)]
            ),
            'cumulative_variance_ratio': pd.Series(
                np.cumsum(self.pca_object.explained_variance_ratio_),
                index=[f'PC{i+1}' for i in range(self.pca_object.n_components_)]
            ),
            'standardized': self.standardize,
            'centered': self.center,
            'scaler': self.scaler,
            'pca_object': self.pca_object
        }
        
        # Interpret components using helper
        results['component_interpretation'] = interpret_components(results['loadings'])
        
        # Calculate quality metrics using helper
        results['quality_metrics'] = calculate_quality_metrics(
            prepared_data, results
        )
        
        # Identify outliers using helper
        results['outliers'] = identify_outliers(results['scores'])
        
        return results
    
    def predict(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate predictions (component scores) from the fitted model.
        
        Args:
            data: New data to transform into component scores.
            
        Returns:
            DataFrame with predicted component scores.
        """
        if self.pca_object is None:
            raise RuntimeError("Model must be fitted before prediction.")
            
        logger.info("Generating component score predictions.")
        
        # Prepare new data using the same scaler if standardization was applied
        numeric_data = data.select_dtypes(include=[np.number])
        clean_data = numeric_data.dropna()
        
        if self.scaler:
            scaled_data = self.scaler.transform(clean_data)
            data_for_prediction = pd.DataFrame(
                scaled_data,
                index=clean_data.index,
                columns=clean_data.columns
            )
        else:
            data_for_prediction = clean_data
            
        scores = self.pca_object.transform(data_for_prediction)
        
        component_names = [f'PC{i+1}' for i in range(scores.shape[1])]
        return pd.DataFrame(
            scores,
            index=data_for_prediction.index,
            columns=component_names
        )
        
    def get_diagnostics(self) -> Dict[str, Any]:
        """Get diagnostic tests for the PCA model."""
        # Diagnostics are typically part of the fit results for PCA
        # This method would return relevant parts of the results dictionary
        if self.pca_object is None:
            return {"error": "Model not fitted."}
        
        # For simplicity, return a subset of key diagnostics
        return {
            "explained_variance_ratio": self.pca_object.explained_variance_ratio_.tolist(),
            "cumulative_variance_ratio": np.cumsum(self.pca_object.explained_variance_ratio_).tolist(),
            "n_components": self.pca_object.n_components_,
            "eigenvalues": self.pca_object.explained_variance_.tolist(),
            "loadings": self.pca_object.components_.T.tolist()
        }
    
    def create_composite_index(self, results: Dict[str, Any],
                               weights: Optional[Dict[str, float]] = None) -> pd.Series:
        """Create composite index from principal components using helper."""
        return create_composite_index(results, weights)
    
    def plot_results(self, results: Dict[str, Any],
                     save_path: Optional[str] = None) -> None:
        """Plot PCA results using helper."""
        plot_pca_results(results, save_path)
