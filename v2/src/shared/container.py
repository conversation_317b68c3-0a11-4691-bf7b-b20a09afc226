"""Dependency injection container configuration."""

from dependency_injector import containers, providers

from ..application.commands import (
    AnalyzeMarketIntegrationHandler,
)
from ..application.services import (
    AnalysisOrchestrator,
    DataPreparationService,
    PolicyDataAdapter,
    PolicyOrchestrator,
    ModelEstimatorService,
)
from ..core.domain.market.services import (
    MarketIntegrationService,
    PriceTransmissionService,
)
from ..infrastructure.caching import RedisCache, MemoryCache
from ..infrastructure.external_services import HDXClient, WFPClient, ACLEDClient
from ..infrastructure.messaging import InMemoryEventBus, AsyncEventBus
from ..infrastructure.persistence.unit_of_work import PostgresUnitOfWork
from ..infrastructure.persistence.policy_repository import PolicyResultsRepository
from ..infrastructure.security import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>, PasswordHandler, RBACManager, APIKeyManager, RateLimiter
)
from ..core.domain.auth import (
    AuthenticationService, UserService, UserRepository, APIKeyRepository, RefreshTokenRepository
)
from ..interfaces.api.rest.dependencies import get_current_user as get_current_user_dep


class Container(containers.DeclarativeContainer):
    """Dependency injection container."""
    
    # Configuration
    config = providers.Configuration()
    
    # Infrastructure - Database
    database_url = providers.Callable(
        lambda: config.database.url
    )
    
    unit_of_work = providers.Factory(
        PostgresUnitOfWork,
        connection_string=database_url
    )
    
    # Infrastructure - Caching
    cache = providers.Selector(
        config.cache.type,
        redis=providers.Singleton(
            RedisCache,
            redis_url=config.cache.redis.url,
            default_ttl=config.cache.default_ttl
        ),
        memory=providers.Singleton(
            MemoryCache,
            default_ttl=config.cache.default_ttl,
            max_size=config.cache.memory.max_size
        )
    )
    
    # Infrastructure - Event Bus
    event_bus = providers.Selector(
        config.events.type,
        inmemory=providers.Singleton(
            InMemoryEventBus
        ),
        async=providers.Singleton(
            AsyncEventBus,
            max_queue_size=config.events.queue_size
        )
    )
    
    # Infrastructure - External Services
    hdx_client = providers.Singleton(
        HDXClient,
        timeout=config.external.hdx.timeout
    )
    
    wfp_client = providers.Singleton(
        WFPClient,
        api_key=config.external.wfp.api_key
    )
    
    acled_client = providers.Singleton(
        ACLEDClient,
        api_key=config.external.acled.api_key,
        email=config.external.acled.email
    )
    
    # Domain Services
    price_transmission_service = providers.Factory(
        PriceTransmissionService
    )
    
    market_integration_service = providers.Factory(
        MarketIntegrationService,
        transmission_service=price_transmission_service
    )
    
    # Application Services
    data_preparation_service = providers.Factory(
        DataPreparationService,
        wfp_client=wfp_client,
        hdx_client=hdx_client
    )
    
    analysis_orchestrator = providers.Factory(
        AnalysisOrchestrator,
        price_repo=providers.Callable(
            lambda uow: uow.prices,
            unit_of_work
        ),
        transmission_service=price_transmission_service,
        integration_service=market_integration_service,
        event_bus=event_bus,
        cache=cache
    )
    
    # Application Services - Model Estimation
    model_estimator_service = providers.Factory(
        ModelEstimatorService,
        cache=cache
    )
    
    # Application Services - Policy
    policy_data_adapter = providers.Factory(
        PolicyDataAdapter,
        market_repo=providers.Callable(
            lambda uow: uow.markets,
            unit_of_work
        ),
        price_repo=providers.Callable(
            lambda uow: uow.prices,
            unit_of_work
        )
    )
    
    policy_orchestrator = providers.Factory(
        PolicyOrchestrator,
        data_adapter=policy_data_adapter,
        analysis_orchestrator=analysis_orchestrator,
        event_bus=event_bus
    )
    
    # Infrastructure - Policy Results Storage
    policy_repository = providers.Singleton(
        PolicyResultsRepository,
        storage_path=config.storage.policy_results_path
    )
    
    # Infrastructure - Data Processors (for policy endpoints)
    acled_processor = providers.Factory(
        lambda: None  # Placeholder - would be actual ACLED processor
    )
    
    panel_builder = providers.Factory(
        lambda: None  # Placeholder - would be actual panel builder
    )
    
    # Command Handlers
    analyze_market_integration_handler = providers.Factory(
        AnalyzeMarketIntegrationHandler,
        uow=unit_of_work,
        market_repo=providers.Callable(
            lambda uow: uow.markets,
            unit_of_work
        ),
        price_repo=providers.Callable(
            lambda uow: uow.prices,
            unit_of_work
        ),
        integration_service=market_integration_service,
        orchestrator=analysis_orchestrator,
        event_bus=event_bus
    )
    
    # Security Infrastructure
    jwt_handler = providers.Singleton(
        JWTHandler,
        config=None  # Uses environment variables
    )
    
    password_handler = providers.Singleton(
        PasswordHandler,
        rounds=12
    )
    
    rbac_manager = providers.Singleton(
        RBACManager
    )
    
    api_key_manager = providers.Singleton(
        APIKeyManager,
        cache=cache
    )
    
    rate_limiter = providers.Singleton(
        RateLimiter,
        cache=cache
    )
    
    # Auth Domain - Repositories (placeholders for now)
    user_repository = providers.Factory(
        lambda: None  # Would be actual UserRepository implementation
    )
    
    api_key_repository = providers.Factory(
        lambda: None  # Would be actual APIKeyRepository implementation
    )
    
    refresh_token_repository = providers.Factory(
        lambda: None  # Would be actual RefreshTokenRepository implementation
    )
    
    # Auth Domain - Services
    auth_service = providers.Factory(
        AuthenticationService,
        user_repository=user_repository,
        refresh_token_repository=refresh_token_repository
    )
    
    user_service = providers.Factory(
        UserService,
        user_repository=user_repository,
        api_key_repository=api_key_repository
    )
    
    # Dependencies
    get_current_user = providers.Callable(
        get_current_user_dep
    )