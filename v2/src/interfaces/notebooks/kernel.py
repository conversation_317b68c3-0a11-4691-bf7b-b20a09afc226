"""Jupyter kernel integration for Yemen Market Integration v2."""

import asyncio
from IPython import get_ipython
from IPython.display import display, Markdown
from dependency_injector.wiring import Provide, inject
from typing import Any, Dict, List, Optional # Added Any, Dict, List, Optional
from datetime import datetime # Added datetime

from ...shared.container import Container
from ...application.commands import RunThreeTierAnalysisCommand
from ...application.services import AnalysisOrchestrator, ModelEstimatorService
from ...core.domain.market.repositories import MarketRepository, PriceRepository
from ...infrastructure.caching import MemoryCache, RedisCache
from ...infrastructure.messaging import AsyncEventBus, InMemoryEventBus
from ...infrastructure.logging import Logger # Corrected import path

logger = Logger(__name__)

class YemenMarketIntegrationKernel:
    """
    Provides an interface for Jupyter notebooks to interact with the
    Yemen Market Integration v2 application.
    """

    @inject
    def __init__(
        self,
        market_repo: MarketRepository = Provide[Container.market_repository],
        price_repo: PriceRepository = Provide[Container.price_repository],
        orchestrator: AnalysisOrchestrator = Provide[Container.analysis_orchestrator],
        estimator_service: ModelEstimatorService = Provide[Container.estimator_service],
        event_bus: Any = Provide[Container.event_bus],
        cache: Any = Provide[Container.cache]
    ):
        self.market_repo = market_repo
        self.price_repo = price_repo
        self.orchestrator = orchestrator
        self.estimator_service = estimator_service
        self.event_bus = event_bus
        self.cache = cache
        logger.info("Yemen Market Integration Kernel initialized.")

    async def run_three_tier_analysis(
        self,
        start_date: str,
        end_date: str,
        market_ids: Optional[List[str]] = None,
        commodity_ids: Optional[List[str]] = None,
        tier1_config: Optional[Dict[str, Any]] = None,
        tier2_config: Optional[Dict[str, Any]] = None,
        tier3_config: Optional[Dict[str, Any]] = None,
        run_diagnostics: bool = True,
        apply_corrections: bool = True,
        save_intermediate: bool = True
    ) -> str:
        """
        Executes the full three-tier econometric analysis.

        Args:
            start_date (str): Start date for the analysis (YYYY-MM-DD).
            end_date (str): End date for the analysis (YYYY-MM-DD).
            market_ids (Optional[List[str]]): List of market IDs to include. None for all.
            commodity_ids (Optional[List[str]]): List of commodity IDs to include. None for all.
            tier1_config (Optional[Dict[str, Any]]): Configuration for Tier 1.
            tier2_config (Optional[Dict[str, Any]]): Configuration for Tier 2.
            tier3_config (Optional[Dict[str, Any]]): Configuration for Tier 3.
            run_diagnostics (bool): Whether to run diagnostic tests.
            apply_corrections (bool): Whether to apply standard error corrections.
            save_intermediate (bool): Whether to save intermediate results.

        Returns:
            str: The ID of the initiated analysis job.
        """
        from ...application.commands import RunThreeTierAnalysisCommand # Import locally to avoid circular dep
        command = RunThreeTierAnalysisCommand(
            start_date=datetime.strptime(start_date, "%Y-%m-%d"),
            end_date=datetime.strptime(end_date, "%Y-%m-%d"),
            market_ids=market_ids,
            commodity_ids=commodity_ids,
            tier1_config=tier1_config,
            tier2_config=tier2_config,
            tier3_config=tier3_config,
            run_diagnostics=run_diagnostics,
            apply_corrections=apply_corrections,
            save_intermediate=save_intermediate
        )
        
        display(Markdown(f"Initiating Three-Tier Analysis with Job ID: **{command.id}**"))
        job_id = await self.orchestrator.handle_command(command) # Assuming orchestrator has a handle_command method
        display(Markdown(f"Analysis job started. Monitor status with `kernel.get_analysis_status('{job_id}')`"))
        return job_id

    async def get_analysis_status(self, job_id: str) -> Dict[str, Any]:
        """
        Retrieves the current status of an analysis job.

        Args:
            job_id (str): The ID of the analysis job.

        Returns:
            Dict[str, Any]: A dictionary containing the job's status, progress, and results.
        """
        status = await self.orchestrator.get_analysis_status(job_id)
        if status:
            display(Markdown(f"### Analysis Job Status: `{status['id']}`"))
            display(Markdown(f"- **Type**: `{status['type']}`"))
            display(Markdown(f"- **Overall Status**: `{status['status']}` ({status['progress']}%)"))
            display(Markdown(f"- **Start Time**: `{status['start_time']}`"))
            if status['end_time']:
                display(Markdown(f"- **End Time**: `{status['end_time']}`"))
            if status['error']:
                display(Markdown(f"- **Error**: `{status['error']}`"))
            
            display(Markdown("#### Tier Progress:"))
            for tier, prog in status['tiers_progress'].items():
                display(Markdown(f"- **{tier.capitalize()}**: `{prog['status']}` ({prog['progress']}%) - `{prog['message']}`"))
            
            if status['status'] == 'completed' and status['results']:
                display(Markdown("#### Results Summary (Partial View):"))
                # Display a summary of results, not the full object
                if 'tier1' in status['results']:
                    display(Markdown(f"- **Tier 1 Model**: {status['results']['tier1'].get('model', 'N/A')}"))
                    display(Markdown(f"- **Tier 1 R-squared**: {status['results']['tier1'].get('result', {}).get('r_squared', 'N/A'):.4f}"))
                if 'tier2' in status['results']:
                    display(Markdown(f"- **Tier 2 Commodities Analyzed**: {len(status['results']['tier2'])}"))
                if 'tier3' in status['results']:
                    display(Markdown(f"- **Tier 3 Validation Methods Run**: {list(status['results']['tier3'].keys())}"))
            
            return status
        else:
            display(Markdown(f"Analysis job `{job_id}` not found."))
            return {}

    async def get_market_prices(
        self,
        market_ids: Optional[List[str]] = None,
        commodity_codes: Optional[List[str]] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieves market price observations.

        Args:
            market_ids (Optional[List[str]]): List of market IDs.
            commodity_codes (Optional[List[str]]): List of commodity codes.
            start_date (Optional[str]): Start date (YYYY-MM-DD).
            end_date (Optional[str]): End date (YYYY-MM-DD).

        Returns:
            List[Dict[str, Any]]: A list of market price observation dictionaries.
        """
        from ...application.queries.get_market_prices_query import GetMarketPricesQuery, GetMarketPricesQueryHandler
        
        query = GetMarketPricesQuery(
            market_ids=market_ids,
            commodity_codes=commodity_codes,
            start_date=datetime.strptime(start_date, "%Y-%m-%d") if start_date else None,
            end_date=datetime.strptime(end_date, "%Y-%m-%d") if end_date else None
        )
        
        handler = GetMarketPricesQueryHandler(self.price_repo)
        results = await handler.handle(query)
        
        display(Markdown(f"Retrieved **{len(results)}** market price observations."))
        return results

    def _setup_logging(self):
        # Configure logging for the kernel environment
        Logger.configure_logging(log_level="INFO", log_to_console=True)

# Initialize the kernel
def load_ipython_extension(ipython):
    """
    Called when the extension is loaded.
    """
    container = Container()
    container.wire(modules=[sys.modules[__name__]]) # Wire this module for DI
    
    kernel_instance = YemenMarketIntegrationKernel()
    
    # Register the instance methods as magic commands or directly callable functions
    ipython.run_line_magic("store", "kernel_instance") # Store for direct access
    ipython.push({"kernel": kernel_instance}) # Make it available as 'kernel' variable
    
    kernel_instance._setup_logging()
    
    display(Markdown("### Yemen Market Integration v2 Kernel Loaded!"))
    display(Markdown("You can now access core functionalities via the `kernel` object."))
    display(Markdown("- `await kernel.run_three_tier_analysis(...)` to start an analysis."))
    display(Markdown("- `await kernel.get_analysis_status(job_id)` to check status."))
    display(Markdown("- `await kernel.get_market_prices(...)` to fetch data."))
    logger.info("Jupyter extension loaded and kernel instance registered.")

import sys
