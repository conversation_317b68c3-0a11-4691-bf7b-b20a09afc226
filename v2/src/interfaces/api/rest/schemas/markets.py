"""Market API schemas."""

from datetime import datetime, date
from typing import List, Optional, Dict

from pydantic import BaseModel, Field


class MarketResponse(BaseModel):
    """Response schema for market data."""
    
    market_id: str = Field(..., description="Unique market identifier")
    name: str = Field(..., description="Market name")
    governorate: str = Field(..., description="Governorate name")
    district: str = Field(..., description="District name")
    market_type: str = Field(..., description="Market type (wholesale, retail, etc.)")
    latitude: float = Field(..., description="Latitude coordinate")
    longitude: float = Field(..., description="Longitude coordinate")
    active_since: datetime = Field(..., description="Date market became active")
    active_until: Optional[datetime] = Field(None, description="Date market became inactive")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "market_id": "SANAA_CENTRAL",
                "name": "Sana'a Central Market",
                "governorate": "Sana'a",
                "district": "Old City",
                "market_type": "wholesale",
                "latitude": 15.3694,
                "longitude": 44.1910,
                "active_since": "2019-01-01T00:00:00",
                "active_until": None
            }
        }


class MarketListResponse(BaseModel):
    """Response schema for market list."""
    
    markets: List[MarketResponse] = Field(..., description="List of markets")
    total: int = Field(..., description="Total number of markets matching filters")
    skip: int = Field(..., description="Number of markets skipped")
    limit: int = Field(..., description="Maximum markets returned")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "markets": [
                    {
                        "market_id": "SANAA_CENTRAL",
                        "name": "Sana'a Central Market",
                        "governorate": "Sana'a",
                        "district": "Old City",
                        "market_type": "wholesale",
                        "latitude": 15.3694,
                        "longitude": 44.1910,
                        "active_since": "2019-01-01T00:00:00",
                        "active_until": None
                    }
                ],
                "total": 42,
                "skip": 0,
                "limit": 20
            }
        }


class PricePointDTO(BaseModel):
    """Price point data transfer object."""
    date: date
    price: float
    currency: str
    commodity: str
    
    @classmethod
    def from_entity(cls, entity):
        """Create DTO from domain entity."""
        return cls(
            date=entity.date,
            price=entity.price,
            currency=entity.currency,
            commodity=entity.commodity
        )


class PriceSeriesResponse(BaseModel):
    """Price series response."""
    market_id: str = Field(..., description="Market identifier")
    commodity: Optional[str] = Field(None, description="Commodity filter")
    currency: str = Field(..., description="Currency of prices")
    data: List[PricePointDTO] = Field(..., description="Price time series data")
    statistics: Dict[str, Optional[float]] = Field(..., description="Summary statistics")
    
    class Config:
        """Pydantic config."""
        schema_extra = {
            "example": {
                "market_id": "SANAA_CENTRAL",
                "commodity": "Wheat",
                "currency": "USD",
                "data": [
                    {
                        "date": "2023-01-01",
                        "price": 0.75,
                        "currency": "USD",
                        "commodity": "Wheat"
                    }
                ],
                "statistics": {
                    "count": 365,
                    "mean": 0.78,
                    "std": 0.05,
                    "min": 0.65,
                    "max": 0.95,
                    "trend": "increasing"
                }
            }
        }


class PaginationInfo(BaseModel):
    """Pagination information."""
    skip: int
    limit: int
    total: int
    has_more: bool
    
    @property
    def pages(self) -> int:
        return (self.total + self.limit - 1) // self.limit
    
    @property
    def current_page(self) -> int:
        return (self.skip // self.limit) + 1