"""API routes for econometric analysis."""

import asyncio
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, AsyncGenerator
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from .....shared.container import Container
from .....application.commands import RunThreeTierAnalysisCommand
from .....application.queries import GetAnalysisStatusQuery
from .....application.services import AnalysisOrchestrator
from .....infrastructure.logging import Logger
from .....core.domain.shared.events import DomainEvent
from ..schemas.analysis import (
    ThreeTierAnalysisRequest, 
    AnalysisResponse, 
    AnalysisResultsResponse,
    AnalysisStatus
)
from ..dependencies import require_auth
from .....infrastructure.security.rbac import require_permission, Permission

logger = Logger(__name__)

router = APIRouter(prefix="/analysis", tags=["analysis"])


class RunAnalysisRequest(BaseModel):
    start_date: str
    end_date: str
    market_ids: Optional[List[str]] = None
    commodity_codes: Optional[List[str]] = None
    tier1_config: Optional[Dict[str, Any]] = None
    tier2_config: Optional[Dict[str, Any]] = None
    tier3_config: Optional[Dict[str, Any]] = None
    run_diagnostics: bool = True
    apply_corrections: bool = True
    save_intermediate: bool = True


class AnalysisStatusResponse(BaseModel):
    id: str
    type: str
    status: str
    progress: int
    start_time: datetime
    end_time: Optional[datetime] = None
    error: Optional[str] = None
    tiers_progress: Dict[str, Dict[str, Any]]
    results: Optional[Dict[str, Any]] = None


@router.post("/three-tier", response_model=AnalysisResponse, status_code=status.HTTP_202_ACCEPTED)
@require_permission(Permission.ANALYSIS_CREATE)
async def create_three_tier_analysis(
    request: ThreeTierAnalysisRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(require_auth),
    orchestrator: AnalysisOrchestrator = Depends(Container.analysis_orchestrator)
):
    """
    Create a new three-tier econometric analysis.
    
    This endpoint initiates a comprehensive market integration analysis including:
    - Tier 1: Pooled panel regression
    - Tier 2: Commodity-specific VECM models
    - Tier 3: Factor analysis validation
    """
    try:
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        
        # Validate request
        await validate_analysis_request(request, orchestrator)
        
        # Create analysis record (would be persisted in a real implementation)
        analysis_metadata = {
            "id": analysis_id,
            "status": AnalysisStatus.PENDING,
            "config": request.dict(),
            "created_at": datetime.utcnow()
        }
        
        # Queue background analysis
        background_tasks.add_task(
            run_analysis_task,
            analysis_id=analysis_id,
            request=request,
            orchestrator=orchestrator
        )
        
        return AnalysisResponse(
            id=analysis_id,
            status=AnalysisStatus.PENDING,
            message="Analysis queued for processing",
            estimated_duration_seconds=300,
            created_at=analysis_metadata["created_at"]
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating three-tier analysis: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create analysis: {e}"
        )


@router.post("/analysis/run-three-tier", response_model=Dict[str, str], status_code=status.HTTP_202_ACCEPTED)
async def run_three_tier_analysis(
    request: RunAnalysisRequest,
    orchestrator: AnalysisOrchestrator = Depends(Container.analysis_orchestrator)
):
    """
    Initiate a three-tier econometric analysis (legacy endpoint).
    """
    try:
        command = RunThreeTierAnalysisCommand(
            start_date=datetime.strptime(request.start_date, "%Y-%m-%d"),
            end_date=datetime.strptime(request.end_date, "%Y-%m-%d"),
            market_ids=request.market_ids,
            commodity_codes=request.commodity_codes,
            tier1_config=request.tier1_config,
            tier2_config=request.tier2_config,
            tier3_config=request.tier3_config,
            run_diagnostics=request.run_diagnostics,
            apply_corrections=request.apply_corrections,
            save_intermediate=request.save_intermediate
        )
        
        job_id = await orchestrator.handle_command(command)
        logger.info(f"Three-tier analysis job {job_id} initiated via API.")
        return {"job_id": job_id, "message": "Analysis started successfully."}
    except Exception as e:
        logger.error(f"Error initiating three-tier analysis: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start analysis: {e}"
        )


@router.get("/analysis/status/{job_id}", response_model=AnalysisStatusResponse)
async def get_analysis_status(
    job_id: str,
    orchestrator: AnalysisOrchestrator = Depends(Container.analysis_orchestrator)
):
    """
    Get the current status of an econometric analysis job.
    """
    query = GetAnalysisStatusQuery(job_id=job_id)
    status_data = await orchestrator.get_analysis_status(query.job_id) # Assuming orchestrator can handle query directly
    
    if not status_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Analysis job with ID {job_id} not found."
        )
    
    return AnalysisStatusResponse(**status_data)


@router.get("/analyses/{id}/status")
async def get_analysis_status_sse(
    id: str,
    event_bus = Depends(Container.event_bus),
    orchestrator: AnalysisOrchestrator = Depends(Container.analysis_orchestrator)
):
    """
    Stream real-time status updates for an analysis using Server-Sent Events (SSE).
    
    This endpoint returns a text/event-stream response that pushes updates
    whenever the analysis status changes.
    """
    # Verify the analysis exists
    status_data = await orchestrator.get_analysis_status(id)
    if not status_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Analysis with ID {id} not found."
        )
    
    async def event_generator() -> AsyncGenerator[str, None]:
        """Generate SSE events for analysis status updates."""
        # Send initial status
        yield f"data: {{\n"
        yield f'  "event": "initial",\n'
        yield f'  "analysis_id": "{id}",\n'
        yield f'  "status": "{status_data.get("status", "unknown")}",\n'
        yield f'  "progress": {status_data.get("progress", 0)},\n'
        yield f'  "timestamp": "{datetime.utcnow().isoformat()}"\n'
        yield f"}}\n\n"
        
        # Create queue for this client
        event_queue = asyncio.Queue()
        
        # Subscribe to analysis events
        def event_handler(event: DomainEvent):
            """Handle domain events for this analysis."""
            # Check if event is related to this analysis
            if hasattr(event, 'analysis_id') and event.analysis_id == id:
                asyncio.create_task(event_queue.put(event))
        
        # Subscribe to events
        event_bus.subscribe(f"analysis.progress.{id}", event_handler)
        event_bus.subscribe(f"analysis.status.{id}", event_handler)
        event_bus.subscribe(f"analysis.completed.{id}", event_handler)
        event_bus.subscribe(f"analysis.failed.{id}", event_handler)
        
        try:
            # Stream events
            while True:
                try:
                    # Wait for event with timeout
                    event = await asyncio.wait_for(event_queue.get(), timeout=30.0)
                    
                    # Format event as SSE
                    yield f"data: {{\n"
                    yield f'  "event": "{event.event_name}",\n'
                    yield f'  "analysis_id": "{id}",\n'
                    
                    # Add event-specific data
                    if hasattr(event, 'status'):
                        yield f'  "status": "{event.status}",\n'
                    if hasattr(event, 'progress'):
                        yield f'  "progress": {event.progress},\n'
                    if hasattr(event, 'tier'):
                        yield f'  "tier": "{event.tier}",\n'
                    if hasattr(event, 'message'):
                        yield f'  "message": "{event.message}",\n'
                    if hasattr(event, 'error'):
                        yield f'  "error": "{event.error}",\n'
                    
                    yield f'  "timestamp": "{event.occurred_at.isoformat()}"\n'
                    yield f"}}\n\n"
                    
                    # Check if analysis is complete
                    if event.event_name in ["analysis.completed", "analysis.failed"]:
                        break
                        
                except asyncio.TimeoutError:
                    # Send heartbeat to keep connection alive
                    yield f": heartbeat {datetime.utcnow().isoformat()}\n\n"
                    
        except asyncio.CancelledError:
            logger.info(f"SSE connection closed for analysis {id}")
            raise
        finally:
            # Unsubscribe handlers
            # Note: In a real implementation, we'd need to properly unsubscribe
            logger.info(f"Cleaning up SSE handlers for analysis {id}")
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # Disable nginx buffering
        }
    )


@router.get("/{id}", response_model=AnalysisStatusResponse)
async def get_analysis(
    id: str,
    orchestrator: AnalysisOrchestrator = Depends(Container.analysis_orchestrator)
):
    """
    Get analysis status by ID.
    """
    status_data = await orchestrator.get_analysis_status(id)
    
    if not status_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Analysis with ID {id} not found."
        )
    
    return AnalysisStatusResponse(**status_data)


@router.get("/{id}/results", response_model=AnalysisResultsResponse)
async def get_analysis_results(
    id: str,
    orchestrator: AnalysisOrchestrator = Depends(Container.analysis_orchestrator)
):
    """
    Get complete results for a finished analysis.
    """
    # Get analysis status first
    status_data = await orchestrator.get_analysis_status(id)
    
    if not status_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Analysis with ID {id} not found."
        )
    
    if status_data.get("status") != "completed":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Analysis {id} is not completed. Current status: {status_data.get('status')}"
        )
    
    # Get results
    results = await orchestrator.get_analysis_results(id)
    
    if not results:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Results not found for analysis {id}"
        )
    
    return AnalysisResultsResponse(**results)


@router.delete("/{id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_analysis(
    id: str,
    orchestrator: AnalysisOrchestrator = Depends(Container.analysis_orchestrator)
):
    """
    Cancel or delete an analysis.
    """
    try:
        # Check if analysis exists
        status_data = await orchestrator.get_analysis_status(id)
        
        if not status_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Analysis with ID {id} not found."
            )
        
        # Cancel if running, otherwise delete
        if status_data.get("status") in ["pending", "running"]:
            await orchestrator.cancel_analysis(id)
        else:
            await orchestrator.delete_analysis(id)
            
        return None
        
    except Exception as e:
        logger.error(f"Error deleting analysis {id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete analysis: {e}"
        )


# Helper functions
async def validate_analysis_request(request: ThreeTierAnalysisRequest, orchestrator: AnalysisOrchestrator):
    """
    Validate analysis request parameters.
    """
    # Validate date range
    if request.end_date <= request.start_date:
        raise ValueError("End date must be after start date")
    
    # Validate markets if specified
    if request.markets:
        # Would check against available markets in real implementation
        pass
    
    # Validate commodities if specified
    if request.commodities:
        # Would check against available commodities in real implementation
        pass
    
    return True


async def run_analysis_task(analysis_id: str, request: ThreeTierAnalysisRequest, orchestrator: AnalysisOrchestrator):
    """
    Background task to run the analysis.
    """
    try:
        logger.info(f"Starting analysis task {analysis_id}")
        
        # Convert request to command
        command = RunThreeTierAnalysisCommand(
            start_date=request.start_date,
            end_date=request.end_date,
            market_ids=request.markets,
            commodity_codes=request.commodities,
            tier1_config={
                "confidence_level": request.confidence_level
            },
            tier2_config={
                "commodity_groups": request.commodity_groups
            },
            tier3_config={},
            run_diagnostics=request.include_diagnostics,
            apply_corrections=True,
            save_intermediate=True
        )
        
        # Run analysis
        await orchestrator.handle_command(command)
        
        logger.info(f"Analysis task {analysis_id} completed")
        
    except Exception as e:
        logger.error(f"Error in analysis task {analysis_id}: {e}")
        # Would update analysis status to failed in real implementation
