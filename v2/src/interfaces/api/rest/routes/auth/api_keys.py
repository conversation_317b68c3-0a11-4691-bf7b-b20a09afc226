"""API Key management endpoints."""

from typing import List, Optional
from uuid import UUID
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel

from ......infrastructure.security import require_permission
from ......infrastructure.security.rbac import Permission
from ......infrastructure.security.api_key_manager import APIKeyManager
from ......infrastructure.logging import Logger
from ......shared.container import Container

logger = Logger(__name__)

router = APIRouter(prefix="/api-keys", tags=["API Key Management"])


# Request/Response models
class CreateAPIKeyRequest(BaseModel):
    """Create API key request."""
    name: str
    permissions: Optional[List[str]] = None
    expires_in_days: Optional[int] = None
    rate_limit: Optional[int] = None  # Requests per minute
    allowed_ips: Optional[List[str]] = None


class APIKeyResponse(BaseModel):
    """API key response (without the actual key)."""
    id: str
    name: str
    key_prefix: str
    created_at: str
    expires_at: Optional[str] = None
    last_used_at: Optional[str] = None
    is_active: bool
    permissions: List[str]
    rate_limit: Optional[int] = None
    usage_count: int


class CreateAPIKeyResponse(BaseModel):
    """Create API key response (includes the key only once)."""
    api_key: str  # Full key, shown only on creation
    key_info: APIKeyResponse


class APIKeyUsageStats(BaseModel):
    """API key usage statistics."""
    total_requests: int
    requests_last_minute: int
    requests_last_hour: int
    last_used: Optional[str] = None


@router.post("/", response_model=CreateAPIKeyResponse, status_code=status.HTTP_201_CREATED)
@require_permission(Permission.API_KEY_CREATE)
async def create_api_key(
    request: CreateAPIKeyRequest,
    current_user: dict = Depends(Container.get_current_user),
    api_key_manager: APIKeyManager = Depends(Container.api_key_manager),
    api_key_repository = Depends(Container.api_key_repository)
):
    """
    Create a new API key.
    
    **Important**: The API key is only shown once in the response. Store it securely.
    
    Requires API_KEY_CREATE permission.
    """
    try:
        # Calculate expiration
        expires_at = None
        if request.expires_in_days:
            from datetime import timedelta
            expires_at = datetime.utcnow() + timedelta(days=request.expires_in_days)
        
        # Create API key
        plain_key, api_key = api_key_manager.generate_api_key(
            name=request.name,
            permissions=request.permissions,
            expires_at=expires_at,
            rate_limit=request.rate_limit,
            metadata={
                "created_by": current_user.get("username"),
                "user_id": current_user.get("sub")
            }
        )
        
        # Set user association
        api_key.user_id = UUID(current_user.get("sub"))
        
        # Save to repository
        await api_key_repository.save(api_key)
        
        logger.info(f"Created API key '{request.name}' for user {current_user.get('username')}")
        
        return CreateAPIKeyResponse(
            api_key=plain_key,
            key_info=APIKeyResponse(
                id=str(api_key.id),
                name=api_key.name,
                key_prefix=api_key.key_prefix,
                created_at=api_key.created_at.isoformat(),
                expires_at=api_key.expires_at.isoformat() if api_key.expires_at else None,
                last_used_at=None,
                is_active=api_key.is_active,
                permissions=api_key.permissions,
                rate_limit=api_key.rate_limit,
                usage_count=0
            )
        )
        
    except Exception as e:
        logger.error(f"Error creating API key: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create API key"
        )


@router.get("/", response_model=List[APIKeyResponse])
async def list_api_keys(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    include_inactive: bool = Query(False),
    current_user: dict = Depends(Container.get_current_user),
    api_key_repository = Depends(Container.api_key_repository)
):
    """
    List API keys for the current user.
    
    Regular users can only see their own keys.
    Admins with API_KEY_READ permission can see all keys.
    """
    try:
        user_id = UUID(current_user.get("sub"))
        user_roles = current_user.get("roles", [])
        
        # Check if admin
        if "admin" in user_roles:
            # Admin can see all keys
            keys = await api_key_repository.list_active_keys(skip=skip, limit=limit)
        else:
            # Regular users see only their keys
            keys = await api_key_repository.find_by_user_id(user_id)
            # Apply pagination
            keys = keys[skip:skip + limit]
        
        # Filter inactive if requested
        if not include_inactive:
            keys = [k for k in keys if k.is_active]
        
        return [
            APIKeyResponse(
                id=str(key.id),
                name=key.name,
                key_prefix=key.key_prefix,
                created_at=key.created_at.isoformat(),
                expires_at=key.expires_at.isoformat() if key.expires_at else None,
                last_used_at=key.last_used_at.isoformat() if key.last_used_at else None,
                is_active=key.is_active,
                permissions=key.permissions,
                rate_limit=key.rate_limit,
                usage_count=key.usage_count
            )
            for key in keys
        ]
        
    except Exception as e:
        logger.error(f"Error listing API keys: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list API keys"
        )


@router.get("/{key_id}", response_model=APIKeyResponse)
async def get_api_key(
    key_id: UUID,
    current_user: dict = Depends(Container.get_current_user),
    api_key_repository = Depends(Container.api_key_repository)
):
    """Get API key details."""
    try:
        api_key = await api_key_repository.find_by_id(key_id)
        
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )
        
        # Check ownership
        user_id = UUID(current_user.get("sub"))
        user_roles = current_user.get("roles", [])
        
        if api_key.user_id != user_id and "admin" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return APIKeyResponse(
            id=str(api_key.id),
            name=api_key.name,
            key_prefix=api_key.key_prefix,
            created_at=api_key.created_at.isoformat(),
            expires_at=api_key.expires_at.isoformat() if api_key.expires_at else None,
            last_used_at=api_key.last_used_at.isoformat() if api_key.last_used_at else None,
            is_active=api_key.is_active,
            permissions=api_key.permissions,
            rate_limit=api_key.rate_limit,
            usage_count=api_key.usage_count
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting API key: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get API key"
        )


@router.get("/{key_id}/usage", response_model=APIKeyUsageStats)
async def get_api_key_usage(
    key_id: UUID,
    current_user: dict = Depends(Container.get_current_user),
    api_key_repository = Depends(Container.api_key_repository),
    api_key_manager: APIKeyManager = Depends(Container.api_key_manager)
):
    """Get API key usage statistics."""
    try:
        api_key = await api_key_repository.find_by_id(key_id)
        
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )
        
        # Check ownership
        user_id = UUID(current_user.get("sub"))
        user_roles = current_user.get("roles", [])
        
        if api_key.user_id != user_id and "admin" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Get usage stats
        stats = api_key_manager.get_key_usage_stats(str(key_id))
        
        if not stats:
            stats = {
                "total_requests": api_key.usage_count,
                "requests_last_minute": 0,
                "requests_last_hour": 0,
                "last_used": api_key.last_used_at.isoformat() if api_key.last_used_at else None
            }
        
        return APIKeyUsageStats(**stats)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting API key usage: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get API key usage"
        )


@router.post("/{key_id}/revoke", status_code=status.HTTP_204_NO_CONTENT)
async def revoke_api_key(
    key_id: UUID,
    current_user: dict = Depends(Container.get_current_user),
    api_key_repository = Depends(Container.api_key_repository),
    api_key_manager: APIKeyManager = Depends(Container.api_key_manager)
):
    """Revoke an API key."""
    try:
        api_key = await api_key_repository.find_by_id(key_id)
        
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )
        
        # Check ownership
        user_id = UUID(current_user.get("sub"))
        user_roles = current_user.get("roles", [])
        
        if api_key.user_id != user_id and "admin" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Revoke key
        api_key_manager.revoke_api_key(str(key_id))
        
        logger.info(f"Revoked API key {key_id}")
        return None
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error revoking API key: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke API key"
        )


@router.post("/{key_id}/rotate", response_model=CreateAPIKeyResponse)
@require_permission(Permission.API_KEY_UPDATE)
async def rotate_api_key(
    key_id: UUID,
    grace_period_minutes: int = Query(60, ge=0, le=10080),  # Max 1 week
    current_user: dict = Depends(Container.get_current_user),
    api_key_repository = Depends(Container.api_key_repository),
    api_key_manager: APIKeyManager = Depends(Container.api_key_manager)
):
    """
    Rotate an API key, creating a new one with the same permissions.
    
    The old key remains valid for the grace period.
    
    Requires API_KEY_UPDATE permission.
    """
    try:
        api_key = await api_key_repository.find_by_id(key_id)
        
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )
        
        # Check ownership
        user_id = UUID(current_user.get("sub"))
        user_roles = current_user.get("roles", [])
        
        if api_key.user_id != user_id and "admin" not in user_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Rotate key
        result = api_key_manager.rotate_api_key(
            str(key_id),
            grace_period_minutes=grace_period_minutes
        )
        
        if not result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to rotate API key"
            )
        
        new_plain_key, new_api_key = result
        
        # Save new key
        await api_key_repository.save(new_api_key)
        
        logger.info(f"Rotated API key {key_id} -> {new_api_key.id}")
        
        return CreateAPIKeyResponse(
            api_key=new_plain_key,
            key_info=APIKeyResponse(
                id=str(new_api_key.id),
                name=new_api_key.name,
                key_prefix=new_api_key.key_prefix,
                created_at=new_api_key.created_at.isoformat(),
                expires_at=new_api_key.expires_at.isoformat() if new_api_key.expires_at else None,
                last_used_at=None,
                is_active=new_api_key.is_active,
                permissions=new_api_key.permissions,
                rate_limit=new_api_key.rate_limit,
                usage_count=0
            )
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error rotating API key: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to rotate API key"
        )