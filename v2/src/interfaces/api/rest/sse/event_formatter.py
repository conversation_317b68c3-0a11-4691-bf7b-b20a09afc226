"""
SSE event formatter for converting domain events to SSE format.
"""

import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from enum import Enum

from .....core.domain.shared.events import DomainEvent
from .....application.events.analysis_events import (
    AnalysisProgressEvent,
    AnalysisStatusChangedEvent,
    AnalysisCompletedEvent,
    AnalysisFailedEvent,
    TierStartedEvent,
    TierCompletedEvent,
    CommodityProcessingEvent
)


class SSEEventType(str, Enum):
    """SSE event types."""
    PROGRESS = "progress"
    STATUS = "status"
    COMPLETED = "completed"
    FAILED = "failed"
    TIER_STARTED = "tier_started"
    TIER_COMPLETED = "tier_completed"
    COMMODITY_UPDATE = "commodity_update"
    HEARTBEAT = "heartbeat"
    INITIAL = "initial"
    ERROR = "error"


class SSEEventFormatter:
    """
    Formats domain events for SSE transmission.
    
    Follows SSE spec:
    - Lines starting with 'data:' for data
    - Lines starting with 'event:' for event type
    - Lines starting with 'id:' for event ID
    - Lines starting with 'retry:' for reconnection time
    - Empty line to end event
    """
    
    @staticmethod
    def format_event(
        event: Optional[DomainEvent] = None,
        event_type: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None,
        event_id: Optional[str] = None,
        retry: Optional[int] = None
    ) -> str:
        """
        Format an event for SSE transmission.
        
        Args:
            event: Domain event to format
            event_type: Override event type
            data: Override event data
            event_id: Optional event ID
            retry: Optional retry interval in milliseconds
            
        Returns:
            Formatted SSE event string
        """
        lines = []
        
        # Add event ID if provided
        if event_id:
            lines.append(f"id: {event_id}")
            
        # Add retry if provided
        if retry is not None:
            lines.append(f"retry: {retry}")
            
        # Determine event type
        if event:
            sse_type = SSEEventFormatter._get_sse_type(event)
            if event_type:
                sse_type = event_type
        else:
            sse_type = event_type or SSEEventType.HEARTBEAT
            
        lines.append(f"event: {sse_type}")
        
        # Format data
        if event:
            event_data = SSEEventFormatter._format_event_data(event)
        else:
            event_data = data or {}
            
        # Add timestamp if not present
        if "timestamp" not in event_data:
            event_data["timestamp"] = datetime.utcnow().isoformat()
            
        # Convert data to JSON and split by lines
        json_data = json.dumps(event_data, default=str)
        for line in json_data.split('\n'):
            lines.append(f"data: {line}")
            
        # Add empty line to end event
        lines.append("")
        
        return "\n".join(lines) + "\n"
        
    @staticmethod
    def format_heartbeat() -> str:
        """
        Format a heartbeat message.
        
        Returns:
            Formatted SSE heartbeat
        """
        return SSEEventFormatter.format_event(
            event_type=SSEEventType.HEARTBEAT,
            data={
                "type": "heartbeat",
                "timestamp": datetime.utcnow().isoformat()
            }
        )
        
    @staticmethod
    def format_initial_status(
        analysis_id: str,
        status: str,
        progress: int = 0,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Format initial status message.
        
        Args:
            analysis_id: Analysis ID
            status: Current status
            progress: Current progress (0-100)
            details: Optional additional details
            
        Returns:
            Formatted SSE initial status
        """
        data = {
            "analysis_id": analysis_id,
            "status": status,
            "progress": progress,
            "type": "initial"
        }
        
        if details:
            data.update(details)
            
        return SSEEventFormatter.format_event(
            event_type=SSEEventType.INITIAL,
            data=data
        )
        
    @staticmethod
    def format_error(
        error: str,
        error_type: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Format error message.
        
        Args:
            error: Error message
            error_type: Optional error type
            details: Optional error details
            
        Returns:
            Formatted SSE error
        """
        data = {
            "error": error,
            "type": "error"
        }
        
        if error_type:
            data["error_type"] = error_type
            
        if details:
            data["details"] = details
            
        return SSEEventFormatter.format_event(
            event_type=SSEEventType.ERROR,
            data=data
        )
        
    @staticmethod
    def _get_sse_type(event: DomainEvent) -> str:
        """
        Get SSE event type from domain event.
        
        Args:
            event: Domain event
            
        Returns:
            SSE event type
        """
        if isinstance(event, AnalysisProgressEvent):
            return SSEEventType.PROGRESS
        elif isinstance(event, AnalysisStatusChangedEvent):
            return SSEEventType.STATUS
        elif isinstance(event, AnalysisCompletedEvent):
            return SSEEventType.COMPLETED
        elif isinstance(event, AnalysisFailedEvent):
            return SSEEventType.FAILED
        elif isinstance(event, TierStartedEvent):
            return SSEEventType.TIER_STARTED
        elif isinstance(event, TierCompletedEvent):
            return SSEEventType.TIER_COMPLETED
        elif isinstance(event, CommodityProcessingEvent):
            return SSEEventType.COMMODITY_UPDATE
        else:
            # Extract from event name
            parts = event.event_name.split('.')
            if len(parts) > 1:
                return parts[1]
            return "update"
            
    @staticmethod
    def _format_event_data(event: DomainEvent) -> Dict[str, Any]:
        """
        Format domain event data for SSE.
        
        Args:
            event: Domain event
            
        Returns:
            Event data dictionary
        """
        # Common fields
        data = {
            "event_name": event.event_name,
            "timestamp": event.occurred_at.isoformat()
        }
        
        # Add event-specific fields
        if isinstance(event, AnalysisProgressEvent):
            data.update({
                "analysis_id": event.analysis_id,
                "progress": event.progress,
                "tier": event.tier,
                "message": event.message,
                "details": event.details
            })
        elif isinstance(event, AnalysisStatusChangedEvent):
            data.update({
                "analysis_id": event.analysis_id,
                "old_status": event.old_status,
                "status": event.status,
                "reason": event.reason
            })
        elif isinstance(event, AnalysisCompletedEvent):
            data.update({
                "analysis_id": event.analysis_id,
                "status": event.status,
                "results_summary": event.results_summary,
                "duration_seconds": event.duration_seconds,
                "message": event.message
            })
        elif isinstance(event, AnalysisFailedEvent):
            data.update({
                "analysis_id": event.analysis_id,
                "status": event.status,
                "error": event.error,
                "error_type": event.error_type,
                "tier": event.tier,
                "message": event.message
            })
        elif isinstance(event, TierStartedEvent):
            data.update({
                "analysis_id": event.analysis_id,
                "tier": event.tier,
                "config": event.config,
                "message": event.message
            })
        elif isinstance(event, TierCompletedEvent):
            data.update({
                "analysis_id": event.analysis_id,
                "tier": event.tier,
                "results": event.results,
                "duration_seconds": event.duration_seconds,
                "message": event.message
            })
        elif isinstance(event, CommodityProcessingEvent):
            data.update({
                "analysis_id": event.analysis_id,
                "tier": event.tier,
                "commodity": event.commodity,
                "action": event.action,
                "progress": event.progress,
                "details": event.details,
                "message": event.message
            })
        else:
            # Generic event - include all attributes
            for key, value in event.__dict__.items():
                if not key.startswith('_') and key not in data:
                    try:
                        # Try to serialize value
                        json.dumps(value, default=str)
                        data[key] = value
                    except:
                        # Skip non-serializable values
                        pass
                        
        return data
        
    @staticmethod
    def format_batch(events: List[DomainEvent]) -> str:
        """
        Format multiple events as a batch.
        
        Args:
            events: List of domain events
            
        Returns:
            Formatted SSE batch
        """
        formatted_events = []
        
        for i, event in enumerate(events):
            formatted = SSEEventFormatter.format_event(
                event=event,
                event_id=f"batch_{i}"
            )
            formatted_events.append(formatted)
            
        return "".join(formatted_events)