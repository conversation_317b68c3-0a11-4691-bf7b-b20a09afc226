/**
 * JavaScript/TypeScript client for consuming SSE streams from Yemen Market Integration API
 * 
 * Features:
 * - Automatic reconnection with exponential backoff
 * - Event type filtering
 * - Progress tracking
 * - Error handling
 * - TypeScript types included
 */

// TypeScript interfaces (remove if using plain JavaScript)
interface AnalysisEvent {
    event_name: string;
    analysis_id: string;
    timestamp: string;
    [key: string]: any;
}

interface ProgressEvent extends AnalysisEvent {
    progress: number;
    tier?: string;
    message?: string;
    details?: Record<string, any>;
}

interface StatusEvent extends AnalysisEvent {
    old_status: string;
    status: string;
    reason?: string;
}

interface CompletedEvent extends AnalysisEvent {
    status: 'completed';
    results_summary: Record<string, any>;
    duration_seconds: number;
    message: string;
}

interface FailedEvent extends AnalysisEvent {
    status: 'failed';
    error: string;
    error_type: string;
    tier?: string;
    message: string;
}

/**
 * SSE client for real-time analysis updates
 */
class AnalysisSSEClient {
    private baseUrl: string;
    private authToken: string;
    private eventSource: EventSource | null = null;
    private analysisId: string;
    private reconnectAttempts: number = 0;
    private maxReconnectAttempts: number = 10;
    private reconnectDelay: number = 1000; // Start with 1 second
    private maxReconnectDelay: number = 30000; // Max 30 seconds
    private listeners: Map<string, Set<Function>> = new Map();
    private isConnected: boolean = false;
    private shouldReconnect: boolean = true;

    constructor(baseUrl: string, authToken: string, analysisId: string) {
        this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
        this.authToken = authToken;
        this.analysisId = analysisId;
    }

    /**
     * Connect to the SSE endpoint
     */
    public connect(): void {
        if (this.eventSource) {
            this.disconnect();
        }

        const url = `${this.baseUrl}/api/v1/sse/analysis/${this.analysisId}/status`;
        
        // EventSource doesn't support headers directly, so we use a workaround
        // In production, consider using a polyfill like eventsource-polyfill
        this.eventSource = new EventSource(url + `?token=${this.authToken}`);

        this.eventSource.onopen = () => {
            console.log(`SSE connected for analysis ${this.analysisId}`);
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.reconnectDelay = 1000;
            this.emit('connected', { analysisId: this.analysisId });
        };

        this.eventSource.onerror = (error) => {
            console.error('SSE error:', error);
            this.isConnected = false;
            this.emit('error', error);

            if (this.shouldReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
                this.scheduleReconnect();
            } else {
                this.emit('disconnected', { 
                    reason: 'max_reconnect_attempts',
                    attempts: this.reconnectAttempts 
                });
            }
        };

        // Set up event listeners for different event types
        this.setupEventListeners();
    }

    /**
     * Set up listeners for different SSE event types
     */
    private setupEventListeners(): void {
        if (!this.eventSource) return;

        // Initial status
        this.eventSource.addEventListener('initial', (e) => {
            const data = JSON.parse(e.data);
            this.emit('initial', data);
        });

        // Progress updates
        this.eventSource.addEventListener('progress', (e) => {
            const data: ProgressEvent = JSON.parse(e.data);
            this.emit('progress', data);
        });

        // Status changes
        this.eventSource.addEventListener('status', (e) => {
            const data: StatusEvent = JSON.parse(e.data);
            this.emit('status', data);
        });

        // Tier events
        this.eventSource.addEventListener('tier_started', (e) => {
            const data = JSON.parse(e.data);
            this.emit('tier_started', data);
        });

        this.eventSource.addEventListener('tier_completed', (e) => {
            const data = JSON.parse(e.data);
            this.emit('tier_completed', data);
        });

        // Commodity updates
        this.eventSource.addEventListener('commodity_update', (e) => {
            const data = JSON.parse(e.data);
            this.emit('commodity_update', data);
        });

        // Completion
        this.eventSource.addEventListener('completed', (e) => {
            const data: CompletedEvent = JSON.parse(e.data);
            this.emit('completed', data);
            this.shouldReconnect = false; // Don't reconnect after completion
            this.disconnect();
        });

        // Failure
        this.eventSource.addEventListener('failed', (e) => {
            const data: FailedEvent = JSON.parse(e.data);
            this.emit('failed', data);
            this.shouldReconnect = false; // Don't reconnect after failure
            this.disconnect();
        });

        // Heartbeat
        this.eventSource.addEventListener('heartbeat', (e) => {
            const data = JSON.parse(e.data);
            this.emit('heartbeat', data);
        });
    }

    /**
     * Schedule a reconnection attempt with exponential backoff
     */
    private scheduleReconnect(): void {
        this.reconnectAttempts++;
        
        setTimeout(() => {
            console.log(`Reconnecting... (attempt ${this.reconnectAttempts})`);
            this.connect();
        }, this.reconnectDelay);

        // Exponential backoff
        this.reconnectDelay = Math.min(
            this.reconnectDelay * 2,
            this.maxReconnectDelay
        );
    }

    /**
     * Disconnect from the SSE endpoint
     */
    public disconnect(): void {
        this.shouldReconnect = false;
        
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        this.isConnected = false;
        this.emit('disconnected', { reason: 'manual' });
    }

    /**
     * Add event listener
     */
    public on(event: string, callback: Function): void {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, new Set());
        }
        this.listeners.get(event)!.add(callback);
    }

    /**
     * Remove event listener
     */
    public off(event: string, callback: Function): void {
        const callbacks = this.listeners.get(event);
        if (callbacks) {
            callbacks.delete(callback);
        }
    }

    /**
     * Emit an event to all listeners
     */
    private emit(event: string, data: any): void {
        const callbacks = this.listeners.get(event);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in ${event} listener:`, error);
                }
            });
        }
    }

    /**
     * Get connection status
     */
    public get connected(): boolean {
        return this.isConnected;
    }

    /**
     * Get current analysis ID
     */
    public get id(): string {
        return this.analysisId;
    }
}

/**
 * Example usage
 */
function exampleUsage() {
    // Create client
    const client = new AnalysisSSEClient(
        'https://api.yemen-market.example.com',
        'your-auth-token',
        'analysis-uuid-here'
    );

    // Set up event handlers
    client.on('connected', () => {
        console.log('Connected to SSE stream');
    });

    client.on('progress', (event: ProgressEvent) => {
        console.log(`Progress: ${event.progress}% - ${event.message}`);
        
        // Update UI
        updateProgressBar(event.progress);
        
        if (event.tier) {
            updateTierStatus(event.tier, event.progress);
        }
    });

    client.on('status', (event: StatusEvent) => {
        console.log(`Status changed: ${event.old_status} -> ${event.status}`);
        updateStatusDisplay(event.status);
    });

    client.on('tier_started', (event) => {
        console.log(`Started processing ${event.tier}`);
        addLogEntry(`${event.tier} analysis started`);
    });

    client.on('tier_completed', (event) => {
        console.log(`Completed ${event.tier} in ${event.duration_seconds}s`);
        addLogEntry(`${event.tier} completed`);
    });

    client.on('commodity_update', (event) => {
        console.log(`Processing ${event.commodity}: ${event.action}`);
        updateCommodityStatus(event.commodity, event.action);
    });

    client.on('completed', (event: CompletedEvent) => {
        console.log('Analysis completed!', event.results_summary);
        showCompletionMessage(event);
        downloadResults(event.analysis_id);
    });

    client.on('failed', (event: FailedEvent) => {
        console.error('Analysis failed:', event.error);
        showErrorMessage(event);
    });

    client.on('error', (error) => {
        console.error('Connection error:', error);
        showConnectionError();
    });

    client.on('disconnected', (event) => {
        console.log('Disconnected:', event.reason);
        if (event.reason === 'max_reconnect_attempts') {
            showReconnectFailure();
        }
    });

    // Connect
    client.connect();

    // Clean up on page unload
    window.addEventListener('beforeunload', () => {
        client.disconnect();
    });

    return client;
}

// UI update functions (implement based on your framework)
function updateProgressBar(progress: number) {
    // Update progress bar UI
}

function updateTierStatus(tier: string, progress: number) {
    // Update tier-specific UI
}

function updateStatusDisplay(status: string) {
    // Update status display
}

function addLogEntry(message: string) {
    // Add to activity log
}

function updateCommodityStatus(commodity: string, action: string) {
    // Update commodity processing display
}

function showCompletionMessage(event: CompletedEvent) {
    // Show success message
}

function showErrorMessage(event: FailedEvent) {
    // Show error message
}

function showConnectionError() {
    // Show connection error
}

function showReconnectFailure() {
    // Show reconnect failure message
}

function downloadResults(analysisId: string) {
    // Trigger results download
}

// Export for use in other modules
export { AnalysisSSEClient, AnalysisEvent, ProgressEvent, StatusEvent, CompletedEvent, FailedEvent };