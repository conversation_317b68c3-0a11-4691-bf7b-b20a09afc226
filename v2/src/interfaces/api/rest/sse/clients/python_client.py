"""
Python client for consuming SSE streams from Yemen Market Integration API.

Features:
- Automatic reconnection with exponential backoff
- Event type filtering
- Progress tracking
- Error handling
- Async/await support
"""

import asyncio
import json
import logging
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, Callable, Set
from urllib.parse import urljoin

import aiohttp
from aiohttp_sse_client import client as sse_client

logger = logging.getLogger(__name__)


class EventType(str, Enum):
    """SSE event types."""
    INITIAL = "initial"
    PROGRESS = "progress"
    STATUS = "status"
    TIER_STARTED = "tier_started"
    TIER_COMPLETED = "tier_completed"
    COMMODITY_UPDATE = "commodity_update"
    COMPLETED = "completed"
    FAILED = "failed"
    HEARTBEAT = "heartbeat"
    ERROR = "error"


@dataclass
class AnalysisEvent:
    """Base class for analysis events."""
    event_name: str
    analysis_id: str
    timestamp: str
    raw_data: Dict[str, Any]


@dataclass
class ProgressEvent(AnalysisEvent):
    """Progress update event."""
    progress: int
    tier: Optional[str] = None
    message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


@dataclass
class StatusEvent(AnalysisEvent):
    """Status change event."""
    old_status: str
    status: str
    reason: Optional[str] = None


@dataclass
class CompletedEvent(AnalysisEvent):
    """Analysis completed event."""
    status: str = "completed"
    results_summary: Dict[str, Any] = None
    duration_seconds: float = 0.0
    message: str = ""


@dataclass
class FailedEvent(AnalysisEvent):
    """Analysis failed event."""
    status: str = "failed"
    error: str = ""
    error_type: str = ""
    tier: Optional[str] = None
    message: str = ""


class AnalysisSSEClient:
    """
    SSE client for real-time analysis updates.
    
    Example:
        async with AnalysisSSEClient(base_url, auth_token, analysis_id) as client:
            async for event in client.stream():
                if isinstance(event, ProgressEvent):
                    print(f"Progress: {event.progress}%")
                elif isinstance(event, CompletedEvent):
                    print(f"Completed in {event.duration_seconds}s")
                    break
    """
    
    def __init__(
        self,
        base_url: str,
        auth_token: str,
        analysis_id: str,
        reconnect_attempts: int = 10,
        reconnect_delay: float = 1.0,
        max_reconnect_delay: float = 30.0,
        timeout: float = 300.0
    ):
        """
        Initialize SSE client.
        
        Args:
            base_url: API base URL
            auth_token: Authentication token
            analysis_id: Analysis ID to monitor
            reconnect_attempts: Maximum reconnection attempts
            reconnect_delay: Initial reconnection delay in seconds
            max_reconnect_delay: Maximum reconnection delay
            timeout: Connection timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.auth_token = auth_token
        self.analysis_id = analysis_id
        self.reconnect_attempts = reconnect_attempts
        self.reconnect_delay = reconnect_delay
        self.max_reconnect_delay = max_reconnect_delay
        self.timeout = timeout
        
        self._session: Optional[aiohttp.ClientSession] = None
        self._connected = False
        self._should_reconnect = True
        self._callbacks: Dict[EventType, Set[Callable]] = {
            event_type: set() for event_type in EventType
        }
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
        
    async def connect(self):
        """Create HTTP session for SSE connection."""
        if self._session:
            await self._session.close()
            
        self._session = aiohttp.ClientSession(
            headers={
                "Authorization": f"Bearer {self.auth_token}",
                "Accept": "text/event-stream"
            },
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        
    async def disconnect(self):
        """Close HTTP session."""
        self._should_reconnect = False
        if self._session:
            await self._session.close()
            self._session = None
            
    async def stream(self):
        """
        Stream events from SSE endpoint.
        
        Yields:
            Analysis events
        """
        url = urljoin(
            self.base_url,
            f"/api/v1/sse/analysis/{self.analysis_id}/status"
        )
        
        attempt = 0
        current_delay = self.reconnect_delay
        
        while self._should_reconnect and attempt < self.reconnect_attempts:
            try:
                logger.info(f"Connecting to SSE stream for analysis {self.analysis_id}")
                
                async with sse_client.EventSource(
                    url,
                    session=self._session,
                    timeout=self.timeout
                ) as event_source:
                    self._connected = True
                    attempt = 0  # Reset on successful connection
                    current_delay = self.reconnect_delay
                    
                    await self._emit(EventType.INITIAL, {"connected": True})
                    
                    async for event in event_source:
                        if event.type == "message":
                            # Default message type
                            continue
                            
                        # Parse event data
                        try:
                            data = json.loads(event.data)
                            parsed_event = self._parse_event(event.type, data)
                            
                            if parsed_event:
                                # Emit to callbacks
                                await self._emit(
                                    EventType(event.type),
                                    parsed_event
                                )
                                
                                # Yield event
                                yield parsed_event
                                
                                # Check if terminal event
                                if isinstance(parsed_event, (CompletedEvent, FailedEvent)):
                                    logger.info(f"Analysis {self.analysis_id} finished")
                                    self._should_reconnect = False
                                    return
                                    
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse event data: {e}")
                        except Exception as e:
                            logger.error(f"Error processing event: {e}")
                            
            except asyncio.TimeoutError:
                logger.warning(f"SSE connection timeout for analysis {self.analysis_id}")
                attempt += 1
                
            except aiohttp.ClientError as e:
                logger.error(f"SSE connection error: {e}")
                attempt += 1
                
            except Exception as e:
                logger.error(f"Unexpected error in SSE stream: {e}")
                attempt += 1
                
            finally:
                self._connected = False
                
            if self._should_reconnect and attempt < self.reconnect_attempts:
                logger.info(f"Reconnecting in {current_delay}s (attempt {attempt})")
                await asyncio.sleep(current_delay)
                current_delay = min(current_delay * 2, self.max_reconnect_delay)
                
        logger.error(f"Max reconnection attempts reached for analysis {self.analysis_id}")
        
    def _parse_event(self, event_type: str, data: Dict[str, Any]) -> Optional[AnalysisEvent]:
        """
        Parse raw event data into typed event objects.
        
        Args:
            event_type: SSE event type
            data: Event data
            
        Returns:
            Parsed event or None
        """
        base_args = {
            "event_name": data.get("event_name", event_type),
            "analysis_id": data.get("analysis_id", self.analysis_id),
            "timestamp": data.get("timestamp", datetime.utcnow().isoformat()),
            "raw_data": data
        }
        
        try:
            if event_type == EventType.PROGRESS:
                return ProgressEvent(
                    **base_args,
                    progress=data.get("progress", 0),
                    tier=data.get("tier"),
                    message=data.get("message"),
                    details=data.get("details")
                )
                
            elif event_type == EventType.STATUS:
                return StatusEvent(
                    **base_args,
                    old_status=data.get("old_status", ""),
                    status=data.get("status", ""),
                    reason=data.get("reason")
                )
                
            elif event_type == EventType.COMPLETED:
                return CompletedEvent(
                    **base_args,
                    results_summary=data.get("results_summary", {}),
                    duration_seconds=data.get("duration_seconds", 0.0),
                    message=data.get("message", "")
                )
                
            elif event_type == EventType.FAILED:
                return FailedEvent(
                    **base_args,
                    error=data.get("error", ""),
                    error_type=data.get("error_type", ""),
                    tier=data.get("tier"),
                    message=data.get("message", "")
                )
                
            else:
                # Generic event
                return AnalysisEvent(**base_args)
                
        except Exception as e:
            logger.error(f"Failed to parse {event_type} event: {e}")
            return None
            
    def on(self, event_type: EventType, callback: Callable):
        """
        Register event callback.
        
        Args:
            event_type: Event type to listen for
            callback: Async callback function
        """
        self._callbacks[event_type].add(callback)
        
    def off(self, event_type: EventType, callback: Callable):
        """
        Unregister event callback.
        
        Args:
            event_type: Event type
            callback: Callback to remove
        """
        self._callbacks[event_type].discard(callback)
        
    async def _emit(self, event_type: EventType, event: Any):
        """
        Emit event to registered callbacks.
        
        Args:
            event_type: Event type
            event: Event data
        """
        for callback in self._callbacks.get(event_type, set()):
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event)
                else:
                    callback(event)
            except Exception as e:
                logger.error(f"Error in {event_type} callback: {e}")
                
    @property
    def connected(self) -> bool:
        """Check if currently connected."""
        return self._connected
        
    @property
    def id(self) -> str:
        """Get analysis ID."""
        return self.analysis_id


async def example_usage():
    """Example usage of SSE client."""
    base_url = "https://api.yemen-market.example.com"
    auth_token = "your-auth-token"
    analysis_id = "analysis-uuid-here"
    
    # Create client
    client = AnalysisSSEClient(base_url, auth_token, analysis_id)
    
    # Option 1: Using callbacks
    async def on_progress(event: ProgressEvent):
        print(f"Progress: {event.progress}% - {event.message}")
        
    async def on_completed(event: CompletedEvent):
        print(f"Analysis completed in {event.duration_seconds} seconds")
        print(f"Results: {event.results_summary}")
        
    client.on(EventType.PROGRESS, on_progress)
    client.on(EventType.COMPLETED, on_completed)
    
    # Option 2: Using async iteration
    async with client:
        async for event in client.stream():
            if isinstance(event, ProgressEvent):
                print(f"Progress: {event.progress}%")
                
                if event.tier:
                    print(f"  Tier: {event.tier}")
                    
                if event.details:
                    print(f"  Details: {event.details}")
                    
            elif isinstance(event, StatusEvent):
                print(f"Status changed: {event.old_status} -> {event.status}")
                
            elif isinstance(event, CompletedEvent):
                print(f"Analysis completed!")
                print(f"Duration: {event.duration_seconds}s")
                print(f"Results summary: {event.results_summary}")
                break
                
            elif isinstance(event, FailedEvent):
                print(f"Analysis failed: {event.error}")
                print(f"Error type: {event.error_type}")
                if event.tier:
                    print(f"Failed at tier: {event.tier}")
                break


if __name__ == "__main__":
    # Run example
    asyncio.run(example_usage())