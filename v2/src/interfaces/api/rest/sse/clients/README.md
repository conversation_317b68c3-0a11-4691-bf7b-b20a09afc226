# SSE Client Utilities

This directory contains client implementations for consuming Server-Sent Events (SSE) from the Yemen Market Integration API.

## Available Clients

### JavaScript Client (`javascript_client.js`)

A full-featured JavaScript/TypeScript client with:
- Automatic reconnection with exponential backoff
- Event type filtering
- Progress tracking
- TypeScript types included
- Connection state management

**Usage:**
```javascript
import { AnalysisSSEClient } from './javascript_client.js';

const client = new AnalysisSSEClient(
    'https://api.yemen-market.example.com',
    'your-auth-token',
    'analysis-uuid'
);

client.on('progress', (event) => {
    console.log(`Progress: ${event.progress}%`);
});

client.connect();
```

### Python Client (`python_client.py`)

An async Python client using `aiohttp` with:
- Async/await support
- Automatic reconnection
- Event parsing and typing
- Context manager support

**Usage:**
```python
from python_client import AnalysisSSEClient

async with AnalysisSSEClient(base_url, auth_token, analysis_id) as client:
    async for event in client.stream():
        if isinstance(event, ProgressEvent):
            print(f"Progress: {event.progress}%")
```

### HTML Example (`example.html`)

A complete interactive web interface demonstrating:
- Real-time progress visualization
- Multi-tier progress tracking
- Commodity processing status
- Activity log
- Connection management

Open `example.html` in a web browser and configure:
1. API URL
2. Authentication token
3. Analysis ID

## Installation

### JavaScript Client

No installation required for browser usage. For Node.js:

```bash
npm install eventsource  # For Node.js EventSource support
```

### Python Client

```bash
pip install aiohttp aiohttp-sse-client
```

## Authentication

All clients require authentication. Options:

1. **Bearer Token** (Recommended):
   - Include in Authorization header
   - Most secure method

2. **Query Parameter** (Alternative):
   - Append `?token=YOUR_TOKEN` to URL
   - Use when headers not supported

## Event Types

All clients handle these event types:

- `initial` - Connection established, current status
- `progress` - Progress updates
- `status` - Status changes
- `tier_started` - Tier processing started
- `tier_completed` - Tier processing completed
- `commodity_update` - Commodity-specific updates
- `completed` - Analysis completed successfully
- `failed` - Analysis failed
- `heartbeat` - Keep-alive signal
- `error` - Error notifications

## Error Handling

All clients implement:

1. **Automatic Reconnection**: Exponential backoff strategy
2. **Connection Monitoring**: Detect disconnections
3. **Error Events**: Handle server-side errors
4. **Graceful Shutdown**: Clean disconnect on completion

## Best Practices

1. **Always close connections** when analysis completes
2. **Implement reconnection logic** for network issues
3. **Handle all event types** even if not displayed
4. **Monitor connection state** for user feedback
5. **Use event filtering** to reduce bandwidth
6. **Process events asynchronously** to avoid blocking

## Testing

Test clients against the API:

```bash
# Start a test server
python -m http.server 8000

# Or use the actual API
curl -N -H "Authorization: Bearer TOKEN" \
     https://api.example.com/api/v1/sse/analysis/ID/status
```

## Troubleshooting

### Connection Issues

1. Check authentication token validity
2. Verify analysis ID exists
3. Ensure SSE endpoint is accessible
4. Check for proxy/firewall blocking

### Event Processing

1. Verify JSON parsing for malformed events
2. Check event type handlers are registered
3. Monitor console/logs for errors
4. Validate event data structure

### Performance

1. Implement event throttling if needed
2. Batch UI updates for rapid events
3. Monitor memory usage for long streams
4. Close connections when inactive