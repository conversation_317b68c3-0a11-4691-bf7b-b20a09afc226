"""Error handling middleware."""

import sys
import traceback
from datetime import datetime
from typing import Callable

from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from starlette.middleware.base import BaseHTTPMiddleware

from .....core.domain.shared.exceptions import (
    DomainException,
    EntityNotFoundException,
    ValidationException as DomainValidationException,
)
from .....infrastructure.logging import Logger

logger = Logger(__name__)


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """Middleware for handling exceptions and converting to proper responses."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and handle exceptions."""
        try:
            response = await call_next(request)
            return response
            
        except EntityNotFoundException as e:
            return JSONResponse(
                status_code=404,
                content={
                    "error": {
                        "type": "not_found",
                        "message": str(e),
                        "details": {
                            "entity_type": getattr(e, 'entity_type', None),
                            "entity_id": getattr(e, 'entity_id', None)
                        }
                    },
                    "request_id": getattr(request.state, 'request_id', None),
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
        except DomainValidationException as e:
            return JSONResponse(
                status_code=422,
                content={
                    "error": {
                        "type": "validation_error",
                        "message": str(e),
                        "details": {
                            "code": getattr(e, 'code', None)
                        }
                    },
                    "request_id": getattr(request.state, 'request_id', None),
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
        except DomainException as e:
            return JSONResponse(
                status_code=400,
                content={
                    "error": {
                        "type": "domain_error",
                        "message": str(e),
                        "details": {
                            "code": getattr(e, 'code', None)
                        }
                    },
                    "request_id": getattr(request.state, 'request_id', None),
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
        except Exception as e:
            # Log the full traceback
            tb = "".join(traceback.format_exception(*sys.exc_info()))
            logger.error(f"Unhandled exception: {tb}")
            
            # Return generic error response
            return JSONResponse(
                status_code=500,
                content={
                    "error": {
                        "type": "internal_server_error",
                        "message": "An unexpected error occurred",
                        "details": None
                    },
                    "request_id": getattr(request.state, 'request_id', None),
                    "timestamp": datetime.utcnow().isoformat()
                }
            )


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle request validation errors."""
    
    errors = []
    for error in exc.errors():
        errors.append({
            "field": ".".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })
    
    logger.warning(f"Validation error on {request.url.path}: {errors}")
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": {
                "type": "ValidationError",
                "message": "Request validation failed",
                "details": errors
            },
            "request_id": getattr(request.state, 'request_id', None),
            "timestamp": datetime.utcnow().isoformat()
        }
    )


def register_exception_handlers(app):
    """Register all exception handlers with the FastAPI app."""
    
    # Validation exceptions
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(ValidationError, validation_exception_handler)