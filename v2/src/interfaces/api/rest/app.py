"""
Enhanced FastAPI application with SSE support.
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware

from ....shared.container import Container
from ....infrastructure.logging import Logger
from .middleware import (
    RequestIDMiddleware,
    LoggingMiddleware,
    ErrorHandlerMiddleware,
    AuthenticationMiddleware
)
from .routes import analysis, markets, commodities, prices, sse
from .sse import SSEConnectionManager, SSEEventHandler

logger = Logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Manage application lifecycle events.
    """
    # Startup
    logger.info("Starting Yemen Market Integration API v2")
    
    # Initialize container
    await Container.initialize()
    
    # Initialize SSE services
    event_bus = await Container.event_bus()
    
    # Create and start SSE manager
    sse_manager = SSEConnectionManager(
        heartbeat_interval=30.0,
        connection_timeout=300.0,
        max_queue_size=1000
    )
    await sse_manager.start()
    
    # Create and start SSE handler
    sse_handler = SSEEventHandler(event_bus, sse_manager)
    await sse_handler.start()
    
    # Store in app state
    app.state.sse_manager = sse_manager
    app.state.sse_handler = sse_handler
    
    # Start event bus if it's an AsyncEventBus
    if hasattr(event_bus, 'start'):
        await event_bus.start()
    
    logger.info("API startup complete")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Yemen Market Integration API v2")
    
    # Stop SSE services
    if hasattr(app.state, 'sse_handler'):
        await app.state.sse_handler.stop()
    
    if hasattr(app.state, 'sse_manager'):
        await app.state.sse_manager.stop()
    
    # Stop event bus
    if hasattr(event_bus, 'stop'):
        await event_bus.stop()
    
    # Cleanup container
    await Container.cleanup()
    
    logger.info("API shutdown complete")


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        Configured FastAPI application
    """
    app = FastAPI(
        title="Yemen Market Integration API",
        description="Econometric analysis of market integration in Yemen",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan
    )
    
    # Add middleware in reverse order (last added is first executed)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    app.add_middleware(ErrorHandlerMiddleware)
    app.add_middleware(AuthenticationMiddleware)
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(RequestIDMiddleware)
    
    # Include routers
    app.include_router(
        analysis.router,
        prefix="/api/v1",
        tags=["analysis"]
    )
    
    app.include_router(
        markets.router,
        prefix="/api/v1",
        tags=["markets"]
    )
    
    app.include_router(
        commodities.router,
        prefix="/api/v1",
        tags=["commodities"]
    )
    
    app.include_router(
        prices.router,
        prefix="/api/v1",
        tags=["prices"]
    )
    
    app.include_router(
        sse.router,
        prefix="/api/v1",
        tags=["sse", "real-time"]
    )
    
    # Root endpoint
    @app.get("/")
    async def root():
        """API root endpoint."""
        return {
            "name": "Yemen Market Integration API",
            "version": "2.0.0",
            "status": "operational",
            "documentation": "/docs",
            "endpoints": {
                "analysis": "/api/v1/analysis",
                "markets": "/api/v1/markets",
                "commodities": "/api/v1/commodities",
                "prices": "/api/v1/prices",
                "sse": "/api/v1/sse"
            }
        }
    
    # Health check
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "service": "yemen-market-integration-api",
            "version": "2.0.0"
        }
    
    return app


# Create app instance
app = create_app()


# SSE helper functions for routes
def get_sse_manager() -> SSEConnectionManager:
    """Get SSE manager from app state."""
    return app.state.sse_manager


def get_sse_handler() -> SSEEventHandler:
    """Get SSE handler from app state."""
    return app.state.sse_handler