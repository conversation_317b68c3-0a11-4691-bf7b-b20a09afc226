"""Main FastAPI application for the REST API."""

from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.responses import RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
from dependency_injector.wiring import inject, Provide
from typing import Any

from ....shared.container import Container
from ....infrastructure.logging import Logger
from .routes import analysis, markets, commodities
from .routes.auth import auth_router, users_router, api_keys_router
from ..policy_endpoints import router as policy_router
from .middleware.error_handler import ErrorHandlerMiddleware, register_exception_handlers
from .middleware.request_id import RequestIDMiddleware
from ....infrastructure.security import RateLimitMiddleware, SecurityHeadersMiddleware

logger = Logger(__name__)

def create_app() -> FastAPI:
    """
    Creates and configures the FastAPI application.
    """
    app = FastAPI(
        title="Yemen Market Integration API v2",
        description="API for econometric analysis of market integration in Yemen, focusing on conflict impacts.",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json"
    )

    # Initialize Dependency Injector container
    container = Container()
    app.container = container # Attach container to app for easy access
    
    # Configure logging
    Logger.configure_logging(log_level="INFO", log_to_console=True)
    logger.info("FastAPI application starting up.")
    
    # Add middleware (order matters - add in reverse order of execution)
    # CORS should be last to execute first
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Security headers
    app.add_middleware(SecurityHeadersMiddleware)
    
    # Rate limiting
    app.add_middleware(RateLimitMiddleware)
    
    # Error handling and request tracking
    app.add_middleware(ErrorHandlerMiddleware)
    app.add_middleware(RequestIDMiddleware)
    
    # Register exception handlers
    register_exception_handlers(app)

    # Include routers
    app.include_router(analysis.router, prefix="/api/v1")
    app.include_router(markets.router, prefix="/api/v1")
    app.include_router(commodities.router, prefix="/api/v1")
    app.include_router(policy_router, prefix="/api/v1", tags=["Policy Analysis"])

    @app.get("/", include_in_schema=False)
    async def root():
        return RedirectResponse(url="/docs")
    
    @app.get("/health", tags=["Health"])
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "version": "2.0.0",
            "service": "yemen-market-integration-api"
        }

    @app.on_event("startup")
    async def startup_event():
        logger.info("Application startup complete.")

    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info("Application shutting down.")
        # Clean up resources if necessary
        if hasattr(app.container, 'shutdown_resources'):
            await app.container.shutdown_resources()

    return app

app = create_app()
