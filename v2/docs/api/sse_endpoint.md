# SSE (Server-Sent Events) Analysis Status Endpoint

## Overview

The SSE endpoint provides real-time status updates for running econometric analyses. It streams events as they occur during the three-tier analysis process, enabling clients to display live progress updates without polling.

## Endpoint

```
GET /api/v2/analyses/{id}/status
```

### Parameters

- `id` (path parameter): The unique identifier of the analysis job

### Response

- **Content-Type**: `text/event-stream`
- **Status Code**: 200 OK (or 404 if analysis not found)

## Event Format

Events are streamed in the SSE format, with each event containing JSON data:

```
data: {
  "event": "event_name",
  "analysis_id": "analysis-123",
  "timestamp": "2025-05-31T10:30:00Z",
  ... additional fields ...
}

```

## Event Types

### 1. Initial Status Event

Sent immediately upon connection to provide current analysis state.

```json
{
  "event": "initial",
  "analysis_id": "analysis-123",
  "status": "running",
  "progress": 25,
  "timestamp": "2025-05-31T10:30:00Z"
}
```

### 2. Progress Events

Sent when analysis makes progress.

```json
{
  "event": "analysis.progress.analysis-123",
  "analysis_id": "analysis-123",
  "progress": 50,
  "tier": "tier2",
  "message": "Processing commodity: Wheat",
  "timestamp": "2025-05-31T10:31:00Z"
}
```

### 3. Status Change Events

Sent when analysis status changes (e.g., pending → running).

```json
{
  "event": "analysis.status.analysis-123",
  "analysis_id": "analysis-123",
  "status": "running",
  "timestamp": "2025-05-31T10:30:00Z"
}
```

### 4. Tier Events

Sent when individual tiers start or complete.

```json
{
  "event": "analysis.tier.started.analysis-123",
  "analysis_id": "analysis-123",
  "tier": "tier1",
  "message": "Started processing tier1",
  "timestamp": "2025-05-31T10:30:00Z"
}
```

### 5. Completion Event

Sent when analysis completes successfully.

```json
{
  "event": "analysis.completed.analysis-123",
  "analysis_id": "analysis-123",
  "status": "completed",
  "message": "Analysis completed in 45.2 seconds",
  "timestamp": "2025-05-31T10:35:00Z"
}
```

### 6. Failure Event

Sent if analysis fails.

```json
{
  "event": "analysis.failed.analysis-123",
  "analysis_id": "analysis-123",
  "status": "failed",
  "error": "Insufficient data for commodity analysis",
  "tier": "tier2",
  "timestamp": "2025-05-31T10:32:00Z"
}
```

### 7. Heartbeat

Sent periodically to keep the connection alive.

```
: heartbeat 2025-05-31T10:33:00Z

```

## Client Examples

### JavaScript/Browser

```javascript
const eventSource = new EventSource('/api/v2/analyses/analysis-123/status');

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received event:', data);
    
    // Update UI based on event type
    if (data.event === 'analysis.progress') {
        updateProgressBar(data.progress);
    } else if (data.event === 'analysis.completed') {
        showResults();
        eventSource.close();
    }
};

eventSource.onerror = function(error) {
    console.error('SSE connection error:', error);
};
```

### Python

```python
import httpx
import json

async def monitor_analysis(analysis_id: str):
    async with httpx.AsyncClient() as client:
        async with client.stream(
            'GET', 
            f'http://api.example.com/api/v2/analyses/{analysis_id}/status'
        ) as response:
            async for line in response.aiter_lines():
                if line.startswith('data: '):
                    data = json.loads(line[6:])
                    print(f"Event: {data['event']} - Progress: {data.get('progress')}%")
```

### cURL

```bash
curl -N -H "Accept: text/event-stream" \
  https://api.example.com/api/v2/analyses/analysis-123/status
```

## Connection Management

### Timeouts

- The connection sends heartbeats every 30 seconds to prevent timeouts
- Clients should handle reconnection if the connection drops

### Completion

- The stream automatically closes when the analysis completes or fails
- Clients should handle the connection close event

### Error Handling

- 404 Not Found: Analysis ID does not exist
- 500 Internal Server Error: Server-side error

## Best Practices

1. **Implement Reconnection Logic**: Network issues can cause disconnections
2. **Handle All Event Types**: Even if not all are displayed to users
3. **Close Connections**: Explicitly close when analysis completes
4. **Parse Safely**: Always validate JSON before parsing
5. **Update UI Efficiently**: Batch updates if events arrive rapidly

## Example Flow

1. Client initiates analysis via POST `/api/v2/analysis/run-three-tier`
2. Server returns `job_id`
3. Client connects to SSE endpoint using `job_id`
4. Server streams events as analysis progresses
5. Client updates UI in real-time
6. Connection closes when analysis completes

## Security Considerations

- Ensure proper authentication/authorization before allowing SSE connections
- Consider rate limiting to prevent abuse
- Validate analysis IDs to prevent information leakage
- Use HTTPS to encrypt the event stream