name: Comprehensive Testing Suite

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'v2/**'
      - '.github/workflows/comprehensive-testing.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'v2/**'
  schedule:
    # Run nightly tests at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_level:
        description: 'Test level to run'
        required: true
        default: 'standard'
        type: choice
        options:
          - standard
          - comprehensive
          - performance
          - security

env:
  PYTHON_VERSION: '3.11'
  POSTGRES_DB: yemen_test_db
  POSTGRES_USER: test_user
  POSTGRES_PASSWORD: test_password
  REDIS_URL: redis://localhost:6379/0

jobs:
  # Code Quality and Static Analysis
  code-quality:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Cache Poetry dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/pypoetry
            v2/.venv
          key: ${{ runner.os }}-poetry-${{ hashFiles('v2/pyproject.toml') }}

      - name: Install Poetry
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Install dependencies
        working-directory: v2
        run: |
          poetry config virtualenvs.in-project true
          poetry install --with dev

      - name: Run linting (Ruff)
        working-directory: v2
        run: poetry run ruff check src/ tests/ --output-format=github

      - name: Run type checking (MyPy)
        working-directory: v2
        run: poetry run mypy src/ --show-error-codes

      - name: Check code formatting (Black)
        working-directory: v2
        run: poetry run black --check src/ tests/

      - name: Check import sorting (isort)
        working-directory: v2
        run: poetry run isort --check-only src/ tests/

      - name: Security scan (Bandit)
        working-directory: v2
        run: |
          pip install bandit[toml]
          bandit -r src/ -f json -o bandit-report.json || true

      - name: Upload security scan results
        uses: actions/upload-artifact@v3
        with:
          name: security-scan-results
          path: v2/bandit-report.json

  # Unit Tests
  unit-tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.11', '3.12']
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/pypoetry
            v2/.venv
          key: ${{ runner.os }}-${{ matrix.python-version }}-poetry-${{ hashFiles('v2/pyproject.toml') }}

      - name: Install Poetry and dependencies
        working-directory: v2
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
          echo "$HOME/.local/bin" >> $GITHUB_PATH
          poetry config virtualenvs.in-project true
          poetry install --with dev

      - name: Run unit tests
        working-directory: v2
        run: |
          poetry run pytest tests/unit/ \
            --cov=src \
            --cov-report=xml \
            --cov-report=html \
            --cov-report=term-missing \
            --junit-xml=junit-unit.xml \
            --tb=short \
            -v

      - name: Upload unit test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: unit-test-results-${{ matrix.python-version }}
          path: |
            v2/junit-unit.xml
            v2/coverage.xml
            v2/htmlcov/

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: v2/coverage.xml
          flags: unit-tests
          name: unit-tests-${{ matrix.python-version }}

  # Integration Tests
  integration-tests:
    runs-on: ubuntu-latest
    needs: [code-quality]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: ${{ env.POSTGRES_DB }}
          POSTGRES_USER: ${{ env.POSTGRES_USER }}
          POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Poetry and dependencies
        working-directory: v2
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
          echo "$HOME/.local/bin" >> $GITHUB_PATH
          poetry config virtualenvs.in-project true
          poetry install --with dev

      - name: Wait for services
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client redis-tools
          
          # Wait for PostgreSQL
          until pg_isready -h localhost -p 5432 -U ${{ env.POSTGRES_USER }}; do
            echo "Waiting for PostgreSQL..."
            sleep 2
          done
          
          # Wait for Redis
          until redis-cli -h localhost -p 6379 ping; do
            echo "Waiting for Redis..."
            sleep 2
          done

      - name: Run database migrations
        working-directory: v2
        env:
          DATABASE_URL: postgresql://${{ env.POSTGRES_USER }}:${{ env.POSTGRES_PASSWORD }}@localhost:5432/${{ env.POSTGRES_DB }}
        run: |
          # Run any database setup/migrations here
          echo "Database setup completed"

      - name: Run integration tests
        working-directory: v2
        env:
          DATABASE_URL: postgresql://${{ env.POSTGRES_USER }}:${{ env.POSTGRES_PASSWORD }}@localhost:5432/${{ env.POSTGRES_DB }}
          REDIS_URL: ${{ env.REDIS_URL }}
        run: |
          poetry run pytest tests/integration/ \
            --cov=src \
            --cov-append \
            --cov-report=xml \
            --junit-xml=junit-integration.xml \
            --tb=short \
            -v \
            --timeout=300

      - name: Upload integration test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: |
            v2/junit-integration.xml
            v2/coverage.xml

  # End-to-End Tests
  e2e-tests:
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: ${{ env.POSTGRES_DB }}
          POSTGRES_USER: ${{ env.POSTGRES_USER }}
          POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Poetry and dependencies
        working-directory: v2
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
          echo "$HOME/.local/bin" >> $GITHUB_PATH
          poetry config virtualenvs.in-project true
          poetry install --with dev

      - name: Start application services
        working-directory: v2
        env:
          DATABASE_URL: postgresql://${{ env.POSTGRES_USER }}:${{ env.POSTGRES_PASSWORD }}@localhost:5432/${{ env.POSTGRES_DB }}
          REDIS_URL: ${{ env.REDIS_URL }}
        run: |
          # Start the application in background
          poetry run uvicorn src.interfaces.api.rest.app:app --host 0.0.0.0 --port 8000 &
          echo $! > app.pid
          
          # Wait for application to start
          sleep 10
          
          # Health check
          curl -f http://localhost:8000/health || exit 1

      - name: Run E2E tests
        working-directory: v2
        env:
          API_BASE_URL: http://localhost:8000
          DATABASE_URL: postgresql://${{ env.POSTGRES_USER }}:${{ env.POSTGRES_PASSWORD }}@localhost:5432/${{ env.POSTGRES_DB }}
          REDIS_URL: ${{ env.REDIS_URL }}
        run: |
          poetry run pytest tests/e2e/ \
            --junit-xml=junit-e2e.xml \
            --tb=short \
            -v \
            --timeout=600

      - name: Stop application
        if: always()
        working-directory: v2
        run: |
          if [ -f app.pid ]; then
            kill $(cat app.pid) || true
            rm app.pid
          fi

      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-results
          path: v2/junit-e2e.xml

  # Performance Tests
  performance-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || (github.event_name == 'workflow_dispatch' && github.event.inputs.test_level == 'performance')
    needs: [integration-tests]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: ${{ env.POSTGRES_DB }}
          POSTGRES_USER: ${{ env.POSTGRES_USER }}
          POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y htop stress-ng

      - name: Install Poetry and dependencies
        working-directory: v2
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
          echo "$HOME/.local/bin" >> $GITHUB_PATH
          poetry config virtualenvs.in-project true
          poetry install --with dev

      - name: Run performance tests
        working-directory: v2
        env:
          DATABASE_URL: postgresql://${{ env.POSTGRES_USER }}:${{ env.POSTGRES_PASSWORD }}@localhost:5432/${{ env.POSTGRES_DB }}
          REDIS_URL: ${{ env.REDIS_URL }}
        run: |
          poetry run pytest tests/performance/ \
            --junit-xml=junit-performance.xml \
            --tb=short \
            -v \
            --timeout=1800 \
            -m performance

      - name: Upload performance test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-test-results
          path: |
            v2/junit-performance.xml
            v2/performance-reports/

  # Security Tests
  security-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || (github.event_name == 'workflow_dispatch' && github.event.inputs.test_level == 'security')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Poetry and dependencies
        working-directory: v2
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
          echo "$HOME/.local/bin" >> $GITHUB_PATH
          poetry config virtualenvs.in-project true
          poetry install --with dev

      - name: Run security tests
        working-directory: v2
        run: |
          poetry run pytest tests/unit/infrastructure/test_security_comprehensive.py \
            --junit-xml=junit-security.xml \
            --tb=short \
            -v

      - name: Run OWASP ZAP security scan
        run: |
          docker run -t owasp/zap2docker-stable zap-baseline.py \
            -t http://localhost:8000 \
            -J zap-report.json || true

      - name: Upload security test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-test-results
          path: |
            v2/junit-security.xml
            zap-report.json

  # Coverage Report
  coverage-report:
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests]
    if: always()
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download all test artifacts
        uses: actions/download-artifact@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install coverage tools
        run: |
          pip install coverage[toml] codecov

      - name: Combine coverage reports
        run: |
          # Combine all coverage.xml files if they exist
          find . -name "coverage.xml" -exec echo "Found coverage file: {}" \;
          
          # Create combined coverage report
          if ls ./*/coverage.xml 1> /dev/null 2>&1; then
            # Merge coverage files and generate report
            coverage combine || true
            coverage report --show-missing || true
            coverage html -d combined-coverage || true
          fi

      - name: Upload combined coverage
        uses: actions/upload-artifact@v3
        with:
          name: combined-coverage-report
          path: combined-coverage/

      - name: Coverage comment
        if: github.event_name == 'pull_request'
        run: |
          # Add coverage comment to PR (simplified)
          echo "Coverage report generated and uploaded to artifacts."

  # Test Summary
  test-summary:
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests, integration-tests, e2e-tests]
    if: always()
    
    steps:
      - name: Download all test results
        uses: actions/download-artifact@v3

      - name: Generate test summary
        run: |
          echo "## Test Summary" > test-summary.md
          echo "" >> test-summary.md
          
          # Count test files
          junit_files=$(find . -name "junit-*.xml" | wc -l)
          echo "- Total test suites: $junit_files" >> test-summary.md
          
          # Basic pass/fail summary (would be more sophisticated in practice)
          if [ "${{ needs.unit-tests.result }}" == "success" ]; then
            echo "- ✅ Unit tests: PASSED" >> test-summary.md
          else
            echo "- ❌ Unit tests: FAILED" >> test-summary.md
          fi
          
          if [ "${{ needs.integration-tests.result }}" == "success" ]; then
            echo "- ✅ Integration tests: PASSED" >> test-summary.md
          else
            echo "- ❌ Integration tests: FAILED" >> test-summary.md
          fi
          
          if [ "${{ needs.e2e-tests.result }}" == "success" ]; then
            echo "- ✅ E2E tests: PASSED" >> test-summary.md
          else
            echo "- ❌ E2E tests: FAILED" >> test-summary.md
          fi
          
          echo "" >> test-summary.md
          echo "### Code Quality" >> test-summary.md
          
          if [ "${{ needs.code-quality.result }}" == "success" ]; then
            echo "- ✅ Code quality checks: PASSED" >> test-summary.md
          else
            echo "- ❌ Code quality checks: FAILED" >> test-summary.md
          fi
          
          cat test-summary.md

      - name: Upload test summary
        uses: actions/upload-artifact@v3
        with:
          name: test-summary
          path: test-summary.md

  # Notification
  notify:
    runs-on: ubuntu-latest
    needs: [test-summary]
    if: always() && (github.event_name == 'schedule' || github.ref == 'refs/heads/main')
    
    steps:
      - name: Notify on failure
        if: contains(needs.*.result, 'failure')
        run: |
          echo "Tests failed on main branch or scheduled run"
          # In practice, this would send notifications to Slack, email, etc.
          
      - name: Notify on success
        if: ${{ !contains(needs.*.result, 'failure') }}
        run: |
          echo "All tests passed successfully"