# Methodological Action Plan

Based on the comprehensive methodological review, this action plan outlines specific steps to address identified gaps and enhance the Yemen Market Integration analysis to meet World Bank publication standards.

## Phase 1: Critical Enhancements (Week 1-2)

### 1.1 Implement Spatial Features
**File:** `src/yemen_market/features/data_preparation.py`

```python
def add_spatial_features(panel: pd.DataFrame, k: int = 3) -> pd.DataFrame:
    """Add K-nearest neighbor price features.
    
    Parameters
    ----------
    panel : pd.DataFrame
        Panel data with market locations
    k : int
        Number of nearest neighbors
        
    Returns
    -------
    pd.DataFrame
        Panel with spatial features added
    """
    # Implementation:
    # 1. Calculate distance matrix between markets
    # 2. For each market, identify k nearest neighbors
    # 3. Add spatial lag of prices (average of k neighbors)
    # 4. Add spatial lag of conflict
    # 5. Add distance-weighted averages
```

### 1.2 Add Interaction Effects
**File:** `src/yemen_market/features/feature_engineering.py`

```python
def add_interaction_terms(panel: pd.DataFrame) -> pd.DataFrame:
    """Add critical interaction terms for analysis."""
    # Zone × Time interactions
    panel['zone_year'] = panel['zone_DFA'].astype(str) + '_' + panel['year'].astype(str)
    panel['zone_month'] = panel['zone_DFA'].astype(str) + '_' + panel['month'].astype(str)
    
    # Conflict × Commodity interactions  
    commodities = pd.get_dummies(panel['commodity'], prefix='comm')
    for col in commodities.columns:
        panel[f'conflict_x_{col}'] = panel['conflict_intensity'] * commodities[col]
    
    # Zone × Commodity interactions
    for col in commodities.columns:
        panel[f'zone_x_{col}'] = panel['zone_DFA'].astype(str) + '_' + commodities[col].astype(str)
    
    return panel
```

### 1.3 Dual Exchange Rate Treatment
**File:** `src/yemen_market/features/data_preparation.py`

```python
def add_exchange_rate_features(panel: pd.DataFrame) -> pd.DataFrame:
    """Add dual exchange rate system features."""
    # Calculate exchange rate premium
    panel['er_premium'] = (panel['parallel_rate'] / panel['official_rate'] - 1) * 100
    
    # Interaction with control zones
    panel['er_premium_x_DFA'] = panel['er_premium'] * (panel['zone_DFA'] == 1)
    
    # Time-varying premium
    panel['er_premium_ma3'] = panel.groupby('market')['er_premium'].transform(
        lambda x: x.rolling(3, min_periods=1).mean()
    )
    
    return panel
```

## Phase 2: Diagnostic Test Additions (Week 2-3)

### 2.1 Ramsey RESET Test
**File:** `src/yemen_market/models/three_tier/diagnostics/test_implementations.py`

```python
def ramsey_reset_test(y: np.ndarray, X: np.ndarray, powers: List[int] = [2, 3]) -> Tuple[float, float, str]:
    """Ramsey RESET test for functional form misspecification."""
    # Implementation following statsmodels.stats.diagnostic.reset_ramsey
```

### 2.2 Chow Test for Structural Breaks
**File:** `src/yemen_market/models/three_tier/diagnostics/structural_breaks.py`

```python
def chow_test_panel(panel_data: pd.DataFrame, break_date: str, 
                    formula: str) -> Dict[str, Any]:
    """Chow test for structural break in panel data."""
    # Split sample at break_date
    # Estimate models for both periods
    # Calculate Chow statistic
```

### 2.3 Spatial Autocorrelation Tests
**File:** `src/yemen_market/models/three_tier/diagnostics/spatial_tests.py`

```python
def moran_i_test(residuals: pd.DataFrame, W: np.ndarray) -> Dict[str, float]:
    """Moran's I test for spatial autocorrelation."""
    # Calculate Moran's I statistic
    # Compute expected value and variance
    # Return test statistic and p-value
```

## Phase 3: Model Enhancements (Week 3-4)

### 3.1 Threshold VECM Confidence Intervals
**File:** `src/yemen_market/models/three_tier/tier2_commodity/threshold_vecm.py`

```python
def bootstrap_threshold_ci(self, data: pd.DataFrame, alpha: float = 0.05, 
                          n_bootstrap: int = 1000) -> Tuple[float, float]:
    """Bootstrap confidence intervals for threshold estimate."""
    # Implement fixed-regressor bootstrap
    # Return (lower_ci, upper_ci)
```

### 3.2 Dynamic Factor Models
**File:** `src/yemen_market/models/three_tier/tier3_validation/dynamic_factors.py`

```python
class DynamicFactorModel:
    """Dynamic factor model for panel data."""
    
    def __init__(self, n_factors: int = 2, n_lags: int = 1):
        self.n_factors = n_factors
        self.n_lags = n_lags
    
    def fit(self, panel_data: pd.DataFrame) -> 'DynamicFactorModel':
        """Estimate dynamic factor model using EM algorithm."""
        # Implement Doz, Giannone, and Reichlin (2011) approach
```

### 3.3 Spatial Panel Regression
**File:** `src/yemen_market/models/three_tier/tier1_pooled/spatial_panel.py`

```python
class SpatialPanelModel(PooledPanelModel):
    """Spatial panel regression with spatial lag and error terms."""
    
    def __init__(self, W: np.ndarray, model_type: str = 'sar'):
        """
        Parameters
        ----------
        W : np.ndarray
            Spatial weights matrix
        model_type : str
            'sar' for spatial lag, 'sem' for spatial error, 'sarar' for both
        """
        super().__init__()
        self.W = W
        self.model_type = model_type
```

## Phase 4: Advanced Analysis Features (Week 4-5)

### 4.1 Welfare Impact Analysis
**File:** `src/yemen_market/analysis/welfare_impacts.py`

```python
def calculate_consumer_surplus_loss(price_data: pd.DataFrame, 
                                   elasticities: Dict[str, float]) -> pd.DataFrame:
    """Calculate consumer surplus loss from price increases."""
    # Implement Deaton (1989) approach for welfare analysis
    # Account for substitution effects
    # Return welfare losses by market and commodity
```

### 4.2 Non-linear Exchange Rate Pass-through
**File:** `src/yemen_market/analysis/nonlinear_passthrough.py`

```python
class ThresholdPassthroughModel:
    """Threshold model for exchange rate pass-through."""
    
    def estimate_passthrough_regimes(self, panel_data: pd.DataFrame) -> Dict[str, Any]:
        """Estimate regime-dependent pass-through coefficients."""
        # Implement threshold regression for pass-through
        # Allow for asymmetric responses
```

### 4.3 Cross-validation Framework
**File:** `src/yemen_market/models/validation/cross_validation.py`

```python
def spatial_temporal_cv(panel_data: pd.DataFrame, n_folds: int = 5, 
                       strategy: str = 'blocked') -> List[Tuple[pd.Index, pd.Index]]:
    """Generate spatial-temporal cross-validation folds."""
    # Implement blocked CV for spatial-temporal data
    # Ensure no data leakage across space/time
```

## Implementation Timeline

| Week | Tasks | Priority |
|------|-------|----------|
| 1 | Spatial features, Interaction terms | Critical |
| 2 | Dual exchange rates, RESET test | Critical |
| 3 | Structural break tests, Threshold CIs | High |
| 4 | Dynamic factors, Spatial panel | High |
| 5 | Welfare analysis, Cross-validation | Medium |

## Testing Requirements

For each enhancement:
1. Unit tests with >90% coverage
2. Integration tests with existing pipeline
3. Performance benchmarks
4. Documentation updates

## Success Metrics

1. All diagnostic tests pass at 5% level
2. Spatial features improve model R² by >5%
3. Structural break tests identify known conflict events
4. Cross-validation shows stable out-of-sample performance
5. Welfare analysis produces policy-relevant magnitudes

## Resources Needed

1. Additional compute resources for bootstrap procedures
2. Spatial weights matrix construction tools
3. Access to exchange rate time series data
4. Review by World Bank econometrics team

---

*Action Plan Version: 1.0*  
*Created: January 2024*  
*Target Completion: 5 weeks*