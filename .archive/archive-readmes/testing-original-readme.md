# Testing Guide

**Status**: V1 85% Coverage ✅ | V2 Limited Coverage ⚠️ | Last Updated: 2025-05-31

## Overview

This guide covers testing best practices, coverage requirements, and test execution for the Yemen Market Integration Platform. The platform uses pytest as its primary testing framework with comprehensive coverage reporting.

## Test Coverage Requirements

### Target Coverage Levels

- **Overall Target**: >90% (PRD requirement)
- **Current V1 Coverage**: ~85% (production-ready)
- **Current V2 Coverage**: ~30% (architecture validation only)
- **Critical Paths**: 100% coverage required

### Coverage by Component

| Component | Target | Current | Status |
|-----------|--------|---------|--------|
| Data Processing | 95% | 88% | 🟡 Good |
| Panel Builder | 95% | 91% | ✅ Excellent |
| Three-Tier Models | 90% | 85% | 🟡 Good |
| Diagnostics | 90% | 70% | ⚠️ Needs Work |
| Utilities | 85% | 92% | ✅ Excellent |
| V2 Core | 90% | 35% | ❌ In Progress |

## Running Tests

### Quick Test Commands

```bash
# Run all tests
pytest

# Run with coverage
make test-coverage

# Run specific test file
pytest tests/unit/test_wfp_processor.py

# Run specific test class/function
pytest tests/unit/test_panel_builder.py::TestPanelBuilder::test_create_balanced_panel

# Run tests matching pattern
pytest -k "threshold"

# Run with verbose output
pytest -v

# Run in parallel (faster)
pytest -n auto
```

### Coverage Commands

```bash
# Generate coverage report with HTML
make test-coverage

# Generate all coverage formats
python scripts/generate_coverage_report.py

# Check coverage for specific module
pytest --cov=yemen_market.data --cov-report=term-missing

# Set coverage threshold
pytest --cov=yemen_market --cov-fail-under=90

# Include V2 in coverage
python scripts/generate_coverage_report.py --v2
```

## Test Organization

### Directory Structure

```
tests/
├── __init__.py
├── conftest.py              # Shared fixtures
├── unit/                    # Unit tests
│   ├── test_*.py           # Individual component tests
│   └── models/             # Model-specific tests
│       └── three_tier/     # Three-tier framework tests
├── integration/            # Integration tests
│   ├── test_full_pipeline.py
│   └── test_models_quick.py
└── fixtures/               # Test data files
    ├── sample_prices.csv
    └── sample_panel.parquet
```

### Test Categories

#### Unit Tests
- Test individual functions/methods in isolation
- Use mocks for external dependencies
- Should run quickly (<0.1s per test)
- Example: `test_wfp_processor.py`

#### Integration Tests
- Test component interactions
- Use real data when possible
- May take longer (1-10s per test)
- Example: `test_full_pipeline.py`

#### End-to-End Tests
- Test complete workflows
- Minimal mocking
- Can be slow (>10s)
- Example: `test_three_tier_analysis.py`

## Writing Tests

### Test Structure

```python
import pytest
from yemen_market.utils.logging import get_logger

logger = get_logger(__name__)


class TestComponentName:
    """Test suite for ComponentName."""
    
    @pytest.fixture
    def sample_data(self):
        """Create sample data for tests."""
        return pd.DataFrame({
            'market': ['Sana\'a', 'Aden'],
            'price': [100, 120],
            'date': pd.date_range('2023-01-01', periods=2)
        })
    
    def test_basic_functionality(self, sample_data):
        """Test basic component functionality."""
        # Arrange
        component = ComponentName()
        
        # Act
        result = component.process(sample_data)
        
        # Assert
        assert result is not None
        assert len(result) == 2
        assert 'processed' in result.columns
    
    def test_edge_case(self):
        """Test handling of edge cases."""
        component = ComponentName()
        
        # Test empty input
        with pytest.raises(ValueError, match="Input cannot be empty"):
            component.process(pd.DataFrame())
    
    @pytest.mark.parametrize("input_val,expected", [
        (10, 100),
        (20, 400),
        (0, 0),
    ])
    def test_calculations(self, input_val, expected):
        """Test calculation accuracy."""
        result = calculate_something(input_val)
        assert result == expected
```

### Testing Best Practices

1. **Use Descriptive Names**
   ```python
   # Good
   def test_panel_builder_handles_missing_markets_gracefully():
   
   # Bad
   def test_pb_1():
   ```

2. **Test One Thing**
   ```python
   # Good - focused test
   def test_threshold_detection_identifies_conflict_breakpoint():
       # Test only threshold detection
   
   # Bad - testing multiple things
   def test_model():
       # Tests fitting, prediction, and validation
   ```

3. **Use Fixtures**
   ```python
   @pytest.fixture
   def market_panel():
       """Reusable market panel data."""
       return create_test_panel()
   
   def test_analysis(market_panel):
       result = analyze(market_panel)
       assert result.success
   ```

4. **Mock External Dependencies**
   ```python
   @patch('yemen_market.data.hdx_client.HDXClient.download_dataset')
   def test_data_download(mock_download):
       mock_download.return_value = 'path/to/file.csv'
       client = HDXClient()
       result = client.download_dataset('test-dataset')
       assert result == 'path/to/file.csv'
   ```

5. **Test Error Conditions**
   ```python
   def test_invalid_input_raises_error():
       with pytest.raises(ValueError) as exc_info:
           process_invalid_data(None)
       assert "cannot be None" in str(exc_info.value)
   ```

## Coverage Analysis

### Understanding Coverage Reports

#### Console Output
```
Name                                  Stmts   Miss  Cover   Missing
-----------------------------------------------------------------
yemen_market/__init__.py                  5      0   100%
yemen_market/data/wfp_processor.py      245     23    91%   45-67, 125
yemen_market/models/three_tier.py       189     19    90%   234-252
-----------------------------------------------------------------
TOTAL                                  2456    234    90%
```

#### HTML Report
- Open `htmlcov/index.html` in browser
- Click on files to see line-by-line coverage
- Red lines = not covered
- Green lines = covered
- Yellow lines = partially covered (branches)

### Improving Coverage

1. **Identify Gaps**
   ```bash
   # Show missing lines
   pytest --cov=yemen_market --cov-report=term-missing
   ```

2. **Focus on Critical Paths**
   - Data validation logic
   - Model estimation code
   - Error handling
   - Edge cases

3. **Add Edge Case Tests**
   ```python
   def test_empty_panel_handling():
       """Test behavior with empty panel."""
       
   def test_single_market_panel():
       """Test with minimal data."""
       
   def test_missing_price_interpolation():
       """Test missing data handling."""
   ```

4. **Test Error Paths**
   ```python
   def test_network_error_retry():
       """Test retry logic on network failure."""
       
   def test_invalid_data_format():
       """Test handling of malformed input."""
   ```

## Continuous Integration

### GitHub Actions Setup

The project includes GitHub Actions workflow for automated testing:

```yaml
# .github/workflows/coverage.yml
name: Test Coverage

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  coverage:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    - name: Install dependencies
      run: |
        pip install -e ".[dev]"
    - name: Run tests with coverage
      run: |
        pytest --cov=yemen_market --cov-report=xml --cov-fail-under=90
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
```

### Local CI Simulation

```bash
# Run same checks as CI
act push  # Using act tool

# Or manually
pytest --cov=yemen_market --cov-report=xml --cov-fail-under=90
```

## Test Data Management

### Using Fixtures

```python
# conftest.py
@pytest.fixture(scope='session')
def sample_price_data():
    """Load sample price data once per session."""
    return pd.read_csv('tests/fixtures/sample_prices.csv')

@pytest.fixture
def mock_hdx_client():
    """Mock HDX client for testing."""
    with patch('yemen_market.data.hdx_client.HDXClient') as mock:
        mock.return_value.download_dataset.return_value = 'path/to/file'
        yield mock
```

### Test Data Guidelines

1. **Keep Test Data Small**
   - Use minimal datasets that demonstrate functionality
   - Store in `tests/fixtures/`
   - Generate programmatically when possible

2. **Use Realistic Data**
   - Match production data structure
   - Include edge cases
   - Test with various data quality issues

3. **Version Control Test Data**
   - Small CSV/JSON files in repo
   - Large files in Git LFS
   - Document data sources

## Performance Testing

### Benchmarking Tests

```python
@pytest.mark.benchmark
def test_panel_construction_performance(benchmark):
    """Benchmark panel construction."""
    data = load_test_data()
    result = benchmark(build_panel, data)
    assert result is not None
```

### Memory Testing

```python
@pytest.mark.memory
def test_memory_usage():
    """Test memory consumption stays reasonable."""
    import tracemalloc
    
    tracemalloc.start()
    result = process_large_dataset()
    current, peak = tracemalloc.get_traced_memory()
    tracemalloc.stop()
    
    # Assert memory usage < 1GB
    assert peak < 1_000_000_000
```

## Debugging Tests

### Useful pytest Options

```bash
# Drop into debugger on failure
pytest --pdb

# Show local variables on failure
pytest --showlocals

# Stop on first failure
pytest -x

# Run last failed tests
pytest --lf

# Show test durations
pytest --durations=10

# Verbose output with print statements
pytest -vs
```

### VS Code Integration

```json
// .vscode/settings.json
{
    "python.testing.pytestEnabled": true,
    "python.testing.unittestEnabled": false,
    "python.testing.pytestArgs": [
        "--cov=yemen_market",
        "--cov-report=html"
    ]
}
```

## Common Issues

### Import Errors
```bash
# Solution: Install package in development mode
pip install -e .
```

### Fixture Not Found
```python
# Solution: Ensure conftest.py is in test directory
# Fixtures are discovered from conftest.py files
```

### Flaky Tests
```python
# Solution: Use proper mocking and deterministic data
@pytest.mark.flaky(reruns=3)  # Temporary workaround
def test_network_dependent():
    pass
```

### Slow Tests
```python
# Solution: Mark slow tests and run separately
@pytest.mark.slow
def test_full_analysis():
    pass

# Run without slow tests
pytest -m "not slow"
```

## Next Steps

1. **Increase Coverage**: Focus on diagnostic tests (70% → 90%)
2. **Add Integration Tests**: Test V2 components when integrated
3. **Performance Benchmarks**: Add systematic performance tests
4. **Documentation Tests**: Test code examples in documentation

## Resources

- [pytest Documentation](https://docs.pytest.org/)
- [Coverage.py Documentation](https://coverage.readthedocs.io/)
- [Testing Best Practices](https://testdriven.io/blog/testing-best-practices/)
- [Project Test Examples](../../tests/)