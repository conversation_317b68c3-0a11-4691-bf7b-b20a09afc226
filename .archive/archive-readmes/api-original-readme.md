# API Reference

**Status**: V1 Complete ✅ | V2 Structure Only ⚠️ | Last Updated: 2025-05-31

This directory contains comprehensive API documentation for the Yemen Market Integration Platform, covering both the production-ready V1 system and the architectural preview of V2.

## V1 API (Production-Ready)

### Data Processing (`yemen_market.data`)

- **[HDXClient](./data/hdx_client.md)**: Interface for downloading data from Humanitarian Data Exchange
- **[WFPProcessor](./data/wfp_processor.md)**: Processes World Food Programme price data
- **[ACAPSProcessor](./data/acaps_processor.md)**: Processes ACAPS control zone shapefiles
- **[ACLEDProcessor](./data/acled_processor.md)**: Processes ACLED conflict event data
- **[SpatialJoiner](./data/spatial_joiner.md)**: Performs spatial joins between markets and zones
- **[PanelBuilder](./data/panel_builder.md)**: Builds integrated panel datasets (88.4% coverage achieved)

### Feature Engineering (`yemen_market.features`)

- **[DataPreparation](./features/data_preparation.md)**: Data cleaning and preparation utilities
- **[FeatureEngineer](./features/feature_engineer.md)**: Main class for creating analysis features

### Econometric Models (`yemen_market.models`)

- **[Three-Tier Framework](./models/three_tier/README.md)**: Complete three-tier econometric framework
  - **Tier 1**: Pooled panel models with fixed effects
  - **Tier 2**: Commodity-specific threshold models (21 commodities)
  - **Tier 3**: Factor analysis and external validation
- **[Diagnostics](./models/three_tier/diagnostics/test_implementations.md)**: Comprehensive test battery (70% implemented)
- **[Integration](./models/three_tier/integration/results_analyzer.md)**: Cross-tier validation and results analysis

### Utilities (`yemen_market.utils`)

- **[Enhanced Logging](./utils/logging.md)**: Context-aware logging system with timers and progress tracking
- **Performance Monitoring**: Operation timing and resource tracking
- **Security Utilities**: Input validation and sanitization

### Configuration (`yemen_market.config`)

- **[Settings](./config/settings.md)**: Project configuration and constants

## V2 API Preview (Architecture Only)

> **Warning**: V2 APIs exist in code but are not integrated with data pipeline. Do not use for production.

### REST API Endpoints

#### Markets
- `GET /api/v2/markets` - List all markets with filtering
- `GET /api/v2/markets/{id}` - Get specific market details
- `GET /api/v2/markets/{id}/prices` - Get price history for market

#### Prices
- `GET /api/v2/prices` - Query price data with filters
- `POST /api/v2/prices/bulk` - Bulk price data upload (admin)

#### Analysis
- `POST /api/v2/analyses` - Start new analysis job
- `GET /api/v2/analyses/{id}` - Get analysis results
- `GET /api/v2/analyses/{id}/status` - Real-time status (SSE)
- `DELETE /api/v2/analyses/{id}` - Cancel running analysis

#### Policy Models
- `POST /api/v2/policy/welfare-impact` - Run welfare impact assessment
- `POST /api/v2/policy/early-warning` - Generate early warning signals

### GraphQL API (Planned)

```graphql
type Query {
  markets(filter: MarketFilter): [Market!]!
  prices(filter: PriceFilter): [Price!]!
  analysis(id: ID!): Analysis
}

type Mutation {
  createAnalysis(input: AnalysisInput!): Analysis!
  runPolicyModel(type: PolicyModelType!, params: JSON!): PolicyResult!
}

type Subscription {
  analysisProgress(id: ID!): AnalysisEvent!
}
```

### V2 Domain Models

- **Market Entity**: Geographic market with metadata
- **Price Value Object**: Immutable price record
- **Analysis Aggregate**: Complete analysis workflow
- **Policy Models**: Welfare impact and early warning systems

## V1 Quick Examples

### Processing WFP Data

```python
from yemen_market.data import WFPProcessor

processor = WFPProcessor(min_market_coverage=0.5)
commodity_prices, exchange_rates = processor.process_price_data()
smart_panel = processor.create_smart_panels(commodity_prices)
```

### Building Panels

```python
from yemen_market.data import PanelBuilder

builder = PanelBuilder()
# Load and integrate all data sources
data_dict = builder.load_component_data()
# Create balanced panel with 88.4% coverage
balanced_panel = builder.create_core_balanced_panel(data_dict)
```

### Running Three-Tier Analysis

```python
from yemen_market.models.three_tier.integration import ThreeTierRunner

runner = ThreeTierRunner()
results = runner.run_all_tiers(balanced_panel)

# Access tier-specific results
print(f"Tier 1 - Conflict impact: {results.tier1.coefficients['conflict_intensity']:.3f}")
print(f"Tier 2 - Commodities with thresholds: {results.tier2.threshold_commodities}")
print(f"Tier 3 - Integration score: {results.tier3.integration_score:.3f}")
```

### Running Diagnostics

```python
from yemen_market.models.three_tier.diagnostics import PanelDiagnostics

diagnostics = PanelDiagnostics()
test_results = diagnostics.run_all_tests(results.tier1)

# Check for issues
if test_results['serial_correlation']['reject_null']:
    print("Serial correlation detected - using robust standard errors")
```

## API Stability Guarantees

### V1 APIs (Stable)
- All V1 APIs are stable and backward compatible
- Used in production for World Bank analysis
- Comprehensive test coverage (>85%)

### V2 APIs (Experimental)
- API contracts may change
- Not connected to data pipeline
- Use for architectural reference only

### V3 APIs (Future)
- Performance-focused redesign
- Will maintain V1 compatibility layer
- Timeline: 6-12 months

## Performance Characteristics

### V1 Performance
- Panel construction: 2-5 minutes for full dataset
- Three-tier analysis: 10-30 minutes depending on panel size
- Memory usage: 2-4GB for typical analysis

### V2 Performance (Projected)
- 10x faster through async processing (unverified)
- Real-time updates via SSE
- Horizontal scaling with Kubernetes

### V3 Performance (Target)
- 100x faster with GPU acceleration
- Sub-second queries with DuckDB
- Streaming support for real-time data

## Error Handling

All APIs follow consistent error handling:

```python
# V1 Pattern
try:
    result = processor.process_data()
except DataValidationError as e:
    logger.error(f"Validation failed: {e}")
except NetworkError as e:
    logger.error(f"Network issue: {e}")
    # Retry logic

# V2 Pattern (when available)
response = await client.post("/api/v2/analyses", json=params)
if response.status_code != 201:
    error = response.json()
    # Handle structured error response
```

## Module Documentation

Detailed documentation for each module is available in the linked files above. For implementation examples, see the [notebooks](../../notebooks/) directory.
