.PHONY: help install test lint format clean download-data process-data pipeline analysis analysis-config analysis-tier report report-pdf report-html report-custom full-analysis quick-analysis all week5-models quick-test check-deps

# Assume venv is active
PYTHON := python3
PIP := pip
PYTEST := pytest
RUFF := ruff
BLACK := black
MYPY := mypy

help:
	@echo "Yemen Market Integration Analysis - Available Commands:"
	@echo "  make install       Install dependencies and package"
	@echo "  make test          Run test suite with coverage"
	@echo "  make test-coverage Generate HTML coverage report"
	@echo "  make test-coverage-full Run comprehensive coverage analysis"
	@echo "  make test-coverage-v2 Include V2 code in coverage"
	@echo "  make coverage-badge Generate coverage badge"
	@echo "  make lint          Check code style"
	@echo "  make format        Format code with black"
	@echo "  make download-data Download initial datasets from HDX"
	@echo "  make process-data  Run complete data processing pipeline"
	@echo "  make pipeline      Run full data pipeline (download + process)"
	@echo "  make analysis      Run three-tier econometric analysis"
	@echo "  make analysis-config CONFIG=path  Run analysis with custom config"
	@echo "  make analysis-tier TIER=1|2|3     Run specific tier only"
	@echo "  make report        Generate comprehensive reports"
	@echo "  make report-pdf    Generate PDF report only"
	@echo "  make report-html   Generate HTML report only"
	@echo "  make full-analysis Run complete analysis + reporting workflow"
	@echo "  make quick-analysis Validate analysis configuration (dry run)"
	@echo "  make week5-models  Run Week 5 dual-track models ⭐ NEW"
	@echo "  make quick-test    Test models with synthetic data ⭐ NEW"
	@echo "  make check-deps    Check dependencies for models ⭐ NEW"
	@echo "  make update-claude-commands Update Claude context files ⭐ NEW"
	@echo "  make clean         Remove generated files"

install:
	@echo "Ensuring virtual environment is active and installing dependencies..."
	@if [ -z "$(VIRTUAL_ENV)" ]; then \
		echo "ERROR: No virtual environment detected. Please activate your venv."; \
		false; \
	else \
		echo "Virtual environment '$(VIRTUAL_ENV)' detected."; \
		$(PIP) install -e ".[dev]"; \
		pre-commit install; \
	fi

test:
	$(PYTEST) tests/ -v --cov=yemen_market

test-coverage:
	$(PYTEST) tests/ -v --cov=yemen_market --cov-report=html --cov-report=term-missing
	@echo "Coverage report generated in htmlcov/index.html"

test-coverage-full:
	$(PYTHON) scripts/generate_coverage_report.py
	@echo "Full coverage analysis complete!"

test-coverage-v2:
	$(PYTHON) scripts/generate_coverage_report.py --v2
	@echo "Coverage analysis including V2 complete!"

coverage-badge:
	$(PYTHON) scripts/generate_coverage_report.py --no-html
	@echo "Coverage badge updated in docs/badges/coverage.svg"

lint:
	$(RUFF) check src/ tests/
	$(MYPY) src/

format:
	$(BLACK) src/ tests/ scripts/
	$(RUFF) check src/ tests/ --fix

download-data:
	$(PYTHON) scripts/download_data.py

process-wfp:
	$(PYTHON) scripts/process_wfp_data.py

process-acaps:
	$(PYTHON) scripts/process_acaps_data.py

process-acled:
	$(PYTHON) scripts/process_acled_data.py

spatial-joins:
	$(PYTHON) scripts/run_spatial_joins.py

build-panels:
	$(PYTHON) scripts/build_panel_datasets.py

process-data: process-wfp process-acaps process-acled spatial-joins build-panels
	@echo "Data processing pipeline complete!"

pipeline: download-data process-data
	@echo "Full data pipeline complete!"

analysis:
	@echo "Running three-tier econometric analysis..."
	$(PYTHON) scripts/run_analysis.py --verbose

analysis-config:
	@echo "Running analysis with custom configuration..."
	$(PYTHON) scripts/run_analysis.py --config $(CONFIG) --verbose

analysis-tier:
	@echo "Running specific tier analysis..."
	$(PYTHON) scripts/run_analysis.py --tier $(TIER) --verbose

report:
	@echo "Generating comprehensive reports..."
	$(PYTHON) scripts/generate_report.py --verbose

report-pdf:
	@echo "Generating PDF report..."
	$(PYTHON) scripts/generate_report.py --format pdf --verbose

report-html:
	@echo "Generating HTML report..."
	$(PYTHON) scripts/generate_report.py --format html --verbose

report-custom:
	@echo "Generating report with custom input..."
	$(PYTHON) scripts/generate_report.py --input $(INPUT) --format $(FORMAT) --verbose

# Complete analysis workflow
full-analysis: analysis report
	@echo "Complete three-tier analysis and reporting workflow finished!"

# Quick analysis for testing
quick-analysis:
	@echo "Running quick analysis with default settings..."
	$(PYTHON) scripts/run_analysis.py --dry-run --verbose
	@echo "Dry run completed successfully. Use 'make analysis' to run full analysis."

clean:
	find . -type d -name "__pycache__" -delete
	rm -rf .pytest_cache/ .coverage htmlcov/
	rm -rf data/interim/* data/processed/*
	rm -rf reports/figures/* reports/tables/*

clean-cache:
	rm -rf data/raw/*/cache_info.json
	@echo "Cache metadata cleared (data files retained)"

all: install pipeline
	@echo "Installation and data pipeline complete!"

# Week 5 Sprint Implementation
week5-models:
	@echo "Running Week 5 dual-track econometric models..."
	$(PYTHON) scripts/analysis/run_week5_models.py

quick-test:
	@echo "Running quick model test with synthetic data..."
	$(PYTHON) scripts/test_models_quick.py

check-deps:
	@echo "Checking dependencies for Week 5 models..."
	$(PYTHON) scripts/check_dependencies.py

run-pipeline:
	@echo "Running complete data pipeline..."
	$(PYTHON) scripts/run_data_pipeline.py

# Claude context management
update-claude-commands:
	@echo "Updating Claude command context..."
	$(PYTHON) scripts/utilities/generate_claude_commands.py