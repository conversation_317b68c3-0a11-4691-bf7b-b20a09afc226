{"name": "yemen-market-integration-v2", "version": "2.0.0", "description": "Yemen Market Integration Econometric Analysis Platform V2", "private": true, "scripts": {"dashboard": "./scripts/run_dashboard.sh", "generate-results": "python3 scripts/generate_executive_results.py", "test": "pytest", "lint": "make lint"}, "repository": {"type": "git", "url": "https://github.com/star-boy-95/yemen-market-integration-v2.git"}, "keywords": ["econometrics", "market-integration", "yemen", "conflict-analysis", "panel-data"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"dotenv": "^16.5.0"}}