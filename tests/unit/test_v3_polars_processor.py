"""Unit tests for V3 Polars WFP processor."""

import pytest
from pathlib import Path
from datetime import datetime
import polars as pl
import pandas as pd
import numpy as np

from src.yemen_market.data.v3_polars_processor import V3PolarsWFPProcessor


class TestV3PolarsWFPProcessor:
    """Test V3 Polars WFP processor functionality."""
    
    @pytest.fixture
    def processor(self):
        """Create a V3 processor instance."""
        return V3PolarsWFPProcessor(
            commodities=['Wheat', 'Rice (Imported)', 'Sugar'],
            start_date='2023-01-01',
            end_date='2023-12-31'
        )
    
    @pytest.fixture
    def sample_data(self):
        """Create sample WFP data as Polars DataFrame."""
        data = {
            'date': ['2023-01-15', '2023-01-15', '2023-02-15', '2023-02-15'],
            'admin1': ['Sana\'a', 'Aden', 'Sana\'a', 'Aden'],
            'admin2': ['District1', 'District2', 'District1', 'District2'],
            'market': ['Market A', 'Market B', 'Market A', 'Market B'],
            'latitude': [15.3694, 12.7855, 15.3694, 12.7855],
            'longitude': [44.1910, 45.0187, 44.1910, 45.0187],
            'commodity': ['Wheat', 'Wheat', 'Rice (Imported)', 'Sugar'],
            'unit': ['KG', 'KG', 'KG', 'KG'],
            'price': [500.0, 450.0, 800.0, 600.0],
            'usdprice': [1.0, 0.9, 1.6, 1.2],
            'currency': ['YER', 'YER', 'YER', 'YER'],
            'pricetype': ['Retail', 'Retail', 'Retail', 'Retail']
        }
        return pl.DataFrame(data)
    
    def test_initialization(self):
        """Test processor initialization."""
        processor = V3PolarsWFPProcessor()
        assert processor.commodities == processor.KEY_COMMODITIES
        assert processor.min_market_coverage == 0.3
        assert processor.start_date is not None
        assert processor.end_date is not None
    
    def test_process_raw_data_polars(self, processor, sample_data):
        """Test raw data processing with Polars."""
        # Create lazy frame from sample data
        lazy_df = sample_data.lazy()
        
        # Process data
        processed = processor.process_raw_data_polars(lazy_df)
        
        # Check output type
        assert isinstance(processed, pl.DataFrame)
        
        # Check columns renamed
        assert 'governorate' in processed.columns
        assert 'market_name' in processed.columns
        assert 'price_local' in processed.columns
        assert 'price_usd' in processed.columns
        
        # Check data types
        assert processed['date'].dtype == pl.Date
        assert processed['price_local'].dtype == pl.Float64
        assert processed['price_usd'].dtype == pl.Float64
        
        # Check text standardization
        assert all(processed['commodity'].to_list())  # All title case
        
        # Check year_month added
        assert 'year_month' in processed.columns
    
    def test_extract_exchange_rates_polars(self, processor, sample_data):
        """Test exchange rate extraction with Polars."""
        # Process sample data first
        lazy_df = sample_data.lazy()
        processed = processor.process_raw_data_polars(lazy_df)
        
        # Extract exchange rates
        exchange_rates = processor.extract_exchange_rates_polars(processed)
        
        # Check output
        assert isinstance(exchange_rates, pl.DataFrame)
        assert 'exchange_rate' in exchange_rates.columns
        assert 'n_observations' in exchange_rates.columns
        assert 'control_zone' in exchange_rates.columns
        
        # Check zone classification
        zones = exchange_rates['control_zone'].unique().to_list()
        assert all(z in ['Houthi', 'Government', 'Contested'] for z in zones)
    
    def test_extract_commodity_prices_polars(self, processor, sample_data):
        """Test commodity price extraction with Polars."""
        # Process sample data
        lazy_df = sample_data.lazy()
        processed = processor.process_raw_data_polars(lazy_df)
        
        # Extract commodity prices
        prices = processor.extract_commodity_prices_polars(processed)
        
        # Check output
        assert isinstance(prices, pl.DataFrame)
        assert 'market_id' in prices.columns
        assert 'commodity' in prices.columns
        assert 'price_local' in prices.columns
        
        # Check filtering
        commodities = prices['commodity'].unique().to_list()
        assert all(c in processor.commodities for c in commodities)
        
        # Check market_id format
        market_ids = prices['market_id'].to_list()
        assert all('_' in mid for mid in market_ids)
        assert all(' ' not in mid for mid in market_ids)
    
    def test_governorate_mapping(self, processor):
        """Test governorate name standardization."""
        # Create data with names to map
        data = pl.DataFrame({
            'governorate': ["Al Dhale'e", "Al Hudaydah", "Amanat Al Asimah", "Sana'a"]
        })
        
        # Apply mapping
        mapped = data.with_columns([
            pl.col('governorate').replace(processor.GOVERNORATE_MAPPINGS)
        ])
        
        # Check mappings applied
        gov_list = mapped['governorate'].to_list()
        assert "Ad Dale'" in gov_list
        assert "Al Hodeidah" in gov_list
        assert "Sana'a City" in gov_list
        assert "Sana'a" in gov_list
    
    def test_lazy_evaluation(self, processor, tmp_path):
        """Test lazy evaluation benefits."""
        # Create a larger test file
        n_rows = 10000
        dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
        
        large_data = {
            'date': np.random.choice(dates, n_rows).astype(str),
            'admin1': np.random.choice(['Sana\'a', 'Aden', 'Taiz'], n_rows),
            'admin2': [f'District{i%10}' for i in range(n_rows)],
            'market': [f'Market{i%20}' for i in range(n_rows)],
            'latitude': np.random.uniform(12, 18, n_rows),
            'longitude': np.random.uniform(42, 48, n_rows),
            'commodity': np.random.choice(['Wheat', 'Rice (Imported)', 'Sugar', 'Salt'], n_rows),
            'unit': ['KG'] * n_rows,
            'price': np.random.uniform(100, 1000, n_rows),
            'usdprice': np.random.uniform(0.2, 2.0, n_rows),
            'currency': ['YER'] * n_rows,
            'pricetype': ['Retail'] * n_rows
        }
        
        # Save as CSV
        csv_path = tmp_path / "test_data.csv"
        pd.DataFrame(large_data).to_csv(csv_path, index=False)
        
        # Load lazily
        lazy_df = processor.load_raw_data_lazy(csv_path)
        
        # Lazy operations should be instant
        assert isinstance(lazy_df, pl.LazyFrame)
        
        # Process and collect
        processed = processor.process_raw_data_polars(lazy_df)
        assert isinstance(processed, pl.DataFrame)
        assert len(processed) > 0
    
    def test_performance_vs_pandas(self, processor, tmp_path):
        """Test that Polars operations are faster than pandas equivalents."""
        import time
        
        # Create test data
        n_rows = 5000
        data = {
            'admin1': np.random.choice(['Sana\'a', 'Aden'], n_rows),
            'commodity': np.random.choice(['Wheat', 'Rice'], n_rows),
            'price': np.random.uniform(100, 1000, n_rows)
        }
        
        df_polars = pl.DataFrame(data)
        df_pandas = pd.DataFrame(data)
        
        # Test groupby performance
        start = time.time()
        result_polars = df_polars.group_by(['admin1', 'commodity']).agg([
            pl.col('price').mean()
        ])
        polars_time = time.time() - start
        
        start = time.time()
        result_pandas = df_pandas.groupby(['admin1', 'commodity'])['price'].mean()
        pandas_time = time.time() - start
        
        # Polars should be faster (allow some variance)
        assert polars_time < pandas_time * 1.5  # At least comparable
    
    def test_to_pandas_conversion(self, processor, sample_data):
        """Test conversion to pandas DataFrame."""
        # Process with Polars
        lazy_df = sample_data.lazy()
        processed = processor.process_raw_data_polars(lazy_df)
        
        # Convert to pandas
        df_pandas = processor.to_pandas(processed)
        
        # Check it's a pandas DataFrame
        assert isinstance(df_pandas, pd.DataFrame)
        assert len(df_pandas) == len(processed)
        assert list(df_pandas.columns) == processed.columns
    
    def test_save_processed_data_polars(self, processor, sample_data, tmp_path):
        """Test saving Polars DataFrames."""
        # Process data
        lazy_df = sample_data.lazy()
        processed = processor.process_raw_data_polars(lazy_df)
        
        # Extract prices
        prices = processor.extract_commodity_prices_polars(processed)
        exchange = processor.extract_exchange_rates_polars(processed)
        
        # Mock the output directory
        processor.PROCESSED_DATA_DIR = tmp_path
        
        # Save data
        processor.save_processed_data_polars(prices, exchange, format='parquet')
        
        # Check files created
        v3_dir = tmp_path / 'v3_polars'
        assert v3_dir.exists()
        assert (v3_dir / 'wfp_commodity_prices.parquet').exists()
        assert (v3_dir / 'wfp_exchange_rates.parquet').exists()
        
        # Test CSV format
        processor.save_processed_data_polars(prices, exchange, format='csv')
        assert (v3_dir / 'wfp_commodity_prices.csv').exists()
    
    def test_empty_data_handling(self, processor):
        """Test handling of empty DataFrames."""
        # Create empty DataFrame
        empty_df = pl.DataFrame()
        
        # Process should handle gracefully
        commodity_df, exchange_df = processor.process_price_data()
        
        # Both should be empty DataFrames, not errors
        assert isinstance(commodity_df, pl.DataFrame)
        assert isinstance(exchange_df, pl.DataFrame)