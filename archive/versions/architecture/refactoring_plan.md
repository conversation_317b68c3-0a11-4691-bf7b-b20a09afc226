# Source Code Refactoring Plan

## Overview

This document outlines the recommended refactoring plan for the Yemen Market Integration codebase to improve maintainability and reduce technical debt.

## Priority Matrix

| Priority | Module | Current LOC | Target LOC | Complexity | Impact |
|----------|--------|-------------|------------|------------|--------|
| **HIGH** | `data/panel_builder.py` | 1904 | 400/file | High | Critical for data pipeline |
| **HIGH** | `models/.../threshold_vecm.py` | 1246 | 400/file | Very High | Core model implementation |
| **MEDIUM** | `models/.../model_migration.py` | 1343 | 400/file | Medium | Support functionality |
| **MEDIUM** | `models/.../data_validator.py` | 1135 | 400/file | Medium | Validation logic |
| **LOW** | `models/model_comparison.py` | 1139 | 600 | Low | Utility module |
| **LOW** | Configuration management | N/A | N/A | Low | Better organization |

## Refactoring Strategy

### 1. Panel Builder Decomposition (HIGH PRIORITY)

```
Current: data/panel_builder.py (1904 lines)

Proposed structure:
data/panel_builder/
├── __init__.py              # Exports PanelBuilder for backward compatibility
├── base.py                  # Core PanelBuilder class (~400 lines)
├── balanced_panel.py        # Balanced panel creation (~400 lines)
├── integration.py           # Data source integration (~400 lines)
├── validation.py            # Panel validation methods (~300 lines)
├── transformations.py       # Data transformations (~200 lines)
└── utils.py                 # Helper functions (~200 lines)
```

**Migration approach:**
```python
# In data/panel_builder/__init__.py
from .base import PanelBuilder
from .balanced_panel import BalancedPanelMixin
from .integration import IntegrationMixin
from .validation import ValidationMixin

# Maintain backward compatibility
__all__ = ['PanelBuilder']

# The main class inherits from mixins
class PanelBuilder(BalancedPanelMixin, IntegrationMixin, ValidationMixin):
    pass
```

### 2. Threshold VECM Decomposition (HIGH PRIORITY)

```
Current: models/three_tier/tier2_commodity/threshold_vecm.py (1246 lines)

Proposed structure:
models/three_tier/tier2_commodity/threshold_vecm/
├── __init__.py              # Exports ThresholdVECM
├── model.py                 # Core model class (~300 lines)
├── estimation.py            # Estimation algorithms (~400 lines)
├── threshold_detection.py   # Threshold search (~300 lines)
├── regime_analysis.py       # Regime-specific methods (~200 lines)
└── diagnostics.py          # Model diagnostics (~200 lines)
```

### 3. Common Utilities Extraction (MEDIUM PRIORITY)

Create new utility modules:

```
utils/
├── econometrics/
│   ├── __init__.py
│   ├── standard_errors.py   # Common SE calculations
│   ├── test_statistics.py   # Statistical tests
│   ├── transformations.py   # Data transformations
│   └── panel_utils.py       # Panel data utilities
├── validation/
│   ├── __init__.py
│   ├── data_validators.py   # Data validation
│   ├── param_validators.py  # Parameter validation
│   └── schema.py           # Validation schemas
```

### 4. Configuration Consolidation (MEDIUM PRIORITY)

```
config/
├── __init__.py
├── base.py                  # Base configuration class
├── data_config.py           # Data processing settings
├── model_config.py          # Model parameters
├── pipeline_config.py       # Pipeline settings
├── logging_config.py        # Logging configuration
└── defaults/
    ├── __init__.py
    ├── commodities.py       # Default commodity lists
    ├── markets.py           # Default market lists
    └── parameters.py        # Default model parameters
```

## Implementation Steps

### Week 1: Foundation
1. **Set up testing infrastructure**
   - Create comprehensive tests for modules to be refactored
   - Set up integration tests to ensure no breaking changes
   
2. **Create utility modules**
   - Extract common econometric functions
   - Create validation utilities
   - Ensure all tests pass

### Week 2: Core Refactoring
1. **Refactor panel_builder.py**
   - Create sub-package structure
   - Move code incrementally
   - Test each component
   
2. **Refactor threshold_vecm.py**
   - Split into logical components
   - Maintain API compatibility
   - Update documentation

### Week 3: Cleanup
1. **Configuration management**
   - Consolidate settings
   - Create configuration classes
   - Update all imports
   
2. **Dead code removal**
   - Remove unused imports
   - Clean up commented code
   - Optimize __init__.py files

## Quality Metrics

### Before Refactoring
- Files >1000 lines: 6
- Average complexity: 8.2
- Test coverage: 90%
- Import depth: 4 levels

### After Refactoring
- Files >1000 lines: 0
- Average complexity: <6
- Test coverage: >95%
- Import depth: 3 levels

## Risk Mitigation

1. **Backward Compatibility**
   - Maintain all public APIs
   - Use __init__.py to preserve imports
   - Deprecate old patterns gradually

2. **Testing Strategy**
   - Write tests before refactoring
   - Use integration tests
   - Run full test suite after each change

3. **Documentation**
   - Update all docstrings
   - Create migration guide
   - Document new structure

## Benefits

1. **Improved Maintainability**
   - Smaller, focused files
   - Clear separation of concerns
   - Easier to understand and modify

2. **Better Testability**
   - Isolated components
   - Mockable dependencies
   - Higher test coverage

3. **Enhanced Performance**
   - Lazy imports possible
   - Reduced memory footprint
   - Faster test execution

4. **Team Productivity**
   - Less merge conflicts
   - Easier code reviews
   - Clearer ownership

## Success Criteria

- [ ] No files exceed 800 lines
- [ ] All tests pass with >95% coverage
- [ ] No circular dependencies
- [ ] Documentation updated
- [ ] Performance benchmarks maintained
- [ ] Zero breaking changes to public APIs