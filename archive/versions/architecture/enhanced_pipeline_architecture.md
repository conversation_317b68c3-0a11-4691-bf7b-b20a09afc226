# Enhanced Analysis Pipeline Architecture

## Overview

The enhanced pipeline integrates all advanced capabilities available in the codebase, transforming the analysis from a basic three-tier approach to a comprehensive econometric framework suitable for World Bank standards.

## Architecture Diagram

```mermaid
graph TB
    %% Data Sources
    subgraph "Data Sources"
        A1[WFP Price Data]
        A2[ACLED Conflict Data]
        A3[ACAPS Control Zones]
        A4[HDX Boundaries]
        A5[Exchange Rates]
    end

    %% Enhanced Data Preparation
    subgraph "Enhanced Data Preparation"
        B1[Panel Builder<br/>- Balanced panels<br/>- Integration]
        B2[Feature Engineering<br/>- Spatial features K-NN<br/>- Interaction terms<br/>- Zone × Time]
        B3[Data Preparation<br/>- Winsorization<br/>- Stationarity tests<br/>- Conflict regimes]
        
        A1 --> B1
        A2 --> B1
        A3 --> B1
        A4 --> B1
        A5 --> B1
        
        B1 --> B2
        B2 --> B3
    end

    %% Three-Tier Analysis (Enhanced)
    subgraph "Three-Tier Analysis - Enhanced"
        C1[Tier 1: Pooled Panel<br/>+ Spatial features<br/>+ Driscoll-<PERSON><PERSON><PERSON> SEs<br/>+ Interaction effects]
        
        C2[Tier 2: Commodity<br/>+ Threshold VECM<br/>+ Regime switching<br/>+ Grid search]
        
        C3[Tier 3: Validation<br/>+ Econometric tests<br/>+ Granger causality<br/>+ Impulse response]
        
        B3 --> C1
        B3 --> C2
        B3 --> C3
    end

    %% Additional Analyses
    subgraph "Advanced Analyses"
        D1[Price Transmission<br/>- Market pairs<br/>- ECM models<br/>- Corridor analysis]
        
        D2[Exchange Pass-through<br/>- Dual rates impact<br/>- Commodity-specific<br/>- Complete/incomplete]
        
        D3[Model Comparison<br/>- Cross-validation<br/>- Ensemble methods<br/>- Performance metrics]
        
        B3 --> D1
        B3 --> D2
        C1 --> D3
        C2 --> D3
    end

    %% Results Integration
    subgraph "Results Integration"
        E1[Results Analyzer<br/>- Coefficient extraction<br/>- P-values & significance<br/>- Impact quantification]
        
        E2[Market Integration<br/>- Correlation analysis<br/>- High integration pairs<br/>- Zone effects]
        
        E3[Policy Insights<br/>- Volatility analysis<br/>- Essential commodities<br/>- Recommendations]
        
        C1 --> E1
        C2 --> E1
        C3 --> E1
        D1 --> E2
        D2 --> E3
    end

    %% Visualization & Reporting
    subgraph "Enhanced Outputs"
        F1[Spatial Visualizations<br/>- Price heatmaps<br/>- Market networks<br/>- Geographic patterns]
        
        F2[Dynamic Plots<br/>- Convergence analysis<br/>- Structural breaks<br/>- Regime transitions]
        
        F3[Comprehensive Report<br/>- All findings<br/>- Policy brief<br/>- Technical appendix]
        
        E1 --> F1
        E2 --> F1
        E1 --> F2
        E3 --> F3
    end

    %% Styling
    classDef dataNode fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef prepNode fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef modelNode fill:#e8f5e9,stroke:#1b5e20,stroke-width:2px
    classDef analysisNode fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef resultNode fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef outputNode fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    
    class A1,A2,A3,A4,A5 dataNode
    class B1,B2,B3 prepNode
    class C1,C2,C3 modelNode
    class D1,D2,D3 analysisNode
    class E1,E2,E3 resultNode
    class F1,F2,F3 outputNode
```

## Pipeline Components

### 1. Enhanced Data Preparation

#### Panel Builder Extensions
- **Price Transmission Panels**: Market pair-specific datasets
- **Exchange Pass-through Panels**: Currency-focused analysis
- **Model-specific Panels**: Optimized for each methodology

#### Spatial Features (NEW)
- **K-Nearest Neighbors**: Consider 3 closest markets
- **Distance Weighting**: Inverse distance weighting
- **Spatial Lags**: Price spillover effects

#### Interaction Features (NEW)
- **Zone × Time**: Capture zone-specific trends
- **Conflict × Zone**: Differential conflict impacts
- **Market × Commodity**: Heterogeneous effects

### 2. Enhanced Three-Tier Models

#### Tier 1 Enhancements
- **Spatial Features**: Geographic proximity effects
- **Robust Standard Errors**: Driscoll-Kraay for spatial correlation
- **Interaction Effects**: Zone and time interactions

#### Tier 2: Threshold VECM (NEW)
```python
# Configuration
'use_threshold_model': True
'threshold_search_method': 'grid'
'min_regime_pct': 0.15
```
- **Regime Switching**: Captures market behavior changes
- **Grid Search**: Optimal threshold identification
- **Commodity-specific**: Tailored to each product

#### Tier 3: Advanced Validation (NEW)
- **Granger Causality**: Test causal relationships
- **Variance Decomposition**: Contribution analysis
- **Impulse Response**: Dynamic shock propagation

### 3. Additional Analyses

#### Price Transmission Analysis
Analyzes how prices transmit between market pairs:
- **Error Correction Models**: Short and long-run dynamics
- **Corridor Analysis**: Key trade routes
- **Integration Speed**: Time for price adjustment

#### Exchange Rate Pass-through
Studies dual exchange rate impacts:
- **Complete vs Incomplete**: Degree of pass-through
- **Commodity Heterogeneity**: Different sensitivities
- **Policy Implications**: Currency intervention effects

### 4. Model Comparison Framework
- **Cross-validation**: Spatial and temporal splits
- **Ensemble Methods**: Combine model predictions
- **Performance Metrics**: RMSE, MAE, R-squared

### 5. Enhanced Visualizations

#### Spatial Visualizations
- **Price Heatmaps**: Geographic price patterns
- **Market Networks**: Integration visualization
- **Conflict Overlay**: Price-conflict relationships

#### Dynamic Analysis
- **Convergence Plots**: Market pair dynamics
- **Structural Breaks**: Regime change identification
- **Threshold Visualization**: Regime transitions

## Implementation Flow

### Script Execution Order

1. **prepare_data_for_modeling.py**
   - Loads balanced panel
   - Adds spatial features
   - Adds interaction terms
   - Validates and saves

2. **run_three_tier_models_updated.py**
   - Runs enhanced three-tier analysis
   - Enables threshold VECM for Tier 2
   - Uses advanced validation for Tier 3

3. **analyze_price_transmission.py**
   - Analyzes key corridors
   - Estimates transmission speeds
   - Studies exchange pass-through

4. **enhanced_analysis_pipeline.py**
   - Orchestrates complete pipeline
   - Runs model comparison
   - Creates visualizations
   - Generates comprehensive report

### Data Flow

```
Raw Data → Panel Builder → Feature Engineering → Enhanced Models
                ↓                    ↓                    ↓
         Balanced Panel    Spatial Features    Threshold Results
                ↓                    ↓                    ↓
         Price Transmission → Results Analyzer → Policy Insights
                                     ↓
                            Visualizations & Report
```

## Key Enhancements Over Basic Pipeline

| Component | Basic Pipeline | Enhanced Pipeline |
|-----------|---------------|-------------------|
| **Features** | Basic econometric | + Spatial, interactions |
| **Tier 2** | Standard models | + Threshold VECM |
| **Tier 3** | Basic validation | + Granger, IRF, VD |
| **Analysis** | Three-tier only | + Transmission, pass-through |
| **Models** | Individual | + Ensemble, comparison |
| **Visualization** | Basic plots | + Spatial, dynamic |
| **Validation** | Simple tests | + Advanced econometrics |

## Configuration

Key configuration parameters in `enhanced_analysis_pipeline.py`:

```python
# Spatial features
k_neighbors = 3
distance_weighted = True

# Threshold VECM
use_threshold_model = True
min_regime_pct = 0.15

# Price transmission
corridors = [
    {'base': 'Aden', 'targets': ["Sana'a City", 'Taiz']},
    {'base': 'Al Hodeidah', 'targets': ["Sana'a City", 'Dhamar']}
]

# Advanced validation
use_econometric_validation = True
granger_test = True
variance_decomposition = True
```

## Output Structure

```
results/enhanced_analysis/
├── enhanced_analysis_report.json    # Comprehensive findings
├── figures/
│   ├── spatial_price_heatmap_*.html
│   ├── price_convergence_*.html
│   └── structural_breaks_*.html
├── price_transmission/
│   └── transmission_results.json
└── three_tier_analysis_new/
    ├── tier1/
    ├── tier2/  # With threshold results
    └── tier3/  # With advanced validation
```

## Benefits

1. **Methodological Rigor**: Matches World Bank econometric standards
2. **Deeper Insights**: Regime switching, transmission mechanisms
3. **Policy Relevance**: Actionable recommendations
4. **Robustness**: Multiple validation approaches
5. **Comprehensive**: Integrates all available capabilities