# Revised Econometric Research Plan: The Yemen Exchange Rate-Price Puzzle

## Critical Discovery: The Exchange Rate Mechanism

Your insight about currency differences fundamentally changes our analysis. The "negative price premiums" in conflict areas may be an artifact of exchange rate divergence:

- **Houthi areas**: 535-539 YER/USD (stable, controlled)
- **Government areas**: 2,000-2,150 YER/USD (4x depreciation)

This means the same local price translates to 4x higher USD price in government areas!

## Revised Research Questions

1. **Primary**: Does the law of one price hold in USD terms across Yemen's fragmented markets?
2. **Secondary**: How do exchange rate regimes, global prices, and aid flows explain price patterns?
3. **Mechanism**: Are traders arbitraging using USD as reference currency despite local price controls?

## New Theoretical Framework

### 1. Extended Price Model
```
P_it^local = α_i + β₁Exchange_it + β₂Global_t + β₃Aid_it + β₄Conflict_it + β₅Controls_it + ε_it

Where:
- Exchange_it = Official exchange rate × (1 + Black_market_premium_it)
- Global_t = International commodity price index
- Aid_it = Humanitarian assistance per capita
- Conflict_it = Conflict intensity (instrumented)
```

### 2. Law of One Price Test
```
P_it^USD = P_jt^USD + τ_ij + η_ijt

Where:
- τ_ij = Trade costs between markets i and j
- η_ijt = Arbitrage opportunities (should → 0 if LOP holds)
```

### 3. Exchange Rate Pass-Through
```
ΔlogP_it^local = θ₀ + θ₁ΔlogER_it + θ₂ΔlogP_t^global + θ₃Exchange_regime_it + ν_it
```

## Enhanced Variable Set

### 1. Price Variables (NEW APPROACH)
- **Local currency prices** (YER) - Primary for within-Yemen analysis
- **USD prices** - For law of one price tests
- **Exchange rate adjusted prices** - Using parallel market rates
- **WFP vs FAO prices** - Cross-validation

### 2. Exchange Rate Variables
- Official exchange rates by region
- Black market premiums
- Exchange rate volatility
- Currency control dummy (Houthi vs Government)

### 3. Global Price Controls
- FAO Food Price Index components
- World Bank commodity prices (wheat, rice, sugar)
- Brent crude oil prices (transport costs)
- Baltic Dry Index (shipping costs)

### 4. Aid Distribution Variables
- Cash assistance per capita by governorate
- In-kind food aid volumes
- Number of humanitarian partners
- Distance to aid distribution centers
- OCHA presence indicators

### 5. Additional Control Variables
```python
# Economic variables
- Remittance flows (CBY data)
- Mobile money transactions
- Fuel prices (local)
- Electricity availability hours

# Infrastructure
- Port functionality index
- Road accessibility scores
- Storage facility capacity
- Market infrastructure quality

# Demographic
- Population displacement rates
- Urban/rural classification
- Pre-war market size
- Trader density

# Seasonal/Religious
- Ramadan timing
- Harvest seasons
- Eid celebrations
- School calendar

# Conflict-related
- Frontline distance
- Checkpoint density
- Siege/blockade status
- Coalition airstrikes
```

## Revised Identification Strategy

### 1. Exchange Rate Discontinuity Design
```
# Exploit sharp boundary between currency zones
P_it = α + βTreatment_i + γDistance_to_boundary_i + δ(Treatment × Distance) + X_it + ε_it

Where Treatment = 1 if Houthi-controlled (stable exchange rate)
```

### 2. Triple Difference Approach
```
P_ict = α + β₁Post_t + β₂Houthi_i + β₃Import_c + 
        β₄(Post × Houthi) + β₅(Post × Import) + β₆(Houthi × Import) +
        β₇(Post × Houthi × Import) + ε_ict

# β₇ identifies differential impact on import-dependent goods in controlled FX areas
```

### 3. Instrumental Variables (Revised)
- **IV1**: Pre-war banking infrastructure (predicts currency zone assignment)
- **IV2**: Distance to Saudi border × oil price shocks
- **IV3**: Interaction of global commodity prices × pre-war import shares
- **IV4**: Timing of UN Security Council resolutions

## Empirical Specifications

### Specification 1: Law of One Price Test
```python
# Test if USD prices converge across markets
P_it^USD - P_jt^USD = α + β₁Distance_ij + β₂Conflict_diff_ijt + 
                      β₃Same_control_ijt + β₄Trade_route_open_ijt + ε_ijt
```

### Specification 2: Exchange Rate Pass-Through
```python
# How much do global prices affect local prices?
ΔlogP_it^local = α_i + β₁ΔlogER_it + β₂ΔlogP_t^global + 
                 β₃(ΔlogER × Houthi_i) + β₄Aid_per_capita_it + ε_it
```

### Specification 3: Aid Impact Analysis
```python
# Do aid flows explain negative premiums?
P_it^local = α_i + β₁Conflict_it + β₂Aid_flow_it + β₃(Conflict × Aid) +
             β₄Exchange_rate_it + β₅Global_price_t + ε_it
```

### Specification 4: Full Structural Model
```python
# System of equations
(1) Price equation: P_it = f(Supply, Demand, Aid, Exchange_rate)
(2) Aid equation: Aid_it = g(Conflict, Population, Access)
(3) Exchange equation: ER_it = h(Monetary_policy, Trade_balance)
```

## Data Construction Steps

### 1. Currency Conversion Matrix
```python
# Create consistent price series
for market in markets:
    if market in houthi_areas:
        fx_rate = 537  # Stable rate
        black_market_premium = 0.05
    else:
        fx_rate = cbr_aden_rate[t]  # Time-varying
        black_market_premium = estimate_premium(market, t)
    
    price_usd[market, t] = price_local[market, t] / (fx_rate * (1 + black_market_premium))
```

### 2. Aid Distribution Matching
```python
# Match OCHA data to market areas
- Download OCHA 3W (Who, What, Where) dataset
- Geocode aid distribution points
- Calculate distance to nearest distribution center
- Aggregate cash vs in-kind assistance by governorate/month
```

### 3. Global Price Alignment
```python
# Match commodities to global benchmarks
commodity_mapping = {
    'Wheat flour': 'wheat_us_hrw',
    'Rice (imported)': 'rice_thai_5percent',
    'Sugar': 'sugar_world',
    'Fuel (diesel)': 'diesel_us_gulf'
}
```

## Robustness & Sensitivity Analysis

### 1. Currency Specification Tests
- [ ] Results using only local currency prices
- [ ] Results using only USD prices
- [ ] Results with various black market premium estimates
- [ ] Exclude markets with extreme FX volatility

### 2. Aid Endogeneity Checks
- [ ] Instrument aid with lagged casualties
- [ ] Use predetermined aid allocation formulas
- [ ] Exclude emergency response periods
- [ ] Control for media attention

### 3. Global Price Sensitivity
- [ ] Include/exclude global financial crisis periods
- [ ] Use regional (Dubai/Saudi) prices vs global
- [ ] Test different lag structures (0-6 months)
- [ ] Interact with pre-war import dependence

### 4. Sample Restrictions
- [ ] Balanced panel only
- [ ] Markets with <20% missing data
- [ ] Exclude transition periods (control changes)
- [ ] Commodity-by-commodity analysis

## Expected Results (Revised)

### 1. Main Findings
- **Law of One Price**: Likely holds in USD terms (traders arbitrage)
- **Exchange Rate**: Explains 60-80% of price variation
- **Aid Effect**: Significant in high-assistance areas (-10 to -15%)
- **Global Pass-Through**: Higher in government areas (flexible FX)

### 2. Mechanism Tests
- Currency controls prevent global price transmission in Houthi areas
- Aid creates local price depressions (Dutch disease in reverse)
- Traders use USD pricing despite local currency transactions

### 3. Policy Implications
- Exchange rate unification crucial for market integration
- Cash assistance may be efficient given USD price convergence
- Import support should account for FX regime differences

## Implementation Timeline

### Week 1: Data Preparation
```bash
# 1. Construct exchange rate series
python scripts/build_exchange_rates.py --include-black-market

# 2. Download global prices
python scripts/fetch_global_prices.py --source='worldbank,fao'

# 3. Process OCHA aid data
python scripts/process_ocha_data.py --aggregate='governorate'
```

### Week 2: Price Analysis
```bash
# 1. Test law of one price
python scripts/test_law_of_one_price.py --currency='both'

# 2. Estimate pass-through
python scripts/exchange_rate_passthrough.py

# 3. Run aid impact models
python scripts/aid_impact_analysis.py
```

### Week 3: Full Models
```bash
# 1. Estimate all specifications
python scripts/run_full_models.py --specs='all'

# 2. Robustness checks
python scripts/robustness_battery.py --comprehensive
```

### Week 4: Write-up
- Emphasize exchange rate mechanism discovery
- Show how it resolves the "negative premium puzzle"
- Policy focus on currency unification

## Critical Data Needs

1. **Exchange Rates**: Daily parallel market rates by city
2. **Aid Data**: OCHA 3W dataset with cash/in-kind breakdown
3. **Global Prices**: FAO FPMA + World Bank Pink Sheet
4. **WFP vs FAO**: Both datasets for validation
5. **Trade Routes**: UN OCHA access monitoring

This revised framework addresses the fundamental currency issue and provides a more credible explanation for the price patterns in Yemen.