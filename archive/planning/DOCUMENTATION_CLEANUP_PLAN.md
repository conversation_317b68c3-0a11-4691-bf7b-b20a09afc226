# Documentation Cleanup and Consolidation Plan

## Current State: Documentation Chaos
- 13 root-level markdown files (too many!)
- 66 files in docs/ (needs organization)
- 22 files in reports/ (mixed purposes)
- Multiple versions of similar documents
- No clear entry point for new developers
- Violates CLAUDE.md hierarchy rules

## Target State: Clean, Professional Documentation

### Root Directory (Maximum 5 files)
1. **README.md** - Public-facing, installation, quick start
2. **CLAUDE.md** - Development rules and standards
3. **METHODOLOGY.md** - Econometric approaches (no code)
4. **EXECUTIVE_SUMMARY.md** - High-level findings
5. **CONTRIBUTING.md** - How to contribute

### Organized Structure
```
docs/
├── README.md                    # Documentation hub with clear navigation
├── 00-getting-started/         # For new users/developers
│   ├── installation.md
│   ├── quick-start.md
│   └── first-analysis.md
├── 01-architecture/            # System design
│   ├── overview.md
│   ├── v1-design.md
│   └── v2-design.md
├── 02-user-guides/             # How to use the platform
│   ├── data-pipeline.md
│   ├── running-analysis.md
│   └── interpreting-results.md
├── 03-api-reference/           # Technical API docs
│   ├── rest-api.md
│   ├── python-api.md
│   └── data-formats.md
├── 04-development/             # For contributors
│   ├── setup.md
│   ├── testing.md
│   └── deployment.md
└── 05-methodology/             # Detailed technical methods
    ├── econometric-models.md
    ├── data-processing.md
    └── validation-framework.md

reports/                        # Analysis outputs only
├── analysis/                   # Actual analysis results
├── validation/                 # Model validation reports
└── archive/                    # Old reports

archive/                        # All old/superseded docs
├── planning/                   # Old planning docs
├── versions/                   # Previous versions
└── explorations/               # Research notes
```

## Files to Move/Archive

### Archive These Root Files
- `DOCUMENTATION_SUMMARY.md` → `archive/versions/`
- `ECONOMETRIC_RESEARCH_PLAN.md` → `archive/planning/`
- `ECONOMETRIC_RESEARCH_PLAN_REVISED.md` → Merge into `docs/05-methodology/`
- `ADDITIONAL_ECONOMETRIC_CONSIDERATIONS.md` → Merge into `docs/05-methodology/`
- `NEXT_STEPS_ROADMAP.md` → `archive/planning/`
- `V2_FINALIZATION_STRATEGY.md` → `archive/planning/`
- `V2_IMMEDIATE_ACTION_PLAN.md` → `archive/planning/`
- `V2_PRODUCTION_TRANSITION_PLAN.md` → Extract key parts to `docs/04-development/`

### Consolidate docs/ folder
- Merge overlapping content
- Remove draft versions
- Create clear navigation in docs/README.md
- Archive anything not actively used

### Clean reports/ folder
- Keep only actual analysis results
- Move planning documents to archive
- Remove test reports

## Implementation Steps

1. **Create archive structure**
   ```bash
   mkdir -p archive/{planning,versions,explorations}
   mkdir -p docs/{00-getting-started,01-architecture,02-user-guides,03-api-reference,04-development,05-methodology}
   mkdir -p reports/{analysis,validation,archive}
   ```

2. **Move files to archive**
   - All planning documents
   - Old versions
   - Exploratory notes

3. **Consolidate documentation**
   - Merge similar content
   - Update cross-references
   - Remove duplication

4. **Create navigation hub**
   - Clear docs/README.md with table of contents
   - Purpose-driven organization
   - Search-friendly structure

5. **Update root files**
   - Simplify README.md
   - Ensure CLAUDE.md rules are followed
   - Clean up EXECUTIVE_SUMMARY.md

## Quality Standards

### Every Document Must Have:
1. **Clear Purpose** - One sentence explaining why it exists
2. **Target Audience** - Who should read this
3. **Last Updated** - Date at the top
4. **Navigation** - Links to related docs
5. **Examples** - Code samples where appropriate

### Documentation Principles:
- **DRY** - Don't Repeat Yourself
- **SSOT** - Single Source of Truth
- **Progressive Disclosure** - Simple → Detailed
- **Task-Oriented** - Help users accomplish goals
- **Maintainable** - Easy to update

## Success Criteria
- [ ] Maximum 5 files in root directory
- [ ] Clear navigation from docs/README.md
- [ ] No duplicate content
- [ ] All planning docs archived
- [ ] New developer can understand project in 30 minutes
- [ ] Follows CLAUDE.md hierarchy exactly
- [ ] Search for any topic leads to one authoritative source

## Next Steps
1. Review and approve this plan
2. Execute consolidation (2-3 hours)
3. Update all internal references
4. Create redirect notes for moved files
5. Commit with clear message explaining reorganization