# V2 Immediate Action Plan: Days 1-3

## Day 1 (Monday): Core V2 Activation

### Morning (9 AM - 12 PM)
```bash
# 1. Verify V2 environment
cd v2
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 2. Start V2 services
docker-compose up -d postgres redis
python main.py  # Should start but may fail on data connections
```

### Afternoon (1 PM - 5 PM)
```python
# 3. Wire PanelBuilder to V2
# In v2/src/infrastructure/adapters/panel_builder_adapter.py
class PanelBuilderAdapter:
    def __init__(self, v1_panel_builder):
        self.v1_builder = v1_panel_builder
    
    async def get_price_panel(self, params):
        # Convert V2 params to V1 format
        v1_params = self._convert_params(params)
        # Call V1 synchronously
        panel = await asyncio.to_thread(
            self.v1_builder.create_price_panel,
            **v1_params
        )
        # Convert to V2 domain model
        return self._to_domain_model(panel)
```

### Evening Check
- [ ] V2 API starts without errors
- [ ] Can query V1 data through V2 adapter
- [ ] Basic health check endpoint works

## Day 2 (Tuesday): Exchange Rate Integration

### Morning (9 AM - 12 PM)
```python
# 1. Create Exchange Rate Domain
# In v2/src/core/domain/exchange_rates/entities.py
@dataclass
class ExchangeRate:
    date: datetime
    currency_from: str = "YER"
    currency_to: str = "USD"
    official_rate: float
    parallel_rate: Optional[float]
    zone: str  # "houthi" or "government"
    source: str
    
    @property
    def premium(self) -> float:
        if self.parallel_rate:
            return (self.parallel_rate - self.official_rate) / self.official_rate
        return 0.0
```

### Afternoon (1 PM - 5 PM)
```python
# 2. Quick Exchange Rate Plugin
# In v2/plugins/data_sources/exchange_rates/plugin.py
class StaticExchangeRatePlugin(DataSourcePlugin):
    """Temporary plugin with hardcoded rates for testing"""
    
    RATES = {
        "houthi": {"official": 537, "parallel": 545},
        "government": {"official": 1850, "parallel": 2150}
    }
    
    async def fetch_rates(self, date, zone):
        # TODO: Replace with real data source
        rates = self.RATES.get(zone, self.RATES["government"])
        return ExchangeRate(
            date=date,
            zone=zone,
            **rates,
            source="static"
        )
```

### Evening Check
- [ ] Exchange rate domain models created
- [ ] Basic plugin returning static rates
- [ ] Can query rates through V2 API

## Day 3 (Wednesday): First Econometric Model

### Morning (9 AM - 12 PM)
```python
# 1. Law of One Price Test
# In v2/src/core/models/econometric/law_of_one_price.py
class LawOfOnePriceTest(BaseEconometricModel):
    """Tests if prices converge when converted to USD"""
    
    async def estimate(self, price_panel: PricePanel):
        # Get exchange rates for each market
        rates = await self._get_exchange_rates(price_panel.markets)
        
        # Convert prices to USD
        usd_prices = self._convert_to_usd(price_panel, rates)
        
        # Calculate price differences between market pairs
        price_diffs = self._calculate_price_differences(usd_prices)
        
        # Test convergence
        results = self._test_convergence(price_diffs)
        
        return LawOfOnePriceResults(
            converged=results.pvalue > 0.05,
            speed_of_convergence=results.coefficient,
            half_life_days=self._calculate_half_life(results.coefficient)
        )
```

### Afternoon (1 PM - 5 PM)
```python
# 2. Wire to REST API
# In v2/src/interfaces/api/rest/routes/econometric.py
@router.post("/api/v2/econometric/law-of-one-price")
async def test_law_of_one_price(
    request: LawOfOnePriceRequest,
    analyzer: EconometricAnalyzer = Depends(get_analyzer)
):
    # Run analysis
    results = await analyzer.test_law_of_one_price(
        start_date=request.start_date,
        end_date=request.end_date,
        commodities=request.commodities,
        markets=request.markets
    )
    
    # Return results
    return {
        "status": "success",
        "results": results.dict(),
        "interpretation": results.get_interpretation()
    }
```

### Evening Check
- [ ] Can run law of one price test via API
- [ ] Results include exchange rate adjustments
- [ ] Output suitable for paper tables

## Day 4-5: Rapid Feature Addition

### Priority Features
1. **OCHA Aid Data Plugin** (4 hours)
2. **Global Price Integration** (3 hours)
3. **Triple Difference Model** (6 hours)
4. **Exchange Pass-through Model** (4 hours)
5. **Batch Analysis Runner** (3 hours)

### Quick Win Scripts
```python
# Scripts to accelerate research
# In v2/scripts/run_all_specifications.py
async def main():
    # Initialize V2
    app = await create_application()
    
    # Define all specifications
    specs = [
        {"model": "law_of_one_price", "params": {...}},
        {"model": "exchange_passthrough", "params": {...}},
        {"model": "triple_difference", "params": {...}},
        # ... 20 more specifications
    ]
    
    # Run in parallel
    results = await asyncio.gather(*[
        run_specification(app, spec) for spec in specs
    ])
    
    # Generate LaTeX tables
    generate_paper_outputs(results)
```

## Success Metrics (End of Day 5)

### Must Have
- [ ] V2 API operational
- [ ] Can load V1 data through adapters
- [ ] Exchange rates integrated (static or real)
- [ ] One econometric model working end-to-end
- [ ] Results exportable to LaTeX

### Nice to Have
- [ ] OCHA aid data integrated
- [ ] Multiple model specifications
- [ ] Real exchange rate data source
- [ ] GraphQL endpoint active
- [ ] Performance tracking

## Parallel Work Streams

### Stream 1: V2 Integration (Developer 1)
- Days 1-3: Core activation
- Days 4-5: Plugin development

### Stream 2: Econometric Models (Developer 2/You)
- Days 1-2: Refine specifications
- Days 3-5: Implement in V2 structure

### Stream 3: Data Preparation (Analyst)
- Days 1-2: Gather exchange rate data
- Days 3-4: Process OCHA aid data
- Day 5: Validate all data sources

## Risk Mitigations

### If V2 Activation Stalls
1. Use V1 for initial results
2. Build exchange rate processor in V1
3. Port to V2 when ready

### If Data Sources Unavailable
1. Use static exchange rates initially
2. Approximate aid data from existing sources
3. Focus on methodological contribution

### If Time Runs Short
1. Implement only law of one price test
2. Use simplified exchange rate model
3. Generate key tables manually

## Daily Standup Questions

1. **Yesterday**: What V2 components were activated?
2. **Today**: What econometric model are we implementing?
3. **Blockers**: Any missing data or technical issues?
4. **Paper Progress**: Which tables/figures can we generate?

This aggressive but achievable plan gets V2 operational for your research within a week.