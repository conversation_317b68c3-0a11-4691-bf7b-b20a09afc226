name: Test Coverage

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  coverage:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt', '**/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libgdal-dev libspatialindex-dev
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"
    
    - name: Run tests with coverage
      run: |
        pytest --cov=yemen_market --cov-report=xml --cov-report=html --cov-report=term-missing --cov-fail-under=70
      env:
        HDX_API_KEY: ${{ secrets.HDX_API_KEY }}
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        fail_ci_if_error: false
        verbose: true
    
    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: coverage-report
        path: |
          htmlcov/
          coverage.xml
    
    - name: Generate coverage badge
      run: |
        python scripts/generate_coverage_report.py --no-html --min-coverage=70
    
    - name: Comment coverage on PR
      if: github.event_name == 'pull_request'
      uses: py-cov-action/python-coverage-comment-action@v3
      with:
        GITHUB_TOKEN: ${{ github.token }}
        MINIMUM_GREEN: 90
        MINIMUM_ORANGE: 80
        ANNOTATE_MISSING_LINES: true
        ANNOTATION_TYPE: warning