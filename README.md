# Yemen Market Integration Platform

A sophisticated econometric analysis platform for understanding market dynamics and humanitarian impacts in conflict-affected Yemen.

## Overview

The Yemen Market Integration Platform analyzes price dynamics across 21 markets and 16 essential commodities, revealing how conflict and territorial fragmentation impact food security. Using advanced three-tier econometric modeling, the platform processes 46,200+ price observations to guide humanitarian response.

**Key Finding**: Markets show 40% price variation across regions with 292-540% hyperinflation in essential commodities, demanding immediate intervention.

## Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/your-org/yemen-market-integration.git
cd yemen-market-integration

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment
cp .env.example .env
# Edit .env with your API keys
```

### Running Your First Analysis

```bash
# Download latest data
python scripts/download_data.py

# Run the three-tier econometric analysis
make week5-models

# Generate executive report
python scripts/generate_executive_results.py
```

### View Results

```bash
# Launch interactive dashboard
python scripts/streamlit_dashboard.py

# Generate policy brief
python scripts/generate_report.py --format=policy-brief
```

## Key Features

- **Three-Tier Econometric Framework**: Pooled models, threshold VECM, and factor analysis
- **Multi-Source Data Integration**: WFP, ACLED, ACAPS, and HDX data harmonization  
- **Advanced Diagnostics**: 15+ statistical tests ensuring robust results
- **Policy Tools**: Welfare impact and early warning systems
- **Real-time Monitoring**: Track market dynamics as they evolve

## Documentation

Comprehensive documentation is available in the [`docs/`](./docs/) directory:

- [Getting Started Guide](./docs/00-getting-started/quick-start.md)
- [User Guides](./docs/02-user-guides/)
- [API Reference](./docs/03-api-reference/)
- [Methodology](./METHODOLOGY.md)

## Project Structure

```
yemen-market-integration/
├── src/yemen_market/     # Core analysis modules
├── scripts/              # Analysis and utility scripts
├── data/                 # Data directory (not in git)
├── results/              # Analysis outputs
├── docs/                 # Documentation
├── tests/                # Test suite
└── notebooks/            # Jupyter notebooks
```

## Results

Current analysis reveals:
- **292% wheat price increase** (2019-2025)
- **540% sugar price increase** showing extreme stress
- **88.4% data coverage** despite conflict conditions
- **Negative price premiums** in high-conflict areas (unexpected finding)

For detailed findings, see the [Executive Summary](./EXECUTIVE_SUMMARY.md).

## Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) and follow the development standards in [CLAUDE.md](./CLAUDE.md).

## Testing

```bash
# Run test suite
make test

# Run with coverage
make test-coverage

# Run specific tests
pytest tests/unit/test_panel_builder.py
```

## Requirements

- Python 3.9+
- PostgreSQL 12+ (for V2)
- 8GB RAM minimum
- API keys for data sources (see `.env.example`)

## License

This project is licensed under the MIT License - see [LICENSE](./LICENSE) file for details.

## Citation

If you use this platform in your research, please cite:

```bibtex
@software{yemen_market_integration,
  title={Yemen Market Integration Platform},
  author={[Your Team]},
  year={2024},
  url={https://github.com/your-org/yemen-market-integration}
}
```

## Support

- **Documentation**: [docs/README.md](./docs/README.md)
- **Issues**: [GitHub Issues](https://github.com/your-org/yemen-market-integration/issues)
- **Email**: <EMAIL>

---

*For technical methodology details, see [METHODOLOGY.md](./METHODOLOGY.md). For high-level findings, see [EXECUTIVE_SUMMARY.md](./EXECUTIVE_SUMMARY.md).*