# Quick Reference Card - Yemen Market Integration Research

## 🔤 Variable Naming Convention
```python
# Prices
price_yer_it      # Local currency price, market i, time t
price_usd_it      # USD price (converted)
log_price_it      # Natural log of price

# Exchange Rates  
fx_official_it    # Official exchange rate
fx_parallel_it    # Parallel/black market rate
fx_premium_it     # (parallel - official) / official

# Conflict
conflict_events_it     # Count of events
conflict_binary_it     # 0/1 indicator
conflict_intensity_it  # Events per 100k population
conflict_neighbors_it  # Spatial lag (IV)

# Aid
aid_total_it      # Total aid in USD
aid_cash_it       # Cash assistance only
aid_inkind_it     # In-kind aid value
aid_pc_it         # Aid per capita

# Controls
population_it     # From IOM/OCHA
distance_port_i   # Time-invariant
ramadan_t        # Monthly dummy
global_wheat_t   # FAO global price
```

## 📊 Common Data Transformations
```python
# Log transformation for count data
df['log_conflict'] = np.log(1 + df['conflict_events'])

# Real prices
df['real_price'] = df['price_yer'] / df['cpi']

# First differences (for non-stationarity)
df['d_price'] = df.groupby('market')['price_yer'].diff()

# USD conversion
df['price_usd'] = df['price_yer'] / (df['fx_official'] * (1 + df['fx_premium']))

# Spatial lags
from libpysal.weights import Queen
w = Queen.from_dataframe(markets_gdf)
df['conflict_lag'] = w.lag_spatial(df['conflict'])
```

## 🧮 Standard Specifications in Python
```python
import statsmodels.formula.api as smf
from linearmodels import PanelOLS

# Basic OLS with clustering
model1 = smf.ols('log_price_yer ~ conflict + fx_official + log_global_price', 
                  data=df).fit(cov_type='cluster', cov_kwds={'groups': df['market']})

# Panel Fixed Effects
panel_df = df.set_index(['market', 'date'])
model2 = PanelOLS.from_formula('log_price_yer ~ conflict + fx_official + EntityEffects + TimeEffects',
                               data=panel_df).fit(cov_type='clustered', cluster_entity=True)

# First Differences
model3 = smf.ols('d_price ~ d_conflict + d_fx_official + d_global_price',
                  data=df).fit(cov_type='cluster', cov_kwds={'groups': df['market']})

# IV Regression
from linearmodels.iv import IV2SLS
model4 = IV2SLS.from_formula('log_price_yer ~ [conflict ~ conflict_neighbors] + fx_official',
                             data=df).fit(cov_type='clustered', clusters=df['market'])
```

## 📈 Creating Publication Tables
```python
from stargazer.stargazer import Stargazer

# Create table with multiple models
stargazer = Stargazer([model1, model2, model3])
stargazer.custom_columns(['OLS', 'FE', 'First Diff'])
stargazer.covariate_order(['conflict', 'fx_official', 'log_global_price'])
stargazer.add_line('Market FE', ['No', 'Yes', 'No'])
stargazer.add_line('Time FE', ['No', 'Yes', 'No'])

# Export to LaTeX
with open('tables/main_results.tex', 'w') as f:
    f.write(stargazer.render_latex())
```

## 🎨 Standard Plots
```python
import matplotlib.pyplot as plt
import seaborn as sns

# Time series by currency zone
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))

# YER prices
df.groupby(['date', 'zone'])['price_yer'].mean().unstack().plot(ax=ax1)
ax1.set_title('Average Prices in YER by Currency Zone')
ax1.set_ylabel('Price (YER)')

# USD prices  
df.groupby(['date', 'zone'])['price_usd'].mean().unstack().plot(ax=ax2)
ax2.set_title('Average Prices in USD by Currency Zone')
ax2.set_ylabel('Price (USD)')

plt.tight_layout()
plt.savefig('figures/price_comparison.pdf')

# Coefficient plot
coef_df = pd.DataFrame({
    'variable': ['Conflict', 'Exchange Rate', 'Global Price'],
    'coefficient': [model1.params[1:4]],
    'ci_lower': [model1.conf_int().iloc[1:4, 0]],
    'ci_upper': [model1.conf_int().iloc[1:4, 1]]
})

fig, ax = plt.subplots(figsize=(8, 6))
ax.scatter(coef_df['coefficient'], coef_df['variable'])
ax.hlines(coef_df['variable'], coef_df['ci_lower'], coef_df['ci_upper'])
ax.axvline(0, color='red', linestyle='--', alpha=0.5)
ax.set_xlabel('Coefficient Estimate')
plt.tight_layout()
plt.savefig('figures/coefficient_plot.pdf')
```

## 🚀 Quick Commands
```bash
# Update all data
python scripts/update_all_data.py

# Run main analysis
python scripts/run_main_specifications.py

# Generate summary statistics
python scripts/generate_summary_stats.py

# Check data quality
python scripts/data_quality_checks.py

# Create all figures
python scripts/create_all_figures.py

# Run robustness battery
python scripts/run_robustness_checks.py --all

# Generate LaTeX tables
python scripts/export_tables.py --format=latex
```

## ⚠️ Remember
1. **Always check currency** (YER vs USD)
2. **Log conflict** to handle zeros
3. **Cluster standard errors** by market
4. **Include time FE** for common shocks
5. **Test stationarity** before levels regression

## 📞 Key Contacts
- WFP Data: [email]
- Exchange Rates: [source]
- OCHA Aid: [contact]

---
*Print this page for easy reference during analysis*