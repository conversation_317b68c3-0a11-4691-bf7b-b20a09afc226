{"permissions": {"allow": ["<PERSON><PERSON>(source:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(ls:*)", "<PERSON><PERSON>(chmod:*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(python scripts/utilities/generate_claude_commands.py:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(make update-claude-commands:*)", "Bash(pytest:*)"], "deny": []}, "enableAllProjectMcpServers": false}