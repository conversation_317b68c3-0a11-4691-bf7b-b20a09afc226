Refresh the project context by running:

```bash
cd /Users/<USER>/Documents/GitHub/yemen-market-integration && make update-claude-commands
```

Then read and display the updated context from `.claude/commands/project_context.md`

This command refreshes all project information including:
- Current progress and phase
- Implementation status
- Test coverage
- Recent git activity
- Next steps and focus areas