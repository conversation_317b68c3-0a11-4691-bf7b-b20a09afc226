# Active Research Context

## Current Focus: Exchange Rate Data Construction
**Date**: January 31, 2025  
**Sprint Goal**: Build comprehensive exchange rate dataset to test if "negative premiums" are FX artifacts

## Today's Priority Tasks
- [ ] Find sources for parallel market exchange rates
- [ ] Create script to merge official and parallel rates
- [ ] Build price dataset in both YER and USD
- [ ] Run initial comparison of price variation

## Key Decisions Made Today
1. **Use LOCAL currency (YER) as primary**: More accurate for within-Yemen analysis
2. **Create separate USD series**: For law of one price tests
3. **Include black market premiums**: Essential for true exchange rates
4. **Focus on tradeable goods first**: Where arbitrage should work

## Current Blockers
1. **Need parallel market rates**: Official rates don't reflect reality
   - Action: Contact Yemen market monitors
   - Backup: Estimate from price differentials

2. **OCHA aid data access**: Need 3W dataset
   - Action: Download from HDX platform
   - Backup: Use proxy indicators

## Recent Discoveries
- Exchange rates differ 4x between zones (Houthi: 537, Gov: 2,150)
- This discovery potentially explains entire "negative premium" puzzle
- Need to reframe paper around currency mechanism

## Questions for Next Meeting
1. Should we lead with exchange rate discovery?
2. How to handle markets that switch control?
3. Is triple-diff the best identification?
4. Where to submit - JDE or conflict journal?

## Code Snippets for Today
```python
# Build exchange rate dataset
fx_official = pd.read_csv('data/raw/cby_exchange_rates.csv')
fx_parallel = estimate_parallel_rates(fx_official, price_data)

# Create dual price series
df['price_usd_official'] = df['price_yer'] / df['fx_official']
df['price_usd_parallel'] = df['price_yer'] / df['fx_parallel']

# Quick comparison
print(f"CV in YER: {df['price_yer'].std() / df['price_yer'].mean()}")
print(f"CV in USD: {df['price_usd_parallel'].std() / df['price_usd_parallel'].mean()}")
```

## Files Modified Today
- `CLAUDE.md` - Refactored for econometric research
- `.claude/research/*` - Created new research structure
- `ECONOMETRIC_RESEARCH_PLAN_REVISED.md` - Major revision with FX focus

## Tomorrow's Plan
1. Complete exchange rate dataset
2. Run first specifications comparing YER vs USD
3. Create visualization of price convergence
4. Draft introduction highlighting puzzle

## Paper Framing Notes
- Lead with puzzle: "Conflict lowers prices?"
- Reveal: It's exchange rates, not conflict
- Contribution: First to identify this mechanism
- Policy: Currency unification matters more than expected

## Reminders for Next Session
- Check if HDX has updated OCHA data
- Look for Yemen trader surveys on pricing
- Review Cariolle et al. (2023) on multi-currency zones
- Set up meeting to discuss identification strategy

---
*Last updated: End of session January 31, 2025*
*Next session: Focus on data construction*