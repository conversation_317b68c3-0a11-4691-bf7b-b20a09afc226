# Yemen Panel Data: Quick Implementation Guide

## Overview
This guide provides practical implementation steps for the three-tier panel methodology designed to handle Yemen's 3D panel data structure (market × commodity × time).

## Quick Start

### 1. Pooled Panel (Primary Analysis)
```python
import linearmodels as lm
import pandas as pd

# Load and prepare data
data = pd.read_parquet('data/processed/panels/integrated_panel.parquet')
data['log_price'] = np.log(data['price_usd'])

# Set multi-index
panel = data.set_index(['market_id', 'commodity_id', 'date'])

# Create interactions
panel['conflict_x_wheat'] = panel['conflict_events'] * (panel['commodity'] == 'Wheat')
panel['conflict_x_rice'] = panel['conflict_events'] * (panel['commodity'] == 'Rice')

# Estimate model
model = lm.PanelOLS(
    dependent=panel['log_price'],
    exog=panel[['conflict_events', 'conflict_x_wheat', 'conflict_x_rice']],
    entity_effects=True,    # Market FE
    time_effects=True,      # Time FE
    other_effects=panel.index.get_level_values('commodity_id')  # Commodity FE
)

# Fit with clustered SEs
results = model.fit(
    cov_type='clustered',
    clusters=panel.index.get_level_values(['market_id', 'date'])
)

print(results.summary)
```

### 2. Commodity-Specific Analysis (Secondary)
```python
def analyze_commodity(data, commodity_name):
    """Run threshold VECM for specific commodity"""
    
    # Extract 2D panel
    comm_data = data[data['commodity'] == commodity_name]
    panel_2d = comm_data.pivot(
        index='date',
        columns='market_id',
        values='price_usd'
    )
    
    # Get conflict data
    conflict = comm_data.pivot(
        index='date',
        columns='market_id',
        values='conflict_events'
    ).mean(axis=1)  # Average across markets
    
    # Estimate threshold
    from src.yemen_market.models.track2_simple import SimpleThresholdVECM
    
    model = SimpleThresholdVECM(
        data=panel_2d,
        conflict_data=conflict,
        threshold_range=(20, 100)
    )
    
    results = model.fit()
    return results

# Run for key commodities
commodities = ['Wheat', 'Rice', 'Sugar', 'Vegetable oil']
commodity_results = {}

for commodity in commodities:
    print(f"\nAnalyzing {commodity}...")
    commodity_results[commodity] = analyze_commodity(data, commodity)
```

### 3. Factor Analysis (Validation)
```python
from sklearn.decomposition import PCA
import numpy as np

# Create commodity matrix
commodity_matrix = data.pivot_table(
    index=['market_id', 'date'],
    columns='commodity_id',
    values='log_price',
    aggfunc='mean'
)

# Handle missing values
filled_matrix = commodity_matrix.fillna(method='ffill').fillna(method='bfill')

# Extract factors
pca = PCA(n_components=3)
factors = pca.fit_transform(filled_matrix)

# Analyze factor loadings
loadings = pd.DataFrame(
    pca.components_.T,
    columns=['Factor1', 'Factor2', 'Factor3'],
    index=commodity_matrix.columns
)

print(f"Variance explained: {pca.explained_variance_ratio_}")
print(f"\nTop commodities per factor:")
for i in range(3):
    print(f"Factor {i+1}: {loadings[f'Factor{i+1}'].nlargest(3).index.tolist()}")

# Regress factors on conflict
factor_df = pd.DataFrame(factors, index=filled_matrix.index)
factor_df['conflict'] = data.groupby(['market_id', 'date'])['conflict_events'].mean()

# Simple regression
import statsmodels.api as sm
X = sm.add_constant(factor_df['conflict'])
y = factor_df[0]  # First factor

model = sm.OLS(y, X).fit()
print(f"\nConflict effect on Factor 1: {model.params['conflict']:.4f} (p={model.pvalues['conflict']:.3f})")
```

## Data Preparation Checklist

### For Pooled Panel
- [ ] Ensure multi-index compatibility
- [ ] Create log prices
- [ ] Generate interaction terms
- [ ] Check for missing combinations
- [ ] Verify balanced time periods

### For Commodity-Specific
- [ ] Filter to single commodity
- [ ] Pivot to wide format
- [ ] Align conflict data
- [ ] Check minimum observations per market
- [ ] Handle missing values appropriately

### For Factor Analysis
- [ ] Create wide commodity matrix
- [ ] Standardize prices if needed
- [ ] Handle missing data consistently
- [ ] Choose number of factors
- [ ] Prepare conflict regressors

## Common Issues and Solutions

### Issue 1: Reshape Error
```python
# Error: "Index contains duplicate entries, cannot reshape"

# Solution: Check for duplicates
duplicates = data.groupby(['date', 'market_id', 'commodity']).size()
print(f"Duplicate entries: {(duplicates > 1).sum()}")

# Remove duplicates (keeping mean)
data_clean = data.groupby(['date', 'market_id', 'commodity']).agg({
    'price_usd': 'mean',
    'conflict_events': 'first',
    # other columns...
}).reset_index()
```

### Issue 2: Memory Error with Full Panel
```python
# Solution: Work with subset first
key_commodities = ['Wheat', 'Rice', 'Sugar', 'Vegetable oil']
data_subset = data[data['commodity'].isin(key_commodities)]

# Or sample markets
key_markets = data.groupby('market_id').size().nlargest(10).index
data_subset = data[data['market_id'].isin(key_markets)]
```

### Issue 3: Convergence Issues
```python
# For pooled panel - try different SE corrections
try:
    results = model.fit(cov_type='clustered', clusters=...)
except:
    # Fall back to simpler SE
    results = model.fit(cov_type='robust')
```

## Interpretation Guidelines

### Pooled Model
- **Base effect**: Average impact across all commodities
- **Interactions**: Differential effects by commodity
- **Magnitude**: exp(coefficient) - 1 = % change in prices

### Commodity-Specific
- **Threshold**: Conflict level causing regime change
- **Speed**: How fast prices converge in each regime
- **Comparison**: Which commodities are most sensitive

### Factor Analysis
- **Factor 1**: Usually captures overall price level
- **Factor 2-3**: Commodity group variations
- **Validation**: Should confirm pooled results

## Next Steps

1. **Run all three approaches** on the same data subset
2. **Compare results** across methods
3. **Document findings** in standardized format
4. **Create visualizations** for each approach
5. **Write up** for World Bank standards

## Quick Commands

```bash
# Run pooled model
python scripts/analysis/run_pooled_panel.py

# Run commodity analysis
python scripts/analysis/run_commodity_models.py --commodity wheat

# Run factor analysis  
python scripts/analysis/run_factor_analysis.py

# Generate comparison report
python scripts/analysis/compare_methodologies.py
```

## Diagnostic Requirements

### Critical Diagnostics for Publication

All models must pass diagnostic tests to meet World Bank standards:

#### For Pooled Panel (Tier 1)
```python
# After fitting model, run diagnostics
from yemen_market.models.three_tier.diagnostics import ThreeTierPanelDiagnostics

diagnostics = ThreeTierPanelDiagnostics(tier=1)
diag_report = diagnostics.run_diagnostics(model.results, data)

# Check critical tests
if not diag_report.passed['wooldridge_test']:
    print("⚠️ Serial correlation detected - applying robust SEs")
    results = model.fit(cov_type='clustered', cluster_entity=True, cluster_time=True)

if not diag_report.passed['pesaran_cd']:
    print("⚠️ Cross-sectional dependence - applying Driscoll-Kraay SEs")
    results = model.fit(cov_type='driscoll-kraay')
```

#### Required Tests Checklist
- [ ] **Wooldridge test** for serial correlation (p > 0.05)
- [ ] **Pesaran CD test** for cross-sectional dependence (p > 0.05)
- [ ] **Im-Pesaran-Shin** panel unit root test
- [ ] **Modified Wald** for heteroskedasticity
- [ ] **Hausman test** for FE vs RE specification

### Handling Diagnostic Failures

1. **Serial Correlation**: Use cluster-robust or HAC standard errors
2. **Cross-sectional Dependence**: Apply Driscoll-Kraay corrections
3. **Unit Roots**: Consider first-differencing or VECM
4. **Heteroskedasticity**: Use robust (HC3) standard errors

### Diagnostic Workflow

```python
def ensure_valid_model(model, data):
    """Ensure model passes diagnostic tests."""
    
    # Initial fit
    results = model.fit()
    
    # Run diagnostics
    diagnostics = ThreeTierPanelDiagnostics(tier=1)
    report = diagnostics.run_diagnostics(results, data)
    
    # Apply corrections if needed
    if report.has_critical_failures():
        corrections = report.get_recommended_corrections()
        
        for correction in corrections:
            if correction == 'cluster_robust':
                results = model.fit(cov_type='clustered')
            elif correction == 'driscoll_kraay':
                results = model.fit(cov_type='driscoll-kraay')
        
        # Re-run diagnostics
        report = diagnostics.run_diagnostics(results, data)
    
    # Generate diagnostic table for publication
    report.save_publication_table('diagnostics_table.tex')
    
    return results, report
```

---
**Remember**: This is a publication-grade analysis. Document everything, test thoroughly, validate results across methods, and ensure all diagnostic tests pass or are properly corrected.