# Task 101: Design V2 Database Schema

## Context
V2 requires a complete database schema that supports all V1 functionality while enabling new capabilities like multi-currency support, exchange rates, and aid distribution tracking.

## Requirements

### Core Tables

1. **Markets Table**
```sql
CREATE TABLE markets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(20) UNIQUE NOT NULL,  -- Legacy code for V1 compatibility
    name VARCHAR(100) NOT NULL,
    governorate VARCHAR(100) NOT NULL,
    district VARCHAR(100) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    control_zone VARCHAR(50),  -- 'houthi', 'government', 'contested'
    zone_since DATE,  -- When current control started
    population INTEGER,
    urban_rural VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_markets_governorate ON markets(governorate);
CREATE INDEX idx_markets_control_zone ON markets(control_zone);
```

2. **Commodities Table**
```sql
CREATE TABLE commodities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50),  -- 'food', 'fuel', 'non-food'
    unit VARCHAR(20),  -- 'KG', 'L', 'piece'
    international_code VARCHAR(20),  -- For global price matching
    is_imported BOOLEAN DEFAULT true,
    is_aid_distributed BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT NOW()
);
```

3. **Prices Table**
```sql
CREATE TABLE prices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    market_id UUID REFERENCES markets(id),
    commodity_id UUID REFERENCES commodities(id),
    date DATE NOT NULL,
    price_local DECIMAL(12, 2) NOT NULL,
    currency_code VARCHAR(3) DEFAULT 'YER',
    price_usd DECIMAL(12, 2),
    exchange_rate_used DECIMAL(10, 4),
    exchange_rate_source VARCHAR(50),
    data_source VARCHAR(50),  -- 'WFP', 'FAO', 'MARKET_SURVEY'
    quality_flag VARCHAR(20),  -- 'actual', 'interpolated', 'estimated'
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(market_id, commodity_id, date, data_source)
);

CREATE INDEX idx_prices_date ON prices(date);
CREATE INDEX idx_prices_market_commodity ON prices(market_id, commodity_id);
```

4. **Exchange Rates Table**
```sql
CREATE TABLE exchange_rates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    zone VARCHAR(50) NOT NULL,  -- 'houthi', 'government', 'aden', 'sana'
    currency_from VARCHAR(3) DEFAULT 'YER',
    currency_to VARCHAR(3) DEFAULT 'USD',
    official_rate DECIMAL(10, 4),
    parallel_rate DECIMAL(10, 4),
    bank_rate DECIMAL(10, 4),
    source VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(date, zone, currency_from, currency_to)
);
```

5. **Conflict Events Table**
```sql
CREATE TABLE conflict_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    governorate VARCHAR(100),
    district VARCHAR(100),
    event_type VARCHAR(50),
    actors TEXT[],
    fatalities INTEGER DEFAULT 0,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    source VARCHAR(50),  -- 'ACLED', 'CIMP'
    source_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_conflict_date_location ON conflict_events(date, governorate);
```

6. **Control Areas Table**
```sql
CREATE TABLE control_areas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    governorate VARCHAR(100) NOT NULL,
    district VARCHAR(100),
    control_authority VARCHAR(100),  -- 'Houthi', 'IRG', 'STC', etc.
    control_type VARCHAR(50),  -- 'full', 'partial', 'contested'
    source VARCHAR(50),  -- 'ACAPS', 'UN'
    geometry GEOMETRY(MultiPolygon, 4326),  -- PostGIS spatial data
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(date, governorate, district)
);
```

7. **Aid Distribution Table**
```sql
CREATE TABLE aid_distribution (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    governorate VARCHAR(100) NOT NULL,
    district VARCHAR(100),
    organization VARCHAR(100),
    aid_type VARCHAR(50),  -- 'cash', 'voucher', 'in-kind'
    commodity_id UUID REFERENCES commodities(id),
    quantity DECIMAL(12, 2),
    unit VARCHAR(20),
    value_usd DECIMAL(12, 2),
    beneficiaries INTEGER,
    source VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Analysis Tables

8. **Analysis Runs Table**
```sql
CREATE TABLE analysis_runs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200),
    analysis_type VARCHAR(50),  -- 'three-tier', 'law-of-one-price', etc.
    parameters JSONB,
    status VARCHAR(50),  -- 'pending', 'running', 'completed', 'failed'
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_by VARCHAR(100),
    results_summary JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

9. **Model Results Table**
```sql
CREATE TABLE model_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_run_id UUID REFERENCES analysis_runs(id),
    model_type VARCHAR(50),
    tier INTEGER,
    commodity_id UUID REFERENCES commodities(id),
    results JSONB,  -- Full model output
    diagnostics JSONB,  -- Diagnostic test results
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Migration Support

10. **V1 Compatibility View**
```sql
CREATE VIEW v1_prices AS
SELECT 
    m.code as market,
    c.code as commodity,
    p.date,
    p.price_local,
    p.price_usd,
    p.exchange_rate_used
FROM prices p
JOIN markets m ON p.market_id = m.id
JOIN commodities c ON p.commodity_id = c.id;
```

## Data Migration Strategy

1. **Markets Migration**
```sql
INSERT INTO markets (code, name, governorate, district, latitude, longitude)
SELECT DISTINCT 
    market_id,
    market_name,
    governorate,
    district,
    lat,
    lon
FROM v1_panel_data;
```

2. **Commodities Migration**
```sql
INSERT INTO commodities (code, name, category, unit)
SELECT DISTINCT
    commodity,
    commodity,
    CASE 
        WHEN commodity LIKE '%Fuel%' THEN 'fuel'
        ELSE 'food'
    END,
    unit
FROM v1_panel_data;
```

3. **Prices Migration**
```sql
-- Run in batches to handle large volume
INSERT INTO prices (market_id, commodity_id, date, price_local, price_usd, exchange_rate_used)
SELECT 
    m.id,
    c.id,
    v1.date,
    v1.price_local,
    v1.price_usd,
    v1.price_usd / NULLIF(v1.price_local, 0)
FROM v1_panel_data v1
JOIN markets m ON v1.market_id = m.code
JOIN commodities c ON v1.commodity = c.code;
```

## Performance Considerations

1. **Indexing Strategy**
   - Primary keys on all tables (UUID for flexibility)
   - Composite indexes on common query patterns
   - Partial indexes for filtered queries
   - BRIN indexes for time-series data

2. **Partitioning**
   - Partition prices table by year
   - Partition conflict_events by year
   - Consider sharding by governorate for scale

3. **Query Optimization**
   - Materialized views for common aggregations
   - Table statistics updated regularly
   - Query plan analysis for critical paths

## Acceptance Criteria

- [ ] All V1 data models represented in schema
- [ ] Migration scripts tested with sample data
- [ ] Performance benchmarks meet requirements (<100ms for typical queries)
- [ ] Indexes optimized for common query patterns
- [ ] Constraints ensure data integrity
- [ ] Schema supports multi-currency analysis
- [ ] PostGIS configured for spatial queries
- [ ] Backup and recovery procedures documented
- [ ] Schema versioning implemented (Flyway/Liquibase)
- [ ] Complete ERD diagram created

## Dependencies
- PostgreSQL 14+ with PostGIS extension
- Database design review from team
- Sample V1 data for migration testing

## Estimated Effort
3 days (design, review, implementation, testing)

## Files to Create
- `v2/migrations/001_initial_schema.sql`
- `v2/migrations/002_indexes.sql` 
- `v2/migrations/003_v1_data_migration.sql`
- `v2/docs/database/ERD.png`
- `v2/docs/database/migration_guide.md`