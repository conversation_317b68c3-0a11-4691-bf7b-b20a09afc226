# Prompt: Run and Fix All Three-Tier Methodology Tests

## Context

You are working on the Yemen Market Integration project that implements a rigorous three-tier econometric methodology for analyzing market integration in conflict settings. The project has comprehensive test coverage for all three phases, but some tests may be failing due to missing implementations or import issues.

## Critical Requirements

1. **NO SHORTCUTS**: Every failing test must be fixed properly by implementing the missing functionality, not by simplifying tests
2. **NO MOCK SIMPLIFICATION**: Keep all sophisticated mocking patterns - they test real behavior
3. **MAINTAIN TEST QUALITY**: All edge cases, error handling, and comprehensive testing must remain
4. **IMPLEMENT MISSING CODE**: If a test fails because code doesn't exist, implement it fully according to the methodology

## Your Task

### Step 1: Initial Test Discovery

```bash
# First, understand the test structure
find tests/unit/models/three_tier -name "test_*.py" -type f | sort

# Count total tests
python -m pytest tests/unit/models/three_tier --collect-only | grep "test session starts" -A 10
```

### Step 2: Run Tests by Tier

Run tests systematically, tier by tier, to identify failures:

```bash
# Test Tier 1 (Pooled Panel)
python -m pytest tests/unit/models/three_tier/tier1_pooled/ -xvs

# Test Tier 2 (Commodity-Specific) 
python -m pytest tests/unit/models/three_tier/tier2_commodity/ -xvs

# Test Tier 3 (Validation)
python -m pytest tests/unit/models/three_tier/tier3_validation/ -xvs

# Test Core Components
python -m pytest tests/unit/models/three_tier/core/ -xvs

# Test Integration
python -m pytest tests/unit/models/three_tier/integration/ -xvs

# Test Migration
python -m pytest tests/unit/models/three_tier/migration/ -xvs
```

### Step 3: Fix Import Errors

Many tests will fail due to missing imports. For each import error:

1. Check if the module/class exists in the codebase
2. If it doesn't exist, CREATE IT with full implementation based on:
   - The test expectations
   - The three-tier methodology in METHODOLOGY.md
   - The docstrings and type hints in the tests

Example fixes needed:

- `PooledPanelConfig` class in `pooled_panel_model.py`
- `ValidationResult` class in `data_validator.py`
- `VersionManager` class in `model_migration.py`
- Any missing methods indicated by the tests

### Step 4: Implement Missing Classes

Based on test requirements, implement these core classes:

#### For Core Components

- `BaseThreeTierModel` (abstract base class)
- `BaseModelConfig` (pydantic model for configuration)
- `PanelDataHandler` with methods like `transform_to_2d`, `check_balance`, etc.
- `DataValidator` with comprehensive validation methods
- `ResultsContainer` with all accessor methods

#### For Tier 2

- Ensure all config classes exist (CommodityModelConfig, ThresholdVECMConfig, etc.)
- Implement all methods expected by tests

### Step 5: Fix Failing Tests

For each failing test:

1. Read the test to understand expected behavior
2. Implement the missing functionality completely
3. Ensure all assertions pass
4. Run the specific test again to confirm

### Step 6: Verify Full Test Suite

After fixing individual components:

```bash
# Run entire test suite
python -m pytest tests/unit/models/three_tier/ -v

# Generate coverage report
python -m pytest tests/unit/models/three_tier/ --cov=yemen_market.models.three_tier --cov-report=html
```

### Step 7: Document Test Results

Create a summary of:

- Total tests run
- All tests passing
- Coverage percentage
- Any remaining warnings (deprecation warnings are OK)

## Important Implementation Guidelines

### When Creating Missing Classes

1. Follow the structure implied by the tests
2. Use proper type hints and docstrings
3. Implement all methods that tests expect
4. Use the enhanced logging system (`from yemen_market.utils.logging import info, debug`)
5. Follow the architectural patterns in existing code

### Example Implementation Pattern

```python
"""Module docstring explaining purpose."""

from dataclasses import dataclass
from typing import Optional, Dict, Any, List, Tuple
import pandas as pd
from yemen_market.utils.logging import info, bind

# Set module context
bind(module=__name__)

@dataclass
class ConfigClass:
    """Configuration for the component."""
    param1: float = 0.5
    param2: int = 100
    
class ComponentClass:
    """Main implementation class."""
    
    def __init__(self, config: Optional[ConfigClass] = None):
        self.config = config or ConfigClass()
        
    def expected_method(self, data: pd.DataFrame) -> pd.DataFrame:
        """Implement based on test expectations."""
        info("Processing data", shape=data.shape)
        # Full implementation here
        return processed_data
```

## Expected Outcome

- ALL tests in tests/unit/models/three_tier/ should pass (100%)
- No simplified implementations or shortcuts
- Full functionality as expected by the comprehensive test suite
- Proper logging throughout the implementation
- Clean, well-documented code following project standards

## Final Verification

After all tests pass, run:

```bash
# Lint check
make lint

# Type check  
make typecheck

# Final test run with coverage
python -m pytest tests/unit/models/three_tier/ --cov=yemen_market.models.three_tier --cov-report=term-missing
```

Remember: The tests define the contract. Implement exactly what they expect, not simplified versions.
