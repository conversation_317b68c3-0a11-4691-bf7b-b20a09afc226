# Cloud Code Migration & Alignment Inspection Prompt

## Context
You are an expert software architect and econometrician tasked with conducting a comprehensive manual inspection of the Yemen Market Integration v2 migration. Your goal is to verify alignment with the architectural proposal and assess migration completeness.

## Your Mission
Perform a detailed, systematic inspection of the v2 implementation against the original architectural proposal located at `docs/architecture/yemen_market_integration_v2_proposal.md`. Focus on identifying gaps, misalignments, and areas where implementation exceeds or falls short of the plan.

## Inspection Framework

### 1. Architecture Principles Verification
**Check each principle against implementation:**

```bash
# Hexagonal Architecture Assessment
- Examine `v2/src/core/` for domain purity (no external dependencies)
- Verify `v2/src/infrastructure/` contains all external concerns
- Check dependency inversion in `v2/src/shared/container.py`

# Domain-Driven Design Assessment  
- Inspect bounded contexts: `v2/src/core/domain/{market,conflict,geography}/`
- Verify rich domain models with business rules
- Check ubiquitous language consistency

# Event-Driven Architecture Assessment
- Examine `v2/src/core/domain/shared/events.py`
- Check event bus implementation in `v2/src/infrastructure/messaging/`
- Verify async processing patterns
```

### 2. Directory Structure Compliance
**Compare proposed vs actual structure:**

```bash
# Proposed Structure (from docs/architecture/yemen_market_integration_v2_proposal.md lines 54-210)
# vs
# Actual Structure in v2/

# Key areas to inspect:
- Core domain organization
- Application layer structure  
- Infrastructure components
- Interface layer implementation
- Plugin system architecture
```

### 3. Domain Layer Deep Dive
**Inspect each bounded context:**

```bash
# Market Bounded Context
v2/src/core/domain/market/
├── entities.py          # Market, PriceObservation
├── value_objects.py     # MarketId, Coordinates, Price
├── repositories.py      # Repository interfaces
└── services.py          # Domain services

# Conflict Bounded Context  
v2/src/core/domain/conflict/
├── entities.py          # ConflictEvent
├── value_objects.py     # ConflictIntensity, ConflictType
└── services.py          # ConflictAnalysisService

# Geography Bounded Context
v2/src/core/domain/geography/
├── entities.py          # GeographicZone, District, Governorate  
└── services.py          # SpatialAnalysisService
```

### 4. Econometric Models Assessment
**Verify three-tier methodology implementation:**

```bash
# Core Models (v2/src/core/models/)
- interfaces/            # Model, Estimator abstractions
- panel/                # Tier 1: PooledPanel, FixedEffects, TwoWayFixedEffects
- time_series/          # Tier 2: VECM, ThresholdVECM  
- validation/           # Tier 3: Factor, PCA, ConflictValidation, CrossValidation

# Compare with v1 implementation in src/yemen_market/models/three_tier/
# Check for feature parity and methodological consistency
```

### 5. Technology Stack Verification
**Check proposed vs implemented technologies:**

```bash
# Inspect v2/pyproject.toml against proposal requirements:
# - Python 3.11+
# - FastAPI for API
# - Pydantic v2 for validation
# - Typer for CLI
# - asyncio for async processing
# - dependency-injector for DI
# - PostgreSQL with asyncpg
# - Redis for caching
```

### 6. Infrastructure Components Audit
**Examine each infrastructure layer:**

```bash
# Persistence Layer
v2/src/infrastructure/persistence/
├── repositories/        # PostgreSQL implementations
├── migrations/         # Database schema
└── unit_of_work.py     # Transaction management

# External Services
v2/src/infrastructure/external_services/
├── hdx_client.py       # HDX integration
├── wfp_client.py       # WFP data source
└── acled_client.py     # Conflict data

# Caching Layer
v2/src/infrastructure/caching/
├── redis_cache.py      # Redis implementation
└── memory_cache.py     # In-memory cache

# Messaging
v2/src/infrastructure/messaging/
└── event_bus.py        # Event handling
```

### 7. Application Layer Analysis
**Verify CQRS and use case implementation:**

```bash
# Commands (v2/src/application/commands/)
- analyze_market_integration.py
- run_three_tier_analysis.py

# Queries (v2/src/application/queries/)
- Check query handlers implementation

# Services (v2/src/application/services/)
- analysis_orchestrator.py
- data_preparation_service.py  
- model_estimator_service.py
```

### 8. Interface Layer Inspection
**Check API and CLI implementations:**

```bash
# REST API (v2/src/interfaces/api/rest/)
- app.py                # FastAPI application
- routes/               # API endpoints
- middleware/           # Request handling

# CLI (v2/src/interfaces/cli/)
- app.py                # Typer CLI application
- commands/             # CLI commands
```

### 9. Plugin System Evaluation
**Assess extensibility framework:**

```bash
# Plugin Infrastructure
v2/src/shared/plugins/
├── interfaces.py       # Plugin contracts
├── manager.py          # Plugin management
└── registry.py         # Plugin discovery

# Example Plugin
v2/plugins/models/custom_vecm/
└── plugin.py           # Custom VECM implementation
```

### 10. Deployment & DevOps Verification
**Check production readiness:**

```bash
# Docker Configuration
- v2/Dockerfile         # Multi-stage build
- v2/Dockerfile.worker  # Background workers
- v2/docker-compose.yml # Local development

# Kubernetes Manifests
v2/kubernetes/
├── api-deployment.yaml
├── worker-deployment.yaml  
├── postgres.yaml
├── redis.yaml
├── monitoring.yaml
└── ingress.yaml

# CI/CD Pipeline
v2/.github/workflows/ci.yml

# Deployment Scripts
v2/scripts/deploy.sh
```

## Specific Inspection Tasks

### Task 1: Migration Completeness Assessment
```bash
# Compare v1 vs v2 feature parity:
1. List all econometric models in src/yemen_market/models/three_tier/
2. Verify corresponding implementations in v2/src/core/models/
3. Check for missing functionality or degraded capabilities
4. Assess v1 adapter implementation in v2/src/infrastructure/adapters/v1_adapter.py
```

### Task 2: Architectural Compliance Check
```bash
# Verify clean architecture principles:
1. Check core domain has no external dependencies
2. Verify dependency inversion throughout
3. Assess separation of concerns
4. Check for architectural violations
```

### Task 3: Production Readiness Evaluation
```bash
# Assess deployment and operational readiness:
1. Review Kubernetes manifests completeness
2. Check monitoring and observability setup
3. Verify security configurations
4. Assess scalability provisions
```

### Task 4: Performance & Quality Metrics
```bash
# Verify success criteria achievement:
1. Check file sizes (<300 lines requirement)
2. Assess test coverage (>95% target)
3. Verify type coverage (100% target)
4. Check performance optimizations
```

## Inspection Deliverables

### 1. Alignment Matrix
Create a detailed comparison table:
```
| Component | Proposed | Implemented | Status | Gap Analysis |
|-----------|----------|-------------|---------|--------------|
| Domain Models | X | Y | ✅/⚠️/❌ | Details |
```

### 2. Gap Analysis Report
Document any discrepancies:
- **Critical Gaps**: Missing core functionality
- **Minor Gaps**: Non-essential features not implemented  
- **Enhancements**: Areas where implementation exceeds proposal

### 3. Migration Status Assessment
Provide percentage completion:
- **Architecture**: X% complete
- **Domain Models**: X% complete
- **Infrastructure**: X% complete
- **Deployment**: X% complete
- **Overall**: X% complete

### 4. Recommendations
Prioritized action items:
1. **High Priority**: Critical gaps requiring immediate attention
2. **Medium Priority**: Important improvements
3. **Low Priority**: Nice-to-have enhancements

## Inspection Commands

### Quick Architecture Overview
```bash
# Get high-level structure
find v2/src -type f -name "*.py" | head -20
tree v2/src -I "__pycache__|*.pyc" -L 3

# Check key files exist
ls -la v2/src/core/domain/
ls -la v2/src/core/models/
ls -la v2/kubernetes/
```

### Detailed Component Analysis
```bash
# Count lines per file (should be <300)
find v2/src -name "*.py" -exec wc -l {} + | sort -n

# Check imports for dependency violations
grep -r "import.*infrastructure" v2/src/core/
grep -r "import.*interfaces" v2/src/core/

# Verify test coverage
find v2/tests -name "*.py" | wc -l
```

### Technology Stack Verification
```bash
# Check dependencies
cat v2/pyproject.toml | grep -A 20 "\[tool.poetry.dependencies\]"

# Verify Docker setup
docker build -t test-build v2/ --dry-run
```

## Success Criteria
Your inspection should verify:
- ✅ **95%+ alignment** with architectural proposal
- ✅ **100% feature parity** with v1 econometric capabilities  
- ✅ **Production readiness** for deployment
- ✅ **Code quality standards** met
- ✅ **Performance targets** achievable

## Final Assessment
Provide a clear verdict:
- **READY FOR PRODUCTION**: Full alignment, no critical gaps
- **MINOR ADJUSTMENTS NEEDED**: Small gaps, mostly aligned
- **SIGNIFICANT WORK REQUIRED**: Major gaps or misalignments

Focus on being thorough, objective, and providing actionable insights for any identified gaps or improvements.
