# .claude Directory Structure

This directory contains research-specific context for the Yemen Market Integration econometric analysis.

## Directory Structure

```
.claude/
├── README.md                    # This file
├── ACTIVE_CONTEXT.md           # Current work tracking (create new)
├── research/                   # Research organization
│   ├── RESEARCH_DASHBOARD.md   # Overall progress tracking
│   ├── QUICK_REFERENCE.md      # Variable names, commands
│   ├── data_sources/           # Documentation for each dataset
│   ├── specifications/         # Econometric models to run
│   ├── results_log/           # Track results as we get them
│   ├── hypotheses/            # Hypotheses and evidence
│   ├── literature/            # Key papers and findings
│   └── paper_sections/        # Draft sections of paper
├── tasks/                     # Original task files (completed)
├── archive/                   # Historical documents
├── commands/                  # Custom slash commands
├── prompts/                   # Reusable prompts
└── models/                    # Model guides

## Research Workflow

1. **Start each session**: Check `ACTIVE_CONTEXT.md` for current focus
2. **Review dashboard**: See `research/RESEARCH_DASHBOARD.md` for overall status
3. **Run specifications**: Use models from `research/specifications/`
4. **Log results**: Use template in `research/results_log/`
5. **Update hypotheses**: Track evidence in `research/hypotheses/`
6. **Quick reference**: Keep `research/QUICK_REFERENCE.md` open while coding

## Key Research Files

- **ACTIVE_CONTEXT.md**: What we're working on RIGHT NOW
- **research/RESEARCH_DASHBOARD.md**: Overall progress, data status, paper outline
- **research/QUICK_REFERENCE.md**: Variable definitions, code snippets
- **research/specifications/main_specifications.md**: Core models to test
- **research/hypotheses/exchange_rate_hypothesis.md**: Primary hypothesis tracking

## How to Use for Research

### Daily Workflow
1. Check ACTIVE_CONTEXT for today's focus
2. Review relevant hypothesis file
3. Run specifications from specifications/
4. Log results using template
5. Update hypothesis with evidence
6. Update dashboard with progress
7. Set tomorrow's tasks in ACTIVE_CONTEXT

### Data Documentation
- Each data source has detailed documentation in `data_sources/`
- Track coverage, quality issues, merge keys
- Document any data transformations

### Results Tracking
- Every regression gets logged in `results_log/`
- Include exact code, results, interpretation
- Track which results go in which paper table

## Legacy Content

### `/commands/` - Development commands (kept for reference)
### `/prompts/` - Development prompts (kept for reference)
### `/models/` - Original model guides
### `/tasks/` - Completed development tasks

## See Also
- Research Guide: `/CLAUDE.md` (updated for econometric focus)
- Methodology: `/METHODOLOGY.md`
- Research Plans: `/ECONOMETRIC_RESEARCH_PLAN_REVISED.md`