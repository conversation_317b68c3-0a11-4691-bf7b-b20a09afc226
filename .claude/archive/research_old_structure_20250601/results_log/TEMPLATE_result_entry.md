# Result Entry: [SPEC_NAME]

## Specification Details
**Date Run**: [DATE]  
**Specification ID**: [SPEC_X]  
**Description**: [One line description]  
**Sample**: [e.g., "All markets, 2019-2025, balanced panel"]  
**N**: [Number of observations]

## Code
```python
# Exact code that produced this result
[PASTE CODE HERE]
```

## Main Results

### Key Coefficients
| Variable | Coefficient | Std Error | P-value | 95% CI |
|----------|-------------|-----------|---------|---------|
| conflict | -0.XXX | (0.XXX) | 0.XXX | [-X.XX, -X.XX] |
| exchange_rate | X.XXX | (0.XXX) | 0.XXX | [X.XX, X.XX] |
| global_price | X.XXX | (0.XXX) | 0.XXX | [X.XX, X.XX] |
| aid_pc | -X.XXX | (0.XXX) | 0.XXX | [-X.XX, -X.XX] |

### Model Statistics
- R-squared: 0.XXX
- F-statistic: XXX.XX
- Within R-sq (if FE): 0.XXX
- Number of clusters: XXX

## Interpretation
[2-3 sentences on what this means]

## Surprises/Concerns
- [Any unexpected results]
- [Potential issues with specification]
- [Things to investigate further]

## Robustness Performed
- [ ] Winsorized at 1%: [Result]
- [ ] Excluded capitals: [Result]  
- [ ] Different clustering: [Result]
- [ ] Alternative measure: [Result]

## Next Steps
1. [What to test next based on these results]
2. [Additional robustness needed]
3. [Follow-up specifications]

## Files
- Output saved to: `results/[date]/spec_X_output.log`
- Table saved to: `results/[date]/spec_X_table.tex`
- Data used: `data/processed/panel_[version].dta`

---
**For Paper**: [Which table/figure this contributes to]