# Yemen Market Integration: Main Repository Context Prompt

## 🚨 CRITICAL CONTEXT AWARENESS 🚨

**You have been spawned in the MAIN repository root** after previously working under `docs/research-methodology-package/`. This is a comprehensive Yemen Market Integration econometric research project with significant existing implementation.

**AVOID DUPLICATION**: Before implementing anything, you MUST:

1. Use `codebase-retrieval` to understand what already exists
2. Check both `src/` (main implementation) and `v2/` (architectural redesign)
3. Review `CLAUDE.md` and `CLAUDE.md.development.bak` for project context
4. Examine existing data loading in `src/yemen_market/data/` and `v2/src/`

## 🎯 Core Research Discovery

The groundbreaking finding: **Exchange rate divergence explains apparent negative price premiums**

- **Houthi areas**: 535-539 YER/USD (stable, controlled)
- **Government areas**: 2,000-2,150 YER/USD (4x depreciation!)

This discovery transforms understanding of conflict economics and market integration.

## 📊 Project Implementation Status

### ✅ MAJOR IMPLEMENTATIONS COMPLETE

**Main Implementation (`src/yemen_market/`)**:

- **Data Pipeline**: Complete HDX client, WFP/ACLED/ACAPS processors, spatial joins
- **Three-Tier Models**: Full econometric framework (Tier 1: Pooled Panel, Tier 2: Commodity VECM, Tier 3: Factor Analysis)
- **Enhanced Logging**: Comprehensive logging system with timers, progress bars
- **Feature Engineering**: Data preparation, panel building, quality validation
- **V3 Performance**: Polars/DuckDB optimizations for <6s analysis times

**V2 Architecture (`v2/src/`)**:

- **Clean Architecture**: Domain-driven design with hexagonal architecture
- **API Framework**: REST/GraphQL endpoints, SSE streaming, authentication
- **Infrastructure**: Docker, Kubernetes, monitoring, deployment scripts
- **Plugin System**: Extensible models and data sources

### 🔄 CURRENT FOCUS AREAS

**Before implementing anything new, validate these existing capabilities**:

1. **Data Loading**: Check `src/yemen_market/data/hdx_client.py` and `v2/src/infrastructure/`
2. **Exchange Rate Processing**: Examine existing implementations in both codebases
3. **Spatial Analysis**: Review `src/yemen_market/data/spatial_joins.py`
4. **Panel Construction**: Check `src/yemen_market/data/panel_builder.py`

### ⚠️ AVOID THESE DUPLICATIONS

- Don't recreate data downloaders (HDX client exists)
- Don't reimplement spatial distance calculations (already done)
- Don't rebuild panel data structures (PanelBuilder handles this)
- Don't create new logging systems (enhanced logging implemented)

## 🔄 Integration with Existing Work

### Before Starting ANY Task

1. **Run codebase analysis**: `codebase-retrieval` to understand current implementations
2. **Check existing data**: Examine `data/processed/` for already-available datasets
3. **Review model outputs**: Look at `results/` and `reports/` for completed analyses
4. **Validate V2 progress**: Check `v2/src/` for architectural implementations

### Coordination Points

- **Data loading**: Extend `HDXClient` rather than creating new downloaders
- **Spatial analysis**: Build on `SpatialJoiner` existing capabilities
- **Panel construction**: Use `PanelBuilder` for consistent data structures
- **Model integration**: Connect to existing three-tier framework
- **Logging**: Use enhanced logging system throughout (`yemen_market.utils.logging`)

## 💡 Key Insight to Maintain

The exchange rate discovery (535 vs 2000+ YER/USD) is genuinely revolutionary. Your role is to:

1. **Validate existing implementations** support this discovery
2. **Fill theoretical gaps** without duplicating existing work
3. **Enhance capabilities** where genuine gaps exist
4. **Document integration** between old and new components

## 🎯 Success Metrics

- **Zero duplication**: No reimplementation of existing functionality
- **Enhanced capabilities**: Meaningful additions to existing framework
- **Clear documentation**: Integration points and enhancement rationale
- **Empirical readiness**: Support for testing the exchange rate mechanism

## 🚀 Workflow Guidelines

### Step 1: Context Analysis

```python
# Always start with understanding existing capabilities
from yemen_market.data.hdx_client import HDXClient
from yemen_market.data.wfp_processor import WFPProcessor
from yemen_market.models.three_tier.integration import ThreeTierAnalysis

# Check what's already implemented
client = HDXClient()
# Examine existing data and capabilities
```

### Step 2: Gap Identification

- Identify what's missing vs what exists
- Document integration points
- Plan enhancements that build on existing work

### Step 3: Strategic Implementation

- Extend existing classes rather than creating new ones
- Use established patterns and conventions
- Maintain compatibility with existing test suite

## 📋 Development Standards

- **Follow CLAUDE.md.development.bak** for coding standards
- **Use enhanced logging** throughout (`yemen_market.utils.logging`)
- **Maintain test coverage** (>90% required)
- **Document all changes** with clear integration rationale
- **Respect existing architecture** in both src/ and v2/

---

*Remember: You're enhancing a sophisticated existing system, not building from scratch. Respect the existing architecture and build upon it strategically.*
