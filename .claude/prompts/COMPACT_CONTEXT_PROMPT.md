# Yemen Market Integration: Compact Context Knowledge

## 🎯 ESSENTIAL CONTEXT

**You are working on the Yemen Market Integration econometric research project.** 

**CRITICAL DISCOVERY**: Exchange rate divergence (535 YER/USD in Houthi areas vs 2000+ YER/USD in government areas) explains why conflict zones show LOWER prices - it's a currency artifact, not an economic anomaly.

## 📊 PROJECT STATUS

### ✅ MAJOR IMPLEMENTATIONS COMPLETE
- **Main codebase**: `src/yemen_market/` - Three-tier econometric framework, HDX data pipeline, enhanced logging
- **V2 architecture**: `v2/src/` - Clean architecture, domain models, API framework, Kubernetes deployment
- **Documentation**: Comprehensive 00-10 structure in `docs/`
- **Data pipeline**: HDX client, WFP/ACLED/ACAPS processors, spatial joins, panel builder

### 🔄 CURRENT FOCUS
**Before implementing anything**: Use `codebase-retrieval` to check existing implementations in both `src/` and `v2/`

## 🚨 CRITICAL RULES

1. **NEVER mix YER and USD** in same analysis
2. **Exchange rate timing matters** - match price and FX dates exactly
3. **Missing data is non-random** - markets stop reporting during conflict
4. **Aid is endogenous** - requires instrumental variables
5. **Use existing infrastructure** - extend HDXClient, PanelBuilder, SpatialJoiner rather than recreating

## 🏗️ TECHNICAL FRAMEWORK

### Three-Tier Methodology
```python
# Tier 1: Pooled panel (44k+ observations)
# Fixed effects with Driscoll-Kraay standard errors

# Tier 2: Commodity-specific TVECM
# Threshold models by currency zone

# Tier 3: Policy validation
# Factor analysis and natural experiments
```

### Key Specifications
```stata
# Main test - if currency_zone coefficient ≈ 0, exchange rate mechanism proven
reg price_usd conflict global_price aid_pc i.market##i.commodity i.month, cluster(market)
```

## 📁 ESSENTIAL FILES TO REVIEW

**Context Knowledge**: `docs/research-methodology-package/working-sessions/COMPLETE_CONTEXT_DUMP.md`

**Development Rules**: `CLAUDE.md` and `CLAUDE.md.development.bak`

**Main Implementation**:
- `src/yemen_market/data/hdx_client.py` - Data loading
- `src/yemen_market/data/panel_builder.py` - Panel construction  
- `src/yemen_market/models/three_tier/` - Econometric models

**V2 Architecture**:
- `v2/src/core/domain/` - Domain models
- `v2/src/application/services/` - Analysis pipeline

## 🎯 KEY HYPOTHESES (H1-H10)

**H1 (PRIMARY)**: Exchange rate mechanism explains price paradox
**H2**: Aid distribution affects local prices
**H3**: Demand destruction from displacement
**H4-H10**: Import channels, market power, information, storage, transport, regulation, expectations

## 📈 CRITICAL FINDINGS

- Market integration declined 35% since conflict
- Price increases: 292-540% in YER, much less in USD
- Aid 26% less effective in volatile currency zones
- Natural experiments: 2020 aid cuts, 2021 fuel crisis

## 🔧 WORKFLOW

1. **Context Analysis**: Review existing implementations with `codebase-retrieval`
2. **Gap Identification**: Find what's missing vs what exists
3. **Strategic Enhancement**: Extend existing components, don't recreate
4. **Integration**: Ensure compatibility with three-tier framework
5. **Documentation**: Clear rationale for all changes

## 💡 SUCCESS CRITERIA

- **Zero duplication** of existing functionality
- **Enhanced capabilities** that support exchange rate discovery
- **Clear integration** with existing architecture
- **Empirical readiness** for testing H1-H10

---

**Remember**: This is a sophisticated existing system. Your job is to enhance it strategically, not rebuild it. The exchange rate discovery (535 vs 2000+ YER/USD) is revolutionary - everything should support testing this mechanism.

**For complete context**: Review `docs/research-methodology-package/working-sessions/COMPLETE_CONTEXT_DUMP.md`
