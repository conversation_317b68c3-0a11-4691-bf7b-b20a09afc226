# Variable Definitions and Transformations

prices:
  price_yer:
    description: "Local currency price"
    unit: "YER per unit"
    source: "WFP field data"
    
  price_usd:
    description: "USD price (converted)"
    unit: "USD per unit"
    transformation: "price_yer / (fx_official * (1 + fx_premium))"
    
  log_price:
    description: "Natural log of price"
    transformation: "np.log(price)"
    note: "Use for percentage interpretation"

exchange_rates:
  fx_official:
    description: "Official exchange rate"
    unit: "YER/USD"
    source: "CBY Aden and Sana'a"
    
  fx_parallel:
    description: "Parallel market rate"
    unit: "YER/USD"
    source: "Market monitors"
    
  fx_premium:
    description: "Black market premium"
    transformation: "(fx_parallel - fx_official) / fx_official"
    unit: "Percentage"

conflict:
  conflict_events:
    description: "Count of conflict events"
    source: "ACLED"
    unit: "Count per month"
    
  log_conflict:
    description: "Log of conflict events"
    transformation: "np.log(1 + conflict_events)"
    note: "Handles zeros, percentage interpretation"
    
  conflict_binary:
    description: "Any conflict indicator"
    transformation: "conflict_events > 0"
    unit: "0/1 dummy"

aid:
  aid_total:
    description: "Total humanitarian aid"
    source: "OCHA 3W"
    unit: "USD"
    
  aid_cash:
    description: "Cash assistance only"
    source: "OCHA 3W"
    unit: "USD"
    
  aid_pc:
    description: "Aid per capita"
    transformation: "aid_total / population"
    unit: "USD per person"

controls:
  population:
    description: "Market area population"
    source: "IOM/OCHA estimates"
    unit: "Persons"
    frequency: "Annual with interpolation"
    
  distance_port:
    description: "Distance to nearest port"
    source: "Calculated from coordinates"
    unit: "Kilometers"
    time_varying: false
    
  ramadan:
    description: "Ramadan month indicator"
    source: "Islamic calendar"
    unit: "0/1 dummy"
    
  global_wheat:
    description: "Global wheat price"
    source: "FAO/World Bank"
    unit: "USD per metric ton"

standard_transformations:
  first_difference: "df.groupby('market')[var].diff()"
  lag_one: "df.groupby('market')[var].shift(1)"
  spatial_lag: "w.lag_spatial(df[var])"  # Requires weight matrix
  
interaction_terms:
  zone_time: "pd.get_dummies(df['zone']) * df['post']"
  import_conflict: "df['import_share'] * df['log_conflict']"