# Task 27: V2 Core Data Pipeline Implementation - Domain Models

## Overview
Implement comprehensive domain models for markets, prices, commodities, and conflict data in V2 architecture. Port V1's data structures to V2 domain entities with proper value objects and business rules.

## Dependencies
- Task 4: V2 Codebase Structure Analysis (completed)
- Task 7: Codebase Reality Report (completed)

## Detailed Implementation Plan

### 1. Market Aggregate Implementation

```python
# v2/src/core/domain/market/entities.py
class Market(Aggregate):
    """Market aggregate root with business rules"""
    
    def __init__(self, market_id: MarketId, name: str, coordinates: Coordinates):
        self.id = market_id
        self.name = name
        self.coordinates = coordinates
        self.governorate = None
        self.control_status = ControlStatus.UNKNOWN
        self.active = True
        
    def update_control_status(self, status: ControlStatus, date: datetime):
        """Update control status with validation"""
        if self.control_status != status:
            self.raise_event(ControlStatusChanged(self.id, status, date))
            self.control_status = status
```

### 2. Price Observation Entities

```python
# v2/src/core/domain/market/value_objects.py
@dataclass(frozen=True)
class Price:
    """Immutable price value object with currency"""
    amount: Decimal
    currency: Currency
    
    def convert_to(self, target_currency: Currency, rate: Decimal) -> 'Price':
        """Convert price to different currency"""
        if self.currency == target_currency:
            return self
        return Price(self.amount * rate, target_currency)
```

### 3. Conflict Domain Models

```python
# v2/src/core/domain/conflict/entities.py
class ConflictEvent(Entity):
    """Conflict event with spatial-temporal properties"""
    
    def __init__(self, event_id: str, event_type: EventType, 
                 location: Coordinates, date: datetime, fatalities: int):
        self.id = event_id
        self.event_type = event_type
        self.location = location
        self.date = date
        self.fatalities = fatalities
        self.severity = self._calculate_severity()
```

### 4. Data Validation Rules

```python
# v2/src/core/domain/shared/validators.py
class PriceValidator:
    """Domain validation for prices"""
    
    @staticmethod
    def validate_outlier(price: Price, historical: List[Price]) -> bool:
        """Detect price outliers using IQR method"""
        if len(historical) < 10:
            return True
        
        values = [p.amount for p in historical]
        q1, q3 = np.percentile(values, [25, 75])
        iqr = q3 - q1
        lower = q1 - 1.5 * iqr
        upper = q3 + 1.5 * iqr
        
        return lower <= price.amount <= upper
```

### 5. Domain Services

```python
# v2/src/core/domain/market/services.py
class MarketIntegrationService:
    """Calculate market integration metrics"""
    
    def calculate_integration(self, market1: Market, market2: Market, 
                            prices: List[PriceObservation]) -> IntegrationScore:
        """Calculate pairwise market integration"""
        # Implementation matching V1 logic
        pass
```

## Testing Strategy

### Unit Tests
```python
# tests/unit/core/domain/test_market_entities.py
def test_market_control_status_update():
    market = Market(MarketId("M001"), "Sana'a", Coordinates(15.3694, 44.1910))
    market.update_control_status(ControlStatus.HOUTHI, datetime.now())
    
    assert market.control_status == ControlStatus.HOUTHI
    assert len(market.events) == 1
```

### Business Rule Tests
```python
def test_price_outlier_detection():
    historical = [Price(Decimal("100"), Currency.YER) for _ in range(20)]
    outlier = Price(Decimal("500"), Currency.YER)
    
    assert not PriceValidator.validate_outlier(outlier, historical)
```

## Implementation Checklist

- [ ] Create domain entity base classes
- [ ] Implement Market aggregate with value objects
- [ ] Create Price and Commodity value objects
- [ ] Implement Conflict domain models
- [ ] Add validation rules and policies
- [ ] Create domain services
- [ ] Write comprehensive unit tests
- [ ] Ensure V1 compatibility
- [ ] Document business rules

## Success Criteria

1. All domain models follow DDD principles
2. 100% unit test coverage for domain layer
3. Business rules clearly expressed in code
4. Value objects are immutable
5. Aggregates maintain consistency
6. Compatible with V1 data structures

## Estimated Duration
- Development: 3-4 days
- Testing: 1-2 days
- Documentation: 1 day
- Total: 5-7 days