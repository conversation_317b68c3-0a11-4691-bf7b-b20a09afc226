# Task 31: V2 Three-Tier Analysis Service via V1Adapter

## Overview
Implement ThreeTierAnalysisService using V1Adapter pattern to leverage existing econometric models. Ensure results match V1's validated 35% conflict effect findings.

## Dependencies
- Task 30: V2 Panel Builder Service Implementation

## Detailed Implementation Plan

### 1. V1Adapter Interface Design

```python
# v2/src/infrastructure/adapters/v1_adapter.py
from abc import ABC, abstractmethod
from typing import Any, Dict
import pandas as pd

class V1ModelAdapter(ABC):
    """Base adapter for V1 econometric models"""
    
    @abstractmethod
    async def prepare_data(self, panel_data: PanelData) -> pd.DataFrame:
        """Convert V2 panel data to V1 format"""
        pass
    
    @abstractmethod
    async def run_model(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Execute V1 model and return results"""
        pass
    
    @abstractmethod
    async def extract_results(self, raw_results: Any) -> ModelResults:
        """Convert V1 results to V2 format"""
        pass
```

### 2. Three-Tier Analysis Service

```python
# v2/src/application/services/three_tier_analysis_service.py
class ThreeTierAnalysisService:
    """Orchestrate three-tier econometric analysis"""
    
    def __init__(self, 
                 tier1_adapter: Tier1Adapter,
                 tier2_adapter: Tier2Adapter,
                 tier3_adapter: Tier3Adapter,
                 event_bus: EventBus):
        self.tier1 = tier1_adapter
        self.tier2 = tier2_adapter
        self.tier3 = tier3_adapter
        self.event_bus = event_bus
    
    async def run_analysis(self, 
                          panel_data: PanelData,
                          config: AnalysisConfig) -> ThreeTierResults:
        """Execute complete three-tier analysis"""
        
        # Emit start event
        await self.event_bus.publish(
            AnalysisStarted(analysis_id=config.id, timestamp=datetime.now())
        )
        
        try:
            # Tier 1: Pooled Panel
            tier1_results = await self._run_tier1(panel_data, config)
            
            # Tier 2: Commodity-Specific
            tier2_results = await self._run_tier2(panel_data, config)
            
            # Tier 3: Validation
            tier3_results = await self._run_tier3(panel_data, tier1_results, tier2_results)
            
            # Aggregate results
            final_results = self._aggregate_results(tier1_results, tier2_results, tier3_results)
            
            # Validate conflict effect
            self._validate_conflict_effect(final_results)
            
            return final_results
            
        except Exception as e:
            await self.event_bus.publish(
                AnalysisFailed(analysis_id=config.id, error=str(e))
            )
            raise
```

### 3. Tier1 Adapter Implementation

```python
# v2/src/infrastructure/adapters/tier1_adapter.py
from src.models.three_tier.tier1_pooled import PooledPanelModel

class Tier1Adapter(V1ModelAdapter):
    """Adapter for V1 Tier1 pooled panel models"""
    
    async def prepare_data(self, panel_data: PanelData) -> pd.DataFrame:
        """Convert to V1 panel format"""
        # Market × Commodity × Time structure
        df = pd.DataFrame()
        
        for observation in panel_data.observations:
            df = df.append({
                'market_id': observation.market_id,
                'commodity': observation.commodity.name,
                'date': observation.date,
                'price_usd': observation.price.convert_to(Currency.USD).amount,
                'conflict_intensity': observation.conflict_intensity,
                'lag_price_usd': observation.lag_price
            }, ignore_index=True)
        
        return df
    
    async def run_model(self, data: pd.DataFrame, **kwargs) -> Dict[str, Any]:
        """Execute V1 pooled panel model"""
        model = PooledPanelModel()
        results = model.estimate(
            data=data,
            dependent_var='price_usd',
            fixed_effects=['market_id', 'commodity'],
            cluster_var='market_id'
        )
        return results
    
    async def extract_results(self, raw_results: Any) -> Tier1Results:
        """Extract and validate Tier1 results"""
        conflict_coef = raw_results.params['conflict_intensity']
        
        return Tier1Results(
            conflict_effect=conflict_coef,
            std_error=raw_results.bse['conflict_intensity'],
            p_value=raw_results.pvalues['conflict_intensity'],
            r_squared=raw_results.rsquared,
            n_observations=raw_results.nobs
        )
```

### 4. Result Validation

```python
# v2/src/application/services/validation/result_validator.py
class ConflictEffectValidator:
    """Validate econometric results match expected findings"""
    
    EXPECTED_CONFLICT_EFFECT = 0.35  # 35% increase
    TOLERANCE = 0.05  # ±5% tolerance
    
    def validate(self, results: ThreeTierResults) -> ValidationResult:
        """Ensure conflict effect matches V1 findings"""
        
        actual_effect = results.tier1.conflict_effect
        expected_range = (
            self.EXPECTED_CONFLICT_EFFECT - self.TOLERANCE,
            self.EXPECTED_CONFLICT_EFFECT + self.TOLERANCE
        )
        
        is_valid = expected_range[0] <= actual_effect <= expected_range[1]
        
        if not is_valid:
            logger.warning(
                f"Conflict effect {actual_effect:.3f} outside expected range "
                f"[{expected_range[0]:.3f}, {expected_range[1]:.3f}]"
            )
        
        return ValidationResult(
            is_valid=is_valid,
            actual=actual_effect,
            expected=self.EXPECTED_CONFLICT_EFFECT,
            message=f"Conflict effect validation: {actual_effect:.1%}"
        )
```

### 5. Integration Tests

```python
# tests/integration/test_three_tier_service.py
@pytest.mark.asyncio
async def test_three_tier_analysis_matches_v1():
    """Verify V2 analysis matches V1 results"""
    
    # Load test data
    panel_data = await load_test_panel_data()
    
    # Run V2 analysis
    service = ThreeTierAnalysisService(
        tier1_adapter=Tier1Adapter(),
        tier2_adapter=Tier2Adapter(),
        tier3_adapter=Tier3Adapter(),
        event_bus=MockEventBus()
    )
    
    v2_results = await service.run_analysis(panel_data, AnalysisConfig())
    
    # Run V1 analysis for comparison
    v1_results = run_v1_analysis(panel_data)
    
    # Compare results
    assert abs(v2_results.tier1.conflict_effect - v1_results['conflict_effect']) < 0.001
    assert v2_results.tier1.p_value < 0.001  # Highly significant
```

## Testing Strategy

1. **Unit Tests**: Test each adapter method independently
2. **Integration Tests**: Verify V1/V2 parity
3. **Performance Tests**: Ensure no significant slowdown
4. **Validation Tests**: Confirm 35% conflict effect
5. **Regression Tests**: Compare with historical V1 results

## Implementation Checklist

- [ ] Design V1Adapter base class
- [ ] Implement Tier1 adapter
- [ ] Implement Tier2 adapter
- [ ] Implement Tier3 adapter
- [ ] Create ThreeTierAnalysisService
- [ ] Add result aggregation logic
- [ ] Implement conflict effect validation
- [ ] Write comprehensive tests
- [ ] Performance optimization
- [ ] Document adapter patterns

## Success Criteria

1. Results match V1 within 0.1% tolerance
2. 35% conflict effect reproduced (p < 0.001)
3. No performance degradation
4. All diagnostic tests pass
5. Clean adapter pattern implementation

## Estimated Duration
- Development: 4-5 days
- Testing: 2-3 days
- Validation: 1-2 days
- Total: 7-10 days