# Task 33: V2 Core Analysis API Endpoints

## Overview
Implement missing REST API endpoints for three-tier analysis including request handling, validation, and response formatting. Complete 60% of unimplemented endpoints.

## Dependencies
- Task 31: V2 Three-Tier Analysis Service via V1Adapter
- Task 32: V2 Policy Model Integration

## Detailed Implementation Plan

### 1. Analysis Endpoints

```python
# v2/src/interfaces/api/rest/routes/analysis.py
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from typing import List, Optional
import uuid

router = APIRouter(prefix="/api/v1/analysis", tags=["analysis"])

@router.post("/three-tier", response_model=AnalysisResponse)
async def create_three_tier_analysis(
    request: ThreeTierAnalysisRequest,
    background_tasks: BackgroundTasks,
    service: ThreeTierAnalysisService = Depends(get_analysis_service),
    db: AsyncSession = Depends(get_db)
) -> AnalysisResponse:
    """
    Create a new three-tier econometric analysis.
    
    This endpoint initiates a comprehensive market integration analysis including:
    - Tier 1: Pooled panel regression
    - Tier 2: Commodity-specific VECM models
    - Tier 3: Factor analysis validation
    """
    try:
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        
        # Validate request
        await validate_analysis_request(request)
        
        # Create analysis record
        analysis = Analysis(
            id=analysis_id,
            status=AnalysisStatus.PENDING,
            config=request.dict(),
            created_at=datetime.utcnow()
        )
        db.add(analysis)
        await db.commit()
        
        # Queue background analysis
        background_tasks.add_task(
            run_analysis_task,
            analysis_id=analysis_id,
            request=request,
            service=service
        )
        
        return AnalysisResponse(
            id=analysis_id,
            status=AnalysisStatus.PENDING,
            message="Analysis queued for processing",
            estimated_duration_seconds=300
        )
        
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))
```

### 2. Market Endpoints

```python
# v2/src/interfaces/api/rest/routes/markets.py
@router.get("/markets", response_model=MarketListResponse)
async def list_markets(
    governorate: Optional[str] = None,
    control_zone: Optional[str] = None,
    active: Optional[bool] = True,
    skip: int = 0,
    limit: int = 100,
    repo: MarketRepository = Depends(get_market_repository)
) -> MarketListResponse:
    """
    List all markets with optional filtering.
    
    Query parameters:
    - governorate: Filter by governorate name
    - control_zone: Filter by control zone (HOUTHI, GOVERNMENT, CONTESTED)
    - active: Filter active/inactive markets
    - skip: Pagination offset
    - limit: Maximum results (max 1000)
    """
    filters = MarketFilters(
        governorate=governorate,
        control_zone=control_zone,
        active=active
    )
    
    markets = await repo.find_many(
        filters=filters,
        skip=skip,
        limit=min(limit, 1000)
    )
    
    total = await repo.count(filters=filters)
    
    return MarketListResponse(
        data=[MarketDTO.from_entity(m) for m in markets],
        pagination=PaginationInfo(
            skip=skip,
            limit=limit,
            total=total,
            has_more=skip + limit < total
        )
    )

@router.get("/markets/{market_id}/prices", response_model=PriceSeriesResponse)
async def get_market_prices(
    market_id: str,
    commodity: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    currency: str = "USD",
    repo: PriceRepository = Depends(get_price_repository)
) -> PriceSeriesResponse:
    """Get price time series for a specific market"""
    
    prices = await repo.find_by_market(
        market_id=market_id,
        commodity=commodity,
        start_date=start_date,
        end_date=end_date
    )
    
    # Convert currency if needed
    if currency != "USD":
        prices = await convert_prices(prices, target_currency=currency)
    
    return PriceSeriesResponse(
        market_id=market_id,
        commodity=commodity,
        currency=currency,
        data=[PricePointDTO.from_entity(p) for p in prices],
        statistics=calculate_price_statistics(prices)
    )
```

### 3. Request/Response Models

```python
# v2/src/interfaces/api/rest/schemas/analysis.py
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict
from datetime import date

class ThreeTierAnalysisRequest(BaseModel):
    """Request model for three-tier analysis"""
    
    name: str = Field(..., description="Analysis name for identification")
    description: Optional[str] = Field(None, description="Analysis description")
    
    # Temporal parameters
    start_date: date = Field(..., description="Analysis start date")
    end_date: date = Field(..., description="Analysis end date")
    
    # Spatial parameters
    markets: Optional[List[str]] = Field(
        None, 
        description="Specific markets to include (all if not specified)"
    )
    governorates: Optional[List[str]] = Field(
        None,
        description="Filter by governorates"
    )
    
    # Commodity parameters
    commodities: Optional[List[str]] = Field(
        None,
        description="Specific commodities to analyze (all if not specified)"
    )
    commodity_groups: Optional[List[str]] = Field(
        None,
        description="Analyze commodity groups (FOOD, FUEL, etc.)"
    )
    
    # Model parameters
    confidence_level: float = Field(
        0.95,
        ge=0.8,
        le=0.99,
        description="Confidence level for intervals"
    )
    include_diagnostics: bool = Field(
        True,
        description="Include diagnostic tests in results"
    )
    
    @validator('end_date')
    def validate_date_range(cls, v, values):
        if 'start_date' in values and v <= values['start_date']:
            raise ValueError('end_date must be after start_date')
        return v

class AnalysisResultsResponse(BaseModel):
    """Complete analysis results"""
    
    id: str
    status: AnalysisStatus
    
    # Tier 1 Results
    tier1: Tier1ResultsDTO
    
    # Tier 2 Results
    tier2: Dict[str, Tier2ResultsDTO]  # By commodity
    
    # Tier 3 Results
    tier3: Tier3ResultsDTO
    
    # Summary
    summary: AnalysisSummaryDTO
    
    # Metadata
    config: Dict
    started_at: datetime
    completed_at: datetime
    duration_seconds: float
```

### 4. Error Handling

```python
# v2/src/interfaces/api/rest/middleware/error_handler.py
from fastapi import Request, status
from fastapi.responses import JSONResponse
from src.core.domain.shared.exceptions import *

async def domain_exception_handler(request: Request, exc: DomainException):
    """Handle domain-specific exceptions"""
    
    error_mapping = {
        MarketNotFoundException: status.HTTP_404_NOT_FOUND,
        InvalidDateRangeException: status.HTTP_400_BAD_REQUEST,
        InsufficientDataException: status.HTTP_422_UNPROCESSABLE_ENTITY,
        AnalysisFailedException: status.HTTP_500_INTERNAL_SERVER_ERROR
    }
    
    status_code = error_mapping.get(type(exc), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return JSONResponse(
        status_code=status_code,
        content={
            "error": {
                "type": exc.__class__.__name__,
                "message": str(exc),
                "details": getattr(exc, 'details', None)
            },
            "request_id": request.state.request_id,
            "timestamp": datetime.utcnow().isoformat()
        }
    )
```

### 5. Pagination and Filtering

```python
# v2/src/interfaces/api/rest/utils/pagination.py
from typing import Generic, TypeVar, List
from pydantic.generics import GenericModel

T = TypeVar('T')

class PaginatedResponse(GenericModel, Generic[T]):
    """Generic paginated response"""
    
    data: List[T]
    pagination: PaginationInfo
    
class PaginationInfo(BaseModel):
    skip: int
    limit: int
    total: int
    has_more: bool
    
    @property
    def pages(self) -> int:
        return (self.total + self.limit - 1) // self.limit
    
    @property
    def current_page(self) -> int:
        return (self.skip // self.limit) + 1
```

## Testing Strategy

### API Integration Tests
```python
# tests/integration/api/test_analysis_endpoints.py
@pytest.mark.asyncio
async def test_create_analysis(client: AsyncClient):
    """Test analysis creation endpoint"""
    
    request_data = {
        "name": "Test Analysis",
        "start_date": "2023-01-01",
        "end_date": "2023-12-31",
        "commodities": ["Wheat", "Rice"],
        "include_diagnostics": True
    }
    
    response = await client.post("/api/v1/analysis/three-tier", json=request_data)
    
    assert response.status_code == 200
    data = response.json()
    assert "id" in data
    assert data["status"] == "PENDING"
```

## Implementation Checklist

- [ ] Implement analysis creation endpoint
- [ ] Add analysis status endpoint
- [ ] Create results retrieval endpoint
- [ ] Implement market listing endpoint
- [ ] Add market prices endpoint
- [ ] Create commodity endpoints
- [ ] Implement request validation
- [ ] Add error handling middleware
- [ ] Create pagination utilities
- [ ] Write API documentation
- [ ] Add integration tests
- [ ] Implement rate limiting

## Success Criteria

1. All endpoints return correct status codes
2. Request validation works properly
3. Pagination handles large datasets
4. Error responses are consistent
5. API documentation is complete
6. Integration tests pass
7. Response times < 100ms for queries

## Estimated Duration
- Development: 3-4 days
- Testing: 2 days
- Documentation: 1 day
- Total: 6-7 days