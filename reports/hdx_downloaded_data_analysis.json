{"Affected People: Humanitarian Needs for Yemen": {"file_name": "Affected People: Humanitarian Needs for Yemen.csv", "shape": [1000, 17], "columns": ["location_code", "has_hrp", "in_gho", "provider_admin1_name", "provider_admin2_name", "admin1_code", "admin1_name", "admin2_code", "admin2_name", "admin_level", "sector_code", "sector_name", "category", "population_status", "population", "reference_period_start", "reference_period_end"], "memory_usage": 0.9704475402832031, "time_columns": ["reference_period_start", "reference_period_end"], "date_range": {"min": "2024-01-01 00:00:00", "max": "2024-01-01 00:00:00", "unique_dates": 1}, "geographic_columns": ["location_code", "provider_admin1_name", "provider_admin2_name", "admin1_code", "admin1_name", "admin2_code", "admin2_name", "admin_level", "sector_code", "population_status", "population"], "geographic_coverage": {"column": "provider_admin1_name", "unique_locations": 1, "sample_locations": ["#adm1+name+provider"]}}, "Food Security, Nutrition & Poverty: Food Prices & Market Monitor for Yemen": {"file_name": "Food Security, Nutrition & Poverty: Food Prices & Market Monitor for Yemen.csv", "shape": [1000, 22], "columns": ["location_code", "has_hrp", "in_gho", "provider_admin1_name", "provider_admin2_name", "admin1_code", "admin1_name", "admin2_code", "admin2_name", "admin_level", "market_name", "lat", "lon", "commodity_category", "commodity_name", "unit", "price_flag", "price_type", "currency_code", "price", "reference_period_start", "reference_period_end"], "memory_usage": 1.337193489074707, "time_columns": ["reference_period_start", "reference_period_end"], "date_range": {"min": "2024-01-15 00:00:00", "max": "2025-03-15 00:00:00", "unique_dates": 4}, "geographic_columns": ["location_code", "provider_admin1_name", "provider_admin2_name", "admin1_code", "admin1_name", "admin2_code", "admin2_name", "admin_level", "lat", "lon", "currency_code"], "geographic_coverage": {"column": "provider_admin1_name", "unique_locations": 18, "sample_locations": ["#adm1+name+provider", "<PERSON><PERSON><PERSON>", "Aden", "Al Dhale'e", "Al Maharah"]}, "price_columns": ["price_flag", "price_type", "price"]}, "Coordination & Context: Conflict Events for Yemen": {"file_name": "Coordination & Context: Conflict Events for Yemen.csv", "shape": [1000, 15], "columns": ["location_code", "has_hrp", "in_gho", "provider_admin1_name", "provider_admin2_name", "admin1_code", "admin1_name", "admin2_code", "admin2_name", "admin_level", "event_type", "events", "fatalities", "reference_period_start", "reference_period_end"], "memory_usage": 0.9045705795288086, "time_columns": ["reference_period_start", "reference_period_end"], "date_range": {"min": "2025-01-01 00:00:00", "max": "2025-03-01 00:00:00", "unique_dates": 3}, "geographic_columns": ["location_code", "provider_admin1_name", "provider_admin2_name", "admin1_code", "admin1_name", "admin2_code", "admin2_name", "admin_level"], "geographic_coverage": {"column": "provider_admin1_name", "unique_locations": 23, "sample_locations": ["#adm1+name+provider", "<PERSON><PERSON><PERSON>", "Aden", "Al Bayda", "Al Hudaydah"]}, "conflict_columns": ["event_type", "events"]}, "Coordination & Context: Operational Presence for Yemen": {"file_name": "Coordination & Context: Operational Presence for Yemen.csv", "shape": [1000, 17], "columns": ["location_code", "has_hrp", "in_gho", "provider_admin1_name", "provider_admin2_name", "admin1_code", "admin1_name", "admin2_code", "admin2_name", "admin_level", "org_acronym", "org_name", "org_type_description", "sector_code", "sector_name", "reference_period_start", "reference_period_end"], "memory_usage": 1.052358627319336, "time_columns": ["reference_period_start", "reference_period_end"], "date_range": {"min": "2024-01-01 00:00:00", "max": "2024-01-01 00:00:00", "unique_dates": 1}, "geographic_columns": ["location_code", "provider_admin1_name", "provider_admin2_name", "admin1_code", "admin1_name", "admin2_code", "admin2_name", "admin_level", "sector_code"], "geographic_coverage": {"column": "provider_admin1_name", "unique_locations": 4, "sample_locations": ["#adm1+name+provider", "<PERSON><PERSON><PERSON>", "<PERSON>'", "Aden"]}, "organization_columns": ["sector_code", "sector_name"]}, "Coordination & Context: Funding for Yemen": {"file_name": "Coordination & Context: Funding for Yemen.csv", "shape": [49, 11], "columns": ["location_code", "has_hrp", "in_gho", "appeal_code", "appeal_name", "appeal_type", "requirements_usd", "funding_usd", "funding_pct", "reference_period_start", "reference_period_end"], "memory_usage": 0.03182411193847656, "time_columns": ["reference_period_start", "reference_period_end"], "date_range": {"min": "2001-01-01 00:00:00", "max": "2027-01-01 00:00:00", "unique_dates": 29}, "geographic_columns": ["location_code", "appeal_code"], "organization_columns": []}}